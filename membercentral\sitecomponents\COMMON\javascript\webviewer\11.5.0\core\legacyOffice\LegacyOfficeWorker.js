!(function (e) {
	var t = {};
	function n(r) {
		if (t[r]) return t[r].exports;
		var o = (t[r] = { i: r, l: !1, exports: {} });
		return e[r].call(o.exports, o, o.exports, n), (o.l = !0), o.exports;
	}
	(n.m = e),
		(n.c = t),
		(n.d = function (e, t, r) {
			n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r });
		}),
		(n.r = function (e) {
			"undefined" != typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }),
				Object.defineProperty(e, "__esModule", { value: !0 });
		}),
		(n.t = function (e, t) {
			if ((1 & t && (e = n(e)), 8 & t)) return e;
			if (4 & t && "object" == typeof e && e && e.__esModule) return e;
			var r = Object.create(null);
			if (
				(n.r(r),
				Object.defineProperty(r, "default", { enumerable: !0, value: e }),
				2 & t && "string" != typeof e)
			)
				for (var o in e)
					n.d(
						r,
						o,
						function (t) {
							return e[t];
						}.bind(null, o),
					);
			return r;
		}),
		(n.n = function (e) {
			var t =
				e && e.__esModule
					? function () {
							return e.default;
						}
					: function () {
							return e;
						};
			return n.d(t, "a", t), t;
		}),
		(n.o = function (e, t) {
			return Object.prototype.hasOwnProperty.call(e, t);
		}),
		(n.p = "/core/legacyOffice/"),
		n((n.s = 2));
})([
	function (e, t) {
		var n;
		n = (function () {
			return this;
		})();
		try {
			n = n || new Function("return this")();
		} catch (e) {
			"object" == typeof window && (n = window);
		}
		e.exports = n;
	},
	function (e, t, n) {
		/*! @license DOMPurify 3.2.5 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE */
		e.exports = (function () {
			"use strict";
			const {
				entries: e,
				setPrototypeOf: t,
				isFrozen: n,
				getPrototypeOf: r,
				getOwnPropertyDescriptor: o,
			} = Object;
			let { freeze: i, seal: a, create: c } = Object,
				{ apply: s, construct: u } = "undefined" != typeof Reflect && Reflect;
			i ||
				(i = function (e) {
					return e;
				}),
				a ||
					(a = function (e) {
						return e;
					}),
				s ||
					(s = function (e, t, n) {
						return e.apply(t, n);
					}),
				u ||
					(u = function (e, t) {
						return new e(...t);
					});
			const l = S(Array.prototype.forEach),
				f = S(Array.prototype.lastIndexOf),
				m = S(Array.prototype.pop),
				d = S(Array.prototype.push),
				p = S(Array.prototype.splice),
				h = S(String.prototype.toLowerCase),
				g = S(String.prototype.toString),
				y = S(String.prototype.match),
				v = S(String.prototype.replace),
				b = S(String.prototype.indexOf),
				T = S(String.prototype.trim),
				w = S(Object.prototype.hasOwnProperty),
				A = S(RegExp.prototype.test),
				E =
					((_ = TypeError),
					function () {
						for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
							t[n] = arguments[n];
						return u(_, t);
					});
			var _;
			function S(e) {
				return function (t) {
					t instanceof RegExp && (t.lastIndex = 0);
					for (
						var n = arguments.length, r = new Array(n > 1 ? n - 1 : 0), o = 1;
						o < n;
						o++
					)
						r[o - 1] = arguments[o];
					return s(e, t, r);
				};
			}
			function x(e, r) {
				let o =
					arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : h;
				t && t(e, null);
				let i = r.length;
				for (; i--; ) {
					let t = r[i];
					if ("string" == typeof t) {
						const e = o(t);
						e !== t && (n(r) || (r[i] = e), (t = e));
					}
					e[t] = !0;
				}
				return e;
			}
			function N(e) {
				for (let t = 0; t < e.length; t++) w(e, t) || (e[t] = null);
				return e;
			}
			function O(t) {
				const n = c(null);
				for (const [r, o] of e(t))
					w(t, r) &&
						(Array.isArray(o)
							? (n[r] = N(o))
							: o && "object" == typeof o && o.constructor === Object
								? (n[r] = O(o))
								: (n[r] = o));
				return n;
			}
			function R(e, t) {
				for (; null !== e; ) {
					const n = o(e, t);
					if (n) {
						if (n.get) return S(n.get);
						if ("function" == typeof n.value) return S(n.value);
					}
					e = r(e);
				}
				return function () {
					return null;
				};
			}
			const M = i([
					"a",
					"abbr",
					"acronym",
					"address",
					"area",
					"article",
					"aside",
					"audio",
					"b",
					"bdi",
					"bdo",
					"big",
					"blink",
					"blockquote",
					"body",
					"br",
					"button",
					"canvas",
					"caption",
					"center",
					"cite",
					"code",
					"col",
					"colgroup",
					"content",
					"data",
					"datalist",
					"dd",
					"decorator",
					"del",
					"details",
					"dfn",
					"dialog",
					"dir",
					"div",
					"dl",
					"dt",
					"element",
					"em",
					"fieldset",
					"figcaption",
					"figure",
					"font",
					"footer",
					"form",
					"h1",
					"h2",
					"h3",
					"h4",
					"h5",
					"h6",
					"head",
					"header",
					"hgroup",
					"hr",
					"html",
					"i",
					"img",
					"input",
					"ins",
					"kbd",
					"label",
					"legend",
					"li",
					"main",
					"map",
					"mark",
					"marquee",
					"menu",
					"menuitem",
					"meter",
					"nav",
					"nobr",
					"ol",
					"optgroup",
					"option",
					"output",
					"p",
					"picture",
					"pre",
					"progress",
					"q",
					"rp",
					"rt",
					"ruby",
					"s",
					"samp",
					"section",
					"select",
					"shadow",
					"small",
					"source",
					"spacer",
					"span",
					"strike",
					"strong",
					"style",
					"sub",
					"summary",
					"sup",
					"table",
					"tbody",
					"td",
					"template",
					"textarea",
					"tfoot",
					"th",
					"thead",
					"time",
					"tr",
					"track",
					"tt",
					"u",
					"ul",
					"var",
					"video",
					"wbr",
				]),
				L = i([
					"svg",
					"a",
					"altglyph",
					"altglyphdef",
					"altglyphitem",
					"animatecolor",
					"animatemotion",
					"animatetransform",
					"circle",
					"clippath",
					"defs",
					"desc",
					"ellipse",
					"filter",
					"font",
					"g",
					"glyph",
					"glyphref",
					"hkern",
					"image",
					"line",
					"lineargradient",
					"marker",
					"mask",
					"metadata",
					"mpath",
					"path",
					"pattern",
					"polygon",
					"polyline",
					"radialgradient",
					"rect",
					"stop",
					"style",
					"switch",
					"symbol",
					"text",
					"textpath",
					"title",
					"tref",
					"tspan",
					"view",
					"vkern",
				]),
				P = i([
					"feBlend",
					"feColorMatrix",
					"feComponentTransfer",
					"feComposite",
					"feConvolveMatrix",
					"feDiffuseLighting",
					"feDisplacementMap",
					"feDistantLight",
					"feDropShadow",
					"feFlood",
					"feFuncA",
					"feFuncB",
					"feFuncG",
					"feFuncR",
					"feGaussianBlur",
					"feImage",
					"feMerge",
					"feMergeNode",
					"feMorphology",
					"feOffset",
					"fePointLight",
					"feSpecularLighting",
					"feSpotLight",
					"feTile",
					"feTurbulence",
				]),
				I = i([
					"animate",
					"color-profile",
					"cursor",
					"discard",
					"font-face",
					"font-face-format",
					"font-face-name",
					"font-face-src",
					"font-face-uri",
					"foreignobject",
					"hatch",
					"hatchpath",
					"mesh",
					"meshgradient",
					"meshpatch",
					"meshrow",
					"missing-glyph",
					"script",
					"set",
					"solidcolor",
					"unknown",
					"use",
				]),
				k = i([
					"math",
					"menclose",
					"merror",
					"mfenced",
					"mfrac",
					"mglyph",
					"mi",
					"mlabeledtr",
					"mmultiscripts",
					"mn",
					"mo",
					"mover",
					"mpadded",
					"mphantom",
					"mroot",
					"mrow",
					"ms",
					"mspace",
					"msqrt",
					"mstyle",
					"msub",
					"msup",
					"msubsup",
					"mtable",
					"mtd",
					"mtext",
					"mtr",
					"munder",
					"munderover",
					"mprescripts",
				]),
				C = i([
					"maction",
					"maligngroup",
					"malignmark",
					"mlongdiv",
					"mscarries",
					"mscarry",
					"msgroup",
					"mstack",
					"msline",
					"msrow",
					"semantics",
					"annotation",
					"annotation-xml",
					"mprescripts",
					"none",
				]),
				D = i(["#text"]),
				U = i([
					"accept",
					"action",
					"align",
					"alt",
					"autocapitalize",
					"autocomplete",
					"autopictureinpicture",
					"autoplay",
					"background",
					"bgcolor",
					"border",
					"capture",
					"cellpadding",
					"cellspacing",
					"checked",
					"cite",
					"class",
					"clear",
					"color",
					"cols",
					"colspan",
					"controls",
					"controlslist",
					"coords",
					"crossorigin",
					"datetime",
					"decoding",
					"default",
					"dir",
					"disabled",
					"disablepictureinpicture",
					"disableremoteplayback",
					"download",
					"draggable",
					"enctype",
					"enterkeyhint",
					"face",
					"for",
					"headers",
					"height",
					"hidden",
					"high",
					"href",
					"hreflang",
					"id",
					"inputmode",
					"integrity",
					"ismap",
					"kind",
					"label",
					"lang",
					"list",
					"loading",
					"loop",
					"low",
					"max",
					"maxlength",
					"media",
					"method",
					"min",
					"minlength",
					"multiple",
					"muted",
					"name",
					"nonce",
					"noshade",
					"novalidate",
					"nowrap",
					"open",
					"optimum",
					"pattern",
					"placeholder",
					"playsinline",
					"popover",
					"popovertarget",
					"popovertargetaction",
					"poster",
					"preload",
					"pubdate",
					"radiogroup",
					"readonly",
					"rel",
					"required",
					"rev",
					"reversed",
					"role",
					"rows",
					"rowspan",
					"spellcheck",
					"scope",
					"selected",
					"shape",
					"size",
					"sizes",
					"span",
					"srclang",
					"start",
					"src",
					"srcset",
					"step",
					"style",
					"summary",
					"tabindex",
					"title",
					"translate",
					"type",
					"usemap",
					"valign",
					"value",
					"width",
					"wrap",
					"xmlns",
					"slot",
				]),
				j = i([
					"accent-height",
					"accumulate",
					"additive",
					"alignment-baseline",
					"amplitude",
					"ascent",
					"attributename",
					"attributetype",
					"azimuth",
					"basefrequency",
					"baseline-shift",
					"begin",
					"bias",
					"by",
					"class",
					"clip",
					"clippathunits",
					"clip-path",
					"clip-rule",
					"color",
					"color-interpolation",
					"color-interpolation-filters",
					"color-profile",
					"color-rendering",
					"cx",
					"cy",
					"d",
					"dx",
					"dy",
					"diffuseconstant",
					"direction",
					"display",
					"divisor",
					"dur",
					"edgemode",
					"elevation",
					"end",
					"exponent",
					"fill",
					"fill-opacity",
					"fill-rule",
					"filter",
					"filterunits",
					"flood-color",
					"flood-opacity",
					"font-family",
					"font-size",
					"font-size-adjust",
					"font-stretch",
					"font-style",
					"font-variant",
					"font-weight",
					"fx",
					"fy",
					"g1",
					"g2",
					"glyph-name",
					"glyphref",
					"gradientunits",
					"gradienttransform",
					"height",
					"href",
					"id",
					"image-rendering",
					"in",
					"in2",
					"intercept",
					"k",
					"k1",
					"k2",
					"k3",
					"k4",
					"kerning",
					"keypoints",
					"keysplines",
					"keytimes",
					"lang",
					"lengthadjust",
					"letter-spacing",
					"kernelmatrix",
					"kernelunitlength",
					"lighting-color",
					"local",
					"marker-end",
					"marker-mid",
					"marker-start",
					"markerheight",
					"markerunits",
					"markerwidth",
					"maskcontentunits",
					"maskunits",
					"max",
					"mask",
					"media",
					"method",
					"mode",
					"min",
					"name",
					"numoctaves",
					"offset",
					"operator",
					"opacity",
					"order",
					"orient",
					"orientation",
					"origin",
					"overflow",
					"paint-order",
					"path",
					"pathlength",
					"patterncontentunits",
					"patterntransform",
					"patternunits",
					"points",
					"preservealpha",
					"preserveaspectratio",
					"primitiveunits",
					"r",
					"rx",
					"ry",
					"radius",
					"refx",
					"refy",
					"repeatcount",
					"repeatdur",
					"restart",
					"result",
					"rotate",
					"scale",
					"seed",
					"shape-rendering",
					"slope",
					"specularconstant",
					"specularexponent",
					"spreadmethod",
					"startoffset",
					"stddeviation",
					"stitchtiles",
					"stop-color",
					"stop-opacity",
					"stroke-dasharray",
					"stroke-dashoffset",
					"stroke-linecap",
					"stroke-linejoin",
					"stroke-miterlimit",
					"stroke-opacity",
					"stroke",
					"stroke-width",
					"style",
					"surfacescale",
					"systemlanguage",
					"tabindex",
					"tablevalues",
					"targetx",
					"targety",
					"transform",
					"transform-origin",
					"text-anchor",
					"text-decoration",
					"text-rendering",
					"textlength",
					"type",
					"u1",
					"u2",
					"unicode",
					"values",
					"viewbox",
					"visibility",
					"version",
					"vert-adv-y",
					"vert-origin-x",
					"vert-origin-y",
					"width",
					"word-spacing",
					"wrap",
					"writing-mode",
					"xchannelselector",
					"ychannelselector",
					"x",
					"x1",
					"x2",
					"xmlns",
					"y",
					"y1",
					"y2",
					"z",
					"zoomandpan",
				]),
				F = i([
					"accent",
					"accentunder",
					"align",
					"bevelled",
					"close",
					"columnsalign",
					"columnlines",
					"columnspan",
					"denomalign",
					"depth",
					"dir",
					"display",
					"displaystyle",
					"encoding",
					"fence",
					"frame",
					"height",
					"href",
					"id",
					"largeop",
					"length",
					"linethickness",
					"lspace",
					"lquote",
					"mathbackground",
					"mathcolor",
					"mathsize",
					"mathvariant",
					"maxsize",
					"minsize",
					"movablelimits",
					"notation",
					"numalign",
					"open",
					"rowalign",
					"rowlines",
					"rowspacing",
					"rowspan",
					"rspace",
					"rquote",
					"scriptlevel",
					"scriptminsize",
					"scriptsizemultiplier",
					"selection",
					"separator",
					"separators",
					"stretchy",
					"subscriptshift",
					"supscriptshift",
					"symmetric",
					"voffset",
					"width",
					"xmlns",
				]),
				z = i([
					"xlink:href",
					"xml:id",
					"xlink:title",
					"xml:space",
					"xmlns:xlink",
				]),
				H = a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),
				W = a(/<%[\w\W]*|[\w\W]*%>/gm),
				B = a(/\$\{[\w\W]*/gm),
				G = a(/^data-[\-\w.\u00B7-\uFFFF]+$/),
				Y = a(/^aria-[\-\w]+$/),
				X = a(
					/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
				),
				q = a(/^(?:\w+script|data):/i),
				$ = a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),
				V = a(/^html$/i),
				K = a(/^[a-z][.\w]*(-[.\w]+)+$/i);
			var Z = Object.freeze({
				__proto__: null,
				ARIA_ATTR: Y,
				ATTR_WHITESPACE: $,
				CUSTOM_ELEMENT: K,
				DATA_ATTR: G,
				DOCTYPE_NAME: V,
				ERB_EXPR: W,
				IS_ALLOWED_URI: X,
				IS_SCRIPT_OR_DATA: q,
				MUSTACHE_EXPR: H,
				TMPLIT_EXPR: B,
			});
			const J = 1,
				Q = 3,
				ee = 7,
				te = 8,
				ne = 9,
				re = function () {
					return "undefined" == typeof window ? null : window;
				},
				oe = function (e, t) {
					if ("object" != typeof e || "function" != typeof e.createPolicy)
						return null;
					let n = null;
					t &&
						t.hasAttribute("data-tt-policy-suffix") &&
						(n = t.getAttribute("data-tt-policy-suffix"));
					const r = "dompurify" + (n ? "#" + n : "");
					try {
						return e.createPolicy(r, {
							createHTML: (e) => e,
							createScriptURL: (e) => e,
						});
					} catch (e) {
						return (
							console.warn(
								"TrustedTypes policy " + r + " could not be created.",
							),
							null
						);
					}
				};
			return (function t() {
				let n =
					arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : re();
				const r = (e) => t(e);
				if (
					((r.version = "3.2.5"),
					(r.removed = []),
					!n || !n.document || n.document.nodeType !== ne || !n.Element)
				)
					return (r.isSupported = !1), r;
				let { document: o } = n;
				const a = o,
					s = a.currentScript,
					{
						DocumentFragment: u,
						HTMLTemplateElement: _,
						Node: S,
						Element: N,
						NodeFilter: H,
						NamedNodeMap: W = n.NamedNodeMap || n.MozNamedAttrMap,
						HTMLFormElement: B,
						DOMParser: G,
						trustedTypes: Y,
					} = n,
					q = N.prototype,
					$ = R(q, "cloneNode"),
					K = R(q, "remove"),
					ie = R(q, "nextSibling"),
					ae = R(q, "childNodes"),
					ce = R(q, "parentNode");
				if ("function" == typeof _) {
					const e = o.createElement("template");
					e.content && e.content.ownerDocument && (o = e.content.ownerDocument);
				}
				let se,
					ue = "";
				const {
						implementation: le,
						createNodeIterator: fe,
						createDocumentFragment: me,
						getElementsByTagName: de,
					} = o,
					{ importNode: pe } = a;
				let he = {
					afterSanitizeAttributes: [],
					afterSanitizeElements: [],
					afterSanitizeShadowDOM: [],
					beforeSanitizeAttributes: [],
					beforeSanitizeElements: [],
					beforeSanitizeShadowDOM: [],
					uponSanitizeAttribute: [],
					uponSanitizeElement: [],
					uponSanitizeShadowNode: [],
				};
				r.isSupported =
					"function" == typeof e &&
					"function" == typeof ce &&
					le &&
					void 0 !== le.createHTMLDocument;
				const {
					MUSTACHE_EXPR: ge,
					ERB_EXPR: ye,
					TMPLIT_EXPR: ve,
					DATA_ATTR: be,
					ARIA_ATTR: Te,
					IS_SCRIPT_OR_DATA: we,
					ATTR_WHITESPACE: Ae,
					CUSTOM_ELEMENT: Ee,
				} = Z;
				let { IS_ALLOWED_URI: _e } = Z,
					Se = null;
				const xe = x({}, [...M, ...L, ...P, ...k, ...D]);
				let Ne = null;
				const Oe = x({}, [...U, ...j, ...F, ...z]);
				let Re = Object.seal(
						c(null, {
							tagNameCheck: {
								writable: !0,
								configurable: !1,
								enumerable: !0,
								value: null,
							},
							attributeNameCheck: {
								writable: !0,
								configurable: !1,
								enumerable: !0,
								value: null,
							},
							allowCustomizedBuiltInElements: {
								writable: !0,
								configurable: !1,
								enumerable: !0,
								value: !1,
							},
						}),
					),
					Me = null,
					Le = null,
					Pe = !0,
					Ie = !0,
					ke = !1,
					Ce = !0,
					De = !1,
					Ue = !0,
					je = !1,
					Fe = !1,
					ze = !1,
					He = !1,
					We = !1,
					Be = !1,
					Ge = !0,
					Ye = !1;
				const Xe = "user-content-";
				let qe = !0,
					$e = !1,
					Ve = {},
					Ke = null;
				const Ze = x({}, [
					"annotation-xml",
					"audio",
					"colgroup",
					"desc",
					"foreignobject",
					"head",
					"iframe",
					"math",
					"mi",
					"mn",
					"mo",
					"ms",
					"mtext",
					"noembed",
					"noframes",
					"noscript",
					"plaintext",
					"script",
					"style",
					"svg",
					"template",
					"thead",
					"title",
					"video",
					"xmp",
				]);
				let Je = null;
				const Qe = x({}, ["audio", "video", "img", "source", "image", "track"]);
				let et = null;
				const tt = x({}, [
						"alt",
						"class",
						"for",
						"id",
						"label",
						"name",
						"pattern",
						"placeholder",
						"role",
						"summary",
						"title",
						"value",
						"style",
						"xmlns",
					]),
					nt = "http://www.w3.org/1998/Math/MathML",
					rt = "http://www.w3.org/2000/svg",
					ot = "http://www.w3.org/1999/xhtml";
				let it = ot,
					at = !1,
					ct = null;
				const st = x({}, [nt, rt, ot], g);
				let ut = x({}, ["mi", "mo", "mn", "ms", "mtext"]),
					lt = x({}, ["annotation-xml"]);
				const ft = x({}, ["title", "style", "font", "a", "script"]);
				let mt = null;
				const dt = ["application/xhtml+xml", "text/html"],
					pt = "text/html";
				let ht = null,
					gt = null;
				const yt = o.createElement("form"),
					vt = function (e) {
						return e instanceof RegExp || e instanceof Function;
					},
					bt = function () {
						let e =
							arguments.length > 0 && void 0 !== arguments[0]
								? arguments[0]
								: {};
						if (!gt || gt !== e) {
							if (
								((e && "object" == typeof e) || (e = {}),
								(e = O(e)),
								(mt =
									-1 === dt.indexOf(e.PARSER_MEDIA_TYPE)
										? pt
										: e.PARSER_MEDIA_TYPE),
								(ht = "application/xhtml+xml" === mt ? g : h),
								(Se = w(e, "ALLOWED_TAGS") ? x({}, e.ALLOWED_TAGS, ht) : xe),
								(Ne = w(e, "ALLOWED_ATTR") ? x({}, e.ALLOWED_ATTR, ht) : Oe),
								(ct = w(e, "ALLOWED_NAMESPACES")
									? x({}, e.ALLOWED_NAMESPACES, g)
									: st),
								(et = w(e, "ADD_URI_SAFE_ATTR")
									? x(O(tt), e.ADD_URI_SAFE_ATTR, ht)
									: tt),
								(Je = w(e, "ADD_DATA_URI_TAGS")
									? x(O(Qe), e.ADD_DATA_URI_TAGS, ht)
									: Qe),
								(Ke = w(e, "FORBID_CONTENTS")
									? x({}, e.FORBID_CONTENTS, ht)
									: Ze),
								(Me = w(e, "FORBID_TAGS") ? x({}, e.FORBID_TAGS, ht) : {}),
								(Le = w(e, "FORBID_ATTR") ? x({}, e.FORBID_ATTR, ht) : {}),
								(Ve = !!w(e, "USE_PROFILES") && e.USE_PROFILES),
								(Pe = !1 !== e.ALLOW_ARIA_ATTR),
								(Ie = !1 !== e.ALLOW_DATA_ATTR),
								(ke = e.ALLOW_UNKNOWN_PROTOCOLS || !1),
								(Ce = !1 !== e.ALLOW_SELF_CLOSE_IN_ATTR),
								(De = e.SAFE_FOR_TEMPLATES || !1),
								(Ue = !1 !== e.SAFE_FOR_XML),
								(je = e.WHOLE_DOCUMENT || !1),
								(He = e.RETURN_DOM || !1),
								(We = e.RETURN_DOM_FRAGMENT || !1),
								(Be = e.RETURN_TRUSTED_TYPE || !1),
								(ze = e.FORCE_BODY || !1),
								(Ge = !1 !== e.SANITIZE_DOM),
								(Ye = e.SANITIZE_NAMED_PROPS || !1),
								(qe = !1 !== e.KEEP_CONTENT),
								($e = e.IN_PLACE || !1),
								(_e = e.ALLOWED_URI_REGEXP || X),
								(it = e.NAMESPACE || ot),
								(ut = e.MATHML_TEXT_INTEGRATION_POINTS || ut),
								(lt = e.HTML_INTEGRATION_POINTS || lt),
								(Re = e.CUSTOM_ELEMENT_HANDLING || {}),
								e.CUSTOM_ELEMENT_HANDLING &&
									vt(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck) &&
									(Re.tagNameCheck = e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),
								e.CUSTOM_ELEMENT_HANDLING &&
									vt(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck) &&
									(Re.attributeNameCheck =
										e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),
								e.CUSTOM_ELEMENT_HANDLING &&
									"boolean" ==
										typeof e.CUSTOM_ELEMENT_HANDLING
											.allowCustomizedBuiltInElements &&
									(Re.allowCustomizedBuiltInElements =
										e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),
								De && (Ie = !1),
								We && (He = !0),
								Ve &&
									((Se = x({}, D)),
									(Ne = []),
									!0 === Ve.html && (x(Se, M), x(Ne, U)),
									!0 === Ve.svg && (x(Se, L), x(Ne, j), x(Ne, z)),
									!0 === Ve.svgFilters && (x(Se, P), x(Ne, j), x(Ne, z)),
									!0 === Ve.mathMl && (x(Se, k), x(Ne, F), x(Ne, z))),
								e.ADD_TAGS &&
									(Se === xe && (Se = O(Se)), x(Se, e.ADD_TAGS, ht)),
								e.ADD_ATTR &&
									(Ne === Oe && (Ne = O(Ne)), x(Ne, e.ADD_ATTR, ht)),
								e.ADD_URI_SAFE_ATTR && x(et, e.ADD_URI_SAFE_ATTR, ht),
								e.FORBID_CONTENTS &&
									(Ke === Ze && (Ke = O(Ke)), x(Ke, e.FORBID_CONTENTS, ht)),
								qe && (Se["#text"] = !0),
								je && x(Se, ["html", "head", "body"]),
								Se.table && (x(Se, ["tbody"]), delete Me.tbody),
								e.TRUSTED_TYPES_POLICY)
							) {
								if ("function" != typeof e.TRUSTED_TYPES_POLICY.createHTML)
									throw E(
										'TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.',
									);
								if ("function" != typeof e.TRUSTED_TYPES_POLICY.createScriptURL)
									throw E(
										'TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.',
									);
								(se = e.TRUSTED_TYPES_POLICY), (ue = se.createHTML(""));
							} else
								void 0 === se && (se = oe(Y, s)),
									null !== se &&
										"string" == typeof ue &&
										(ue = se.createHTML(""));
							i && i(e), (gt = e);
						}
					},
					Tt = x({}, [...L, ...P, ...I]),
					wt = x({}, [...k, ...C]),
					At = function (e) {
						let t = ce(e);
						(t && t.tagName) || (t = { namespaceURI: it, tagName: "template" });
						const n = h(e.tagName),
							r = h(t.tagName);
						return (
							!!ct[e.namespaceURI] &&
							(e.namespaceURI === rt
								? t.namespaceURI === ot
									? "svg" === n
									: t.namespaceURI === nt
										? "svg" === n && ("annotation-xml" === r || ut[r])
										: Boolean(Tt[n])
								: e.namespaceURI === nt
									? t.namespaceURI === ot
										? "math" === n
										: t.namespaceURI === rt
											? "math" === n && lt[r]
											: Boolean(wt[n])
									: e.namespaceURI === ot
										? !(t.namespaceURI === rt && !lt[r]) &&
											!(t.namespaceURI === nt && !ut[r]) &&
											!wt[n] &&
											(ft[n] || !Tt[n])
										: !("application/xhtml+xml" !== mt || !ct[e.namespaceURI]))
						);
					},
					Et = function (e) {
						d(r.removed, { element: e });
						try {
							ce(e).removeChild(e);
						} catch (t) {
							K(e);
						}
					},
					_t = function (e, t) {
						try {
							d(r.removed, { attribute: t.getAttributeNode(e), from: t });
						} catch (e) {
							d(r.removed, { attribute: null, from: t });
						}
						if ((t.removeAttribute(e), "is" === e))
							if (He || We)
								try {
									Et(t);
								} catch (e) {}
							else
								try {
									t.setAttribute(e, "");
								} catch (e) {}
					},
					St = function (e) {
						let t = null,
							n = null;
						if (ze) e = "<remove></remove>" + e;
						else {
							const t = y(e, /^[\r\n\t ]+/);
							n = t && t[0];
						}
						"application/xhtml+xml" === mt &&
							it === ot &&
							(e =
								'<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' +
								e +
								"</body></html>");
						const r = se ? se.createHTML(e) : e;
						if (it === ot)
							try {
								t = new G().parseFromString(r, mt);
							} catch (e) {}
						if (!t || !t.documentElement) {
							t = le.createDocument(it, "template", null);
							try {
								t.documentElement.innerHTML = at ? ue : r;
							} catch (e) {}
						}
						const i = t.body || t.documentElement;
						return (
							e &&
								n &&
								i.insertBefore(o.createTextNode(n), i.childNodes[0] || null),
							it === ot
								? de.call(t, je ? "html" : "body")[0]
								: je
									? t.documentElement
									: i
						);
					},
					xt = function (e) {
						return fe.call(
							e.ownerDocument || e,
							e,
							H.SHOW_ELEMENT |
								H.SHOW_COMMENT |
								H.SHOW_TEXT |
								H.SHOW_PROCESSING_INSTRUCTION |
								H.SHOW_CDATA_SECTION,
							null,
						);
					},
					Nt = function (e) {
						return (
							e instanceof B &&
							("string" != typeof e.nodeName ||
								"string" != typeof e.textContent ||
								"function" != typeof e.removeChild ||
								!(e.attributes instanceof W) ||
								"function" != typeof e.removeAttribute ||
								"function" != typeof e.setAttribute ||
								"string" != typeof e.namespaceURI ||
								"function" != typeof e.insertBefore ||
								"function" != typeof e.hasChildNodes)
						);
					},
					Ot = function (e) {
						return "function" == typeof S && e instanceof S;
					};
				function Rt(e, t, n) {
					l(e, (e) => {
						e.call(r, t, n, gt);
					});
				}
				const Mt = function (e) {
						let t = null;
						if ((Rt(he.beforeSanitizeElements, e, null), Nt(e)))
							return Et(e), !0;
						const n = ht(e.nodeName);
						if (
							(Rt(he.uponSanitizeElement, e, { tagName: n, allowedTags: Se }),
							e.hasChildNodes() &&
								!Ot(e.firstElementChild) &&
								A(/<[/\w!]/g, e.innerHTML) &&
								A(/<[/\w!]/g, e.textContent))
						)
							return Et(e), !0;
						if (e.nodeType === ee) return Et(e), !0;
						if (Ue && e.nodeType === te && A(/<[/\w]/g, e.data))
							return Et(e), !0;
						if (!Se[n] || Me[n]) {
							if (!Me[n] && Pt(n)) {
								if (Re.tagNameCheck instanceof RegExp && A(Re.tagNameCheck, n))
									return !1;
								if (Re.tagNameCheck instanceof Function && Re.tagNameCheck(n))
									return !1;
							}
							if (qe && !Ke[n]) {
								const t = ce(e) || e.parentNode,
									n = ae(e) || e.childNodes;
								if (n && t)
									for (let r = n.length - 1; r >= 0; --r) {
										const o = $(n[r], !0);
										(o.__removalCount = (e.__removalCount || 0) + 1),
											t.insertBefore(o, ie(e));
									}
							}
							return Et(e), !0;
						}
						return e instanceof N && !At(e)
							? (Et(e), !0)
							: ("noscript" !== n && "noembed" !== n && "noframes" !== n) ||
									!A(/<\/no(script|embed|frames)/i, e.innerHTML)
								? (De &&
										e.nodeType === Q &&
										((t = e.textContent),
										l([ge, ye, ve], (e) => {
											t = v(t, e, " ");
										}),
										e.textContent !== t &&
											(d(r.removed, { element: e.cloneNode() }),
											(e.textContent = t))),
									Rt(he.afterSanitizeElements, e, null),
									!1)
								: (Et(e), !0);
					},
					Lt = function (e, t, n) {
						if (Ge && ("id" === t || "name" === t) && (n in o || n in yt))
							return !1;
						if (Ie && !Le[t] && A(be, t));
						else if (Pe && A(Te, t));
						else if (!Ne[t] || Le[t]) {
							if (
								!(
									(Pt(e) &&
										((Re.tagNameCheck instanceof RegExp &&
											A(Re.tagNameCheck, e)) ||
											(Re.tagNameCheck instanceof Function &&
												Re.tagNameCheck(e))) &&
										((Re.attributeNameCheck instanceof RegExp &&
											A(Re.attributeNameCheck, t)) ||
											(Re.attributeNameCheck instanceof Function &&
												Re.attributeNameCheck(t)))) ||
									("is" === t &&
										Re.allowCustomizedBuiltInElements &&
										((Re.tagNameCheck instanceof RegExp &&
											A(Re.tagNameCheck, n)) ||
											(Re.tagNameCheck instanceof Function &&
												Re.tagNameCheck(n))))
								)
							)
								return !1;
						} else if (et[t]);
						else if (A(_e, v(n, Ae, "")));
						else if (
							("src" !== t && "xlink:href" !== t && "href" !== t) ||
							"script" === e ||
							0 !== b(n, "data:") ||
							!Je[e]
						)
							if (ke && !A(we, v(n, Ae, "")));
							else if (n) return !1;
						return !0;
					},
					Pt = function (e) {
						return "annotation-xml" !== e && y(e, Ee);
					},
					It = function (e) {
						Rt(he.beforeSanitizeAttributes, e, null);
						const { attributes: t } = e;
						if (!t || Nt(e)) return;
						const n = {
							attrName: "",
							attrValue: "",
							keepAttr: !0,
							allowedAttributes: Ne,
							forceKeepAttr: void 0,
						};
						let o = t.length;
						for (; o--; ) {
							const i = t[o],
								{ name: a, namespaceURI: c, value: s } = i,
								u = ht(a);
							let f = "value" === a ? s : T(s);
							if (
								((n.attrName = u),
								(n.attrValue = f),
								(n.keepAttr = !0),
								(n.forceKeepAttr = void 0),
								Rt(he.uponSanitizeAttribute, e, n),
								(f = n.attrValue),
								!Ye || ("id" !== u && "name" !== u) || (_t(a, e), (f = Xe + f)),
								Ue && A(/((--!?|])>)|<\/(style|title)/i, f))
							) {
								_t(a, e);
								continue;
							}
							if (n.forceKeepAttr) continue;
							if ((_t(a, e), !n.keepAttr)) continue;
							if (!Ce && A(/\/>/i, f)) {
								_t(a, e);
								continue;
							}
							De &&
								l([ge, ye, ve], (e) => {
									f = v(f, e, " ");
								});
							const d = ht(e.nodeName);
							if (Lt(d, u, f)) {
								if (
									se &&
									"object" == typeof Y &&
									"function" == typeof Y.getAttributeType
								)
									if (c);
									else
										switch (Y.getAttributeType(d, u)) {
											case "TrustedHTML":
												f = se.createHTML(f);
												break;
											case "TrustedScriptURL":
												f = se.createScriptURL(f);
										}
								try {
									c ? e.setAttributeNS(c, a, f) : e.setAttribute(a, f),
										Nt(e) ? Et(e) : m(r.removed);
								} catch (e) {}
							}
						}
						Rt(he.afterSanitizeAttributes, e, null);
					},
					kt = function e(t) {
						let n = null;
						const r = xt(t);
						for (Rt(he.beforeSanitizeShadowDOM, t, null); (n = r.nextNode()); )
							Rt(he.uponSanitizeShadowNode, n, null),
								Mt(n),
								It(n),
								n.content instanceof u && e(n.content);
						Rt(he.afterSanitizeShadowDOM, t, null);
					};
				return (
					(r.sanitize = function (e) {
						let t =
								arguments.length > 1 && void 0 !== arguments[1]
									? arguments[1]
									: {},
							n = null,
							o = null,
							i = null,
							c = null;
						if (
							((at = !e),
							at && (e = "\x3c!--\x3e"),
							"string" != typeof e && !Ot(e))
						) {
							if ("function" != typeof e.toString)
								throw E("toString is not a function");
							if ("string" != typeof (e = e.toString()))
								throw E("dirty is not a string, aborting");
						}
						if (!r.isSupported) return e;
						if (
							(Fe || bt(t),
							(r.removed = []),
							"string" == typeof e && ($e = !1),
							$e)
						) {
							if (e.nodeName) {
								const t = ht(e.nodeName);
								if (!Se[t] || Me[t])
									throw E(
										"root node is forbidden and cannot be sanitized in-place",
									);
							}
						} else if (e instanceof S)
							(n = St("\x3c!----\x3e")),
								(o = n.ownerDocument.importNode(e, !0)),
								(o.nodeType === J && "BODY" === o.nodeName) ||
								"HTML" === o.nodeName
									? (n = o)
									: n.appendChild(o);
						else {
							if (!He && !De && !je && -1 === e.indexOf("<"))
								return se && Be ? se.createHTML(e) : e;
							if (((n = St(e)), !n)) return He ? null : Be ? ue : "";
						}
						n && ze && Et(n.firstChild);
						const s = xt($e ? e : n);
						for (; (i = s.nextNode()); )
							Mt(i), It(i), i.content instanceof u && kt(i.content);
						if ($e) return e;
						if (He) {
							if (We)
								for (c = me.call(n.ownerDocument); n.firstChild; )
									c.appendChild(n.firstChild);
							else c = n;
							return (
								(Ne.shadowroot || Ne.shadowrootmode) && (c = pe.call(a, c, !0)),
								c
							);
						}
						let f = je ? n.outerHTML : n.innerHTML;
						return (
							je &&
								Se["!doctype"] &&
								n.ownerDocument &&
								n.ownerDocument.doctype &&
								n.ownerDocument.doctype.name &&
								A(V, n.ownerDocument.doctype.name) &&
								(f = "<!DOCTYPE " + n.ownerDocument.doctype.name + ">\n" + f),
							De &&
								l([ge, ye, ve], (e) => {
									f = v(f, e, " ");
								}),
							se && Be ? se.createHTML(f) : f
						);
					}),
					(r.setConfig = function () {
						let e =
							arguments.length > 0 && void 0 !== arguments[0]
								? arguments[0]
								: {};
						bt(e), (Fe = !0);
					}),
					(r.clearConfig = function () {
						(gt = null), (Fe = !1);
					}),
					(r.isValidAttribute = function (e, t, n) {
						gt || bt({});
						const r = ht(e),
							o = ht(t);
						return Lt(r, o, n);
					}),
					(r.addHook = function (e, t) {
						"function" == typeof t && d(he[e], t);
					}),
					(r.removeHook = function (e, t) {
						if (void 0 !== t) {
							const n = f(he[e], t);
							return -1 === n ? void 0 : p(he[e], n, 1)[0];
						}
						return m(he[e]);
					}),
					(r.removeHooks = function (e) {
						he[e] = [];
					}),
					(r.removeAllHooks = function () {
						he = {
							afterSanitizeAttributes: [],
							afterSanitizeElements: [],
							afterSanitizeShadowDOM: [],
							beforeSanitizeAttributes: [],
							beforeSanitizeElements: [],
							beforeSanitizeShadowDOM: [],
							uponSanitizeAttribute: [],
							uponSanitizeElement: [],
							uponSanitizeShadowNode: [],
						};
					}),
					r
				);
			})();
		})();
	},
	function (e, t, n) {
		e.exports = n(8);
	},
	function (e, t, n) {
		"use strict";
		(function (e, t) {
			function r(e) {
				return (r =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			!(function (e) {
				e.createPromiseCapability = function () {
					var e = {};
					return (
						(e.promise = new w(function (t, n) {
							(e.resolve = t), (e.reject = n);
						})),
						e
					);
				};
				var o = e.Promise,
					i =
						o &&
						"resolve" in o &&
						"reject" in o &&
						"all" in o &&
						"race" in o &&
						(function () {
							var e;
							return (
								new o(function (t) {
									e = t;
								}),
								"function" == typeof e
							);
						})();
				"undefined" != typeof exports && exports
					? ((exports.Promise = i ? o : w), (exports.Polyfill = w))
					: "function" == typeof define && n(7)
						? define(function () {
								return i ? o : w;
							})
						: i || (e.Promise = w);
				var a = function () {};
				function c(e) {
					return "[object Array]" === Object.prototype.toString.call(e);
				}
				var s,
					u = void 0 !== t ? t : setTimeout,
					l = [];
				function f() {
					for (var e = 0; e < l.length; e++) l[e][0](l[e][1]);
					(l = []), (s = !1);
				}
				function m(e, t) {
					l.push([e, t]), s || ((s = !0), u(f, 0));
				}
				function d(e) {
					var t = e.owner,
						n = t.state_,
						r = t.data_,
						o = e[n],
						i = e.then;
					if ("function" == typeof o) {
						n = "fulfilled";
						try {
							r = o(r);
						} catch (e) {
							y(i, e);
						}
					}
					p(i, r) ||
						("fulfilled" === n && h(i, r), "rejected" === n && y(i, r));
				}
				function p(e, t) {
					var n;
					try {
						if (e === t)
							throw new TypeError(
								"A promises callback cannot return that same promise.",
							);
						if (t && ("function" == typeof t || "object" === r(t))) {
							var o = t.then;
							if ("function" == typeof o)
								return (
									o.call(
										t,
										function (r) {
											n || ((n = !0), t !== r ? h(e, r) : g(e, r));
										},
										function (t) {
											n || ((n = !0), y(e, t));
										},
									),
									!0
								);
						}
					} catch (t) {
						return n || y(e, t), !0;
					}
					return !1;
				}
				function h(e, t) {
					(e !== t && p(e, t)) || g(e, t);
				}
				function g(e, t) {
					"pending" === e.state_ &&
						((e.state_ = "sealed"), (e.data_ = t), m(b, e));
				}
				function y(e, t) {
					"pending" === e.state_ &&
						((e.state_ = "sealed"), (e.data_ = t), m(T, e));
				}
				function v(e) {
					var t = e.then_;
					e.then_ = void 0;
					for (var n = 0; n < t.length; n++) d(t[n]);
				}
				function b(e) {
					(e.state_ = "fulfilled"), v(e);
				}
				function T(e) {
					(e.state_ = "rejected"), v(e);
				}
				function w(e) {
					if ("function" != typeof e)
						throw new TypeError(
							"Promise constructor takes a function argument",
						);
					if (!(this instanceof w))
						throw new TypeError(
							"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.",
						);
					(this.then_ = []),
						(function (e, t) {
							function n(e) {
								y(t, e);
							}
							try {
								e(function (e) {
									h(t, e);
								}, n);
							} catch (e) {
								n(e);
							}
						})(e, this);
				}
				(w.prototype = {
					constructor: w,
					state_: "pending",
					then_: null,
					data_: void 0,
					then: function (e, t) {
						var n = {
							owner: this,
							then: new this.constructor(a),
							fulfilled: e,
							rejected: t,
						};
						return (
							"fulfilled" === this.state_ || "rejected" === this.state_
								? m(d, n)
								: this.then_.push(n),
							n.then
						);
					},
					catch: function (e) {
						return this.then(null, e);
					},
				}),
					(w.all = function (e) {
						if (!c(e))
							throw new TypeError("You must pass an array to Promise.all().");
						return new this(function (t, n) {
							var r = [],
								o = 0;
							function i(e) {
								return (
									o++,
									function (n) {
										(r[e] = n), --o || t(r);
									}
								);
							}
							for (var a, c = 0; c < e.length; c++)
								(a = e[c]) && "function" == typeof a.then
									? a.then(i(c), n)
									: (r[c] = a);
							o || t(r);
						});
					}),
					(w.race = function (e) {
						if (!c(e))
							throw new TypeError("You must pass an array to Promise.race().");
						return new this(function (t, n) {
							for (var r, o = 0; o < e.length; o++)
								(r = e[o]) && "function" == typeof r.then ? r.then(t, n) : t(r);
						});
					}),
					(w.resolve = function (e) {
						return e && "object" === r(e) && e.constructor === this
							? e
							: new this(function (t) {
									t(e);
								});
					}),
					(w.reject = function (e) {
						return new this(function (t, n) {
							n(e);
						});
					});
			})(
				"undefined" != typeof window
					? window
					: void 0 !== e
						? e
						: "undefined" != typeof self
							? self
							: void 0,
			);
		}).call(this, n(0), n(4).setImmediate);
	},
	function (e, t, n) {
		(function (e) {
			var r =
					(void 0 !== e && e) || ("undefined" != typeof self && self) || window,
				o = Function.prototype.apply;
			function i(e, t) {
				(this._id = e), (this._clearFn = t);
			}
			(t.setTimeout = function () {
				return new i(o.call(setTimeout, r, arguments), clearTimeout);
			}),
				(t.setInterval = function () {
					return new i(o.call(setInterval, r, arguments), clearInterval);
				}),
				(t.clearTimeout = t.clearInterval =
					function (e) {
						e && e.close();
					}),
				(i.prototype.unref = i.prototype.ref = function () {}),
				(i.prototype.close = function () {
					this._clearFn.call(r, this._id);
				}),
				(t.enroll = function (e, t) {
					clearTimeout(e._idleTimeoutId), (e._idleTimeout = t);
				}),
				(t.unenroll = function (e) {
					clearTimeout(e._idleTimeoutId), (e._idleTimeout = -1);
				}),
				(t._unrefActive = t.active =
					function (e) {
						clearTimeout(e._idleTimeoutId);
						var t = e._idleTimeout;
						t >= 0 &&
							(e._idleTimeoutId = setTimeout(function () {
								e._onTimeout && e._onTimeout();
							}, t));
					}),
				n(5),
				(t.setImmediate =
					("undefined" != typeof self && self.setImmediate) ||
					(void 0 !== e && e.setImmediate) ||
					(this && this.setImmediate)),
				(t.clearImmediate =
					("undefined" != typeof self && self.clearImmediate) ||
					(void 0 !== e && e.clearImmediate) ||
					(this && this.clearImmediate));
		}).call(this, n(0));
	},
	function (e, t, n) {
		(function (e, t) {
			!(function (e, n) {
				"use strict";
				if (!e.setImmediate) {
					var r,
						o,
						i,
						a,
						c,
						s = 1,
						u = {},
						l = !1,
						f = e.document,
						m = Object.getPrototypeOf && Object.getPrototypeOf(e);
					(m = m && m.setTimeout ? m : e),
						"[object process]" === {}.toString.call(e.process)
							? (r = function (e) {
									t.nextTick(function () {
										p(e);
									});
								})
							: !(function () {
										if (e.postMessage && !e.importScripts) {
											var t = !0,
												n = e.onmessage;
											return (
												(e.onmessage = function () {
													t = !1;
												}),
												e.postMessage("", "*"),
												(e.onmessage = n),
												t
											);
										}
									})()
								? e.MessageChannel
									? (((i = new MessageChannel()).port1.onmessage = function (
											e,
										) {
											p(e.data);
										}),
										(r = function (e) {
											i.port2.postMessage(e);
										}))
									: f && "onreadystatechange" in f.createElement("script")
										? ((o = f.documentElement),
											(r = function (e) {
												var t = f.createElement("script");
												(t.onreadystatechange = function () {
													p(e),
														(t.onreadystatechange = null),
														o.removeChild(t),
														(t = null);
												}),
													o.appendChild(t);
											}))
										: (r = function (e) {
												setTimeout(p, 0, e);
											})
								: ((a = "setImmediate$" + Math.random() + "$"),
									(c = function (t) {
										t.source === e &&
											"string" == typeof t.data &&
											0 === t.data.indexOf(a) &&
											p(+t.data.slice(a.length));
									}),
									e.addEventListener
										? e.addEventListener("message", c, !1)
										: e.attachEvent("onmessage", c),
									(r = function (t) {
										e.postMessage(a + t, "*");
									})),
						(m.setImmediate = function (e) {
							"function" != typeof e && (e = new Function("" + e));
							for (
								var t = new Array(arguments.length - 1), n = 0;
								n < t.length;
								n++
							)
								t[n] = arguments[n + 1];
							var o = { callback: e, args: t };
							return (u[s] = o), r(s), s++;
						}),
						(m.clearImmediate = d);
				}
				function d(e) {
					delete u[e];
				}
				function p(e) {
					if (l) setTimeout(p, 0, e);
					else {
						var t = u[e];
						if (t) {
							l = !0;
							try {
								!(function (e) {
									var t = e.callback,
										n = e.args;
									switch (n.length) {
										case 0:
											t();
											break;
										case 1:
											t(n[0]);
											break;
										case 2:
											t(n[0], n[1]);
											break;
										case 3:
											t(n[0], n[1], n[2]);
											break;
										default:
											t.apply(void 0, n);
									}
								})(t);
							} finally {
								d(e), (l = !1);
							}
						}
					}
				}
			})("undefined" == typeof self ? (void 0 === e ? this : e) : self);
		}).call(this, n(0), n(6));
	},
	function (e, t) {
		var n,
			r,
			o = (e.exports = {});
		function i() {
			throw new Error("setTimeout has not been defined");
		}
		function a() {
			throw new Error("clearTimeout has not been defined");
		}
		function c(e) {
			if (n === setTimeout) return setTimeout(e, 0);
			if ((n === i || !n) && setTimeout)
				return (n = setTimeout), setTimeout(e, 0);
			try {
				return n(e, 0);
			} catch (t) {
				try {
					return n.call(null, e, 0);
				} catch (t) {
					return n.call(this, e, 0);
				}
			}
		}
		!(function () {
			try {
				n = "function" == typeof setTimeout ? setTimeout : i;
			} catch (e) {
				n = i;
			}
			try {
				r = "function" == typeof clearTimeout ? clearTimeout : a;
			} catch (e) {
				r = a;
			}
		})();
		var s,
			u = [],
			l = !1,
			f = -1;
		function m() {
			l &&
				s &&
				((l = !1), s.length ? (u = s.concat(u)) : (f = -1), u.length && d());
		}
		function d() {
			if (!l) {
				var e = c(m);
				l = !0;
				for (var t = u.length; t; ) {
					for (s = u, u = []; ++f < t; ) s && s[f].run();
					(f = -1), (t = u.length);
				}
				(s = null),
					(l = !1),
					(function (e) {
						if (r === clearTimeout) return clearTimeout(e);
						if ((r === a || !r) && clearTimeout)
							return (r = clearTimeout), clearTimeout(e);
						try {
							r(e);
						} catch (t) {
							try {
								return r.call(null, e);
							} catch (t) {
								return r.call(this, e);
							}
						}
					})(e);
			}
		}
		function p(e, t) {
			(this.fun = e), (this.array = t);
		}
		function h() {}
		(o.nextTick = function (e) {
			var t = new Array(arguments.length - 1);
			if (arguments.length > 1)
				for (var n = 1; n < arguments.length; n++) t[n - 1] = arguments[n];
			u.push(new p(e, t)), 1 !== u.length || l || c(d);
		}),
			(p.prototype.run = function () {
				this.fun.apply(null, this.array);
			}),
			(o.title = "browser"),
			(o.browser = !0),
			(o.env = {}),
			(o.argv = []),
			(o.version = ""),
			(o.versions = {}),
			(o.on = h),
			(o.addListener = h),
			(o.once = h),
			(o.off = h),
			(o.removeListener = h),
			(o.removeAllListeners = h),
			(o.emit = h),
			(o.prependListener = h),
			(o.prependOnceListener = h),
			(o.listeners = function (e) {
				return [];
			}),
			(o.binding = function (e) {
				throw new Error("process.binding is not supported");
			}),
			(o.cwd = function () {
				return "/";
			}),
			(o.chdir = function (e) {
				throw new Error("process.chdir is not supported");
			}),
			(o.umask = function () {
				return 0;
			});
	},
	function (e, t) {
		(function (t) {
			e.exports = t;
		}).call(this, {});
	},
	function (e, t, n) {
		"use strict";
		n.r(t);
		n(3),
			(function (e) {
				void 0 === e.crypto &&
					(e.crypto = {
						getRandomValues: function (e) {
							for (var t = 0; t < e.length; t++) e[t] = 256 * Math.random();
						},
					});
			})("undefined" == typeof window ? self : window),
			console.log,
			console.warn,
			console.error;
		var r = function () {
			return (r =
				Object.assign ||
				function (e) {
					for (var t, n = 1, r = arguments.length; n < r; n++)
						for (var o in (t = arguments[n]))
							Object.prototype.hasOwnProperty.call(t, o) && (e[o] = t[o]);
					return e;
				}).apply(this, arguments);
		};
		function o(e, t, n, r) {
			return new (n || (n = Promise))(function (o, i) {
				function a(e) {
					try {
						s(r.next(e));
					} catch (e) {
						i(e);
					}
				}
				function c(e) {
					try {
						s(r.throw(e));
					} catch (e) {
						i(e);
					}
				}
				function s(e) {
					var t;
					e.done
						? o(e.value)
						: ((t = e.value),
							t instanceof n
								? t
								: new n(function (e) {
										e(t);
									})).then(a, c);
				}
				s((r = r.apply(e, t || [])).next());
			});
		}
		function i(e, t) {
			var n,
				r,
				o,
				i = {
					label: 0,
					sent: function () {
						if (1 & o[0]) throw o[1];
						return o[1];
					},
					trys: [],
					ops: [],
				},
				a = Object.create(
					("function" == typeof Iterator ? Iterator : Object).prototype,
				);
			return (
				(a.next = c(0)),
				(a.throw = c(1)),
				(a.return = c(2)),
				"function" == typeof Symbol &&
					(a[Symbol.iterator] = function () {
						return this;
					}),
				a
			);
			function c(c) {
				return function (s) {
					return (function (c) {
						if (n) throw new TypeError("Generator is already executing.");
						for (; a && ((a = 0), c[0] && (i = 0)), i; )
							try {
								if (
									((n = 1),
									r &&
										(o =
											2 & c[0]
												? r.return
												: c[0]
													? r.throw || ((o = r.return) && o.call(r), 0)
													: r.next) &&
										!(o = o.call(r, c[1])).done)
								)
									return o;
								switch (((r = 0), o && (c = [2 & c[0], o.value]), c[0])) {
									case 0:
									case 1:
										o = c;
										break;
									case 4:
										return i.label++, { value: c[1], done: !1 };
									case 5:
										i.label++, (r = c[1]), (c = [0]);
										continue;
									case 7:
										(c = i.ops.pop()), i.trys.pop();
										continue;
									default:
										if (
											!((o = i.trys),
											(o = o.length > 0 && o[o.length - 1]) ||
												(6 !== c[0] && 2 !== c[0]))
										) {
											i = 0;
											continue;
										}
										if (3 === c[0] && (!o || (c[1] > o[0] && c[1] < o[3]))) {
											i.label = c[1];
											break;
										}
										if (6 === c[0] && i.label < o[1]) {
											(i.label = o[1]), (o = c);
											break;
										}
										if (o && i.label < o[2]) {
											(i.label = o[2]), i.ops.push(c);
											break;
										}
										o[2] && i.ops.pop(), i.trys.pop();
										continue;
								}
								c = t.call(e, i);
							} catch (e) {
								(c = [6, e]), (r = 0);
							} finally {
								n = o = 0;
							}
						if (5 & c[0]) throw c[1];
						return { value: c[0] ? c[1] : void 0, done: !0 };
					})([c, s]);
				};
			}
		}
		Object.create;
		Object.create;
		"function" == typeof SuppressedError && SuppressedError;
		var a = {
				flattenedResources: !1,
				CANVAS_CACHE_SIZE: void 0,
				maxPagesBefore: void 0,
				maxPagesAhead: void 0,
				disableLogs: !1,
				wvsQueryParameters: {},
				_trnDebugMode: !1,
				_logFiltersEnabled: null,
			},
			c = function (e) {
				return a[e];
			},
			s = function (e, t) {
				c("disableLogs") ||
					(t ? console.warn("".concat(e, ": ").concat(t)) : console.warn(e));
			};
		function u(e, t, n) {
			return new Promise(function (r) {
				if (!e) return r();
				var o = n.document.createElement("script");
				(o.type = "text/javascript"),
					(o.onload = function () {
						r();
					}),
					(o.onerror = function () {
						t && s(t), r();
					}),
					(o.src = e),
					n.document.getElementsByTagName("head")[0].appendChild(o);
			});
		}
		var l,
			f = function (e) {
				if ("string" == typeof e) {
					for (
						var t = new Uint8Array(e.length), n = e.length, r = 0;
						r < n;
						r++
					)
						t[r] = e.charCodeAt(r);
					return t;
				}
				return e;
			},
			m = function (e) {
				if ("string" != typeof e) {
					for (var t = "", n = 0, r = e.length, o = void 0; n < r; )
						(o = e.subarray(n, n + 1024)),
							(n += 1024),
							(t += String.fromCharCode.apply(null, o));
					return t;
				}
				return e;
			},
			d = n(1),
			p = n.n(d),
			h = new Map(),
			g = function () {
				return ("undefined" == typeof window ? self : window).trustedTypes;
			},
			y = function (e, t) {
				var n,
					o = {
						createHTML: function (e) {
							return p.a.sanitize(e, r({}, t));
						},
						createScript: function (e) {
							return e;
						},
						createScriptURL: function (e) {
							return e;
						},
					};
				if (null === (n = g()) || void 0 === n ? void 0 : n.createPolicy) {
					var i = "".concat(e, "-po");
					h.has(e) ||
						((o = (function (e) {
							return g().createPolicy(e, {
								createHTML: function (e) {
									return e;
								},
								createScript: function (e) {
									return e;
								},
								createScriptURL: function (e) {
									return e;
								},
							});
						})(i)),
						h.set(i, o),
						(o = (function (e, t) {
							return g().createPolicy(e, {
								createHTML: function (e) {
									return p.a.sanitize(
										e,
										r(r({}, t), { RETURN_TRUSTED_TYPE: !1 }),
									);
								},
								createScript: function (e) {
									return e;
								},
								createScriptURL: function (e) {
									return e;
								},
							});
						})(e, t)),
						h.set(e, o));
					var a = t.createPolicyOnly ? i : e;
					o = h.get(a);
				}
				return o;
			},
			v = function (e, t) {
				return (
					void 0 === t &&
						(t = { createPolicyOnly: !0, trustedTypesPolicyName: "webviewer" }),
					y(t.trustedTypesPolicyName, t).createScriptURL(e.toString())
				);
			},
			b = "undefined" == typeof window ? self : window,
			T = b.importScripts,
			w = !1,
			A = function (e, t) {
				w || (T(v("".concat(b.basePath, "decode.min.js"))), (w = !0));
				var n = f(e),
					r = self.BrotliDecode(n);
				return t ? r : m(r);
			},
			E = function (e, t) {
				return o(void 0, void 0, Promise, function () {
					var n;
					return i(this, function (r) {
						switch (r.label) {
							case 0:
								return w
									? [3, 2]
									: [
											4,
											u(
												"".concat(
													self.Core.getWorkerPath(),
													"external/decode.min.js",
												),
												"Failed to download decode.min.js",
												window,
											),
										];
							case 1:
								r.sent(), (w = !0), (r.label = 2);
							case 2:
								return (n = self.BrotliDecode(f(e))), [2, t ? n : m(n)];
						}
					});
				});
			},
			_ =
				((function () {
					function e() {
						this.remainingDataArrays = [];
					}
					(e.prototype.processRaw = function (e) {
						return e;
					}),
						(e.prototype.processBrotli = function (e) {
							return this.remainingDataArrays.push(e), null;
						}),
						(e.prototype.GetNextChunk = function (e) {
							return (
								this.decodeFunction ||
									(0 === e[0] && 97 === e[1] && 115 === e[2] && 109 === e[3]
										? (this.decodeFunction = this.processRaw)
										: (this.decodeFunction = this.processBrotli)),
								this.decodeFunction(e)
							);
						}),
						(e.prototype.End = function () {
							if (this.remainingDataArrays.length) {
								for (var e = this.arrays, t = 0, n = 0; n < e.length; ++n)
									t += e[n].length;
								var r = new Uint8Array(t),
									o = 0;
								for (n = 0; n < e.length; ++n) {
									var i = e[n];
									r.set(i, o), (o += i.length);
								}
								return A(r, !0);
							}
							return null;
						});
				})(),
				function (e, t, n) {
					void 0 === t && (t = !0), void 0 === n && (n = !1);
					var r = new XMLHttpRequest();
					r.open("GET", e, t);
					var o = n && r.overrideMimeType;
					return (
						(r.responseType = o ? "text" : "arraybuffer"),
						o && r.overrideMimeType("text/plain; charset=x-user-defined"),
						r
					);
				}),
			S = function (e, t) {
				var n = t.decompressFunction,
					r = t.shouldOutputArray,
					o = t.compressedMaximum,
					i = void 0 !== T ? Date.now() : null;
				try {
					var a = r ? O(e) : R(e);
					return (
						"Result length is ".concat(a.length),
						a.length < o ? (a = x(a, n, r)) : r || (a = m(a)),
						T && N(t.paths, i),
						a
					);
				} catch (e) {
					throw new Error("Failed to decompress: ".concat(e));
				}
			},
			x = function (e, t, n) {
				var r = t(e, n);
				return (
					s(
						"There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.",
					),
					T && "Decompressed length is ".concat(r.length),
					r
				);
			},
			N = function (e, t) {
				var n = e.join(", ");
				"".concat(n, " Decompression took ").concat(Date.now() - t, " ms");
			},
			O = function (e) {
				var t = e.reduce(function (e, t) {
					var n = new Uint8Array(t);
					return e.concat(Array.from(n));
				}, []);
				return new Uint8Array(t);
			},
			R = function (e) {
				return e.join("");
			},
			M = function (e) {
				var t,
					n = !e.shouldOutputArray,
					r = e.paths,
					o = e.isAsync;
				if (o)
					t = Promise.all(
						r.map(function (e) {
							return (function (e, t, n) {
								return new Promise(function (r, o) {
									var i = _(e, t, n);
									i.send(),
										(i.onload = function () {
											200 === this.status || 0 === this.status
												? r(i.response)
												: o(new Error("Download Failed ".concat(e)));
										}),
										(i.onerror = function () {
											o(new Error("Network error occurred ".concat(e)));
										});
								});
							})(e, o, n);
						}),
					)
						.then(function (t) {
							return S(t, e);
						})
						.catch(function (e) {
							throw new Error(
								"Failed to fetch or decompress files: ".concat(e.message),
							);
						});
				else {
					var i = r.map(function (e) {
						var t = _(e, o, n);
						if ((t.send(), 200 === t.status || 0 === t.status))
							return t.response;
						throw new Error("Failed to load ".concat(e));
					});
					t = S(i, e);
				}
				return t;
			},
			L = function (e) {
				var t = e.lastIndexOf("/");
				-1 === t && (t = 0);
				var n = e.slice(t).replace(".", ".br.");
				return (
					T ||
						(n.endsWith(".js.mem")
							? (n = n.replace(".js.mem", ".mem"))
							: n.endsWith(".js") && (n = n.concat(".mem"))),
					e.slice(0, t) + n
				);
			},
			P = function (e, t) {
				return (
					(t.decompressFunction = T ? A : E),
					(t.paths = e.map(function (e) {
						return L(e);
					})),
					M(t)
				);
			},
			I = function (e, t, n, r) {
				return e.catch(function (e) {
					return s(e), r(t, n);
				});
			},
			k = function (e, t, n, r) {
				return (function (e, t, n) {
					if (n.isAsync) {
						for (var r = t[0](e, n), o = 1; o < t.length; ++o)
							r = I(r, e, n, t[o]);
						return r;
					}
					for (var i = 0, a = t; i < a.length; i++) {
						var c = a[i];
						try {
							return c(e, n);
						} catch (e) {
							s(e.message);
						}
					}
					throw new Error("None of the worker files were able to load. ");
				})(Array.isArray(e) ? e : [e], [P], {
					compressedMaximum: t,
					isAsync: n,
					shouldOutputArray: r,
				});
			};
		function C(e, t, n, r) {
			return (function e(t, n, r, o, i, a, c) {
				if (((a = a || Date.now()), i && !o))
					return fetch(L(t))
						.then(function (e) {
							return WebAssembly.instantiateStreaming(e, n);
						})
						.catch(function (i) {
							return (
								"instantiateStreaming Failed "
									.concat(t, " message ")
									.concat(i.message),
								e(t, n, r, o, !1, a, c)
							);
						});
				var s = o
					? o.map(function (e, t) {
							return "".concat(e, "PDFNetCWasm-chunk-").concat(t, ".wasm");
						})
					: t;
				return k(s, r, !0, !0).then(function (e) {
					return (
						(c = Date.now()),
						"Request took ".concat(c - a, " ms"),
						WebAssembly.instantiate(e, n)
					);
				});
			})(e, t, n, r, !!WebAssembly.instantiateStreaming, void 0, void 0).then(
				function (e) {
					return "WASM compilation took ".concat(Date.now() - void 0, " ms"), e;
				},
			);
		}
		var D,
			U,
			j,
			F,
			z,
			H = "undefined" == typeof window ? self : window,
			W =
				((D = navigator.userAgent.toLowerCase()),
				(U =
					/(msie) ([\w.]+)/.exec(D) || /(trident)(?:.*? rv:([\w.]+)|)/.exec(D))
					? parseInt(U[2], 10)
					: U),
			B =
				((j = H.navigator.userAgent.match(/OPR/)),
				(F = H.navigator.userAgent.match(/Maxthon/)),
				(z = H.navigator.userAgent.match(/Edge/)),
				H.navigator.userAgent.match(/Chrome\/(.*?) /) && !j && !F && !z),
			G =
				((function () {
					if (!B) return null;
					var e = H.navigator.userAgent.match(/Chrome\/([0-9]+)\./);
					e && parseInt(e[1], 10);
				})(),
				!!navigator.userAgent.match(/Edge/i) ||
					(navigator.userAgent.match(/Edg\/(.*?)/) &&
						H.navigator.userAgent.match(/Chrome\/(.*?) /))),
			Y =
				((function () {
					if (!G) return null;
					var e = H.navigator.userAgent.match(/Edg\/([0-9]+)\./);
					e && parseInt(e[1], 10);
				})(),
				/iPad|iPhone|iPod/.test(H.navigator.platform) ||
					("MacIntel" === navigator.platform && navigator.maxTouchPoints > 1) ||
					/iPad|iPhone|iPod/.test(H.navigator.userAgent)),
			X = (function () {
				var e = H.navigator.userAgent.match(
					/.*\/([0-9\.]+)\s(Safari|Mobile).*/i,
				);
				return e ? parseFloat(e[1]) : e;
			})(),
			q =
				/^((?!chrome|android).)*safari/i.test(H.navigator.userAgent) ||
				(/^((?!chrome|android).)*$/.test(H.navigator.userAgent) && Y),
			$ =
				(q &&
					/^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
					parseInt(
						null === (l = navigator.userAgent.match(/Version\/(\d+)/)) ||
							void 0 === l
							? void 0
							: l[1],
						10,
					),
				H.navigator.userAgent.match(/Firefox/)),
			V =
				((function () {
					if (!$) return null;
					var e = H.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
					e && parseInt(e[1], 10);
				})(),
				W || /Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent),
				navigator.userAgent.match(/(iPad|iPhone|iPod)/i),
				H.navigator.userAgent.indexOf("Android"),
				/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(H.navigator.userAgent)),
			K =
				!!H.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i) &&
				parseInt(
					H.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],
					10,
				) >= 14,
			Z = !(!self.WebAssembly || !self.WebAssembly.validate),
			J =
				H.navigator.userAgent.indexOf("Edge/16") > -1 ||
				H.navigator.userAgent.indexOf("MSAppHost") > -1,
			Q = function () {
				return Z && !J && !(!K && ((q && null !== X && X < 14) || V));
			};
		var ee = (function () {
			function e(e) {
				var t = this;
				this.promise = e.then(function (e) {
					(t.response = e), (t.status = 200);
				});
			}
			return (
				(e.prototype.addEventListener = function (e, t) {
					this.promise.then(t);
				}),
				e
			);
		})();
		function te(e) {
			return (te =
				"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
					? function (e) {
							return typeof e;
						}
					: function (e) {
							return e &&
								"function" == typeof Symbol &&
								e.constructor === Symbol &&
								e !== Symbol.prototype
								? "symbol"
								: typeof e;
						})(e);
		}
		var ne = null;
		!(function (e) {
			var t,
				n,
				r,
				o,
				i = [];
			function a(e) {
				i || (i = []), i.push(e);
			}
			var c = function () {
				e.basePath = "../";
				var a,
					c,
					s = e.legacyOfficeWorkerPath || "";
				function u(e) {
					var t = (function (e) {
						var t = [];
						return {
							resource_array: t,
							msg: JSON.stringify(e.data, function (e, n) {
								if ("object" === te(n)) {
									var i = null;
									if (
										(n instanceof Uint8Array
											? (i = n)
											: n instanceof ArrayBuffer && (i = new Uint8Array(n)),
										i)
									) {
										var a = r(i.length),
											c = o(a);
										if (c)
											new Uint8Array(Module.HEAPU8.buffer, c, i.length).set(i);
										return t.push(a), { __trn_res_id: a };
									}
								}
								return n;
							}),
						};
					})(e);
					n(t.msg);
				}
				e.workerBasePath && (e.basePath = e.workerBasePath),
					e.externalPath
						? (e.basePath = e.externalPath)
						: (e.basePath += "external/"),
					importScripts("".concat(e.basePath, "Promise.js")),
					(e.ContinueFunc = function (e) {
						t("ContinueFunc called"),
							setTimeout(function () {
								onmessage({ data: { action: "continue" } });
							}, e);
					}),
					e.pdfWorkerPath && (a = e.pdfWorkerPath),
					e.officeAsmPath && (c = e.officeAsmPath),
					(e.Module = {
						memoryInitializerPrefixURL: a,
						asmjsPrefix: c,
						onRuntimeInitialized: function () {
							t || (t = function () {});
							var e = Date.now() - ne;
							"time duration from start to ready: ".concat(JSON.stringify(e)),
								(n = function (e) {
									if (null != e && 0 !== e) {
										var t = 1 + (e.length << 2),
											n = Module._malloc(t);
										stringToUTF8(e, n, t) > 0 && Module._TRN_OnMessage(n);
									}
								}),
								(r = function (e) {
									return Module._TRN_CreateBufferResource(e);
								}),
								(o = function (e) {
									return Module._TRN_GetResourcePointer(e);
								}),
								t("OnReady called"),
								(onmessage = u),
								Module._TRN_InitWorker();
							for (var a = 0; a < i.length; ++a) onmessage(i[a]);
							i = null;
						},
						fetchSelf: function () {
							(ne = Date.now()),
								(function (e, t, n, r) {
									var o, i;
									if (Q() && !n) {
										if (
											((self.Module.instantiateWasm = function (n, o) {
												return C(
													"".concat(e, "Wasm.wasm"),
													n,
													t["Wasm.wasm"],
													r,
												).then(function (e) {
													o(e.instance);
												});
											}),
											t.disableObjectURLBlobs)
										)
											return void importScripts("".concat(e, "Wasm.js"));
										(o = k(
											"".concat(e, "Wasm.js.mem"),
											t["Wasm.js.mem"],
											!1,
											!1,
										)),
											(i = new Blob([o], { type: "application/javascript" })),
											importScripts(v(URL.createObjectURL(i)));
									} else {
										if (t.disableObjectURLBlobs)
											return void importScripts(
												"".concat(
													(self.Module.asmjsPrefix
														? self.Module.asmjsPrefix
														: "") + e,
													".js",
												),
											);
										o = k(
											"".concat(
												(self.Module.asmjsPrefix
													? self.Module.asmjsPrefix
													: "") + e,
												".js.mem",
											),
											t[".js.mem"],
											!1,
										);
										var a = k(
											"".concat(
												(self.Module.memoryInitializerPrefixURL
													? self.Module.memoryInitializerPrefixURL
													: "") + e,
												".mem",
											),
											t[".mem"],
											!0,
											!0,
										);
										(self.Module.memoryInitializerRequest = new ee(a)),
											(i = new Blob([o], { type: "application/javascript" })),
											importScripts(v(URL.createObjectURL(i)));
									}
								})(
									"".concat(s, "WebB2XOfficeWorker"),
									{
										"Wasm.wasm": 5e6,
										"Wasm.js.mem": 1e5,
										".js.mem": 5e6,
										".mem": 5e5,
										disableObjectURLBlobs: e.disableObjectURLBlobs,
									},
									!!navigator.userAgent.match(/Edge/i) || e.wasmDisabled,
								);
						},
						noExitRuntime: !0,
					});
			};
			e.onmessage = function (t) {
				"init" === t.data.action &&
					((e.wasmDisabled = !t.data.wasm),
					(e.externalPath = t.data.externalPath),
					(e.officeAsmPath = t.data.officeAsmPath),
					(e.pdfWorkerPath = t.data.pdfWorkerPath),
					(e.disableObjectURLBlobs = t.data.disableObjectURLBlobs),
					(e.onmessage = a),
					c(),
					e.Module.fetchSelf());
			};
		})("undefined" == typeof window ? self : window);
		ArrayBuffer.prototype.slice ||
			(ArrayBuffer.prototype.slice = function (e, t) {
				if (
					(void 0 === e && (e = 0),
					void 0 === t && (t = this.byteLength),
					(e = Math.floor(e)),
					(t = Math.floor(t)),
					e < 0 && (e += this.byteLength),
					t < 0 && (t += this.byteLength),
					(e = Math.min(Math.max(0, e), this.byteLength)),
					(t = Math.min(Math.max(0, t), this.byteLength)) - e <= 0)
				)
					return new ArrayBuffer(0);
				var n = new ArrayBuffer(t - e),
					r = new Uint8Array(n),
					o = new Uint8Array(this, e, t - e);
				return r.set(o), n;
			});
	},
]);
