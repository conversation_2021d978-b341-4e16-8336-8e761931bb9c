/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[6],
		{
			632: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(366);
				ya = r(624);
				r(36);
				r = r(547);
				var ma = (function (ja) {
					function ia(ca, y) {
						var x = ja.call(this, ca, y) || this;
						x.url = ca;
						x.range = y;
						x.status = na.a.NOT_STARTED;
						return x;
					}
					Object(la.c)(ia, ja);
					ia.prototype.start = function () {
						var ca = document.createElement("IFRAME");
						ca.setAttribute("src", this.url);
						document.documentElement.appendChild(ca);
						ca.parentNode.removeChild(ca);
						this.status = na.a.STARTED;
						this.oM();
					};
					return ia;
				})(ya.ByteRangeRequest);
				ya = (function (ja) {
					function ia(ca, y, x, n) {
						ca = ja.call(this, ca, y, x, n) || this;
						ca.CG = ma;
						return ca;
					}
					Object(la.c)(ia, ja);
					ia.prototype.DD = function (ca, y) {
						return ""
							.concat(ca, "#")
							.concat(y.start, "&")
							.concat(y.stop ? y.stop : "");
					};
					return ia;
				})(ya["default"]);
				Object(r.a)(ya);
				Object(r.b)(ya);
				va["default"] = ya;
			},
		},
	]);
}).call(this || window);
