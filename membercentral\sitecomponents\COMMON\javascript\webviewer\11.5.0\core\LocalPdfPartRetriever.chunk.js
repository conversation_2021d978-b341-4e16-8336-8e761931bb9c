/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[8],
		{
			627: function (ya, va, r) {
				r.r(va);
				var la = r(0);
				ya = r(53);
				var na = r(547),
					ma = r(308),
					ja = r(26),
					ia = window;
				r = (function () {
					function ca(y) {
						var x = this;
						this.FHa = function (n) {
							return (
								n &&
								("image" === n.type.split("/")[0].toLowerCase() ||
									(n.name && !!n.name.match(/.(jpg|jpeg|png|gif)$/i)))
							);
						};
						this.file = y;
						this.YHa = new Promise(function (n) {
							return Object(la.b)(x, void 0, void 0, function () {
								var h;
								return Object(la.d)(this, function (b) {
									switch (b.label) {
										case 0:
											return this.FHa(this.file)
												? [4, Object(ma.b)(y)]
												: [3, 2];
										case 1:
											(h = b.aa()),
												(this.file = ja.r
													? new Blob([h], { type: y.type })
													: new File(
															[h],
															null === y || void 0 === y ? void 0 : y.name,
															{ type: y.type },
														)),
												(b.label = 2);
										case 2:
											return n(!0), [2];
									}
								});
							});
						});
					}
					ca.prototype.getFileData = function (y) {
						var x = this,
							n = new FileReader();
						n.onload = function (h) {
							x.trigger(ca.Events.DOCUMENT_LOADING_PROGRESS, [
								h.loaded,
								h.loaded,
							]);
							y(new Uint8Array(h.target.result));
						};
						n.onprogress = function (h) {
							h.lengthComputable &&
								x.trigger(ca.Events.DOCUMENT_LOADING_PROGRESS, [
									h.loaded,
									0 < h.total ? h.total : 0,
								]);
						};
						n.readAsArrayBuffer(this.file);
					};
					ca.prototype.getFile = function () {
						return Object(la.b)(this, void 0, Promise, function () {
							return Object(la.d)(this, function (y) {
								switch (y.label) {
									case 0:
										return [4, this.YHa];
									case 1:
										return (
											y.aa(),
											ia.da.isJSWorker ? [2, this.file.path] : [2, this.file]
										);
								}
							});
						});
					};
					ca.Events = { DOCUMENT_LOADING_PROGRESS: "documentLoadingProgress" };
					return ca;
				})();
				Object(ya.a)(r);
				Object(na.a)(r);
				Object(na.b)(r);
				va["default"] = r;
			},
		},
	]);
}).call(this || window);
