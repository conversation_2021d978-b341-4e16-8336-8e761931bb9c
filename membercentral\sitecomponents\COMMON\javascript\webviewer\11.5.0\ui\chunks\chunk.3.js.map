{"version": 3, "sources": ["webpack:///./src/ui/node_modules/react-dnd/dist/esm/hooks/useDragDropManager.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/hooks/useIsomorphicLayoutEffect.js", "webpack:///../src/index.ts", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/hooks/useCollector.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/wrapConnectorHooks.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/isRef.js", "webpack:///./src/ui/node_modules/fast-deep-equal/index.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/DropTargetMonitorImpl.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/TargetConnector.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/registration.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/DragSourceMonitorImpl.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/internals/SourceConnector.js"], "names": ["useDragDropManager", "dragDropManager", "useContext", "DndContext", "invariant", "useIsomorphicLayoutEffect", "window", "useLayoutEffect", "useEffect", "shallowEqual", "objA", "objB", "compare", "compareContext", "compareResult", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB", "_slicedToArray", "arr", "i", "Array", "isArray", "_arrayWithHoles", "_i", "Symbol", "iterator", "_s", "_e", "_arr", "_n", "_d", "next", "done", "push", "value", "err", "_iterableToArrayLimit", "o", "minLen", "_arrayLikeToArray", "n", "toString", "slice", "constructor", "name", "from", "test", "_unsupportedIterableToArray", "TypeError", "_nonIterableRest", "len", "arr2", "useCollector", "monitor", "collect", "onUpdate", "_useState2", "useState", "collected", "setCollected", "updateCollected", "useCallback", "nextValue", "equal", "throwIfCompositeComponentElement", "element", "type", "displayName", "Error", "concat", "wrapConnectorHooks", "hooks", "<PERSON><PERSON><PERSON>s", "for<PERSON>ach", "hook", "endsWith", "wrappedHook", "elementOrNode", "arguments", "undefined", "options", "isValidElement", "node", "ref", "cloneWithRef", "wrapHookToRecognizeElement", "setRef", "current", "newRef", "previousRef", "cloneElement", "_typeof", "obj", "isRef", "module", "exports", "a", "b", "RegExp", "source", "flags", "valueOf", "_defineProperties", "target", "props", "descriptor", "enumerable", "configurable", "writable", "defineProperty", "_defineProperty", "isCallingCanDrop", "DropTargetMonitorImpl", "manager", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_classCallCheck", "this", "internalMonitor", "getMonitor", "protoProps", "staticProps", "targetId", "listener", "subscribeToStateChange", "canDropOnTarget", "isOverTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "TargetConnector", "backend", "_this", "drop<PERSON>ar<PERSON>", "clearDropTarget", "dropTargetOptions", "dropTargetRef", "dropTargetNode", "reconnect", "get", "<PERSON><PERSON><PERSON><PERSON>", "didHandlerIdChange", "didDropTargetChange", "didOptionsChange", "disconnectDropTarget", "handlerId", "lastConnectedHandlerId", "lastConnectedDropTarget", "lastConnectedDropTargetOptions", "unsubscribeDropTarget", "connectDropTarget", "newHandlerId", "dropTargetOptionsInternal", "set", "registerTarget", "registry", "getRegistry", "addTarget", "remove<PERSON>arget", "registerSource", "sourceId", "addSource", "removeSource", "isCallingCanDrag", "isCallingIsDragging", "DragSourceMonitorImpl", "canDragSource", "isDraggingSource", "getTargetIds", "isSourcePublic", "getSourceId", "subscribeToOffsetChange", "SourceConnector", "dragSource", "clearDragSource", "dragSourceOptions", "dragSourceRef", "dragSourceNode", "reconnectDragSource", "dragPreview", "clearDragPreview", "dragPreviewOptions", "dragPreviewRef", "dragPreviewNode", "reconnectDragPreview", "dragSourceOptionsInternal", "dragPreviewOptionsInternal", "didConnectedDragSourceChange", "didDragSourceOptionsChange", "disconnectDragSource", "lastConnectedDragSource", "lastConnectedDragSourceOptions", "dragSourceUnsubscribe", "connectDragSource", "didConnectedDragPreviewChange", "didDragPreviewOptionsChange", "disconnectDragPreview", "lastConnectedDragPreview", "lastConnectedDragPreviewOptions", "dragPreviewUnsubscribe", "connectDragPreview"], "mappings": "2FAAA,8DAOO,SAASA,IACd,IACIC,EADcC,qBAAWC,KACKF,gBAGlC,OADAG,YAA6B,MAAnBH,EAAyB,8BAC5BA,I,kCCZT,6CAEWI,EAA8C,oBAAXC,OAAyBC,kBAAkBC,a,sWCFzEC,EACdC,EACAC,EACAC,EACAC,G,IAEIC,EAAgBF,EAChBA,EAAQG,KAAKF,EAAgBH,EAAMC,QACnC,E,QACkB,IAAlBG,E,QACOA,E,GAGPJ,IAASC,E,OACJ,E,GAGW,WAAhB,EAAOD,KAAsBA,GAAwB,WAAhB,EAAOC,KAAsBA,E,OAC7D,E,IAGLK,EAAQC,OAAOC,KAAKR,GACpBS,EAAQF,OAAOC,KAAKP,G,GAEpBK,EAAMI,SAAWD,EAAMC,O,OAClB,E,QAGLC,EAAkBJ,OAAOK,UAAUC,eAAeC,KAAKb,GAGlDc,EAAM,EAAGA,EAAMT,EAAMI,OAAQK,IAAO,C,IACvCC,EAAMV,EAAMS,G,IAEXJ,EAAgBK,G,OACZ,E,IAGLC,EAAUjB,EAAagB,GACvBE,EAAUjB,EAAae,G,IAOP,KALpBZ,EAAgBF,EACZA,EAAQG,KAAKF,EAAgBc,EAAQC,EAAQF,QAC7C,SAIiB,IAAlBZ,GAA4Ba,IAAWC,E,OAEjC,E,OAIJ,I,kLCrDT,SAASC,EAAeC,EAAKC,GAAK,OAUlC,SAAyBD,GAAO,GAAIE,MAAMC,QAAQH,GAAM,OAAOA,EAVtBI,CAAgBJ,IAQzD,SAA+BA,EAAKC,GAAK,IAAII,EAAY,MAAPL,EAAc,KAAyB,oBAAXM,QAA0BN,EAAIM,OAAOC,WAAaP,EAAI,cAAe,GAAU,MAANK,EAAY,OAAQ,IAAkDG,EAAIC,EAAlDC,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAmB,IAAM,IAAKP,EAAKA,EAAGpB,KAAKe,KAAQW,GAAMH,EAAKH,EAAGQ,QAAQC,QAAoBJ,EAAKK,KAAKP,EAAGQ,QAAYf,GAAKS,EAAKpB,SAAWW,GAA3DU,GAAK,IAAoE,MAAOM,GAAOL,GAAK,EAAMH,EAAKQ,EAAO,QAAU,IAAWN,GAAsB,MAAhBN,EAAW,QAAWA,EAAW,SAAO,QAAU,GAAIO,EAAI,MAAMH,GAAQ,OAAOC,EARzbQ,CAAsBlB,EAAKC,IAI5F,SAAqCkB,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,EAAkBF,EAAGC,GAAS,IAAIE,EAAInC,OAAOK,UAAU+B,SAAStC,KAAKkC,GAAGK,MAAM,GAAI,GAAc,WAANF,GAAkBH,EAAEM,cAAaH,EAAIH,EAAEM,YAAYC,MAAM,GAAU,QAANJ,GAAqB,QAANA,EAAa,OAAOpB,MAAMyB,KAAKR,GAAI,GAAU,cAANG,GAAqB,2CAA2CM,KAAKN,GAAI,OAAOD,EAAkBF,EAAGC,GAJpTS,CAA4B7B,EAAKC,IAEnI,WAA8B,MAAM,IAAI6B,UAAU,6IAFuFC,GAMzI,SAASV,EAAkBrB,EAAKgC,IAAkB,MAAPA,GAAeA,EAAMhC,EAAIV,UAAQ0C,EAAMhC,EAAIV,QAAQ,IAAK,IAAIW,EAAI,EAAGgC,EAAO,IAAI/B,MAAM8B,GAAM/B,EAAI+B,EAAK/B,IAAOgC,EAAKhC,GAAKD,EAAIC,GAAM,OAAOgC,EAgBzK,SAASC,EAAaC,EAASC,EAASC,GAC7C,IAGIC,EAAavC,EAHDwC,oBAAS,WACvB,OAAOH,EAAQD,MAE0B,GACvCK,EAAYF,EAAW,GACvBG,EAAeH,EAAW,GAE1BI,EAAkBC,uBAAY,WAChC,IAAIC,EAAYR,EAAQD,GAGnBU,IAAML,EAAWI,KACpBH,EAAaG,GAETP,GACFA,OAGH,CAACG,EAAWL,EAASE,IAKxB,OADA9D,YAA0BmE,GACnB,CAACF,EAAWE,K,2HC3CrB,SAASI,EAAiCC,GAGxC,GAA4B,iBAAjBA,EAAQC,KAAnB,CAIA,IAAIC,EAAcF,EAAQC,KAAKC,aAAeF,EAAQC,KAAKtB,MAAQ,gBACnE,MAAM,IAAIwB,MAAM,uEAAyE,uBAAuBC,OAAOF,EAAa,qCAAuC,yCA8BtK,SAASG,EAAmBC,GACjC,IAAIC,EAAe,GAcnB,OAbAnE,OAAOC,KAAKiE,GAAOE,SAAQ,SAAU3D,GACnC,IAAI4D,EAAOH,EAAMzD,GAEjB,GAAIA,EAAI6D,SAAS,OACfH,EAAa1D,GAAOyD,EAAMzD,OACrB,CACL,IAAI8D,EAnCV,SAAoCF,GAClC,OAAO,WACL,IAAIG,EAAgBC,UAAUtE,OAAS,QAAsBuE,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KACpFE,EAAUF,UAAUtE,OAAS,QAAsBuE,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAGlF,IAAKG,yBAAeJ,GAAgB,CAClC,IAAIK,EAAOL,EAIX,OAHAH,EAAKQ,EAAMF,GAGJE,EAMT,IAAIjB,EAAUY,EACdb,EAAiCC,GAEjC,IAAIkB,EAAMH,EAAU,SAAUE,GAC5B,OAAOR,EAAKQ,EAAMF,IAChBN,EACJ,OAAOU,EAAanB,EAASkB,IAYTE,CAA2BX,GAE7CF,EAAa1D,GAAO,WAClB,OAAO8D,OAINJ,EAGT,SAASc,EAAOH,EAAKD,GACA,mBAARC,EACTA,EAAID,GAEJC,EAAII,QAAUL,EAIlB,SAASE,EAAanB,EAASuB,GAC7B,IAAIC,EAAcxB,EAAQkB,IAG1B,OAFA3F,YAAiC,iBAAhBiG,EAA0B,mOAEtCA,EAMIC,uBAAazB,EAAS,CAC3BkB,IAAK,SAAaD,GAChBI,EAAOG,EAAaP,GACpBI,EAAOE,EAAQN,MAPZQ,uBAAazB,EAAS,CAC3BkB,IAAKK,M,4GC1EX,SAASG,EAAQC,GAAmV,OAAtOD,EAArD,mBAAXnE,QAAoD,iBAApBA,OAAOC,SAAmC,SAAiBmE,GAAO,cAAcA,GAA2B,SAAiBA,GAAO,OAAOA,GAAyB,mBAAXpE,QAAyBoE,EAAIjD,cAAgBnB,QAAUoE,IAAQpE,OAAOd,UAAY,gBAAkBkF,IAAyBA,GAE5W,SAASC,EAAMD,GACpB,OACU,OAARA,GAAiC,WAAjBD,EAAQC,IAAqBvF,OAAOK,UAAUC,eAAeR,KAAKyF,EAAK,a,kCCE3FE,EAAOC,QAAU,SAAShC,EAAMiC,EAAGC,GACjC,GAAID,IAAMC,EAAG,OAAO,EAEpB,GAAID,GAAKC,GAAiB,iBAALD,GAA6B,iBAALC,EAAe,CAC1D,GAAID,EAAErD,cAAgBsD,EAAEtD,YAAa,OAAO,EAE5C,IAAInC,EAAQW,EAAGb,EACf,GAAIc,MAAMC,QAAQ2E,GAAI,CAEpB,IADAxF,EAASwF,EAAExF,SACGyF,EAAEzF,OAAQ,OAAO,EAC/B,IAAKW,EAAIX,EAAgB,GAARW,KACf,IAAK4C,EAAMiC,EAAE7E,GAAI8E,EAAE9E,IAAK,OAAO,EACjC,OAAO,EAKT,GAAI6E,EAAErD,cAAgBuD,OAAQ,OAAOF,EAAEG,SAAWF,EAAEE,QAAUH,EAAEI,QAAUH,EAAEG,MAC5E,GAAIJ,EAAEK,UAAYhG,OAAOK,UAAU2F,QAAS,OAAOL,EAAEK,YAAcJ,EAAEI,UACrE,GAAIL,EAAEvD,WAAapC,OAAOK,UAAU+B,SAAU,OAAOuD,EAAEvD,aAAewD,EAAExD,WAIxE,IADAjC,GADAF,EAAOD,OAAOC,KAAK0F,IACLxF,UACCH,OAAOC,KAAK2F,GAAGzF,OAAQ,OAAO,EAE7C,IAAKW,EAAIX,EAAgB,GAARW,KACf,IAAKd,OAAOK,UAAUC,eAAeR,KAAK8F,EAAG3F,EAAKa,IAAK,OAAO,EAEhE,IAAKA,EAAIX,EAAgB,GAARW,KAAY,CAC3B,IAAIL,EAAMR,EAAKa,GAEf,IAAK4C,EAAMiC,EAAElF,GAAMmF,EAAEnF,IAAO,OAAO,EAGrC,OAAO,EAIT,OAAOkF,GAAIA,GAAKC,GAAIA,I,sFC1CtB,SAASK,EAAkBC,EAAQC,GAAS,IAAK,IAAIrF,EAAI,EAAGA,EAAIqF,EAAMhG,OAAQW,IAAK,CAAE,IAAIsF,EAAaD,EAAMrF,GAAIsF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMvG,OAAOwG,eAAeN,EAAQE,EAAW3F,IAAK2F,IAI7S,SAASK,EAAgBlB,EAAK9E,EAAKoB,GAAiK,OAApJpB,KAAO8E,EAAOvF,OAAOwG,eAAejB,EAAK9E,EAAK,CAAEoB,MAAOA,EAAOwE,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBhB,EAAI9E,GAAOoB,EAAgB0D,EAG3M,IAAImB,GAAmB,EACZC,EAAqC,WAC9C,SAASA,EAAsBC,IAXjC,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAInE,UAAU,qCAY5GoE,CAAgBC,KAAML,GAEtBF,EAAgBO,KAAM,uBAAmB,GAEzCP,EAAgBO,KAAM,WAAY,MAElCA,KAAKC,gBAAkBL,EAAQM,aAdnC,IAAsBJ,EAAaK,EAAYC,EA2G7C,OA3GoBN,EAiBPH,GAjBoBQ,EAiBG,CAAC,CACnC1G,IAAK,mBACLoB,MAAO,SAA0BwF,GAC/BL,KAAKK,SAAWA,IAEjB,CACD5G,IAAK,eACLoB,MAAO,WACL,OAAOmF,KAAKK,WAEb,CACD5G,IAAK,yBACLoB,MAAO,SAAgCyF,EAAU3C,GAC/C,OAAOqC,KAAKC,gBAAgBM,uBAAuBD,EAAU3C,KAE9D,CACDlE,IAAK,UACLoB,MAAO,WAIL,IAAKmF,KAAKK,SACR,OAAO,EAGTlI,aAAWuH,EAAkB,yJAE7B,IAEE,OADAA,GAAmB,EACZM,KAAKC,gBAAgBO,gBAAgBR,KAAKK,UACjD,QACAX,GAAmB,KAGtB,CACDjG,IAAK,SACLoB,MAAO,SAAgB8C,GACrB,QAAKqC,KAAKK,UAIHL,KAAKC,gBAAgBQ,aAAaT,KAAKK,SAAU1C,KAEzD,CACDlE,IAAK,cACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBS,gBAE7B,CACDjH,IAAK,UACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBU,YAE7B,CACDlH,IAAK,gBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBW,kBAE7B,CACDnH,IAAK,UACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBY,YAE7B,CACDpH,IAAK,yBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBa,2BAE7B,CACDrH,IAAK,+BACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBc,iCAE7B,CACDtH,IAAK,wBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBe,0BAE7B,CACDvH,IAAK,kBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBgB,oBAE7B,CACDxH,IAAK,iCACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBiB,sCAvG4CjC,EAAkBa,EAAYzG,UAAW8G,GAAiBC,GAAanB,EAAkBa,EAAaM,GA2G3KT,EArGuC,I,4GCRhD,SAASV,EAAkBC,EAAQC,GAAS,IAAK,IAAIrF,EAAI,EAAGA,EAAIqF,EAAMhG,OAAQW,IAAK,CAAE,IAAIsF,EAAaD,EAAMrF,GAAIsF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMvG,OAAOwG,eAAeN,EAAQE,EAAW3F,IAAK2F,IAI7S,SAASK,EAAgBlB,EAAK9E,EAAKoB,GAAiK,OAApJpB,KAAO8E,EAAOvF,OAAOwG,eAAejB,EAAK9E,EAAK,CAAEoB,MAAOA,EAAOwE,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBhB,EAAI9E,GAAOoB,EAAgB0D,EAKpM,IAAI4C,EAA+B,WAExC,SAASA,EAAgBC,GACvB,IAAIC,EAAQrB,MAdhB,SAAyBH,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAInE,UAAU,qCAgB5GoE,CAAgBC,KAAMmB,GAEtB1B,EAAgBO,KAAM,QAAS/C,YAAmB,CAChDqE,WAAY,SAAoBzD,EAAMF,GACpC0D,EAAME,kBAENF,EAAMG,kBAAoB7D,EAEtBa,YAAMX,GACRwD,EAAMI,cAAgB5D,EAEtBwD,EAAMK,eAAiB7D,EAGzBwD,EAAMM,gBAIVlC,EAAgBO,KAAM,YAAa,MAEnCP,EAAgBO,KAAM,gBAAiB,MAEvCP,EAAgBO,KAAM,sBAAkB,GAExCP,EAAgBO,KAAM,4BAA6B,MAEnDP,EAAgBO,KAAM,6BAAyB,GAE/CP,EAAgBO,KAAM,yBAA0B,MAEhDP,EAAgBO,KAAM,0BAA2B,MAEjDP,EAAgBO,KAAM,iCAAkC,MAExDP,EAAgBO,KAAM,eAAW,GAEjCA,KAAKoB,QAAUA,EAhDnB,IAAsBtB,EAAaK,EAAYC,EA0I7C,OA1IoBN,EAmDPqB,GAnDoBhB,EAmDH,CAAC,CAC7B1G,IAAK,gBACLmI,IAAK,WACH,OAAO5B,KAAKsB,aAEb,CACD7H,IAAK,YACLoB,MAAO,WAEL,IAAIgH,EAAY7B,KAAK8B,sBAAwB9B,KAAK+B,uBAAyB/B,KAAKgC,mBAE5EH,GACF7B,KAAKiC,uBAGP,IAAIX,EAAatB,KAAKsB,WAEjBtB,KAAKkC,YAILZ,EAKDO,IACF7B,KAAKmC,uBAAyBnC,KAAKkC,UACnClC,KAAKoC,wBAA0Bd,EAC/BtB,KAAKqC,+BAAiCrC,KAAKwB,kBAC3CxB,KAAKsC,sBAAwBtC,KAAKoB,QAAQmB,kBAAkBvC,KAAKkC,UAAWZ,EAAYtB,KAAKwB,oBAR7FxB,KAAKoC,wBAA0Bd,KAWlC,CACD7H,IAAK,mBACLoB,MAAO,SAA0B2H,GAC3BA,IAAiBxC,KAAKkC,YAI1BlC,KAAKkC,UAAYM,EACjBxC,KAAK2B,eAEN,CACDlI,IAAK,oBACLmI,IAAK,WACH,OAAO5B,KAAKyC,2BAEdC,IAAK,SAAa/E,GAChBqC,KAAKyC,0BAA4B9E,IAElC,CACDlE,IAAK,qBACLoB,MAAO,WACL,OAAOmF,KAAKmC,yBAA2BnC,KAAKkC,YAE7C,CACDzI,IAAK,sBACLoB,MAAO,WACL,OAAOmF,KAAKoC,0BAA4BpC,KAAKsB,aAE9C,CACD7H,IAAK,mBACLoB,MAAO,WACL,OAAQrC,YAAawH,KAAKqC,+BAAgCrC,KAAKwB,qBAEhE,CACD/H,IAAK,uBACLoB,MAAO,WACDmF,KAAKsC,wBACPtC,KAAKsC,wBACLtC,KAAKsC,2BAAwB5E,KAGhC,CACDjE,IAAK,aACLmI,IAAK,WACH,OAAO5B,KAAK0B,gBAAkB1B,KAAKyB,eAAiBzB,KAAKyB,cAAcvD,UAExE,CACDzE,IAAK,kBACLoB,MAAO,WACLmF,KAAKyB,cAAgB,KACrBzB,KAAK0B,eAAiB,UAtIkDzC,EAAkBa,EAAYzG,UAAW8G,GAAiBC,GAAanB,EAAkBa,EAAaM,GA0I3Ke,EAnIiC,I,kCCXnC,SAASwB,EAAe9F,EAAMqC,EAAQU,GAC3C,IAAIgD,EAAWhD,EAAQiD,cACnBxC,EAAWuC,EAASE,UAAUjG,EAAMqC,GACxC,MAAO,CAACmB,EAAU,WAChB,OAAOuC,EAASG,aAAa1C,KAG1B,SAAS2C,EAAenG,EAAMiC,EAAQc,GAC3C,IAAIgD,EAAWhD,EAAQiD,cACnBI,EAAWL,EAASM,UAAUrG,EAAMiC,GACxC,MAAO,CAACmE,EAAU,WAChB,OAAOL,EAASO,aAAaF,KAXjC,qE,sFCEA,SAAShE,EAAkBC,EAAQC,GAAS,IAAK,IAAIrF,EAAI,EAAGA,EAAIqF,EAAMhG,OAAQW,IAAK,CAAE,IAAIsF,EAAaD,EAAMrF,GAAIsF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMvG,OAAOwG,eAAeN,EAAQE,EAAW3F,IAAK2F,IAI7S,SAASK,EAAgBlB,EAAK9E,EAAKoB,GAAiK,OAApJpB,KAAO8E,EAAOvF,OAAOwG,eAAejB,EAAK9E,EAAK,CAAEoB,MAAOA,EAAOwE,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBhB,EAAI9E,GAAOoB,EAAgB0D,EAG3M,IAAI6E,GAAmB,EACnBC,GAAsB,EACfC,EAAqC,WAC9C,SAASA,EAAsB1D,IAZjC,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAInE,UAAU,qCAa5GoE,CAAgBC,KAAMsD,GAEtB7D,EAAgBO,KAAM,uBAAmB,GAEzCP,EAAgBO,KAAM,WAAY,MAElCA,KAAKC,gBAAkBL,EAAQM,aAfnC,IAAsBJ,EAAaK,EAAYC,EAoJ7C,OApJoBN,EAkBPwD,GAlBoBnD,EAkBG,CAAC,CACnC1G,IAAK,mBACLoB,MAAO,SAA0BoI,GAC/BjD,KAAKiD,SAAWA,IAEjB,CACDxJ,IAAK,eACLoB,MAAO,WACL,OAAOmF,KAAKiD,WAEb,CACDxJ,IAAK,UACLoB,MAAO,WACL1C,aAAWiL,EAAkB,yJAE7B,IAEE,OADAA,GAAmB,EACZpD,KAAKC,gBAAgBsD,cAAcvD,KAAKiD,UAC/C,QACAG,GAAmB,KAGtB,CACD3J,IAAK,aACLoB,MAAO,WACL,IAAKmF,KAAKiD,SACR,OAAO,EAGT9K,aAAWkL,EAAqB,+JAEhC,IAEE,OADAA,GAAsB,EACfrD,KAAKC,gBAAgBuD,iBAAiBxD,KAAKiD,UAClD,QACAI,GAAsB,KAGzB,CACD5J,IAAK,yBACLoB,MAAO,SAAgCyF,EAAU3C,GAC/C,OAAOqC,KAAKC,gBAAgBM,uBAAuBD,EAAU3C,KAE9D,CACDlE,IAAK,mBACLoB,MAAO,SAA0BoI,GAC/B,OAAOjD,KAAKC,gBAAgBuD,iBAAiBP,KAE9C,CACDxJ,IAAK,eACLoB,MAAO,SAAsBwF,EAAU1C,GACrC,OAAOqC,KAAKC,gBAAgBQ,aAAaJ,EAAU1C,KAEpD,CACDlE,IAAK,eACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBwD,iBAE7B,CACDhK,IAAK,iBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgByD,mBAE7B,CACDjK,IAAK,cACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgB0D,gBAE7B,CACDlK,IAAK,0BACLoB,MAAO,SAAiCyF,GACtC,OAAON,KAAKC,gBAAgB2D,wBAAwBtD,KAErD,CACD7G,IAAK,gBACLoB,MAAO,SAAuBoI,GAC5B,OAAOjD,KAAKC,gBAAgBsD,cAAcN,KAE3C,CACDxJ,IAAK,kBACLoB,MAAO,SAAyBwF,GAC9B,OAAOL,KAAKC,gBAAgBO,gBAAgBH,KAE7C,CACD5G,IAAK,cACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBS,gBAE7B,CACDjH,IAAK,UACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBU,YAE7B,CACDlH,IAAK,gBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBW,kBAE7B,CACDnH,IAAK,UACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBY,YAE7B,CACDpH,IAAK,yBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBa,2BAE7B,CACDrH,IAAK,+BACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBc,iCAE7B,CACDtH,IAAK,wBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBe,0BAE7B,CACDvH,IAAK,kBACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBgB,oBAE7B,CACDxH,IAAK,iCACLoB,MAAO,WACL,OAAOmF,KAAKC,gBAAgBiB,sCAhJ4CjC,EAAkBa,EAAYzG,UAAW8G,GAAiBC,GAAanB,EAAkBa,EAAaM,GAoJ3KkD,EA7IuC,I,4GCThD,SAASrE,EAAkBC,EAAQC,GAAS,IAAK,IAAIrF,EAAI,EAAGA,EAAIqF,EAAMhG,OAAQW,IAAK,CAAE,IAAIsF,EAAaD,EAAMrF,GAAIsF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMvG,OAAOwG,eAAeN,EAAQE,EAAW3F,IAAK2F,IAI7S,SAASK,EAAgBlB,EAAK9E,EAAKoB,GAAiK,OAApJpB,KAAO8E,EAAOvF,OAAOwG,eAAejB,EAAK9E,EAAK,CAAEoB,MAAOA,EAAOwE,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBhB,EAAI9E,GAAOoB,EAAgB0D,EAKpM,IAAIsF,EAA+B,WAGxC,SAASA,EAAgBzC,GACvB,IAAIC,EAAQrB,MAfhB,SAAyBH,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAInE,UAAU,qCAiB5GoE,CAAgBC,KAAM6D,GAEtBpE,EAAgBO,KAAM,QAAS/C,YAAmB,CAChD6G,WAAY,SAAoBjG,EAAMF,GACpC0D,EAAM0C,kBAEN1C,EAAM2C,kBAAoBrG,GAAW,KAEjCa,YAAMX,GACRwD,EAAM4C,cAAgBpG,EAEtBwD,EAAM6C,eAAiBrG,EAGzBwD,EAAM8C,uBAERC,YAAa,SAAqBvG,EAAMF,GACtC0D,EAAMgD,mBAENhD,EAAMiD,mBAAqB3G,GAAW,KAElCa,YAAMX,GACRwD,EAAMkD,eAAiB1G,EAEvBwD,EAAMmD,gBAAkB3G,EAG1BwD,EAAMoD,2BAIVhF,EAAgBO,KAAM,YAAa,MAEnCP,EAAgBO,KAAM,gBAAiB,MAEvCP,EAAgBO,KAAM,sBAAkB,GAExCP,EAAgBO,KAAM,4BAA6B,MAEnDP,EAAgBO,KAAM,6BAAyB,GAE/CP,EAAgBO,KAAM,iBAAkB,MAExCP,EAAgBO,KAAM,uBAAmB,GAEzCP,EAAgBO,KAAM,6BAA8B,MAEpDP,EAAgBO,KAAM,8BAA0B,GAEhDP,EAAgBO,KAAM,yBAA0B,MAEhDP,EAAgBO,KAAM,0BAA2B,MAEjDP,EAAgBO,KAAM,iCAAkC,MAExDP,EAAgBO,KAAM,2BAA4B,MAElDP,EAAgBO,KAAM,kCAAmC,MAEzDP,EAAgBO,KAAM,eAAW,GAEjCA,KAAKoB,QAAUA,EA1EnB,IAAsBtB,EAAaK,EAAYC,EA2O7C,OA3OoBN,EA6EP+D,GA7EoB1D,EA6EH,CAAC,CAC7B1G,IAAK,mBACLoB,MAAO,SAA0B2H,GAC3BxC,KAAKkC,YAAcM,IAIvBxC,KAAKkC,UAAYM,EACjBxC,KAAK2B,eAEN,CACDlI,IAAK,gBACLmI,IAAK,WACH,OAAO5B,KAAK8D,aAEb,CACDrK,IAAK,oBACLmI,IAAK,WACH,OAAO5B,KAAK0E,2BAEdhC,IAAK,SAAa/E,GAChBqC,KAAK0E,0BAA4B/G,IAElC,CACDlE,IAAK,qBACLmI,IAAK,WACH,OAAO5B,KAAK2E,4BAEdjC,IAAK,SAAa/E,GAChBqC,KAAK2E,2BAA6BhH,IAEnC,CACDlE,IAAK,YACLoB,MAAO,WACLmF,KAAKmE,sBACLnE,KAAKyE,yBAEN,CACDhL,IAAK,sBACLoB,MAAO,WACL,IAAIiJ,EAAa9D,KAAK8D,WAElBjC,EAAY7B,KAAK8B,sBAAwB9B,KAAK4E,gCAAkC5E,KAAK6E,6BAErFhD,GACF7B,KAAK8E,uBAGF9E,KAAKkC,YAIL4B,EAKDjC,IACF7B,KAAKmC,uBAAyBnC,KAAKkC,UACnClC,KAAK+E,wBAA0BjB,EAC/B9D,KAAKgF,+BAAiChF,KAAKgE,kBAC3ChE,KAAKiF,sBAAwBjF,KAAKoB,QAAQ8D,kBAAkBlF,KAAKkC,UAAW4B,EAAY9D,KAAKgE,oBAR7FhE,KAAK+E,wBAA0BjB,KAWlC,CACDrK,IAAK,uBACLoB,MAAO,WACL,IAAIuJ,EAAcpE,KAAKoE,YAEnBvC,EAAY7B,KAAK8B,sBAAwB9B,KAAKmF,iCAAmCnF,KAAKoF,8BAEtFvD,GACF7B,KAAKqF,wBAGFrF,KAAKkC,YAILkC,EAKDvC,IACF7B,KAAKmC,uBAAyBnC,KAAKkC,UACnClC,KAAKsF,yBAA2BlB,EAChCpE,KAAKuF,gCAAkCvF,KAAKsE,mBAC5CtE,KAAKwF,uBAAyBxF,KAAKoB,QAAQqE,mBAAmBzF,KAAKkC,UAAWkC,EAAapE,KAAKsE,qBARhGtE,KAAKsF,yBAA2BlB,KAWnC,CACD3K,IAAK,qBACLoB,MAAO,WACL,OAAOmF,KAAKmC,yBAA2BnC,KAAKkC,YAE7C,CACDzI,IAAK,+BACLoB,MAAO,WACL,OAAOmF,KAAK+E,0BAA4B/E,KAAK8D,aAE9C,CACDrK,IAAK,gCACLoB,MAAO,WACL,OAAOmF,KAAKsF,2BAA6BtF,KAAKoE,cAE/C,CACD3K,IAAK,6BACLoB,MAAO,WACL,OAAQrC,YAAawH,KAAKgF,+BAAgChF,KAAKgE,qBAEhE,CACDvK,IAAK,8BACLoB,MAAO,WACL,OAAQrC,YAAawH,KAAKuF,gCAAiCvF,KAAKsE,sBAEjE,CACD7K,IAAK,uBACLoB,MAAO,WACDmF,KAAKiF,wBACPjF,KAAKiF,wBACLjF,KAAKiF,2BAAwBvH,KAGhC,CACDjE,IAAK,wBACLoB,MAAO,WACDmF,KAAKwF,yBACPxF,KAAKwF,yBACLxF,KAAKwF,4BAAyB9H,EAC9BsC,KAAKwE,gBAAkB,KACvBxE,KAAKuE,eAAiB,QAGzB,CACD9K,IAAK,aACLmI,IAAK,WACH,OAAO5B,KAAKkE,gBAAkBlE,KAAKiE,eAAiBjE,KAAKiE,cAAc/F,UAExE,CACDzE,IAAK,cACLmI,IAAK,WACH,OAAO5B,KAAKwE,iBAAmBxE,KAAKuE,gBAAkBvE,KAAKuE,eAAerG,UAE3E,CACDzE,IAAK,kBACLoB,MAAO,WACLmF,KAAKkE,eAAiB,KACtBlE,KAAKiE,cAAgB,OAEtB,CACDxK,IAAK,mBACLoB,MAAO,WACLmF,KAAKwE,gBAAkB,KACvBxE,KAAKuE,eAAiB,UAvOkDtF,EAAkBa,EAAYzG,UAAW8G,GAAiBC,GAAanB,EAAkBa,EAAaM,GA2O3KyD,EApOiC", "file": "chunks/chunk.3.js", "sourcesContent": ["import { useContext } from 'react';\nimport { invariant } from '@react-dnd/invariant';\nimport { DndContext } from '../core';\n/**\n * A hook to retrieve the DragDropManager from Context\n */\n\nexport function useDragDropManager() {\n  var _useContext = useContext(DndContext),\n      dragDropManager = _useContext.dragDropManager;\n\n  invariant(dragDropManager != null, 'Expected drag drop context');\n  return dragDropManager;\n}", "import { useLayoutEffect, useEffect } from 'react'; // suppress the useLayoutEffect warning on server side.\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;", "export function shallowEqual<T>(\n  objA: T,\n  objB: T,\n  compare?: (a: T, b: T, key?: string) => boolean | void,\n  compareContext?: any\n) {\n  var compareResult = compare\n    ? compare.call(compareContext, objA, objB)\n    : void 0;\n  if (compareResult !== void 0) {\n    return !!compareResult;\n  }\n\n  if (objA === objB) {\n    return true;\n  }\n\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n\n  var bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n\n  // Test for A's keys different from B.\n  for (var idx = 0; idx < keysA.length; idx++) {\n    var key = keysA[idx];\n\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n\n    var valueA = (objA as any)[key];\n    var valueB = (objB as any)[key];\n\n    compareResult = compare\n      ? compare.call(compareContext, valueA, valueB, key)\n      : void 0;\n\n    if (\n      compareResult === false ||\n      (compareResult === void 0 && valueA !== valueB)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport equal from 'fast-deep-equal';\nimport { useState, useCallback } from 'react';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect';\n/**\n *\n * @param monitor The monitor to collect state from\n * @param collect The collecting function\n * @param onUpdate A method to invoke when updates occur\n */\n\nexport function useCollector(monitor, collect, onUpdate) {\n  var _useState = useState(function () {\n    return collect(monitor);\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      collected = _useState2[0],\n      setCollected = _useState2[1];\n\n  var updateCollected = useCallback(function () {\n    var nextValue = collect(monitor); // This needs to be a deep-equality check because some monitor-collected values\n    // include XYCoord objects that may be equivalent, but do not have instance equality.\n\n    if (!equal(collected, nextValue)) {\n      setCollected(nextValue);\n\n      if (onUpdate) {\n        onUpdate();\n      }\n    }\n  }, [collected, monitor, onUpdate]); // update the collected properties after react renders.\n  // Note that the \"Dustbin Stress Test\" fails if this is not\n  // done when the component updates\n\n  useIsomorphicLayoutEffect(updateCollected);\n  return [collected, updateCollected];\n}", "import { invariant } from '@react-dnd/invariant';\nimport { cloneElement, isValidElement } from 'react';\n\nfunction throwIfCompositeComponentElement(element) {\n  // Custom components can no longer be wrapped directly in React DnD 2.0\n  // so that we don't need to depend on findDOMNode() from react-dom.\n  if (typeof element.type === 'string') {\n    return;\n  }\n\n  var displayName = element.type.displayName || element.type.name || 'the component';\n  throw new Error('Only native element nodes can now be passed to React DnD connectors.' + \"You can either wrap \".concat(displayName, \" into a <div>, or turn it into a \") + 'drag source or a drop target itself.');\n}\n\nfunction wrapHookToRecognizeElement(hook) {\n  return function () {\n    var elementOrNode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n\n    // When passed a node, call the hook straight away.\n    if (!isValidElement(elementOrNode)) {\n      var node = elementOrNode;\n      hook(node, options); // return the node so it can be chained (e.g. when within callback refs\n      // <div ref={node => connectDragSource(connectDropTarget(node))}/>\n\n      return node;\n    } // If passed a ReactElement, clone it and attach this function as a ref.\n    // This helps us achieve a neat API where user doesn't even know that refs\n    // are being used under the hood.\n\n\n    var element = elementOrNode;\n    throwIfCompositeComponentElement(element); // When no options are passed, use the hook directly\n\n    var ref = options ? function (node) {\n      return hook(node, options);\n    } : hook;\n    return cloneWithRef(element, ref);\n  };\n}\n\nexport function wrapConnectorHooks(hooks) {\n  var wrappedHooks = {};\n  Object.keys(hooks).forEach(function (key) {\n    var hook = hooks[key]; // ref objects should be passed straight through without wrapping\n\n    if (key.endsWith('Ref')) {\n      wrappedHooks[key] = hooks[key];\n    } else {\n      var wrappedHook = wrapHookToRecognizeElement(hook);\n\n      wrappedHooks[key] = function () {\n        return wrappedHook;\n      };\n    }\n  });\n  return wrappedHooks;\n}\n\nfunction setRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else {\n    ref.current = node;\n  }\n}\n\nfunction cloneWithRef(element, newRef) {\n  var previousRef = element.ref;\n  invariant(typeof previousRef !== 'string', 'Cannot connect React DnD to an element with an existing string ref. ' + 'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' + 'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs');\n\n  if (!previousRef) {\n    // When there is no ref on the element, use the new ref directly\n    return cloneElement(element, {\n      ref: newRef\n    });\n  } else {\n    return cloneElement(element, {\n      ref: function ref(node) {\n        setRef(previousRef, node);\n        setRef(newRef, node);\n      }\n    });\n  }\n}", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nexport function isRef(obj) {\n  return (// eslint-disable-next-line no-prototype-builtins\n    obj !== null && _typeof(obj) === 'object' && Object.prototype.hasOwnProperty.call(obj, 'current')\n  );\n}", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nvar isCallingCanDrop = false;\nexport var DropTargetMonitorImpl = /*#__PURE__*/function () {\n  function DropTargetMonitorImpl(manager) {\n    _classCallCheck(this, DropTargetMonitorImpl);\n\n    _defineProperty(this, \"internalMonitor\", void 0);\n\n    _defineProperty(this, \"targetId\", null);\n\n    this.internalMonitor = manager.getMonitor();\n  }\n\n  _createClass(DropTargetMonitorImpl, [{\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(targetId) {\n      this.targetId = targetId;\n    }\n  }, {\n    key: \"getHandlerId\",\n    value: function getHandlerId() {\n      return this.targetId;\n    }\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, options) {\n      return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n  }, {\n    key: \"canDrop\",\n    value: function canDrop() {\n      // Cut out early if the target id has not been set. This should prevent errors\n      // where the user has an older version of dnd-core like in\n      // https://github.com/react-dnd/react-dnd/issues/1310\n      if (!this.targetId) {\n        return false;\n      }\n\n      invariant(!isCallingCanDrop, 'You may not call monitor.canDrop() inside your canDrop() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor');\n\n      try {\n        isCallingCanDrop = true;\n        return this.internalMonitor.canDropOnTarget(this.targetId);\n      } finally {\n        isCallingCanDrop = false;\n      }\n    }\n  }, {\n    key: \"isOver\",\n    value: function isOver(options) {\n      if (!this.targetId) {\n        return false;\n      }\n\n      return this.internalMonitor.isOverTarget(this.targetId, options);\n    }\n  }, {\n    key: \"getItemType\",\n    value: function getItemType() {\n      return this.internalMonitor.getItemType();\n    }\n  }, {\n    key: \"getItem\",\n    value: function getItem() {\n      return this.internalMonitor.getItem();\n    }\n  }, {\n    key: \"getDropResult\",\n    value: function getDropResult() {\n      return this.internalMonitor.getDropResult();\n    }\n  }, {\n    key: \"didDrop\",\n    value: function didDrop() {\n      return this.internalMonitor.didDrop();\n    }\n  }, {\n    key: \"getInitialClientOffset\",\n    value: function getInitialClientOffset() {\n      return this.internalMonitor.getInitialClientOffset();\n    }\n  }, {\n    key: \"getInitialSourceClientOffset\",\n    value: function getInitialSourceClientOffset() {\n      return this.internalMonitor.getInitialSourceClientOffset();\n    }\n  }, {\n    key: \"getSourceClientOffset\",\n    value: function getSourceClientOffset() {\n      return this.internalMonitor.getSourceClientOffset();\n    }\n  }, {\n    key: \"getClientOffset\",\n    value: function getClientOffset() {\n      return this.internalMonitor.getClientOffset();\n    }\n  }, {\n    key: \"getDifferenceFromInitialOffset\",\n    value: function getDifferenceFromInitialOffset() {\n      return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n  }]);\n\n  return DropTargetMonitorImpl;\n}();", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { shallowEqual } from '@react-dnd/shallowequal';\nimport { wrapConnectorHooks } from './wrapConnectorHooks';\nimport { isRef } from './isRef';\nexport var TargetConnector = /*#__PURE__*/function () {\n  // The drop target may either be attached via ref or connect function\n  function TargetConnector(backend) {\n    var _this = this;\n\n    _classCallCheck(this, TargetConnector);\n\n    _defineProperty(this, \"hooks\", wrapConnectorHooks({\n      dropTarget: function dropTarget(node, options) {\n        _this.clearDropTarget();\n\n        _this.dropTargetOptions = options;\n\n        if (isRef(node)) {\n          _this.dropTargetRef = node;\n        } else {\n          _this.dropTargetNode = node;\n        }\n\n        _this.reconnect();\n      }\n    }));\n\n    _defineProperty(this, \"handlerId\", null);\n\n    _defineProperty(this, \"dropTargetRef\", null);\n\n    _defineProperty(this, \"dropTargetNode\", void 0);\n\n    _defineProperty(this, \"dropTargetOptionsInternal\", null);\n\n    _defineProperty(this, \"unsubscribeDropTarget\", void 0);\n\n    _defineProperty(this, \"lastConnectedHandlerId\", null);\n\n    _defineProperty(this, \"lastConnectedDropTarget\", null);\n\n    _defineProperty(this, \"lastConnectedDropTargetOptions\", null);\n\n    _defineProperty(this, \"backend\", void 0);\n\n    this.backend = backend;\n  }\n\n  _createClass(TargetConnector, [{\n    key: \"connectTarget\",\n    get: function get() {\n      return this.dropTarget;\n    }\n  }, {\n    key: \"reconnect\",\n    value: function reconnect() {\n      // if nothing has changed then don't resubscribe\n      var didChange = this.didHandlerIdChange() || this.didDropTargetChange() || this.didOptionsChange();\n\n      if (didChange) {\n        this.disconnectDropTarget();\n      }\n\n      var dropTarget = this.dropTarget;\n\n      if (!this.handlerId) {\n        return;\n      }\n\n      if (!dropTarget) {\n        this.lastConnectedDropTarget = dropTarget;\n        return;\n      }\n\n      if (didChange) {\n        this.lastConnectedHandlerId = this.handlerId;\n        this.lastConnectedDropTarget = dropTarget;\n        this.lastConnectedDropTargetOptions = this.dropTargetOptions;\n        this.unsubscribeDropTarget = this.backend.connectDropTarget(this.handlerId, dropTarget, this.dropTargetOptions);\n      }\n    }\n  }, {\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(newHandlerId) {\n      if (newHandlerId === this.handlerId) {\n        return;\n      }\n\n      this.handlerId = newHandlerId;\n      this.reconnect();\n    }\n  }, {\n    key: \"dropTargetOptions\",\n    get: function get() {\n      return this.dropTargetOptionsInternal;\n    },\n    set: function set(options) {\n      this.dropTargetOptionsInternal = options;\n    }\n  }, {\n    key: \"didHandlerIdChange\",\n    value: function didHandlerIdChange() {\n      return this.lastConnectedHandlerId !== this.handlerId;\n    }\n  }, {\n    key: \"didDropTargetChange\",\n    value: function didDropTargetChange() {\n      return this.lastConnectedDropTarget !== this.dropTarget;\n    }\n  }, {\n    key: \"didOptionsChange\",\n    value: function didOptionsChange() {\n      return !shallowEqual(this.lastConnectedDropTargetOptions, this.dropTargetOptions);\n    }\n  }, {\n    key: \"disconnectDropTarget\",\n    value: function disconnectDropTarget() {\n      if (this.unsubscribeDropTarget) {\n        this.unsubscribeDropTarget();\n        this.unsubscribeDropTarget = undefined;\n      }\n    }\n  }, {\n    key: \"dropTarget\",\n    get: function get() {\n      return this.dropTargetNode || this.dropTargetRef && this.dropTargetRef.current;\n    }\n  }, {\n    key: \"clearDropTarget\",\n    value: function clearDropTarget() {\n      this.dropTargetRef = null;\n      this.dropTargetNode = null;\n    }\n  }]);\n\n  return TargetConnector;\n}();", "export function registerTarget(type, target, manager) {\n  var registry = manager.getRegistry();\n  var targetId = registry.addTarget(type, target);\n  return [targetId, function () {\n    return registry.removeTarget(targetId);\n  }];\n}\nexport function registerSource(type, source, manager) {\n  var registry = manager.getRegistry();\n  var sourceId = registry.addSource(type, source);\n  return [sourceId, function () {\n    return registry.removeSource(sourceId);\n  }];\n}", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nvar isCallingCanDrag = false;\nvar isCallingIsDragging = false;\nexport var DragSourceMonitorImpl = /*#__PURE__*/function () {\n  function DragSourceMonitorImpl(manager) {\n    _classCallCheck(this, DragSourceMonitorImpl);\n\n    _defineProperty(this, \"internalMonitor\", void 0);\n\n    _defineProperty(this, \"sourceId\", null);\n\n    this.internalMonitor = manager.getMonitor();\n  }\n\n  _createClass(DragSourceMonitorImpl, [{\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(sourceId) {\n      this.sourceId = sourceId;\n    }\n  }, {\n    key: \"getHandlerId\",\n    value: function getHandlerId() {\n      return this.sourceId;\n    }\n  }, {\n    key: \"canDrag\",\n    value: function canDrag() {\n      invariant(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n\n      try {\n        isCallingCanDrag = true;\n        return this.internalMonitor.canDragSource(this.sourceId);\n      } finally {\n        isCallingCanDrag = false;\n      }\n    }\n  }, {\n    key: \"isDragging\",\n    value: function isDragging() {\n      if (!this.sourceId) {\n        return false;\n      }\n\n      invariant(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n\n      try {\n        isCallingIsDragging = true;\n        return this.internalMonitor.isDraggingSource(this.sourceId);\n      } finally {\n        isCallingIsDragging = false;\n      }\n    }\n  }, {\n    key: \"subscribeToStateChange\",\n    value: function subscribeToStateChange(listener, options) {\n      return this.internalMonitor.subscribeToStateChange(listener, options);\n    }\n  }, {\n    key: \"isDraggingSource\",\n    value: function isDraggingSource(sourceId) {\n      return this.internalMonitor.isDraggingSource(sourceId);\n    }\n  }, {\n    key: \"isOverTarget\",\n    value: function isOverTarget(targetId, options) {\n      return this.internalMonitor.isOverTarget(targetId, options);\n    }\n  }, {\n    key: \"getTargetIds\",\n    value: function getTargetIds() {\n      return this.internalMonitor.getTargetIds();\n    }\n  }, {\n    key: \"isSourcePublic\",\n    value: function isSourcePublic() {\n      return this.internalMonitor.isSourcePublic();\n    }\n  }, {\n    key: \"getSourceId\",\n    value: function getSourceId() {\n      return this.internalMonitor.getSourceId();\n    }\n  }, {\n    key: \"subscribeToOffsetChange\",\n    value: function subscribeToOffsetChange(listener) {\n      return this.internalMonitor.subscribeToOffsetChange(listener);\n    }\n  }, {\n    key: \"canDragSource\",\n    value: function canDragSource(sourceId) {\n      return this.internalMonitor.canDragSource(sourceId);\n    }\n  }, {\n    key: \"canDropOnTarget\",\n    value: function canDropOnTarget(targetId) {\n      return this.internalMonitor.canDropOnTarget(targetId);\n    }\n  }, {\n    key: \"getItemType\",\n    value: function getItemType() {\n      return this.internalMonitor.getItemType();\n    }\n  }, {\n    key: \"getItem\",\n    value: function getItem() {\n      return this.internalMonitor.getItem();\n    }\n  }, {\n    key: \"getDropResult\",\n    value: function getDropResult() {\n      return this.internalMonitor.getDropResult();\n    }\n  }, {\n    key: \"didDrop\",\n    value: function didDrop() {\n      return this.internalMonitor.didDrop();\n    }\n  }, {\n    key: \"getInitialClientOffset\",\n    value: function getInitialClientOffset() {\n      return this.internalMonitor.getInitialClientOffset();\n    }\n  }, {\n    key: \"getInitialSourceClientOffset\",\n    value: function getInitialSourceClientOffset() {\n      return this.internalMonitor.getInitialSourceClientOffset();\n    }\n  }, {\n    key: \"getSourceClientOffset\",\n    value: function getSourceClientOffset() {\n      return this.internalMonitor.getSourceClientOffset();\n    }\n  }, {\n    key: \"getClientOffset\",\n    value: function getClientOffset() {\n      return this.internalMonitor.getClientOffset();\n    }\n  }, {\n    key: \"getDifferenceFromInitialOffset\",\n    value: function getDifferenceFromInitialOffset() {\n      return this.internalMonitor.getDifferenceFromInitialOffset();\n    }\n  }]);\n\n  return DragSourceMonitorImpl;\n}();", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { wrapConnectorHooks } from './wrapConnectorHooks';\nimport { isRef } from './isRef';\nimport { shallowEqual } from '@react-dnd/shallowequal';\nexport var SourceConnector = /*#__PURE__*/function () {\n  // The drop target may either be attached via ref or connect function\n  // The drag preview may either be attached via ref or connect function\n  function SourceConnector(backend) {\n    var _this = this;\n\n    _classCallCheck(this, SourceConnector);\n\n    _defineProperty(this, \"hooks\", wrapConnectorHooks({\n      dragSource: function dragSource(node, options) {\n        _this.clearDragSource();\n\n        _this.dragSourceOptions = options || null;\n\n        if (isRef(node)) {\n          _this.dragSourceRef = node;\n        } else {\n          _this.dragSourceNode = node;\n        }\n\n        _this.reconnectDragSource();\n      },\n      dragPreview: function dragPreview(node, options) {\n        _this.clearDragPreview();\n\n        _this.dragPreviewOptions = options || null;\n\n        if (isRef(node)) {\n          _this.dragPreviewRef = node;\n        } else {\n          _this.dragPreviewNode = node;\n        }\n\n        _this.reconnectDragPreview();\n      }\n    }));\n\n    _defineProperty(this, \"handlerId\", null);\n\n    _defineProperty(this, \"dragSourceRef\", null);\n\n    _defineProperty(this, \"dragSourceNode\", void 0);\n\n    _defineProperty(this, \"dragSourceOptionsInternal\", null);\n\n    _defineProperty(this, \"dragSourceUnsubscribe\", void 0);\n\n    _defineProperty(this, \"dragPreviewRef\", null);\n\n    _defineProperty(this, \"dragPreviewNode\", void 0);\n\n    _defineProperty(this, \"dragPreviewOptionsInternal\", null);\n\n    _defineProperty(this, \"dragPreviewUnsubscribe\", void 0);\n\n    _defineProperty(this, \"lastConnectedHandlerId\", null);\n\n    _defineProperty(this, \"lastConnectedDragSource\", null);\n\n    _defineProperty(this, \"lastConnectedDragSourceOptions\", null);\n\n    _defineProperty(this, \"lastConnectedDragPreview\", null);\n\n    _defineProperty(this, \"lastConnectedDragPreviewOptions\", null);\n\n    _defineProperty(this, \"backend\", void 0);\n\n    this.backend = backend;\n  }\n\n  _createClass(SourceConnector, [{\n    key: \"receiveHandlerId\",\n    value: function receiveHandlerId(newHandlerId) {\n      if (this.handlerId === newHandlerId) {\n        return;\n      }\n\n      this.handlerId = newHandlerId;\n      this.reconnect();\n    }\n  }, {\n    key: \"connectTarget\",\n    get: function get() {\n      return this.dragSource;\n    }\n  }, {\n    key: \"dragSourceOptions\",\n    get: function get() {\n      return this.dragSourceOptionsInternal;\n    },\n    set: function set(options) {\n      this.dragSourceOptionsInternal = options;\n    }\n  }, {\n    key: \"dragPreviewOptions\",\n    get: function get() {\n      return this.dragPreviewOptionsInternal;\n    },\n    set: function set(options) {\n      this.dragPreviewOptionsInternal = options;\n    }\n  }, {\n    key: \"reconnect\",\n    value: function reconnect() {\n      this.reconnectDragSource();\n      this.reconnectDragPreview();\n    }\n  }, {\n    key: \"reconnectDragSource\",\n    value: function reconnectDragSource() {\n      var dragSource = this.dragSource; // if nothing has changed then don't resubscribe\n\n      var didChange = this.didHandlerIdChange() || this.didConnectedDragSourceChange() || this.didDragSourceOptionsChange();\n\n      if (didChange) {\n        this.disconnectDragSource();\n      }\n\n      if (!this.handlerId) {\n        return;\n      }\n\n      if (!dragSource) {\n        this.lastConnectedDragSource = dragSource;\n        return;\n      }\n\n      if (didChange) {\n        this.lastConnectedHandlerId = this.handlerId;\n        this.lastConnectedDragSource = dragSource;\n        this.lastConnectedDragSourceOptions = this.dragSourceOptions;\n        this.dragSourceUnsubscribe = this.backend.connectDragSource(this.handlerId, dragSource, this.dragSourceOptions);\n      }\n    }\n  }, {\n    key: \"reconnectDragPreview\",\n    value: function reconnectDragPreview() {\n      var dragPreview = this.dragPreview; // if nothing has changed then don't resubscribe\n\n      var didChange = this.didHandlerIdChange() || this.didConnectedDragPreviewChange() || this.didDragPreviewOptionsChange();\n\n      if (didChange) {\n        this.disconnectDragPreview();\n      }\n\n      if (!this.handlerId) {\n        return;\n      }\n\n      if (!dragPreview) {\n        this.lastConnectedDragPreview = dragPreview;\n        return;\n      }\n\n      if (didChange) {\n        this.lastConnectedHandlerId = this.handlerId;\n        this.lastConnectedDragPreview = dragPreview;\n        this.lastConnectedDragPreviewOptions = this.dragPreviewOptions;\n        this.dragPreviewUnsubscribe = this.backend.connectDragPreview(this.handlerId, dragPreview, this.dragPreviewOptions);\n      }\n    }\n  }, {\n    key: \"didHandlerIdChange\",\n    value: function didHandlerIdChange() {\n      return this.lastConnectedHandlerId !== this.handlerId;\n    }\n  }, {\n    key: \"didConnectedDragSourceChange\",\n    value: function didConnectedDragSourceChange() {\n      return this.lastConnectedDragSource !== this.dragSource;\n    }\n  }, {\n    key: \"didConnectedDragPreviewChange\",\n    value: function didConnectedDragPreviewChange() {\n      return this.lastConnectedDragPreview !== this.dragPreview;\n    }\n  }, {\n    key: \"didDragSourceOptionsChange\",\n    value: function didDragSourceOptionsChange() {\n      return !shallowEqual(this.lastConnectedDragSourceOptions, this.dragSourceOptions);\n    }\n  }, {\n    key: \"didDragPreviewOptionsChange\",\n    value: function didDragPreviewOptionsChange() {\n      return !shallowEqual(this.lastConnectedDragPreviewOptions, this.dragPreviewOptions);\n    }\n  }, {\n    key: \"disconnectDragSource\",\n    value: function disconnectDragSource() {\n      if (this.dragSourceUnsubscribe) {\n        this.dragSourceUnsubscribe();\n        this.dragSourceUnsubscribe = undefined;\n      }\n    }\n  }, {\n    key: \"disconnectDragPreview\",\n    value: function disconnectDragPreview() {\n      if (this.dragPreviewUnsubscribe) {\n        this.dragPreviewUnsubscribe();\n        this.dragPreviewUnsubscribe = undefined;\n        this.dragPreviewNode = null;\n        this.dragPreviewRef = null;\n      }\n    }\n  }, {\n    key: \"dragSource\",\n    get: function get() {\n      return this.dragSourceNode || this.dragSourceRef && this.dragSourceRef.current;\n    }\n  }, {\n    key: \"dragPreview\",\n    get: function get() {\n      return this.dragPreviewNode || this.dragPreviewRef && this.dragPreviewRef.current;\n    }\n  }, {\n    key: \"clearDragSource\",\n    value: function clearDragSource() {\n      this.dragSourceNode = null;\n      this.dragSourceRef = null;\n    }\n  }, {\n    key: \"clearDragPreview\",\n    value: function clearDragPreview() {\n      this.dragPreviewNode = null;\n      this.dragPreviewRef = null;\n    }\n  }]);\n\n  return SourceConnector;\n}();"], "sourceRoot": ""}