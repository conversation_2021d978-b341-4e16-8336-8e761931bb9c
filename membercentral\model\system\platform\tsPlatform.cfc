<cfcomponent output="No">

	<!--- ************* -- al<PERSON> hartman searching --->
	<!--- ************, ************, *************  ************  ************* **************  **************    *************  *********** -- sql injection --->
	<!--- *************,*************** synapse -1 --->
	<!--- ************** spamming forms --->
	<!--- *********** bad spidering --->
	<cfset variables.bannedIPs = "***********,*************,************,************,*************,***************,*************,***************,************,************,*************,**************,*************,**************,***********,**************,**************,*************">
	<cfset variables.bannedPages = ".php,file:/,sumthin,nul.cfm,nul..cfm,application.cfm">
	
	<cfset variables.privateIPregex = "(^127\.0\.0\.1)|(^10\.)|(^172\.1[6-9]\.)|(^172\.2[0-9]\.)|(^172\.3[0-1]\.)|(^192\.168\.)"/>
	<cfset variables.ipValidatorRegex = "^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])$"/>
	<cfset variables.objectTimeStamp = getTickCount()>

	<cfif listFindNoCase('local,newlocal',application.MCEnvironment)>
		<!--- Make key developer specific so that we don't trigger reloads on each others boxes --->
		<cfset variables.lastClusterResetAppVarsRequestKey = "#application.hostname#:lastClusterResetAppVarsRequest">
	<cfelse>
		<cfset variables.lastClusterResetAppVarsRequestKey = "lastClusterResetAppVarsRequest">
	</cfif>

	<cfset variables.lastHostResetAppVarsRequestKey = "#application.hostname#:lastHostResetAppVarsRequest">
	<cfset variables.sessionKillListkeyPrefix = "SessionKillList">

	<cffunction name="OnMissingMethod" access="public" returntype="any" output="false" hint="Handles missing method exceptions.">
		<cfargument name="MissingMethodName" type="string" required="true" hint="The name of the missing method." />
		<cfargument name="MissingMethodArguments" type="struct" required="true" hint="The arguments that were passed to the missing method. This might be a named argument set or a numerically indexed set."/>
	
		<cfset var freshObj = "">
		<cfset var freshFunction = "">
		<cflock name="TSPlatformOnMissingMethod" timeout="3">
			<cfscript>
			//race condition: make sure another request didn't already inject function
			if (structKeyExists(this, arguments.MissingMethodName)) {
				freshFunction = this[arguments.MissingMethodName];
				return freshFunction(argumentCollection=arguments.MissingMethodArguments);
			} else {
				//Test if new function exists in cfc, but application scope hasn't been refreshed yet
				freshObj = createObject("component","tsPlatform");
				if (structKeyExists(freshObj, arguments.MissingMethodName)) {
					freshFunction = freshObj[arguments.MissingMethodName];
					structInsert(this,arguments.MissingMethodName,freshFunction);
					return freshFunction(argumentCollection=arguments.MissingMethodArguments);
				} else {
					throw(type="Missing Method", message="Missing method '#ARGUMENTS.missingMethodName#()'. Also checked disk to see if function exists in updated source file");	
				}
			}
			</cfscript>
		</cflock>
	</cffunction>
	
	<cffunction name="getCurrentHostname" access="public" returntype="string" output="no">
		<cfset var local = structNew()>
		
		<!--- cfthread doesnt always have GetHttpRequestData if origin request ends before thread runs --->
		<cftry>
			<cfset local.httpRequestHeaders = GetHttpRequestData().headers>
			<cfif structKeyExists(local.httpRequestHeaders,"X-MC-Real-Hostname")>
				<cfreturn local.httpRequestHeaders["X-MC-Real-Hostname"]>
			<cfelse>
				<cfreturn cgi.SERVER_NAME>
			</cfif>
		<cfcatch type="Any">
			<cfreturn cgi.SERVER_NAME>
		</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="getClientIP" access="public" returntype="string" output="no">
		<cfset var local = structNew()>
		<cfset local.httpRequestHeaders = GetHttpRequestData().headers>
		<cfif structKeyExists(local.httpRequestHeaders,"X-MC-Real-IP")>
			<cfreturn local.httpRequestHeaders["X-MC-Real-IP"]>
		<cfelseif structKeyExists(local.httpRequestHeaders,"X-Forwarded-For")>
			<cfreturn trim(listfirst(local.httpRequestHeaders["X-Forwarded-For"]))>
		<cfelse>
			<cfreturn cgi.remote_addr>
		</cfif>
	</cffunction>

	<cffunction name="getHeaders" access="public" returntype="struct" output="no">
		<cfargument name="headerArray" type="array" required="true" hint="An Array of header names" />
		<cfscript>
			var result = {};
			var wantedHeaders = arguments.headerArray;
			try {
				result = GetHttpRequestData().headers.filter((key, value) => wantedHeaders.containsNoCase(key));
			} catch (e) {
				application.objError.sendError(cfcatch=e);
				result = {};
			}
			return result;
		</cfscript>
	</cffunction>


	<cffunction name="getClientIPLocationInfo" access="public" returntype="struct" output="no">
		<cfscript>
			var local = structNew();
			local.locationInfo = {
				timezone:"",
				latitude:"",
				longitude:"",
				continentCode:"",
				countryCode:"",
				regionCode:"",
				regionName:"",
				cityName:"",
				postalCode:""
			}
			local.headers=GetHttpRequestData().headers;
			if (local.headers.keyExists('CF-Ipcontinent') and local.headers['CF-Ipcontinent'].len() eq 2 )
				local.locationInfo.continentCode = local.headers['CF-Ipcontinent'];
			if (local.headers.keyExists('CF-Timezone') and local.headers['CF-Timezone'].len())
				local.locationInfo.timezone = local.headers['CF-Timezone'];

			if (local.headers.keyExists('CF-Ipcountry') and local.headers['CF-Ipcountry'].len() eq 2 )
				local.locationInfo.countryCode = local.headers['CF-Ipcountry'];
			if (local.headers.keyExists('CF-Region-Code') and local.headers['CF-Region-Code'].len())
				local.locationInfo.regionCode = local.headers['CF-Region-Code'].left(10);
			if (local.headers.keyExists('CF-Region') and local.headers['CF-Region'].len())
				local.locationInfo.regionName = local.headers['CF-Region'];
			if (local.headers.keyExists('CF-Ipcity') and local.headers['CF-Ipcity'].len())
				local.locationInfo.cityName = local.headers['CF-Ipcity'].left(100);
			if (local.headers.keyExists('CF-Postal-Code') and local.headers['CF-Postal-Code'].len())
				local.locationInfo.postalCode = local.headers['CF-Postal-Code'];


			if (local.headers.keyExists('CF-Iplatitude') and local.headers['CF-Iplatitude'].len())
				local.locationInfo.latitude = local.headers['CF-Iplatitude'];
			if (local.headers.keyExists('CF-Iplongitude') and local.headers['CF-Iplongitude'].len())
				local.locationInfo.longitude = local.headers['CF-Iplongitude'];

			return local.locationInfo;
		</cfscript>
	</cffunction>
	
	<cffunction name="isValidIPAddress" access="public" returntype="boolean" output="no">
		<cfargument name="ipaddress" type="string" required="true"/>
	
		<cfreturn refind(variables.ipValidatorRegex,arguments.ipaddress)>
	</cffunction>
	
	<cffunction name="isPrivateIPAddress" access="public" returntype="boolean" output="no">
		<cfargument name="ipaddress" type="string" required="true"/>
	
		<cfreturn (isValidIPAddress(arguments.ipaddress) && refind(variables.privateIPregex,arguments.ipaddress))>
	</cffunction>
	
	<cffunction name="isRequestSecure" access="public" returntype="boolean" hint="relies on webserver setting custom header to indicate protocol">
		<cfset var headers = GetHttpRequestData().headers>
		<cfif structKeyExists(headers,"X-MC-Forwarded-Proto") and headers["X-MC-Forwarded-Proto"] is "https">
			<cfreturn true>
		<cfelse>
			<cfreturn false>
		</cfif>
	</cffunction>
	
	<cffunction name="shouldWeBlock" access="public" returntype="boolean" hint="Should we block this request?" output="no">
		<cfargument name="Event" type="any">
	
		<!--- if not get or post, redirect --->
		<cfif NOT ListFindNoCase("get,post",CGI.Request_Method)>
			<cflog text="#now()#|badMethod|#cgi.Request_Method#" file="floodcheck" type="Information" application="YES">
			<cfreturn true>
		</cfif>
	
		<!--- is IP banned? --->
		<cfif isIPBanned(getClientIP())>
			<cflog text="#now()#|isIPBanned|#getClientIP()#" file="floodcheck" type="Information" application="YES">
			<cfreturn true>
		</cfif>
	
		<!--- is script name banned? if so, block IP --->
		<cfif isPageBanned(getClientIP(),arguments.event)>
			<cflog text="#now()#|isPageBanned|#getClientIP()#|#cgi.SCRIPT_NAME#" file="floodcheck" type="Information" application="YES">
			<cfreturn true>
		</cfif>
		
		<!--- if blank useragent, redirect --->
		<cfif len(trim(CGI.HTTP_USER_AGENT)) is 0>
			<cflog text="#now()#|noUserAgent|#cgi.HTTP_USER_AGENT#" file="floodcheck" type="Information" application="YES">
			<cfreturn true>
		</cfif>
		
		<cfreturn false>
	</cffunction>
	
	<cffunction name="isSpider" access="public" output="no" returntype="boolean" hint="Is browser a known spider?">
		<cfargument name="browser" required="Yes" type="string">
	
		<cfset var knownSpider = false>
	
		<cfif isDefined("session.mcStruct.deviceProfile.type") and findnocase("Bot", session.mcStruct.deviceProfile.type)>
			<cfset knownSpider = true>
		<cfelseif (
				NOT findNoCase("mozilla",arguments.browser)
				AND NOT findNoCase("Blackberry",arguments.browser)
				AND NOT findNoCase("MSIE",arguments.browser)
				AND NOT findNoCase("Firefox",arguments.browser)
				AND NOT findNoCase("Nokia",arguments.browser)
				AND NOT findNoCase("Opera",arguments.browser)
				AND NOT findNoCase("Konqueror",arguments.browser)
				AND NOT findNoCase("SonyEricsson",arguments.browser)
				AND NOT findNoCase("Safari",arguments.browser)
			  )
			  OR findNoCase("spider",arguments.browser)
			  OR findNoCase("crawler",arguments.browser)
			  OR findNoCase("searchbot",arguments.browser)
			  OR findNoCase("searchme",arguments.browser)
			  OR findNoCase("AdsBot-Google",arguments.browser)
			  OR findNoCase("Mediapartners-Google",arguments.browser)
			  OR findNoCase("googlebot",arguments.browser)
			  OR findNoCase("Synapse",arguments.browser)
			  OR findNoCase("slurp",arguments.browser)
			  OR findNoCase("robot",arguments.browser)
			  OR findNoCase("siteimprove",arguments.browser)
			  OR findNoCase("<script",arguments.browser)
			  >
			<cfset knownSpider = true>
		</cfif>
	
		<cfreturn knownSpider>
	</cffunction>
	
	<cffunction name="isIPBanned" access="private" returntype="boolean" hint="Check client IP against banned list" output="no">
		<cfargument name="ip" required="true" type="string">
		<cfreturn listFind(variables.bannedIPs,arguments.ip)>
	</cffunction>
	
	<cffunction name="isPageBanned" access="private" returntype="boolean" hint="Check requested page against banned list" output="no">
		<cfargument name="ip" required="true" type="string">
		<cfargument name="Event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.rc = arguments.event.getCollection()>
	
		<cfloop list="#variables.bannedPages#" index="local.ThisPage">
			<cfif FindNoCase(local.ThisPage,cgi.SCRIPT_NAME)>
				<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
				<cfreturn true>
			</cfif>
		</cfloop>
		
		<cfif findNoCase("EXEC(",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("EXEC%28",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("CHAR(45",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("%2540version",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("@@version",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("%3Cscript",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("etc/passwd",CGI.QUERY_STRING)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"qSearchType") and findNoCase("http://",local.rc.qSearchType)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"SearchType") and findNoCase("http://",local.rc.SearchType)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"s_key_all") and findNoCase("http://",local.rc.s_key_all)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"s_key_phrase") and findNoCase("http://",local.rc.s_key_phrase)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"s_key_one") and findNoCase("http://",local.rc.s_key_one)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"s_key_x") and findNoCase("http://",local.rc.s_key_x)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"eventsAction") and findNoCase("http://",local.rc.eventsAction)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"start") and findNoCase("http://",local.rc.start)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"panel") and isSimpleValue(local.rc.panel) and findNoCase("http://",local.rc.panel)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"eventid") and findNoCase("http://",local.rc.eventid)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"membersAction") and findNoCase("http://",local.rc.membersAction)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif structKeyExists(local.rc,"memberFilter") and findNoCase("http://",local.rc.memberFilter)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		<cfelseif findNoCase("rapiditalialinux",CGI.HTTP_REFERER)>
			<cfset structInsert(application.floodCheck.ipsBlocked, arguments.ip, now(), true)>
			<cfreturn true>
		</cfif>
	
		<cfreturn false>
	</cffunction>
	
	<cffunction name="getDeviceCapabilities" access="public" returntype="struct" hint="Get session device capabilities" output="no">
		<cfscript>
			var local = structNew();
			local.deviceProfile = structNew();
			local.ignoredUseragents = "CFSCHEDULE";
	
			if (not listFindNoCase(local.ignoredUseragents, cgi.http_user_agent)) {        
				// Cache rollover once per week
				local.cacheKeyPrefix = 'whatismybrowser';
				local.useragentHash = hash(cgi.http_user_agent);
				local.cacheKey = local.cacheKeyPrefix & ':' & local.useragentHash;
				local.cacheResult = false;
				local.previousCachedResultsFound = false;
	
				try {
					local.deviceProfile = application.mcCacheManager.clusterGetValue(keyname=local.cacheKey);
					local.cacheResult = false;
					local.previousCachedResultsFound = true;
				} catch(e) {
					local.previousCachedResultsFound = false;
					local.cacheResult = true;
				}
	
				if (not local.previousCachedResultsFound) {
					try {
						local.args = {
							"user_agent": cgi.http_user_agent,
							"parse_options": {
								"allow_servers_to_impersonate_devices": false,
								"return_metadata_for_useragent": false,
								"dont_sanitize_before_parse": false
							}
						};
	
						// get device info
						cfhttp(method="post", url="https://api.whatismybrowser.com/api/v2/user_agent_parse", result="local.APIResult", timeout="1", throwonerror="true") {
							cfhttpparam(type="header", name="X-API-KEY", value="#application.strPlatformAPIKeys.whatismybrowser.key#");
							cfhttpparam(type="body", value="#serializeJSON(local.args)#");
						}
	
						local.response = deserializeJSON(local.APIResult.fileContent);
						local.software_type = local.response.parse.software_type ?: "";
						local.software_sub_type = local.response.parse.software_sub_type ?: "";
						local.hardware_type = local.response.parse.hardware_type ?: "";
						local.hardware_sub_type = local.response.parse.hardware_sub_type ?: "";
	
						local.deviceProfile = {
							"request_date": dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss"),
							"type": local.software_type,
							"subtype": local.software_sub_type,
							"hardware_type": local.hardware_type,
							"hardware_subtype": local.hardware_sub_type,
							"is_tablet": local.hardware_type EQ 'mobile' AND local.hardware_sub_type EQ 'tablet' ? 1 : 0,
							"is_small_screen": local.hardware_type EQ 'mobile' AND listFindNoCase("phone,phablet,handheld-game,music-player,pda,wearable",local.hardware_sub_type) ? 1 : 0,
							"is_bot": local.software_type EQ 'bot' ? 1 : 0,
							"browser_name": local.response.parse.software_name_code ?: "",
							"browser_version": local.response.parse.software_version ?: "",
							"device_os": local.response.parse.operating_system_name_code ?: "",
							"is_abusive": local.response.parse.is_abusive ?: "",
							"is_weird": local.response.parse.is_weird ?: "",
							"is_restricted": local.response.parse.is_restricted ?: "",
							"is_spam": local.response.parse.is_spam ?: ""
						};
	
						if (local.cacheResult) {
							// Change timespan to 1 week
							application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=local.deviceProfile, timespan=createtimespan(7,0,0,0), idletime=createtimespan(1,0,0,0));
						}
					} catch(e) {
						if (NOT findNoCase("Request Time-out", e.message))
							application.objError.sendError(cfcatch=e);
					}
				}
			}
	
			return local.deviceProfile;
		</cfscript>
	</cffunction>	
	
	<cffunction name="resetSession" access="public" returntype="void" output="false">
		<cfargument name="doRedirect" type="boolean" required="true">
		<cfargument name="customRedirectURL" type="string" required="false" default="">
		
		<cfset var local = StructNew()>
		<cfscript>
			try {sessionInvalidate();} catch(any e){}
		</cfscript>
	
		<cfset local.expirationDateTime = GetHttpTimeString(DateAdd("d", -1, Now()))>
		<cfloop index="local.cookieName" list="cfid,cftoken,jsessionID">
			<cfheader name="Set-Cookie" value="#lcase(local.cookieName)#=;expires=#local.expirationDateTime#;path=/">
			<cfheader name="Set-Cookie" value="#ucase(local.cookieName)#=;expires=#local.expirationDateTime#;path=/">
		</cfloop>
		<cfif doRedirect>
			<cfheader name="X-MC-REDIRECTREASON" value="reset Session requested Redirect">
			<cfset local.defaultRefreshURL = "/?" & application.objWebsite.removeURLParam("logout",cgi.query_string)>
			<cftry>
				<cfif len(arguments.customRedirectURL)>
					<cflocation url="#arguments.customRedirectURL#" addtoken="false">
				<cfelse>
					<cflocation url="#local.defaultRefreshURL#" addtoken="false">
				</cfif>
				<cfcatch type="Any">
					<cflocation url="#local.defaultRefreshURL#" addtoken="false">
				</cfcatch>
			</cftry>
		</cfif>
	</cffunction>
	
	<cffunction name="hasMinimumOSVersion" access="public" returntype="boolean" output="false" hint="verifies that the user agent reports a version equal to or higher than the specified OS/version">
		<cfargument name="OSVersionList" type="string" required="true" hint="list of operating systems and the minimum version number. Format is: OSName|5.5,OSName|9">
		
		<cfset var local = StructNew()>
		<cfset local.validVersion=true>
		
		<cfloop list="#arguments.OSVersionList#" index="local.OSVersion">
			<cfset local.OS=GetToken(local.OSVersion,1,"|")>
			<cfset local.listVersion=GetToken(local.OSVersion,2,"|")>
			<cfif CompareNoCase(local.OS, "ipad") eq 0 OR CompareNoCase(local.OS, "ipod") eq 0 OR CompareNoCase(local.OS, "iphone") eq 0>
				<cfif reFindNoCase("(#local.OS#)", cgi.user_agent) neq 0>
					<cfset local.versionArray=reMatchNoCase("OS (\d+)_(\d+)",cgi.http_user_agent)>
					<cfset local.version="#replaceNoCase(replaceNoCase(local.versionArray[1],"OS ",""),"_",".","all")#">
					<cfif local.version LT local.listVersion>
						<cfset local.validVersion = false>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>
		<cfreturn local.validVersion>
	</cffunction>

	<cffunction name="responseHeaderExists" access="public" returntype="boolean" output="no">
		<cfargument name="header" type="string" required="true"/>
		<cfreturn getPageContext().getResponse().containsHeader(header)>
	</cffunction>
	<cffunction name="appendResponseHeaderString" access="public" returntype="void" output="no" hint="Append header to response. Preexisting headers with same name will still remain">
		<cfargument name="header" type="string" required="true"/>
		<cfargument name="value" type="string" required="true"/>

		<cfset getPageContext().getResponse().addHeader(header,value)>
	</cffunction>
	<cffunction name="setResponseHeaderString" access="public" returntype="void" output="no" hint="Set header in response. Overwrites preexisting headers with same name">
		<cfargument name="header" type="string" required="true"/>
		<cfargument name="value" type="string" required="true"/>

		<cfset getPageContext().getResponse().setHeader(header,value)>
	</cffunction>

	<cffunction name="appendResponseHeaderInt" access="public" returntype="void" output="no" hint="Append header to response. Preexisting headers with same name will still remain">
		<cfargument name="header" type="string" required="true"/>
		<cfargument name="value" type="string" required="true"/>

		<cfset getPageContext().getResponse().addIntHeader(header,value)>
	</cffunction>
	<cffunction name="setResponseHeaderInt" access="public" returntype="void" output="no" hint="Set header in response. Overwrites preexisting headers with same name">
		<cfargument name="header" type="string" required="true"/>
		<cfargument name="value" type="string" required="true"/>

		<cfset getPageContext().getResponse().setIntHeader(header,value)>
	</cffunction>

	<cffunction name="setProxyCacheSeconds" access="public" returntype="void" output="no" hint="Modifies response headers to determine how long reverse proxy should cache this request">
		<cfargument name="seconds" type="numeric" required="true"/>
		<cfargument name="secondsStaleWhileRevalidate" type="numeric" required="false" default="0"/>

		<cfif arguments.seconds gt 0 and arguments.secondsStaleWhileRevalidate and listfind("get,head,options",lcase(cgi.request_method))>
			<cfset setResponseHeaderString("Cache-Control","public, max-age=0, s-maxage=#seconds#, stale-while-revalidate=#secondsStaleWhileRevalidate#") />
		<cfelseif arguments.seconds gt 0 and listfind("get,head,options",lcase(cgi.request_method))>
			<cfset setResponseHeaderString("Cache-Control","public, max-age=0, s-maxage=#seconds#") />
		<cfelse>
			<cfset setResponseHeaderString("Cache-Control","private, must-revalidate, proxy-revalidate, no-transform") />
		</cfif>
	</cffunction>

	<cffunction name="triggerClusterWideResetAppVars" access="public" returntype="struct" output="false">
		<cfset var local = structNew()>
		<cfset local.response = {clusterWideResetRequested=false}>
		<cfset local.currentTimeStampString = dateTimeFormat(now(),"yyyymmddHHnnss")>
		<cfset local.oneMinuteAgoTimeStampString = dateTimeFormat(dateadd("n", -1, now()),"yyyymmddHHnnss")>

		<cftry>
			<cfset local.lastClusterResetAppVarsRequest = application.mcCacheManager.clusterGetValue(keyname=variables.lastClusterResetAppVarsRequestKey)>
			<cfset local.response.lastClusterResetAppVarsRequest = local.lastClusterResetAppVarsRequest>
			<cfset local.response.currentTimeStampString = local.currentTimeStampString>

			<cfif local.oneMinuteAgoTimeStampString GT local.lastClusterResetAppVarsRequest>
				<cfset application.mcCacheManager.clusterSetValue(keyname=variables.lastClusterResetAppVarsRequestKey, value=local.currentTimeStampString)>
				<cfset systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [tsPlatform.cfc] triggerClusterWideResetAppVars received and executed by #server.machinename#. Previous ClusterWide ResetAppVars Request #local.LastClusterResetAppVarsRequest#", addNewLine=true, doErrorStream=false)>
				<cfset local.response.clusterWideResetRequested = true>
			<cfelse>
				<cfset systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [tsPlatform.cfc] triggerClusterWideResetAppVars request received and ignored by #server.machinename#. Previous ClusterWide ResetAppVars Request #local.LastClusterResetAppVarsRequest#", addNewLine=true, doErrorStream=false)>
			</cfif>
			<cfcatch type="any">
				<!--- The key has been deleted or does not exist --->
				<cfset application.mcCacheManager.clusterSetValue(keyname=variables.lastClusterResetAppVarsRequestKey, value=local.currentTimeStampString)>
			</cfcatch>
		</cftry>    
		<cfreturn local.response>
	</cffunction>
	<cffunction name="triggerHostResetAppVars" access="public" returntype="struct" output="false">
		<cfargument name="hostname" type="string" required="false" default="">
		<cfset var local = structNew()>
		<cfset local.response = {hostResetRequested=false}>
		<cfset local.lastHostResetRequestKeyForRequestedHost = "#arguments.hostname#:lastHostResetAppVarsRequest">

		<cfset local.currentTimeStampString = dateTimeFormat(now(),"yyyymmddHHnnss")>
		<cfset local.oneMinuteAgoTimeStampString = dateTimeFormat(dateadd("n", -1, now()),"yyyymmddHHnnss")>

		<cftry>
			<cfset local.lastResetRequest = application.mcCacheManager.clusterGetValue(keyname=local.lastHostResetRequestKeyForRequestedHost)>
			<cfset local.response.lastResetRequest = local.lastResetRequest>
			<cfset local.response.currentTimeStampString = local.currentTimeStampString>
			<cfif local.oneMinuteAgoTimeStampString GT local.lastResetRequest>
				<cfset application.mcCacheManager.clusterSetValue(keyname=local.lastHostResetRequestKeyForRequestedHost, value=local.currentTimeStampString)>
				<cfset local.response.hostResetRequested = true>
				<cfset systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [tsPlatform.cfc] triggerHostResetAppVars received and executed by #server.machinename# for containers on #arguments.hostname# (key: #local.lastHostResetRequestKeyForRequestedHost#). Previous #arguments.hostname# ResetAppVars Request #local.lastResetRequest#", addNewLine=true, doErrorStream=false)>
			<cfelse>
				<cfset systemOutput(obj="#dateTimeFormat(now(),"yyyy-mm-dd HH:nn:ss")# [tsPlatform.cfc] triggerHostResetAppVars request received and ignored by #server.machinename# for containers on #arguments.hostname# (key: #local.lastHostResetRequestKeyForRequestedHost#). Previous #arguments.hostname# ResetAppVars Request #local.lastResetRequest#", addNewLine=true, doErrorStream=false)>
			</cfif>
			<cfcatch type="any">
				<!--- The key has been deleted or does not exist --->
				<cfset application.mcCacheManager.clusterSetValue(keyname=local.lastHostResetRequestKeyForRequestedHost, value=local.currentTimeStampString)>
			</cfcatch>
		</cftry>    

		<cfreturn local.response>
	</cffunction>

	<cffunction name="isResetAppVarsNeeded" access="public" returntype="boolean" output="false">
		<cfset var local = structNew()>
		<cfset local.isResetAppVarsNeeded = false>

		<!--- Check if reset appVars should be called.  Check on every request and ignore resetAppVars requests since key is set here--->
		<cfif structKeyExists(application, "ResetAppVarsTimeStampString") and not structKeyExists(application,"triggeredResetAppVarsInProgress")>
			<cftry>
				<!--- Get Cluster Last ResetAppVars Request and update it to current container start time if it doesn't exist  --->
				<cftry>
					<cfset local.lastClusterResetAppVarsRequest = application.mcCacheManager.clusterGetValue(keyname=variables.lastClusterResetAppVarsRequestKey)>
					<cfcatch type="any">
						<cfset local.lastClusterResetAppVarsRequest = application.ResetAppVarsTimeStampString>
						<cfset application.mcCacheManager.clusterSetValue(keyname=variables.lastClusterResetAppVarsRequestKey, value=application.ResetAppVarsTimeStampString)>
					</cfcatch>
				</cftry>

				<!--- Get Host Last ResetAppVars Request and update it to current container start time if it doesn't exist  --->

				<cftry>
					<cfset local.lastHostResetAppVarsRequest = application.mcCacheManager.clusterGetValue(keyname=variables.lastHostResetAppVarsRequestKey)>
					<cfcatch type="any">
						<cfset local.lastHostResetAppVarsRequest = application.ResetAppVarsTimeStampString>
						<cfset application.mcCacheManager.clusterSetValue(keyname=variables.lastHostResetAppVarsRequestKey, value=application.ResetAppVarsTimeStampString)>
					</cfcatch>
				</cftry>
				<!--- Figure out which request date is most recent --->
				<cfif local.lastClusterResetAppVarsRequest gt local.lastHostResetAppVarsRequest>
					<cfset local.mostRecentRequest = local.lastClusterResetAppVarsRequest>
				<cfelse>
					<cfset local.mostRecentRequest = local.lastHostResetAppVarsRequest>
				</cfif>

				<cfset local.isResetAppVarsNeeded = (local.mostRecentRequest GT application.ResetAppVarsTimeStampString)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, customMessage="exception caught trying to check for cluster or host-specific RestAppVars", objectToDump=local)>
				</cfcatch>
			</cftry>    
		</cfif>
		<cfreturn local.isResetAppVarsNeeded>
	</cffunction>

	<cffunction name="addSessionToKillList" access="public" returntype="struct" output="no" hint="Adds a session ID the session kill list.">
		<cfargument name="cfid" type="string" required="true">
		<cfargument name="killReasonCode" type="string" required="true">
		<cfargument name="isTestMode" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.response = { killList: "" }>

		<cfquery name="local.qryUpdateLogin" datasource="#application.dsn.platformstatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @killReasonID int, @loginID int, @isTestMode bit=0, @siteID int, @memberID int;

			<cfif arguments.isTestMode>
				set @isTestMode=1;
			</cfif>
			
			select @killReasonID = killReasonID
			from dbo.tblKillReasons
			where killReasonCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.killReasonCode#">;

			select @loginID = loginID, @siteID = siteID, @memberID = memberID
			from dbo.ams_memberLoginsActive
			where cfid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.cfid#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			if @loginID IS NOT NULL
				update ml
				set killRequestedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">, 
					killReasonID = @killReasonID,
					isKillTestMode = @isTestMode
				from dbo.ams_memberLogins as ml
				where ml.siteID = @siteID 
				and ml.loginID = @loginID;
		</cfquery>

		<cfset local.cachekey = "#variables.sessionKillListkeyPrefix#:#arguments.cfid#">
		<cfset application.mcCacheManager.clusterSetValue(keyname=local.cachekey, value=1)>
		<cfreturn local.response>
	</cffunction>


	<cffunction name="removeSessionFromKillList" access="private" returntype="void" output="no" hint="Removes a session ID from the session kill list.">
		<cfargument name="cfid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.response = { killList: "" }>

		<cfset local.cachekey = "#variables.sessionKillListkeyPrefix#:#arguments.cfid#">
		<cfset application.mcCacheManager.clusterDeleteValue(keyname=local.cachekey)>
	</cffunction>

	<cffunction name="shouldSessionBeKilled" access="public" returntype="struct" output="no" hint="Test if a session should be killed and return kill mode">
		<cfargument name="cfid" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="false" default="0">
		<cfargument name="loginID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<cfset local.cachekey = "#variables.sessionKillListkeyPrefix#:#arguments.cfid#">
		<cfset local.killcheck = application.mcCacheManager.clusterGetValue(keyname=local.cachekey, defaultValue=false)>


		<cfset local.returnStruct = {
			"kill" = local.killcheck,
			"killReasonCode" = "",
			"notificationTitle" = "",
			"notificationText" = "",
		}>

		<cfif local.returnStruct.kill and not arguments.loginID>
			<cfset local.returnStruct.killReasonCode = "Manual">
		<cfelseif local.returnStruct.kill>
			<!--- get kill mode --->
			<cfquery name="local.qryKillDetails" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				declare @loginID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select kr.killReasonCode, kr.notificationTitle, kr.notificationText, ml.isKillTestMode
				from dbo.ams_memberLogins as ml
				inner join dbo.tblKillReasons as kr on kr.killReasonID = ml.killReasonID
					and ml.siteID = @siteID
					and ml.loginID = @loginID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryKillDetails.recordcount>
				<cfset local.returnStruct.killReasonCode = local.qryKillDetails.killReasonCode>
				<cfset local.returnStruct.notificationTitle = local.qryKillDetails.notificationTitle>
				<cfset local.returnStruct.notificationText = local.qryKillDetails.notificationText>
				<cfset local.returnStruct.isKillTestMode = val(local.qryKillDetails.isKillTestMode)>
			<cfelse>
				<cfset local.returnStruct.killReasonCode = "Manual">
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>

	</cffunction>

	<cffunction name="recordSessionKilled" access="public" returntype="void" output="no" hint="Removes a session ID from the session kill list.">
		<cfargument name="cfid" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="false" default="0">
		<cfargument name="loginID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>

		<cfif arguments.loginID>
			<cfquery name="local.qryUpdateDateKilled" datasource="#application.dsn.platformstatsMC.dsn#">
				update dbo.ams_memberLogins
				set dateKilled = getDate()
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and loginID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginID#">
			</cfquery>
		</cfif>

		<cfset removeSessionFromKillList(arguments.cfid)>
	</cffunction>

	<cffunction name="getSignedCookieKey" access="private" returntype="string" output="no">
		<cfreturn "#chr(7)##chr(9)##chr(11)#" & lcase(getCurrentHostname()) & "-membercentral-Hosted-Website">
	</cffunction>

	<cffunction name="getRawCookies" access="private" returntype="struct" output="no">
		<cfscript>
			var local = structNew();
			local.rawcookieheader = (GetHttpRequestData().headers?.cookie ?: "");
			local.cookieStruct = listToArray(local.rawcookieheader,";").reduce(function(accumulator, element) {
				var key = listFirst(element, "=").trim();
				var value = listLast(element, "=").trim();
				accumulator[key] = value;
				return accumulator;
			}, {});
			return local.cookieStruct;
		</cfscript>
	</cffunction>
	
	<cffunction name="setSignedCookie" access="public" returntype="void" output="no" hint="generates a signature for the cookie and prepends it to the stored cookie value">
		<cfargument name="cookiename" type="string" required="true">
		<cfargument name="value" type="string" required="true">
		<cfargument name="expires" type="any" required="false" default="">
		<cfargument name="allowCrossDomainWhenSecure" type="boolean" required="false" default="false">

		<cfscript>
			var local = structNew();
			local.cookiedomain = lcase(getCurrentHostname());
			local.signature = hmac(arguments.value, getSignedCookieKey(), "HMACSHA256", "utf-8");
			local.cookievalue = urlEncodedFormat(local.signature & chr(9) & arguments.value);
			local.secure = isRequestSecure();

			if (isRequestSecure() and arguments.allowCrossDomainWhenSecure)
				local.cookieSecurity= " Secure; HttpOnly; SameSite=None; Domain=.#local.cookiedomain#; path=/";
			else if (isRequestSecure())
				local.cookieSecurity= " Secure; HttpOnly; SameSite=Lax; Domain=.#local.cookiedomain#; path=/";
			else
				local.cookieSecurity=" HttpOnly; SameSite=Lax; Domain=.#local.cookiedomain#; path=/";

			// attempt to replicate how cfcookie handles various values of expires
			if (not len(arguments.expires))
				local.expires = "";
			else if (isdate(arguments.expires))
				local.expires = GetHttpTimeString(arguments.expires);
			else if (isnumeric(arguments.expires))
				local.expires = GetHttpTimeString(DateAdd("d", arguments.expires, Now()));
			else if (arguments.expires eq "now")
				local.expires = GetHttpTimeString(DateAdd("d", -1, Now()));
			else if (arguments.expires eq "never")
				local.expires = GetHttpTimeString(DateAdd("y", 30, Now()));
			else 
				local.expires = "";

			if (len(local.expires)) 
				local.expires = " Expires=#local.expires#;";

			cfheader(name="Set-Cookie", value="#lcase(arguments.cookiename)#=#local.cookievalue#;#local.expires##local.cookieSecurity#");

		</cfscript>
	</cffunction>

	<cffunction name="readSignedCookie" access="public" returntype="struct" output="no" hint="validates the signature and returns data from a signed cookie">
		<cfargument name="cookiename" type="string" required="true">
		<cfscript>
			var local = structNew();
			local.returnstruct = {cookieName=arguments.cookieName, cookieFound=false, isValid=false, verifiedValue = ""};
			try {
				if (structKeyExists(cookie,arguments.cookiename)) {
					local.rawcookie = getRawCookies()[arguments.cookiename];
					local.returnStruct.cookieFound=true;
					local.value = urlDecode(local.rawcookie);
					local.submittedSignature = listFirst(local.value,chr(9));
					local.submittedData = listRest(local.value,chr(9));
					//test submitted data
					local.signature = hmac(local.submittedData, getSignedCookieKey(), "HMACSHA256", "utf-8");
					if (local.signature eq local.submittedSignature)  {
						local.returnstruct.isValid=true
						local.returnstruct.verifiedValue=local.submittedData;
					}
				}
			} catch(e) {};
			return local.returnstruct;
		</cfscript>
	</cffunction>

	<cffunction name="getUseMemberIDFromMCIDMECookie" access="public" returntype="numeric" output="no">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.useMemberID = 0>

		<cfset local.mcidmeCookieVal = readSignedCookie(cookiename="mcidme")>
		<cfif NOT local.mcidmeCookieVal.isValid>
			<cfset setSignedCookie(cookiename="mcidme", value="", expires="-1")>
		<cfelse>
			<cfset local.memberIDFromCookie = int(val(GetToken(local.mcidmeCookieVal.verifiedValue,1,'|')))>
			<cfset local.orgIDFromCookie = int(val(GetToken(local.mcidmeCookieVal.verifiedValue,2,'|')))>
			<cfif local.orgIDFromCookie EQ arguments.orgID>
				<cfif NOT application.objUser.IsSiteAdminByMemberID(orgID=local.orgIDFromCookie, memberID=local.memberIDFromCookie)>
					<cfset local.useMemberID = application.objCommon.getActiveMemberID(memberID=local.memberIDFromCookie)>
					<cfif local.useMemberID gt 0 AND local.useMemberID neq local.memberIDFromCookie>
						<cfset setSignedCookie(cookiename="mcidme", value="#local.useMemberID#|#local.orgIDFromCookie#|#GetTickCount()#", expires="90")>
					</cfif>
				<cfelse>
					<cfset setSignedCookie(cookiename="mcidme", value="", expires="-1")>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.useMemberID>
	</cffunction>

	<cffunction name="getPlatformAPIKeys" access="public" returntype="struct" output="no">
		<cfscript>
		var strKeys = {
			"halonapi" = { "key"="eS1DVXNwTUIzYVlOZ2RfU3hKTno6WC0wb3RYTWZSQVc5cWZTajJXNHlkZw==" },
			"cicero" = { "key"="****************************************" },
			"cloudflare" = { "key"="21ac8c03b27d3513ea2dd7f1aba2fd6a87d04", "email"="<EMAIL>" },
			"datadog" = { "clienttoken"="pub01c3dc4781a2d0d60b9e4dd9ef0aa90b" },
			"encoding" = { "userkey"="913bf50bf3ff0a5cc911b7d9de18db8f" },
			"github" = { "token"="****************************************" },
			"route53" = { "key"="********************", "secret"="09Q50fRTP6znEnDgWUUitPWYpWX0/ZXaGJaqOh3E" },
			"s3" = { "key"="********************", "secret"="Msd/+JBR/uxq+2p2YnqOMxTWCFtNaxGG7CuEk8e/" },
			"s3encoding" = { "key"="********************", "secret"="GhTBZI5cqaVKrQ8jWDyUKPGixSIItP/lIMYjvkx3" },
			"smartystreets" = { "authid"="ee421d73-5742-2139-71bd-95dc43ef8d76", "token"="gXQ4qaX97gYAGNVWIWVN" },
			"whatismybrowser" = { "key"="3ee1ad707d4f02834bb2a02c4ec33ea6" },
			"zendesk" = { "email"="<EMAIL>", "token"="MgtIv7TTgRALkTBuvxpiAvFryePDSjcxUEzqq2Jz" },
			"tsgpay" = { 
				"public"="BIgHnmjRpAELLfGFyv6pGcN+A2p614+BOse4WcX8d1al3f6CtUSz33j5BAseV7O16ZTh0PoHdHzgAlHoh/XvMDg=",
				"privateKey" = "
*************************************************************************************************************************************************************************************************************************************************************************
				"
			},
			"swgpay" = { 
				"public"="BEdrS8kMBBYVRq0e/DTcol45T0A9lcXEO2f1KxAt2Q6/J4A1KHLFF9cNP9iF67GqBC2mgwd6+3bfefA8xnamtY8=",
				"privateKey" = "
*************************************************************************************************************************************************************************************************************************************************************************
				"
			}
		};
		switch (application.MCEnvironment) {
			case "production":
				strKeys.insert("authorize", { "name"="", "transactionkey"="" } );
				strKeys.insert("affinipaycc", { "public"="", "secret"="", "merchantid"="" } );
				strKeys.insert("mcpayecheck", { "merchantid"="", "login"="", "pwd"="" } );
				strKeys.insert("tokenex", { "id"="****************", "key"="k41ggBRKGVW90GAPjh2YFSTg0EjJei4iKLMauR3a" } );
				strKeys.insert("googlerecaptchav2", { "public"="6LernD8eAAAAAFFgHPFol7eS4oJ-DuXFrqGXfPVu", "secret"="6LernD8eAAAAAHfW690z2ntxCF8XZU_Zir2p_gg5" } );
				strKeys.insert("twilio", { "accountsid"="**********************************", "authtoken"="1d65c33d1437105a141c743eed08956c" } );
				strKeys.insert("zoom", { "account"="x1Di01BlQ7-XJlWScI9uQw", "client"="Y8x2vSUkQjuPBrDJVj56Q", "secret"="eICO4WgsTA1lqd26ylyTj2igRNNviQOi" } );
				break;
			case "beta":
				strKeys.insert("authorize", { "name"="9JdzR5Z6K", "transactionkey"="7p7a4Vbt2e6FvF4x" } );
				strKeys.insert("affinipaycc", { "public"="m_mhcTIhnkQqa8WcjMSjIYyg", "secret"="QXt_RARBS5SfnpHltboFdQyPbvcGra0noHbemVsiEjzdQFrSZJsV15ee9ozVTyEV", "merchantid"="F0cqoEgwTfS-8D8aWbMmrw" } );
				strKeys.insert("mcpayecheck", { "merchantid"="**********", "login"="API**********", "pwd"="Temp1234!" } );
				strKeys.insert("tokenex", { "id"="****************", "key"="u1eiGMZgTJpMZ5ZTtt1Ss5mF6hC5BhPEUgGF8y69" } );
				strKeys.insert("googlerecaptchav2", { "public"="6Ld-WTweAAAAAHCyweY8MbB7iNqg4c-gc93b4L5I", "secret"="6Ld-WTweAAAAAG1KSompiOEDNJRP-QQW8_bHqvSg" } );
				strKeys.insert("twilio", { "accountsid"="**********************************", "authtoken"="86f3a3296f12bb2291f97fc853a7339a" } );
				strKeys.insert("zoom", { "account"="x1Di01BlQ7-XJlWScI9uQw", "client"="axXS2dFfRNadXu87ft_0Qg", "secret"="yas06zrHJr6dhLLT6MBVjQGiYN4ad0p2" } );
				break;
			default:
				strKeys.insert("authorize", { "name"="9JdzR5Z6K", "transactionkey"="7p7a4Vbt2e6FvF4x" } );
				strKeys.insert("affinipaycc", { "public"="m_mhcTIhnkQqa8WcjMSjIYyg", "secret"="QXt_RARBS5SfnpHltboFdQyPbvcGra0noHbemVsiEjzdQFrSZJsV15ee9ozVTyEV", "merchantid"="F0cqoEgwTfS-8D8aWbMmrw" } );
				strKeys.insert("mcpayecheck", { "merchantid"="**********", "login"="API**********", "pwd"="Temp1234!" } );
				strKeys.insert("tokenex", { "id"="****************", "key"="u1eiGMZgTJpMZ5ZTtt1Ss5mF6hC5BhPEUgGF8y69" } );
				strKeys.insert("googlerecaptchav2", { "public"="6Ld-WTweAAAAAHCyweY8MbB7iNqg4c-gc93b4L5I", "secret"="6Ld-WTweAAAAAG1KSompiOEDNJRP-QQW8_bHqvSg" } );
				strKeys.insert("twilio", { "accountsid"="**********************************", "authtoken"="86f3a3296f12bb2291f97fc853a7339a" } );
				strKeys.insert("zoom", { "account"="x1Di01BlQ7-XJlWScI9uQw", "client"="k004o0EBTomn3mXzSGfoWg", "secret"="xir6KgmRrGgU5fVVKKwOH3gvbeVkaflt" } );
				break;
		}
		return strKeys;
		</cfscript>
	</cffunction>
</cfcomponent>
