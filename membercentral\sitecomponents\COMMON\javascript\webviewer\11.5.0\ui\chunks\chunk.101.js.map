{"version": 3, "sources": ["webpack:///./src/ui/node_modules/core-js/modules/es.number.is-integer.js"], "names": ["$", "target", "stat", "isInteger"], "mappings": "gFAAQ,EAAQ,GAKhBA,CAAE,CAAEC,OAAQ,SAAUC,MAAM,GAAQ,CAClCC,UALqB,EAAQ", "file": "chunks/chunk.101.js", "sourcesContent": ["var $ = require('../internals/export');\nvar isIntegralNumber = require('../internals/is-integral-number');\n\n// `Number.isInteger` method\n// https://tc39.es/ecma262/#sec-number.isinteger\n$({ target: 'Number', stat: true }, {\n  isInteger: isIntegralNumber\n});\n"], "sourceRoot": ""}