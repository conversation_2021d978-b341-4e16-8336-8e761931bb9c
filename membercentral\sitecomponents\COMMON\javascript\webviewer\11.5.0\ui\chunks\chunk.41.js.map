{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/applyRedactions.js", "webpack:///./src/ui/src/components/AnnotationStylePopup/AnnotationStylePopup.scss?a51c", "webpack:///./src/ui/src/components/AnnotationStylePopup/AnnotationStylePopup.scss", "webpack:///./src/ui/src/components/AnnotationStylePopup/AnnotationStylePopup.js", "webpack:///./src/ui/src/components/AnnotationStylePopup/index.js", "webpack:///./src/ui/src/components/AnnotationPopup/AnnotationPopup.scss?e728", "webpack:///./src/ui/src/components/CalibrationPopup/CalibrationPopup.scss?4bd2", "webpack:///./src/ui/src/components/CalibrationPopup/CalibrationPopup.scss", "webpack:///./src/ui/src/components/AnnotationPopup/AnnotationPopup.scss", "webpack:///./src/ui/src/components/DatePicker/DatePicker.js", "webpack:///./src/ui/src/components/DatePicker/index.js", "webpack:///./src/ui/src/components/CalibrationPopup/CalibrationPopup.js", "webpack:///./src/ui/src/components/CalibrationPopup/index.js", "webpack:///./src/ui/src/components/AnnotationPopup/AnnotationPopup.js", "webpack:///./src/ui/src/components/AnnotationPopup/AnnotationPopupContainer.js", "webpack:///./src/ui/src/components/AnnotationPopup/index.js"], "names": ["noop", "annotations", "onRedactionCompleted", "activeDocumentViewerKey", "dispatch", "core", "isWebViewerServerDocument", "webViewerServerApply", "webViewerApply", "applyRedactions", "then", "results", "url", "downloadPdf", "filename", "includeAnnotations", "externalURL", "console", "warn", "warning", "message", "i18next", "t", "title", "confirmBtnText", "onConfirm", "err", "fireError", "Promise", "resolve", "actions", "showWarningMessage", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "PropTypes", "array", "isRequired", "style", "object", "properties", "isRedaction", "bool", "isFreeText", "isEllipse", "hasBackToMenu", "onBackToMenu", "func", "AnnotationStylePopup", "props", "isMeasure", "colorMapKey", "showLineStyleOptions", "hideSnapModeCheckbox", "onResize", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "ANNOTATION_STYLE_POPUP", "isToolDefaultStyleUpdateFromAnnotationPopupEnabled", "getActiveDocumentViewerKey", "isDisabled", "useDispatch", "useTranslation", "useState", "isAutoSizeFont", "setAutoSizeFont", "handleSliderChange", "property", "value", "annotationManager", "getAnnotationManager", "annotation", "adjustFreeTextBoundingBox", "redrawAnnotation", "Core", "Annotations", "WidgetAnnotation", "refresh", "handlePropertyChange", "setAnnotationStyles", "setToolStyles", "ToolName", "handleStyleChange", "handleRichTextStyleChange", "updateAnnotationRichTextStyle", "handleLineStyleChange", "section", "lineStyle", "setStartStyle", "setEndStyle", "dashes", "split", "shift", "trigger", "handleClick", "e", "isMobile", "target", "currentTarget", "closeElement", "ANNOTATION_POPUP", "className", "getClassName", "measureRef", "data-element", "onClick", "ref", "ANNOTATION_STYLE_POPUP_BACK_BUTTON_CONTAINER", "ActionButton", "dataElement", "ANNOTATION_STYLE_POPUP_BACK_BUTTON", "label", "img", "StylePopup", "isFreeTextAutoSize", "onFreeTextSizeToggle", "handleFreeTextAutoSizeToggle", "onStyleChange", "onSliderChange", "onPropertyChange", "disableSeparator", "onRichTextStyleChange", "onLineStyleChange", "DatePicker", "onDatePickerShow", "dateRef", "useRef", "dateContainerRef", "useEffect", "datePicker", "createDatePicker", "field", "current", "container", "format", "getDateFormat", "getDatePicker", "destroy", "Scale", "parseMeasurementContentsByAnnotation", "factor", "Measure", "axis", "getLine<PERSON><PERSON>th", "CalibrationPropType", "shape", "arrayOf", "CalibrationPopup", "getMeasurementUnits", "getCalibrationInfo", "shallowEqual", "measurementUnits", "tempScale", "isFractionalUnit", "defaultUnit", "valueDisplay", "setValueDisplay", "inputRef", "unitTo", "worldScale", "unit", "unitToOptions", "to", "filter", "fractionalUnits", "includes", "isFractionalUnitsToggleDisabled", "valueInputType", "inputValueClass", "classNames", "updateTempScale", "scaleValue", "scaleUnit", "pageUnit", "currentDistance", "currentScale", "newRatio", "pageScale", "defaultPageUnit", "metricUnits", "defaultPageValue", "convertUnit", "updateCalibrationInfo", "setValue", "updateValueDisplay", "newValueDisplay", "getFormattedValue", "tempScaleRef", "onAnnotationChanged", "action", "currentUnit", "addEventListener", "removeEventListener", "deleteAnnotations", "activeElement", "id", "htmlFor", "type", "min", "onChange", "inputValue", "trim", "inFractionalRegex", "test", "result", "parseInFractional", "ftInFractionalRegex", "parseFtInFractional", "ftInDecimalRegex", "parseFtInDecimal", "floatRegex", "parseFloat", "onBlur", "placeholder", "hintValues", "<PERSON><PERSON><PERSON>", "Dropdown", "items", "currentSelectionKey", "onClickItem", "labelledById", "Choice", "isSwitch", "leftLabel", "disabled", "checked", "isIE", "isOpen", "isRightClickMenu", "isNotesPanelOpenOrActive", "isRichTextPopupOpen", "isLinkModalOpen", "isWarningModalOpen", "isContextMenuPopupOpen", "isVisible", "focusedAnnotation", "multipleAnnotationsSelected", "popupRef", "any", "position", "showViewFileButton", "onViewFile", "showCommentButton", "onCommentAnnotation", "isDateFreeTextCanEdit", "isDatePickerOpen", "handleDateChange", "isCalibrationPopupOpen", "showEditStyleButton", "isStylePopupOpen", "openEditStylePopup", "closeEditStylePopup", "annotationStyle", "showContentEditButton", "onEditContent", "openContentEditDeleteWarningModal", "isAppearanceSignature", "onClearAppearanceSignature", "showRedactionButton", "onApplyRedaction", "showGroupButton", "onGroupAnnotations", "showUngroupButton", "onUngroupAnnotations", "showFormFieldButton", "onOpenFormField", "showDeleteButton", "onDeleteAnnotation", "showLinkButton", "hasAssociatedLink", "linkAnnotationToURL", "showFileDownloadButton", "downloadFileAttachment", "showAudioPlayButton", "handlePlaySound", "showCalibrateButton", "onOpenCalibration", "customizableUI", "openStylePanel", "isInReadOnlyMode", "onOpenAlignmentModal", "AnnotationPopup", "shortCutKeysFor3DVisible", "setShortCutKeysFor3DVisible", "commentButtonLabel", "commentButtonImg", "show3DShortCutButton", "Model3DAnnotation", "isRectangle", "RectangleAnnotation", "EllipseAnnotation", "isPolygon", "PolygonAnnotation", "FreeTextAnnotation", "getIntent", "Intent", "FreeText", "FreeTextCallout", "RedactionAnnotation", "mapAnnotationToKey", "getDataWithKey", "mapToolNameToKey", "hasLineEndings", "isInstanceActive", "getRootNode", "StrokeStyle", "isContentEdit", "isContentEditPlaceholder", "isReadOnlySignature", "SignatureWidgetAnnotation", "fieldFlags", "get", "WidgetFlags", "READ_ONLY", "showClearSignatureButton", "error", "StartLineStyle", "getStartStyle", "EndLineStyle", "getEndStyle", "richTextStyles", "getRichTextStyle", "calculatedFontSize", "getCalculatedFontSize", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "strikeout", "OverlayText", "annotationPopup", "Popup", "open", "closed", "stylePopupOpen", "visibility", "undefined", "FocusTrap", "locked", "CustomizablePopup", "childrenClassName", "isNotClickableSelector", "CALIBRATION_POPUP_BUTTON", "renderPopup", "cancel", "ToolNames", "Tools", "selectedMultipleAnnotations", "canModify", "focusedAnnotationStyle", "setIsStylePopupOpen", "setDatePickerOpen", "isDatePickerMount", "setDatePickerMount", "includesFormFieldAnnotation", "stylePopupRepositionFlag", "setStylePopupRepositionFlag", "closePopup", "AnnotationPopupContainer", "isElementOpen", "CONTEXT_MENU_POPUP", "isRightClickAnnotationPopupEnabled", "NOTES_PANEL", "INLINE_COMMENT_POPUP", "LINK_MODAL", "WARNING_MODAL", "getIsMultiTab", "getTabManager", "getTabs", "getNotesInLeftPanel", "LEFT_PANEL", "getActiveLeftPanel", "isAnyCustomPanelOpen", "getFeatureFlags", "STYLE_PANEL", "isNotesPanelDisabled", "isAnnotationStylePopupDisabled", "isInlineCommentingDisabled", "isNotesPanelOpen", "isMultiTab", "tabManager", "tabs", "notesInLeftPanel", "leftPanelOpen", "activeLeftPanel", "featureFlags", "isStylePanelOpen", "isStylePanelDisabled", "left", "top", "setPosition", "setIsVisible", "setCalibrationPopupOpen", "isFocusedAnnotationSelected", "isAnnotationSelected", "annotManager", "useOnClickOutside", "useCallback", "notesPanel", "querySelector", "clickedInNotesPanel", "contains", "clickedInLinkModal", "warningModal", "getOpenedWarningModal", "colorPicker", "getOpenedColorPicker", "handleResize", "debounce", "getAnnotationPopupPositionBasedOn", "handleVisibility", "sixtyFramesPerSecondIncrement", "useLayoutEffect", "CALIBRATION_MEASUREMENT", "acceptFormats", "getHashParameters", "SupportedFileFormats", "SERVER", "CLIENT", "FileAttachmentAnnotation", "mimeTypeToExtension", "getFileMetadata", "mimeType", "metaData", "fileAttachmentTab", "find", "tab", "setActiveTab", "getFileData", "extension", "saveCurrentActiveTabState", "setActive", "addTab", "selectedAnnotations", "getSelectedAnnotations", "numberOfSelectedAnnotations", "canModifyContents", "numberOfGroups", "getNumberOfGroups", "canGroup", "canUngroup", "isSignedByAppearance", "getIsReadOnly", "CROP", "hasStyle", "Object", "keys", "toolsWithNoStyling", "VIDEO_REDACTION", "VIDEO_AND_AUDIO_REDACTION", "AUDIO_REDACTION", "PushButtonWidgetAnnotation", "isFullPDFEnabled", "getContentEditType", "ContentEdit", "Types", "TEXT", "isAnnotationRedactable", "primaryAnnotation", "selectedAnnotation", "InReplyTo", "isInFormFieldCreationMode", "getFormFieldCreationManager", "SIGNATURE", "REDACTION", "REDACTION2", "REDACTION3", "REDACTION4", "STICKY", "STICKY2", "STICKY3", "STICKY4", "SoundAnnotation", "LineAnnotation", "annot", "getFullFileMetadata", "fileData", "fileName", "saveAs", "hasAudioData", "contentEditManager", "getContentEditManager", "isInContentEditMode", "endContentEditMode", "triggerNoteEditing", "openElement", "text", "setNoteContents", "updateAnnotation", "isDatePickerShowed", "clearSignature", "groupAnnotations", "ungroupAnnotations", "disableElement", "PRIORITY_THREE", "FORM_FIELD_EDIT_POPUP", "FORM_FIELD_PANEL", "getGroupedLinkAnnotations", "linkAnnot", "index", "TextHighlightAnnotation", "Opacity", "deleteAnnotation", "setActiveSoundAnnotation", "triggerResetAudioPlaybackPosition", "CALIBRATION_MODAL", "ANNOTATION_ALIGNMENT_POPUP"], "mappings": "kJAOA,SAASA,KAEM,aAACC,GAAW,IAAEC,EAAuB,UAAH,6CAAGF,EAAMG,EAA0B,UAAH,6CAAG,EAAC,OAAK,SAACC,GACzF,OAAIC,IAAKC,4BAEAC,EAAqBN,EAAaG,EAAUD,GAE9CK,EAAeP,EAAaC,EAAsBE,EAAUD,KAGrE,IAAMI,EAAuB,SAACN,EAAaG,EAAUD,GAAuB,OAAKE,IAAKI,gBAAgBR,EAAaE,GAAyBO,MAAK,SAACC,GAChJ,GAAIA,GAAWA,EAAQC,IACrB,OAAOC,YAAYT,EAAU,CAC3BU,SAAU,eACVC,oBAAoB,EACpBC,YAAaL,EAAQC,MAGzBK,QAAQC,KAAK,sDAGTV,EAAiB,SAACP,EAAaC,EAAsBE,EAAUD,GACnE,IAIMgB,EAAU,CACdC,QALcC,IAAQC,EAAE,kCAMxBC,MALYF,IAAQC,EAAE,+BAMtBE,eALqBH,IAAQC,EAAE,gBAM/BG,UAAW,WAMT,OALApB,IAAKI,gBAAgBR,EAAaE,GAC/BO,MAAK,WACJR,OACA,OACK,SAACwB,GAAG,OAAKC,YAAUD,MACrBE,QAAQC,YAInB,OAAOzB,EAAS0B,IAAQC,mBAAmBZ,M,qBC/C7C,IAAIa,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ipEAAkpE,KAG3qE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,g/DCSvB,IAAMC,EAAY,CAChBhE,YAAaiE,IAAUC,MAAMC,WAC7BC,MAAOH,IAAUI,OAAOF,WACxBG,WAAYL,IAAUI,OAAOF,WAC7BI,YAAaN,IAAUO,KACvBC,WAAYR,IAAUO,KACtBE,UAAWT,IAAUO,KACrBG,cAAeV,IAAUO,KACzBI,aAAcX,IAAUY,MAGpBC,EAAuB,SAACC,GAC5B,IACE/E,EAaE+E,EAbF/E,YACAoE,EAYEW,EAZFX,MACAG,EAWEQ,EAXFR,YACAE,EAUEM,EAVFN,WACAC,EASEK,EATFL,UACAM,EAQED,EARFC,UACAC,EAOEF,EAPFE,YACAC,EAMEH,EANFG,qBACAZ,EAKES,EALFT,WACAa,EAIEJ,EAJFI,qBACAC,EAGEL,EAHFK,SACAT,EAEEI,EAFFJ,cACAC,EACEG,EADFH,aAWA,IAJES,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,kBAAkBF,EAAOG,IAAaC,wBAChDH,IAAUI,mDAAmDL,GAC7DC,IAAUK,2BAA2BN,OACrC,GAPAO,EAAU,KACVF,EAAkD,KAClDzF,EAAuB,KAOnBC,EAAW2F,cACVzE,EAAqB,EAAhB0E,cAAgB,GAApB,GACqE,IAAnCC,mBAAS1B,EAAW2B,gBAAe,GAAtEA,EAAc,KAAEC,EAAe,KAEhCC,EAAqB,SAACC,EAAUC,GACpC,IAAMC,EAAoBlG,IAAKmG,qBAAqBrG,GACpDF,EAAYoD,SAAQ,SAACoD,GACnBA,EAAWJ,GAAYC,EACN,oBAAbD,GACFK,YAA0BD,GAE5BF,EAAkBI,iBAAiBF,GAE/BA,aAAsBjE,OAAOoE,KAAKC,YAAYC,kBAChDL,EAAWM,cAKXC,EAAuB,SAACX,EAAUC,GACtCrG,EAAYoD,SAAQ,SAACoD,GACnBpG,IAAK4G,oBAAoBR,EAAY,EAAF,GAChCJ,EAAWC,GACXnG,GACCyF,GACFsB,YAAcT,EAAWU,SAAUd,EAAUC,GAE9B,aAAbD,GAAwC,SAAbA,GAC7BK,YAA0BD,OAK1BW,EAAoB,SAACf,EAAUC,GACnCrG,EAAYoD,SAAQ,SAACoD,GACnBpG,IAAK4G,oBAAoBR,EAAY,EAAF,GAChCJ,EAAWC,GACXnG,GAECyF,GACFsB,YAAcT,EAAWU,SAAUd,EAAUC,OAK7Ce,EAA4B,SAAChB,EAAUC,GAC3CrG,EAAYoD,SAAQ,SAACoD,GACnBpG,IAAKiH,8BAA8Bb,EAAY,EAAF,GAAKJ,EAAWC,GAASnG,OAIpEoH,EAAwB,SAACC,EAASlB,GACtCrG,EAAYoD,SAAQ,SAACoD,GACnB,IAAIgB,EAAY,GAChB,GAAgB,UAAZD,EACFf,EAAWiB,cAAcpB,GACzBmB,EAAY,sBACP,GAAgB,QAAZD,EACTf,EAAWkB,YAAYrB,GACvBmB,EAAY,oBACP,GAAgB,WAAZD,EAAsB,CAC/B,IAAMI,EAAStB,EAAMuB,MAAM,KACrBxD,EAAQuD,EAAOE,QACrBrB,EAAkB,MAAIpC,EACtBoC,EAAmB,OAAImB,EACvBH,EAAY,cAGV7B,GACFsB,YAAcT,EAAWU,SAAUM,EAAWnB,GAGhDjG,IAAKmG,qBAAqBrG,GAAyBwG,iBAAiBF,MAGtEpG,IAAKmG,qBAAqBrG,GAAyB4H,QAAQ,oBAAqB,CAAC9H,EAAa,SAAU,MAGpG+H,EAAc,SAACC,GACfC,eAAcD,EAAEE,SAAWF,EAAEG,eAC/BhI,EAAS0B,IAAQuG,aAAa3C,IAAa4C,oBAQzCC,EAAYC,YAAa,6BAA8BxD,GAE7D,OAAOc,EAAa,KAClB,kBAAC,IAAO,CACNT,SARiB,WACnBA,GAAYA,OAST,gBAAGoD,EAAU,EAAVA,WAAU,OACZ,yBACEF,UAAWA,EACXG,eAAchD,IAAaC,uBAC3BgD,QAASX,EACTY,IAAKH,GAEJ7D,GACC,yBACE2D,UAAU,yBACVG,eAAchD,IAAamD,8CAE3B,kBAACC,EAAA,EAAY,CACXP,UAAU,sBACVQ,YAAarD,IAAasD,mCAC1BC,MAAO3H,EAAE,qBACT4H,IAAI,oBACJP,QAAS9D,KAKf,kBAACsE,EAAA,EAAU,CACT/D,qBAAsBA,EACtBF,YAAaA,EACbb,MAAOA,EACPK,WAAYA,EACZ0E,mBAAoBlD,EACpBmD,qBAAsB,kBAAMC,YAA6BrJ,EAAY,GAAIkG,EAAiBD,IAC1FvB,UAAWA,EACXM,UAAWA,EACXsE,cAAenC,EACfoC,eAAgBpD,EAChBqD,iBAAkBzC,EAClB0C,kBAAgB,EAChBnF,WAAYA,EACZoF,sBAAuBtC,EACvB7C,YAAaA,EACbW,qBAAsBA,EACtByE,kBAAmBrC,SAQ/BxC,EAAqBd,UAAYA,EAElBc,QCtMAA,O,qBCFf,IAAI/C,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,qBClEnC,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,2sEAA4sE,KAGruE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,sBCVvBD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,u2GAAw2G,KAGj4G0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,83BCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+XAEA,IAAMwH,EAAa,SAAH,GAAkD,IAA5ClB,EAAO,EAAPA,QAASlC,EAAU,EAAVA,WAAYqD,EAAgB,EAAhBA,iBACnCC,EAAUC,iBAAO,MACjBC,EAAmBD,iBAAO,MAoBhC,OAnBAE,qBAAU,WACR,IAAIC,EAYJ,OAXmB,eAPvB,EAOuB,GAPvB,EAOuB,UAAG,8FACD3H,OAAOoE,KAAKwD,iBAAiB,CAC9CC,MAAON,EAAQO,QACf3B,UACA4B,UAAWN,EAAiBK,QAC5BE,OAAQ/D,EAAWgE,kBACnB,OALFN,EAAa,EAAH,KAMVL,GAAiB,GAAM,0CAd7B,+KAeK,kBARkB,kCASnBY,GAEO,WACLP,EAAWQ,UACXR,EAAa,KACbL,GAAiB,MAElB,IAED,yBAAKpB,eAAa,uBAChB,yBAAKE,IAAKmB,IACV,yBAAKnB,IAAKqB,MAKhBJ,EAAW5F,UAAY,CACrB0E,QAASzE,IAAUY,KAAKV,WACxBqC,WAAYvC,IAAUI,OAAOF,WAC7B0F,iBAAkB5F,IAAUY,KAAKV,YAGpByF,ICrCAA,EDqCAA,E,qmCEZf,IAAMe,EAAQpI,OAAOoE,KAAKgE,MAEpBC,EAAuC,SAACpE,GAC5C,IAAMqE,EAASrE,EAAWsE,QAAQC,KAAK,GAAGF,OAG1C,OAFarE,EAAWmE,MAAM,GAAG,IAG/B,IAAK,QACH,OAAQnE,EAAWwE,gBAAkBH,EAAU,GACjD,IAAK,KACL,QACE,OAAOrE,EAAWwE,gBAAkBH,IAcpCI,EAAsB,CAC1BzE,WAAYvC,IAAUiH,MAAM,CAC1BP,MAAO1G,IAAUkH,QAAQlH,IAAUC,UAIjCkH,EAAmB,SAAH,GAAuB,QAAjB5E,EAAU,EAAVA,WACnBnF,EAAqB,EAAhB0E,cAAgB,GAApB,GACF5F,EAAW2F,cAQD,IAHZT,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU8F,oBAAoB/F,GAC9BC,IAAU+F,mBAAmBhG,MAC5BiG,KAAa,GALdC,EAAgB,YACdC,EAAS,EAATA,UAAWC,EAAgB,EAAhBA,iBAAkBC,EAAW,EAAXA,YAKmB,IAAZ3F,mBAAS,IAAG,GAA7C4F,EAAY,KAAEC,EAAe,KAC9BC,EAAW/B,iBAAO,MAElBgC,GAAwC,QAA/B,MAAIpB,EAAMc,GAAWO,kBAAU,aAA/B,EAAiCC,OAAQ,KAClDC,EAAgBR,EAAmBF,EAAiBW,GAAGC,QAAO,SAACH,GAAI,OAAKI,IAAgBC,SAASL,MAAST,EAAiBW,GAC3HI,GAAmCF,IAAgBC,SAASP,GAC5DS,EAAkBd,GAA+B,UAAXK,EAAsB,OAAS,SACrEU,EAAkBC,IAAW,cAAe,CAChD,kBAAmBjB,IAA4C,QAA/B,MAAId,EAAMc,GAAWO,kBAAU,aAA/B,EAAiC3F,OAAQ,KAGrEsG,EAAkB,SAACC,EAAYC,GACnC,IAvCwBC,EAuClBC,EAAkBnC,EAAqCpE,GACvDwG,EAAexG,EAAWmE,MAC1BsC,EAAWF,EAAkBC,EAAa,GAAG,GAC7CE,EAAY,CAACF,EAAa,GAAG,GAAKC,EAAUD,EAAa,GAAG,IAC5DG,EA1CS,QADSL,EA2CmBD,GAzCpC,KAELO,IAAYd,SAASQ,GAChB,KAEF,KAqCCO,EAAmBC,YAAYJ,EAAU,GAAIA,EAAU,GAAIC,GACjEhN,EAAS0B,IAAQ0L,sBAAsB,CAAE9B,UAAW,GAAF,OAAK4B,EAAgB,YAAIF,EAAe,cAAMP,EAAU,YAAIC,GAAanB,uBAGvH8B,EAAW,SAACZ,GAAe,MAC/BD,EAAgBC,EAA2C,QAAjC,EAAE,IAAIjC,EAAMc,GAAWO,kBAAU,aAA/B,EAAiCC,OAoDzDwB,EAAqB,WAAM,MAE3BC,EADEd,EAA4C,QAAlC,EAAG,IAAIjC,EAAMc,GAAWO,kBAAU,aAA/B,EAAiC3F,MAKlDqH,EAHGhC,GAA+B,UAAXK,EAGLpB,EAAMgD,kBAAkBf,EAAYb,EAAQL,EAAmB,EAAI,GAAK,MAAQ,GAAO,GAFvF,GAAH,OAAMkB,GAIvBf,EAAgB6B,GAAmB,KAG/BE,EAAe7D,iBAAO0B,GAyC5B,OAxCAxB,qBAAU,WACR2D,EAAavD,QAAUoB,IACtB,CAACA,IACJxB,qBAAU,WACR,GAAIzD,EAAY,CACd,IAAMH,EAAQuE,EAAqCpE,GAC7CyF,EAAOzF,EAAWmE,MAAM,GAAG,GAC7BgB,EACFgB,EAAgBW,YAAYjH,EAAO4F,EAAMN,GAAcA,GAEvDgB,EAAgBtG,EAAO4F,GAI3B,IAAM4B,EAAsB,SAAC7N,EAAa8N,GACxC,GAAe,WAAXA,GAA8C,IAAvB9N,EAAY8C,QAAgB9C,EAAY,KAAOwG,EAAY,OAC9EH,EAAQuE,EAAqCpE,GAC7CyF,EAAOzF,EAAWmE,MAAM,GAAG,GAC3BoD,EAAwD,QAA7C,EAAG,IAAIpD,EAAMiD,EAAavD,SAAS2B,kBAAU,aAA1C,EAA4CC,KAC5D8B,EACFpB,EAAgBW,YAAYjH,EAAO4F,EAAM8B,GAAcA,GAEvDpB,EAAgBtG,EAAO4F,KAM7B,OAFA7L,IAAK4N,iBAAiB,oBAAqBH,GAEpC,WACLzN,IAAK6N,oBAAoB,oBAAqBJ,GAC9CzN,IAAK8N,kBAAkB,CAAC1H,OAEzB,CAACA,IAEJyD,qBAAU,YACJ6B,aAAQ,EAARA,EAAUzB,WAAY5H,SAAS0L,eACjCV,MAED,CAAChC,EAAWC,IAGb,yBAAKpD,UAAU,mBAAmBG,eAAa,oBAC7C,2BAAOH,UAAU,0BAA0B8F,GAAG,0BAA0BC,QAAQ,2BAA2BhN,EAAE,wCAC7G,yBAAKiH,UAAU,mBACb,2BACE8F,GAAG,0BACH9F,UAAWmE,EACX9D,IAAKmD,EACLwC,KAAM9B,EACNnG,MAAOuF,EACP2C,IAAI,IACJC,SAxGmB,SAACxG,GAC1B6D,EAAgB7D,EAAEE,OAAO7B,OACzB,IAAMoI,EAAazG,EAAEE,OAAO7B,MAAMqI,OAClC,GAAKhD,GAaH,GAAe,OAAXK,GACF,GAAI4C,IAAkBC,KAAKH,GAAa,CACtC,IAAMI,EAASC,YAAkBL,GACjC,GAAII,EAAS,EAEX,YADArB,EAASqB,SAIR,GAAe,UAAX9C,GACLgD,IAAoBH,KAAKH,GAAa,CACxC,IAAMI,EAASG,YAAoBP,GACnC,GAAII,EAAS,EAEX,YADArB,EAASqB,SAxBf,GAAe,UAAX9C,GAAsBkD,IAAiBL,KAAKH,GAAa,CAC3D,IAAMI,EAASK,YAAiBT,GAChC,GAAII,EAAS,EAEX,YADArB,EAASqB,QAGN,GAAIM,IAAWP,KAAKH,GAAa,CACtC,IAAMI,EAASO,WAAWX,IAAe,EAEzC,YADAjB,EAASqB,GAsBbrB,EAAS,IAuEH6B,OApEiB,WACvB5B,KAoEM6B,YAAa5D,EAAmB6D,IAAWxD,GAAsB,UAAXA,EAAqBwD,IAAW,iBAAmB,KAE3G,kBAACC,EAAA,EAAO,CAACxN,QAAS,8CAChB,yBAAKsG,UAAU,eACb,kBAACmH,EAAA,EAAQ,CACPrB,GAAG,0BACHtF,YAAY,mBACZ4G,MAAOxD,EACPyD,oBAAqB5D,EACrB6D,YA3HM,SAAC/C,GAAc,MAC/BF,EAA+C,QAAhC,EAAC,IAAIhC,EAAMc,GAAWO,kBAAU,aAA/B,EAAiC3F,MAAOwG,IA2H9CgD,aAAa,+BAKrB,kBAACL,EAAA,EAAO,CAACxN,QAASX,EAAE,uDAClB,6BACE,kBAACyO,EAAA,EAAM,CACLC,UAAQ,EACRC,WAAS,EACThH,MAAO3H,EAAE,iDACT4O,SAAU1D,EACV2D,QAASxE,EACT0C,GAAG,qCACH9F,UAAU,aACVkG,SAvIoB,WAC5BrO,EAAS0B,IAAQ0L,sBAAsB,CAAE9B,YAAWC,kBAAmBA,YA8I3EN,EAAiBpH,UAAYiH,EAEdG,ICjPAA,EDiPAA,E,sxEE/Nf,IAAMpH,GAAY,CAChBiE,SAAUhE,IAAUO,KACpB2L,KAAMlM,IAAUO,KAChB4L,OAAQnM,IAAUO,KAClB6L,iBAAkBpM,IAAUO,KAC5B8L,yBAA0BrM,IAAUO,KACpC+L,oBAAqBtM,IAAUO,KAC/BgM,gBAAiBvM,IAAUO,KAC3BiM,mBAAoBxM,IAAUO,KAC9BkM,uBAAwBzM,IAAUO,KAClCmM,UAAW1M,IAAUO,KAErBoM,kBAAmB3M,IAAUI,OAC7BwM,4BAA6B5M,IAAUO,KACvCsM,SAAU7M,IAAU8M,IACpBC,SAAU/M,IAAUI,OAEpB4M,mBAAoBhN,IAAUO,KAC9B0M,WAAYjN,IAAUY,KAEtBsM,kBAAmBlN,IAAUO,KAC7B4M,oBAAqBnN,IAAUY,KAC/BwM,sBAAuBpN,IAAUO,KACjC8M,iBAAkBrN,IAAUO,KAC5B+M,iBAAkBtN,IAAUY,KAC5BgF,iBAAkB5F,IAAUY,KAC5B2M,uBAAwBvN,IAAUO,KAElCiN,oBAAqBxN,IAAUO,KAC/BkN,iBAAkBzN,IAAUO,KAC5BW,qBAAsBlB,IAAUO,KAChCmN,mBAAoB1N,IAAUY,KAC9B+M,oBAAqB3N,IAAUY,KAC/BgN,gBAAiB5N,IAAUI,OAC3Be,SAAUnB,IAAUY,KAEpBiN,sBAAuB7N,IAAUO,KACjCuN,cAAe9N,IAAUY,KACzBmN,kCAAmC/N,IAAUY,KAE7CoN,sBAAuBhO,IAAUO,KACjC0N,2BAA4BjO,IAAUY,KAEtCsN,oBAAqBlO,IAAUO,KAC/B4N,iBAAkBnO,IAAUY,KAE5BwN,gBAAiBpO,IAAUO,KAC3B8N,mBAAoBrO,IAAUY,KAC9B0N,kBAAmBtO,IAAUO,KAC7BgO,qBAAsBvO,IAAUY,KAEhC4N,oBAAqBxO,IAAUO,KAC/BkO,gBAAiBzO,IAAUY,KAE3B8N,iBAAkB1O,IAAUO,KAC5BoO,mBAAoB3O,IAAUY,KAE9BgO,eAAgB5O,IAAUO,KAC1BsO,kBAAmB7O,IAAUO,KAC7BuO,oBAAqB9O,IAAUY,KAE/BmO,uBAAwB/O,IAAUO,KAClCyO,uBAAwBhP,IAAUY,KAElCqO,oBAAqBjP,IAAUO,KAC/B2O,gBAAiBlP,IAAUY,KAE3BuO,oBAAqBnP,IAAUO,KAC/B6O,kBAAmBpP,IAAUY,KAE7ByO,eAAgBrP,IAAUO,KAC1B+O,eAAgBtP,IAAUY,KAC1B2O,iBAAkBvP,IAAUO,KAC5BiP,qBAAsBxP,IAAUY,MAG5B6O,GAAkB,SAAH,GA0Ef,QAzEJzL,EAAQ,EAARA,SACAkI,EAAI,EAAJA,KACAC,EAAM,EAANA,OACAC,EAAgB,EAAhBA,iBACAC,EAAwB,EAAxBA,yBACAC,EAAmB,EAAnBA,oBACAC,EAAe,EAAfA,gBACAC,EAAkB,EAAlBA,mBACAC,EAAsB,EAAtBA,uBACAC,EAAS,EAATA,UAEAC,EAAiB,EAAjBA,kBACAE,EAAQ,EAARA,SACAE,EAAQ,EAARA,SACAH,EAA2B,EAA3BA,4BAEAI,EAAkB,EAAlBA,mBACAC,EAAU,EAAVA,WAEAC,EAAiB,EAAjBA,kBACAC,EAAmB,EAAnBA,oBACAC,EAAqB,EAArBA,sBACAC,EAAgB,EAAhBA,iBACAC,EAAgB,EAAhBA,iBACA1H,EAAgB,EAAhBA,iBACA2H,EAAsB,EAAtBA,uBAEAC,EAAmB,EAAnBA,oBACAC,EAAgB,EAAhBA,iBACAvM,EAAoB,EAApBA,qBACAwM,EAAkB,EAAlBA,mBACAC,EAAmB,EAAnBA,oBACAC,EAAe,EAAfA,gBACAzM,EAAQ,EAARA,SAEA0M,EAAqB,EAArBA,sBACAC,EAAa,EAAbA,cACAC,EAAiC,EAAjCA,kCAEAC,EAAqB,EAArBA,sBACAC,EAA0B,EAA1BA,2BAEAC,EAAmB,EAAnBA,oBACAC,GAAgB,EAAhBA,iBAEAC,GAAe,EAAfA,gBACAC,GAAkB,EAAlBA,mBACAC,GAAiB,EAAjBA,kBACAC,GAAoB,EAApBA,qBAEAC,GAAmB,EAAnBA,oBACAC,GAAe,EAAfA,gBAEAC,GAAgB,EAAhBA,iBACAC,GAAkB,EAAlBA,mBAEAC,GAAc,EAAdA,eACAC,GAAiB,EAAjBA,kBACAC,GAAmB,EAAnBA,oBAEAC,GAAsB,EAAtBA,uBACAC,GAAsB,EAAtBA,uBAEAC,GAAmB,EAAnBA,oBACAC,GAAe,EAAfA,gBAEAC,GAAmB,EAAnBA,oBACAC,GAAiB,EAAjBA,kBAEAC,GAAc,EAAdA,eACAC,GAAc,EAAdA,eACAC,GAAgB,EAAhBA,iBACAC,GAAoB,EAApBA,qBAEOpS,GAAqB,GAAhB0E,cAAgB,GAApB,GACuE,MAAfC,oBAAS,GAAM,GAAxE2N,GAAwB,MAAEC,GAA2B,MAEtDC,GAAqBxC,EAAwB,oBAAsB,iBACnEyC,GAAmBzC,EAAwB,mCAAqC,wBAChF0C,IAAwBP,IAAoB5C,aAA6BrO,OAAOoE,KAAKC,YAAYoN,oBAAsB/L,EACvHgM,GAAcrD,aAA6BrO,OAAOoE,KAAKC,YAAYsN,oBACnExP,GAAYkM,aAA6BrO,OAAOoE,KAAKC,YAAYuN,kBACjEC,GAAYxD,aAA6BrO,OAAOoE,KAAKC,YAAYyN,kBACjE5P,GACJmM,aAA6BrO,OAAOoE,KAAKC,YAAY0N,qBACpD1D,EAAkB2D,cAAgBhS,OAAOoE,KAAKC,YAAY0N,mBAAmBE,OAAOC,UACnF7D,EAAkB2D,cAAgBhS,OAAOoE,KAAKC,YAAY0N,mBAAmBE,OAAOE,iBAClFnQ,GAAcqM,aAA6BrO,OAAOoE,KAAKC,YAAY+N,oBACnE1P,GAAc2P,YAAmBhE,GACjC5L,KAAc4L,EAAkB9F,QAChC5F,GAAuB2P,YAAeC,YAAiBlE,EAAkB1J,WAAW6N,eACpFC,IAAoBzS,OAAOC,gCAAuD,QAAtB,EAAAC,SAAS0L,qBAAa,aAAtB,EAAwB5K,cAAe0R,cACrGC,GAAc,QACZC,GAA0D,QAA7C,EAAGvE,EAAkBwE,gCAAwB,aAA1C,OAAAxE,GAChByE,GAAsBzE,aAA6BrO,OAAOoE,KAAKC,YAAY0O,2BAA6B1E,EAAkB2E,WAAWC,IAAIjT,OAAOoE,KAAKC,YAAY6O,YAAYC,WAC7KC,GAA2B1D,IAA0BQ,GAC3D,IACEyC,GAA8C,SAA/BtE,EAAyB,MAAY,UAC7CA,EAAyB,MAAC,YAAIA,EAA0B,QAC3DA,EAAyB,MAC7B,MAAOnP,GACPT,QAAQ4U,MAAMnU,GAEhB,IAAI6C,GAAa,GAejB,GAdIY,KACFZ,GAAa,CACXuR,eAAgBjF,EAAkBkF,gBAClCC,aAAcnF,EAAkBoF,cAChCd,kBAIAjB,IAAevP,IAAa0P,MAC9B9P,GAAa,CACX4Q,iBAIAzQ,GAAY,sCACRwR,GAAiBrF,EAAkBsF,mBACnCjQ,GAAiB2K,EAAkB3K,iBACnCkQ,GAAqBvF,EAAkBwF,wBAE7C9R,GAAa,CACX+R,KAAMzF,EAAkByF,KACxBC,SAAU1F,EAAkB0F,SAC5BC,UAAW3F,EAAkB2F,UAC7BC,kBAAmB5F,EAAkB4F,kBACrCC,KAAqD,QAAjD,GAA2C,UAAzCR,UAAmB,QAAL,GAAdA,GAAiB,UAAE,YAAL,EAAd,GAAsB,uBAAyB,WACrDS,OAAwD,QAAlD,GAA0C,YAAxCT,UAAmB,QAAL,GAAdA,GAAiB,UAAE,YAAL,EAAd,GAAsB,sBAA0B,WACxDU,WAAWV,UAAmB,QAAL,GAAdA,GAAiB,UAAE,QAAqB,QAArB,GAAnB,GAAsB,0BAAkB,YAA1B,EAAd,GAA0C3J,SAAS,gBAAgB2J,UAAmB,QAAL,GAAdA,GAAiB,UAAE,QAAqB,QAArB,GAAnB,GAAsB,0BAAkB,YAA1B,EAAd,GAA0C3J,SAAS,SACjIsK,UAA6E,QAApE,GAAEX,UAAmB,QAAL,GAAdA,GAAiB,UAAE,QAAqB,QAArB,GAAnB,GAAsB,0BAAkB,YAA1B,EAAd,GAA0C3J,SAAS,uBAAe,WAC7E4I,eACAjP,kBACAkQ,uBAIA5R,KACFD,GAAa,CACXuS,YAAajG,EAA+B,YAC5CyF,KAAMzF,EAAwB,KAC9B0F,SAAU1F,EAA4B,SACtC2F,UAAW3F,EAA6B,YAI5C,IAqNMkG,GACJ,yBACExO,UAAWoE,IAAW,CACpBqK,OAAO,EACPrD,iBAAiB,EACjBsD,KAAM5G,EACN6G,QAAS7G,EACT8G,eAAgBxF,EAChB,cAAerB,EACf,iBAAkBA,IAEpB1H,IAAKmI,EACLrI,eAAchD,IAAa4C,iBAC3BjE,MAAK,OAAO4M,GAAQ,IAAEmG,WAAYxG,QAA2ByG,IAAdzG,EAA0B,UAAY,YAlOrE,WAClB,QAAQ,GACN,KAAKe,EACH,OACE,kBAAC5M,EAAA,EAAoB,CACnB9E,YAAa,CAAC4Q,GACdxM,MAAOyN,EACPzB,OAAQA,EACRhL,SAAUA,EACVX,WAAYA,GACZC,UAAWA,GACXH,YAAaA,GACbS,UAAWA,GACXC,YAAaA,GACbC,qBAAsBA,GACtBZ,WAAYA,GACZa,qBAAsBA,EACtBR,cAAe0L,EACfzL,aAAcgN,IAGpB,KAAKN,EACH,OACE,kBAAC,EAAU,CAAC5I,QAAS6I,EAAkB/K,WAAYoK,EAAmB/G,iBAAkBA,IAE5F,KAAK2H,EACH,OAAO,kBAAC,EAAgB,CAAChL,WAAYoK,IACvC,KAAK+C,IAA4B/C,aAA6BrO,OAAOoE,KAAKC,YAAYoN,kBACpF,OACE,yBAAK1L,UAAU,eACb,yBAAKA,UAAU,cAAcI,QAAS,kBAAMkL,IAA4B,KAAQ,KAChF,yBAAKtL,UAAU,OAAOjH,GAAE,mBAAmB,IAAC,8BAAOA,GAAE,uBACrD,yBAAKiH,UAAU,OAAOjH,GAAE,eAAe,IAAC,8BAAOA,GAAE,sBAGvD,QACE,OACE,kBAACgW,EAAA,EAAS,CACRC,OAAQlH,GAAU4E,KAAqBzE,IAAwBD,IAA6BE,IAAoBC,IAAuBhM,KAAeiM,GAEtJ,yBAAKpI,UAAU,aACb,kBAACiP,EAAA,EAAiB,CAChBzO,YAAarD,IAAa4C,iBAC1BmP,kBAAkB,oBAEjBvG,GACC,kBAACpI,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,iBACZE,MAAOqH,EAAmB,kBAAoB,GAC9C/O,MAAQ+O,EAAuC,GAApB,kBAC3BpH,IAAI,YACJP,QAASwI,IAGZC,GACC,kBAACtI,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,0BACZE,MAAOqH,EAAmBwD,GAAqB,GAC/CvS,MAAQ+O,EAAwC,GAArBwD,GAC3B5K,IAAK6K,GACLpL,QAAS0I,IAGZK,GACC,kBAAC5I,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,4BACZE,MAAOqH,EAAmB,eAAiB,GAC3C/O,MAAQ+O,EAAoC,GAAjB,eAC3BpH,IAAI,uBACJP,QAAS4K,GAAiBC,GAAiB5B,IAG9CG,GACC,kBAACjJ,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,8BACZE,MAAOqH,EAAmB,cAAgB,GAC1C/O,MAAQ+O,EAAmC,GAAhB,cAC3BpH,IAAI,oBACJP,QAASqJ,IAGZ4D,IACC,kBAAC9M,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,iCACZE,OAAQqM,IAAuBhF,EAAmB,wBAA0B,GAC5E/O,MAAO+T,GAAsB,2BAA+BhF,EAA6C,GAA1B,wBAC/EpH,IAAK,mBACLP,QAASwJ,EACTuF,uBAAwB,kBAAMpC,MAGjClD,GACC,kBAACtJ,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,yBACZE,MAAOqH,EAAmB,eAAiB,GAC3C/O,MAAQ+O,EAAoC,GAAjB,eAC3BpH,IAAI,sBACJP,QAAS0J,KAGZC,IACC,kBAACxJ,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,wBACZE,MAAOqH,EAAmB,eAAiB,GAC3C/O,MAAQ+O,EAAoC,GAAjB,eAC3BpH,IAAI,yBACJP,QAAS4J,KAGZC,IACC,kBAAC1J,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,0BACZE,MAAOqH,EAAmB,iBAAmB,GAC7C/O,MAAQ+O,EAAsC,GAAnB,iBAC3BpH,IAAI,2BACJP,QAAS8J,KAGZ3B,IAAgC5I,GAC/B,kBAACY,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,sBACZE,MAAOqH,EAAmB,YAAc,GACxC/O,MAAQ+O,EAA6B,GAAV,QAC3BpH,IAAI,oBACJP,QAAS+K,KAGZhB,IACC,kBAAC5J,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,sBACZE,MAAOqH,EAAmB,uBAAyB,GACnD/O,MAAQ+O,EAA4C,GAAzB,uBAC3BpH,IAAI,uBACJP,QAASgK,KAGZC,IACC,kBAAC9J,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,yBACZE,MAAOqH,EAAmB,gBAAkB,GAC5C/O,MAAQ+O,EAAqC,GAAlB,gBAC3BpH,IAAI,mBACJP,QAASyM,GAAgBnD,EAAoCY,KAGhEQ,IACC,kBAACvK,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAarD,IAAaiS,yBAC1B1O,MAAOqH,EAAmB,mBAAqB,GAC/C/O,MAAQ+O,EAAwC,GAArB,mBAC3BpH,IAAI,YACJP,QAAS2K,KAGZR,IACC,kBAAChK,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,aACZE,MAAOqH,EAAmB,YAAc,GACxC/O,MAAQ+O,EAAiC,GAAd,YAC3BpH,IAAK6J,GAAoB,mBAAqB,iBAC9CpK,QAASqK,KAGZC,IACC,kBAACnK,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,yBACZE,MAAOqH,EAAmB,gCAAkC,GAC5D/O,MAAQ+O,EAAqD,GAAlC,gCAC3BpH,IAAI,gBACJP,QAAS,kBAAMuK,GAAuBrC,MAGzCmD,IACC,kBAAClL,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,oBACZE,MAAOqH,EAAmB,+BAAiC,GAC3D/O,MAAQ+O,EAAoD,GAAjC,+BAC3BpH,IAAI,gBACJP,QAAS,kBAAMkL,IAA4B,MAG9CV,IACC,kBAACrK,EAAA,EAAY,CACXP,UAAU,mBACVQ,YAAY,kBACZE,MAAOqH,EAAmB,mBAAqB,GAC/C/O,MAAQ+O,EAAwC,GAArB,mBAC3BpH,IAAI,eACJP,QAAS,kBAAMyK,GAAgBvC,UAyB5C+G,IAIL,OAAOxH,GAAQlI,EACb6O,GAEA,kBAAC,IAAS,CAACc,OAAO,iEAAiEd,KAIvFpD,GAAgB1P,UAAYA,GAEb0P,U,yYCpef,gmGAAAtR,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,olBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,8SAAAA,IAAA,4OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAwBA,IAAQyV,GAActV,OAAOoE,KAAKmR,MAA1BD,UACAjR,GAAgBrE,OAAOoE,KAAvBC,YAEF5C,GAAY,CAChB4M,kBAAmB3M,IAAUI,OAC7B0T,4BAA6B9T,IAAUO,KACvCwT,UAAW/T,IAAUO,KACrByT,uBAAwBhU,IAAUI,OAClCqN,iBAAkBzN,IAAUO,KAC5B0T,oBAAqBjU,IAAUY,KAC/ByM,iBAAkBrN,IAAUO,KAC5B2T,kBAAmBlU,IAAUY,KAC7BuT,kBAAmBnU,IAAUO,KAC7B6T,mBAAoBpU,IAAUY,KAC9BiO,kBAAmB7O,IAAUO,KAC7B8T,4BAA6BrU,IAAUO,KACvC+T,yBAA0BtU,IAAUO,KACpCgU,4BAA6BvU,IAAUY,KACvC4T,WAAYxU,IAAUY,MAGlB6T,GAA2B,SAA3BA,EAAwB,GAgBxB,IAfJ9H,EAAiB,EAAjBA,kBACAmH,EAA2B,EAA3BA,4BACAC,EAAS,EAATA,UACAC,EAAsB,EAAtBA,uBACAvG,EAAgB,EAAhBA,iBACAwG,EAAmB,EAAnBA,oBACA5G,EAAgB,EAAhBA,iBACA6G,EAAiB,EAAjBA,kBACAC,EAAiB,EAAjBA,kBACAC,EAAkB,EAAlBA,mBACAvF,EAAiB,EAAjBA,kBACAwF,EAA2B,EAA3BA,4BACAC,EAAwB,EAAxBA,yBACAC,EAA2B,EAA3BA,4BACAC,EAAU,EAAVA,WAmDC,KA1BGpT,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,kBAAkBF,EAAOG,IAAa4C,kBAChD9C,IAAUoT,cAAcrT,EAAOG,IAAa4C,kBAC5C9C,IAAUoT,cAAcrT,EAAOG,IAAamT,oBAC5CrT,IAAUsT,mCAAmCvT,GAC7CC,IAAUC,kBAAkBF,EAAOG,IAAaqT,aAChDvT,IAAUC,kBAAkBF,EAAOG,IAAaC,wBAChDH,IAAUC,kBAAkBF,EAAOG,IAAasT,sBAChDxT,IAAUoT,cAAcrT,EAAOG,IAAaqT,aAC5CvT,IAAUoT,cAAcrT,EAAOG,IAAauT,YAC5CzT,IAAUoT,cAAcrT,EAAOG,IAAawT,eAC5C1T,IAAUoT,cAAcrT,EAAO,iBAC/BC,IAAU2T,cAAc5T,GACxBC,IAAU4T,cAAc7T,GACxBC,IAAU6T,QAAQ9T,GAClBC,IAAU8T,oBAAoB/T,GAC9BC,IAAUoT,cAAcrT,EAAOG,IAAa6T,YAC5C/T,IAAUgU,mBAAmBjU,GAC7BC,IAAUK,2BAA2BN,GACrCC,IAAUiU,qBAAqBlU,GAC/BC,IAAUkU,gBAAgBnU,GAC1BC,IAAUoT,cAAcrT,EAAOG,IAAaiU,aAC5CnU,IAAUC,kBAAkBF,EAAOG,IAAaiU,gBAElDnO,KACD,IAhDC1F,EAAU,KACVuK,EAAM,KACNM,EAAsB,KACtBmI,EAAkC,KAClCc,EAAoB,KACpBC,EAA8B,KAC9BC,EAA0B,KAC1BC,EAAgB,KAChBtJ,EAAe,KACfC,EAAkB,KAClBF,EAAmB,MACnBwJ,EAAU,MACVC,EAAU,MACVC,EAAI,MACJC,EAAgB,MAChBC,EAAa,MACbC,EAAe,MACfla,GAAuB,MACvBsZ,GAAoB,MACpBa,GAAY,MACZC,GAAgB,MAChBC,GAAoB,MA4BflZ,GAAqB,GAAhB0E,cAAgB,GAApB,GACF5F,GAAW2F,cAC4C,MAA7BE,mBAAS,CAAEwU,KAAM,EAAGC,IAAK,IAAI,GAAtDzJ,GAAQ,MAAE0J,GAAW,MACqB,MAAf1U,oBAAS,GAAM,GAA1C2K,GAAS,MAAEgK,GAAY,MAC2C,MAAf3U,oBAAS,GAAM,GAAlEwL,GAAsB,MAAEoJ,GAAuB,MAChD9J,GAAW/G,mBAEX8Q,IAA8BhC,GAAqCzY,IAAK0a,qBAAqBlK,EAAmB1Q,IAChH6a,GAAe3a,IAAKmG,qBAAqBrG,IAIvCoT,GAAmB+G,GAAnB/G,eASR0H,YACElK,GACAmK,uBAAY,SAACjT,GAAM,MACXkT,EAAajG,cAAckG,cAAc,kBAAD,OAAmB1V,IAAaqT,YAAW,OACnFsC,EAAsBF,aAAU,EAAVA,EAAYG,SAASrT,EAAEE,QAC7CoT,EAAmE,QAAjD,EAAGrG,cAAckG,cAAc,0BAAkB,aAA9C,EAAgDE,SAASrT,EAAEE,QAChFgC,EAAaO,cACb8Q,EAAeC,cACfC,EAAcC,cAMfN,GAAwBE,GAAuBC,GAAiBE,GAAgBvR,IAC/E2O,EACFJ,IAEAtY,GAAS0B,IAAQuG,aAAa3C,IAAa4C,sBAG9C,CAACwQ,KAGN,IAMM8C,GAAeC,KAAS,WACxBlD,GANA5H,GAASzG,SACXqQ,GAAYmB,YAAkCjL,EAAmBE,GAAU5Q,OAtCzC,GA8CJ,CAAE,UAAY,EAAM,SAAW,IAE3D4b,GAAmBF,KAAS,WAC5BlD,GACFiC,GAAaE,MAEdkB,GAAmC,CAAE,UAAY,EAAM,SAAW,IAErE9R,qBAAU,WAGR,OAFA1H,OAAOyL,iBAAiB,SAAU2N,IAE3B,WACLpZ,OAAO0L,oBAAoB,SAAU0N,OAEtC,IAGHK,2BAAgB,YACVpL,GAAqBc,GAAoB0G,KAC3CuD,KACAhB,IAAa,GACbmB,QAGD,CAAClL,EAAmBc,EAAkB0G,EAAmBJ,EAAW9X,KAEvE+J,qBAAU,WACJ2G,GAAqBA,EAAkB1J,WAAa2Q,GAAUoE,yBAChErB,IAAwB,KAEzB,CAAChK,IAEJ3G,qBAAU,YACJmG,GAAUG,IACZpQ,GAAS0B,IAAQuG,aAAa3C,IAAasT,yBAE5C,CAAC3I,EAAQG,IAGZ,IACM2L,KADaC,YAAkB,qBAAsB,MAC1B5Z,OAAOoE,KAAKyV,qBAAqBC,OAAS9Z,OAAOoE,KAAKyV,qBAAqBE,OACtGrL,GAAqBL,aAA6BhK,GAAY2V,0BAA4BxC,GAC3FmC,GAAc5P,SAAS/J,OAAOoE,KAAK6V,oBAAoB5L,EAAkB6L,kBAAkBC,WAE1FxL,GAAa+J,sBAAW,cAAC,0FACxBjB,GAAeD,EAAU,yCACrB/Y,QAAQC,KAAK,0CAAyC,OAGyB,GADlF0b,EAAW/L,EAAkB6L,oBAC7BG,EAAoB3C,EAAK4C,MAAK,SAACC,GAAG,OAAKA,EAAIza,QAAQxB,WAAa8b,EAAS9b,aACxD,CAAF,+BACbmZ,EAAW+C,aAAaH,EAAkBxO,IAAI,GAAK,wCAG3C,OAH2C,KAGrD4L,EAAU,UAAcpJ,EAAkBoM,cAAa,QAK5D,OAL4D,iBAAE,CAC7DC,UAAW1a,OAAOoE,KAAK6V,oBAAoBG,EAASD,UACpD7b,SAAU8b,EAAS9b,SACnBqc,2BAA2B,EAC3BC,WAAW,GACZ,eALgBC,OAAM,iEAMtB,CAACpD,EAAYpJ,EAAmBqJ,EAAMF,IAGzC,GAAIlU,IAAe+K,EACjB,OAAO,KAIT,IAAMyM,GAAsBjd,IAAKkd,uBAAuBpd,IAClDqd,GAA8BF,GAAoBva,OAClD+N,GAA8B0M,GAA8B,EAE5DlM,GADkBT,aAA6BhK,GAAY0N,sBACd1D,EAAkBpG,iBAAmBpK,IAAKod,kBAAkB5M,EAAmB1Q,IAC5Hud,GAAiBrd,IAAKsd,kBAAkBL,GAAqBnd,IAC7Dyd,GAAWF,GAAiB,EAC5BG,GAAgC,IAAnBH,IAAwBF,GAA8B,GAAK1C,GACxE5I,GACJrB,aAA6BhK,GAAY0O,2BACtC1E,EAAkBiN,uBACjBrK,GAAmBpT,IAAK0d,gBAExB3M,KACFwI,IAAyBE,MACtBhJ,IAAgCA,KAAgCgK,KAClEjK,EAAkB1J,WAAa2Q,GAAUkG,OACxCzF,IACA1H,EAAkBwE,6BAClBnD,GA4CA+L,GAAWC,OAAOC,KAAKjG,GAAwBnV,OAAS,EAExDqb,GAAqB,CACzBtG,GAAUkG,KACVlG,GAAUuG,gBACVvG,GAAUwG,0BACVxG,GAAUyG,iBAGN7M,GACJuG,GACGgG,MACEpE,GAAkCtG,OAClCzC,IAA+B+M,IAAe/M,KAAgCgK,MAC/EsD,GAAmB7R,SAASsE,EAAkB1J,aAC7C0J,aAA6BhK,GAAYoN,qBAC1CpD,EAAkBwE,6BAClBnD,MACCrB,aAA6BhK,GAAY2X,4BAG1CpZ,GAAuByL,aAA6BhK,GAAYuN,oBAAsB/T,IAAKoe,mBAO3F1M,GACJlB,EAAkBwE,4BACfxE,EAAkB6N,uBAAyBlc,OAAOoE,KAAK+X,YAAYC,MAAMC,KAExE7M,GAAa,+BAAG,+EACpBgJ,GAAajT,QAAQ,0BAA2B8I,GAEhDzQ,GAAS0B,IAAQuG,aAAa3C,IAAa4C,mBAAmB,2CAC/D,kBAJkB,mCAcb8J,GADmB/R,IAAKye,uBAAuBjO,EAAmB1Q,MACvB2Q,KAAgCyH,EAQ3EwG,GAAoBzB,GAAoBR,MAAK,SAACkC,GAAkB,OAAMA,EAAmBC,aAEzF3M,GACJ2F,GACG6C,IACA8C,KACCrF,EAMA/F,GAAoBqL,GAQpBqB,GAD2B7e,IAAK8e,4BAA4Bhf,IACP+e,4BACrDxM,GAAsB6F,GACvB2G,MACErO,aAA6BhK,GAAY2X,4BAiB1C5L,GAAmBqF,EAgBnBpF,GAAqB,WACrBiI,GACFza,IAAK8N,kBAAkB9N,IAAKkd,uBAAuBpd,SAA0BkX,EAAWlX,IAExFE,IAAK8N,kBAAkB,CAAC0C,QAAoBwG,EAAWlX,IAEzDuY,KAiBI5F,KAbyB,CAC7BgF,GAAUkG,KACVlG,GAAUsH,UACVtH,GAAUuH,UACVvH,GAAUwH,WACVxH,GAAUyH,WACVzH,GAAU0H,WACV1H,GAAU2H,OACV3H,GAAU4H,QACV5H,GAAU6H,QACV7H,GAAU8H,SAIcrT,SAASsE,EAAkB1J,WAC/CoR,GACA1H,EAAkBwE,4BAEjBxE,aAA6BhK,GAAYgZ,iBAC1C3N,IAGAmB,GACJ4E,GACGpH,EAAkB9F,SAClB8F,aAA6BhK,GAAYiZ,eA4BxC7M,GAAyBpC,aAA6BhK,GAAY2V,yBAElEtJ,GAAsB,+BAAG,WAAO6M,GAAK,4FAEJA,EAAMC,sBAAqB,gBAAxDC,EAAQ,EAARA,SAAUC,EAAQ,EAARA,SAClBC,iBAAOF,EAAUC,GAAU,2CAC5B,gBAJ2B,sCAOtB/M,IACH/C,MACA4H,GACDnH,aAA6BhK,GAAYgZ,iBACzChP,EAAkBuP,eAGd7P,GAA2BwJ,GAC3BI,GAAoBC,GAAqC,eAApBC,GACtCZ,IACAyF,GAQL,OACE,kBAAC,GAAe,CACdhX,SAAUA,cACVkI,KAAMA,IACNC,OAAQA,EACRC,iBAAkBwI,EAClBvI,yBAA0BA,GAC1BC,oBAAqBA,EACrBC,gBAAiBA,EACjBC,mBAAoBA,EACpBC,uBAAwBA,EACxBC,UAAWA,GAEXG,SAAUA,GACVE,SAAUA,GACVJ,kBAAmBA,EACnBC,4BAA6BA,GAE7BI,mBAAoBA,GACpBC,WAAYA,GAEZC,kBAAmBA,GACnBC,oBAvQwB,WAC1B,GAAIC,GACF8G,GAAkB,OADpB,CAKAhY,GAAS0B,IAAQuG,aAAa,gBAC9BjI,GAAS0B,IAAQuG,aAAa,mBAC9B,IAAMgY,EAAqBhgB,IAAKigB,sBAAsBngB,IAClDkgB,EAAmBE,wBACrBngB,GAAS0B,IAAQuG,aAAa,qBAC9BgY,EAAmBG,sBAGrBpgB,GAAS0B,IAAQ2e,sBACb3G,GACF1Z,GAAS0B,IAAQ4e,YAAYhb,IAAaqT,cAC1C3Y,GAAS0B,IAAQuG,aAAa3C,IAAa4C,qBAEtCyR,GACH3Z,GAAS0B,IAAQ4e,YAAYhb,IAAasT,uBAE5CN,OAkPApH,sBAAuBA,GACvBC,iBAAkBA,EAClBC,iBA3OqB,SAACmP,GACxB3F,GAAa4F,gBAAgB/P,EAAmB8P,GAChD3F,GAAa6F,iBAAiBhQ,IA0O5B/G,iBAvOqB,SAACgX,GACxBxI,EAAmBwI,IAuOjBrP,uBAAwBA,GAExBC,oBAAqBA,GACrBC,iBAAkBA,EAClBvM,qBAAsBA,GACtBwM,mBAAoB,kBAAMuG,GAAoB,IAC9CtG,oBAAqB,kBAAMsG,GAAoB,IAC/CrG,gBAAiBoG,EACjB7S,SApNa,WACfoT,GAA6BD,IAqN3BzG,sBAAuBA,GACvBC,cAAeA,GACfC,kCA/IsC,WACxC,IAIM9Q,EAAU,CACdC,QALcE,GAAE,4CAMhBC,MALYD,GAAE,0CAMdE,eALqBF,GAAE,aAMvBG,UAAW,kBAAMoR,OAEnBzS,GAAS0B,IAAQC,mBAAmBZ,KAsIlC+Q,sBAAuBA,GACvBC,2BA3M+B,WACjCtB,EAAkBkQ,eAAe/F,IACjC5a,GAAS0B,IAAQuG,aAAa3C,IAAa4C,oBA2MzC8J,oBAAqBA,GACrBC,iBArMqB,WACvBjS,GAASK,YAAgBoQ,OAAmBwG,EAAWlX,KACvDC,GAAS0B,IAAQuG,aAAa3C,IAAa4C,oBAqMzCgK,gBAAiBA,GACjBC,mBA1LuB,WACzBlS,IAAK2gB,iBAAiBjC,GAAmBzB,GAAqBnd,KA0L5DqS,kBAAmBA,GACnBC,qBAtLyB,WAC3BpS,IAAK4gB,mBAAmB3D,GAAqBnd,KAuL3CuS,oBAAqBA,GACrBC,gBA9KoB,WACtB+F,IAGInF,IACFnT,GAAS0B,IAAQof,eAAeC,MAChC/gB,GAAS0B,IAAQuG,aAAa3C,IAAa0b,yBAE3ChhB,GAAS0B,IAAQof,eAAexb,IAAa4C,iBAAkB6Y,MAEjE/gB,GAAS0B,IAAQ4e,YAAYhb,IAAa0b,wBAC1ChhB,GAAS0B,IAAQ4e,YAAYhb,IAAa2b,oBAqKxCzO,iBAAkBA,GAClBC,mBAAoBA,GAEpBC,eAAgBA,GAChBC,kBAAmBA,EACnBC,oBA5GwB,WACtBD,IAC0B+H,GAA8BwC,GAAsB,CAACzM,IAC7DxN,SAAQ,SAAC0c,GACHuB,YAA0BvB,GAClC1c,SAAQ,SAACke,EAAWC,GAClCxG,GAAaiG,mBAAmB,CAACM,IAC7BxB,aAAiBlZ,GAAY4a,yBAA6C,IAAlB1B,EAAM2B,SAA2B,IAAVF,EACjFxG,GAAa7M,kBAAkB,CAAC4R,EAAOwB,GAAY,MAAM,GAEzDvG,GAAa2G,iBAAiBJ,EAAW,MAAM,SAIrD7I,KAEAtY,GAAS0B,IAAQ4e,YAAYhb,IAAauT,cA8F1ChG,uBAAwBA,GACxBC,uBAAwBA,GAExBC,oBAAqBA,GACrBC,gBAxEoB,SAAC3M,GACvBrG,GAAS0B,IAAQ8f,yBAAyBnb,IAC1CrG,GAAS0B,IAAQ+f,mCAAkC,IACnDzhB,GAAS0B,IAAQ4e,YAAY,wBAuE3BrN,oBAAqBA,GACrBC,kBA1HsB,WACxBlT,GAAS0B,IAAQuG,aAAa3C,IAAa4C,mBAC3ClI,GAAS0B,IAAQ4e,YAAYhb,IAAaoc,qBA0HxCvO,eAAgBA,GAChBC,eA5bmB,WAChB+G,IAAqBC,IACxBpa,GAAS0B,IAAQ4e,YAAYhb,IAAaiU,cAE5CjB,KAybE6B,iBAAkBA,GAClB9G,iBAAkBA,GAElBC,qBAnSyB,WAC3BtT,GAAS0B,IAAQ4e,YAAYhb,IAAaqc,6BAC1CrJ,QAsSJC,GAAyB1U,UAAYA,GAEtB0U,UCpkBAA", "file": "chunks/chunk.41.js", "sourcesContent": ["import core from 'core';\nimport i18next from 'i18next';\n\nimport actions from 'actions';\nimport { fireError } from 'helpers/fireEvent';\nimport downloadPdf from 'helpers/downloadPdf';\n\nfunction noop() { }\n\nexport default (annotations, onRedactionCompleted = noop, activeDocumentViewerKey = 1) => (dispatch) => {\n  if (core.isWebViewerServerDocument()) {\n    // when are using Webviewer Server, it'll download the redacted document\n    return webViewerServerApply(annotations, dispatch, activeDocumentViewerKey);\n  }\n  return webViewerApply(annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey);\n};\n\nconst webViewerServerApply = (annotations, dispatch, activeDocumentViewerKey) => core.applyRedactions(annotations, activeDocumentViewerKey).then((results) => {\n  if (results && results.url) {\n    return downloadPdf(dispatch, {\n      filename: 'redacted.pdf',\n      includeAnnotations: true,\n      externalURL: results.url,\n    });\n  }\n  console.warn('WebViewer Server did not return a valid result');\n});\n\nconst webViewerApply = (annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey) => {\n  const message = i18next.t('warning.redaction.applyMessage');\n  const title = i18next.t('warning.redaction.applyTile');\n  const confirmBtnText = i18next.t('action.apply');\n\n  const warning = {\n    message,\n    title,\n    confirmBtnText,\n    onConfirm: () => {\n      core.applyRedactions(annotations, activeDocumentViewerKey)\n        .then(() => {\n          onRedactionCompleted();\n        })\n        .catch((err) => fireError(err));\n      return Promise.resolve();\n    },\n  };\n\n  return dispatch(actions.showWarningMessage(warning));\n};\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AnnotationStylePopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}.AnnotationStylePopup .back-to-menu-container{margin-top:var(--padding-medium);margin-right:var(--padding-medium);margin-left:var(--padding-medium);padding-bottom:var(--padding-small);border-bottom:1px solid var(--border)}.AnnotationStylePopup .Button.back-to-menu-button{margin:0;width:100%;height:32px;border-radius:0;justify-content:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { useSelector, useDispatch } from 'react-redux';\nimport Measure from 'react-measure';\nimport { useTranslation } from 'react-i18next';\nimport StylePopup from 'components/StylePopup';\nimport ActionButton from 'components/ActionButton';\nimport core from 'core';\nimport getClassName from 'helpers/getClassName';\nimport setToolStyles from 'helpers/setToolStyles';\nimport { isMobile } from 'helpers/device';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport handleFreeTextAutoSizeToggle from 'src/helpers/handleFreeTextAutoSizeToggle';\n\nimport './AnnotationStylePopup.scss';\n\nconst propTypes = {\n  annotations: PropTypes.array.isRequired,\n  style: PropTypes.object.isRequired,\n  properties: PropTypes.object.isRequired,\n  isRedaction: PropTypes.bool,\n  isFreeText: PropTypes.bool,\n  isEllipse: PropTypes.bool,\n  hasBackToMenu: PropTypes.bool,\n  onBackToMenu: PropTypes.func,\n};\n\nconst AnnotationStylePopup = (props) => {\n  const {\n    annotations,\n    style,\n    isRedaction,\n    isFreeText,\n    isEllipse,\n    isMeasure,\n    colorMapKey,\n    showLineStyleOptions,\n    properties,\n    hideSnapModeCheckbox,\n    onResize,\n    hasBackToMenu,\n    onBackToMenu\n  } = props;\n\n  const [\n    isDisabled,\n    isToolDefaultStyleUpdateFromAnnotationPopupEnabled,\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.ANNOTATION_STYLE_POPUP),\n    selectors.isToolDefaultStyleUpdateFromAnnotationPopupEnabled(state),\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n  const [isAutoSizeFont, setAutoSizeFont] = useState(properties.isAutoSizeFont);\n\n  const handleSliderChange = (property, value) => {\n    const annotationManager = core.getAnnotationManager(activeDocumentViewerKey);\n    annotations.forEach((annotation) => {\n      annotation[property] = value;\n      if (property === 'StrokeThickness') {\n        adjustFreeTextBoundingBox(annotation);\n      }\n      annotationManager.redrawAnnotation(annotation);\n\n      if (annotation instanceof window.Core.Annotations.WidgetAnnotation) {\n        annotation.refresh();\n      }\n    });\n  };\n\n  const handlePropertyChange = (property, value) => {\n    annotations.forEach((annotation) => {\n      core.setAnnotationStyles(annotation, {\n        [property]: value,\n      }, activeDocumentViewerKey);\n      if (isToolDefaultStyleUpdateFromAnnotationPopupEnabled) {\n        setToolStyles(annotation.ToolName, property, value);\n      }\n      if (property === 'FontSize' || property === 'Font') {\n        adjustFreeTextBoundingBox(annotation);\n      }\n    });\n  };\n\n  const handleStyleChange = (property, value) => {\n    annotations.forEach((annotation) => {\n      core.setAnnotationStyles(annotation, {\n        [property]: value,\n      }, activeDocumentViewerKey);\n\n      if (isToolDefaultStyleUpdateFromAnnotationPopupEnabled) {\n        setToolStyles(annotation.ToolName, property, value);\n      }\n    });\n  };\n\n  const handleRichTextStyleChange = (property, value) => {\n    annotations.forEach((annotation) => {\n      core.updateAnnotationRichTextStyle(annotation, { [property]: value }, activeDocumentViewerKey);\n    });\n  };\n\n  const handleLineStyleChange = (section, value) => {\n    annotations.forEach((annotation) => {\n      let lineStyle = '';\n      if (section === 'start') {\n        annotation.setStartStyle(value);\n        lineStyle = 'StartLineStyle';\n      } else if (section === 'end') {\n        annotation.setEndStyle(value);\n        lineStyle = 'EndLineStyle';\n      } else if (section === 'middle') {\n        const dashes = value.split(',');\n        const style = dashes.shift();\n        annotation['Style'] = style;\n        annotation['Dashes'] = dashes;\n        lineStyle = 'StrokeStyle';\n      }\n\n      if (isToolDefaultStyleUpdateFromAnnotationPopupEnabled) {\n        setToolStyles(annotation.ToolName, lineStyle, value);\n      }\n\n      core.getAnnotationManager(activeDocumentViewerKey).redrawAnnotation(annotation);\n    });\n\n    core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [annotations, 'modify', {}]);\n  };\n\n  const handleClick = (e) => {\n    if (isMobile() && e.target === e.currentTarget) {\n      dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n    }\n  };\n\n  const handleResize = () => {\n    onResize && onResize();\n  };\n\n  const className = getClassName('Popup AnnotationStylePopup', props);\n\n  return isDisabled ? null : (\n    <Measure\n      onResize={handleResize}\n    >\n      {({ measureRef }) => (\n        <div\n          className={className}\n          data-element={DataElements.ANNOTATION_STYLE_POPUP}\n          onClick={handleClick}\n          ref={measureRef}\n        >\n          {hasBackToMenu &&\n            <div\n              className=\"back-to-menu-container\"\n              data-element={DataElements.ANNOTATION_STYLE_POPUP_BACK_BUTTON_CONTAINER}\n            >\n              <ActionButton\n                className=\"back-to-menu-button\"\n                dataElement={DataElements.ANNOTATION_STYLE_POPUP_BACK_BUTTON}\n                label={t('action.backToMenu')}\n                img=\"icon-chevron-left\"\n                onClick={onBackToMenu}\n              />\n            </div>\n          }\n          {/* Do not show checkbox for ellipse as snap mode does not exist for it */}\n          <StylePopup\n            hideSnapModeCheckbox={hideSnapModeCheckbox}\n            colorMapKey={colorMapKey}\n            style={style}\n            isFreeText={isFreeText}\n            isFreeTextAutoSize={isAutoSizeFont}\n            onFreeTextSizeToggle={() => handleFreeTextAutoSizeToggle(annotations[0], setAutoSizeFont, isAutoSizeFont)}\n            isEllipse={isEllipse}\n            isMeasure={isMeasure}\n            onStyleChange={handleStyleChange}\n            onSliderChange={handleSliderChange}\n            onPropertyChange={handlePropertyChange}\n            disableSeparator\n            properties={properties}\n            onRichTextStyleChange={handleRichTextStyleChange}\n            isRedaction={isRedaction}\n            showLineStyleOptions={showLineStyleOptions}\n            onLineStyleChange={handleLineStyleChange}\n          />\n        </div>\n      )}\n    </Measure>\n  );\n};\n\nAnnotationStylePopup.propTypes = propTypes;\n\nexport default AnnotationStylePopup;\n", "import AnnotationStylePopup from './AnnotationStylePopup';\n\nexport default AnnotationStylePopup;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./AnnotationPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CalibrationPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.CalibrationPopup{display:flex;flex-direction:column;align-items:flex-start;padding:var(--padding-medium);width:220px;background:var(--gray-0);box-shadow:0 0 3px var(--gray-7);border-radius:4px}.CalibrationPopup .calibration-popup-label{font-weight:700;margin-bottom:var(--padding-medium)}.CalibrationPopup .pop-switch{margin-top:var(--padding-medium)}.CalibrationPopup .pop-switch.ui__choice--disabled .ui__choice__label{color:var(--gray-5)}.CalibrationPopup .input-container{display:flex;flex-direction:row;justify-content:space-between;align-items:flex-start;grid-gap:var(--padding-small);gap:var(--padding-small);height:32px}.CalibrationPopup .input-container .input-field{width:94px;height:32px}.CalibrationPopup .input-container .input-field.invalid-value{border-color:red}.CalibrationPopup .input-container .input-field:focus{border:1px solid var(--blue-5)}.CalibrationPopup .input-container .input-field .Dropdown__wrapper{width:100%;height:100%}.CalibrationPopup .input-container .input-field .Dropdown__wrapper .Dropdown{height:100%;width:100%!important;text-align:left}.CalibrationPopup .input-container .input-field .Dropdown__wrapper .Dropdown__items{width:100%}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.AnnotationPopup{visibility:visible}.closed.AnnotationPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.AnnotationPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.AnnotationPopup:empty{padding:0}.AnnotationPopup .buttons{display:flex}.AnnotationPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationPopup .Button{width:42px;height:42px}}.AnnotationPopup .Button:hover{background:var(--popup-button-hover)}.AnnotationPopup .Button:hover:disabled{background:none}.AnnotationPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationPopup .Button .Icon{width:24px;height:24px}}.is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.AnnotationPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.AnnotationPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.AnnotationPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.AnnotationPopup{position:fixed;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.AnnotationPopup.is-horizontal .container{display:inherit}.AnnotationPopup.is-vertical{flex-direction:column;align-items:flex-start}.shortCuts3D{position:relative}.shortCuts3D .closeButton{position:absolute;right:4px;padding:2px;cursor:pointer;width:20px;text-align:center}.shortCuts3D .row{padding:4px 0;margin:8px 26px 8px 16px}.shortCuts3D .row span{background:#e7ebee;padding:4px 8px;border-radius:4px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useRef, useEffect } from 'react';\nimport PropTypes from 'prop-types';\n\nconst DatePicker = ({ onClick, annotation, onDatePickerShow }) => {\n  const dateRef = useRef(null);\n  const dateContainerRef = useRef(null);\n  useEffect(() => {\n    let datePicker;\n    const getDatePicker = async () => {\n      datePicker = await window.Core.createDatePicker({\n        field: dateRef.current,\n        onClick,\n        container: dateContainerRef.current,\n        format: annotation.getDateFormat(),\n      });\n      onDatePickerShow(true);\n    };\n    getDatePicker();\n\n    return () => {\n      datePicker.destroy();\n      datePicker = null;\n      onDatePickerShow(false);\n    };\n  }, []);\n  return (\n    <div data-element=\"datePickerContainer\">\n      <div ref={dateRef} />\n      <div ref={dateContainerRef}/>\n    </div>\n  );\n};\n\nDatePicker.propTypes = {\n  onClick: PropTypes.func.isRequired,\n  annotation: PropTypes.object.isRequired,\n  onDatePickerShow: PropTypes.func.isRequired\n};\n\nexport default DatePicker;\n", "import DatePicker from './DatePicker';\n\nexport default DatePicker;", "import React, { useState, useEffect, useRef } from 'react';\nimport selectors from 'selectors';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport core from 'core';\nimport actions from 'actions';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport Dropdown from '../Dropdown';\nimport Tooltip from '../Tooltip';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport {\n  metricUnits,\n  convertUnit,\n  fractionalUnits,\n  floatRegex,\n  inFractionalRegex,\n  ftInFractionalRegex,\n  ftInDecimalRegex,\n  parseFtInDecimal,\n  parseInFractional,\n  parseFtInFractional,\n  hintValues\n} from 'constants/measurementScale';\nimport classNames from 'classnames';\n\nimport './CalibrationPopup.scss';\n\nconst Scale = window.Core.Scale;\n\nconst parseMeasurementContentsByAnnotation = (annotation) => {\n  const factor = annotation.Measure.axis[0].factor;\n  const unit = annotation.Scale[1][1];\n\n  switch (unit) {\n    case 'ft-in':\n      return (annotation.getLineLength() * factor) / 12;\n    case 'in':\n    default:\n      return annotation.getLineLength() * factor;\n  }\n};\n\nconst getDefaultPageUnit = (pageUnit) => {\n  if (pageUnit === 'pt') {\n    return 'pt';\n  }\n  if (metricUnits.includes(pageUnit)) {\n    return 'mm';\n  }\n  return 'in';\n};\n\nconst CalibrationPropType = {\n  annotation: PropTypes.shape({\n    Scale: PropTypes.arrayOf(PropTypes.array),\n  }),\n};\n\nconst CalibrationPopup = ({ annotation }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [\n    measurementUnits,\n    { tempScale, isFractionalUnit, defaultUnit }\n  ] = useSelector((state) => [\n    selectors.getMeasurementUnits(state),\n    selectors.getCalibrationInfo(state)\n  ], shallowEqual);\n  const [valueDisplay, setValueDisplay] = useState('');\n  const inputRef = useRef(null);\n\n  const unitTo = new Scale(tempScale).worldScale?.unit || 'mm';\n  const unitToOptions = isFractionalUnit ? measurementUnits.to.filter((unit) => fractionalUnits.includes(unit)) : measurementUnits.to;\n  const isFractionalUnitsToggleDisabled = !fractionalUnits.includes(unitTo);\n  const valueInputType = (isFractionalUnit || unitTo === 'ft-in') ? 'text' : 'number';\n  const inputValueClass = classNames('input-field', {\n    'invalid-value': !(tempScale && new Scale(tempScale).worldScale?.value > 0)\n  });\n\n  const updateTempScale = (scaleValue, scaleUnit) => {\n    const currentDistance = parseMeasurementContentsByAnnotation(annotation);\n    const currentScale = annotation.Scale;\n    const newRatio = currentDistance / currentScale[1][0];\n    const pageScale = [currentScale[0][0] * newRatio, currentScale[0][1]];\n    const defaultPageUnit = getDefaultPageUnit(scaleUnit);\n    const defaultPageValue = convertUnit(pageScale[0], pageScale[1], defaultPageUnit);\n    dispatch(actions.updateCalibrationInfo({ tempScale: `${defaultPageValue} ${defaultPageUnit} = ${scaleValue} ${scaleUnit}`, isFractionalUnit }));\n  };\n\n  const setValue = (scaleValue) => {\n    updateTempScale(scaleValue, new Scale(tempScale).worldScale?.unit);\n  };\n\n  const setUnitTo = (scaleUnit) => {\n    updateTempScale(new Scale(tempScale).worldScale?.value, scaleUnit);\n  };\n\n  const toggleFractionalUnits = () => {\n    dispatch(actions.updateCalibrationInfo({ tempScale, isFractionalUnit: !isFractionalUnit }));\n  };\n\n  const onValueInputChange = (e) => {\n    setValueDisplay(e.target.value);\n    const inputValue = e.target.value.trim();\n    if (!isFractionalUnit) {\n      if (unitTo === 'ft-in' && ftInDecimalRegex.test(inputValue)) {\n        const result = parseFtInDecimal(inputValue);\n        if (result > 0) {\n          setValue(result);\n          return;\n        }\n      } else if (floatRegex.test(inputValue)) {\n        const result = parseFloat(inputValue) || 0;\n        setValue(result);\n        return;\n      }\n    } else {\n      if (unitTo === 'in') {\n        if (inFractionalRegex.test(inputValue)) {\n          const result = parseInFractional(inputValue);\n          if (result > 0) {\n            setValue(result);\n            return;\n          }\n        }\n      } else if (unitTo === 'ft-in') {\n        if (ftInFractionalRegex.test(inputValue)) {\n          const result = parseFtInFractional(inputValue);\n          if (result > 0) {\n            setValue(result);\n            return;\n          }\n        }\n      }\n    }\n    setValue(0);\n  };\n\n  const onValueInputBlur = () => {\n    updateValueDisplay();\n  };\n\n  const updateValueDisplay = () => {\n    const scaleValue = new Scale(tempScale).worldScale?.value;\n    let newValueDisplay;\n    if (!isFractionalUnit && unitTo !== 'ft-in') {\n      newValueDisplay = `${scaleValue}`;\n    } else {\n      newValueDisplay = Scale.getFormattedValue(scaleValue, unitTo, isFractionalUnit ? 1 / 64 : 0.0001, false, true);\n    }\n    setValueDisplay(newValueDisplay || '');\n  };\n\n  const tempScaleRef = useRef(tempScale);\n  useEffect(() => {\n    tempScaleRef.current = tempScale;\n  }, [tempScale]);\n  useEffect(() => {\n    if (annotation) {\n      const value = parseMeasurementContentsByAnnotation(annotation);\n      const unit = annotation.Scale[1][1];\n      if (defaultUnit) {\n        updateTempScale(convertUnit(value, unit, defaultUnit), defaultUnit);\n      } else {\n        updateTempScale(value, unit);\n      }\n    }\n\n    const onAnnotationChanged = (annotations, action) => {\n      if (action === 'modify' && annotations.length === 1 && annotations[0] === annotation) {\n        const value = parseMeasurementContentsByAnnotation(annotation);\n        const unit = annotation.Scale[1][1];\n        const currentUnit = new Scale(tempScaleRef.current).worldScale?.unit;\n        if (currentUnit) {\n          updateTempScale(convertUnit(value, unit, currentUnit), currentUnit);\n        } else {\n          updateTempScale(value, unit);\n        }\n      }\n    };\n    core.addEventListener('annotationChanged', onAnnotationChanged);\n\n    return () => {\n      core.removeEventListener('annotationChanged', onAnnotationChanged);\n      core.deleteAnnotations([annotation]);\n    };\n  }, [annotation]);\n\n  useEffect(() => {\n    if (inputRef?.current !== document.activeElement) {\n      updateValueDisplay();\n    }\n  }, [tempScale, isFractionalUnit]);\n\n  return (\n    <div className=\"CalibrationPopup\" data-element=\"calibrationPopup\">\n      <label className=\"calibration-popup-label\" id='calibration-popup-label' htmlFor='calibration-popup-value'>{t('option.measurement.scaleModal.units')}</label>\n      <div className=\"input-container\">\n        <input\n          id='calibration-popup-value'\n          className={inputValueClass}\n          ref={inputRef}\n          type={valueInputType}\n          value={valueDisplay}\n          min='0'\n          onChange={onValueInputChange}\n          onBlur={onValueInputBlur}\n          placeholder={isFractionalUnit ? hintValues[unitTo] : (unitTo === 'ft-in' ? hintValues['ft-in decimal'] : '')}\n        />\n        <Tooltip content={'option.measurement.scaleModal.displayUnits'}>\n          <div className=\"input-field\">\n            <Dropdown\n              id=\"calibration-popup-units\"\n              dataElement=\"calibrationUnits\"\n              items={unitToOptions}\n              currentSelectionKey={unitTo}\n              onClickItem={setUnitTo}\n              labelledById='calibration-popup-label'\n            />\n          </div>\n        </Tooltip>\n      </div>\n      <Tooltip content={t('option.measurement.scaleModal.fractionUnitsTooltip')}>\n        <div>\n          <Choice\n            isSwitch\n            leftLabel\n            label={t('option.measurement.scaleModal.fractionalUnits')}\n            disabled={isFractionalUnitsToggleDisabled}\n            checked={isFractionalUnit}\n            id=\"calibration-popup-fractional-units\"\n            className=\"pop-switch\"\n            onChange={toggleFractionalUnits}\n          />\n        </div>\n      </Tooltip>\n    </div>\n  );\n};\n\nCalibrationPopup.propTypes = CalibrationPropType;\n\nexport default CalibrationPopup;\n", "import CalibrationPopup from './CalibrationPopup';\n\nexport default CalibrationPopup;", "import React, { useState } from 'react';\nimport Draggable from 'react-draggable';\nimport classNames from 'classnames';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport FocusTrap from 'components/FocusTrap';\n\nimport ActionButton from 'components/ActionButton';\nimport AnnotationStylePopup from 'components/AnnotationStylePopup';\nimport DatePicker from 'components/DatePicker';\nimport CustomizablePopup from 'components/CustomizablePopup';\nimport CalibrationPopup from 'components/CalibrationPopup';\n\nimport { getDataWithKey, mapToolNameToKey, mapAnnotationToKey } from 'constants/map';\n\nimport DataElements from 'constants/dataElement';\n\nimport './AnnotationPopup.scss';\nimport getRootNode from 'helpers/getRootNode';\n\nconst propTypes = {\n  isMobile: PropTypes.bool,\n  isIE: PropTypes.bool,\n  isOpen: PropTypes.bool,\n  isRightClickMenu: PropTypes.bool,\n  isNotesPanelOpenOrActive: PropTypes.bool,\n  isRichTextPopupOpen: PropTypes.bool,\n  isLinkModalOpen: PropTypes.bool,\n  isWarningModalOpen: PropTypes.bool,\n  isContextMenuPopupOpen: PropTypes.bool,\n  isVisible: PropTypes.bool,\n\n  focusedAnnotation: PropTypes.object,\n  multipleAnnotationsSelected: PropTypes.bool,\n  popupRef: PropTypes.any,\n  position: PropTypes.object,\n\n  showViewFileButton: PropTypes.bool,\n  onViewFile: PropTypes.func,\n\n  showCommentButton: PropTypes.bool,\n  onCommentAnnotation: PropTypes.func,\n  isDateFreeTextCanEdit: PropTypes.bool,\n  isDatePickerOpen: PropTypes.bool,\n  handleDateChange: PropTypes.func,\n  onDatePickerShow: PropTypes.func,\n  isCalibrationPopupOpen: PropTypes.bool,\n\n  showEditStyleButton: PropTypes.bool,\n  isStylePopupOpen: PropTypes.bool,\n  hideSnapModeCheckbox: PropTypes.bool,\n  openEditStylePopup: PropTypes.func,\n  closeEditStylePopup: PropTypes.func,\n  annotationStyle: PropTypes.object,\n  onResize: PropTypes.func,\n\n  showContentEditButton: PropTypes.bool,\n  onEditContent: PropTypes.func,\n  openContentEditDeleteWarningModal: PropTypes.func,\n\n  isAppearanceSignature: PropTypes.bool,\n  onClearAppearanceSignature: PropTypes.func,\n\n  showRedactionButton: PropTypes.bool,\n  onApplyRedaction: PropTypes.func,\n\n  showGroupButton: PropTypes.bool,\n  onGroupAnnotations: PropTypes.func,\n  showUngroupButton: PropTypes.bool,\n  onUngroupAnnotations: PropTypes.func,\n\n  showFormFieldButton: PropTypes.bool,\n  onOpenFormField: PropTypes.func,\n\n  showDeleteButton: PropTypes.bool,\n  onDeleteAnnotation: PropTypes.func,\n\n  showLinkButton: PropTypes.bool,\n  hasAssociatedLink: PropTypes.bool,\n  linkAnnotationToURL: PropTypes.func,\n\n  showFileDownloadButton: PropTypes.bool,\n  downloadFileAttachment: PropTypes.func,\n\n  showAudioPlayButton: PropTypes.bool,\n  handlePlaySound: PropTypes.func,\n\n  showCalibrateButton: PropTypes.bool,\n  onOpenCalibration: PropTypes.func,\n\n  customizableUI: PropTypes.bool,\n  openStylePanel: PropTypes.func,\n  isInReadOnlyMode: PropTypes.bool,\n  onOpenAlignmentModal: PropTypes.func,\n};\n\nconst AnnotationPopup = ({\n  isMobile,\n  isIE,\n  isOpen,\n  isRightClickMenu,\n  isNotesPanelOpenOrActive,\n  isRichTextPopupOpen,\n  isLinkModalOpen,\n  isWarningModalOpen,\n  isContextMenuPopupOpen,\n  isVisible,\n\n  focusedAnnotation,\n  popupRef,\n  position,\n  multipleAnnotationsSelected,\n\n  showViewFileButton,\n  onViewFile,\n\n  showCommentButton,\n  onCommentAnnotation,\n  isDateFreeTextCanEdit,\n  isDatePickerOpen,\n  handleDateChange,\n  onDatePickerShow,\n  isCalibrationPopupOpen,\n\n  showEditStyleButton,\n  isStylePopupOpen,\n  hideSnapModeCheckbox,\n  openEditStylePopup,\n  closeEditStylePopup,\n  annotationStyle,\n  onResize,\n\n  showContentEditButton,\n  onEditContent,\n  openContentEditDeleteWarningModal,\n\n  isAppearanceSignature,\n  onClearAppearanceSignature,\n\n  showRedactionButton,\n  onApplyRedaction,\n\n  showGroupButton,\n  onGroupAnnotations,\n  showUngroupButton,\n  onUngroupAnnotations,\n\n  showFormFieldButton,\n  onOpenFormField,\n\n  showDeleteButton,\n  onDeleteAnnotation,\n\n  showLinkButton,\n  hasAssociatedLink,\n  linkAnnotationToURL,\n\n  showFileDownloadButton,\n  downloadFileAttachment,\n\n  showAudioPlayButton,\n  handlePlaySound,\n\n  showCalibrateButton,\n  onOpenCalibration,\n\n  customizableUI,\n  openStylePanel,\n  isInReadOnlyMode,\n  onOpenAlignmentModal,\n}) => {\n  const [t] = useTranslation();\n  const [shortCutKeysFor3DVisible, setShortCutKeysFor3DVisible] = useState(false);\n\n  const commentButtonLabel = isDateFreeTextCanEdit ? 'action.changeDate' : 'action.comment';\n  const commentButtonImg = isDateFreeTextCanEdit ? 'icon-tool-fill-and-sign-calendar' : 'icon-header-chat-line';\n  const show3DShortCutButton = !isInReadOnlyMode && focusedAnnotation instanceof window.Core.Annotations.Model3DAnnotation && !isMobile;\n  const isRectangle = focusedAnnotation instanceof window.Core.Annotations.RectangleAnnotation;\n  const isEllipse = focusedAnnotation instanceof window.Core.Annotations.EllipseAnnotation;\n  const isPolygon = focusedAnnotation instanceof window.Core.Annotations.PolygonAnnotation;\n  const isFreeText =\n    focusedAnnotation instanceof window.Core.Annotations.FreeTextAnnotation &&\n    (focusedAnnotation.getIntent() === window.Core.Annotations.FreeTextAnnotation.Intent.FreeText ||\n      focusedAnnotation.getIntent() === window.Core.Annotations.FreeTextAnnotation.Intent.FreeTextCallout);\n  const isRedaction = focusedAnnotation instanceof window.Core.Annotations.RedactionAnnotation;\n  const colorMapKey = mapAnnotationToKey(focusedAnnotation);\n  const isMeasure = !!focusedAnnotation.Measure;\n  const showLineStyleOptions = getDataWithKey(mapToolNameToKey(focusedAnnotation.ToolName)).hasLineEndings;\n  const isInstanceActive = !window.isApryseWebViewerWebComponent || document.activeElement?.shadowRoot === getRootNode();\n  let StrokeStyle = 'solid';\n  const isContentEdit = focusedAnnotation.isContentEditPlaceholder?.();\n  const isReadOnlySignature = focusedAnnotation instanceof window.Core.Annotations.SignatureWidgetAnnotation && focusedAnnotation.fieldFlags.get(window.Core.Annotations.WidgetFlags.READ_ONLY);\n  const showClearSignatureButton = isAppearanceSignature && !showFormFieldButton;\n  try {\n    StrokeStyle = (focusedAnnotation['Style'] === 'dash')\n      ? `${focusedAnnotation['Style']},${focusedAnnotation['Dashes']}`\n      : focusedAnnotation['Style'];\n  } catch (err) {\n    console.error(err);\n  }\n  let properties = {};\n  if (showLineStyleOptions) {\n    properties = {\n      StartLineStyle: focusedAnnotation.getStartStyle(),\n      EndLineStyle: focusedAnnotation.getEndStyle(),\n      StrokeStyle,\n    };\n  }\n\n  if (isRectangle || isEllipse || isPolygon) {\n    properties = {\n      StrokeStyle,\n    };\n  }\n\n  if (isFreeText) {\n    const richTextStyles = focusedAnnotation.getRichTextStyle();\n    const isAutoSizeFont = focusedAnnotation.isAutoSizeFont();\n    const calculatedFontSize = focusedAnnotation.getCalculatedFontSize();\n\n    properties = {\n      Font: focusedAnnotation.Font,\n      FontSize: focusedAnnotation.FontSize,\n      TextAlign: focusedAnnotation.TextAlign,\n      TextVerticalAlign: focusedAnnotation.TextVerticalAlign,\n      bold: richTextStyles?.[0]?.['font-weight'] === 'bold' ?? false,\n      italic: richTextStyles?.[0]?.['font-style'] === 'italic' ?? false,\n      underline: richTextStyles?.[0]?.['text-decoration']?.includes('underline') || richTextStyles?.[0]?.['text-decoration']?.includes('word'),\n      strikeout: richTextStyles?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n      StrokeStyle,\n      isAutoSizeFont,\n      calculatedFontSize,\n    };\n  }\n\n  if (isRedaction) {\n    properties = {\n      OverlayText: focusedAnnotation['OverlayText'],\n      Font: focusedAnnotation['Font'],\n      FontSize: focusedAnnotation['FontSize'],\n      TextAlign: focusedAnnotation['TextAlign']\n    };\n  }\n\n  const renderPopup = () => {\n    switch (true) {\n      case isStylePopupOpen:\n        return (\n          <AnnotationStylePopup\n            annotations={[focusedAnnotation]}\n            style={annotationStyle}\n            isOpen={isOpen}\n            onResize={onResize}\n            isFreeText={isFreeText}\n            isEllipse={isEllipse}\n            isRedaction={isRedaction}\n            isMeasure={isMeasure}\n            colorMapKey={colorMapKey}\n            showLineStyleOptions={showLineStyleOptions}\n            properties={properties}\n            hideSnapModeCheckbox={hideSnapModeCheckbox}\n            hasBackToMenu={isRightClickMenu}\n            onBackToMenu={closeEditStylePopup}\n          />\n        );\n      case isDatePickerOpen:\n        return (\n          <DatePicker onClick={handleDateChange} annotation={focusedAnnotation} onDatePickerShow={onDatePickerShow} />\n        );\n      case isCalibrationPopupOpen:\n        return <CalibrationPopup annotation={focusedAnnotation} />;\n      case shortCutKeysFor3DVisible && focusedAnnotation instanceof window.Core.Annotations.Model3DAnnotation:\n        return (\n          <div className=\"shortCuts3D\">\n            <div className=\"closeButton\" onClick={() => setShortCutKeysFor3DVisible(false)}>x</div>\n            <div className=\"row\">{t('action.rotate3D')} <span>{t('shortcut.rotate3D')}</span></div>\n            <div className=\"row\">{t('action.zoom')} <span>{t('shortcut.zoom3D')}</span></div>\n          </div>\n        );\n      default:\n        return (\n          <FocusTrap\n            locked={isOpen && isInstanceActive && !isRichTextPopupOpen && !isNotesPanelOpenOrActive && !isLinkModalOpen && !isWarningModalOpen && !isFreeText && !isContextMenuPopupOpen}\n          >\n            <div className=\"container\">\n              <CustomizablePopup\n                dataElement={DataElements.ANNOTATION_POPUP}\n                childrenClassName='main-menu-button'\n              >\n                {showViewFileButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"viewFileButton\"\n                    label={isRightClickMenu ? 'action.viewFile' : ''}\n                    title={!isRightClickMenu ? 'action.viewFile' : ''}\n                    img=\"icon-view\"\n                    onClick={onViewFile}\n                  />\n                )}\n                {showCommentButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationCommentButton\"\n                    label={isRightClickMenu ? commentButtonLabel : ''}\n                    title={!isRightClickMenu ? commentButtonLabel : ''}\n                    img={commentButtonImg}\n                    onClick={onCommentAnnotation}\n                  />\n                )}\n                {showEditStyleButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationStyleEditButton\"\n                    label={isRightClickMenu ? 'action.style' : ''}\n                    title={!isRightClickMenu ? 'action.style' : ''}\n                    img=\"icon-menu-style-line\"\n                    onClick={customizableUI ? openStylePanel : openEditStylePopup}\n                  />\n                )}\n                {showContentEditButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationContentEditButton\"\n                    label={isRightClickMenu ? 'action.edit' : ''}\n                    title={!isRightClickMenu ? 'action.edit' : ''}\n                    img=\"ic_edit_page_24px\"\n                    onClick={onEditContent}\n                  />\n                )}\n                {showClearSignatureButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationClearSignatureButton\"\n                    label={!isReadOnlySignature && isRightClickMenu ? 'action.clearSignature' : ''}\n                    title={isReadOnlySignature ? 'action.readOnlySignature' : (!isRightClickMenu ? 'action.clearSignature' : '')}\n                    img={'icon-delete-line'}\n                    onClick={onClearAppearanceSignature}\n                    isNotClickableSelector={() => isReadOnlySignature}\n                  />\n                )}\n                {showRedactionButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationRedactButton\"\n                    label={isRightClickMenu ? 'action.apply' : ''}\n                    title={!isRightClickMenu ? 'action.apply' : ''}\n                    img=\"ic_check_black_24px\"\n                    onClick={onApplyRedaction}\n                  />\n                )}\n                {showGroupButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationGroupButton\"\n                    label={isRightClickMenu ? 'action.group' : ''}\n                    title={!isRightClickMenu ? 'action.group' : ''}\n                    img=\"group-annotations-icon\"\n                    onClick={onGroupAnnotations}\n                  />\n                )}\n                {showUngroupButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationUngroupButton\"\n                    label={isRightClickMenu ? 'action.ungroup' : ''}\n                    title={!isRightClickMenu ? 'action.ungroup' : ''}\n                    img=\"ungroup-annotations-icon\"\n                    onClick={onUngroupAnnotations}\n                  />\n                )}\n                {multipleAnnotationsSelected && !isMobile && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement='openAlignmentButton'\n                    label={isRightClickMenu ? 'alignment' : ''}\n                    title={!isRightClickMenu ? 'Align' : ''}\n                    img=\"ic-alignment-main\"\n                    onClick={onOpenAlignmentModal}\n                  />\n                )}\n                {showFormFieldButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"formFieldEditButton\"\n                    label={isRightClickMenu ? 'action.formFieldEdit' : ''}\n                    title={!isRightClickMenu ? 'action.formFieldEdit' : ''}\n                    img=\"icon-edit-form-field\"\n                    onClick={onOpenFormField}\n                  />\n                )}\n                {showDeleteButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"annotationDeleteButton\"\n                    label={isRightClickMenu ? 'action.delete' : ''}\n                    title={!isRightClickMenu ? 'action.delete' : ''}\n                    img=\"icon-delete-line\"\n                    onClick={isContentEdit ? openContentEditDeleteWarningModal : onDeleteAnnotation}\n                  />\n                )}\n                {showCalibrateButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement={DataElements.CALIBRATION_POPUP_BUTTON}\n                    label={isRightClickMenu ? 'action.calibrate' : ''}\n                    title={!isRightClickMenu ? 'action.calibrate' : ''}\n                    img=\"calibrate\"\n                    onClick={onOpenCalibration}\n                  />\n                )}\n                {showLinkButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"linkButton\"\n                    label={isRightClickMenu ? 'tool.Link' : ''}\n                    title={!isRightClickMenu ? 'tool.Link' : ''}\n                    img={hasAssociatedLink ? 'icon-tool-unlink' : 'icon-tool-link'}\n                    onClick={linkAnnotationToURL}\n                  />\n                )}\n                {showFileDownloadButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"fileAttachmentDownload\"\n                    label={isRightClickMenu ? 'action.fileAttachmentDownload' : ''}\n                    title={!isRightClickMenu ? 'action.fileAttachmentDownload' : ''}\n                    img=\"icon-download\"\n                    onClick={() => downloadFileAttachment(focusedAnnotation)}\n                  />\n                )}\n                {show3DShortCutButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"shortCutKeysFor3D\"\n                    label={isRightClickMenu ? 'action.viewShortCutKeysFor3D' : ''}\n                    title={!isRightClickMenu ? 'action.viewShortCutKeysFor3D' : ''}\n                    img=\"icon-keyboard\"\n                    onClick={() => setShortCutKeysFor3DVisible(true)}\n                  />\n                )}\n                {showAudioPlayButton && (\n                  <ActionButton\n                    className=\"main-menu-button\"\n                    dataElement=\"playSoundButton\"\n                    label={isRightClickMenu ? 'action.playAudio' : ''}\n                    title={!isRightClickMenu ? 'action.playAudio' : ''}\n                    img=\"ic_play_24px\"\n                    onClick={() => handlePlaySound(focusedAnnotation)}\n                  />\n                )}\n              </CustomizablePopup>\n            </div>\n          </FocusTrap>\n        );\n    }\n  };\n\n  const annotationPopup = (\n    <div\n      className={classNames({\n        Popup: true,\n        AnnotationPopup: true,\n        open: isOpen,\n        closed: !isOpen,\n        stylePopupOpen: isStylePopupOpen,\n        'is-vertical': isRightClickMenu,\n        'is-horizontal': !isRightClickMenu,\n      })}\n      ref={popupRef}\n      data-element={DataElements.ANNOTATION_POPUP}\n      style={{ ...position, visibility: isVisible || isVisible === undefined ? 'visible' : 'hidden' }}\n    >\n      {renderPopup()}\n    </div>\n  );\n\n  return isIE || isMobile ? (\n    annotationPopup\n  ) : (\n    <Draggable cancel=\".Button, .cell, .sliders-container svg, select, button, input\">{annotationPopup}</Draggable>\n  );\n};\n\nAnnotationPopup.propTypes = propTypes;\n\nexport default AnnotationPopup;", "import React, { useState, useEffect, useRef, useCallback, useLayoutEffect } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport debounce from 'lodash/debounce';\nimport { saveAs } from 'file-saver';\n\nimport core from 'core';\nimport { getAnnotationPopupPositionBasedOn } from 'helpers/getPopupPosition';\nimport applyRedactions from 'helpers/applyRedactions';\nimport { isMobile, isIE } from 'helpers/device';\nimport { useTranslation } from 'react-i18next';\nimport { getOpenedWarningModal, getOpenedColorPicker, getDatePicker } from 'helpers/getElements';\nimport getGroupedLinkAnnotations from 'helpers/getGroupedLinkAnnotations';\nimport getHashParameters from 'helpers/getHashParameters';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport { PRIORITY_THREE } from 'constants/actionPriority';\nimport getRootNode from 'helpers/getRootNode';\n\nimport AnnotationPopup from './AnnotationPopup';\n\nimport './AnnotationPopup.scss';\n\nconst { ToolNames } = window.Core.Tools;\nconst { Annotations } = window.Core;\n\nconst propTypes = {\n  focusedAnnotation: PropTypes.object,\n  selectedMultipleAnnotations: PropTypes.bool,\n  canModify: PropTypes.bool,\n  focusedAnnotationStyle: PropTypes.object,\n  isStylePopupOpen: PropTypes.bool,\n  setIsStylePopupOpen: PropTypes.func,\n  isDatePickerOpen: PropTypes.bool,\n  setDatePickerOpen: PropTypes.func,\n  isDatePickerMount: PropTypes.bool,\n  setDatePickerMount: PropTypes.func,\n  hasAssociatedLink: PropTypes.bool,\n  includesFormFieldAnnotation: PropTypes.bool,\n  stylePopupRepositionFlag: PropTypes.bool,\n  setStylePopupRepositionFlag: PropTypes.func,\n  closePopup: PropTypes.func,\n};\n\nconst AnnotationPopupContainer = ({\n  focusedAnnotation,\n  selectedMultipleAnnotations,\n  canModify,\n  focusedAnnotationStyle,\n  isStylePopupOpen,\n  setIsStylePopupOpen,\n  isDatePickerOpen,\n  setDatePickerOpen,\n  isDatePickerMount,\n  setDatePickerMount,\n  hasAssociatedLink,\n  includesFormFieldAnnotation,\n  stylePopupRepositionFlag,\n  setStylePopupRepositionFlag,\n  closePopup,\n}) => {\n  const [\n    isDisabled,\n    isOpen,\n    isContextMenuPopupOpen,\n    isRightClickAnnotationPopupEnabled,\n    isNotesPanelDisabled,\n    isAnnotationStylePopupDisabled,\n    isInlineCommentingDisabled,\n    isNotesPanelOpen,\n    isLinkModalOpen,\n    isWarningModalOpen,\n    isRichTextPopupOpen,\n    isMultiTab,\n    tabManager,\n    tabs,\n    notesInLeftPanel,\n    leftPanelOpen,\n    activeLeftPanel,\n    activeDocumentViewerKey,\n    isAnyCustomPanelOpen,\n    featureFlags,\n    isStylePanelOpen,\n    isStylePanelDisabled,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.ANNOTATION_POPUP),\n      selectors.isElementOpen(state, DataElements.ANNOTATION_POPUP),\n      selectors.isElementOpen(state, DataElements.CONTEXT_MENU_POPUP),\n      selectors.isRightClickAnnotationPopupEnabled(state),\n      selectors.isElementDisabled(state, DataElements.NOTES_PANEL),\n      selectors.isElementDisabled(state, DataElements.ANNOTATION_STYLE_POPUP),\n      selectors.isElementDisabled(state, DataElements.INLINE_COMMENT_POPUP),\n      selectors.isElementOpen(state, DataElements.NOTES_PANEL),\n      selectors.isElementOpen(state, DataElements.LINK_MODAL),\n      selectors.isElementOpen(state, DataElements.WARNING_MODAL),\n      selectors.isElementOpen(state, 'richTextPopup'),\n      selectors.getIsMultiTab(state),\n      selectors.getTabManager(state),\n      selectors.getTabs(state),\n      selectors.getNotesInLeftPanel(state),\n      selectors.isElementOpen(state, DataElements.LEFT_PANEL),\n      selectors.getActiveLeftPanel(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.isAnyCustomPanelOpen(state),\n      selectors.getFeatureFlags(state),\n      selectors.isElementOpen(state, DataElements.STYLE_PANEL),\n      selectors.isElementDisabled(state, DataElements.STYLE_PANEL),\n    ],\n    shallowEqual,\n  );\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const [isVisible, setIsVisible] = useState(false);\n  const [isCalibrationPopupOpen, setCalibrationPopupOpen] = useState(false);\n  const popupRef = useRef();\n\n  const isFocusedAnnotationSelected = isRightClickAnnotationPopupEnabled ? core.isAnnotationSelected(focusedAnnotation, activeDocumentViewerKey) : true;\n  const annotManager = core.getAnnotationManager(activeDocumentViewerKey);\n  const sixtyFramesPerSecondIncrement = 16;\n  // on tablet, the behaviour will be like on desktop, including being draggable\n\n  const { customizableUI } = featureFlags;\n\n  const openStylePanel = () => {\n    if (!isStylePanelOpen && !isStylePanelDisabled) {\n      dispatch(actions.openElement(DataElements.STYLE_PANEL));\n    }\n    closePopup();\n  };\n\n  useOnClickOutside(\n    popupRef,\n    useCallback((e) => {\n      const notesPanel = getRootNode().querySelector(`[data-element=\"${DataElements.NOTES_PANEL}\"]`);\n      const clickedInNotesPanel = notesPanel?.contains(e.target);\n      const clickedInLinkModal = getRootNode().querySelector('.LinkModal.open')?.contains(e.target);\n      const datePicker = getDatePicker();\n      const warningModal = getOpenedWarningModal();\n      const colorPicker = getOpenedColorPicker();\n\n      // the notes panel has mousedown handlers to handle the opening/closing states of this component\n      // we don't want this handler to run when clicked in the notes panel otherwise the opening/closing states may mess up\n      // for example: click on a note will call core.selectAnnotation which triggers the annotationSelected event\n      // and opens this component. If we don't exclude the notes panel this handler will run and close it after\n      if (!clickedInNotesPanel && !clickedInLinkModal && !warningModal && !colorPicker && !datePicker) {\n        if (isRightClickAnnotationPopupEnabled) {\n          closePopup();\n        } else {\n          dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n        }\n      }\n    }, [isRightClickAnnotationPopupEnabled])\n  );\n\n  const setPopupPosition = () => {\n    if (popupRef.current) {\n      setPosition(getAnnotationPopupPositionBasedOn(focusedAnnotation, popupRef, activeDocumentViewerKey));\n    }\n  };\n\n  const handleResize = debounce(() => {\n    if (AnnotationPopupContainer) {\n      setPopupPosition();\n    }\n  }, sixtyFramesPerSecondIncrement, { 'trailing': true, 'leading': false });\n\n  const handleVisibility = debounce(() => {\n    if (AnnotationPopupContainer) {\n      setIsVisible(isFocusedAnnotationSelected);\n    }\n  }, sixtyFramesPerSecondIncrement * 2, { 'trailing': true, 'leading': false });\n\n  useEffect(() => {\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  // useLayoutEffect here to avoid flashing issue when popup is close and open on scroll\n  useLayoutEffect(() => {\n    if (focusedAnnotation || isStylePopupOpen || isDatePickerMount) {\n      handleResize();\n      setIsVisible(false);\n      handleVisibility();\n    }\n    // canModify is needed here because the effect from useOnAnnotationPopupOpen hook will run again and determine which button to show, which in turn change the popup size and will need to recalculate position\n  }, [focusedAnnotation, isStylePopupOpen, isDatePickerMount, canModify, activeDocumentViewerKey]);\n\n  useEffect(() => {\n    if (focusedAnnotation && focusedAnnotation.ToolName === ToolNames.CALIBRATION_MEASUREMENT) {\n      setCalibrationPopupOpen(true);\n    }\n  }, [focusedAnnotation]);\n\n  useEffect(() => {\n    if (isOpen || isRichTextPopupOpen) {\n      dispatch(actions.closeElement(DataElements.INLINE_COMMENT_POPUP));\n    }\n  }, [isOpen, isRichTextPopupOpen]);\n\n  /* VIEW FILE */\n  const wvServer = !!getHashParameters('webviewerServerURL', null);\n  const acceptFormats = wvServer ? window.Core.SupportedFileFormats.SERVER : window.Core.SupportedFileFormats.CLIENT;\n  const showViewFileButton = focusedAnnotation instanceof Annotations.FileAttachmentAnnotation && isMultiTab\n    && acceptFormats.includes(window.Core.mimeTypeToExtension[focusedAnnotation.getFileMetadata().mimeType]);\n\n  const onViewFile = useCallback(async () => {\n    if (!tabManager || !isMultiTab) {\n      return console.warn('Can\\'t open file in non-multi-tab mode');\n    }\n    const metaData = focusedAnnotation.getFileMetadata();\n    const fileAttachmentTab = tabs.find((tab) => tab.options.filename === metaData.filename);\n    if (fileAttachmentTab) { // If already opened once\n      await tabManager.setActiveTab(fileAttachmentTab.id, true);\n      return;\n    }\n    await tabManager.addTab(await focusedAnnotation.getFileData(), {\n      extension: window.Core.mimeTypeToExtension[metaData.mimeType],\n      filename: metaData.filename,\n      saveCurrentActiveTabState: true,\n      setActive: true,\n    });\n  }, [tabManager, focusedAnnotation, tabs, isMultiTab]);\n\n  /* ALL REACT HOOKS NEED TO BE BEFORE RENDERING */\n  if (isDisabled || !focusedAnnotation) {\n    return null;\n  }\n\n  /* COMMENT / DATE */\n  const selectedAnnotations = core.getSelectedAnnotations(activeDocumentViewerKey);\n  const numberOfSelectedAnnotations = selectedAnnotations.length;\n  const multipleAnnotationsSelected = numberOfSelectedAnnotations > 1;\n  const isFreeTextAnnot = focusedAnnotation instanceof Annotations.FreeTextAnnotation;\n  const isDateFreeTextCanEdit = isFreeTextAnnot && !!focusedAnnotation.getDateFormat() && core.canModifyContents(focusedAnnotation, activeDocumentViewerKey);\n  const numberOfGroups = core.getNumberOfGroups(selectedAnnotations, activeDocumentViewerKey);\n  const canGroup = numberOfGroups > 1;\n  const canUngroup = numberOfGroups === 1 && numberOfSelectedAnnotations > 1 && isFocusedAnnotationSelected;\n  const isAppearanceSignature =\n    focusedAnnotation instanceof Annotations.SignatureWidgetAnnotation\n    && focusedAnnotation.isSignedByAppearance();\n  const isInReadOnlyMode = core.getIsReadOnly();\n\n  const showCommentButton = (\n    (!isNotesPanelDisabled || !isInlineCommentingDisabled)\n    && (!multipleAnnotationsSelected || (multipleAnnotationsSelected && !isFocusedAnnotationSelected))\n    && focusedAnnotation.ToolName !== ToolNames.CROP\n    && !includesFormFieldAnnotation\n    && !focusedAnnotation.isContentEditPlaceholder()\n    && !isAppearanceSignature\n  );\n\n  const onCommentAnnotation = () => {\n    if (isDateFreeTextCanEdit) {\n      setDatePickerOpen(true);\n      return;\n    }\n\n    dispatch(actions.closeElement('searchPanel'));\n    dispatch(actions.closeElement('redactionPanel'));\n    const contentEditManager = core.getContentEditManager(activeDocumentViewerKey);\n    if (contentEditManager.isInContentEditMode()) {\n      dispatch(actions.closeElement('textEditingPanel'));\n      contentEditManager.endContentEditMode();\n    }\n\n    dispatch(actions.triggerNoteEditing());\n    if (isInlineCommentingDisabled) {\n      dispatch(actions.openElement(DataElements.NOTES_PANEL));\n      dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n    } else {\n      if (!isNotesPanelOpen) {\n        dispatch(actions.openElement(DataElements.INLINE_COMMENT_POPUP));\n      }\n      closePopup();\n    }\n  };\n\n  const onOpenAlignmentModal = () => {\n    dispatch(actions.openElement(DataElements.ANNOTATION_ALIGNMENT_POPUP));\n    closePopup();\n  };\n\n  const handleDateChange = (text) => {\n    annotManager.setNoteContents(focusedAnnotation, text);\n    annotManager.updateAnnotation(focusedAnnotation);\n  };\n\n  const onDatePickerShow = (isDatePickerShowed) => {\n    setDatePickerMount(isDatePickerShowed);\n  };\n\n  /* EDIT STYLE */\n  const hasStyle = Object.keys(focusedAnnotationStyle).length > 0;\n\n  const toolsWithNoStyling = [\n    ToolNames.CROP,\n    ToolNames.VIDEO_REDACTION,\n    ToolNames.VIDEO_AND_AUDIO_REDACTION,\n    ToolNames.AUDIO_REDACTION,\n  ];\n\n  const showEditStyleButton = (\n    canModify\n    && hasStyle\n    && (!isAnnotationStylePopupDisabled || customizableUI)\n    && (!multipleAnnotationsSelected || canUngroup || (multipleAnnotationsSelected && !isFocusedAnnotationSelected))\n    && !toolsWithNoStyling.includes(focusedAnnotation.ToolName)\n    && !(focusedAnnotation instanceof Annotations.Model3DAnnotation)\n    && !focusedAnnotation.isContentEditPlaceholder()\n    && !isAppearanceSignature\n    && !(focusedAnnotation instanceof Annotations.PushButtonWidgetAnnotation)\n  );\n\n  const hideSnapModeCheckbox = focusedAnnotation instanceof Annotations.EllipseAnnotation || !core.isFullPDFEnabled();\n\n  const onResize = () => {\n    setStylePopupRepositionFlag(!stylePopupRepositionFlag);\n  };\n\n  /* EDIT CONTENT */\n  const showContentEditButton =\n    focusedAnnotation.isContentEditPlaceholder()\n    && focusedAnnotation.getContentEditType() === window.Core.ContentEdit.Types.TEXT;\n\n  const onEditContent = async () => {\n    annotManager.trigger('annotationDoubleClicked', focusedAnnotation);\n\n    dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n  };\n\n  /* CLEAR APPEARANCE SIGNATURE */\n  const onClearAppearanceSignature = () => {\n    focusedAnnotation.clearSignature(annotManager);\n    dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n  };\n\n  /* REDACTION */\n  const redactionEnabled = core.isAnnotationRedactable(focusedAnnotation, activeDocumentViewerKey);\n  const showRedactionButton = redactionEnabled && !multipleAnnotationsSelected && !includesFormFieldAnnotation;\n\n  const onApplyRedaction = () => {\n    dispatch(applyRedactions(focusedAnnotation, undefined, activeDocumentViewerKey));\n    dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n  };\n\n  /* GROUP / UNGROUP */\n  const primaryAnnotation = selectedAnnotations.find((selectedAnnotation) => !selectedAnnotation.InReplyTo);\n\n  const showGroupButton =\n    canModify\n    && isFocusedAnnotationSelected\n    && canGroup\n    && !includesFormFieldAnnotation;\n\n  const onGroupAnnotations = () => {\n    core.groupAnnotations(primaryAnnotation, selectedAnnotations, activeDocumentViewerKey);\n  };\n\n  const showUngroupButton = canUngroup;\n\n  const onUngroupAnnotations = () => {\n    core.ungroupAnnotations(selectedAnnotations, activeDocumentViewerKey);\n  };\n\n  /* FORM FIELD */\n  const formFieldCreationManager = core.getFormFieldCreationManager(activeDocumentViewerKey);\n  const isInFormFieldCreationMode = formFieldCreationManager.isInFormFieldCreationMode();\n  const showFormFieldButton = includesFormFieldAnnotation\n    && isInFormFieldCreationMode\n    && !(focusedAnnotation instanceof Annotations.PushButtonWidgetAnnotation);\n\n  const onOpenFormField = () => {\n    closePopup();\n    // We disable it while the form field popup is open to prevent having both open\n    // at the same time. We re-enable it when the form field popup is closed.\n    if (customizableUI) {\n      dispatch(actions.disableElement(PRIORITY_THREE));\n      dispatch(actions.closeElement(DataElements.FORM_FIELD_EDIT_POPUP));\n    } else {\n      dispatch(actions.disableElement(DataElements.ANNOTATION_POPUP, PRIORITY_THREE));\n    }\n    dispatch(actions.openElement(DataElements.FORM_FIELD_EDIT_POPUP));\n    dispatch(actions.openElement(DataElements.FORM_FIELD_PANEL));\n  };\n\n  /* DELETE ANNOTATION */\n  const showDeleteButton = canModify;\n\n  const openContentEditDeleteWarningModal = () => {\n    const message = t('option.contentEdit.deletionModal.message');\n    const title = t('option.contentEdit.deletionModal.title');\n    const confirmBtnText = t('action.ok');\n\n    const warning = {\n      message,\n      title,\n      confirmBtnText,\n      onConfirm: () => onDeleteAnnotation(),\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const onDeleteAnnotation = () => {\n    if (isFocusedAnnotationSelected) {\n      core.deleteAnnotations(core.getSelectedAnnotations(activeDocumentViewerKey), undefined, activeDocumentViewerKey);\n    } else {\n      core.deleteAnnotations([focusedAnnotation], undefined, activeDocumentViewerKey);\n    }\n    closePopup();\n  };\n\n  /* LINK ANNOTATION */\n  const toolsThatCantHaveLinks = [\n    ToolNames.CROP,\n    ToolNames.SIGNATURE,\n    ToolNames.REDACTION,\n    ToolNames.REDACTION2,\n    ToolNames.REDACTION3,\n    ToolNames.REDACTION4,\n    ToolNames.STICKY,\n    ToolNames.STICKY2,\n    ToolNames.STICKY3,\n    ToolNames.STICKY4,\n  ];\n\n  const showLinkButton = (\n    !toolsThatCantHaveLinks.includes(focusedAnnotation.ToolName)\n    && !includesFormFieldAnnotation\n    && !focusedAnnotation.isContentEditPlaceholder()\n    // TODO(Adam): Update this once SoundAnnotation tool is created.\n    && !(focusedAnnotation instanceof Annotations.SoundAnnotation)\n    && !isAppearanceSignature\n  );\n\n  const showCalibrateButton =\n    canModify\n    && focusedAnnotation.Measure\n    && focusedAnnotation instanceof Annotations.LineAnnotation;\n\n  const onOpenCalibration = () => {\n    dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n    dispatch(actions.openElement(DataElements.CALIBRATION_MODAL));\n  };\n\n  const linkAnnotationToURL = () => {\n    if (hasAssociatedLink) {\n      const annotationsToUnlink = isFocusedAnnotationSelected ? selectedAnnotations : [focusedAnnotation];\n      annotationsToUnlink.forEach((annot) => {\n        const linkAnnotations = getGroupedLinkAnnotations(annot);\n        linkAnnotations.forEach((linkAnnot, index) => {\n          annotManager.ungroupAnnotations([linkAnnot]);\n          if (annot instanceof Annotations.TextHighlightAnnotation && annot.Opacity === 0 && index === 0) {\n            annotManager.deleteAnnotations([annot, linkAnnot], null, true);\n          } else {\n            annotManager.deleteAnnotation(linkAnnot, null, true);\n          }\n        });\n      });\n      closePopup();\n    } else {\n      dispatch(actions.openElement(DataElements.LINK_MODAL));\n    }\n  };\n\n  /* DOWNLOAD FILE ATTACHMENT */\n  const showFileDownloadButton = focusedAnnotation instanceof Annotations.FileAttachmentAnnotation;\n\n  const downloadFileAttachment = async (annot) => {\n    // no need to check that annot is of type file annot as the check is done in the JSX\n    const { fileData, fileName } = await annot.getFullFileMetadata();\n    saveAs(fileData, fileName);\n  };\n\n  /* AUDIO ANNOTATION */\n  const showAudioPlayButton = (\n    !isIE &&\n    !selectedMultipleAnnotations &&\n    focusedAnnotation instanceof Annotations.SoundAnnotation &&\n    focusedAnnotation.hasAudioData()\n  );\n\n  const isNotesPanelOpenOrActive = isNotesPanelOpen\n    || (notesInLeftPanel && leftPanelOpen && activeLeftPanel === 'notesPanel')\n    || isAnyCustomPanelOpen\n    || isInFormFieldCreationMode;\n\n  const handlePlaySound = (annotation) => {\n    dispatch(actions.setActiveSoundAnnotation(annotation));\n    dispatch(actions.triggerResetAudioPlaybackPosition(true));\n    dispatch(actions.openElement('audioPlaybackPopup'));\n  };\n\n  return (\n    <AnnotationPopup\n      isMobile={isMobile()}\n      isIE={isIE}\n      isOpen={isOpen}\n      isRightClickMenu={isRightClickAnnotationPopupEnabled}\n      isNotesPanelOpenOrActive={isNotesPanelOpenOrActive}\n      isRichTextPopupOpen={isRichTextPopupOpen}\n      isLinkModalOpen={isLinkModalOpen}\n      isWarningModalOpen={isWarningModalOpen}\n      isContextMenuPopupOpen={isContextMenuPopupOpen}\n      isVisible={isVisible}\n\n      popupRef={popupRef}\n      position={position}\n      focusedAnnotation={focusedAnnotation}\n      multipleAnnotationsSelected={multipleAnnotationsSelected}\n\n      showViewFileButton={showViewFileButton}\n      onViewFile={onViewFile}\n\n      showCommentButton={showCommentButton}\n      onCommentAnnotation={onCommentAnnotation}\n      isDateFreeTextCanEdit={isDateFreeTextCanEdit}\n      isDatePickerOpen={isDatePickerOpen}\n      handleDateChange={handleDateChange}\n      onDatePickerShow={onDatePickerShow}\n      isCalibrationPopupOpen={isCalibrationPopupOpen}\n\n      showEditStyleButton={showEditStyleButton}\n      isStylePopupOpen={isStylePopupOpen}\n      hideSnapModeCheckbox={hideSnapModeCheckbox}\n      openEditStylePopup={() => setIsStylePopupOpen(true)}\n      closeEditStylePopup={() => setIsStylePopupOpen(false)}\n      annotationStyle={focusedAnnotationStyle}\n      onResize={onResize}\n\n      showContentEditButton={showContentEditButton}\n      onEditContent={onEditContent}\n      openContentEditDeleteWarningModal={openContentEditDeleteWarningModal}\n\n      isAppearanceSignature={isAppearanceSignature}\n      onClearAppearanceSignature={onClearAppearanceSignature}\n\n      showRedactionButton={showRedactionButton}\n      onApplyRedaction={onApplyRedaction}\n\n      showGroupButton={showGroupButton}\n      onGroupAnnotations={onGroupAnnotations}\n      showUngroupButton={showUngroupButton}\n      onUngroupAnnotations={onUngroupAnnotations}\n\n      showFormFieldButton={showFormFieldButton}\n      onOpenFormField={onOpenFormField}\n\n      showDeleteButton={showDeleteButton}\n      onDeleteAnnotation={onDeleteAnnotation}\n\n      showLinkButton={showLinkButton}\n      hasAssociatedLink={hasAssociatedLink}\n      linkAnnotationToURL={linkAnnotationToURL}\n\n      showFileDownloadButton={showFileDownloadButton}\n      downloadFileAttachment={downloadFileAttachment}\n\n      showAudioPlayButton={showAudioPlayButton}\n      handlePlaySound={handlePlaySound}\n\n      showCalibrateButton={showCalibrateButton}\n      onOpenCalibration={onOpenCalibration}\n\n      customizableUI={customizableUI}\n      openStylePanel={openStylePanel}\n      isStylePanelOpen={isStylePanelOpen}\n      isInReadOnlyMode={isInReadOnlyMode}\n\n      onOpenAlignmentModal={onOpenAlignmentModal}\n    />\n  );\n};\n\nAnnotationPopupContainer.propTypes = propTypes;\n\nexport default AnnotationPopupContainer;\n", "import AnnotationPopupContainer from './AnnotationPopupContainer';\n\nexport default AnnotationPopupContainer;"], "sourceRoot": ""}