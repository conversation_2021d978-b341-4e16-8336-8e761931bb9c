/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	/*
 Pikaday

 Copyright © 2014 <PERSON> | BSD & MIT license | https://github.com/Pikaday/Pikaday
*/
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[16],
		{
			625: function (ya, va) {
				!(function (r, la) {
					if ("object" == typeof va) {
						try {
							var na = require("moment");
						} catch (ma) {}
						ya.exports = la(na);
					} else
						"function" == typeof define && define.amd
							? define(function (ma) {
									try {
										na = ma("moment");
									} catch (ja) {}
									return la(na);
								})
							: (r.Pikaday = la(r.moment));
				})(this, function (r) {
					function la(pa) {
						var oa = this,
							ka = oa.config(pa);
						oa._onMouseDown = function (ra) {
							if (oa._v) {
								var qa = (ra = ra || window.event).target || ra.srcElement;
								if (qa)
									if (
										(a(qa, "is-disabled") ||
											(!a(qa, "pika-button") ||
											a(qa, "is-empty") ||
											a(qa.parentNode, "is-disabled")
												? a(qa, "pika-prev")
													? oa.prevMonth()
													: a(qa, "pika-next")
														? oa.nextMonth()
														: a(qa, "pika-set-today") &&
															(oa.setDate(new Date()), oa.hide())
												: (oa.setDate(
														new Date(
															qa.getAttribute("data-pika-year"),
															qa.getAttribute("data-pika-month"),
															qa.getAttribute("data-pika-day"),
														),
													),
													ka.bound &&
														fa(function () {
															oa.hide();
															ka.blurFieldOnSelect &&
																ka.field &&
																ka.field.blur();
														}, 100))),
										a(qa, "pika-select"))
									)
										oa._c = !0;
									else {
										if (!ra.preventDefault) return (ra.returnValue = !1), !1;
										ra.preventDefault();
									}
							}
						};
						oa._onChange = function (ra) {
							var qa = (ra = ra || window.event).target || ra.srcElement;
							qa &&
								(a(qa, "pika-select-month")
									? oa.gotoMonth(qa.value)
									: a(qa, "pika-select-year") && oa.gotoYear(qa.value));
						};
						oa._onKeyChange = function (ra) {
							if (((ra = ra || window.event), oa.isVisible()))
								switch (ra.keyCode) {
									case 13:
									case 27:
										ka.field && ka.field.blur();
										break;
									case 37:
										oa.adjustDate("subtract", 1);
										break;
									case 38:
										oa.adjustDate("subtract", 7);
										break;
									case 39:
										oa.adjustDate("add", 1);
										break;
									case 40:
										oa.adjustDate("add", 7);
										break;
									case 8:
									case 46:
										oa.setDate(null);
								}
						};
						oa._parseFieldValue = function () {
							if (ka.parse) return ka.parse(ka.field.value, ka.format);
							if (aa) {
								var ra = r(ka.field.value, ka.format, ka.formatStrict);
								return ra && ra.isValid() ? ra.toDate() : null;
							}
							return new Date(Date.parse(ka.field.value));
						};
						oa._onInputChange = function (ra) {
							var qa;
							ra.firedBy !== oa &&
								((qa = oa._parseFieldValue()),
								h(qa) && oa.setDate(qa),
								oa._v || oa.show());
						};
						oa._onInputFocus = function () {
							oa.show();
						};
						oa._onInputClick = function () {
							oa.show();
						};
						oa._onInputBlur = function () {
							var ra = ba.activeElement;
							do if (a(ra, "pika-single")) return;
							while ((ra = ra.parentNode));
							oa._c ||
								(oa._b = fa(function () {
									oa.hide();
								}, 50));
							oa._c = !1;
						};
						oa._onClick = function (ra) {
							var qa = (ra = ra || window.event).target || ra.srcElement;
							if ((ra = qa)) {
								!ea &&
									a(qa, "pika-select") &&
									(qa.onchange ||
										(qa.setAttribute("onchange", "return;"),
										z(qa, "change", oa._onChange)));
								do if (a(ra, "pika-single") || ra === ka.trigger) return;
								while ((ra = ra.parentNode));
								oa._v && qa !== ka.trigger && ra !== ka.trigger && oa.hide();
							}
						};
						oa.el = ba.createElement("div");
						oa.el.className =
							"pika-single" +
							(ka.isRTL ? " is-rtl" : "") +
							(ka.theme ? " " + ka.theme : "");
						z(oa.el, "mousedown", oa._onMouseDown, !0);
						z(oa.el, "touchend", oa._onMouseDown, !0);
						z(oa.el, "change", oa._onChange);
						ka.keyboardInput && z(ba, "keydown", oa._onKeyChange);
						ka.field &&
							(ka.container
								? ka.container.appendChild(oa.el)
								: ka.bound
									? ba.body.appendChild(oa.el)
									: ka.field.parentNode.insertBefore(
											oa.el,
											ka.field.nextSibling,
										),
							z(ka.field, "change", oa._onInputChange),
							ka.defaultDate ||
								((ka.defaultDate = oa._parseFieldValue()),
								(ka.setDefaultDate = !0)));
						pa = ka.defaultDate;
						h(pa)
							? ka.setDefaultDate
								? oa.setDate(pa, !0)
								: oa.gotoDate(pa)
							: oa.gotoDate(new Date());
						ka.bound
							? (this.hide(),
								(oa.el.className += " is-bound"),
								z(ka.trigger, "click", oa._onInputClick),
								z(ka.trigger, "focus", oa._onInputFocus),
								z(ka.trigger, "blur", oa._onInputBlur))
							: this.show();
					}
					function na(pa, oa, ka) {
						return (
							'<table cellpadding="0" cellspacing="0" class="pika-table" role="grid" aria-labelledby="' +
							ka +
							'">' +
							(function (ra) {
								var qa,
									sa = [];
								ra.showWeekNumber && sa.push("<th></th>");
								for (qa = 0; 7 > qa; qa++)
									sa.push(
										'<th scope="col"><abbr title="' +
											ja(ra, qa) +
											'">' +
											ja(ra, qa, !0) +
											"</abbr></th>",
									);
								return (
									"<thead><tr>" +
									(ra.isRTL ? sa.reverse() : sa).join("") +
									"</tr></thead>"
								);
							})(pa) +
							("<tbody>" + oa.join("") + "</tbody>") +
							(pa.showTodayButton
								? (function (ra) {
										var qa = [];
										return (
											qa.push(
												'<td colspan="' +
													(ra.showWeekNumber ? "8" : "7") +
													'"><button class="pika-set-today">' +
													ra.i18n.today +
													"</button></td>",
											),
											"<tfoot>" +
												(ra.isRTL ? qa.reverse() : qa).join("") +
												"</tfoot>"
										);
									})(pa)
								: "") +
							"</table>"
						);
					}
					function ma(pa, oa, ka, ra, qa, sa) {
						var ta,
							wa,
							za = pa._o,
							Aa = ka === za.minYear,
							Ea = ka === za.maxYear,
							Ga =
								'<div id="' +
								sa +
								'" class="pika-title" role="heading" aria-live="assertive">',
							Ca = !0,
							La = !0;
						var Ka = [];
						for (sa = 0; 12 > sa; sa++)
							Ka.push(
								'<option value="' +
									(ka === qa ? sa - oa : 12 + sa - oa) +
									'"' +
									(sa === ra ? ' selected="selected"' : "") +
									((Aa && sa < za.minMonth) || (Ea && sa > za.maxMonth)
										? ' disabled="disabled"'
										: "") +
									">" +
									za.i18n.months[sa] +
									"</option>",
							);
						qa =
							'<div class="pika-label">' +
							za.i18n.months[ra] +
							'<select class="pika-select pika-select-month" tabindex="-1">' +
							Ka.join("") +
							"</select></div>";
						b(za.yearRange)
							? ((sa = za.yearRange[0]), (ta = za.yearRange[1] + 1))
							: ((sa = ka - za.yearRange), (ta = 1 + ka + za.yearRange));
						for (Ka = []; sa < ta && sa <= za.maxYear; sa++)
							sa >= za.minYear &&
								Ka.push(
									'<option value="' +
										sa +
										'"' +
										(sa === ka ? ' selected="selected"' : "") +
										">" +
										sa +
										"</option>",
								);
						return (
							(wa =
								'<div class="pika-label">' +
								ka +
								za.yearSuffix +
								'<select class="pika-select pika-select-year" tabindex="-1">' +
								Ka.join("") +
								"</select></div>"),
							za.showMonthAfterYear ? (Ga += wa + qa) : (Ga += qa + wa),
							Aa && (0 === ra || za.minMonth >= ra) && (Ca = !1),
							Ea && (11 === ra || za.maxMonth <= ra) && (La = !1),
							0 === oa &&
								(Ga +=
									'<button class="pika-prev' +
									(Ca ? "" : " is-disabled") +
									'" type="button">' +
									za.i18n.previousMonth +
									"</button>"),
							oa === pa._o.numberOfMonths - 1 &&
								(Ga +=
									'<button class="pika-next' +
									(La ? "" : " is-disabled") +
									'" type="button">' +
									za.i18n.nextMonth +
									"</button>"),
							Ga + "</div>"
						);
					}
					function ja(pa, oa, ka) {
						for (oa += pa.firstDay; 7 <= oa; ) oa -= 7;
						return ka ? pa.i18n.weekdaysShort[oa] : pa.i18n.weekdays[oa];
					}
					function ia(pa) {
						return (
							0 > pa.month &&
								((pa.year -= Math.ceil(Math.abs(pa.month) / 12)),
								(pa.month += 12)),
							11 < pa.month &&
								((pa.year += Math.floor(Math.abs(pa.month) / 12)),
								(pa.month -= 12)),
							pa
						);
					}
					function ca(pa, oa, ka) {
						var ra;
						ba.createEvent
							? ((ra = ba.createEvent("HTMLEvents")).initEvent(oa, !0, !1),
								(ra = y(ra, ka)),
								pa.dispatchEvent(ra))
							: ba.createEventObject &&
								((ra = ba.createEventObject()),
								(ra = y(ra, ka)),
								pa.fireEvent("on" + oa, ra));
					}
					function y(pa, oa, ka) {
						var ra, qa;
						for (ra in oa)
							(qa = void 0 !== pa[ra]) &&
							"object" == typeof oa[ra] &&
							null !== oa[ra] &&
							void 0 === oa[ra].nodeName
								? h(oa[ra])
									? ka && (pa[ra] = new Date(oa[ra].getTime()))
									: b(oa[ra])
										? ka && (pa[ra] = oa[ra].slice(0))
										: (pa[ra] = y({}, oa[ra], ka))
								: (!ka && qa) || (pa[ra] = oa[ra]);
						return pa;
					}
					function x(pa) {
						h(pa) && pa.setHours(0, 0, 0, 0);
					}
					function n(pa, oa) {
						return [
							31,
							(0 == pa % 4 && 0 != pa % 100) || 0 == pa % 400 ? 29 : 28,
							31,
							30,
							31,
							30,
							31,
							31,
							30,
							31,
							30,
							31,
						][oa];
					}
					function h(pa) {
						return (
							/Date/.test(Object.prototype.toString.call(pa)) &&
							!isNaN(pa.getTime())
						);
					}
					function b(pa) {
						return /Array/.test(Object.prototype.toString.call(pa));
					}
					function e(pa, oa) {
						var ka;
						pa.className = (ka = (" " + pa.className + " ").replace(
							" " + oa + " ",
							" ",
						)).trim
							? ka.trim()
							: ka.replace(/^\s+|\s+$/g, "");
					}
					function f(pa, oa) {
						a(pa, oa) ||
							(pa.className =
								"" === pa.className ? oa : pa.className + " " + oa);
					}
					function a(pa, oa) {
						return -1 !== (" " + pa.className + " ").indexOf(" " + oa + " ");
					}
					function w(pa, oa, ka, ra) {
						ea
							? pa.removeEventListener(oa, ka, !!ra)
							: pa.detachEvent("on" + oa, ka);
					}
					function z(pa, oa, ka, ra) {
						ea
							? pa.addEventListener(oa, ka, !!ra)
							: pa.attachEvent("on" + oa, ka);
					}
					var aa = "function" == typeof r,
						ea = !!window.addEventListener,
						ba = window.document,
						fa = window.setTimeout,
						ha = {
							field: null,
							bound: void 0,
							ariaLabel: "Use the arrow keys to pick a date",
							position: "bottom left",
							reposition: !0,
							format: "YYYY-MM-DD",
							toString: null,
							parse: null,
							defaultDate: null,
							setDefaultDate: !1,
							firstDay: 0,
							firstWeekOfYearMinDays: 4,
							formatStrict: !1,
							minDate: null,
							maxDate: null,
							yearRange: 10,
							showWeekNumber: !1,
							showTodayButton: !1,
							pickWholeWeek: !1,
							minYear: 0,
							maxYear: 9999,
							minMonth: void 0,
							maxMonth: void 0,
							startRange: null,
							endRange: null,
							isRTL: !1,
							yearSuffix: "",
							showMonthAfterYear: !1,
							showDaysInNextAndPreviousMonths: !1,
							enableSelectionDaysInNextAndPreviousMonths: !1,
							numberOfMonths: 1,
							mainCalendar: "left",
							container: void 0,
							blurFieldOnSelect: !0,
							i18n: {
								previousMonth: "Previous Month",
								nextMonth: "Next Month",
								today: "Today",
								months:
									"January February March April May June July August September October November December".split(
										" ",
									),
								weekdays:
									"Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(
										" ",
									),
								weekdaysShort: "Sun Mon Tue Wed Thu Fri Sat".split(" "),
							},
							theme: null,
							events: [],
							onSelect: null,
							onOpen: null,
							onClose: null,
							onDraw: null,
							keyboardInput: !0,
						};
					return (
						(la.prototype = {
							config: function (pa) {
								this._o || (this._o = y({}, ha, !0));
								pa = y(this._o, pa, !0);
								pa.isRTL = !!pa.isRTL;
								pa.field = pa.field && pa.field.nodeName ? pa.field : null;
								pa.theme =
									"string" == typeof pa.theme && pa.theme ? pa.theme : null;
								pa.bound = !!(void 0 !== pa.bound
									? pa.field && pa.bound
									: pa.field);
								pa.trigger =
									pa.trigger && pa.trigger.nodeName ? pa.trigger : pa.field;
								pa.disableWeekends = !!pa.disableWeekends;
								pa.disableDayFn =
									"function" == typeof pa.disableDayFn ? pa.disableDayFn : null;
								var oa = parseInt(pa.numberOfMonths, 10) || 1;
								((pa.numberOfMonths = 4 < oa ? 4 : oa),
								h(pa.minDate) || (pa.minDate = !1),
								h(pa.maxDate) || (pa.maxDate = !1),
								pa.minDate &&
									pa.maxDate &&
									pa.maxDate < pa.minDate &&
									(pa.maxDate = pa.minDate = !1),
								pa.minDate && this.setMinDate(pa.minDate),
								pa.maxDate && this.setMaxDate(pa.maxDate),
								b(pa.yearRange))
									? ((oa = new Date().getFullYear() - 10),
										(pa.yearRange[0] = parseInt(pa.yearRange[0], 10) || oa),
										(pa.yearRange[1] = parseInt(pa.yearRange[1], 10) || oa))
									: ((pa.yearRange =
											Math.abs(parseInt(pa.yearRange, 10)) || ha.yearRange),
										100 < pa.yearRange && (pa.yearRange = 100));
								return pa;
							},
							toString: function (pa) {
								return (
									(pa = pa || this._o.format),
									h(this._d)
										? this._o.toString
											? this._o.toString(this._d, pa)
											: aa
												? r(this._d).format(pa)
												: this._d.toDateString()
										: ""
								);
							},
							getMoment: function () {
								return aa ? r(this._d) : null;
							},
							setMoment: function (pa, oa) {
								aa && r.isMoment(pa) && this.setDate(pa.toDate(), oa);
							},
							getDate: function () {
								return h(this._d) ? new Date(this._d.getTime()) : null;
							},
							setDate: function (pa, oa) {
								if (!pa)
									return (
										(this._d = null),
										this._o.field &&
											((this._o.field.value = ""),
											ca(this._o.field, "change", { firedBy: this })),
										this.draw()
									);
								if (
									("string" == typeof pa && (pa = new Date(Date.parse(pa))),
									h(pa))
								) {
									var ka = this._o.minDate,
										ra = this._o.maxDate;
									h(ka) && pa < ka ? (pa = ka) : h(ra) && pa > ra && (pa = ra);
									this._d = new Date(pa.getTime());
									this.gotoDate(this._d);
									this._o.field &&
										((this._o.field.value = this.toString()),
										ca(this._o.field, "change", { firedBy: this }));
									oa ||
										"function" != typeof this._o.onSelect ||
										this._o.onSelect.call(this, this.getDate());
								}
							},
							clear: function () {
								this.setDate(null);
							},
							gotoDate: function (pa) {
								var oa = !0;
								if (h(pa)) {
									if (this.calendars) {
										oa = new Date(
											this.calendars[0].year,
											this.calendars[0].month,
											1,
										);
										var ka = new Date(
												this.calendars[this.calendars.length - 1].year,
												this.calendars[this.calendars.length - 1].month,
												1,
											),
											ra = pa.getTime();
										ka.setMonth(ka.getMonth() + 1);
										ka.setDate(ka.getDate() - 1);
										oa = ra < oa.getTime() || ka.getTime() < ra;
									}
									oa &&
										((this.calendars = [
											{ month: pa.getMonth(), year: pa.getFullYear() },
										]),
										"right" === this._o.mainCalendar &&
											(this.calendars[0].month += 1 - this._o.numberOfMonths));
									this.adjustCalendars();
								}
							},
							adjustDate: function (pa, oa) {
								var ka,
									ra = this.getDate() || new Date();
								oa = 864e5 * parseInt(oa);
								"add" === pa
									? (ka = new Date(ra.valueOf() + oa))
									: "subtract" === pa && (ka = new Date(ra.valueOf() - oa));
								this.setDate(ka);
							},
							adjustCalendars: function () {
								this.calendars[0] = ia(this.calendars[0]);
								for (var pa = 1; pa < this._o.numberOfMonths; pa++)
									this.calendars[pa] = ia({
										month: this.calendars[0].month + pa,
										year: this.calendars[0].year,
									});
								this.draw();
							},
							gotoToday: function () {
								this.gotoDate(new Date());
							},
							gotoMonth: function (pa) {
								isNaN(pa) ||
									((this.calendars[0].month = parseInt(pa, 10)),
									this.adjustCalendars());
							},
							nextMonth: function () {
								this.calendars[0].month++;
								this.adjustCalendars();
							},
							prevMonth: function () {
								this.calendars[0].month--;
								this.adjustCalendars();
							},
							gotoYear: function (pa) {
								isNaN(pa) ||
									((this.calendars[0].year = parseInt(pa, 10)),
									this.adjustCalendars());
							},
							setMinDate: function (pa) {
								pa instanceof Date
									? (x(pa),
										(this._o.minDate = pa),
										(this._o.minYear = pa.getFullYear()),
										(this._o.minMonth = pa.getMonth()))
									: ((this._o.minDate = ha.minDate),
										(this._o.minYear = ha.minYear),
										(this._o.minMonth = ha.minMonth),
										(this._o.startRange = ha.startRange));
								this.draw();
							},
							setMaxDate: function (pa) {
								pa instanceof Date
									? (x(pa),
										(this._o.maxDate = pa),
										(this._o.maxYear = pa.getFullYear()),
										(this._o.maxMonth = pa.getMonth()))
									: ((this._o.maxDate = ha.maxDate),
										(this._o.maxYear = ha.maxYear),
										(this._o.maxMonth = ha.maxMonth),
										(this._o.endRange = ha.endRange));
								this.draw();
							},
							setStartRange: function (pa) {
								this._o.startRange = pa;
							},
							setEndRange: function (pa) {
								this._o.endRange = pa;
							},
							draw: function (pa) {
								if (this._v || pa) {
									var oa = this._o;
									var ka = oa.minYear;
									var ra = oa.maxYear,
										qa = oa.minMonth,
										sa = oa.maxMonth;
									pa = "";
									this._y <= ka &&
										((this._y = ka),
										!isNaN(qa) && this._m < qa && (this._m = qa));
									this._y >= ra &&
										((this._y = ra),
										!isNaN(sa) && this._m > sa && (this._m = sa));
									for (ra = 0; ra < oa.numberOfMonths; ra++)
										(ka =
											"pika-title-" +
											Math.random()
												.toString(36)
												.replace(/[^a-z]+/g, "")
												.substr(0, 2)),
											(pa +=
												'<div class="pika-lendar">' +
												ma(
													this,
													ra,
													this.calendars[ra].year,
													this.calendars[ra].month,
													this.calendars[0].year,
													ka,
												) +
												this.render(
													this.calendars[ra].year,
													this.calendars[ra].month,
													ka,
												) +
												"</div>");
									this.el.innerHTML = pa;
									oa.bound &&
										"hidden" !== oa.field.type &&
										fa(function () {
											oa.trigger.focus();
										}, 1);
									"function" == typeof this._o.onDraw && this._o.onDraw(this);
									oa.bound && oa.field.setAttribute("aria-label", oa.ariaLabel);
								}
							},
							adjustPosition: function () {
								var pa, oa, ka, ra, qa, sa, ta, wa, za;
								if (!this._o.container) {
									if (
										((this.el.style.position = "absolute"),
										(oa = pa = this._o.trigger),
										(ka = this.el.offsetWidth),
										(ra = this.el.offsetHeight),
										(qa = window.innerWidth || ba.documentElement.clientWidth),
										(sa =
											window.innerHeight || ba.documentElement.clientHeight),
										(ta =
											window.pageYOffset ||
											ba.body.scrollTop ||
											ba.documentElement.scrollTop),
										(wa = !0),
										(za = !0),
										"function" == typeof pa.getBoundingClientRect)
									) {
										var Aa =
											(oa = pa.getBoundingClientRect()).left +
											window.pageXOffset;
										var Ea = oa.bottom + window.pageYOffset;
									} else
										for (
											Aa = oa.offsetLeft, Ea = oa.offsetTop + oa.offsetHeight;
											(oa = oa.offsetParent);
										)
											(Aa += oa.offsetLeft), (Ea += oa.offsetTop);
									((this._o.reposition && Aa + ka > qa) ||
										(-1 < this._o.position.indexOf("right") &&
											0 < Aa - ka + pa.offsetWidth)) &&
										((Aa = Aa - ka + pa.offsetWidth), (wa = !1));
									((this._o.reposition && Ea + ra > sa + ta) ||
										(-1 < this._o.position.indexOf("top") &&
											0 < Ea - ra - pa.offsetHeight)) &&
										((Ea = Ea - ra - pa.offsetHeight), (za = !1));
									0 > Aa && (Aa = 0);
									0 > Ea && (Ea = 0);
									this.el.style.left = Aa + "px";
									this.el.style.top = Ea + "px";
									f(this.el, wa ? "left-aligned" : "right-aligned");
									f(this.el, za ? "bottom-aligned" : "top-aligned");
									e(this.el, wa ? "right-aligned" : "left-aligned");
									e(this.el, za ? "top-aligned" : "bottom-aligned");
								}
							},
							render: function (pa, oa, ka) {
								var ra = this._o,
									qa = new Date(),
									sa = n(pa, oa),
									ta = new Date(pa, oa, 1).getDay(),
									wa = [],
									za = [];
								x(qa);
								0 < ra.firstDay && 0 > (ta -= ra.firstDay) && (ta += 7);
								for (
									var Aa = 0 === oa ? 11 : oa - 1,
										Ea = 11 === oa ? 0 : oa + 1,
										Ga = 0 === oa ? pa - 1 : pa,
										Ca = 11 === oa ? pa + 1 : pa,
										La = n(Ga, Aa),
										Ka = sa + ta,
										Ia = Ka;
									7 < Ia;
								)
									Ia -= 7;
								Ka += 7 - Ia;
								for (var Qa = !1, Sa = (Ia = 0); Ia < Ka; Ia++) {
									var Oa = new Date(pa, oa, Ia - ta + 1),
										Wa = !!h(this._d) && Oa.getTime() === this._d.getTime(),
										db = Oa.getTime() === qa.getTime(),
										Ma = -1 !== ra.events.indexOf(Oa.toDateString()),
										Pa = Ia < ta || Ia >= sa + ta,
										Ha = Ia - ta + 1,
										Za = oa,
										Xa = pa,
										ab =
											ra.startRange && ra.startRange.getTime() === Oa.getTime(),
										cb = ra.endRange && ra.endRange.getTime() === Oa.getTime(),
										bb =
											ra.startRange &&
											ra.endRange &&
											ra.startRange < Oa &&
											Oa < ra.endRange;
									Pa &&
										(Ia < ta
											? ((Ha = La + Ha), (Za = Aa), (Xa = Ga))
											: ((Ha -= sa), (Za = Ea), (Xa = Ca)));
									var pb = Wa,
										tb;
									!(tb =
										(ra.minDate && Oa < ra.minDate) ||
										(ra.maxDate && Oa > ra.maxDate)) &&
										(tb = ra.disableWeekends) &&
										((tb = Oa.getDay()), (tb = 0 === tb || 6 === tb));
									Pa = {
										day: Ha,
										month: Za,
										year: Xa,
										hasEvent: Ma,
										isSelected: pb,
										isToday: db,
										isDisabled: tb || (ra.disableDayFn && ra.disableDayFn(Oa)),
										isEmpty: Pa,
										isStartRange: ab,
										isEndRange: cb,
										isInRange: bb,
										showDaysInNextAndPreviousMonths:
											ra.showDaysInNextAndPreviousMonths,
										enableSelectionDaysInNextAndPreviousMonths:
											ra.enableSelectionDaysInNextAndPreviousMonths,
									};
									ra.pickWholeWeek && Wa && (Qa = !0);
									Wa = za;
									Oa = Wa.push;
									a: {
										ab = Pa;
										cb = [];
										bb = "false";
										if (ab.isEmpty) {
											if (!ab.showDaysInNextAndPreviousMonths) {
												Pa = '<td class="is-empty"></td>';
												break a;
											}
											cb.push("is-outside-current-month");
											ab.enableSelectionDaysInNextAndPreviousMonths ||
												cb.push("is-selection-disabled");
										}
										Pa =
											(ab.isDisabled && cb.push("is-disabled"),
											ab.isToday && cb.push("is-today"),
											ab.isSelected && (cb.push("is-selected"), (bb = "true")),
											ab.hasEvent && cb.push("has-event"),
											ab.isInRange && cb.push("is-inrange"),
											ab.isStartRange && cb.push("is-startrange"),
											ab.isEndRange && cb.push("is-endrange"),
											'<td data-day="' +
												ab.day +
												'" class="' +
												cb.join(" ") +
												'" aria-selected="' +
												bb +
												'"><button class="pika-button pika-day" type="button" data-pika-year="' +
												ab.year +
												'" data-pika-month="' +
												ab.month +
												'" data-pika-day="' +
												ab.day +
												'">' +
												ab.day +
												"</button></td>");
									}
									Oa.call(Wa, Pa);
									7 == ++Sa &&
										(ra.showWeekNumber &&
											((Sa = za),
											(Wa = Sa.unshift),
											(ab = ra.firstWeekOfYearMinDays),
											(Oa = new Date(pa, oa, Ia - ta)),
											aa
												? (Oa = r(Oa).isoWeek())
												: (Oa.setHours(0, 0, 0, 0),
													(cb = Oa.getDate()),
													(Pa = ab - 1),
													Oa.setDate(cb + Pa - ((Oa.getDay() + 7 - 1) % 7)),
													(ab = new Date(Oa.getFullYear(), 0, ab)),
													(Oa =
														1 +
														Math.round(
															((Oa.getTime() - ab.getTime()) / 864e5 -
																Pa +
																((ab.getDay() + 7 - 1) % 7)) /
																7,
														))),
											Wa.call(Sa, '<td class="pika-week">' + Oa + "</td>")),
										(Sa = wa),
										(Wa = Sa.push),
										(za =
											'<tr class="pika-row' +
											(ra.pickWholeWeek ? " pick-whole-week" : "") +
											(Qa ? " is-selected" : "") +
											'">' +
											(ra.isRTL ? za.reverse() : za).join("") +
											"</tr>"),
										Wa.call(Sa, za),
										(za = []),
										(Sa = 0),
										(Qa = !1));
								}
								return na(ra, wa, ka);
							},
							isVisible: function () {
								return this._v;
							},
							show: function () {
								this.isVisible() ||
									((this._v = !0),
									this.draw(),
									e(this.el, "is-hidden"),
									this._o.bound &&
										(z(ba, "click", this._onClick), this.adjustPosition()),
									"function" == typeof this._o.onOpen &&
										this._o.onOpen.call(this));
							},
							hide: function () {
								var pa = this._v;
								!1 !== pa &&
									(this._o.bound && w(ba, "click", this._onClick),
									this._o.container ||
										((this.el.style.position = "static"),
										(this.el.style.left = "auto"),
										(this.el.style.top = "auto")),
									f(this.el, "is-hidden"),
									(this._v = !1),
									void 0 !== pa &&
										"function" == typeof this._o.onClose &&
										this._o.onClose.call(this));
							},
							destroy: function () {
								var pa = this._o;
								this.hide();
								w(this.el, "mousedown", this._onMouseDown, !0);
								w(this.el, "touchend", this._onMouseDown, !0);
								w(this.el, "change", this._onChange);
								pa.keyboardInput && w(ba, "keydown", this._onKeyChange);
								pa.field &&
									(w(pa.field, "change", this._onInputChange),
									pa.bound &&
										(w(pa.trigger, "click", this._onInputClick),
										w(pa.trigger, "focus", this._onInputFocus),
										w(pa.trigger, "blur", this._onInputBlur)));
								this.el.parentNode && this.el.parentNode.removeChild(this.el);
							},
						}),
						la
					);
				});
			},
		},
	]);
}).call(this || window);
