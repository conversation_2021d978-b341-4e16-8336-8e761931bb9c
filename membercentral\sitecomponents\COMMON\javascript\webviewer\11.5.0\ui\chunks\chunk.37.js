(window.webpackJsonp = window.webpackJsonp || []).push([
	[37],
	{
		1916: function (t, e, o) {
			var n = o(30),
				i = o(1917);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						if (0 === n)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(i, a);
			t.exports = i.locals || {};
		},
		1917: function (t, e, o) {
			(t.exports = o(31)(!1)).push([
				t.i,
				".language-dropdown.Dropdown{border:1px solid var(--gray-6)}.language-dropdown .Dropdown__items{left:0;width:336px}.theme-options{width:336px;height:160px;display:flex;justify-content:space-between}.theme-options .theme-option{width:160px;height:160px;display:flex;flex-direction:column}.theme-options .theme-option .Icon{width:160px;height:120px}.theme-options .theme-option .Icon.light-mode-icon{color:#fff}.theme-options .theme-option .Icon.dark-mode-icon{color:#000}.theme-options .theme-option .Icon svg{border:1px solid;border-color:var(--border);border-top-left-radius:4px;border-top-right-radius:4px}.theme-options .theme-option .theme-choice{height:100%;border:1px solid;border-color:var(--border);border-top:0;border-bottom-left-radius:4px;border-bottom-right-radius:4px;display:flex;padding-left:12px}.theme-options .theme-option.active-theme .Icon svg,.theme-options .theme-option.active-theme .theme-choice{border-color:var(--blue-5)}",
				"",
			]);
		},
		1918: function (t, e, o) {
			var n = o(30),
				i = o(1919);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						if (0 === n)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(i, a);
			t.exports = i.locals || {};
		},
		1919: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				".open.EditKeyboardShortcutModal.Modal{visibility:visible}.closed.EditKeyboardShortcutModal.Modal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel:hover,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel.disabled,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.EditKeyboardShortcutModal.Modal .footer .modal-button.cancel.disabled span,.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.EditKeyboardShortcutModal.Modal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.EditKeyboardShortcutModal.Modal .modal-container .wrapper .modal-content{padding:10px}.EditKeyboardShortcutModal.Modal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.EditKeyboardShortcutModal.Modal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.EditKeyboardShortcutModal.Modal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.EditKeyboardShortcutModal.Modal .footer .modal-button.confirm{margin-left:4px}.EditKeyboardShortcutModal.Modal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .footer .modal-button{padding:23px 8px}}.EditKeyboardShortcutModal.Modal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .swipe-indicator{width:32px}}.EditKeyboardShortcutModal.Modal .modal-container{border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App:not(.is-web-component) .modal-container{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App.is-web-component .modal-container{overflow:auto;max-height:100%}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App:not(.is-in-desktop-only-mode):not(.is-web-component) .modal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .EditKeyboardShortcutModal.Modal .modal-container,.EditKeyboardShortcutModal.Modal .App.is-web-component:not(.is-in-desktop-only-mode) .modal-container{width:100%;position:fixed;left:0;bottom:0;padding-top:4px;min-width:100px}}.EditKeyboardShortcutModal.Modal .modal-container{display:flex;flex-direction:column;width:480px;height:320px}.EditKeyboardShortcutModal.Modal .modal-container .header{display:flex;flex-direction:row;justify-content:space-between;padding:16px;font-size:16px;font-weight:700;align-items:center}.EditKeyboardShortcutModal.Modal .modal-container .body{height:100%;display:flex;flex-direction:column;padding:16px;position:relative}.EditKeyboardShortcutModal.Modal .modal-container .body .press-key-note{font-weight:700}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut{height:100%;color:var(--gray-7);display:flex;align-items:center;justify-content:center;background:var(--gray-2);border-radius:4px}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut span:not(:last-child){margin-right:8px}.EditKeyboardShortcutModal.Modal .modal-container .body .keyboard-shortcut.recording{outline:var(--focus-visible-outline)}.EditKeyboardShortcutModal.Modal .modal-container .body .conflict-warning{position:absolute;bottom:50px;font-size:13px;color:var(--red);width:calc(100% - 32px);display:flex;justify-content:center}.EditKeyboardShortcutModal.Modal .modal-container .divider{height:1px;width:100%;background:var(--divider)}.EditKeyboardShortcutModal.Modal .modal-container .footer{padding:16px;margin-top:0;grid-gap:8px;gap:8px}.EditKeyboardShortcutModal.Modal .modal-container .footer .modal-button{height:32px;margin:0;font-weight:unset}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1920: function (t, e, o) {
			var n = o(30),
				i = o(1921);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						if (0 === n)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(i, a);
			t.exports = i.locals || {};
		},
		1921: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.shortcut-table-header{border:1px solid var(--gray-4);border-bottom:0;padding:12px 16px;display:flex;font-weight:700;position:relative}.shortcut-table-header .shortcut-table-header-command{width:220px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-header .shortcut-table-header-command{width:180px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-header .shortcut-table-header-command{width:180px}}.shortcut-table-header .shortcut-table-header-action{position:absolute;right:16px}.shortcut-table-content{border:1px solid var(--gray-4);padding:0 16px;display:flex;flex-direction:column}.shortcut-table-content .shortcut-table-item{display:flex;align-items:center;height:40px;position:relative}.shortcut-table-content .shortcut-table-item .shortcut-table-item-command{color:var(--gray-7);width:220px}.shortcut-table-content .shortcut-table-item .shortcut-table-item-command span:not(:last-child){margin-right:8px}.shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:380px}.shortcut-table-content .shortcut-table-item button{position:absolute;right:0;height:32px;width:32px}.shortcut-table-content .shortcut-table-item button .Icon{height:20px;width:20px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-content .shortcut-table-item .shortcut-table-item-command{width:180px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:340px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-content .shortcut-table-item .shortcut-table-item-command{width:180px}.App.is-web-component:not(.is-in-desktop-only-mode) .shortcut-table-content .shortcut-table-item .shortcut-table-item-description{width:340px}}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1922: function (t, e, o) {
			var n = o(30),
				i = o(1923);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						if (0 === n)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(i, a);
			t.exports = i.locals || {};
		},
		1923: function (t, e, o) {
			(t.exports = o(31)(!1)).push([
				t.i,
				".setting-item{border:1px solid var(--gray-4);padding:16px;display:flex;align-items:flex-start;justify-content:space-between}.setting-item:not(:last-child){border-bottom:0}.setting-item .setting-item-info{display:flex;flex-direction:column;margin-right:18px}.setting-item .setting-item-info .setting-item-label{font-weight:700;margin-bottom:10px}",
				"",
			]);
		},
		1924: function (t, e, o) {
			var n = o(30),
				i = o(1925);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						if (0 === n)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(i, a);
			t.exports = i.locals || {};
		},
		1925: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				".open.SettingsModal{visibility:visible}.closed.SettingsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SettingsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SettingsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SettingsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SettingsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SettingsModal .footer .modal-button.cancel:hover,.SettingsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SettingsModal .footer .modal-button.cancel,.SettingsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SettingsModal .footer .modal-button.cancel.disabled,.SettingsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SettingsModal .footer .modal-button.cancel.disabled span,.SettingsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SettingsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SettingsModal .modal-container .wrapper .modal-content{padding:10px}.SettingsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SettingsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SettingsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SettingsModal .footer .modal-button.confirm{margin-left:4px}.SettingsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .footer .modal-button{padding:23px 8px}}.SettingsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .swipe-indicator{width:32px}}.SettingsModal .container{display:flex;flex-direction:column;width:888px;height:445px}.SettingsModal .container .settings-search-input{display:flex;border:1px solid var(--border);align-items:center;height:32px;padding:6px;border-radius:4px;background:var(--component-background);width:100%}.SettingsModal .container .settings-search-input[focus-within]{outline:none;border:1px solid var(--blue-5)}.SettingsModal .container .settings-search-input:focus-within{outline:none;border:1px solid var(--blue-5)}.SettingsModal .container .settings-search-input .Icon{width:16px;height:16px}.SettingsModal .container .settings-search-input input{width:100%;padding-right:26px;height:20px;border:none;background:transparent;padding-left:8px}.SettingsModal .container .header{display:flex;flex-direction:column;padding:12px 16px;font-size:16px;font-weight:700;align-items:center}.SettingsModal .container .header .title{display:flex;justify-content:space-between;align-items:center;width:100%;height:32px;margin-bottom:8px}.SettingsModal .container .body{height:100%;display:flex;flex-direction:row;overflow-y:hidden}.SettingsModal .container .body .settings-tabs-container{width:168px;flex-shrink:0;border-right:1px solid var(--gray-4)}.SettingsModal .container .body .settings-tabs-container .settings-tabs button{border:none;background:none;width:100%}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{height:32px;margin-top:4px;display:flex;align-items:center;padding:8px;color:var(--gray-9);cursor:pointer}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab:hover{border-right:2px solid var(--blue-6);color:var(--blue-6)}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{background-color:var(--gray-2)!important;border-right:2px solid var(--blue-5);color:var(--blue-5);cursor:default}.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.focus-visible,.SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab:focus-visible{outline-offset:-2px;outline:var(--focus-visible-outline);border-radius:4px}.SettingsModal .container .body .settings-content{padding:16px;overflow-y:scroll;width:100%}.SettingsModal .container .body .settings-content .setting-section{margin-bottom:16px}.SettingsModal .container .body .settings-content .setting-section .setting-label{font-weight:700;margin-bottom:8px}.SettingsModal .container .body .settings-content .Dropdown.language-dropdown{height:32px}.SettingsModal .container .body .Button.custom-ui.icon-only:not([disabled]):hover{box-shadow:none;border:1px solid var(--blue-6)}.SettingsModal .container .divider{height:1px;width:100%;background:var(--divider)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container{height:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body{flex-direction:column}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container{width:100%;position:fixed}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs{display:flex}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{flex:1;height:32px;margin-top:0;justify-content:center;white-space:nowrap;min-width:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{border-right:0;border-bottom:2px solid var(--blue-5)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-content{margin-top:32px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SettingsModal .container .body .settings-content.KeyboardShortcutTab{overflow-x:scroll;width:640px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container{height:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body{flex-direction:column}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container{width:100%;position:fixed}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs{display:flex}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab{flex:1;height:32px;margin-top:0;justify-content:center;white-space:nowrap;min-width:0}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-tabs-container .settings-tabs .settings-tab.selected{border-right:0;border-bottom:2px solid var(--blue-5)}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-content{margin-top:32px}.App.is-web-component:not(.is-in-desktop-only-mode) .SettingsModal .container .body .settings-content.KeyboardShortcutTab{overflow-x:scroll;width:640px}}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2021: function (t, e, o) {
			"use strict";
			o.r(e);
			o(41),
				o(29),
				o(8),
				o(19),
				o(12),
				o(13),
				o(14),
				o(10),
				o(9),
				o(11),
				o(16),
				o(15),
				o(20),
				o(18);
			var n = o(0),
				i = o.n(n),
				a = o(6),
				r = o(347),
				d = o(4),
				s = o(17),
				l = o.n(s),
				c = o(2),
				p = o(5),
				u = o(75),
				m = o(38),
				b = o(333),
				h = o(224),
				g = o(242),
				f = o(78),
				y = o(43),
				x = o(155),
				w = (o(154), o(102), o(46), o(52), Object(n.createContext)()),
				E = function (t) {
					var e = t.children,
						o = t.keywords,
						a = void 0 === o ? [] : o,
						r = Object(n.useContext)(w).trim();
					return !r ||
						a.some(function (t) {
							return t.toLowerCase().includes(r.toLowerCase());
						})
						? i.a.createElement(i.a.Fragment, null, e)
						: null;
				};
			o(1916);
			function S(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								i,
								a,
								r,
								d = [],
								s = !0,
								l = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									s = !1;
								} else
									for (
										;
										!(s = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										s = !0
									);
							} catch (t) {
								(l = !0), (i = t);
							} finally {
								try {
									if (
										!s &&
										null != o.return &&
										((r = o.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (l) throw i;
								}
							}
							return d;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return v(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return v(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function v(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var M = function () {
					var t = S(
							Object(a.e)(function (t) {
								return [d.a.getCurrentLanguage(t), d.a.getActiveTheme(t)];
							}),
							2,
						),
						e = t[0],
						o = t[1],
						n = S(Object(r.a)(), 1)[0],
						s = Object(a.d)(),
						l = Object(a.f)(),
						w = o === h.a.LIGHT,
						v = function (t) {
							s(c.a.setActiveTheme(t));
						};
					return i.a.createElement(
						i.a.Fragment,
						null,
						i.a.createElement(
							E,
							{ keywords: [n("option.settings.language")] },
							i.a.createElement(
								u.a,
								{
									className: "setting-section",
									dataElement: p.a.SETTINGS_LANGUAGE_SECTION,
								},
								i.a.createElement(
									"div",
									{ className: "setting-label" },
									i.a.createElement(
										"label",
										{ id: "language-dropdown-label" },
										n("option.settings.language"),
									),
								),
								i.a.createElement(f.a, {
									id: "language-dropdown",
									labelledById: "language-dropdown-label",
									dataElement: p.a.SETTINGS_LANGUAGE_DROPDOWN,
									items: b.a,
									currentSelectionKey: e,
									getKey: function (t) {
										return t[0];
									},
									getDisplayValue: function (t) {
										return t[1];
									},
									onClickItem: function (t) {
										t !== e && Object(g.a)(l)(t);
									},
									maxHeight: 200,
									width: 336,
									getCustomItemStyle: function () {
										return { textAlign: "left", width: "326px" };
									},
									className: "language-dropdown",
								}),
							),
						),
						i.a.createElement(
							E,
							{
								keywords: [
									n("option.settings.theme"),
									n("option.settings.lightMode"),
									n("option.settings.darkMode"),
								],
							},
							!m.e &&
								i.a.createElement(
									u.a,
									{
										className: "setting-section",
										dataElement: p.a.SETTINGS_THEME_SECTION,
									},
									i.a.createElement(
										"div",
										{ className: "setting-label" },
										i.a.createElement(
											"label",
											null,
											n("option.settings.theme"),
										),
									),
									i.a.createElement(
										"div",
										{ className: "theme-options" },
										i.a.createElement(
											"div",
											{
												className: "theme-option ".concat(
													w ? "active-theme" : "",
												),
											},
											i.a.createElement(y.a, {
												glyph: "icon-light-mode-option",
												className: "light-mode-icon",
											}),
											i.a.createElement(
												"div",
												{ className: "theme-choice" },
												i.a.createElement(x.a, {
													radio: !0,
													checked: w,
													onChange: function () {
														return v(h.a.LIGHT);
													},
													label: n("option.settings.lightMode"),
													name: "theme_choice",
												}),
											),
										),
										i.a.createElement(
											"div",
											{
												className: "theme-option ".concat(
													w ? "" : "active-theme",
												),
											},
											i.a.createElement(y.a, {
												glyph: "icon-dark-mode-option",
												className: "dark-mode-icon",
											}),
											i.a.createElement(
												"div",
												{ className: "theme-choice" },
												i.a.createElement(x.a, {
													radio: !0,
													checked: !w,
													onChange: function () {
														return v(h.a.DARK);
													},
													label: n("option.settings.darkMode"),
													name: "theme_choice",
												}),
											),
										),
									),
								),
						),
					);
				},
				T = (o(462), o(109), o(36), o(42)),
				A = o(56),
				N = (o(145), o(84), o(79), o(153)),
				C = o(351),
				k = o(21),
				O = o(173);
			o(1918);
			function I(t) {
				return (
					(function (t) {
						if (Array.isArray(t)) return D(t);
					})(t) ||
					(function (t) {
						if (
							("undefined" != typeof Symbol && null != t[Symbol.iterator]) ||
							null != t["@@iterator"]
						)
							return Array.from(t);
					})(t) ||
					j(t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function _(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								i,
								a,
								r,
								d = [],
								s = !0,
								l = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									s = !1;
								} else
									for (
										;
										!(s = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										s = !0
									);
							} catch (t) {
								(l = !0), (i = t);
							} finally {
								try {
									if (
										!s &&
										null != o.return &&
										((r = o.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (l) throw i;
								}
							}
							return d;
						}
					})(t, e) ||
					j(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function j(t, e) {
				if (t) {
					if ("string" == typeof t) return D(t, e);
					var o = Object.prototype.toString.call(t).slice(8, -1);
					return (
						"Object" === o && t.constructor && (o = t.constructor.name),
						"Map" === o || "Set" === o
							? Array.from(t)
							: "Arguments" === o ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
								? D(t, e)
								: void 0
					);
				}
			}
			function D(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var K = function (t) {
					var e = t.currentShortcut,
						o = t.finishEditing,
						s = t.getCommandStrings,
						c = _(Object(r.a)(), 1)[0],
						p = Object(a.e)(d.a.getShortcutKeyMap),
						u = _(Object(n.useState)(""), 2),
						m = u[0],
						b = u[1],
						h = _(Object(n.useState)(!1), 2),
						g = h[0],
						f = h[1],
						y = _(Object(n.useState)(new Set()), 2),
						x = y[0],
						w = y[1],
						E = _(Object(n.useState)(!1), 2),
						S = E[0],
						v = E[1];
					Object(n.useEffect)(
						function () {
							b(p[e]);
						},
						[e, p],
					),
						Object(n.useEffect)(
							function () {
								if (g) {
									var t = function (t) {
											t.preventDefault();
											var e = j(t);
											w(function (t) {
												return new Set([].concat(I(t), [e]));
											});
										},
										e = function () {
											if (x.size > 0) {
												var t = Array.from(x).join("+").toLowerCase();
												b(t);
											}
											if ((w(new Set()), f(!1), S)) {
												var e = Object(k.a)().querySelector(
													'[data-element="EditKeyboardShortcutModalEditButton"]',
												);
												e && e.focus();
											}
										};
									return (
										window.addEventListener("keydown", t),
										window.addEventListener("keyup", e),
										N.a.setScope("editShortcut"),
										function () {
											window.removeEventListener("keydown", t),
												window.removeEventListener("keyup", e),
												N.a.setScope(A.e);
										}
									);
								}
							},
							[g, x, S],
						);
					var M = Object(O.a)(o),
						j = function (t) {
							switch (t.keyCode) {
								case 16:
									return "shift";
								case 17:
									return "ctrl";
								case 18:
									return "alt";
								case 91:
								case 93:
									return "command";
								default:
									return N.a.getPressedKeyString()[
										N.a.getPressedKeyCodes().indexOf(t.keyCode)
									];
							}
						},
						D = A.d.hasConflict(e, m);
					return i.a.createElement(
						"div",
						{ className: "Modal EditKeyboardShortcutModal open" },
						i.a.createElement(
							C.a,
							{
								isOpen: !0,
								title: "option.settings.editKeyboardShorcut",
								onCloseClick: o,
							},
							i.a.createElement(
								"div",
								{ className: "body" },
								i.a.createElement(
									"div",
									{
										className: l()({
											"keyboard-shortcut": !0,
											recording: g && S,
										}),
									},
									s(m).map(function (t, e) {
										return i.a.createElement("span", { key: e }, t);
									}),
								),
								D &&
									i.a.createElement(
										"div",
										{ className: "conflict-warning" },
										c("option.settings.shortcutAlreadyExists"),
									),
							),
							i.a.createElement("div", { className: "divider" }),
							i.a.createElement(
								"div",
								{ className: "footer" },
								i.a.createElement(T.a, {
									className: "modal-button secondary-btn-custom",
									label: c("option.settings.editShortcut"),
									disabled: g,
									onClick: function (t) {
										if ((w(new Set()), t.detail > 0)) f(!0), v(!1);
										else {
											v(!0);
											window.addEventListener("keyup", function t() {
												f(!0), window.removeEventListener("keyup", t);
											});
										}
									},
									dataElement: "EditKeyboardShortcutModalEditButton",
								}),
								i.a.createElement(T.a, {
									className: "modal-button confirm",
									label: c("option.settings.setShortcut"),
									disabled: g || D,
									onClick: function () {
										N.a.setScope(A.e), A.d.setShortcutKey(e, m), M();
									},
									dataElement: "EditKeyboardShortcutModalSetButton",
								}),
							),
						),
					);
				},
				R = o(95);
			o(1920);
			function L(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								i,
								a,
								r,
								d = [],
								s = !0,
								l = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									s = !1;
								} else
									for (
										;
										!(s = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										s = !0
									);
							} catch (t) {
								(l = !0), (i = t);
							} finally {
								try {
									if (
										!s &&
										null != o.return &&
										((r = o.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (l) throw i;
								}
							}
							return d;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return G(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return G(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function G(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var P = [
					[A.c.ROTATE_CLOCKWISE, "option.settings.rotateDocumentClockwise"],
					[
						A.c.ROTATE_COUNTER_CLOCKWISE,
						"option.settings.rotateDocumentCounterclockwise",
					],
					[A.c.COPY, "option.settings.copyText"],
					[A.c.PASTE, "option.settings.pasteText"],
					[A.c.UNDO, "option.settings.undoChange"],
					[A.c.REDO, "option.settings.redoChange"],
					[A.c.OPEN_FILE, "option.settings.openFile"],
					[A.c.SEARCH, "option.settings.openSearch"],
					[A.c.ZOOM_IN, "option.settings.zoomIn"],
					[A.c.ZOOM_OUT, "option.settings.zoomOut"],
					[A.c.SET_HEADER_FOCUS, "option.settings.setHeaderFocus"],
					[A.c.FIT_SCREEN_WIDTH, "option.settings.fitScreenWidth"],
					[A.c.PRINT, "option.settings.print"],
					[A.c.BOOKMARK, "option.settings.bookmarkOpenPanel"],
					[A.c.PREVIOUS_PAGE, "option.settings.goToPreviousPage"],
					[A.c.NEXT_PAGE, "option.settings.goToNextPage"],
					[A.c.UP, "option.settings.goToPreviousPageArrowUp"],
					[A.c.DOWN, "option.settings.goToNextPageArrowDown"],
					[A.c.SWITCH_PAN, "option.settings.holdSwitchPan"],
					[A.c.SELECT, "option.settings.selectAnnotationEdit"],
					[A.c.PAN, "option.settings.selectPan"],
					[A.c.ARROW, "option.settings.selectCreateArrowTool"],
					[A.c.CALLOUT, "option.settings.selectCreateCalloutTool"],
					[A.c.ERASER, "option.settings.selectEraserTool"],
					[A.c.FREEHAND, "option.settings.selectCreateFreeHandTool"],
					[A.c.IMAGE, "option.settings.selectCreateStampTool"],
					[A.c.LINE, "option.settings.selectCreateLineTool"],
					[A.c.STICKY_NOTE, "option.settings.selectCreateStickyTool"],
					[A.c.ELLIPSE, "option.settings.selectCreateEllipseTool"],
					[A.c.RECTANGLE, "option.settings.selectCreateRectangleTool"],
					[A.c.RUBBER_STAMP, "option.settings.selectCreateRubberStampTool"],
					[A.c.FREETEXT, "option.settings.selectCreateFreeTextTool"],
					[A.c.SIGNATURE, "option.settings.openSignatureModal"],
					[A.c.SQUIGGLY, "option.settings.selectCreateTextSquigglyTool"],
					[A.c.HIGHLIGHT, "option.settings.selectCreateTextHighlightTool"],
					[A.c.STRIKEOUT, "option.settings.selectCreateTextStrikeoutTool"],
					[A.c.UNDERLINE, "option.settings.selectCreateTextUnderlineTool"],
					[A.c.CLOSE, "option.settings.close"],
				],
				H = function () {
					var t = L(Object(r.a)(), 1)[0],
						e = Object(a.d)(),
						o = Object(a.e)(d.a.getShortcutKeyMap),
						s = L(Object(n.useState)(void 0), 2),
						l = s[0],
						u = s[1],
						b = function (t) {
							if (!t) return [];
							if ((t = t.toUpperCase()).includes(", COMMAND")) {
								var e = t.split(", ");
								t = m.j ? e[1] : e[0];
							}
							return t.split(/(\+)/g);
						},
						h = Object(R.a)(function (t) {
							!(function (t) {
								e(c.a.setIsElementHidden(p.a.SETTINGS_MODAL, !0)), u(t);
							})(
								t.currentTarget
									.getAttribute("data-element")
									.replace("edit-button-", ""),
							);
						});
					return i.a.createElement(
						i.a.Fragment,
						null,
						i.a.createElement(
							"div",
							{ className: "shortcut-table-header" },
							i.a.createElement(
								"div",
								{ className: "shortcut-table-header-command" },
								t("option.settings.command"),
							),
							i.a.createElement(
								"div",
								{ className: "shortcut-table-header-description" },
								t("option.settings.description"),
							),
							i.a.createElement(
								"div",
								{ className: "shortcut-table-header-action" },
								t("option.settings.action"),
							),
						),
						i.a.createElement(
							"div",
							{ className: "shortcut-table-content" },
							P.map(function (e) {
								var n = L(e, 2),
									a = n[0],
									r = n[1];
								return i.a.createElement(
									E,
									{ key: a, keywords: [t(r)] },
									i.a.createElement(
										"div",
										{ className: "shortcut-table-item" },
										i.a.createElement(
											"div",
											{ className: "shortcut-table-item-command" },
											b(o[a]).map(function (t, e) {
												return i.a.createElement("span", { key: e }, t);
											}),
										),
										i.a.createElement(
											"div",
											{ className: "shortcut-table-item-description" },
											t(r),
										),
										i.a.createElement(T.a, {
											dataElement: "edit-button-".concat(a),
											img: "icon-edit-form-field",
											title: t("action.edit"),
											ariaLabel: "".concat(t(r), " ").concat(t("action.edit")),
											onClick: h,
										}),
									),
								);
							}),
						),
						l &&
							i.a.createElement(K, {
								currentShortcut: l,
								finishEditing: function () {
									u(void 0), e(c.a.setIsElementHidden(p.a.SETTINGS_MODAL, !1));
								},
								getCommandStrings: b,
							}),
					);
				},
				U = (o(595), o(596), o(108)),
				B = o(1);
			o(1922);
			function F(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								i,
								a,
								r,
								d = [],
								s = !0,
								l = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									s = !1;
								} else
									for (
										;
										!(s = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										s = !0
									);
							} catch (t) {
								(l = !0), (i = t);
							} finally {
								try {
									if (
										!s &&
										null != o.return &&
										((r = o.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (l) throw i;
								}
							}
							return d;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return W(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return W(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function W(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var z = function (t, e, o, n) {
					return { label: t, description: e, isChecked: o, onToggled: n };
				},
				V = function () {
					var t = F(
							Object(a.e)(function (t) {
								return [
									d.a.shouldFadePageNavigationComponent(t),
									d.a.isNoteSubmissionWithEnterEnabled(t),
									d.a.isCommentThreadExpansionEnabled(t),
									d.a.isNotesPanelRepliesCollapsingEnabled(t),
									d.a.isNotesPanelTextCollapsingEnabled(t),
									d.a.shouldClearSearchPanelOnClose(t),
									d.a.pageDeletionConfirmationModalEnabled(t),
									d.a.isThumbnailSelectingPages(t),
									d.a.getCustomSettings(t),
									d.a.isToolDefaultStyleUpdateFromAnnotationPopupEnabled(t),
									d.a.isWidgetHighlightingEnabled(t),
								];
							}),
							11,
						),
						e = t[0],
						o = t[1],
						s = t[2],
						l = t[3],
						p = t[4],
						u = t[5],
						m = t[6],
						b = t[7],
						h = t[8],
						g = t[9],
						f = t[10],
						y = F(Object(r.a)(), 1)[0],
						w = Object(a.d)(),
						S = F(Object(n.useState)(), 2)[1],
						v = Object(n.useCallback)(function () {
							return S({});
						}, []),
						M = [
							z(
								y("option.settings.disableFadePageNavigationComponent"),
								y("option.settings.disableFadePageNavigationComponentDesc"),
								!e,
								function (t) {
									w(
										t
											? c.a.disableFadePageNavigationComponent()
											: c.a.enableFadePageNavigationComponent(),
									);
								},
							),
							z(
								y("option.settings.disableNativeScrolling"),
								y("option.settings.disableNativeScrollingDesc"),
								!U.a.useNativeScroll,
								function (t) {
									U.a.useNativeScroll = !t;
								},
							),
							z(
								y("option.settings.enabledFormFieldHighlighting"),
								y("option.settings.enabledFormFieldHighlightingDesc"),
								f,
								function (t) {
									t ? A() : T();
								},
							),
						],
						T = function () {
							B.a
								.getAnnotationManager()
								.getFieldManager()
								.disableWidgetHighlighting();
						},
						A = function () {
							B.a
								.getAnnotationManager()
								.getFieldManager()
								.enableWidgetHighlighting();
						},
						N = [
							z(
								y(
									"option.settings.disableToolDefaultStyleUpdateFromAnnotationPopup",
								),
								y(
									"option.settings.disableToolDefaultStyleUpdateFromAnnotationPopupDesc",
								),
								!g,
								function (t) {
									return w(
										c.a.setToolDefaultStyleUpdateFromAnnotationPopupEnabled(!t),
									);
								},
							),
						],
						C = [
							z(
								y("option.settings.disableNoteSubmissionWithEnter"),
								y("option.settings.disableNoteSubmissionWithEnterDesc"),
								!o,
								function (t) {
									return w(c.a.setNoteSubmissionEnabledWithEnter(!t));
								},
							),
							z(
								y("option.settings.disableAutoExpandCommentThread"),
								y("option.settings.disableAutoExpandCommentThreadDesc"),
								!s,
								function (t) {
									return w(c.a.setCommentThreadExpansion(!t));
								},
							),
							z(
								y("option.settings.disableReplyCollapse"),
								y("option.settings.disableReplyCollapseDesc"),
								!l,
								function (t) {
									return w(c.a.setNotesPanelRepliesCollapsing(!t));
								},
							),
							z(
								y("option.settings.disableTextCollapse"),
								y("option.settings.disableTextCollapseDesc"),
								!p,
								function (t) {
									return w(c.a.setNotesPanelTextCollapsing(!t));
								},
							),
						],
						k = [
							z(
								y("option.settings.disableClearSearchOnPanelClose"),
								y("option.settings.disableClearSearchOnPanelCloseDesc"),
								!u,
								function (t) {
									return w(c.a.setClearSearchOnPanelClose(!t));
								},
							),
						],
						O = [
							z(
								y("option.settings.disablePageDeletionConfirmationModal"),
								y("option.settings.disablePageDeletionConfirmationModalDesc"),
								!m,
								function (t) {
									w(
										t
											? c.a.disablePageDeletionConfirmationModal()
											: c.a.enablePageDeletionConfirmationModal(),
									);
								},
							),
							z(
								y("option.settings.disableMultiselect"),
								y("option.settings.disableMultiselectDesc"),
								!b,
								function (t) {
									return w(c.a.setThumbnailSelectingPages(!t));
								},
							),
						],
						I = [
							[y("option.settings.viewing"), M],
							[y("option.settings.annotations"), N],
							[y("option.settings.notesPanel"), C],
							[y("option.settings.search"), k],
							[y("option.settings.pageManipulation"), O],
							[y("option.settings.miscellaneous"), h],
						],
						_ = function (t, e) {
							return [
								t,
								e
									.map(function (t) {
										return [t.label, t.description];
									})
									.flat(),
							].flat();
						};
					return i.a.createElement(
						i.a.Fragment,
						null,
						I.map(function (t) {
							var e = F(t, 2),
								o = e[0],
								n = e[1];
							return n.length < 1
								? null
								: i.a.createElement(
										E,
										{ key: o, keywords: _(o, n) },
										i.a.createElement(
											"div",
											{ className: "setting-section" },
											i.a.createElement(
												"div",
												{ className: "setting-label" },
												i.a.createElement("label", null, o),
											),
											n.map(function (t) {
												return i.a.createElement(
													E,
													{
														key: t.label,
														keywords: [o, t.label, t.description],
													},
													i.a.createElement(
														"div",
														{ className: "setting-item" },
														i.a.createElement(
															"div",
															{ className: "setting-item-info" },
															i.a.createElement(
																"div",
																{ className: "setting-item-label" },
																t.label,
															),
															i.a.createElement("div", null, t.description),
														),
														i.a.createElement(x.a, {
															"aria-label": t.label,
															isSwitch: !0,
															checked:
																"function" == typeof t.isChecked
																	? t.isChecked()
																	: t.isChecked,
															onChange: function (e) {
																t.onToggled(e.target.checked), v();
															},
														}),
													),
												);
											}),
										),
									);
						}),
					);
				};
			o(1924);
			function q(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								i,
								a,
								r,
								d = [],
								s = !0,
								l = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									s = !1;
								} else
									for (
										;
										!(s = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										s = !0
									);
							} catch (t) {
								(l = !0), (i = t);
							} finally {
								try {
									if (
										!s &&
										null != o.return &&
										((r = o.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (l) throw i;
								}
							}
							return d;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return Y(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return Y(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Y(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var $ = p.a.SETTINGS_MODAL,
				J = function () {
					var t = Object(a.e)(function (t) {
							return d.a.isElementDisabled(t, p.a.SETTINGS_MODAL);
						}),
						e = Object(a.e)(function (t) {
							return d.a.isElementOpen(t, p.a.SETTINGS_MODAL);
						}),
						o = Object(a.e)(d.a.isSpreadsheetEditorModeEnabled),
						s = Object(a.e)(function (t) {
							return d.a.getSelectedTab(t, $);
						}),
						m = Object(a.e)(function (t) {
							return d.a.isElementDisabled(t, p.a.SETTINGS_GENERAL_BUTTON);
						}),
						b = Object(a.e)(function (t) {
							return d.a.isElementDisabled(t, p.a.SETTINGS_KEYBOARD_BUTTON);
						}),
						h = Object(a.e)(function (t) {
							return d.a.isElementDisabled(t, p.a.SETTINGS_ADVANCED_BUTTON);
						}),
						g = q(Object(r.a)(), 1)[0],
						f = Object(a.d)(),
						x = q(Object(n.useState)(""), 2),
						E = x[0],
						S = x[1],
						v = [
							[p.a.SETTINGS_GENERAL_BUTTON, g("option.settings.general")],
							[
								p.a.SETTINGS_KEYBOARD_BUTTON,
								g("option.settings.keyboardShortcut"),
							],
							[
								p.a.SETTINGS_ADVANCED_BUTTON,
								g("option.settings.advancedSetting"),
							],
						];
					Object(n.useEffect)(
						function () {
							if (
								(s === p.a.SETTINGS_GENERAL_BUTTON && m) ||
								(s === p.a.SETTINGS_KEYBOARD_BUTTON && b) ||
								(s === p.a.SETTINGS_ADVANCED_BUTTON && h)
							) {
								var t = "";
								m
									? b
										? h || (t = p.a.SETTINGS_ADVANCED_BUTTON)
										: (t = p.a.SETTINGS_KEYBOARD_BUTTON)
									: (t = p.a.SETTINGS_GENERAL_BUTTON),
									f(c.a.setSelectedTab($, t));
							}
						},
						[m, b, h],
					);
					var T = function () {
						f(c.a.closeElement(p.a.SETTINGS_MODAL));
					};
					if (t) return null;
					var A = l()("Modal", "SettingsModal", "open");
					return i.a.createElement(
						w.Provider,
						{ value: E },
						i.a.createElement(
							"div",
							{ className: A, "data-element": p.a.SettingsModal },
							i.a.createElement(
								C.a,
								{
									title: g("option.settings.settings"),
									closeHandler: T,
									onCloseClick: T,
									isOpen: e,
									swipeToClose: !0,
								},
								i.a.createElement(
									"div",
									{ className: "container" },
									i.a.createElement("div", { className: "swipe-indicator" }),
									i.a.createElement(
										"div",
										{ className: "header" },
										i.a.createElement(
											"div",
											{ className: "settings-search-input" },
											i.a.createElement(y.a, { glyph: "icon-header-search" }),
											i.a.createElement("input", {
												value: E,
												onChange: function (t) {
													return S(t.target.value);
												},
												"aria-label": g("message.searchSettingsPlaceholder"),
											}),
										),
									),
									i.a.createElement("div", { className: "divider" }),
									i.a.createElement(
										"div",
										{ className: "body" },
										i.a.createElement(
											"div",
											{ className: "settings-tabs-container" },
											i.a.createElement(
												"div",
												{ className: "settings-tabs" },
												v
													.filter(function (t) {
														var e = q(t, 1)[0];
														return (
															!o ||
															(e !== p.a.SETTINGS_KEYBOARD_BUTTON &&
																e !== p.a.SETTINGS_ADVANCED_BUTTON)
														);
													})
													.map(function (t) {
														var e = q(t, 2),
															o = e[0],
															n = e[1],
															a = l()("settings-tab", { selected: o === s });
														return i.a.createElement(
															u.a,
															{
																type: "button",
																className: a,
																dataElement: o,
																onClick: function () {
																	return (function (t) {
																		t !== s && f(c.a.setSelectedTab($, t));
																	})(o);
																},
																key: o,
																"aria-selected": o === s,
																"aria-current": o === s ? "page" : null,
															},
															n,
														);
													}),
											),
										),
										i.a.createElement(
											"div",
											{
												className: l()("settings-content", {
													KeyboardShortcutTab:
														s === p.a.SETTINGS_KEYBOARD_BUTTON,
												}),
											},
											s === p.a.SETTINGS_GENERAL_BUTTON &&
												i.a.createElement(M, null),
											s === p.a.SETTINGS_KEYBOARD_BUTTON &&
												i.a.createElement(H, null),
											s === p.a.SETTINGS_ADVANCED_BUTTON &&
												i.a.createElement(V, null),
										),
									),
								),
							),
						),
					);
				};
			e.default = J;
		},
	},
]);
//# sourceMappingURL=chunk.37.js.map
