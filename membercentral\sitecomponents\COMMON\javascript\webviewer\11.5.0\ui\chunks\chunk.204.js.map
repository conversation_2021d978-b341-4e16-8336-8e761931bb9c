{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/pt-br.js"], "names": ["module", "exports", "e", "a", "default", "o", "s", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAgL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,QAAQC,SAAS,iFAAiFC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,2FAA2FH,MAAM,KAAKI,YAAY,kDAAkDJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,EAAE,KAAKa,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,mCAAmCC,KAAK,0CAA0CC,aAAa,CAACC,OAAO,QAAQC,KAAK,QAAQlB,EAAE,kBAAkBmB,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,WAAWC,EAAE,SAASC,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,YAAY,OAAO/B,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAA/kCD,CAAE,EAAQ", "file": "chunks/chunk.204.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_pt_br=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var a=o(e),s={name:\"pt-br\",weekdays:\"domingo_segunda-feira_terça-feira_quarta-feira_quinta-feira_sexta-feira_sábado\".split(\"_\"),weekdaysShort:\"dom_seg_ter_qua_qui_sex_sáb\".split(\"_\"),weekdaysMin:\"Do_2ª_3ª_4ª_5ª_6ª_Sá\".split(\"_\"),months:\"janeiro_fevereiro_março_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro\".split(\"_\"),monthsShort:\"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez\".split(\"_\"),ordinal:function(e){return e+\"º\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY [às] HH:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY [às] HH:mm\"},relativeTime:{future:\"em %s\",past:\"há %s\",s:\"poucos segundos\",m:\"um minuto\",mm:\"%d minutos\",h:\"uma hora\",hh:\"%d horas\",d:\"um dia\",dd:\"%d dias\",M:\"um mês\",MM:\"%d meses\",y:\"um ano\",yy:\"%d anos\"}};return a.default.locale(s,null,!0),s}));"], "sourceRoot": ""}