ALTER PROCEDURE dbo.ref_insertReferralUpdateHistory
@orgID int,
@siteID int,
@clientReferralID int,
@message varchar(max),
@changesArray varchar(max) = NULL,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @changesArray = ISNULL(NULLIF(@changesArray,''),'[]');
	
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"historyEntries_SYS_ADMIN_REFERRALUPDATE", "d": {
		"HISTORYCODE":"SYS_ADMIN_REFERRALUPDATE",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
		"CLIENTREFERRALID":' + cast(@clientReferralID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MAINMESSAGE":"' + @message + '",
		"CHANGES":'+ @changesArray +'} }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
