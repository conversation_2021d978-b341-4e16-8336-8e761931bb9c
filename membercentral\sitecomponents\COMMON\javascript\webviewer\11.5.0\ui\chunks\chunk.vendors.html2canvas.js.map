{"version": 3, "sources": ["webpack:///./src/ui/node_modules/html2canvas/dist/npm/index.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Color.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Bounds.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Length.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/NodeContainer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Util.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/background.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Path.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Vector.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/listStyle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/TextContainer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Feature.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textDecoration.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/border.js", "webpack:///./src/ui/node_modules/css-line-break/dist/Util.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/ListItem.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/renderer/CanvasRenderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Logger.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/padding.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/overflowWrap.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/position.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textTransform.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Input.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/TextBounds.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/renderer/ForeignObjectRenderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Unicode.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Font.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Proxy.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Window.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/NodeParser.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/StackingContext.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Size.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/BezierCurve.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/borderRadius.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/display.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/float.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/font.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/letterSpacing.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/lineBreak.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/margin.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/overflow.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/textShadow.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/transform.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/visibility.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/word-break.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/parsing/zIndex.js", "webpack:///./src/ui/node_modules/css-line-break/dist/index.js", "webpack:///./src/ui/node_modules/css-line-break/dist/LineBreak.js", "webpack:///./src/ui/node_modules/css-line-break/dist/Trie.js", "webpack:///./src/ui/node_modules/css-line-break/dist/linebreak-trie.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/drawing/Circle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Renderer.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Gradient.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Angle.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/Clone.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/ResourceLoader.js", "webpack:///./src/ui/node_modules/html2canvas/dist/npm/PseudoNodeContent.js"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "_CanvasRenderer2", "_interopRequireDefault", "_Logger2", "_Window", "_Bounds", "obj", "__esModule", "default", "html2canvas", "element", "conf", "config", "logger", "logging", "log", "ownerDocument", "Promise", "reject", "defaultView", "scrollX", "pageXOffset", "scrollY", "pageYOffset", "_ref", "tagName", "parseDocumentSize", "parseBounds", "width", "height", "left", "top", "defaultOptions", "async", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imageTimeout", "proxy", "remove<PERSON><PERSON><PERSON>", "foreignObjectRendering", "scale", "devicePixelRatio", "canvas", "useCORS", "x", "y", "Math", "ceil", "windowWidth", "innerWidth", "windowHeight", "innerHeight", "renderElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "module", "exports", "defineProperty", "value", "_slicedToArray", "arr", "Array", "isArray", "Symbol", "iterator", "_arr", "_n", "_d", "_e", "undefined", "_s", "_i", "next", "done", "push", "err", "sliceIterator", "TypeError", "_createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "HEX3", "HEX6", "RGB", "RGBA", "Color", "instance", "_classCallCheck", "this", "array", "min", "match", "parseInt", "hex3", "Number", "rgb", "rgba", "NAMED_COLORS", "toLowerCase", "substring", "hex6", "_ref2", "r", "g", "b", "a", "transparent", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "TRANSPARENT", "parseBoundCurves", "calculatePaddingBoxPath", "calculateBorderBoxPath", "parsePathForBorder", "calculateContentBox", "calculatePaddingBox", "Bounds", "_Vector2", "_BezierCurve2", "w", "h", "clientRect", "createPathFromCurves", "node", "fromClientRect", "getBoundingClientRect", "bounds", "borders", "borderWidth", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "document", "body", "documentElement", "Error", "max", "scrollWidth", "offsetWidth", "clientWidth", "scrollHeight", "offsetHeight", "clientHeight", "curves", "borderSide", "topLeftOuter", "topLeftInner", "topRightOuter", "topRightInner", "bottomRightOuter", "bottomRightInner", "bottomLeftOuter", "bottomLeftInner", "outer1", "inner1", "outer2", "inner2", "path", "subdivide", "reverse", "CORNER", "borderRadius", "tlh", "TOP_LEFT", "getAbsoluteValue", "tlv", "trh", "TOP_RIGHT", "trv", "brh", "BOTTOM_RIGHT", "brv", "blh", "BOTTOM_LEFT", "blv", "factors", "maxFactor", "apply", "topWidth", "rightHeight", "bottomWidth", "leftHeight", "getCurvePoints", "r1", "r2", "position", "kappa", "sqrt", "ox", "oy", "xm", "ym", "calculateLengthFromValueWithUnit", "LENGTH_TYPE", "_NodeContainer", "PX", "PERCENTAGE", "Length", "type", "substr", "parsedValue", "parseFloat", "isNaN", "parent<PERSON>ength", "isPercentage", "v", "container", "unit", "style", "font", "fontSize", "getRootFontSize", "parent", "_Color", "_Color2", "_Util", "_background", "_border", "_borderRadius", "_display", "_float", "_font", "_letterSpacing", "_lineBreak", "_listStyle", "_margin", "_overflow", "_overflowWrap", "_padding", "_position", "_textDecoration", "_textShadow", "_textTransform", "_transform", "_visibility", "_wordBreak", "_zIndex", "_Input", "_ListItem", "INPUT_TAGS", "NodeContainer", "resourceLoader", "index", "_this", "childNodes", "listItems", "start", "listStart", "getComputedStyle", "display", "parseDisplay", "IS_INPUT", "parsePosition", "background", "INPUT_BACKGROUND", "parseBackground", "border", "INPUT_BORDERS", "parseBorder", "HTMLInputElement", "getInputBorderRadius", "parseBorderRadius", "color", "INPUT_COLOR", "float", "parseCSSFloat", "parseFont", "letterSpacing", "parseLetterSpacing", "listStyle", "DISPLAY", "LIST_ITEM", "parseListStyle", "lineBreak", "parseLineBreak", "margin", "parse<PERSON><PERSON><PERSON>", "opacity", "overflow", "indexOf", "parseOverflow", "OVERFLOW", "HIDDEN", "overflowWrap", "parseOverflowWrap", "wordWrap", "parsePadding", "textDecoration", "parseTextDecoration", "textShadow", "parseTextShadow", "textTransform", "parseTextTransform", "transform", "parseTransform", "visibility", "parseVisibility", "wordBreak", "parseWordBreak", "zIndex", "parseZIndex", "POSITION", "STATIC", "isTransformed", "listOwner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listIndex", "hasAttribute", "addEventListener", "curvedBounds", "image", "getImage", "reformatInputBounds", "parentClips", "getClipPaths", "VISIBLE", "concat", "isRootElement", "isFloating", "isAbsolutelyPositioned", "contains", "NONE", "VISIBILITY", "RELATIVE", "FLOAT", "isPositioned", "auto", "INLINE", "INLINE_BLOCK", "INLINE_FLEX", "INLINE_GRID", "INLINE_LIST_ITEM", "INLINE_TABLE", "SVGSVGElement", "setAttribute", "s", "XMLSerializer", "loadImage", "encodeURIComponent", "serializeToString", "img", "currentSrc", "src", "loadCanvas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getAttribute", "bit", "distance", "copyCSSStyles", "property", "item", "setProperty", "getPropertyValue", "SMALL_IMAGE", "parseBackgroundImage", "calculateBackgroundRepeatPath", "calculateBackgroundPosition", "calculateBackgroungPositioningArea", "calculateBackgroungPaintingArea", "calculateGradientBackgroundSize", "calculateBackgroundSize", "BACKGROUND_ORIGIN", "BACKGROUND_CLIP", "BACKGROUND_SIZE", "BACKGROUND_REPEAT", "_Length2", "_Size2", "REPEAT", "NO_REPEAT", "REPEAT_X", "REPEAT_Y", "AUTO", "CONTAIN", "COVER", "LENGTH", "BORDER_BOX", "PADDING_BOX", "CONTENT_BOX", "BackgroundSize", "size", "AUTO_SIZE", "backgroundImage", "targetRatio", "currentRatio", "parseBackgroundClip", "clip", "<PERSON><PERSON><PERSON><PERSON>", "paddingBox", "PADDING_SIDES", "LEFT", "RIGHT", "TOP", "BOTTOM", "backgroundPositioningArea", "repeat", "round", "parseBackgroundImages", "backgroundClip", "parseBackgroundOrigin", "parseBackgroundRepeat", "backgroundRepeat", "trim", "sources", "map", "method", "args", "positions", "backgroundPosition", "split", "repeats", "sizes", "backgroundSize", "parseBackgroundSize", "parseBackgoundPosition", "whitespace", "results", "quote", "definition", "mode", "numParen", "appendResult", "prefix", "prefix_i", "for<PERSON>ach", "c", "test", "PATH", "VECTOR", "BEZIER_CURVE", "CIRCLE", "_Path", "Vector", "parseListStyleType", "LIST_STYLE_TYPE", "LIST_STYLE_POSITION", "INSIDE", "OUTSIDE", "DISC", "SQUARE", "DECIMAL", "CJK_DECIMAL", "DECIMAL_LEADING_ZERO", "LOWER_ROMAN", "UPPER_ROMAN", "LOWER_GREEK", "LOWER_ALPHA", "UPPER_ALPHA", "ARABIC_INDIC", "ARMENIAN", "BENGALI", "CAMBODIAN", "CJK_EARTHLY_BRANCH", "CJK_HEAVENLY_STEM", "CJK_IDEOGRAPHIC", "DEVANAGARI", "ETHIOPIC_NUMERIC", "GEORGIAN", "GUJARATI", "GURMUKHI", "HEBREW", "HIRAGANA", "HIRAGANA_IROHA", "JAPANESE_FORMAL", "JAPANESE_INFORMAL", "KANNADA", "KATAKANA", "KATAKANA_IROHA", "KHMER", "KOREAN_HANGUL_FORMAL", "KOREAN_HANJA_FORMAL", "KOREAN_HANJA_INFORMAL", "LAO", "LOWER_ARMENIAN", "MALAYALAM", "MONGOLIAN", "MYANMAR", "ORIYA", "PERSIAN", "SIMP_CHINESE_FORMAL", "SIMP_CHINESE_INFORMAL", "TAMIL", "TELUGU", "THAI", "TIBETAN", "TRAD_CHINESE_FORMAL", "TRAD_CHINESE_INFORMAL", "UPPER_ARMENIAN", "DISCLOSURE_OPEN", "DISCLOSURE_CLOSED", "parseListStylePosition", "listStyleImage", "listStyleType", "listStylePosition", "_TextBounds", "TextContainer", "text", "data", "parseTextBounds", "CAPITALIZE", "TEXT_TRANSFORM", "LOWERCASE", "replace", "capitalize", "UPPERCASE", "toUpperCase", "m", "p1", "p2", "_ForeignObject<PERSON><PERSON>er", "isGreenPixel", "FEATURES", "createRange", "range", "testElement", "createElement", "TEST_HEIGHT", "append<PERSON><PERSON><PERSON>", "selectNode", "rangeBounds", "rangeHeight", "<PERSON><PERSON><PERSON><PERSON>", "testRangeBounds", "Image", "ctx", "getContext", "drawImage", "toDataURL", "e", "testSVG", "_value", "resolve", "onload", "onerror", "complete", "setTimeout", "testBase64", "from", "window", "fetch", "fillStyle", "fillRect", "greenImageSrc", "svg", "createForeignObjectSVG", "loadSerializedSVG", "then", "getImageData", "catch", "testForeignObject", "crossOrigin", "XMLHttpRequest", "responseType", "TEXT_DECORATION_LINE", "TEXT_DECORATION", "TEXT_DECORATION_STYLE", "SOLID", "DOUBLE", "DOTTED", "DASHED", "WAVY", "UNDERLINE", "OVERLINE", "LINE_THROUGH", "BLINK", "parseLine", "line", "textDecorationLine", "textDecorationColor", "textDecorationStyle", "parseTextDecorationStyle", "BORDER_SIDES", "BORDER_STYLE", "SIDES", "keys", "side", "borderColor", "borderStyle", "parseBorderStyle", "toCodePoints", "str", "codePoints", "charCodeAt", "extra", "fromCodePoint", "String", "codeUnits", "result", "codePoint", "fromCharCode", "chars", "lookup", "Uint8Array", "decode", "base64", "bufferLength", "len", "p", "encoded1", "encoded2", "encoded3", "encoded4", "buffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "bytes", "polyUint16Array", "polyUint32Array", "_i2", "createCounterText", "inlineListItemElement", "_NodeContainer2", "_TextContainer2", "_Unicode", "ancestorTypes", "ROMAN_UPPER", "wrapper", "bottom", "right", "textAlign", "MARGIN_TOP", "styleImage", "createTextNode", "fromTextNode", "integers", "values", "createAdditiveCounter", "symbols", "fallback", "suffix", "reduce", "string", "integer", "createCounterStyleWithSymbolResolver", "codePointRangeLength", "isNumeric", "resolver", "createCounterStyleFromRange", "codePointRangeStart", "codePointRangeEnd", "abs", "floor", "createCounterStyleFromSymbols", "createCJKCounter", "numbers", "multipliers", "negativeSign", "flags", "tmp", "digit", "coefficient", "appendSuffix", "defaultSuffix", "cjkSuffix", "koreanSuffix", "CJK_TEN_COEFFICIENTS", "CJK_ZEROS", "addColorStops", "gradient", "canvasGradient", "maxStop", "colorStops", "colorStop", "stop", "f", "addColorStop", "toString", "customCanvas", "options", "translate", "textBaseline", "clipPaths", "callback", "save", "restore", "destination", "fill", "_path", "_this2", "beginPath", "point", "moveTo", "lineTo", "bezierCurveTo", "startControl", "endControl", "end", "arc", "radius", "PI", "closePath", "linearGradient", "createLinearGradient", "direction", "x1", "y1", "x0", "y0", "_this3", "center", "radialGradient", "createRadialGradient", "midX", "midY", "invF", "imageSize", "offsetX", "offsetY", "createPattern", "resizeImage", "textBounds", "textShadows", "_this4", "fontStyle", "fontVariant", "fontWeight", "fontFamily", "join", "shadowColor", "shadowOffsetX", "shadowOffsetY", "<PERSON><PERSON><PERSON><PERSON>", "blur", "fillText", "baseline", "fontMetrics", "getMetrics", "rectangle", "middle", "globalAlpha", "matrix", "<PERSON><PERSON>", "enabled", "id", "Date", "now", "console", "_len", "_key", "Function", "bind", "error", "_len2", "_key2", "_Length", "OVERFLOW_WRAP", "NORMAL", "BREAK_WORD", "ABSOLUTE", "FIXED", "STICKY", "inlineSelectElement", "inlineTextAreaElement", "inlineInputElement", "_Circle2", "INPUT_BORDER_COLOR", "INPUT_BACKGROUND_COLOR", "INPUT_BORDER", "RADIO_BORDER_RADIUS", "RADIO_BORDER_RADIUS_TUPLE", "INPUT_RADIO_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS", "CHECKBOX_BORDER_RADIUS_TUPLE", "INPUT_CHECKBOX_BORDER_RADIUS", "inlineFormElement", "checked", "getInputValue", "option", "selectedIndex", "allowLinebreak", "whiteSpace", "placeholder", "TextBounds", "_Feature", "_Feature2", "getWrapperBounds", "textList", "breakWords", "parentNode", "offset", "SUPPORT_RANGE_BOUNDS", "getRangeBounds", "replacementNode", "splitText", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setStart", "setEnd", "ForeignObject<PERSON><PERSON><PERSON>", "xmlns", "createElementNS", "foreignObject", "setAttributeNS", "_cssLineBreak", "get", "breaker", "LineBreaker", "words", "bk", "FontMetrics", "_data", "_document", "span", "verticalAlign", "offsetTop", "lineHeight", "_parseMetrics", "Proxy", "SUPPORT_CORS_XHR", "SUPPORT_RESPONSE_TYPE", "xhr", "XDomainRequest", "status", "response", "reader", "FileReader", "readAsDataURL", "responseText", "open", "timeout", "ontimeout", "send", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Renderer2", "_ForeignObjectRenderer2", "_<PERSON><PERSON>", "_Font", "windowBounds", "documentBackgroundColor", "bodyBackgroundColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SUPPORT_FOREIGNOBJECT_DRAWING", "supportForeignObject", "cloner", "DocumentCloner", "inlineFonts", "ready", "render", "cloneWindow", "clonedElement", "stack", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clonedDocument", "imageStore", "renderOptions", "all", "_StackingContext2", "parseNodeTree", "IGNORED_NODE_NAMES", "nextNode", "childNode", "nextS<PERSON>ling", "Text", "HTMLElement", "nodeName", "isVisible", "SHOULD_TRAVERSE_CHILDREN", "treatAsRealStackingContext", "createsRealStackingContext", "createsStackingContext", "parentStack", "getRealParentStackingContext", "childS<PERSON>ck", "contexts", "children", "_container", "_treatAsRealStackingContext", "_parentStack", "_childStack", "isPositionedWithZIndex", "isBodyWithTransparentRoot", "StackingContext", "getOpacity", "Size", "_Vector", "lerp", "t", "BezierCurve", "firstHalf", "ab", "bc", "cd", "abbc", "bccd", "dest", "_value$split$map", "create", "_value$split$map2", "horizontal", "vertical", "BLOCK", "RUN_IN", "FLOW", "FLOW_ROOT", "TABLE", "FLEX", "GRID", "RUBY", "SUBGRID", "TABLE_ROW_GROUP", "TABLE_HEADER_GROUP", "TABLE_FOOTER_GROUP", "TABLE_ROW", "TABLE_CELL", "TABLE_COLUMN_GROUP", "TABLE_COLUMN", "TABLE_CAPTION", "RUBY_BASE", "RUBY_TEXT", "RUBY_BASE_CONTAINER", "RUBY_TEXT_CONTAINER", "CONTENTS", "setDisplayBit", "parseDisplayValue", "INLINE_START", "INLINE_END", "weight", "parseFontWeight", "LINE_BREAK", "STRICT", "SCROLL", "NUMBER", "currentValue", "<PERSON><PERSON><PERSON><PERSON>", "shadows", "numParens", "appendValue", "appendShadow", "splice", "toFloat", "MATRIX", "parseTransformOrigin", "parseTransformMatrix", "webkitTransform", "mozTransform", "msTransform", "oTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "mozTransformOrigin", "msTransformOrigin", "oTransformOrigin", "origin", "matrix3d", "COLLAPSE", "WORD_BREAK", "BREAK_ALL", "KEEP_ALL", "order", "_LineBreak", "inlineBreakOpportunities", "lineBreakAtIndex", "codePointsToCharacterClasses", "UnicodeTrie", "BREAK_ALLOWED", "BREAK_NOT_ALLOWED", "BREAK_MANDATORY", "classes", "LETTER_NUMBER_MODIFIER", "_Trie", "_linebreakTrie", "_linebreakTrie2", "SP", "BA", "HY", "CL", "CP", "EX", "IN", "NS", "OP", "IS", "NU", "PO", "PR", "SY", "AL", "EB", "EM", "H2", "H3", "ID", "JL", "JV", "JT", "SA", "BK", "CR", "LF", "CM", "NL", "SG", "WJ", "ZW", "GL", "ZWJ", "B2", "BB", "CB", "QU", "AI", "CJ", "HL", "RI", "XX", "createTrieFromBase64", "ALPHABETICS", "HARD_LINE_BREAKS", "SPACE", "PREFIX_POSTFIX", "LINE_BREAKS", "KOREAN_SYLLABLE_BLOCK", "HYPHEN", "types", "indicies", "categories", "classType", "prev", "isAdjacentWithSpaceIgnored", "currentIndex", "classTypes", "current", "n", "_next", "previousNonSpaceClassType", "_lineBreakAtIndex", "forbiddenBreaks", "beforeIndex", "afterIndex", "before", "prevIndex", "_prevIndex", "_type", "count", "cssFormattedClasses", "_codePointsToCharacte", "_codePointsToCharacte2", "_codePointsToCharacte3", "_codePointsToCharacte4", "isLetterNumber", "Break", "output", "_cssFormattedClasses", "_cssFormattedClasses2", "forbiddenBreakpoints", "_codePoints", "required", "arr2", "_toConsumableArray", "_cssFormattedClasses3", "_cssFormattedClasses4", "lastEnd", "nextIndex", "<PERSON><PERSON>", "UTRIE2_INDEX_2_MASK", "UTRIE2_INDEX_2_BLOCK_LENGTH", "UTRIE2_OMITTED_BMP_INDEX_1_LENGTH", "UTRIE2_INDEX_1_OFFSET", "UTRIE2_UTF8_2B_INDEX_2_LENGTH", "UTRIE2_UTF8_2B_INDEX_2_OFFSET", "UTRIE2_INDEX_2_BMP_LENGTH", "UTRIE2_LSCP_INDEX_2_LENGTH", "UTRIE2_DATA_MASK", "UTRIE2_DATA_BLOCK_LENGTH", "UTRIE2_LSCP_INDEX_2_OFFSET", "UTRIE2_SHIFT_1_2", "UTRIE2_INDEX_SHIFT", "UTRIE2_SHIFT_1", "UTRIE2_SHIFT_2", "view32", "Uint32Array", "view16", "Uint16Array", "<PERSON><PERSON><PERSON><PERSON>", "initialValue", "errorValue", "highStart", "highValueIndex", "ix", "Circle", "_Gradient", "_TextContainer", "<PERSON><PERSON><PERSON>", "renderNodeBackgroundAndBorders", "renderNodeContent", "child", "renderTextNode", "drawShape", "_image", "contentBox", "_width", "_height", "paths", "HAS_BACKGROUND", "hasRenderableBorders", "some", "backgroundPaintingArea", "renderBackgroundImage", "renderBorder", "renderBackgroundRepeat", "renderBackgroundGradient", "backgroundImageSize", "_offsetX", "_offsetY", "renderRepeat", "gradientBounds", "parseGradient", "GRADIENT_TYPE", "LINEAR_GRADIENT", "renderLinearGradient", "RADIAL_GRADIENT", "renderRadialGradient", "curvePoints", "_opacity", "setOpacity", "renderStackContent", "_splitStackingContext", "splitStackingContexts", "_splitStackingContext2", "negativeZIndex", "zeroOrAutoZIndexOrTransformedOrOpacity", "positiveZIndex", "nonPositionedFloats", "nonPositionedInlineLevel", "_splitDescendants", "splitDescendants", "_splitDescendants2", "inlineLevel", "nonInlineLevel", "sort", "sortByZIndex", "renderStack", "renderNode", "get<PERSON><PERSON><PERSON>", "isInlineLevel", "transformWebkitRadialGradientArgs", "RadialGrad<PERSON>", "LinearGradient", "RADIAL_GRADIENT_SHAPE", "_<PERSON>le", "SIDE_OR_CORNER", "PERCENTAGE_ANGLES", "ENDS_WITH_LENGTH", "FROM_TO_COLORSTOP", "RADIAL_SHAPE_DEFINITION", "ELLIPSE", "LENGTH_FOR_POSITION", "shape", "parseColorStops", "parseLinearGradient", "transformObsoleteColorStops", "parseRadialGradient", "firstColorStopIndex", "lineLength", "HAS_LENGTH", "lastSpaceIndex", "lastIndexOf", "_color", "_stop", "absoluteValuedColorStops", "previousColorStop", "_stop2", "steps", "stepSize", "hasPrefix", "angle", "parseAngle", "HAS_SIDE_OR_CORNER", "HAS_DIRECTION", "calculateGradientDirection", "parseSide<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsePercentageAngle", "gradientCenter", "gradientRadius", "calculateRadius", "radian", "HALF_WIDTH", "HALF_HEIGHT", "HALF_LINE_LENGTH", "sin", "cos", "parseTopRight", "acos", "_angle$split$map", "_angle$split$map2", "ratio", "atan", "<PERSON><PERSON><PERSON><PERSON>", "closest", "stat", "corner", "d", "optimumDistance", "opti<PERSON><PERSON><PERSON><PERSON>", "Infinity", "extent", "rx", "ry", "_c", "_corner", "idx", "RADIUS", "matchStartPosition", "matchShapeExtent", "matchStartRadius", "matchEndPosition", "matchEndRadius", "matchPosition", "matchRadius", "filter", "ANGLE", "_Proxy", "_ResourceLoader2", "_P<PERSON>udo<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyInline", "renderer", "referenceElement", "scrolledElements", "copyStyles", "inlineImages", "pseudoContentData", "counters", "quote<PERSON><PERSON><PERSON>", "inlineImage", "backgroundImages", "HTMLImageElement", "clone<PERSON><PERSON><PERSON><PERSON>", "styleSheets", "sheet", "href", "res", "createStyleSheetFontsFromText", "getSheetFonts", "fonts", "acc", "formats", "blob", "dataUri", "fontFace", "cssText", "fontCss", "textContent", "HTMLCanvasElement", "HTMLIFrameElement", "tempIframe", "generateIframeKey", "_parseBounds", "cache", "getIframeDocumentElement", "<PERSON>rame<PERSON><PERSON><PERSON>", "HTMLStyleElement", "cssRules", "css", "rule", "clone", "nodeType", "Node", "TEXT_NODE", "nodeValue", "createElementClone", "styleBefore", "styleAfter", "clonedReferenceElement", "HTMLBodyElement", "createPseudoHideStyles", "parseCounterReset", "contentBefore", "resolvePseudoContent", "ELEMENT_NODE", "ignoreElements", "contentAfter", "popCounters", "inlineAllImages", "inlinePseudoElement", "PSEUDO_BEFORE", "PSEUDO_AFTER", "scrollTop", "scrollLeft", "cloneCanvasContents", "CSSRule", "FONT_FACE_RULE", "format", "baseHref", "doc", "implementation", "createHTMLDocument", "base", "head", "clonedCan<PERSON>", "clonedCtx", "putImageData", "contentItems", "pseudoElt", "content", "anonymousReplacedElement", "PSEUDO_CONTENT_ITEM_TYPE", "IMAGE", "TEXT", "className", "PSEUDO_HIDE_ELEMENT_CLASS_BEFORE", "PSEUDO_HIDE_ELEMENT_CLASS_AFTER", "insertBefore", "createStyles", "styles", "innerHTML", "initNode", "random", "DATA_URI_REGEXP", "contentWindow", "html", "atob", "decodeURIComponent", "createIframeContainer", "cloneIframeContainer", "documentClone", "write", "iframeLoad", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "close", "scrolling", "onreadystatechange", "interval", "setInterval", "readyState", "clearInterval", "serializeDoctype", "scrollTo", "navigator", "userAgent", "onclone", "doctype", "restoreOwnerScroll", "adoptNode", "name", "internalSubset", "publicId", "systemId", "ResourceStore", "Resource<PERSON><PERSON>der", "_window", "<PERSON><PERSON><PERSON><PERSON>", "location", "_index", "hasResourceInCache", "isSVG", "SUPPORT_SVG_DRAWING", "isInlineImage", "isSameOrigin", "addImage", "_loadImage", "SUPPORT_CORS_IMAGES", "xhrImage", "imageLoadHandler", "supportsDataImages", "isInlineBase64Image", "SUPPORT_BASE64_DRAWING", "url", "link", "_link", "protocol", "hostname", "port", "_this5", "images", "resources", "_keys", "_resources", "INLINE_SVG", "INLINE_BASE64", "INLINE_IMG", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TOKEN_TYPE", "STRING", "ATTRIBUTE", "URL", "COUNTER", "COUNTERS", "OPENQUOTE", "CLOSEQUOTE", "counterReset", "counterNames", "counterResets", "lenCounterResets", "_counterResets$i$spli", "_counterResets$i$spli2", "counterName", "counter", "len<PERSON><PERSON><PERSON>s", "pop", "tokens", "counterIncrement", "_counterIncrement$spl", "_counterIncrement$spl2", "incrementValue", "token", "_counter", "formatCounterValue", "_counters", "glue", "getQuote", "isString", "isEscaped", "isFunction", "functionName", "char<PERSON>t", "_counters2", "addOtherToken", "identifier", "isOpening", "quotes"], "mappings": "4FAEA,IAAIA,EAAWC,OAAOC,QAAU,SAAUC,GAAU,IAAK,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAAE,IAAIG,EAASF,UAAUD,GAAI,IAAK,IAAII,KAAOD,EAAcN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAAQL,EAAOK,GAAOD,EAAOC,IAAY,OAAOL,GAInPS,EAAmBC,EAFD,EAAQ,OAM1BC,EAAWD,EAFD,EAAQ,OAIlBE,EAAU,EAAQ,MAElBC,EAAU,EAAQ,MAEtB,SAASH,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAIG,EAAc,SAAqBC,EAASC,GAC5C,IAAIC,EAASD,GAAQ,GACjBE,EAAS,IAAIV,EAASK,QAAkC,kBAAnBI,EAAOE,SAAwBF,EAAOE,SAC/ED,EAAOE,IAAI,oCAMX,IAAIC,EAAgBN,EAAQM,cAC5B,IAAKA,EACD,OAAOC,QAAQC,OAAO,6CAE1B,IAAIC,EAAcH,EAAcG,YAE5BC,EAAUD,EAAYE,YACtBC,EAAUH,EAAYI,YAItBC,EAFiC,SAApBd,EAAQe,SAA0C,SAApBf,EAAQe,SAE/B,EAAIpB,EAAQqB,mBAAmBV,IAAiB,EAAIX,EAAQsB,aAAajB,EAASU,EAASE,GAC/GM,EAAQJ,EAAKI,MACbC,EAASL,EAAKK,OACdC,EAAON,EAAKM,KACZC,EAAMP,EAAKO,IAEXC,EAAiB,CACjBC,OAAO,EACPC,YAAY,EACZC,gBAAiB,UACjBC,aAAc,KACdtB,SAAS,EACTuB,MAAO,KACPC,iBAAiB,EACjBC,wBAAwB,EACxBC,MAAOrB,EAAYsB,kBAAoB,EACvCjD,OAAQ,IAAIS,EAAiBO,QAAQI,EAAO8B,QAC5CC,SAAS,EACTC,EAAGd,EACHe,EAAGd,EACHH,MAAOkB,KAAKC,KAAKnB,GACjBC,OAAQiB,KAAKC,KAAKlB,GAClBmB,YAAa7B,EAAY8B,WACzBC,aAAc/B,EAAYgC,YAC1B/B,QAASD,EAAYE,YACrBC,QAASH,EAAYI,aAWzB,OARa,EAAInB,EAAQgD,eAAe1C,EAASrB,EAAS,GAAI2C,EAAgBpB,GAASC,IAW3FJ,EAAY4C,eAAiBpD,EAAiBO,QAE9C8C,EAAOC,QAAU9C,G,kCC3EjBnB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAIC,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAInB,IAAIG,EAAO,oBASPC,EAAO,oBASPC,EAAM,2DASNC,EAAO,8EAkBPC,EAAQ,WACR,SAASA,EAAMlC,IAhDnB,SAAyBmC,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAiDxGiB,CAAgBC,KAAMH,GAEtB,IAb2BI,EAavBvE,EAAOoC,MAAMC,QAAQJ,IAbEsC,EAaiBtC,EAZzC,CAACX,KAAKkD,IAAID,EAAM,GAAI,KAAMjD,KAAKkD,IAAID,EAAM,GAAI,KAAMjD,KAAKkD,IAAID,EAAM,GAAI,KAAMA,EAAMpG,OAAS,EAAIoG,EAAM,GAAK,OApC1G,SAActC,GACrB,IAAIwC,EAAQxC,EAAMwC,MAAMV,GACxB,QAAIU,GACO,CAACC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAKC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAKC,SAASD,EAAM,GAAG,GAAKA,EAAM,GAAG,GAAI,IAAK,MA6C9EE,CAAK1C,IA9BxD,SAAaA,GACnB,IAAIwC,EAAQxC,EAAMwC,MAAMR,GACxB,QAAIQ,GACO,CAACG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAK,MA2BMI,CAAI5C,IArBrE,SAAcA,GACrB,IAAIwC,EAAQxC,EAAMwC,MAAMP,GACxB,SAAIO,GAASA,EAAMtG,OAAS,IACjB,CAACyG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,IAAKG,OAAOH,EAAM,KAkBOK,CAAK7C,KAR/E8C,EAQoG9C,EARlF+C,iBACP,IAhChB,SAAc/C,GACrB,IAAIwC,EAAQxC,EAAMwC,MAAMT,GACxB,QAAIS,GACO,CAACC,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAKP,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAKP,SAASD,EAAM,GAAGQ,UAAU,EAAG,GAAI,IAAK,MAoCVC,CAAKjD,IAAU,CAAC,EAAG,EAAG,EAAG,MAC3IkD,EAAQjD,EAAelC,EAAM,GAC7BoF,EAAID,EAAM,GACVE,EAAIF,EAAM,GACVG,EAAIH,EAAM,GACVI,EAAIJ,EAAM,GAEdb,KAAKc,EAAIA,EACTd,KAAKe,EAAIA,EACTf,KAAKgB,EAAIA,EACThB,KAAKiB,EAAIA,EAeb,OAZAlC,EAAac,EAAO,CAAC,CACjB9F,IAAK,gBACL4D,MAAO,WACH,OAAkB,IAAXqC,KAAKiB,IAEjB,CACClH,IAAK,WACL4D,MAAO,WACH,OAAkB,OAAXqC,KAAKiB,GAAyB,IAAXjB,KAAKiB,EAAU,QAAUjB,KAAKc,EAAI,IAAMd,KAAKe,EAAI,IAAMf,KAAKgB,EAAI,IAAMhB,KAAKiB,EAAI,IAAM,OAASjB,KAAKc,EAAI,IAAMd,KAAKe,EAAI,IAAMf,KAAKgB,EAAI,QAIhKnB,EA7BC,GAgCZpC,EAAQ/C,QAAUmF,EAGlB,IAAIY,EAAe,CACfS,YAAa,CAAC,EAAG,EAAG,EAAG,GACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,EAAG,EAAG,EAAG,MACjBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,MAAO,CAAC,IAAK,GAAI,GAAI,MACrBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,MAAO,CAAC,IAAK,IAAK,GAAI,MACtBC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,GAAI,GAAI,MACvBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,SAAU,CAAC,EAAG,EAAG,IAAK,MACtBC,SAAU,CAAC,EAAG,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,GAAI,MAC9BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,EAAG,IAAK,EAAG,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,EAAG,IAAK,MAC3BC,eAAgB,CAAC,GAAI,IAAK,GAAI,MAC9BC,WAAY,CAAC,IAAK,IAAK,EAAG,MAC1BC,WAAY,CAAC,IAAK,GAAI,IAAK,MAC3BC,QAAS,CAAC,IAAK,EAAG,EAAG,MACrBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,cAAe,CAAC,GAAI,GAAI,IAAK,MAC7BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,GAAI,GAAI,GAAI,MAC5BC,cAAe,CAAC,EAAG,IAAK,IAAK,MAC7BC,WAAY,CAAC,IAAK,EAAG,IAAK,MAC1BC,SAAU,CAAC,IAAK,GAAI,IAAK,MACzBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,WAAY,CAAC,GAAI,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,YAAa,CAAC,GAAI,IAAK,GAAI,MAC3BC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,KAAM,CAAC,IAAK,IAAK,EAAG,MACpBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,MAAO,CAAC,EAAG,IAAK,EAAG,MACnBC,YAAa,CAAC,IAAK,IAAK,GAAI,MAC5BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,GAAI,MACzBC,OAAQ,CAAC,GAAI,EAAG,IAAK,MACrBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,EAAG,MACzBC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,qBAAsB,CAAC,IAAK,IAAK,IAAK,MACtCC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,cAAe,CAAC,GAAI,IAAK,IAAK,MAC9BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,eAAgB,CAAC,IAAK,IAAK,IAAK,MAChCC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,IAAK,EAAG,MAClBC,UAAW,CAAC,GAAI,IAAK,GAAI,MACzBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,QAAS,CAAC,IAAK,EAAG,IAAK,MACvBC,OAAQ,CAAC,IAAK,EAAG,EAAG,MACpBC,iBAAkB,CAAC,IAAK,IAAK,IAAK,MAClCC,WAAY,CAAC,EAAG,EAAG,IAAK,MACxBC,aAAc,CAAC,IAAK,GAAI,IAAK,MAC7BC,aAAc,CAAC,IAAK,IAAK,IAAK,MAC9BC,eAAgB,CAAC,GAAI,IAAK,IAAK,MAC/BC,gBAAiB,CAAC,IAAK,IAAK,IAAK,MACjCC,kBAAmB,CAAC,EAAG,IAAK,IAAK,MACjCC,gBAAiB,CAAC,GAAI,IAAK,IAAK,MAChCC,gBAAiB,CAAC,IAAK,GAAI,IAAK,MAChCC,aAAc,CAAC,GAAI,GAAI,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,IAAK,IAAK,MAC7BC,KAAM,CAAC,EAAG,EAAG,IAAK,MAClBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,MAAO,CAAC,IAAK,IAAK,EAAG,MACrBC,UAAW,CAAC,IAAK,IAAK,GAAI,MAC1BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,UAAW,CAAC,IAAK,GAAI,EAAG,MACxBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,cAAe,CAAC,IAAK,IAAK,IAAK,MAC/BC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,GAAI,MACrBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,EAAG,IAAK,MACtBC,cAAe,CAAC,IAAK,GAAI,IAAK,MAC9BC,IAAK,CAAC,IAAK,EAAG,EAAG,MACjBC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,YAAa,CAAC,IAAK,GAAI,GAAI,MAC3BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,WAAY,CAAC,IAAK,IAAK,GAAI,MAC3BC,SAAU,CAAC,GAAI,IAAK,GAAI,MACxBC,SAAU,CAAC,IAAK,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,UAAW,CAAC,IAAK,GAAI,IAAK,MAC1BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,UAAW,CAAC,IAAK,IAAK,IAAK,MAC3BC,KAAM,CAAC,IAAK,IAAK,IAAK,MACtBC,YAAa,CAAC,EAAG,IAAK,IAAK,MAC3BC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,IAAK,CAAC,IAAK,IAAK,IAAK,MACrBC,KAAM,CAAC,EAAG,IAAK,IAAK,MACpBC,QAAS,CAAC,IAAK,IAAK,IAAK,MACzBC,OAAQ,CAAC,IAAK,GAAI,GAAI,MACtBC,UAAW,CAAC,GAAI,IAAK,IAAK,MAC1BC,OAAQ,CAAC,IAAK,IAAK,IAAK,MACxBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,MAAO,CAAC,IAAK,IAAK,IAAK,MACvBC,WAAY,CAAC,IAAK,IAAK,IAAK,MAC5BC,OAAQ,CAAC,IAAK,IAAK,EAAG,MACtBC,YAAa,CAAC,IAAK,IAAK,GAAI,OAGd7M,EAAQ8M,YAAc,IAAI1K,EAAM,CAAC,EAAG,EAAG,EAAG,K,kCCpP5DrG,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ+M,iBAAmB/M,EAAQgN,wBAA0BhN,EAAQiN,uBAAyBjN,EAAQkN,mBAAqBlN,EAAQ7B,kBAAoB6B,EAAQmN,oBAAsBnN,EAAQoN,oBAAsBpN,EAAQ5B,YAAc4B,EAAQqN,YAASxM,EAE1P,IAAIS,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAIfyL,EAAW3Q,EAFD,EAAQ,OAMlB4Q,EAAgB5Q,EAFD,EAAQ,OAI3B,SAASA,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAIvF,IAQIsQ,EAASrN,EAAQqN,OAAS,WAC1B,SAASA,EAAOhO,EAAGC,EAAGkO,EAAGC,IAX7B,SAAyBpL,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAYxGiB,CAAgBC,KAAM8K,GAEtB9K,KAAKhE,KAAOc,EACZkD,KAAK/D,IAAMc,EACXiD,KAAKlE,MAAQmP,EACbjL,KAAKjE,OAASmP,EAUlB,OAPAnM,EAAa+L,EAAQ,KAAM,CAAC,CACxB/Q,IAAK,iBACL4D,MAAO,SAAwBwN,EAAY7P,EAASE,GAChD,OAAO,IAAIsP,EAAOK,EAAWnP,KAAOV,EAAS6P,EAAWlP,IAAMT,EAAS2P,EAAWrP,MAAOqP,EAAWpP,YAIrG+O,EAjBmB,GAkE1BM,GA9Cc3N,EAAQ5B,YAAc,SAAqBwP,EAAM/P,EAASE,GACxE,OAAOsP,EAAOQ,eAAeD,EAAKE,wBAAyBjQ,EAASE,IAG9CiC,EAAQoN,oBAAsB,SAA6BW,EAAQC,GACzF,OAAO,IAAIX,EAAOU,EAAOxP,KAAOyP,EA9BzB,GA8BuCC,YAAaF,EAAOvP,IAAMwP,EAjClE,GAiC+EC,YAAaF,EAAO1P,OAAS2P,EAhC1G,GAgCyHC,YAAcD,EA9BxI,GA8BsJC,aAAcF,EAAOzP,QAAU0P,EAjCtL,GAiCmMC,YAAcD,EA/B9M,GA+B8NC,eAGjNjO,EAAQmN,oBAAsB,SAA6BY,EAAQG,EAASF,GAElG,IAAIG,EAAaD,EAtCX,GAsCwBhO,MAC1BkO,EAAeF,EAtCX,GAsC0BhO,MAC9BmO,EAAgBH,EAtCX,GAsC2BhO,MAChCoO,EAAcJ,EAtCX,GAsCyBhO,MAEhC,OAAO,IAAImN,EAAOU,EAAOxP,KAAO+P,EAAcN,EAxCvC,GAwCqDC,YAAaF,EAAOvP,IAAM2P,EAAaH,EA3C7F,GA2C0GC,YAAaF,EAAO1P,OAAS2P,EA1CrI,GA0CoJC,YAAcD,EAxCnK,GAwCiLC,YAAcK,EAAcF,GAAeL,EAAOzP,QAAU0P,EA3C9O,GA2C2PC,YAAcD,EAzCtQ,GAyCsRC,YAAcE,EAAaE,KAGtSrO,EAAQ7B,kBAAoB,SAA2BoQ,GAC3E,IAAIC,EAAOD,EAASC,KAChBC,EAAkBF,EAASE,gBAE/B,IAAKD,IAASC,EACV,MAAM,IAAIC,MAA8E,IAE5F,IAAIrQ,EAAQkB,KAAKoP,IAAIpP,KAAKoP,IAAIH,EAAKI,YAAaH,EAAgBG,aAAcrP,KAAKoP,IAAIH,EAAKK,YAAaJ,EAAgBI,aAActP,KAAKoP,IAAIH,EAAKM,YAAaL,EAAgBK,cAE9KxQ,EAASiB,KAAKoP,IAAIpP,KAAKoP,IAAIH,EAAKO,aAAcN,EAAgBM,cAAexP,KAAKoP,IAAIH,EAAKQ,aAAcP,EAAgBO,cAAezP,KAAKoP,IAAIH,EAAKS,aAAcR,EAAgBQ,eAExL,OAAO,IAAI5B,EAAO,EAAG,EAAGhP,EAAOC,IAGV0B,EAAQkN,mBAAqB,SAA4BgC,EAAQC,GACtF,OAAQA,GACJ,KA9DE,EA+DE,OAAOxB,EAAqBuB,EAAOE,aAAcF,EAAOG,aAAcH,EAAOI,cAAeJ,EAAOK,eACvG,KA/DI,EAgEA,OAAO5B,EAAqBuB,EAAOI,cAAeJ,EAAOK,cAAeL,EAAOM,iBAAkBN,EAAOO,kBAC5G,KAhEK,EAiED,OAAO9B,EAAqBuB,EAAOM,iBAAkBN,EAAOO,iBAAkBP,EAAOQ,gBAAiBR,EAAOS,iBACjH,KAjEG,EAkEH,QACI,OAAOhC,EAAqBuB,EAAOQ,gBAAiBR,EAAOS,gBAAiBT,EAAOE,aAAcF,EAAOG,gBAIzF,SAA8BO,EAAQC,EAAQC,EAAQC,GAC7E,IAAIC,EAAO,GAyBX,OAxBIJ,aAAkBrC,EAActQ,QAChC+S,EAAK9O,KAAK0O,EAAOK,UAAU,IAAK,IAEhCD,EAAK9O,KAAK0O,GAGVE,aAAkBvC,EAActQ,QAChC+S,EAAK9O,KAAK4O,EAAOG,UAAU,IAAK,IAEhCD,EAAK9O,KAAK4O,GAGVC,aAAkBxC,EAActQ,QAChC+S,EAAK9O,KAAK6O,EAAOE,UAAU,IAAK,GAAMC,WAEtCF,EAAK9O,KAAK6O,GAGVF,aAAkBtC,EAActQ,QAChC+S,EAAK9O,KAAK2O,EAAOI,UAAU,IAAK,GAAOC,WAEvCF,EAAK9O,KAAK2O,GAGPG,IAwDPG,GArDyBnQ,EAAQiN,uBAAyB,SAAgCiC,GAC1F,MAAO,CAACA,EAAOE,aAAcF,EAAOI,cAAeJ,EAAOM,iBAAkBN,EAAOQ,kBAGzD1P,EAAQgN,wBAA0B,SAAiCkC,GAC7F,MAAO,CAACA,EAAOG,aAAcH,EAAOK,cAAeL,EAAOO,iBAAkBP,EAAOS,kBAGhE3P,EAAQ+M,iBAAmB,SAA0BgB,EAAQC,EAASoC,GACzF,IAAIC,EAAMD,EAAaD,EAAOG,UA3G1B,GA2GuCC,iBAAiBxC,EAAO1P,OAC/DmS,EAAMJ,EAAaD,EAAOG,UA3G1B,GA2GuCC,iBAAiBxC,EAAOzP,QAC/DmS,EAAML,EAAaD,EAAOO,WA7G1B,GA6GwCH,iBAAiBxC,EAAO1P,OAChEsS,EAAMP,EAAaD,EAAOO,WA7G1B,GA6GwCH,iBAAiBxC,EAAOzP,QAChEsS,EAAMR,EAAaD,EAAOU,cA/G1B,GA+G2CN,iBAAiBxC,EAAO1P,OACnEyS,EAAMV,EAAaD,EAAOU,cA/G1B,GA+G2CN,iBAAiBxC,EAAOzP,QACnEyS,EAAMX,EAAaD,EAAOa,aAjH1B,GAiH0CT,iBAAiBxC,EAAO1P,OAClE4S,EAAMb,EAAaD,EAAOa,aAjH1B,GAiH0CT,iBAAiBxC,EAAOzP,QAElE4S,EAAU,GACdA,EAAQhQ,MAAMmP,EAAMI,GAAO1C,EAAO1P,OAClC6S,EAAQhQ,MAAM6P,EAAMH,GAAO7C,EAAO1P,OAClC6S,EAAQhQ,MAAMsP,EAAMS,GAAOlD,EAAOzP,QAClC4S,EAAQhQ,MAAMyP,EAAMG,GAAO/C,EAAOzP,QAClC,IAAI6S,EAAY5R,KAAKoP,IAAIyC,MAAM7R,KAAM2R,GAEjCC,EAAY,IACZd,GAAOc,EACPX,GAAOW,EACPV,GAAOU,EACPR,GAAOQ,EACPP,GAAOO,EACPL,GAAOK,EACPJ,GAAOI,EACPF,GAAOE,GAGX,IAAIE,EAAWtD,EAAO1P,MAAQoS,EAC1Ba,EAAcvD,EAAOzP,OAASwS,EAC9BS,EAAcxD,EAAO1P,MAAQuS,EAC7BY,EAAazD,EAAOzP,OAAS2S,EAEjC,MAAO,CACH7B,aAAciB,EAAM,GAAKG,EAAM,EAAIiB,EAAe1D,EAAOxP,KAAMwP,EAAOvP,IAAK6R,EAAKG,EAAKL,EAAOG,UAAY,IAAIhD,EAASrQ,QAAQ8Q,EAAOxP,KAAMwP,EAAOvP,KACjJ6Q,aAAcgB,EAAM,GAAKG,EAAM,EAAIiB,EAAe1D,EAAOxP,KAAOyP,EA/I7D,GA+I2EC,YAAaF,EAAOvP,IAAMwP,EAlJtG,GAkJmHC,YAAa1O,KAAKoP,IAAI,EAAG0B,EAAMrC,EA/IjJ,GA+I+JC,aAAc1O,KAAKoP,IAAI,EAAG6B,EAAMxC,EAlJhM,GAkJ6MC,aAAckC,EAAOG,UAAY,IAAIhD,EAASrQ,QAAQ8Q,EAAOxP,KAAOyP,EA/IhR,GA+I8RC,YAAaF,EAAOvP,IAAMwP,EAlJzT,GAkJsUC,aACxUqB,cAAemB,EAAM,GAAKE,EAAM,EAAIc,EAAe1D,EAAOxP,KAAO8S,EAAUtD,EAAOvP,IAAKiS,EAAKE,EAAKR,EAAOO,WAAa,IAAIpD,EAASrQ,QAAQ8Q,EAAOxP,KAAOwP,EAAO1P,MAAO0P,EAAOvP,KAC7K+Q,cAAekB,EAAM,GAAKE,EAAM,EAAIc,EAAe1D,EAAOxP,KAAOgB,KAAKkD,IAAI4O,EAAUtD,EAAO1P,MAAQ2P,EAjJhG,GAiJ8GC,aAAcF,EAAOvP,IAAMwP,EApJ1I,GAoJuJC,YAAaoD,EAAWtD,EAAO1P,MAAQ2P,EAjJ7L,GAiJ2MC,YAAc,EAAIwC,EAAMzC,EAjJnO,GAiJiPC,YAAa0C,EAAM3C,EApJrQ,GAoJkRC,YAAakC,EAAOO,WAAa,IAAIpD,EAASrQ,QAAQ8Q,EAAOxP,KAAOwP,EAAO1P,MAAQ2P,EAnJnW,GAmJkXC,YAAaF,EAAOvP,IAAMwP,EApJ9Y,GAoJ2ZC,aAC7ZuB,iBAAkBoB,EAAM,GAAKE,EAAM,EAAIW,EAAe1D,EAAOxP,KAAOgT,EAAaxD,EAAOvP,IAAM8S,EAAaV,EAAKE,EAAKX,EAAOU,cAAgB,IAAIvD,EAASrQ,QAAQ8Q,EAAOxP,KAAOwP,EAAO1P,MAAO0P,EAAOvP,IAAMuP,EAAOzP,QACjNmR,iBAAkBmB,EAAM,GAAKE,EAAM,EAAIW,EAAe1D,EAAOxP,KAAOgB,KAAKkD,IAAI8O,EAAaxD,EAAO1P,MAAQ2P,EAnJtG,GAmJoHC,aAAcF,EAAOvP,IAAMe,KAAKkD,IAAI6O,EAAavD,EAAOzP,OAAS0P,EAtJtL,GAsJmMC,aAAc1O,KAAKoP,IAAI,EAAGiC,EAAM5C,EArJjO,GAqJgPC,aAAc6C,EAAM9C,EApJnQ,GAoJmRC,YAAakC,EAAOU,cAAgB,IAAIvD,EAASrQ,QAAQ8Q,EAAOxP,KAAOwP,EAAO1P,MAAQ2P,EArJ1W,GAqJyXC,YAAaF,EAAOvP,IAAMuP,EAAOzP,OAAS0P,EApJla,GAoJkbC,aACvbyB,gBAAiBqB,EAAM,GAAKE,EAAM,EAAIQ,EAAe1D,EAAOxP,KAAMwP,EAAOvP,IAAMgT,EAAYT,EAAKE,EAAKd,EAAOa,aAAe,IAAI1D,EAASrQ,QAAQ8Q,EAAOxP,KAAMwP,EAAOvP,IAAMuP,EAAOzP,QACjLqR,gBAAiBoB,EAAM,GAAKE,EAAM,EAAIQ,EAAe1D,EAAOxP,KAAOyP,EArJhE,GAqJ8EC,YAAaF,EAAOvP,IAAMgT,EAAYjS,KAAKoP,IAAI,EAAGoC,EAAM/C,EArJtI,GAqJoJC,aAAcgD,EAAMjD,EAtJtK,GAsJsLC,YAAakC,EAAOa,aAAe,IAAI1D,EAASrQ,QAAQ8Q,EAAOxP,KAAOyP,EArJ9P,GAqJ4QC,YAAaF,EAAOvP,IAAMuP,EAAOzP,OAAS0P,EAtJpT,GAsJoUC,eAIpU,CACTqC,SAAU,EACVI,UAAW,EACXG,aAAc,EACdG,YAAa,IAGbS,EAAiB,SAAwBpS,EAAGC,EAAGoS,EAAIC,EAAIC,GACvD,IAAIC,GAActS,KAAKuS,KAAK,GAAK,GAAK,EAA1B,EACRC,EAAKL,EAAKG,EACVG,EAAKL,EAAKE,EACVI,EAAK5S,EAAIqS,EACTQ,EAAK5S,EAAIqS,EAEb,OAAQC,GACJ,KAAKzB,EAAOG,SACR,OAAO,IAAI/C,EAActQ,QAAQ,IAAIqQ,EAASrQ,QAAQoC,EAAG6S,GAAK,IAAI5E,EAASrQ,QAAQoC,EAAG6S,EAAKF,GAAK,IAAI1E,EAASrQ,QAAQgV,EAAKF,EAAIzS,GAAI,IAAIgO,EAASrQ,QAAQgV,EAAI3S,IAC/J,KAAK6Q,EAAOO,UACR,OAAO,IAAInD,EAActQ,QAAQ,IAAIqQ,EAASrQ,QAAQoC,EAAGC,GAAI,IAAIgO,EAASrQ,QAAQoC,EAAI0S,EAAIzS,GAAI,IAAIgO,EAASrQ,QAAQgV,EAAIC,EAAKF,GAAK,IAAI1E,EAASrQ,QAAQgV,EAAIC,IAC9J,KAAK/B,EAAOU,aACR,OAAO,IAAItD,EAActQ,QAAQ,IAAIqQ,EAASrQ,QAAQgV,EAAI3S,GAAI,IAAIgO,EAASrQ,QAAQgV,EAAI3S,EAAI0S,GAAK,IAAI1E,EAASrQ,QAAQoC,EAAI0S,EAAIG,GAAK,IAAI5E,EAASrQ,QAAQoC,EAAG6S,IAC9J,KAAK/B,EAAOa,YACZ,QACI,OAAO,IAAIzD,EAActQ,QAAQ,IAAIqQ,EAASrQ,QAAQgV,EAAIC,GAAK,IAAI5E,EAASrQ,QAAQgV,EAAKF,EAAIG,GAAK,IAAI5E,EAASrQ,QAAQoC,EAAGC,EAAI0S,GAAK,IAAI1E,EAASrQ,QAAQoC,EAAGC,O,kCCtMvKvD,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQmS,iCAAmCnS,EAAQoS,iBAAcvR,EAEjE,IAMgC9D,EAN5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEfwQ,EAAiB,EAAQ,OAIGtV,EAFasV,IAEQtV,EAAIC,WAIzD,IAEIoV,EAAcpS,EAAQoS,YAAc,CACpCE,GAAI,EACJC,WAAY,GAGZC,EAAS,WACT,SAASA,EAAOtS,IAVpB,SAAyBmC,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAWxGiB,CAAgBC,KAAMiQ,GAEtBjQ,KAAKkQ,KAA0C,MAAnCvS,EAAMwS,OAAOxS,EAAM9D,OAAS,GAAagW,EAAYG,WAAaH,EAAYE,GAC1F,IAAIK,EAAcC,WAAW1S,GAI7BqC,KAAKrC,MAAQ2S,MAAMF,GAAe,EAAIA,EAoB1C,OAjBArR,EAAakR,EAAQ,CAAC,CAClBlW,IAAK,eACL4D,MAAO,WACH,OAAOqC,KAAKkQ,OAASL,EAAYG,aAEtC,CACCjW,IAAK,mBACL4D,MAAO,SAA0B4S,GAC7B,OAAOvQ,KAAKwQ,eAAiBD,GAAgBvQ,KAAKrC,MAAQ,KAAOqC,KAAKrC,SAE1E,CAAC,CACD5D,IAAK,SACL4D,MAAO,SAAgB8S,GACnB,OAAO,IAAIR,EAAOQ,OAInBR,EA7BE,GAgCbxS,EAAQ/C,QAAUuV,EAQqBxS,EAAQmS,iCAAmC,SAA0Cc,EAAW/S,EAAOgT,GAC1I,OAAQA,GACJ,IAAK,KACL,IAAK,IACD,OAAO,IAAIV,EAAOtS,EAAQgT,GAC9B,IAAK,KACL,IAAK,MACD,IAAI9W,EAAS,IAAIoW,EAAOtS,GAExB,OADA9D,EAAO8D,OAAkB,OAATgT,EAAgBN,WAAWK,EAAUE,MAAMC,KAAKC,UAbtD,SAASC,EAAgBL,GAC3C,IAAIM,EAASN,EAAUM,OACvB,OAAOA,EAASD,EAAgBC,GAAUX,WAAWK,EAAUE,MAAMC,KAAKC,UAWUC,CAAgBL,GACrF7W,EACX,QAEI,OAAO,IAAIoW,EAAO,Q,kCC1E9BzW,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAwDgCnD,EAxD5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf2R,EAAS,EAAQ,MAEjBC,GAoD4B1W,EApDKyW,IAoDgBzW,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAlDnF2W,EAAQ,EAAQ,MAEhBC,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAElBC,EAAgB,EAAQ,MAExBC,EAAW,EAAQ,MAEnBC,EAAS,EAAQ,MAEjBC,EAAQ,EAAQ,MAEhBC,EAAiB,EAAQ,MAEzBC,EAAa,EAAQ,MAErBC,EAAa,EAAQ,MAErBC,EAAU,EAAQ,MAElBC,EAAY,EAAQ,MAEpBC,EAAgB,EAAQ,MAExBC,EAAW,EAAQ,MAEnBC,EAAY,EAAQ,MAEpBC,EAAkB,EAAQ,MAE1BC,EAAc,EAAQ,MAEtBC,EAAiB,EAAQ,MAEzBC,EAAa,EAAQ,MAErBC,EAAc,EAAQ,MAEtBC,EAAa,EAAQ,MAErBC,EAAU,EAAQ,MAElBjY,EAAU,EAAQ,MAElBkY,EAAS,EAAQ,MAEjBC,EAAY,EAAQ,MAMxB,IAAIC,EAAa,CAAC,QAAS,WAAY,UAEnCC,EAAgB,WAChB,SAASA,EAAcvH,EAAM2F,EAAQ6B,EAAgBC,GACjD,IAAIC,EAAQ/S,MANpB,SAAyBF,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAQxGiB,CAAgBC,KAAM4S,GAEtB5S,KAAKgR,OAASA,EACdhR,KAAKrE,QAAU0P,EAAK1P,QACpBqE,KAAK8S,MAAQA,EACb9S,KAAKgT,WAAa,GAClBhT,KAAKiT,UAAY,GACS,iBAAf5H,EAAK6H,QACZlT,KAAKmT,UAAY9H,EAAK6H,OAE1B,IAAI7X,EAAcgQ,EAAKnQ,cAAcG,YACjCC,EAAUD,EAAYE,YACtBC,EAAUH,EAAYI,YACtBmV,EAAQvV,EAAY+X,iBAAiB/H,EAAM,MAC3CgI,GAAU,EAAI9B,EAAS+B,cAAc1C,EAAMyC,SAE3CE,EAAyB,UAAdlI,EAAK6E,MAAkC,aAAd7E,EAAK6E,KAEzCb,GAAW,EAAI4C,EAAUuB,eAAe5C,EAAMvB,UAiClD,GA/BArP,KAAK4Q,MAAQ,CACT6C,WAAYF,EAAWd,EAAOiB,kBAAmB,EAAItC,EAAYuC,iBAAiB/C,EAAOiC,GACzFe,OAAQL,EAAWd,EAAOoB,eAAgB,EAAIxC,EAAQyC,aAAalD,GACnE/C,cAAexC,aAAgBhQ,EAAY0Y,kBAAoB1I,aAAgB0I,mBAAqBR,GAAW,EAAId,EAAOuB,sBAAsB3I,IAAQ,EAAIiG,EAAc2C,mBAAmBrD,GAC7LsD,MAAOX,EAAWd,EAAO0B,YAAc,IAAIjD,EAAQxW,QAAQkW,EAAMsD,OACjEb,QAASA,EACTe,OAAO,EAAI5C,EAAO6C,eAAezD,EAAMwD,OACvCvD,MAAM,EAAIY,EAAM6C,WAAW1D,GAC3B2D,eAAe,EAAI7C,EAAe8C,oBAAoB5D,EAAM2D,eAC5DE,UAAWpB,IAAY9B,EAASmD,QAAQC,WAAY,EAAI/C,EAAWgD,gBAAgBhE,GAAS,KAC5FiE,WAAW,EAAIlD,EAAWmD,gBAAgBlE,EAAMiE,WAChDE,QAAQ,EAAIlD,EAAQmD,aAAapE,GACjCqE,QAAS5E,WAAWO,EAAMqE,SAC1BC,UAAgD,IAAtCvC,EAAWwC,QAAQ9J,EAAK1P,UAAkB,EAAImW,EAAUsD,eAAexE,EAAMsE,UAAYpD,EAAUuD,SAASC,OACtHC,cAAc,EAAIxD,EAAcyD,mBAAmB5E,EAAM2E,aAAe3E,EAAM2E,aAAe3E,EAAM6E,UACnG9J,SAAS,EAAIqG,EAAS0D,cAAc9E,GACpCvB,SAAUA,EACVsG,gBAAgB,EAAIzD,EAAgB0D,qBAAqBhF,GACzDiF,YAAY,EAAI1D,EAAY2D,iBAAiBlF,EAAMiF,YACnDE,eAAe,EAAI3D,EAAe4D,oBAAoBpF,EAAMmF,eAC5DE,WAAW,EAAI5D,EAAW6D,gBAAgBtF,GAC1CuF,YAAY,EAAI7D,EAAY8D,iBAAiBxF,EAAMuF,YACnDE,WAAW,EAAI9D,EAAW+D,gBAAgB1F,EAAMyF,WAChDE,QAAQ,EAAI/D,EAAQgE,aAAanH,IAAa4C,EAAUwE,SAASC,OAAS9F,EAAM2F,OAAS,SAGzFvW,KAAK2W,kBAELtL,EAAKuF,MAAMqF,UAAY,uBAGvB5C,IAAY9B,EAASmD,QAAQC,UAAW,CACxC,IAAIiC,GAAY,EAAIlE,EAAUmE,cAAc7W,MAC5C,GAAI4W,EAAW,CACX,IAAIE,EAAYF,EAAU3D,UAAUpZ,OACpC+c,EAAU3D,UAAUtU,KAAKqB,MACzBA,KAAK8W,UAAYzL,EAAK0L,aAAa,UAAkC,iBAAf1L,EAAK1N,MAAqB0N,EAAK1N,MAAsB,IAAdmZ,EAAiD,iBAAxBF,EAAUzD,UAAyByD,EAAUzD,UAAY,EAAIyD,EAAU3D,UAAU6D,EAAY,GAAGA,UAAY,GAKrN,QAAjBzL,EAAK1P,SACL0P,EAAK2L,iBAAiB,QAAQ,WAC1BjE,EAAMvH,QAAS,EAAIjR,EAAQsB,aAAawP,EAAM/P,EAASE,GACvDuX,EAAMkE,cAAe,EAAI1c,EAAQiQ,kBAAkBuI,EAAMvH,OAAQuH,EAAMnC,MAAMgD,OAAQb,EAAMnC,MAAM/C,iBAGzG7N,KAAKkX,MAAQC,EAAS9L,EAAMwH,GAC5B7S,KAAKwL,OAAS+H,GAAW,EAAId,EAAO2E,sBAAqB,EAAI7c,EAAQsB,aAAawP,EAAM/P,EAASE,KAAY,EAAIjB,EAAQsB,aAAawP,EAAM/P,EAASE,GACrJwE,KAAKiX,cAAe,EAAI1c,EAAQiQ,kBAAkBxK,KAAKwL,OAAQxL,KAAK4Q,MAAMgD,OAAQ5T,KAAK4Q,MAAM/C,cAqEjG,OA5DA9O,EAAa6T,EAAe,CAAC,CACzB7Y,IAAK,eACL4D,MAAO,WACH,IAAI0Z,EAAcrX,KAAKgR,OAAShR,KAAKgR,OAAOsG,eAAiB,GAG7D,OAFgBtX,KAAK4Q,MAAMsE,WAAapD,EAAUuD,SAASkC,QAExCF,EAAYG,OAAO,EAAC,EAAIjd,EAAQkQ,yBAAyBzK,KAAKiX,gBAAkBI,IAExG,CACCtd,IAAK,WACL4D,MAAO,WACH,OAAOqC,KAAKyX,kBAAoBzX,KAAK0X,eAAiB1X,KAAK2X,2BAEhE,CACC5d,IAAK,YACL4D,MAAO,WACH,QAAQ,EAAIwT,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQmD,OAAS7X,KAAK4Q,MAAMqE,QAAU,GAAKjV,KAAK4Q,MAAMuF,aAAe7D,EAAYwF,WAAWP,UAE1J,CACCxd,IAAK,yBACL4D,MAAO,WACH,OAAOqC,KAAK4Q,MAAMvB,WAAa4C,EAAUwE,SAASC,QAAU1W,KAAK4Q,MAAMvB,WAAa4C,EAAUwE,SAASsB,WAE5G,CACChe,IAAK,eACL4D,MAAO,WACH,OAAOqC,KAAK4Q,MAAMvB,WAAa4C,EAAUwE,SAASC,SAEvD,CACC3c,IAAK,aACL4D,MAAO,WACH,OAAOqC,KAAK4Q,MAAMwD,QAAU5C,EAAOwG,MAAMH,OAE9C,CACC9d,IAAK,gBACL4D,MAAO,WACH,OAAuB,OAAhBqC,KAAKgR,SAEjB,CACCjX,IAAK,gBACL4D,MAAO,WACH,OAAgC,OAAzBqC,KAAK4Q,MAAMqF,YAEvB,CACClc,IAAK,yBACL4D,MAAO,WACH,OAAOqC,KAAKiY,iBAAmBjY,KAAK4Q,MAAM2F,OAAO2B,OAEtD,CACCne,IAAK,gBACL4D,MAAO,WACH,OAAO,EAAIwT,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQyD,UAAW,EAAIhH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ0D,gBAAiB,EAAIjH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ2D,eAAgB,EAAIlH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ4D,eAAgB,EAAInH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ6D,oBAAqB,EAAIpH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ8D,gBAEnb,CACCze,IAAK,6BACL4D,MAAO,WACH,OAAO,EAAIwT,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ0D,gBAAiB,EAAIjH,EAAMyG,UAAU5X,KAAK4Q,MAAMyC,QAAS9B,EAASmD,QAAQ8D,kBAI3I5F,EA9IS,GAiJpBnV,EAAQ/C,QAAUkY,EAGlB,IAAIuE,EAAW,SAAkB9L,EAAMwH,GACnC,GAAIxH,aAAgBA,EAAKnQ,cAAcG,YAAYod,eAAiBpN,aAAgBoN,cAAe,CAC/F,IAAIjN,GAAS,EAAIjR,EAAQsB,aAAawP,EAAM,EAAG,GAC/CA,EAAKqN,aAAa,QAASlN,EAAO1P,MAAQ,MAC1CuP,EAAKqN,aAAa,SAAUlN,EAAOzP,OAAS,MAC5C,IAAI4c,EAAI,IAAIC,cACZ,OAAO/F,EAAegG,UAAU,sBAAwBC,mBAAmBH,EAAEI,kBAAkB1N,KAEnG,OAAQA,EAAK1P,SACT,IAAK,MAED,IAAIqd,EAAM3N,EACV,OAAOwH,EAAegG,UAAUG,EAAIC,YAAcD,EAAIE,KAC1D,IAAK,SAED,IAAItc,EAASyO,EACb,OAAOwH,EAAesG,WAAWvc,GACrC,IAAK,SACD,IAAIwc,EAAY/N,EAAKgO,aAAa,wCAClC,GAAID,EACA,OAAOA,EAKnB,OAAO,O,kCC/OX5f,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEIF,EAAQma,SAAW,SAAkB0B,EAAK3b,GACrD,OAAyB,IAAjB2b,EAAM3b,IAGHF,EAAQ8b,SAAW,SAAkBtY,EAAGD,GACnD,OAAOhE,KAAKuS,KAAKtO,EAAIA,EAAID,EAAIA,IAGbvD,EAAQ+b,cAAgB,SAAuB5I,EAAOlX,GAEtE,IAAK,IAAIC,EAAIiX,EAAM/W,OAAS,EAAGF,GAAK,EAAGA,IAAK,CACxC,IAAI8f,EAAW7I,EAAM8I,KAAK/f,GAET,YAAb8f,GACA/f,EAAOkX,MAAM+I,YAAYF,EAAU7I,EAAMgJ,iBAAiBH,IAGlE,OAAO/f,GAGO+D,EAAQoc,YAAc,kF,kCCvBxCrgB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQqc,qBAAuBrc,EAAQkW,gBAAkBlW,EAAQsc,8BAAgCtc,EAAQuc,4BAA8Bvc,EAAQwc,mCAAqCxc,EAAQyc,gCAAkCzc,EAAQ0c,gCAAkC1c,EAAQ2c,wBAA0B3c,EAAQ4c,kBAAoB5c,EAAQ6c,gBAAkB7c,EAAQ8c,gBAAkB9c,EAAQ+c,uBAAoBlc,EAEtZ,IAEI4S,EAAU9W,EAFD,EAAQ,OAMjBqgB,EAAWrgB,EAFD,EAAQ,OAMlBsgB,EAAStgB,EAFD,EAAQ,OAMhB2Q,EAAW3Q,EAFD,EAAQ,OAIlBG,EAAU,EAAQ,MAElByX,EAAW,EAAQ,MAEvB,SAAS5X,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAIvF,IAAIggB,EAAoB/c,EAAQ+c,kBAAoB,CAChDG,OAAQ,EACRC,UAAW,EACXC,SAAU,EACVC,SAAU,GAGVP,EAAkB9c,EAAQ8c,gBAAkB,CAC5CQ,KAAM,EACNC,QAAS,EACTC,MAAO,EACPC,OAAQ,GAGRZ,EAAkB7c,EAAQ6c,gBAAkB,CAC5Ca,WAAY,EACZC,YAAa,EACbC,YAAa,GAGbhB,EAAoB5c,EAAQ4c,kBAAoBC,EAIhDgB,EAAiB,SAASA,EAAeC,GAGzC,OA7BJ,SAAyBzb,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCA2B5GiB,CAAgBC,KAAMsb,GAEdC,GACJ,IAAK,UACDvb,KAAKub,KAAOhB,EAAgBS,QAC5B,MACJ,IAAK,QACDhb,KAAKub,KAAOhB,EAAgBU,MAC5B,MACJ,IAAK,OACDjb,KAAKub,KAAOhB,EAAgBQ,KAC5B,MACJ,QACI/a,KAAKrC,MAAQ,IAAI8c,EAAS/f,QAAQ6gB,KAyC1CC,GArC0B/d,EAAQ2c,wBAA0B,SAAiCqB,EAAiBvE,EAAO1L,GACrH,IAAI1P,EAAQ,EACRC,EAAS,EACTwf,EAAOE,EAAgBF,KAC3B,GAAIA,EAAK,GAAGA,OAAShB,EAAgBS,SAAWO,EAAK,GAAGA,OAAShB,EAAgBU,MAAO,CACpF,IAAIS,EAAclQ,EAAO1P,MAAQ0P,EAAOzP,OACpC4f,EAAezE,EAAMpb,MAAQob,EAAMnb,OACvC,OAAO2f,EAAcC,IAAkBJ,EAAK,GAAGA,OAAShB,EAAgBU,OAAS,IAAIP,EAAOhgB,QAAQ8Q,EAAO1P,MAAO0P,EAAO1P,MAAQ6f,GAAgB,IAAIjB,EAAOhgB,QAAQ8Q,EAAOzP,OAAS4f,EAAcnQ,EAAOzP,QAmB7M,OAhBIwf,EAAK,GAAG5d,QACR7B,EAAQyf,EAAK,GAAG5d,MAAMqQ,iBAAiBxC,EAAO1P,QAG9Cyf,EAAK,GAAGA,OAAShB,EAAgBQ,MAAQQ,EAAK,GAAGA,OAAShB,EAAgBQ,KAC1Ehf,EAASmb,EAAMnb,OACRwf,EAAK,GAAGA,OAAShB,EAAgBQ,KACxChf,EAASD,EAAQob,EAAMpb,MAAQob,EAAMnb,OAC9Bwf,EAAK,GAAG5d,QACf5B,EAASwf,EAAK,GAAG5d,MAAMqQ,iBAAiBxC,EAAOzP,SAG/Cwf,EAAK,GAAGA,OAAShB,EAAgBQ,OACjCjf,EAAQC,EAASmb,EAAMnb,OAASmb,EAAMpb,OAGnC,IAAI4e,EAAOhgB,QAAQoB,EAAOC,IAGC0B,EAAQ0c,gCAAkC,SAAyCsB,EAAiBjQ,GACtI,IAAI+P,EAAOE,EAAgBF,KACvBzf,EAAQyf,EAAK,GAAG5d,MAAQ4d,EAAK,GAAG5d,MAAMqQ,iBAAiBxC,EAAO1P,OAAS0P,EAAO1P,MAC9EC,EAASwf,EAAK,GAAG5d,MAAQ4d,EAAK,GAAG5d,MAAMqQ,iBAAiBxC,EAAOzP,QAAUwf,EAAK,GAAG5d,MAAQ7B,EAAQ0P,EAAOzP,OAE5G,OAAO,IAAI2e,EAAOhgB,QAAQoB,EAAOC,IAGrB,IAAIuf,EAzDT,SAkHPM,GAvDkCne,EAAQyc,gCAAkC,SAAyCvN,EAAQkP,GAC7H,OAAQA,GACJ,KAAKvB,EAAgBa,WACjB,OAAO,EAAI5gB,EAAQmQ,wBAAwBiC,GAC/C,KAAK2N,EAAgBc,YACrB,QACI,OAAO,EAAI7gB,EAAQkQ,yBAAyBkC,KAIflP,EAAQwc,mCAAqC,SAA4C6B,EAAkBtQ,EAAQG,EAASiI,GACjK,IAAImI,GAAa,EAAIxhB,EAAQsQ,qBAAqBW,EAAQoI,GAE1D,OAAQkI,GACJ,KAAKzB,EAAkBc,WACnB,OAAO3P,EACX,KAAK6O,EAAkBgB,YACnB,IAAItP,EAAcJ,EAAQqG,EAASgK,cAAcC,MAAMjO,iBAAiBxC,EAAO1P,OAC3E+P,EAAeF,EAAQqG,EAASgK,cAAcE,OAAOlO,iBAAiBxC,EAAO1P,OAC7E8P,EAAaD,EAAQqG,EAASgK,cAAcG,KAAKnO,iBAAiBxC,EAAO1P,OACzEgQ,EAAgBH,EAAQqG,EAASgK,cAAcI,QAAQpO,iBAAiBxC,EAAO1P,OACnF,OAAO,IAAIvB,EAAQuQ,OAAOiR,EAAW/f,KAAO+P,EAAagQ,EAAW9f,IAAM2P,EAAYmQ,EAAWjgB,MAAQiQ,EAAcF,EAAckQ,EAAWhgB,OAAS6P,EAAaE,GAC1K,KAAKuO,EAAkBe,YACvB,QACI,OAAOW,IAIete,EAAQuc,4BAA8B,SAAqC3K,EAAUkM,EAAM/P,GACzH,OAAO,IAAIT,EAASrQ,QAAQ2U,EAAS,GAAGrB,iBAAiBxC,EAAO1P,MAAQyf,EAAKzf,OAAQuT,EAAS,GAAGrB,iBAAiBxC,EAAOzP,OAASwf,EAAKxf,UAGvG0B,EAAQsc,8BAAgC,SAAuCtG,EAAYpE,EAAUkM,EAAMc,EAA2B7Q,GAEtK,OADaiI,EAAW6I,QAEpB,KAAK9B,EAAkBK,SACnB,MAAO,CAAC,IAAI9P,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,MAAOgB,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,IAAK,IAAIgO,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,KAAOwP,EAAO1P,OAAQkB,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,IAAK,IAAIgO,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,KAAOwP,EAAO1P,OAAQkB,KAAKuf,MAAMhB,EAAKxf,OAASsgB,EAA0BpgB,IAAMoT,EAAStS,IAAK,IAAIgO,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,MAAOgB,KAAKuf,MAAMhB,EAAKxf,OAASsgB,EAA0BpgB,IAAMoT,EAAStS,KAC7d,KAAKyd,EAAkBM,SACnB,MAAO,CAAC,IAAI/P,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAAIE,KAAKuf,MAAM/Q,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,EAAIye,EAAKzf,OAAQkB,KAAKuf,MAAM/Q,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,EAAIye,EAAKzf,OAAQkB,KAAKuf,MAAM/Q,EAAOzP,OAASyP,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAAIE,KAAKuf,MAAM/Q,EAAOzP,OAASyP,EAAOvP,OAC3d,KAAKue,EAAkBI,UACnB,MAAO,CAAC,IAAI7P,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAAIE,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,IAAK,IAAIgO,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,EAAIye,EAAKzf,OAAQkB,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,IAAK,IAAIgO,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,EAAIye,EAAKzf,OAAQkB,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,EAAIwe,EAAKxf,SAAU,IAAIgP,EAASrQ,QAAQsC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAAIE,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,EAAIwe,EAAKxf,UACplB,QACI,MAAO,CAAC,IAAIgP,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,MAAOgB,KAAKuf,MAAM/Q,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,KAAOwP,EAAO1P,OAAQkB,KAAKuf,MAAM/Q,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,KAAOwP,EAAO1P,OAAQkB,KAAKuf,MAAM/Q,EAAOzP,OAASyP,EAAOvP,MAAO,IAAI8O,EAASrQ,QAAQsC,KAAKuf,MAAM/Q,EAAOxP,MAAOgB,KAAKuf,MAAM/Q,EAAOzP,OAASyP,EAAOvP,SAIjVwB,EAAQkW,gBAAkB,SAAyB/C,EAAOiC,GAC5E,MAAO,CACHxW,gBAAiB,IAAI6U,EAAQxW,QAAQkW,EAAMvU,iBAC3Cof,gBAAiBe,EAAsB5L,EAAOiC,GAC9C4J,eAAgBb,EAAoBhL,EAAM6L,gBAC1CX,iBAAkBY,EAAsB9L,EAAMkL,oBAI5B,SAA6BW,GACnD,OAAQA,GACJ,IAAK,cACD,OAAOnC,EAAgBc,YAC3B,IAAK,cACD,OAAOd,EAAgBe,YAE/B,OAAOf,EAAgBa,aAGvBuB,EAAwB,SAA+BZ,GACvD,OAAQA,GACJ,IAAK,cACD,OAAOzB,EAAkBe,YAC7B,IAAK,cACD,OAAOf,EAAkBgB,YAEjC,OAAOhB,EAAkBc,YAGzBwB,EAAwB,SAA+BC,GACvD,OAAQA,EAAiBC,QACrB,IAAK,YACD,OAAOrC,EAAkBI,UAC7B,IAAK,WACL,IAAK,mBACD,OAAOJ,EAAkBK,SAC7B,IAAK,WACL,IAAK,mBACD,OAAOL,EAAkBM,SAC7B,IAAK,SACD,OAAON,EAAkBG,OAOjC,OAAOH,EAAkBG,QAGzB6B,EAAwB,SAA+B5L,EAAOiC,GAC9D,IAAIiK,EAAUhD,EAAqBlJ,EAAM6K,iBAAiBsB,KAAI,SAAUtB,GACpE,GAA+B,QAA3BA,EAAgBuB,OAAkB,CAClC,IAAIjjB,EAAM8Y,EAAegG,UAAU4C,EAAgBwB,KAAK,IACxDxB,EAAgBwB,KAAOljB,EAAM,CAACA,GAAO,GAEzC,OAAO0hB,KAEPyB,EAAYtM,EAAMuM,mBAAmBC,MAAM,KAC3CC,EAAUzM,EAAMgM,iBAAiBQ,MAAM,KACvCE,EAAQ1M,EAAM2M,eAAeH,MAAM,KAEvC,OAAON,EAAQC,KAAI,SAAUjjB,EAAQgZ,GACjC,IAAIyI,GAAQ+B,EAAMxK,IAxKf,QAwK+B+J,OAAOO,MAAM,KAAKL,IAAIS,GACpDnO,GAAY6N,EAAUpK,IAzKvB,QAyKuC+J,OAAOO,MAAM,KAAKL,IAAIU,GAEhE,MAAO,CACH3jB,OAAQA,EACRwiB,OAAQK,EAAgD,iBAAnBU,EAAQvK,GAAsBuK,EAAQvK,GAASuK,EAAQ,IAC5F9B,KAAMA,EAAK1hB,OAAS,EAAI,CAAC0hB,EAAK,GAAIC,GAAa,CAACD,EAAK,GAAIA,EAAK,IAC9DlM,SAAUA,EAASxV,OAAS,EAAI,CAACwV,EAAS,GAAIA,EAAS,IAAM,CAACA,EAAS,GAAIA,EAAS,SAK5FmO,EAAsB,SAA6BjC,GACnD,MAAgB,SAATA,EAAkBC,EAAY,IAAIF,EAAeC,IAGxDkC,EAAyB,SAAgCpO,GACzD,OAAQA,GACJ,IAAK,SACL,IAAK,QACD,OAAO,IAAIoL,EAAS/f,QAAQ,QAChC,IAAK,OACL,IAAK,MACD,OAAO,IAAI+f,EAAS/f,QAAQ,MAChC,IAAK,OACD,OAAO,IAAI+f,EAAS/f,QAAQ,KAEpC,OAAO,IAAI+f,EAAS/f,QAAQ2U,IAG5ByK,EAAuBrc,EAAQqc,qBAAuB,SAA8B5C,GACpF,IAAIwG,EAAa,OACbC,EAAU,GAEVV,EAAO,GACPD,EAAS,GACTY,EAAQ,KACRC,EAAa,GACbC,EAAO,EACPC,EAAW,EAEXC,EAAe,WACf,IAAIC,EAAS,GACb,GAAIjB,EAAQ,CACwB,MAA5Ba,EAAW1N,OAAO,EAAG,KACrB0N,EAAaA,EAAW1N,OAAO,EAAG0N,EAAWhkB,OAAS,IAGtDgkB,GACAZ,EAAKte,KAAKkf,EAAWhB,QAGzB,IAAIqB,EAAWlB,EAAO7H,QAAQ,IAAK,GAAK,EACZ,MAAxB6H,EAAO7M,OAAO,EAAG,IAAc+N,EAAW,IAC1CD,EAASjB,EAAO7M,OAAO,EAAG+N,GAAUxd,cACpCsc,EAASA,EAAO7M,OAAO+N,IAGZ,UADflB,EAASA,EAAOtc,gBAEZid,EAAQhf,KAAK,CACTsf,OAAQA,EACRjB,OAAQA,EACRC,KAAMA,IAIlBA,EAAO,GACPD,EAASa,EAAa,IA+D1B,OA5DA3G,EAAMkG,MAAM,IAAIe,SAAQ,SAAUC,GAC9B,GAAa,IAATN,IAAcJ,EAAWW,KAAKD,GAAlC,CAGA,OAAQA,GACJ,IAAK,IACIR,EAEMA,IAAUQ,IACjBR,EAAQ,MAFRA,EAAQQ,EAIZ,MACJ,IAAK,IACD,GAAIR,EACA,MACG,GAAa,IAATE,EAEP,YADAA,EAAO,GAGPC,IAEJ,MACJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAAY,CACnB,GAAiB,IAAbC,EAGA,OAFAD,EAAO,OACPE,IAGAD,IAGR,MAEJ,IAAK,IACD,GAAIH,EACA,MACG,GAAa,IAATE,EAEP,YADAE,IAEG,GAAa,IAATF,GACU,IAAbC,IAAmBf,EAAO7c,MAAM,UAGhC,OAFA8c,EAAKte,KAAKkf,EAAWhB,aACrBgB,EAAa,IAOhB,IAATC,EACAd,GAAUoB,EAEVP,GAAcO,MAItBJ,IACOL,I,kCC7VXnkB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEAF,EAAQ6gB,KAAO,CACtBC,OAAQ,EACRC,aAAc,EACdC,OAAQ,I,kCCNZjlB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAI+gB,EAAQ,EAAQ,MAoBpBjhB,EAAQ/C,QAhBK,SAASikB,EAAO7hB,EAAGC,IAFhC,SAAyB+C,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAG5GiB,CAAgBC,KAAM2e,GAEtB3e,KAAKkQ,KAAOwO,EAAMJ,KAAKC,OACvBve,KAAKlD,EAAIA,EACTkD,KAAKjD,EAAIA,I,kCCbbvD,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQmX,eAAiBnX,EAAQmhB,mBAAqBnhB,EAAQohB,gBAAkBphB,EAAQqhB,yBAAsBxgB,EAE9G,IAAI8S,EAAc,EAAQ,MAEtB0N,EAAsBrhB,EAAQqhB,oBAAsB,CACpDC,OAAQ,EACRC,QAAS,GAGTH,EAAkBphB,EAAQohB,gBAAkB,CAC5ChH,MAAO,EACPoH,KAAM,EACNR,OAAQ,EACRS,OAAQ,EACRC,QAAS,EACTC,YAAa,EACbC,qBAAsB,EACtBC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,EACbC,YAAa,GACbC,aAAc,GACdC,SAAU,GACVC,QAAS,GACTC,UAAW,GACXC,mBAAoB,GACpBC,kBAAmB,GACnBC,gBAAiB,GACjBC,WAAY,GACZC,iBAAkB,GAClBC,SAAU,GACVC,SAAU,GACVC,SAAU,GACVC,OAAQ,GACRC,SAAU,GACVC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,GACnBC,QAAS,GACTC,SAAU,GACVC,eAAgB,GAChBC,MAAO,GACPC,qBAAsB,GACtBC,oBAAqB,GACrBC,sBAAuB,GACvBC,IAAK,GACLC,eAAgB,GAChBC,UAAW,GACXC,UAAW,GACXC,QAAS,GACTC,MAAO,GACPC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,MAAO,GACPC,OAAQ,GACRC,KAAM,GACNC,QAAS,GACTC,oBAAqB,GACrBC,sBAAuB,GACvBC,eAAgB,GAChBC,gBAAiB,GACjBC,kBAAmB,IAGnBxD,EAAqBnhB,EAAQmhB,mBAAqB,SAA4B1O,GAC9E,OAAQA,GACJ,IAAK,OACD,OAAO2O,EAAgBI,KAC3B,IAAK,SACD,OAAOJ,EAAgBJ,OAC3B,IAAK,SACD,OAAOI,EAAgBK,OAC3B,IAAK,UACD,OAAOL,EAAgBM,QAC3B,IAAK,cACD,OAAON,EAAgBO,YAC3B,IAAK,uBACD,OAAOP,EAAgBQ,qBAC3B,IAAK,cACD,OAAOR,EAAgBS,YAC3B,IAAK,cACD,OAAOT,EAAgBU,YAC3B,IAAK,cACD,OAAOV,EAAgBW,YAC3B,IAAK,cACD,OAAOX,EAAgBY,YAC3B,IAAK,cACD,OAAOZ,EAAgBa,YAC3B,IAAK,eACD,OAAOb,EAAgBc,aAC3B,IAAK,WACD,OAAOd,EAAgBe,SAC3B,IAAK,UACD,OAAOf,EAAgBgB,QAC3B,IAAK,YACD,OAAOhB,EAAgBiB,UAC3B,IAAK,qBACD,OAAOjB,EAAgBkB,mBAC3B,IAAK,oBACD,OAAOlB,EAAgBmB,kBAC3B,IAAK,kBACD,OAAOnB,EAAgBoB,gBAC3B,IAAK,aACD,OAAOpB,EAAgBqB,WAC3B,IAAK,mBACD,OAAOrB,EAAgBsB,iBAC3B,IAAK,WACD,OAAOtB,EAAgBuB,SAC3B,IAAK,WACD,OAAOvB,EAAgBwB,SAC3B,IAAK,WACD,OAAOxB,EAAgByB,SAC3B,IAAK,SACD,OAAOzB,EAAgB0B,OAC3B,IAAK,WACD,OAAO1B,EAAgB2B,SAC3B,IAAK,iBACD,OAAO3B,EAAgB4B,eAC3B,IAAK,kBACD,OAAO5B,EAAgB6B,gBAC3B,IAAK,oBACD,OAAO7B,EAAgB8B,kBAC3B,IAAK,UACD,OAAO9B,EAAgB+B,QAC3B,IAAK,WACD,OAAO/B,EAAgBgC,SAC3B,IAAK,iBACD,OAAOhC,EAAgBiC,eAC3B,IAAK,QACD,OAAOjC,EAAgBkC,MAC3B,IAAK,uBACD,OAAOlC,EAAgBmC,qBAC3B,IAAK,sBACD,OAAOnC,EAAgBoC,oBAC3B,IAAK,wBACD,OAAOpC,EAAgBqC,sBAC3B,IAAK,MACD,OAAOrC,EAAgBsC,IAC3B,IAAK,iBACD,OAAOtC,EAAgBuC,eAC3B,IAAK,YACD,OAAOvC,EAAgBwC,UAC3B,IAAK,YACD,OAAOxC,EAAgByC,UAC3B,IAAK,UACD,OAAOzC,EAAgB0C,QAC3B,IAAK,QACD,OAAO1C,EAAgB2C,MAC3B,IAAK,UACD,OAAO3C,EAAgB4C,QAC3B,IAAK,sBACD,OAAO5C,EAAgB6C,oBAC3B,IAAK,wBACD,OAAO7C,EAAgB8C,sBAC3B,IAAK,QACD,OAAO9C,EAAgB+C,MAC3B,IAAK,SACD,OAAO/C,EAAgBgD,OAC3B,IAAK,OACD,OAAOhD,EAAgBiD,KAC3B,IAAK,UACD,OAAOjD,EAAgBkD,QAC3B,IAAK,sBACD,OAAOlD,EAAgBmD,oBAC3B,IAAK,wBACD,OAAOnD,EAAgBoD,sBAC3B,IAAK,iBACD,OAAOpD,EAAgBqD,eAC3B,IAAK,kBACD,OAAOrD,EAAgBsD,gBAC3B,IAAK,oBACD,OAAOtD,EAAgBuD,kBAC3B,IAAK,OACL,QACI,OAAOvD,EAAgBhH,OAa/BwK,GATiB5kB,EAAQmX,eAAiB,SAAwBhE,GAClE,IAAI0R,GAAiB,EAAIlR,EAAY0I,sBAAsBlJ,EAAMgJ,iBAAiB,qBAClF,MAAO,CACH2I,cAAe3D,EAAmBhO,EAAMgJ,iBAAiB,oBACzD0I,eAAgBA,EAAezoB,OAASyoB,EAAe,GAAK,KAC5DE,kBAAmBH,EAAuBzR,EAAMgJ,iBAAiB,0BAI5C,SAAgCvK,GACzD,OAAQA,GACJ,IAAK,SACD,OAAOyP,EAAoBC,OAC/B,IAAK,UACL,QACI,OAAOD,EAAoBE,Y,kCCtMvCxlB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAIoB,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf8S,EAAiB,EAAQ,MAEzBqQ,EAAc,EAAQ,MAI1B,IAAIC,EAAgB,WAChB,SAASA,EAAcC,EAAM3R,EAAQxF,IAHzC,SAAyB1L,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAIxGiB,CAAgBC,KAAM0iB,GAEtB1iB,KAAK2iB,KAAOA,EACZ3iB,KAAKgR,OAASA,EACdhR,KAAKwL,OAASA,EAWlB,OARAzM,EAAa2jB,EAAe,KAAM,CAAC,CAC/B3oB,IAAK,eACL4D,MAAO,SAAsB0N,EAAM2F,GAC/B,IAAI2R,EAAO1M,EAAU5K,EAAKuX,KAAM5R,EAAOJ,MAAMmF,eAC7C,OAAO,IAAI2M,EAAcC,EAAM3R,GAAQ,EAAIyR,EAAYI,iBAAiBF,EAAM3R,EAAQ3F,QAIvFqX,EAjBS,GAoBpBjlB,EAAQ/C,QAAUgoB,EAGlB,IAAII,EAAa,2BAEb7M,EAAY,SAAmB0M,EAAMtQ,GACrC,OAAQA,GACJ,KAAKD,EAAe2Q,eAAeC,UAC/B,OAAOL,EAAKjiB,cAChB,KAAK0R,EAAe2Q,eAAeD,WAC/B,OAAOH,EAAKM,QAAQH,EAAYI,GACpC,KAAK9Q,EAAe2Q,eAAeI,UAC/B,OAAOR,EAAKS,cAChB,QACI,OAAOT,IAInB,SAASO,EAAWG,EAAGC,EAAIC,GACvB,OAAIF,EAAExpB,OAAS,EACJypB,EAAKC,EAAGH,cAGZC,I,kCCvDX7pB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAI6lB,EAAyB,EAAQ,MAmFjCC,EAAe,SAAsBb,GACrC,OAAmB,IAAZA,EAAK,IAAwB,MAAZA,EAAK,IAA0B,IAAZA,EAAK,IAAwB,MAAZA,EAAK,IAuCjEc,EAAW,CAEX,2BAGI,IAAI/lB,EA9HU,SAAyBqO,GAG3C,GAAIA,EAAS2X,YAAa,CACtB,IAAIC,EAAQ5X,EAAS2X,cACrB,GAAIC,EAAMrY,sBAAuB,CAC7B,IAAIsY,EAAc7X,EAAS8X,cAAc,aACzCD,EAAYjT,MAAM7U,OAASgoB,QAC3BF,EAAYjT,MAAMyC,QAAU,QAC5BrH,EAASC,KAAK+X,YAAYH,GAE1BD,EAAMK,WAAWJ,GACjB,IAAIK,EAAcN,EAAMrY,wBACpB4Y,EAAcnnB,KAAKuf,MAAM2H,EAAYnoB,QAEzC,GADAiQ,EAASC,KAAKmY,YAAYP,GAbhB,MAcNM,EACA,OAAO,GAKnB,OAAO,EAyGSE,CAAgBrY,UAE5B,OADAxS,OAAOkE,eAAegmB,EAAU,uBAAwB,CAAE/lB,MAAOA,IAC1DA,GAGX,0BAGI,IAAIA,EApEE,SAAiBqO,GAC3B,IAAIgN,EAAM,IAAIsL,MACV1nB,EAASoP,EAAS8X,cAAc,UAChCS,EAAM3nB,EAAO4nB,WAAW,MAC5BxL,EAAIE,IAAM,oEAEV,IACIqL,EAAIE,UAAUzL,EAAK,EAAG,GACtBpc,EAAO8nB,YACT,MAAOC,GACL,OAAO,EAEX,OAAO,EAwDSC,CAAQ5Y,UAEpB,OADAxS,OAAOkE,eAAegmB,EAAU,sBAAuB,CAAE/lB,MAAOA,IACzDA,GAGX,6BAGI,OAAO,SAAUub,GACb,IAAI2L,EAtHC,SAAoB7Y,EAAUkN,GAC3C,IAAIF,EAAM,IAAIsL,MACV1nB,EAASoP,EAAS8X,cAAc,UAChCS,EAAM3nB,EAAO4nB,WAAW,MAE5B,OAAO,IAAIrpB,SAAQ,SAAU2pB,GAEzB9L,EAAIE,IAAMA,EAEV,IAAI6L,EAAS,WACT,IACIR,EAAIE,UAAUzL,EAAK,EAAG,GACtBpc,EAAO8nB,YACT,MAAOC,GACL,OAAOG,GAAQ,GAGnB,OAAOA,GAAQ,IAGnB9L,EAAI+L,OAASA,EACb/L,EAAIgM,QAAU,WACV,OAAOF,GAAQ,KAGE,IAAjB9L,EAAIiM,UACJC,YAAW,WACPH,MACD,QA0FUI,CAAWnZ,SAAUkN,GAIlC,OAHA1f,OAAOkE,eAAegmB,EAAU,yBAA0B,CAAE/lB,MAAO,WAC3D,OAAOknB,KAERA,IAIf,oCAGI,IAAIlnB,EAA8B,mBAAfG,MAAMsnB,MAA+C,mBAAjBC,OAAOC,MArE9C,SAA2BtZ,GAC/C,IAAIpP,EAASoP,EAAS8X,cAAc,UAEpClnB,EAAOd,MADI,IAEXc,EAAOb,OAFI,IAGX,IAAIwoB,EAAM3nB,EAAO4nB,WAAW,MAC5BD,EAAIgB,UAAY,iBAChBhB,EAAIiB,SAAS,EAAG,EALL,SAOX,IAAIxM,EAAM,IAAIsL,MACVmB,EAAgB7oB,EAAO8nB,YAC3B1L,EAAIE,IAAMuM,EACV,IAAIC,GAAM,EAAIlC,EAAuBmC,wBAV1B,QAU8D,EAAG,EAAG3M,GAI/E,OAHAuL,EAAIgB,UAAY,MAChBhB,EAAIiB,SAAS,EAAG,EAZL,UAcJ,EAAIhC,EAAuBoC,mBAAmBF,GAAKG,MAAK,SAAU7M,GACrEuL,EAAIE,UAAUzL,EAAK,EAAG,GACtB,IAAI4J,EAAO2B,EAAIuB,aAAa,EAAG,EAhBxB,SAgBuClD,KAC9C2B,EAAIgB,UAAY,MAChBhB,EAAIiB,SAAS,EAAG,EAlBT,SAoBP,IAAIna,EAAOW,EAAS8X,cAAc,OAIlC,OAHAzY,EAAKuF,MAAM6K,gBAAkB,OAASgK,EAAgB,IACtDpa,EAAKuF,MAAM7U,OAASwf,QAEbkI,EAAab,IAAQ,EAAIY,EAAuBoC,oBAAmB,EAAIpC,EAAuBmC,wBAxB9F,QAwBkI,EAAG,EAAGta,IAASlQ,QAAQC,QAAO,MACxKyqB,MAAK,SAAU7M,GAGd,OAFAuL,EAAIE,UAAUzL,EAAK,EAAG,GAEfyK,EAAac,EAAIuB,aAAa,EAAG,EA5BjC,SA4BgDlD,SACxDmD,OAAM,SAAUpB,GACf,OAAO,KAqC8EqB,CAAkBha,UAAY7Q,QAAQ2pB,SAAQ,GAEnI,OADAtrB,OAAOkE,eAAegmB,EAAU,gCAAiC,CAAE/lB,MAAOA,IACnEA,GAGX,0BAGI,IAAIA,OAvGkC,KAA5B,IAAI2mB,OAAQ2B,YAyGtB,OADAzsB,OAAOkE,eAAegmB,EAAU,sBAAuB,CAAE/lB,MAAOA,IACzDA,GAGX,4BAGI,IAAIA,EA3G4C,iBAAtC,IAAIuoB,gBAAiBC,aA6G/B,OADA3sB,OAAOkE,eAAegmB,EAAU,wBAAyB,CAAE/lB,MAAOA,IAC3DA,GAGX,uBAGI,IAAIA,EAAQ,oBAAqB,IAAIuoB,eAErC,OADA1sB,OAAOkE,eAAegmB,EAAU,mBAAoB,CAAE/lB,MAAOA,IACtDA,IAIfF,EAAQ/C,QAAUgpB,G,kCC9LlBlqB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQmY,oBAAsBnY,EAAQ2oB,qBAAuB3oB,EAAQ4oB,gBAAkB5oB,EAAQ6oB,2BAAwBhoB,EAEvH,IAIgC9D,EAJ5ByW,EAAS,EAAQ,MAEjBC,GAE4B1W,EAFKyW,IAEgBzW,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAI8rB,EAAwB7oB,EAAQ6oB,sBAAwB,CACxDC,MAAO,EACPC,OAAQ,EACRC,OAAQ,EACRC,OAAQ,EACRC,KAAM,GAGNN,EAAkB5oB,EAAQ4oB,gBAAkB,CAC5CxO,KAAM,MAGNuO,EAAuB3oB,EAAQ2oB,qBAAuB,CACtDQ,UAAW,EACXC,SAAU,EACVC,aAAc,EACdC,MAAO,GAGPC,EAAY,SAAmBC,GAC/B,OAAQA,GACJ,IAAK,YACD,OAAOb,EAAqBQ,UAChC,IAAK,WACD,OAAOR,EAAqBS,SAChC,IAAK,eACD,OAAOT,EAAqBU,aAEpC,OAAOV,EAAqBW,OAyBNtpB,EAAQmY,oBAAsB,SAA6BhF,GACjF,IAvB2DqW,EAuBvDC,EAtBS,UAD8CD,EAuBVrW,EAAMsW,mBAAqBtW,EAAMsW,mBAAqBtW,EAAM+E,gBArBlG,KAGJsR,EAAK7J,MAAM,KAAKL,IAAIiK,GAmB3B,OAA2B,OAAvBE,EACOb,EAAgBxO,KAMpB,CACHqP,mBAAoBA,EACpBC,oBALsBvW,EAAMuW,oBAAsB,IAAIjW,EAAQxW,QAAQkW,EAAMuW,qBAAuB,KAMnGC,oBA1BuB,SAAkCxW,GAC7D,OAAQA,GACJ,IAAK,SACD,OAAO0V,EAAsBE,OACjC,IAAK,SACD,OAAOF,EAAsBG,OACjC,IAAK,SACD,OAAOH,EAAsBI,OACjC,IAAK,OACD,OAAOJ,EAAsBK,KAErC,OAAOL,EAAsBC,MAUHc,CAAyBzW,EAAMwW,wB,kCCvE7D5tB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQqW,YAAcrW,EAAQ6pB,aAAe7pB,EAAQ8pB,kBAAejpB,EAEpE,IAIgC9D,EAJ5ByW,EAAS,EAAQ,MAEjBC,GAE4B1W,EAFKyW,IAEgBzW,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAI+sB,EAAe9pB,EAAQ8pB,aAAe,CACtC1P,KAAM,EACN0O,MAAO,GAGPe,EAAe7pB,EAAQ6pB,aAAe,CACtCnL,IAAK,EACLD,MAAO,EACPE,OAAQ,EACRH,KAAM,GAGNuL,EAAQhuB,OAAOiuB,KAAKH,GAAcvK,KAAI,SAAUpE,GAChD,OAAOA,EAAEjY,iBAWKjD,EAAQqW,YAAc,SAAqBlD,GACzD,OAAO4W,EAAMzK,KAAI,SAAU2K,GACvB,IAAIC,EAAc,IAAIzW,EAAQxW,QAAQkW,EAAMgJ,iBAAiB,UAAY8N,EAAO,WAC5EE,EAXW,SAA0BhX,GAC7C,OAAQA,GACJ,IAAK,OACD,OAAO2W,EAAa1P,KAE5B,OAAO0P,EAAahB,MAMEsB,CAAiBjX,EAAMgJ,iBAAiB,UAAY8N,EAAO,WACzEhc,EAAc2E,WAAWO,EAAMgJ,iBAAiB,UAAY8N,EAAO,WACvE,MAAO,CACHC,YAAaA,EACbC,YAAaA,EACblc,YAAa4E,MAAM5E,GAAe,EAAIA,Q,kCC3ClDlS,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEQF,EAAQqqB,aAAe,SAAsBC,GAI5D,IAHA,IAAIC,EAAa,GACbruB,EAAI,EACJE,EAASkuB,EAAIluB,OACVF,EAAIE,GAAQ,CACf,IAAI8D,EAAQoqB,EAAIE,WAAWtuB,KAC3B,GAAIgE,GAAS,OAAUA,GAAS,OAAUhE,EAAIE,EAAQ,CAClD,IAAIquB,EAAQH,EAAIE,WAAWtuB,KACF,QAAZ,MAARuuB,GACDF,EAAWrpB,OAAe,KAARhB,IAAkB,KAAe,KAARuqB,GAAiB,QAE5DF,EAAWrpB,KAAKhB,GAChBhE,UAGJquB,EAAWrpB,KAAKhB,GAGxB,OAAOqqB,GAGSvqB,EAAQ0qB,cAAgB,WACxC,GAAIC,OAAOD,cACP,OAAOC,OAAOD,cAActZ,MAAMuZ,OAAQxuB,WAG9C,IAAIC,EAASD,UAAUC,OACvB,IAAKA,EACD,MAAO,GAOX,IAJA,IAAIwuB,EAAY,GAEZvV,GAAS,EACTwV,EAAS,KACJxV,EAAQjZ,GAAQ,CACrB,IAAI0uB,EAAY3uB,UAAUC,QAAUiZ,OAAQxU,EAAY1E,UAAUkZ,GAC9DyV,GAAa,MACbF,EAAU1pB,KAAK4pB,IAEfA,GAAa,MACbF,EAAU1pB,KAAyB,OAAnB4pB,GAAa,IAAcA,EAAY,KAAQ,SAE/DzV,EAAQ,IAAMjZ,GAAUwuB,EAAUxuB,OAAS,SAC3CyuB,GAAUF,OAAOI,aAAa3Z,MAAMuZ,OAAQC,GAC5CA,EAAUxuB,OAAS,GAG3B,OAAOyuB,GAOX,IAvDA,IAmDIG,EAAQ,mEAGRC,EAA+B,oBAAfC,WAA6B,GAAK,IAAIA,WAAW,KAC5DhvB,EAAI,EAAGA,EAAI8uB,EAAM5uB,OAAQF,IAC9B+uB,EAAOD,EAAMR,WAAWtuB,IAAMA,EAGrB8D,EAAQmrB,OAAS,SAAgBC,GAC1C,IAAIC,EAA+B,IAAhBD,EAAOhvB,OACtBkvB,EAAMF,EAAOhvB,OACbF,OAAI,EACJqvB,EAAI,EACJC,OAAW,EACXC,OAAW,EACXC,OAAW,EACXC,OAAW,EAEmB,MAA9BP,EAAOA,EAAOhvB,OAAS,KACvBivB,IACkC,MAA9BD,EAAOA,EAAOhvB,OAAS,IACvBivB,KAIR,IAAIO,EAAgC,oBAAhBC,aAAqD,oBAAfX,iBAAoE,IAA/BA,WAAW3uB,UAAUuvB,MAAwB,IAAID,YAAYR,GAAgB,IAAIhrB,MAAMgrB,GAClLU,EAAQ1rB,MAAMC,QAAQsrB,GAAUA,EAAS,IAAIV,WAAWU,GAE5D,IAAK1vB,EAAI,EAAGA,EAAIovB,EAAKpvB,GAAK,EACtBsvB,EAAWP,EAAOG,EAAOZ,WAAWtuB,IACpCuvB,EAAWR,EAAOG,EAAOZ,WAAWtuB,EAAI,IACxCwvB,EAAWT,EAAOG,EAAOZ,WAAWtuB,EAAI,IACxCyvB,EAAWV,EAAOG,EAAOZ,WAAWtuB,EAAI,IAExC6vB,EAAMR,KAAOC,GAAY,EAAIC,GAAY,EACzCM,EAAMR,MAAmB,GAAXE,IAAkB,EAAIC,GAAY,EAChDK,EAAMR,MAAmB,EAAXG,IAAiB,EAAe,GAAXC,EAGvC,OAAOC,GAGW5rB,EAAQgsB,gBAAkB,SAAyBJ,GAGrE,IAFA,IAAIxvB,EAASwvB,EAAOxvB,OAChB2vB,EAAQ,GACHhrB,EAAK,EAAGA,EAAK3E,EAAQ2E,GAAM,EAChCgrB,EAAM7qB,KAAK0qB,EAAO7qB,EAAK,IAAM,EAAI6qB,EAAO7qB,IAE5C,OAAOgrB,GAGW/rB,EAAQisB,gBAAkB,SAAyBL,GAGrE,IAFA,IAAIxvB,EAASwvB,EAAOxvB,OAChB2vB,EAAQ,GACHG,EAAM,EAAGA,EAAM9vB,EAAQ8vB,GAAO,EACnCH,EAAM7qB,KAAK0qB,EAAOM,EAAM,IAAM,GAAKN,EAAOM,EAAM,IAAM,GAAKN,EAAOM,EAAM,IAAM,EAAIN,EAAOM,IAE7F,OAAOH,I,kCC/GXhwB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQmsB,kBAAoBnsB,EAAQosB,sBAAwBpsB,EAAQoZ,kBAAevY,EAEnF,IAAI6S,EAAQ,EAAQ,MAIhB2Y,EAAkB1vB,EAFD,EAAQ,OAMzB2vB,EAAkB3vB,EAFD,EAAQ,OAIzBwX,EAAa,EAAQ,MAErBoY,EAAW,EAAQ,MAEvB,SAAS5vB,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAGvF,IAEIyvB,EAAgB,CAAC,KAAM,KAAM,QAqF7BC,GAnFezsB,EAAQoZ,aAAe,SAAsBnG,GAC5D,IAAIM,EAASN,EAAUM,OACvB,IAAKA,EACD,OAAO,KAGX,EAAG,CAEC,IAD4D,IAA3CiZ,EAAc9U,QAAQnE,EAAOrV,SAE1C,OAAOqV,EAEXA,EAASA,EAAOA,aACXA,GAET,OAAON,EAAUM,QAGOvT,EAAQosB,sBAAwB,SAA+Bxe,EAAMqF,EAAWmC,GACxG,IAAI4B,EAAY/D,EAAUE,MAAM6D,UAEhC,GAAKA,EAAL,CAIA,IAAI7D,EAAQvF,EAAKnQ,cAAcG,YAAY+X,iBAAiB/H,EAAM,MAC9D8e,EAAU9e,EAAKnQ,cAAc4oB,cAAc,sBAQ/C,QAPA,EAAI3S,EAAMqI,eAAe5I,EAAOuZ,GAEhCA,EAAQvZ,MAAMvB,SAAW,WACzB8a,EAAQvZ,MAAMwZ,OAAS,OACvBD,EAAQvZ,MAAMyC,QAAU,QACxB8W,EAAQvZ,MAAM2D,cAAgB,SAEtBE,EAAU+N,mBACd,KAAK5Q,EAAWkN,oBAAoBE,QAChCmL,EAAQvZ,MAAM5U,KAAO,OACrBmuB,EAAQvZ,MAAMyZ,MAAQhf,EAAKnQ,cAAcG,YAAY8B,WAAauT,EAAUlF,OAAOxP,KAAO0U,EAAUE,MAAMmE,OAAO,GAAG/G,iBAAiB0C,EAAUlF,OAAO1P,OAxC/I,EAwCuK,KAC9KquB,EAAQvZ,MAAM0Z,UAAY,QAC1B,MACJ,KAAK1Y,EAAWkN,oBAAoBC,OAChCoL,EAAQvZ,MAAM5U,KAAO0U,EAAUlF,OAAOxP,KAAO0U,EAAUE,MAAMmE,OAAO,GAAG/G,iBAAiB0C,EAAUlF,OAAO1P,OAAS,KAClHquB,EAAQvZ,MAAMyZ,MAAQ,OACtBF,EAAQvZ,MAAM0Z,UAAY,OAIlC,IAAI3H,OAAO,EACP4H,EAAa7Z,EAAUE,MAAMmE,OAAO,GAAG/G,iBAAiB0C,EAAUlF,OAAO1P,OACzE0uB,EAAa/V,EAAU6N,eAC3B,GAAIkI,EACA,GAA0B,QAAtBA,EAAWxN,OAAkB,CAC7B,IAAI9F,EAAQ7L,EAAKnQ,cAAc4oB,cAAc,OAC7C5M,EAAMgC,IAAMsR,EAAWvN,KAAK,GAC5BkN,EAAQvZ,MAAM3U,IAAMyU,EAAUlF,OAAOvP,IAAMsuB,EAAa,KACxDJ,EAAQvZ,MAAM9U,MAAQ,OACtBquB,EAAQvZ,MAAM7U,OAAS,OACvBouB,EAAQnG,YAAY9M,OACjB,CACH,IAAIqE,EAAmD,GAA5ClL,WAAWK,EAAUE,MAAMC,KAAKC,UAC3CqZ,EAAQvZ,MAAM3U,IAAMyU,EAAUlF,OAAOvP,IAAMsuB,EAAa7Z,EAAUlF,OAAOzP,OAAS,IAAMwf,EAAO,KAC/F4O,EAAQvZ,MAAM9U,MAAQyf,EAAO,KAC7B4O,EAAQvZ,MAAM7U,OAASwf,EAAO,KAC9B4O,EAAQvZ,MAAM6K,gBAAkB7K,EAAM0R,mBAEJ,iBAAxB5R,EAAUoG,YACxB6L,EAAOtX,EAAKnQ,cAAcuvB,eAAeb,EAAkBlZ,EAAUoG,UAAWrC,EAAU8N,eAAe,IACzG4H,EAAQnG,YAAYrB,GACpBwH,EAAQvZ,MAAM3U,IAAMyU,EAAUlF,OAAOvP,IAAMsuB,EAAa,MAI5D,IAAIte,EAAOZ,EAAKnQ,cAAc+Q,KAC9BA,EAAK+X,YAAYmG,GAEbxH,GACAjS,EAAUsC,WAAWrU,KAAKorB,EAAgBrvB,QAAQgwB,aAAa/H,EAAMjS,IACrEzE,EAAKmY,YAAY+F,IAGjBzZ,EAAUsC,WAAWrU,KAAK,IAAImrB,EAAgBpvB,QAAQyvB,EAASzZ,EAAWmC,EAAgB,MAIhF,CACd8X,SAAU,CAAC,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,GAC9DC,OAAQ,CAAC,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,IAAK,KAAM,OAG3EhL,EAAW,CACX+K,SAAU,CAAC,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC1KC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAGxLrK,EAAS,CACToK,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC5KC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG5MxK,EAAW,CACXuK,SAAU,CAAC,IAAO,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACjLC,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAG7LC,EAAwB,SAA+BltB,EAAOuC,EAAKkM,EAAK0e,EAASC,EAAUC,GAC3F,OAAIrtB,EAAQuC,GAAOvC,EAAQyO,EAChBwd,EAAkBjsB,EAAOotB,EAAUC,EAAOnxB,OAAS,GAGvDixB,EAAQH,SAASM,QAAO,SAAUC,EAAQC,EAASrY,GACtD,KAAOnV,GAASwtB,GACZxtB,GAASwtB,EACTD,GAAUJ,EAAQF,OAAO9X,GAE7B,OAAOoY,IACR,IAAMF,GAGTI,EAAuC,SAA8CztB,EAAO0tB,EAAsBC,EAAWC,GAC7H,IAAIL,EAAS,GAEb,GACSI,GACD3tB,IAEJutB,EAASK,EAAS5tB,GAASutB,EAC3BvtB,GAAS0tB,QACJ1tB,EAAQ0tB,GAAwBA,GAEzC,OAAOH,GAGPM,EAA8B,SAAqC7tB,EAAO8tB,EAAqBC,EAAmBJ,EAAWN,GAC7H,IAAIK,EAAuBK,EAAoBD,EAAsB,EAErE,OAAQ9tB,EAAQ,EAAI,IAAM,KAAOytB,EAAqCpuB,KAAK2uB,IAAIhuB,GAAQ0tB,EAAsBC,GAAW,SAAU/C,GAC9H,OAAO,EAAIyB,EAAS7B,eAAenrB,KAAK4uB,MAAMrD,EAAY8C,GAAwBI,MACjFT,IAGLa,EAAgC,SAAuCluB,EAAOmtB,GAC9E,IAAIE,EAASpxB,UAAUC,OAAS,QAAsByE,IAAjB1E,UAAU,GAAmBA,UAAU,GAAK,KAE7EyxB,EAAuBP,EAAQjxB,OACnC,OAAOuxB,EAAqCpuB,KAAK2uB,IAAIhuB,GAAQ0tB,GAAsB,GAAO,SAAU9C,GAChG,OAAOuC,EAAQ9tB,KAAK4uB,MAAMrD,EAAY8C,OACrCL,GAQLc,EAAmB,SAA0BnuB,EAAOouB,EAASC,EAAaC,EAAcjB,EAAQkB,GAChG,GAAIvuB,GAAS,MAAQA,EAAQ,KACzB,OAAOisB,EAAkBjsB,EAAOiU,EAAWiN,gBAAgBO,YAAa4L,EAAOnxB,OAAS,GAE5F,IAAIsyB,EAAMnvB,KAAK2uB,IAAIhuB,GACfutB,EAASF,EAEb,GAAY,IAARmB,EACA,OAAOJ,EAAQ,GAAKb,EAGxB,IAAK,IAAIkB,EAAQ,EAAGD,EAAM,GAAKC,GAAS,EAAGA,IAAS,CAChD,IAAIC,EAAcF,EAAM,GAEJ,IAAhBE,IAAqB,EAAIlb,EAAMyG,UAAUsU,EAnBrC,IAmBqE,KAAXhB,EAC9DA,EAASa,EAAQM,GAAenB,EACzBmB,EAAc,GAAqB,IAAhBA,GAA+B,IAAVD,GAA+B,IAAhBC,GAA+B,IAAVD,IAAe,EAAIjb,EAAMyG,UAAUsU,EApBvG,IAoBuJ,IAAhBG,GAA+B,IAAVD,IAAe,EAAIjb,EAAMyG,UAAUsU,EAnB1L,IAmB+NvuB,EAAQ,KAAuB,IAAhB0uB,GAAqBD,EAAQ,IAAK,EAAIjb,EAAMyG,UAAUsU,EAlBrS,GAmBnBhB,EAASa,EAAQM,IAAgBD,EAAQ,EAAIJ,EAAYI,EAAQ,GAAK,IAAMlB,EACrD,IAAhBmB,GAAqBD,EAAQ,IACpClB,EAASc,EAAYI,EAAQ,GAAKlB,GAEtCiB,EAAMnvB,KAAK4uB,MAAMO,EAAM,IAG3B,OAAQxuB,EAAQ,EAAIsuB,EAAe,IAAMf,GAQzCtB,EAAoBnsB,EAAQmsB,kBAAoB,SAA2BjsB,EAAOuS,EAAMoc,GACxF,IAAIC,EAAgBD,EAAe,KAAO,GACtCE,EAAYF,EAAe,IAAM,GACjCG,EAAeH,EAAe,KAAO,GACzC,OAAQpc,GACJ,KAAK0B,EAAWiN,gBAAgBI,KAC5B,MAAO,IACX,KAAKrN,EAAWiN,gBAAgBJ,OAC5B,MAAO,IACX,KAAK7M,EAAWiN,gBAAgBK,OAC5B,MAAO,IACX,KAAKtN,EAAWiN,gBAAgBQ,qBAC5B,IAAI6L,EAASM,EAA4B7tB,EAAO,GAAI,IAAI,EAAM4uB,GAC9D,OAAOrB,EAAOrxB,OAAS,EAAI,IAAMqxB,EAASA,EAC9C,KAAKtZ,EAAWiN,gBAAgBO,YAC5B,OAAOyM,EAA8BluB,EAAO,aAAc6uB,GAC9D,KAAK5a,EAAWiN,gBAAgBS,YAC5B,OAAOuL,EAAsBltB,EAAO,EAAG,KAAMusB,EAAatY,EAAWiN,gBAAgBM,QAASoN,GAAe7rB,cACjH,KAAKkR,EAAWiN,gBAAgBU,YAC5B,OAAOsL,EAAsBltB,EAAO,EAAG,KAAMusB,EAAatY,EAAWiN,gBAAgBM,QAASoN,GAClG,KAAK3a,EAAWiN,gBAAgBW,YAC5B,OAAOgM,EAA4B7tB,EAAO,IAAK,KAAK,EAAO4uB,GAC/D,KAAK3a,EAAWiN,gBAAgBY,YAC5B,OAAO+L,EAA4B7tB,EAAO,GAAI,KAAK,EAAO4uB,GAC9D,KAAK3a,EAAWiN,gBAAgBa,YAC5B,OAAO8L,EAA4B7tB,EAAO,GAAI,IAAI,EAAO4uB,GAC7D,KAAK3a,EAAWiN,gBAAgBc,aAC5B,OAAO6L,EAA4B7tB,EAAO,KAAM,MAAM,EAAM4uB,GAChE,KAAK3a,EAAWiN,gBAAgBe,SAChC,KAAKhO,EAAWiN,gBAAgBqD,eAC5B,OAAO2I,EAAsBltB,EAAO,EAAG,KAAMiiB,EAAUhO,EAAWiN,gBAAgBM,QAASoN,GAC/F,KAAK3a,EAAWiN,gBAAgBuC,eAC5B,OAAOyJ,EAAsBltB,EAAO,EAAG,KAAMiiB,EAAUhO,EAAWiN,gBAAgBM,QAASoN,GAAe7rB,cAC9G,KAAKkR,EAAWiN,gBAAgBgB,QAC5B,OAAO2L,EAA4B7tB,EAAO,KAAM,MAAM,EAAM4uB,GAChE,KAAK3a,EAAWiN,gBAAgBiB,UAChC,KAAKlO,EAAWiN,gBAAgBkC,MAC5B,OAAOyK,EAA4B7tB,EAAO,KAAM,MAAM,EAAM4uB,GAChE,KAAK3a,EAAWiN,gBAAgBkB,mBAC5B,OAAO8L,EAA8BluB,EAAO,eAAgB6uB,GAChE,KAAK5a,EAAWiN,gBAAgBmB,kBAC5B,OAAO6L,EAA8BluB,EAAO,aAAc6uB,GAC9D,KAAK5a,EAAWiN,gBAAgBoB,gBAChC,KAAKrO,EAAWiN,gBAAgBoD,sBAC5B,OAAO6J,EAAiBnuB,EAAO,aAjDR,OAiDoD,IAAK6uB,EAAWE,IAC/F,KAAK9a,EAAWiN,gBAAgBmD,oBAC5B,OAAO8J,EAAiBnuB,EAAO,aAlDV,OAkDoD,IAAK6uB,EAAWG,IAC7F,KAAK/a,EAAWiN,gBAAgB8C,sBAC5B,OAAOmK,EAAiBnuB,EAAO,aArDR,OAqDoD,IAAK6uB,EAAWE,IAC/F,KAAK9a,EAAWiN,gBAAgB6C,oBAC5B,OAAOoK,EAAiBnuB,EAAO,aAtDV,OAsDoD,IAAK6uB,EAAWG,IAC7F,KAAK/a,EAAWiN,gBAAgB8B,kBAC5B,OAAOmL,EAAiBnuB,EAAO,aAAc,OAvDjC,OAuD4D6uB,EAAW,GACvF,KAAK5a,EAAWiN,gBAAgB6B,gBAC5B,OAAOoL,EAAiBnuB,EAAO,aAAc,OAzDjC,OAyD4D6uB,EAAWG,GACvF,KAAK/a,EAAWiN,gBAAgBmC,qBAC5B,OAAO8K,EAAiBnuB,EAAO,aAAc,OA1DnC,QA0D4D8uB,EAAcE,GACxF,KAAK/a,EAAWiN,gBAAgBqC,sBAC5B,OAAO4K,EAAiBnuB,EAAO,aAAc,OA5DnC,QA4D4D8uB,EAAc,GACxF,KAAK7a,EAAWiN,gBAAgBoC,oBAC5B,OAAO6K,EAAiBnuB,EAAO,aAAc,MA9DnC,QA8D2D8uB,EAAcE,GACvF,KAAK/a,EAAWiN,gBAAgBqB,WAC5B,OAAOsL,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBuB,SAC5B,OAAOyK,EAAsBltB,EAAO,EAAG,MAAOyiB,EAAUxO,EAAWiN,gBAAgBM,QAASoN,GAChG,KAAK3a,EAAWiN,gBAAgBwB,SAC5B,OAAOmL,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgByB,SAC5B,OAAOkL,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgB0B,OAC5B,OAAOsK,EAAsBltB,EAAO,EAAG,MAAO4iB,EAAQ3O,EAAWiN,gBAAgBM,QAASoN,GAC9F,KAAK3a,EAAWiN,gBAAgB2B,SAC5B,OAAOqL,EAA8BluB,EAAO,oDAChD,KAAKiU,EAAWiN,gBAAgB4B,eAC5B,OAAOoL,EAA8BluB,EAAO,mDAChD,KAAKiU,EAAWiN,gBAAgB+B,QAC5B,OAAO4K,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBgC,SAC5B,OAAOgL,EAA8BluB,EAAO,mDAAoD6uB,GACpG,KAAK5a,EAAWiN,gBAAgBiC,eAC5B,OAAO+K,EAA8BluB,EAAO,kDAAmD6uB,GACnG,KAAK5a,EAAWiN,gBAAgBsC,IAC5B,OAAOqK,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgByC,UAC5B,OAAOkK,EAA4B7tB,EAAO,KAAQ,MAAQ,EAAM4uB,GACpE,KAAK3a,EAAWiN,gBAAgB0C,QAC5B,OAAOiK,EAA4B7tB,EAAO,KAAQ,MAAQ,EAAM4uB,GACpE,KAAK3a,EAAWiN,gBAAgB2C,MAC5B,OAAOgK,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgB4C,QAC5B,OAAO+J,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgB+C,MAC5B,OAAO4J,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBgD,OAC5B,OAAO2J,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBiD,KAC5B,OAAO0J,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBkD,QAC5B,OAAOyJ,EAA4B7tB,EAAO,KAAO,MAAO,EAAM4uB,GAClE,KAAK3a,EAAWiN,gBAAgBM,QAChC,QACI,OAAOqM,EAA4B7tB,EAAO,GAAI,IAAI,EAAM4uB,M,kCCxTpE/yB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAIoB,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEfof,EAAQ,EAAQ,MAEhBxM,EAAkB,EAAQ,MAI9B,IAAI0a,EAAgB,SAAuBC,EAAUC,GACjD,IAAIC,EAAU/vB,KAAKoP,IAAIyC,MAAM,KAAMge,EAASG,WAAWjQ,KAAI,SAAUkQ,GACjE,OAAOA,EAAUC,SAEjBC,EAAI,EAAInwB,KAAKoP,IAAI,EAAG2gB,GACxBF,EAASG,WAAW7O,SAAQ,SAAU8O,GAClCH,EAAeM,aAAaD,EAAIF,EAAUC,KAAMD,EAAU/Y,MAAMmZ,gBAIpE9vB,EAAiB,WACjB,SAASA,EAAeX,IAb5B,SAAyBkD,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAcxGiB,CAAgBC,KAAMzC,GAEtByC,KAAKpD,OAASA,GAAkBoP,SAAS8X,cAAc,UACvD9jB,KAAKstB,eAAiB1wB,EAgO1B,OA7NAmC,EAAaxB,EAAgB,CAAC,CAC1BxD,IAAK,SACL4D,MAAO,SAAgB4vB,GACnBvtB,KAAKukB,IAAMvkB,KAAKpD,OAAO4nB,WAAW,MAClCxkB,KAAKutB,QAAUA,EACVvtB,KAAKstB,eACNttB,KAAKpD,OAAOd,MAAQkB,KAAK4uB,MAAM2B,EAAQzxB,MAAQyxB,EAAQ7wB,OACvDsD,KAAKpD,OAAOb,OAASiB,KAAK4uB,MAAM2B,EAAQxxB,OAASwxB,EAAQ7wB,OACzDsD,KAAKpD,OAAOgU,MAAM9U,MAAQyxB,EAAQzxB,MAAQ,KAC1CkE,KAAKpD,OAAOgU,MAAM7U,OAASwxB,EAAQxxB,OAAS,MAGhDiE,KAAKukB,IAAI7nB,MAAMsD,KAAKutB,QAAQ7wB,MAAOsD,KAAKutB,QAAQ7wB,OAChDsD,KAAKukB,IAAIiJ,WAAWD,EAAQzwB,GAAIywB,EAAQxwB,GACxCiD,KAAKukB,IAAIkJ,aAAe,SACxBF,EAAQxyB,OAAOE,IAAI,gCAAkCsyB,EAAQzxB,MAAQ,IAAMyxB,EAAQxxB,OAAS,OAASwxB,EAAQzwB,EAAI,IAAMywB,EAAQxwB,EAAI,gBAAkBiD,KAAKutB,QAAQ7wB,SAEvK,CACC3C,IAAK,OACL4D,MAAO,SAAc+vB,EAAWC,GAC5B,IAAI5a,EAAQ/S,KAER0tB,EAAU7zB,SACVmG,KAAKukB,IAAIqJ,OACTF,EAAUvP,SAAQ,SAAU1Q,GACxBsF,EAAMtF,KAAKA,GACXsF,EAAMwR,IAAI1I,WAIlB8R,IAEID,EAAU7zB,QACVmG,KAAKukB,IAAIsJ,YAGlB,CACC9zB,IAAK,YACL4D,MAAO,SAAmBuZ,EAAOpd,EAAQg0B,GACrC9tB,KAAKukB,IAAIE,UAAUvN,EAAOpd,EAAOkC,KAAMlC,EAAOmC,IAAKnC,EAAOgC,MAAOhC,EAAOiC,OAAQ+xB,EAAY9xB,KAAM8xB,EAAY7xB,IAAK6xB,EAAYhyB,MAAOgyB,EAAY/xB,UAEvJ,CACChC,IAAK,YACL4D,MAAO,SAAmB8P,EAAMyG,GAC5BlU,KAAKyN,KAAKA,GACVzN,KAAKukB,IAAIgB,UAAYrR,EAAMmZ,WAC3BrtB,KAAKukB,IAAIwJ,SAEd,CACCh0B,IAAK,OACL4D,MAAO,SAAcuW,GACjBlU,KAAKukB,IAAIgB,UAAYrR,EAAMmZ,WAC3BrtB,KAAKukB,IAAIwJ,SAEd,CACCh0B,IAAK,YACL4D,MAAO,WACH,OAAOxC,QAAQ2pB,QAAQ9kB,KAAKpD,UAEjC,CACC7C,IAAK,OACL4D,MAAO,SAAcqwB,GACjB,IAAIC,EAASjuB,KAEbA,KAAKukB,IAAI2J,YACLpwB,MAAMC,QAAQiwB,GACdA,EAAM7P,SAAQ,SAAUgQ,EAAOrb,GAC3B,IAAII,EAAQib,EAAMje,OAASwO,EAAMJ,KAAKC,OAAS4P,EAAQA,EAAMjb,MAC/C,IAAVJ,EACAmb,EAAO1J,IAAI6J,OAAOlb,EAAMpW,EAAGoW,EAAMnW,GAEjCkxB,EAAO1J,IAAI8J,OAAOnb,EAAMpW,EAAGoW,EAAMnW,GAGjCoxB,EAAMje,OAASwO,EAAMJ,KAAKE,cAC1ByP,EAAO1J,IAAI+J,cAAcH,EAAMI,aAAazxB,EAAGqxB,EAAMI,aAAaxxB,EAAGoxB,EAAMK,WAAW1xB,EAAGqxB,EAAMK,WAAWzxB,EAAGoxB,EAAMM,IAAI3xB,EAAGqxB,EAAMM,IAAI1xB,MAI5IiD,KAAKukB,IAAImK,IAAIV,EAAMlxB,EAAIkxB,EAAMW,OAAQX,EAAMjxB,EAAIixB,EAAMW,OAAQX,EAAMW,OAAQ,EAAa,EAAV3xB,KAAK4xB,IAAQ,GAG/F5uB,KAAKukB,IAAIsK,cAEd,CACC90B,IAAK,YACL4D,MAAO,SAAmBb,EAAGC,EAAGjB,EAAOC,EAAQmY,GAC3ClU,KAAKukB,IAAIgB,UAAYrR,EAAMmZ,WAC3BrtB,KAAKukB,IAAIiB,SAAS1oB,EAAGC,EAAGjB,EAAOC,KAEpC,CACChC,IAAK,uBACL4D,MAAO,SAA8B6N,EAAQqhB,GACzC,IAAIiC,EAAiB9uB,KAAKukB,IAAIwK,qBAAqBvjB,EAAOxP,KAAO6wB,EAASmC,UAAUC,GAAIzjB,EAAOvP,IAAM4wB,EAASmC,UAAUE,GAAI1jB,EAAOxP,KAAO6wB,EAASmC,UAAUG,GAAI3jB,EAAOvP,IAAM4wB,EAASmC,UAAUI,IAEjMxC,EAAcC,EAAUiC,GACxB9uB,KAAKukB,IAAIgB,UAAYuJ,EACrB9uB,KAAKukB,IAAIiB,SAASha,EAAOxP,KAAMwP,EAAOvP,IAAKuP,EAAO1P,MAAO0P,EAAOzP,UAErE,CACChC,IAAK,uBACL4D,MAAO,SAA8B6N,EAAQqhB,GACzC,IAAIwC,EAASrvB,KAETlD,EAAI0O,EAAOxP,KAAO6wB,EAASyC,OAAOxyB,EAClCC,EAAIyO,EAAOvP,IAAM4wB,EAASyC,OAAOvyB,EAEjCwyB,EAAiBvvB,KAAKukB,IAAIiL,qBAAqB1yB,EAAGC,EAAG,EAAGD,EAAGC,EAAG8vB,EAAS8B,OAAO7xB,GAClF,GAAKyyB,EAOL,GAHA3C,EAAcC,EAAU0C,GACxBvvB,KAAKukB,IAAIgB,UAAYgK,EAEjB1C,EAAS8B,OAAO7xB,IAAM+vB,EAAS8B,OAAO5xB,EAAG,CAEzC,IAAI0yB,EAAOjkB,EAAOxP,KAAO,GAAMwP,EAAO1P,MAClC4zB,EAAOlkB,EAAOvP,IAAM,GAAMuP,EAAOzP,OACjCoxB,EAAIN,EAAS8B,OAAO5xB,EAAI8vB,EAAS8B,OAAO7xB,EACxC6yB,EAAO,EAAIxC,EAEfntB,KAAKiW,UAAUwZ,EAAMC,EAAM,CAAC,EAAG,EAAG,EAAGvC,EAAG,EAAG,IAAI,WAC3C,OAAOkC,EAAO9K,IAAIiB,SAASha,EAAOxP,KAAM2zB,GAAQnkB,EAAOvP,IAAMyzB,GAAQA,EAAMlkB,EAAO1P,MAAO0P,EAAOzP,OAAS4zB,WAG7G3vB,KAAKukB,IAAIiB,SAASha,EAAOxP,KAAMwP,EAAOvP,IAAKuP,EAAO1P,MAAO0P,EAAOzP,UAGzE,CACChC,IAAK,eACL4D,MAAO,SAAsB8P,EAAMyJ,EAAO0Y,EAAWC,EAASC,GAC1D9vB,KAAKyN,KAAKA,GACVzN,KAAKukB,IAAIgB,UAAYvlB,KAAKukB,IAAIwL,cAAc/vB,KAAKgwB,YAAY9Y,EAAO0Y,GAAY,UAChF5vB,KAAKukB,IAAIiJ,UAAUqC,EAASC,GAC5B9vB,KAAKukB,IAAIwJ,OACT/tB,KAAKukB,IAAIiJ,WAAWqC,GAAUC,KAEnC,CACC/1B,IAAK,iBACL4D,MAAO,SAAwBsyB,EAAY/b,EAAOrD,EAAM8E,EAAgBua,GACpE,IAAIC,EAASnwB,KAEbA,KAAKukB,IAAI1T,KAAO,CAACA,EAAKuf,UAAWvf,EAAKwf,YAAaxf,EAAKyf,WAAYzf,EAAKC,SAAUD,EAAK0f,YAAYC,KAAK,KAEzGP,EAAW9R,SAAQ,SAAUwE,GAezB,GAdAwN,EAAO5L,IAAIgB,UAAYrR,EAAMmZ,WACzB6C,GAAevN,EAAKA,KAAK9F,OAAOhjB,OAChCq2B,EAAY3G,MAAM,GAAG5b,UAAUwQ,SAAQ,SAAUtI,GAC7Csa,EAAO5L,IAAIkM,YAAc5a,EAAW3B,MAAMmZ,WAC1C8C,EAAO5L,IAAImM,cAAgB7a,EAAWga,QAAUM,EAAO5C,QAAQ7wB,MAC/DyzB,EAAO5L,IAAIoM,cAAgB9a,EAAWia,QAAUK,EAAO5C,QAAQ7wB,MAC/DyzB,EAAO5L,IAAIqM,WAAa/a,EAAWgb,KAEnCV,EAAO5L,IAAIuM,SAASnO,EAAKA,KAAMA,EAAKnX,OAAOxP,KAAM2mB,EAAKnX,OAAOvP,IAAM0mB,EAAKnX,OAAOzP,WAGnFo0B,EAAO5L,IAAIuM,SAASnO,EAAKA,KAAMA,EAAKnX,OAAOxP,KAAM2mB,EAAKnX,OAAOvP,IAAM0mB,EAAKnX,OAAOzP,QAG5D,OAAnB4Z,EAAyB,CACzB,IAAIwR,EAAsBxR,EAAewR,qBAAuBjT,EAChEyB,EAAeuR,mBAAmB/I,SAAQ,SAAU+I,GAChD,OAAQA,GACJ,KAAKhV,EAAgBkU,qBAAqBQ,UAItC,IACImK,EADwBZ,EAAO5C,QAAQyD,YAAYC,WAAWpgB,GAC7BkgB,SAErCZ,EAAOe,UAAUvO,EAAKnX,OAAOxP,KAAMgB,KAAKuf,MAAMoG,EAAKnX,OAAOvP,IAAM80B,GAAWpO,EAAKnX,OAAO1P,MAAO,EAAGqrB,GACjG,MACJ,KAAKjV,EAAgBkU,qBAAqBS,SACtCsJ,EAAOe,UAAUvO,EAAKnX,OAAOxP,KAAMgB,KAAKuf,MAAMoG,EAAKnX,OAAOvP,KAAM0mB,EAAKnX,OAAO1P,MAAO,EAAGqrB,GACtF,MACJ,KAAKjV,EAAgBkU,qBAAqBU,aAEtC,IACIqK,EADyBhB,EAAO5C,QAAQyD,YAAYC,WAAWpgB,GAC/BsgB,OAEpChB,EAAOe,UAAUvO,EAAKnX,OAAOxP,KAAMgB,KAAKC,KAAK0lB,EAAKnX,OAAOvP,IAAMk1B,GAASxO,EAAKnX,OAAO1P,MAAO,EAAGqrB,aAOvH,CACCptB,IAAK,cACL4D,MAAO,SAAqBuZ,EAAOqE,GAC/B,GAAIrE,EAAMpb,QAAUyf,EAAKzf,OAASob,EAAMnb,SAAWwf,EAAKxf,OACpD,OAAOmb,EAGX,IAAIta,EAASoD,KAAKpD,OAAO1B,cAAc4oB,cAAc,UAKrD,OAJAlnB,EAAOd,MAAQyf,EAAKzf,MACpBc,EAAOb,OAASwf,EAAKxf,OACXa,EAAO4nB,WAAW,MACxBC,UAAUvN,EAAO,EAAG,EAAGA,EAAMpb,MAAOob,EAAMnb,OAAQ,EAAG,EAAGwf,EAAKzf,MAAOyf,EAAKxf,QACtEa,IAEZ,CACC7C,IAAK,aACL4D,MAAO,SAAoBsX,GACvBjV,KAAKukB,IAAI6M,YAAcnc,IAE5B,CACClb,IAAK,YACL4D,MAAO,SAAmBkyB,EAASC,EAASuB,EAAQ1D,GAChD3tB,KAAKukB,IAAIqJ,OACT5tB,KAAKukB,IAAIiJ,UAAUqC,EAASC,GAC5B9vB,KAAKukB,IAAItO,UAAUob,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IACjFrxB,KAAKukB,IAAIiJ,WAAWqC,GAAUC,GAE9BnC,IAEA3tB,KAAKukB,IAAIsJ,cAIVtwB,EArOU,GAwOrBE,EAAQ/C,QAAU6C,G,kCC9PlB/D,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAIoB,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAInB,IAAIgyB,EAAS,WACT,SAASA,EAAOC,EAASC,EAAIte,IAHjC,SAAyBpT,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAIxGiB,CAAgBC,KAAMsxB,GAEtBtxB,KAAKuxB,QAA4B,oBAAXlM,QAA0BkM,EAChDvxB,KAAKkT,MAAQA,GAAgBue,KAAKC,MAClC1xB,KAAKwxB,GAAKA,EAsCd,OAnCAzyB,EAAauyB,EAAQ,CAAC,CAClBv3B,IAAK,QACL4D,MAAO,SAAe6zB,GAClB,OAAO,IAAIF,EAAOtxB,KAAKuxB,QAASC,EAAIxxB,KAAKkT,SAK9C,CACCnZ,IAAK,MACL4D,MAAO,WACH,GAAIqC,KAAKuxB,SAAWlM,OAAOsM,SAAWtM,OAAOsM,QAAQ12B,IAAK,CACtD,IAAK,IAAI22B,EAAOh4B,UAAUC,OAAQojB,EAAOnf,MAAM8zB,GAAOC,EAAO,EAAGA,EAAOD,EAAMC,IACzE5U,EAAK4U,GAAQj4B,UAAUi4B,GAG3BC,SAAS93B,UAAU+3B,KAAK73B,KAAKmrB,OAAOsM,QAAQ12B,IAAKoqB,OAAOsM,SAAS9iB,MAAMwW,OAAOsM,QAAS,CAACF,KAAKC,MAAQ1xB,KAAKkT,MAAQ,KAAMlT,KAAKwxB,GAAK,gBAAkBxxB,KAAKwxB,GAAK,KAAO,gBAAgBha,OAAO,GAAG+R,MAAMrvB,KAAK+iB,EAAM,QAMzN,CACCljB,IAAK,QACL4D,MAAO,WACH,GAAIqC,KAAKuxB,SAAWlM,OAAOsM,SAAWtM,OAAOsM,QAAQK,MAAO,CACxD,IAAK,IAAIC,EAAQr4B,UAAUC,OAAQojB,EAAOnf,MAAMm0B,GAAQC,EAAQ,EAAGA,EAAQD,EAAOC,IAC9EjV,EAAKiV,GAASt4B,UAAUs4B,GAG5BJ,SAAS93B,UAAU+3B,KAAK73B,KAAKmrB,OAAOsM,QAAQK,MAAO3M,OAAOsM,SAAS9iB,MAAMwW,OAAOsM,QAAS,CAACF,KAAKC,MAAQ1xB,KAAKkT,MAAQ,KAAMlT,KAAKwxB,GAAK,gBAAkBxxB,KAAKwxB,GAAK,KAAO,gBAAgBha,OAAO,GAAG+R,MAAMrvB,KAAK+iB,EAAM,UAKvNqU,EA5CE,GA+Cb7zB,EAAQ/C,QAAU42B,G,kCCvDlB93B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQiY,aAAejY,EAAQue,mBAAgB1d,EAE/C,IAIgC9D,EAJ5B23B,EAAU,EAAQ,MAElB1X,GAE4BjgB,EAFM23B,IAEe33B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEnEiD,EAAQue,cAAgB,CACxCG,IAAK,EACLD,MAAO,EACPE,OAAQ,EACRH,KAAM,GAJV,IAOIuL,EAAQ,CAAC,MAAO,QAAS,SAAU,QAEpB/pB,EAAQiY,aAAe,SAAsB9E,GAC5D,OAAO4W,EAAMzK,KAAI,SAAU2K,GACvB,OAAO,IAAIjN,EAAS/f,QAAQkW,EAAMgJ,iBAAiB,WAAa8N,S,kCCtBxEluB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIy0B,EAAgB30B,EAAQ20B,cAAgB,CACxCC,OAAQ,EACRC,WAAY,GAGQ70B,EAAQ+X,kBAAoB,SAA2BN,GAC3E,OAAQA,GACJ,IAAK,aACD,OAAOkd,EAAcE,WACzB,IAAK,SACL,QACI,OAAOF,EAAcC,U,kCCdjC74B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAI8Y,EAAWhZ,EAAQgZ,SAAW,CAC9BC,OAAQ,EACRqB,SAAU,EACVwa,SAAU,EACVC,MAAO,EACPC,OAAQ,GAGQh1B,EAAQ+V,cAAgB,SAAuBnE,GAC/D,OAAQA,GACJ,IAAK,WACD,OAAOoH,EAASsB,SACpB,IAAK,WACD,OAAOtB,EAAS8b,SACpB,IAAK,QACD,OAAO9b,EAAS+b,MACpB,IAAK,SACD,OAAO/b,EAASgc,OAGxB,OAAOhc,EAASC,S,kCCvBpBld,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIolB,EAAiBtlB,EAAQslB,eAAiB,CAC1ClL,KAAM,EACNmL,UAAW,EACXG,UAAW,EACXL,WAAY,GAGSrlB,EAAQuY,mBAAqB,SAA4BD,GAC9E,OAAQA,GACJ,IAAK,YACD,OAAOgN,EAAeI,UAC1B,IAAK,YACD,OAAOJ,EAAeC,UAC1B,IAAK,aACD,OAAOD,EAAeD,WAG9B,OAAOC,EAAelL,O,kCCpB1Bre,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ2Z,oBAAsB3Z,EAAQi1B,oBAAsBj1B,EAAQk1B,sBAAwBl1B,EAAQm1B,mBAAqBn1B,EAAQuW,qBAAuBvW,EAAQiW,iBAAmBjW,EAAQoW,cAAgBpW,EAAQ0W,iBAAc7V,EAEjO,IAEIyrB,EAAkB3vB,EAFD,EAAQ,OAIzBgX,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAIlBwhB,EAAWz4B,EAFD,EAAQ,OAMlB2Q,EAAW3Q,EAFD,EAAQ,OAMlB8W,EAAU9W,EAFD,EAAQ,OAMjBqgB,EAAWrgB,EAFD,EAAQ,OAQlB+W,GAJU,EAAQ,MAEJ,EAAQ,MAEd,EAAQ,OAEpB,SAAS/W,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAErEiD,EAAQ0W,YAAc,IAAIjD,EAAQxW,QAAQ,CAAC,GAAI,GAAI,KAArE,IACIo4B,EAAqB,IAAI5hB,EAAQxW,QAAQ,CAAC,IAAK,IAAK,MACpDq4B,EAAyB,IAAI7hB,EAAQxW,QAAQ,CAAC,IAAK,IAAK,MACxDs4B,EAAe,CACftnB,YAAa,EACbic,YAAamL,EACblL,YAAavW,EAAQkW,aAAahB,OAUlC0M,GARgBx1B,EAAQoW,cAAgB,CAACmf,EAAcA,EAAcA,EAAcA,GAChEv1B,EAAQiW,iBAAmB,CAC9CrX,gBAAiB02B,EACjBtX,gBAAiB,GACjBgB,eAAgBrL,EAAYkJ,gBAAgBc,YAC5CU,iBAAkB1K,EAAYiJ,kBAAkBe,aAG1B,IAAIX,EAAS/f,QAAQ,QAC3Cw4B,EAA4B,CAACD,EAAqBA,GAClDE,EAA4B,CAACD,EAA2BA,EAA2BA,EAA2BA,GAE9GE,EAAyB,IAAI3Y,EAAS/f,QAAQ,OAC9C24B,EAA+B,CAACD,EAAwBA,GACxDE,EAA+B,CAACD,EAA8BA,EAA8BA,EAA8BA,GAqC1HE,GAnCuB91B,EAAQuW,qBAAuB,SAA8B3I,GACpF,MAAqB,UAAdA,EAAK6E,KAAmBijB,EAA4BG,GAGtC71B,EAAQm1B,mBAAqB,SAA4BvnB,EAAMqF,GACpF,GAAkB,UAAdrF,EAAK6E,MAAkC,aAAd7E,EAAK6E,MAC9B,GAAI7E,EAAKmoB,QAAS,CACd,IAAIjY,EAAOve,KAAKkD,IAAIwQ,EAAUlF,OAAO1P,MAAO4U,EAAUlF,OAAOzP,QAC7D2U,EAAUsC,WAAWrU,KAAmB,aAAd0M,EAAK6E,KAAsB,CAAC,IAAInF,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,OAAPuf,EAAgB7K,EAAUlF,OAAOvP,IAAa,IAAPsf,GAAc,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,IAAPuf,EAAa7K,EAAUlF,OAAOvP,IAAa,MAAPsf,GAAgB,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,OAAPuf,EAAgB7K,EAAUlF,OAAOvP,IAAa,OAAPsf,GAAiB,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,OAAPuf,EAAgB7K,EAAUlF,OAAOvP,IAAa,MAAPsf,GAAgB,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,OAAPuf,EAAgB7K,EAAUlF,OAAOvP,IAAa,IAAPsf,GAAc,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,IAAPuf,EAAa7K,EAAUlF,OAAOvP,IAAa,OAAPsf,GAAiB,IAAIxQ,EAASrQ,QAAQgW,EAAUlF,OAAOxP,KAAc,OAAPuf,EAAgB7K,EAAUlF,OAAOvP,IAAa,IAAPsf,IAAgB,IAAIsX,EAASn4B,QAAQgW,EAAUlF,OAAOxP,KAAOuf,EAAO,EAAG7K,EAAUlF,OAAOvP,IAAMsf,EAAO,EAAGA,EAAO,UAG70BgY,EAAkBE,EAAcpoB,GAAOA,EAAMqF,GAAW,IAIpCjT,EAAQk1B,sBAAwB,SAA+BtnB,EAAMqF,GAC7F6iB,EAAkBloB,EAAK1N,MAAO0N,EAAMqF,GAAW,IAGzBjT,EAAQi1B,oBAAsB,SAA6BrnB,EAAMqF,GACvF,IAAIgjB,EAASroB,EAAKkiB,QAAQliB,EAAKsoB,eAAiB,GAChDJ,EAAkBG,GAASA,EAAO/Q,MAAa,GAAItX,EAAMqF,GAAW,IAG9CjT,EAAQ2Z,oBAAsB,SAA6B5L,GAQjF,OAPIA,EAAO1P,MAAQ0P,EAAOzP,QACtByP,EAAOxP,OAASwP,EAAO1P,MAAQ0P,EAAOzP,QAAU,EAChDyP,EAAO1P,MAAQ0P,EAAOzP,QACfyP,EAAO1P,MAAQ0P,EAAOzP,SAC7ByP,EAAOvP,MAAQuP,EAAOzP,OAASyP,EAAO1P,OAAS,EAC/C0P,EAAOzP,OAASyP,EAAO1P,OAEpB0P,GAGa,SAA2B7N,EAAO0N,EAAMqF,EAAWkjB,GACvE,IAAI3nB,EAAOZ,EAAKnQ,cAAc+Q,KAC9B,GAAItO,EAAM9D,OAAS,GAAKoS,EAAM,CAC1B,IAAIke,EAAU9e,EAAKnQ,cAAc4oB,cAAc,uBAC/C,EAAI3S,EAAMqI,eAAenO,EAAKnQ,cAAcG,YAAY+X,iBAAiB/H,EAAM,MAAO8e,GACtFA,EAAQvZ,MAAMvB,SAAW,WACzB8a,EAAQvZ,MAAM5U,KAAO0U,EAAUlF,OAAOxP,KAAO,KAC7CmuB,EAAQvZ,MAAM3U,IAAMyU,EAAUlF,OAAOvP,IAAM,KACtC23B,IACDzJ,EAAQvZ,MAAMijB,WAAa,UAE/B,IAAIlR,EAAOtX,EAAKnQ,cAAcuvB,eAAe9sB,GAC7CwsB,EAAQnG,YAAYrB,GACpB1W,EAAK+X,YAAYmG,GACjBzZ,EAAUsC,WAAWrU,KAAKorB,EAAgBrvB,QAAQgwB,aAAa/H,EAAMjS,IACrEzE,EAAKmY,YAAY+F,MAIrBsJ,EAAgB,SAAuBpoB,GACvC,IAAI1N,EAAsB,aAAd0N,EAAK6E,KAAsB,IAAIpS,MAAMuN,EAAK1N,MAAM9D,OAAS,GAAG22B,KAAK,KAAYnlB,EAAK1N,MAE9F,OAAwB,IAAjBA,EAAM9D,OAAewR,EAAKyoB,aAAe,GAAKn2B,I,kCCtHzDnE,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQolB,gBAAkBplB,EAAQs2B,gBAAaz1B,EAE/C,IAUgC9D,EAV5BD,EAAU,EAAQ,MAElB2X,EAAkB,EAAQ,MAE1B8hB,EAAW,EAAQ,MAEnBC,GAI4Bz5B,EAJOw5B,IAIcx5B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAFnFwvB,EAAW,EAAQ,MAMvB,IAAI+J,EAAat2B,EAAQs2B,WAAa,SAASA,EAAWpR,EAAMnX,IAFhE,SAAyB1L,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAG5GiB,CAAgBC,KAAM+zB,GAEtB/zB,KAAK2iB,KAAOA,EACZ3iB,KAAKwL,OAASA,GAgCd0oB,GA7BkBz2B,EAAQolB,gBAAkB,SAAyBllB,EAAOqT,EAAQ3F,GAWpF,IAVA,IACI8oB,EADiD,IAA/BnjB,EAAOJ,MAAM2D,eACF,EAAIyV,EAASlC,cAAcnqB,GAAOof,KAAI,SAAUpjB,GAC7E,OAAO,EAAIqwB,EAAS7B,eAAexuB,OAClC,EAAIqwB,EAASoK,YAAYz2B,EAAOqT,GACjCnX,EAASs6B,EAASt6B,OAClBwB,EAAcgQ,EAAKgpB,WAAahpB,EAAKgpB,WAAWn5B,cAAcG,YAAc,KAC5EC,EAAUD,EAAcA,EAAYE,YAAc,EAClDC,EAAUH,EAAcA,EAAYI,YAAc,EAClDw0B,EAAa,GACbqE,EAAS,EACJ36B,EAAI,EAAGA,EAAIE,EAAQF,IAAK,CAC7B,IAAIgpB,EAAOwR,EAASx6B,GACpB,GAAIqX,EAAOJ,MAAM+E,iBAAmBzD,EAAgBmU,gBAAgBxO,MAAQ8K,EAAK9F,OAAOhjB,OAAS,EAC7F,GAAIo6B,EAAUv5B,QAAQ65B,qBAClBtE,EAAWtxB,KAAK,IAAIo1B,EAAWpR,EAAM6R,EAAenpB,EAAMipB,EAAQ3R,EAAK9oB,OAAQyB,EAASE,SACrF,CACH,IAAIi5B,EAAkBppB,EAAKqpB,UAAU/R,EAAK9oB,QAC1Co2B,EAAWtxB,KAAK,IAAIo1B,EAAWpR,EAAMuR,EAAiB7oB,EAAM/P,EAASE,KACrE6P,EAAOopB,OAEHR,EAAUv5B,QAAQ65B,uBAC1BlpB,EAAOA,EAAKqpB,UAAU/R,EAAK9oB,SAE/By6B,GAAU3R,EAAK9oB,OAEnB,OAAOo2B,GAGY,SAA0B5kB,EAAM/P,EAASE,GAC5D,IAAI2uB,EAAU9e,EAAKnQ,cAAc4oB,cAAc,sBAC/CqG,EAAQnG,YAAY3Y,EAAKspB,WAAU,IACnC,IAAIN,EAAahpB,EAAKgpB,WACtB,GAAIA,EAAY,CACZA,EAAWO,aAAazK,EAAS9e,GACjC,IAAIG,GAAS,EAAIjR,EAAQsB,aAAasuB,EAAS7uB,EAASE,GAIxD,OAHI2uB,EAAQ0K,YACRR,EAAWO,aAAazK,EAAQ0K,WAAY1K,GAEzC3e,EAEX,OAAO,IAAIjR,EAAQuQ,OAAO,EAAG,EAAG,EAAG,KAGnC0pB,EAAiB,SAAwBnpB,EAAMipB,EAAQz6B,EAAQyB,EAASE,GACxE,IAAIooB,EAAQvY,EAAKnQ,cAAcyoB,cAG/B,OAFAC,EAAMkR,SAASzpB,EAAMipB,GACrB1Q,EAAMmR,OAAO1pB,EAAMipB,EAASz6B,GACrBU,EAAQuQ,OAAOQ,eAAesY,EAAMrY,wBAAyBjQ,EAASE,K,kCC1EjFhC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAIoB,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAInB,IAAI01B,EAAwB,WACxB,SAASA,EAAsBp6B,IAHnC,SAAyBkF,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAIxGiB,CAAgBC,KAAMg1B,GAEtBh1B,KAAKpF,QAAUA,EA+BnB,OA5BAmE,EAAai2B,EAAuB,CAAC,CACjCj7B,IAAK,SACL4D,MAAO,SAAgB4vB,GACnB,IAAIxa,EAAQ/S,KAEZA,KAAKutB,QAAUA,EACfvtB,KAAKpD,OAASoP,SAAS8X,cAAc,UACrC9jB,KAAKukB,IAAMvkB,KAAKpD,OAAO4nB,WAAW,MAClCxkB,KAAKpD,OAAOd,MAAQkB,KAAK4uB,MAAM2B,EAAQzxB,OAASyxB,EAAQ7wB,MACxDsD,KAAKpD,OAAOb,OAASiB,KAAK4uB,MAAM2B,EAAQxxB,QAAUwxB,EAAQ7wB,MAC1DsD,KAAKpD,OAAOgU,MAAM9U,MAAQyxB,EAAQzxB,MAAQ,KAC1CkE,KAAKpD,OAAOgU,MAAM7U,OAASwxB,EAAQxxB,OAAS,KAE5CwxB,EAAQxyB,OAAOE,IAAI,uCAAyCsyB,EAAQzxB,MAAQ,IAAMyxB,EAAQxxB,OAAS,OAASwxB,EAAQzwB,EAAI,IAAMywB,EAAQxwB,EAAI,gBAAkBwwB,EAAQ7wB,OACpK,IAAIgpB,EAAMC,EAAuB3oB,KAAKoP,IAAImhB,EAAQrwB,YAAaqwB,EAAQzxB,OAASyxB,EAAQ7wB,MAAOM,KAAKoP,IAAImhB,EAAQnwB,aAAcmwB,EAAQxxB,QAAUwxB,EAAQ7wB,MAAO6wB,EAAQjyB,QAAUiyB,EAAQ7wB,MAAO6wB,EAAQ/xB,QAAU+xB,EAAQ7wB,MAAOsD,KAAKpF,SAEtO,OAAOgrB,EAAkBF,GAAKG,MAAK,SAAU7M,GAOzC,OANIuU,EAAQlxB,kBACR0W,EAAMwR,IAAIgB,UAAYgI,EAAQlxB,gBAAgBgxB,WAC9Cta,EAAMwR,IAAIiB,SAAS,EAAG,EAAG+H,EAAQzxB,MAAQyxB,EAAQ7wB,MAAO6wB,EAAQxxB,OAASwxB,EAAQ7wB,QAGrFqW,EAAMwR,IAAIE,UAAUzL,GAAMuU,EAAQzwB,EAAIywB,EAAQ7wB,OAAQ6wB,EAAQxwB,EAAIwwB,EAAQ7wB,OACnEqW,EAAMnW,cAKlBo4B,EAnCiB,GAsC5Bv3B,EAAQ/C,QAAUs6B,EAClB,IAAIrP,EAAyBloB,EAAQkoB,uBAAyB,SAAgC7pB,EAAOC,EAAQe,EAAGC,EAAGsO,GAC/G,IAAI4pB,EAAQ,6BACRvP,EAAM1Z,SAASkpB,gBAAgBD,EAAO,OACtCE,EAAgBnpB,SAASkpB,gBAAgBD,EAAO,iBAapD,OAZAvP,EAAI0P,eAAe,KAAM,QAASt5B,GAClC4pB,EAAI0P,eAAe,KAAM,SAAUr5B,GAEnCo5B,EAAcC,eAAe,KAAM,QAAS,QAC5CD,EAAcC,eAAe,KAAM,SAAU,QAC7CD,EAAcC,eAAe,KAAM,IAAKt4B,GACxCq4B,EAAcC,eAAe,KAAM,IAAKr4B,GACxCo4B,EAAcC,eAAe,KAAM,4BAA6B,QAChE1P,EAAI1B,YAAYmR,GAEhBA,EAAcnR,YAAY3Y,GAEnBqa,GAGPE,EAAoBnoB,EAAQmoB,kBAAoB,SAA2BF,GAC3E,OAAO,IAAIvqB,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAI4d,EAAM,IAAIsL,MACdtL,EAAI+L,OAAS,WACT,OAAOD,EAAQ9L,IAEnBA,EAAIgM,QAAU5pB,EAEd4d,EAAIE,IAAM,oCAAsCJ,oBAAmB,IAAIF,eAAgBG,kBAAkB2M,S,kCC1EjHlsB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ22B,WAAa32B,EAAQ0qB,cAAgB1qB,EAAQqqB,kBAAexpB,EAEpE,IAAI+2B,EAAgB,EAAQ,MAE5B77B,OAAOkE,eAAeD,EAAS,eAAgB,CAC3C0B,YAAY,EACZm2B,IAAK,WACD,OAAOD,EAAcvN,gBAG7BtuB,OAAOkE,eAAeD,EAAS,gBAAiB,CAC5C0B,YAAY,EACZm2B,IAAK,WACD,OAAOD,EAAclN,iBAI7B,IAMgC3tB,EAN5BsV,EAAiB,EAAQ,MAIzBiC,IAE4BvX,EAJasV,IAIQtV,EAAIC,WAFrC,EAAQ,OAIXgD,EAAQ22B,WAAa,SAAoBrM,EAAK/W,GAS3D,IARA,IAAIukB,GAAU,EAAIF,EAAcG,aAAazN,EAAK,CAC9ClT,UAAW7D,EAAOJ,MAAMiE,UACxBwB,UAAWrF,EAAOJ,MAAM2E,eAAiBxD,EAAcqgB,cAAcE,WAAa,aAAethB,EAAOJ,MAAMyF,YAG9Gof,EAAQ,GACRC,OAAK,IAEAA,EAAKH,EAAQ92B,QAAQC,MAC1B+2B,EAAM92B,KAAK+2B,EAAG/3B,MAAM4rB,SAGxB,OAAOkM,I,kCCzCXj8B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQk4B,iBAAcr3B,EAEtB,IAAIS,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf6R,EAAQ,EAAQ,MAMF1T,EAAQk4B,YAAc,WACpC,SAASA,EAAY3pB,IALzB,SAAyBlM,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAMxGiB,CAAgBC,KAAM21B,GAEtB31B,KAAK41B,MAAQ,GACb51B,KAAK61B,UAAY7pB,EAiErB,OA9DAjN,EAAa42B,EAAa,CAAC,CACvB57B,IAAK,gBACL4D,MAAO,SAAuBkT,GAC1B,IAAIH,EAAY1Q,KAAK61B,UAAU/R,cAAc,OACzC9K,EAAMhZ,KAAK61B,UAAU/R,cAAc,OACnCgS,EAAO91B,KAAK61B,UAAU/R,cAAc,QAEpC7X,EAAOjM,KAAK61B,UAAU5pB,KAC1B,IAAKA,EACD,MAAM,IAAIE,MAAqF,IAGnGuE,EAAUE,MAAMuF,WAAa,SAC7BzF,EAAUE,MAAM2f,WAAa1f,EAAK0f,WAClC7f,EAAUE,MAAME,SAAWD,EAAKC,SAChCJ,EAAUE,MAAMmE,OAAS,IACzBrE,EAAUE,MAAMjF,QAAU,IAE1BM,EAAK+X,YAAYtT,GAEjBsI,EAAIE,IAAM/H,EAAM0I,YAChBb,EAAIld,MAAQ,EACZkd,EAAIjd,OAAS,EAEbid,EAAIpI,MAAMmE,OAAS,IACnBiE,EAAIpI,MAAMjF,QAAU,IACpBqN,EAAIpI,MAAMmlB,cAAgB,WAE1BD,EAAKllB,MAAM2f,WAAa1f,EAAK0f,WAC7BuF,EAAKllB,MAAME,SAAWD,EAAKC,SAC3BglB,EAAKllB,MAAMmE,OAAS,IACpB+gB,EAAKllB,MAAMjF,QAAU,IAErBmqB,EAAK9R,YAAYhkB,KAAK61B,UAAUpL,eA3C1B,gBA4CN/Z,EAAUsT,YAAY8R,GACtBplB,EAAUsT,YAAYhL,GACtB,IAAI+X,EAAW/X,EAAIgd,UAAYF,EAAKE,UAAY,EAEhDtlB,EAAU0T,YAAY0R,GACtBplB,EAAUsT,YAAYhkB,KAAK61B,UAAUpL,eAjD/B,gBAmDN/Z,EAAUE,MAAMqlB,WAAa,SAC7Bjd,EAAIpI,MAAMmlB,cAAgB,QAE1B,IAAI5E,EAASnY,EAAIgd,UAAYtlB,EAAUslB,UAAY,EAInD,OAFA/pB,EAAKmY,YAAY1T,GAEV,CAAEqgB,SAAUA,EAAUI,OAAQA,KAE1C,CACCp3B,IAAK,aACL4D,MAAO,SAAoBkT,GACvB,IAAI9W,EAAM8W,EAAK0f,WAAa,IAAM1f,EAAKC,SAKvC,YAJwBxS,IAApB0B,KAAK41B,MAAM77B,KACXiG,KAAK41B,MAAM77B,GAAOiG,KAAKk2B,cAAcrlB,IAGlC7Q,KAAK41B,MAAM77B,OAInB47B,EAtE6B,I,kCCbxCn8B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ04B,WAAQ73B,EAEhB,IAIgC9D,EAJ5Bw5B,EAAW,EAAQ,MAEnBC,GAE4Bz5B,EAFOw5B,IAEcx5B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAE3EiD,EAAQ04B,MAAQ,SAAejd,EAAKqU,GAC5C,IAAKA,EAAQhxB,MACT,OAAOpB,QAAQC,OAAoE,MAEvF,IAAImB,EAAQgxB,EAAQhxB,MAEpB,OAAO,IAAIpB,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAI+qB,EAAe8N,EAAUv5B,QAAQ07B,kBAAoBnC,EAAUv5B,QAAQ27B,sBAAwB,OAAS,OACxGC,EAAMrC,EAAUv5B,QAAQ07B,iBAAmB,IAAIlQ,eAAmB,IAAIqQ,eAiC1E,GAhCAD,EAAIvR,OAAS,WACT,GAAIuR,aAAepQ,eACf,GAAmB,MAAfoQ,EAAIE,OACJ,GAAqB,SAAjBrQ,EACArB,EAAQwR,EAAIG,cACT,CACH,IAAIC,EAAS,IAAIC,WAEjBD,EAAO1f,iBAAiB,QAAQ,WAC5B,OAAO8N,EAAQ4R,EAAOpO,WACvB,GAEHoO,EAAO1f,iBAAiB,SAAS,SAAU2N,GACvC,OAAOvpB,EAAOupB,MACf,GACH+R,EAAOE,cAAcN,EAAIG,eAG7Br7B,EAAyI,SAG7I0pB,EAAQwR,EAAIO,eAIpBP,EAAItR,QAAU5pB,EACdk7B,EAAIQ,KAAK,MAAOv6B,EAAQ,QAAUuc,mBAAmBI,GAAO,iBAAmBiN,GAE1D,SAAjBA,GAA2BmQ,aAAepQ,iBAC1CoQ,EAAInQ,aAAeA,GAGnBoH,EAAQjxB,aAAc,CACtB,IAAIy6B,EAAUxJ,EAAQjxB,aACtBg6B,EAAIS,QAAUA,EACdT,EAAIU,UAAY,WACZ,OAAO57B,EAAmH,KAIlIk7B,EAAIW,Y,kCC5DZz9B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQH,mBAAgBgB,EAExB,IAAIV,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAMllBo4B,GAFW98B,EAFD,EAAQ,OAIJ,EAAQ,OAItB+8B,EAAa/8B,EAFD,EAAQ,OAMpBg9B,EAA0Bh9B,EAFD,EAAQ,OAMjC65B,EAAY75B,EAFD,EAAQ,OAInBG,EAAU,EAAQ,MAElB88B,EAAS,EAAQ,MAEjBC,EAAQ,EAAQ,MAEhBrmB,EAAS,EAAQ,MAEjBC,EAAU9W,EAAuB6W,GAErC,SAAS7W,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEnEiD,EAAQH,cAAgB,SAASA,EAAc1C,EAAS2yB,EAASxyB,GACjF,IAAIG,EAAgBN,EAAQM,cAExBq8B,EAAe,IAAIh9B,EAAQuQ,OAAOyiB,EAAQjyB,QAASiyB,EAAQ/xB,QAAS+xB,EAAQrwB,YAAaqwB,EAAQnwB,cAGjGo6B,EAA0Bt8B,EAAcgR,gBAAkB,IAAIgF,EAAQxW,QAAQ0Y,iBAAiBlY,EAAcgR,iBAAiB7P,iBAAmB4U,EAAO1G,YACxJktB,EAAsBv8B,EAAc+Q,KAAO,IAAIiF,EAAQxW,QAAQ0Y,iBAAiBlY,EAAc+Q,MAAM5P,iBAAmB4U,EAAO1G,YAE9HlO,EAAkBzB,IAAYM,EAAcgR,gBAAkBsrB,EAAwBE,gBAAkBD,EAAoBC,gBAAkBnK,EAAQlxB,gBAAkB,IAAI6U,EAAQxW,QAAQ6yB,EAAQlxB,iBAAmB,KAAOo7B,EAAsBD,EAA0BjK,EAAQlxB,gBAAkB,IAAI6U,EAAQxW,QAAQ6yB,EAAQlxB,iBAAmB,KAE3V,OAAQkxB,EAAQ9wB,uBAChBw3B,EAAUv5B,QAAQi9B,8BAAgCx8B,QAAQ2pB,SAAQ,IAAQe,MAAK,SAAU+R,GACrF,OAAOA,GAAiCC,EAuBtC,IAAIR,EAAOS,eAAel9B,EAAS2yB,EAASxyB,GAAQ,EAAMuC,IAlB1Cy6B,YAAY78B,GAAe2qB,MAAK,WAC1C,OAAOgS,EAAOhlB,eAAemlB,WAC9BnS,MAAK,WAEJ,OADe,IAAIuR,EAAwB18B,QAAQm9B,EAAO3rB,iBAC1C+rB,OAAO,CACnB57B,gBAAiBA,EACjBtB,OAAQA,EACR2B,MAAO6wB,EAAQ7wB,MACfI,EAAGywB,EAAQzwB,EACXC,EAAGwwB,EAAQxwB,EACXjB,MAAOyxB,EAAQzxB,MACfC,OAAQwxB,EAAQxxB,OAChBmB,YAAaqwB,EAAQrwB,YACrBE,aAAcmwB,EAAQnwB,aACtB9B,QAASiyB,EAAQjyB,QACjBE,QAAS+xB,EAAQ/xB,cAGiD,EAAI67B,EAAOa,aAAah9B,EAAeq8B,EAAc38B,EAAS2yB,EAASxyB,EAAQuC,GAAeuoB,MAAK,SAAUnqB,GACvL,IAAImF,EAAQjD,EAAelC,EAAM,GAC7BgV,EAAY7P,EAAM,GAClBs3B,EAAgBt3B,EAAM,GACtBgS,EAAiBhS,EAAM,GAM3B,IAAIu3B,GAAQ,EAAIlB,EAAYmB,YAAYF,EAAetlB,EAAgB9X,GACnEu9B,EAAiBH,EAAcj9B,cAMnC,OAJImB,IAAoB+7B,EAAM1nB,UAAUE,MAAM6C,WAAWpX,kBACrD+7B,EAAM1nB,UAAUE,MAAM6C,WAAWpX,gBAAkB4U,EAAO1G,aAGvDsI,EAAemlB,QAAQnS,MAAK,SAAU0S,GACzC,IAAIvH,EAAc,IAAIsG,EAAM3B,YAAY2C,GAKxC,IAAIE,EAAgB,CAChBn8B,gBAAiBA,EACjB20B,YAAaA,EACbuH,WAAYA,EACZx9B,OAAQA,EACR2B,MAAO6wB,EAAQ7wB,MACfI,EAAGywB,EAAQzwB,EACXC,EAAGwwB,EAAQxwB,EACXjB,MAAOyxB,EAAQzxB,MACfC,OAAQwxB,EAAQxxB,QAGpB,GAAI+B,MAAMC,QAAQwvB,EAAQ7zB,QACtB,OAAOyB,QAAQs9B,IAAIlL,EAAQ7zB,OAAOqjB,KAAI,SAAUrjB,GAE5C,OADe,IAAIy9B,EAAWz8B,QAAQhB,EAAQ8+B,GAC9BP,OAAOG,OAG3B,IACIx7B,EADW,IAAIu6B,EAAWz8B,QAAQ6yB,EAAQ7zB,OAAQ8+B,GAChCP,OAAOG,GAS7B,OARgC,IAA5B7K,EAAQ/wB,iBACJkU,EAAU2jB,YACV3jB,EAAU2jB,WAAWjQ,YAAY1T,GAMlC9T,QA1EW,IAAUi7B,O,kCClDhDr+B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ46B,gBAAa/5B,EAErB,IAEIo6B,EAAoBt+B,EAFD,EAAQ,OAM3B0vB,EAAkB1vB,EAFD,EAAQ,OAMzB2vB,EAAkB3vB,EAFD,EAAQ,OAIzBqY,EAAS,EAAQ,MAEjBC,EAAY,EAAQ,MAEpBd,EAAa,EAAQ,MAEzB,SAASxX,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEtEiD,EAAQ46B,WAAa,SAAoBhtB,EAAMwH,EAAgB9X,GAK5E,IAAI+X,EAAQ,EAERpC,EAAY,IAAIoZ,EAAgBpvB,QAAQ2Q,EAAM,KAAMwH,EAAgBC,KACpEslB,EAAQ,IAAIM,EAAkBh+B,QAAQgW,EAAW,MAAM,GAQ3D,OANAioB,EAActtB,EAAMqF,EAAW0nB,EAAOvlB,EAHkCC,GASjEslB,GAhBX,IAmBIQ,EAAqB,CAAC,SAAU,OAAQ,QAAS,SAAU,KAAM,UAEjED,EAAgB,SAASA,EAActtB,EAAM2F,EAAQonB,EAAOvlB,EAAgBC,GAK5E,IAAK,IAAiC+lB,EAA7BC,EAAYztB,EAAKwpB,WAAsBiE,EAAWA,EAAYD,EAAU,CAC7EA,EAAWC,EAAUC,YACrB,IAAI19B,EAAcy9B,EAAU59B,cAAcG,YAC1C,GAAIy9B,aAAqBz9B,EAAY29B,MAAQF,aAAqBE,MAAQ39B,EAAY2V,QAAU8nB,aAAqBz9B,EAAY2V,OAAOgoB,KAChIF,EAAUlW,KAAK/F,OAAOhjB,OAAS,GAC/BmX,EAAOgC,WAAWrU,KAAKorB,EAAgBrvB,QAAQgwB,aAAaoO,EAAW9nB,SAExE,GAAI8nB,aAAqBz9B,EAAY49B,aAAeH,aAAqBG,aAAe59B,EAAY2V,QAAU8nB,aAAqBz9B,EAAY2V,OAAOioB,aACzJ,IAAwD,IAApDL,EAAmBzjB,QAAQ2jB,EAAUI,UAAkB,CACvD,IAAIxoB,EAAY,IAAIoZ,EAAgBpvB,QAAQo+B,EAAW9nB,EAAQ6B,EAAgBC,KAC/E,GAAIpC,EAAUyoB,YAAa,CACG,UAAtBL,EAAUn9B,SAEV,EAAI8W,EAAOmgB,oBAAoBkG,EAAWpoB,GACb,aAAtBooB,EAAUn9B,SAEjB,EAAI8W,EAAOkgB,uBAAuBmG,EAAWpoB,GAChB,WAAtBooB,EAAUn9B,SAEjB,EAAI8W,EAAOigB,qBAAqBoG,EAAWpoB,GACpCA,EAAUE,MAAM6D,WAAa/D,EAAUE,MAAM6D,UAAU8N,gBAAkB3Q,EAAWiN,gBAAgBhH,OAC3G,EAAInF,EAAUmX,uBAAuBiP,EAAWpoB,EAAWmC,GAG/D,IAAIumB,EAAiD,aAAtBN,EAAUn9B,QACrC09B,EAA6BC,EAA2B5oB,EAAWooB,GACvE,GAAIO,GAA8BE,EAAuB7oB,GAAY,CAGjE,IAAI8oB,EAAcH,GAA8B3oB,EAAUuH,eAAiBmgB,EAAMqB,+BAAiCrB,EAC9GsB,EAAa,IAAIhB,EAAkBh+B,QAAQgW,EAAW8oB,EAAaH,GACvEG,EAAYG,SAASh7B,KAAK+6B,GACtBN,GACAT,EAAcG,EAAWpoB,EAAWgpB,EAAY7mB,EAAgBC,QAGpEslB,EAAMwB,SAASj7B,KAAK+R,GAChB0oB,GACAT,EAAcG,EAAWpoB,EAAW0nB,EAAOvlB,EAAgBC,UAKxE,GAAIgmB,aAAqBz9B,EAAYod,eAAiBqgB,aAAqBrgB,eAAiBpd,EAAY2V,QAAU8nB,aAAqBz9B,EAAY2V,OAAOyH,cAAe,CAC5K,IAAIohB,EAAa,IAAI/P,EAAgBpvB,QAAQo+B,EAAW9nB,EAAQ6B,EAAgBC,KAC5EgnB,EAA8BR,EAA2BO,EAAYf,GACzE,GAAIgB,GAA+BP,EAAuBM,GAAa,CAGnE,IAAIE,EAAeD,GAA+BD,EAAW5hB,eAAiBmgB,EAAMqB,+BAAiCrB,EACjH4B,EAAc,IAAItB,EAAkBh+B,QAAQm/B,EAAYE,EAAcD,GAC1EC,EAAaJ,SAASh7B,KAAKq7B,QAE3B5B,EAAMwB,SAASj7B,KAAKk7B,MAMhCP,EAA6B,SAAoC5oB,EAAWrF,GAC5E,OAAOqF,EAAU+G,iBAAmB/G,EAAUupB,0BAA4BvpB,EAAUE,MAAMqE,QAAU,GAAKvE,EAAUiG,iBAAmBujB,EAA0BxpB,EAAWrF,IAG3KkuB,EAAyB,SAAgC7oB,GACzD,OAAOA,EAAUuH,gBAAkBvH,EAAUgH,cAG7CwiB,EAA4B,SAAmCxpB,EAAWrF,GAC1E,MAAyB,SAAlBA,EAAK6tB,UAAuBxoB,EAAUM,kBAAkB8Y,EAAgBpvB,SAAWgW,EAAUM,OAAOJ,MAAM6C,WAAWpX,gBAAgBq7B,kB,kCCvHhJl+B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAQgCnD,EAR5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEfwQ,EAAiB,EAAQ,OAMGtV,EAJasV,IAIQtV,EAAIC,WAFzC,EAAQ,MAMxB,IAAI0/B,EAAkB,WAClB,SAASA,EAAgBzpB,EAAWM,EAAQqoB,IAHhD,SAAyBv5B,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAIxGiB,CAAgBC,KAAMm6B,GAEtBn6B,KAAK0Q,UAAYA,EACjB1Q,KAAKgR,OAASA,EACdhR,KAAK25B,SAAW,GAChB35B,KAAK45B,SAAW,GAChB55B,KAAKq5B,2BAA6BA,EAetC,OAZAt6B,EAAao7B,EAAiB,CAAC,CAC3BpgC,IAAK,aACL4D,MAAO,WACH,OAAOqC,KAAKgR,OAAShR,KAAK0Q,UAAUE,MAAMqE,QAAUjV,KAAKgR,OAAOopB,aAAep6B,KAAK0Q,UAAUE,MAAMqE,UAEzG,CACClb,IAAK,+BACL4D,MAAO,WACH,OAAQqC,KAAKgR,QAAUhR,KAAKq5B,2BAA6Br5B,KAAOA,KAAKgR,OAAOyoB,mCAI7EU,EAvBW,GA0BtB18B,EAAQ/C,QAAUy/B,G,kCC1ClB3gC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAYXF,EAAQ/C,QAPG,SAAS2/B,EAAKv+B,EAAOC,IAFhC,SAAyB+D,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAG5GiB,CAAgBC,KAAMq6B,GAEtBr6B,KAAKlE,MAAQA,EACbkE,KAAKjE,OAASA,I,kCCVlBvC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAQgCnD,EAR5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEfof,EAAQ,EAAQ,MAEhB4b,EAAU,EAAQ,MAElBvvB,GAE4BvQ,EAFM8/B,IAEe9/B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAIvF,IAAI+/B,EAAO,SAAct5B,EAAGD,EAAGw5B,GAC3B,OAAO,IAAIzvB,EAASrQ,QAAQuG,EAAEnE,GAAKkE,EAAElE,EAAImE,EAAEnE,GAAK09B,EAAGv5B,EAAElE,GAAKiE,EAAEjE,EAAIkE,EAAElE,GAAKy9B,IAGvEC,EAAc,WACd,SAASA,EAAYvnB,EAAOqb,EAAcC,EAAYC,IAP1D,SAAyB3uB,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAQxGiB,CAAgBC,KAAMy6B,GAEtBz6B,KAAKkQ,KAAOwO,EAAMJ,KAAKE,aACvBxe,KAAKkT,MAAQA,EACblT,KAAKuuB,aAAeA,EACpBvuB,KAAKwuB,WAAaA,EAClBxuB,KAAKyuB,IAAMA,EAqBf,OAlBA1vB,EAAa07B,EAAa,CAAC,CACvB1gC,IAAK,YACL4D,MAAO,SAAmB68B,EAAGE,GACzB,IAAIC,EAAKJ,EAAKv6B,KAAKkT,MAAOlT,KAAKuuB,aAAciM,GACzCI,EAAKL,EAAKv6B,KAAKuuB,aAAcvuB,KAAKwuB,WAAYgM,GAC9CK,EAAKN,EAAKv6B,KAAKwuB,WAAYxuB,KAAKyuB,IAAK+L,GACrCM,EAAOP,EAAKI,EAAIC,EAAIJ,GACpBO,EAAOR,EAAKK,EAAIC,EAAIL,GACpBQ,EAAOT,EAAKO,EAAMC,EAAMP,GAC5B,OAAOE,EAAY,IAAID,EAAYz6B,KAAKkT,MAAOynB,EAAIG,EAAME,GAAQ,IAAIP,EAAYO,EAAMD,EAAMF,EAAI76B,KAAKyuB,OAE3G,CACC10B,IAAK,UACL4D,MAAO,WACH,OAAO,IAAI88B,EAAYz6B,KAAKyuB,IAAKzuB,KAAKwuB,WAAYxuB,KAAKuuB,aAAcvuB,KAAKkT,WAI3EunB,EA7BO,GAgClBh9B,EAAQ/C,QAAU+/B,G,kCCpDlBjhC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQwW,uBAAoB3V,EAE5B,IAMgC9D,EAN5BoD,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllBqzB,EAAU,EAAQ,MAElB1X,GAE4BjgB,EAFM23B,IAEe33B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAIgtB,EAAQ,CAAC,WAAY,YAAa,eAAgB,eAE9B/pB,EAAQwW,kBAAoB,SAA2BrD,GAC3E,OAAO4W,EAAMzK,KAAI,SAAU2K,GACvB,IAEIuT,EAFQrqB,EAAMgJ,iBAAiB,UAAY8N,EAAO,WAEzBtK,MAAM,KAAKL,IAAItC,EAAS/f,QAAQwgC,QACzDC,EAAoBv9B,EAAeq9B,EAAkB,GACrDG,EAAaD,EAAkB,GAC/BE,EAAWF,EAAkB,GAEjC,YAA2B,IAAbE,EAA2B,CAACD,EAAYA,GAAc,CAACA,EAAYC,Q,kCCxBzF7hC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAI+W,EAAUjX,EAAQiX,QAAU,CAC5BmD,KAAM,EACNyjB,MAAO,EACPnjB,OAAQ,EACRojB,OAAQ,EACRC,KAAM,GACNC,UAAW,GACXC,MAAO,GACPC,KAAM,IACNC,KAAM,IACNC,KAAM,IACNC,QAAS,KACTnnB,UAAW,KACXonB,gBAAiB,KACjBC,mBAAoB,KACpBC,mBAAoB,MACpBC,UAAW,MACXC,WAAY,MACZC,mBAAoB,GAAK,GACzBC,aAAc,GAAK,GACnBC,cAAe,GAAK,GACpBC,UAAW,GAAK,GAChBC,UAAW,GAAK,GAChBC,oBAAqB,GAAK,GAC1BC,oBAAqB,GAAK,GAC1BC,SAAU,GAAK,GACfvkB,aAAc,GAAK,GACnBG,iBAAkB,GAAK,GACvBC,aAAc,GAAK,GACnBH,YAAa,GAAK,GAClBC,YAAa,GAAK,IAoElBskB,EAAgB,SAAuBtjB,EAAKjG,GAC5C,OAAOiG,EAlEa,SAA2BjG,GAC/C,OAAQA,GACJ,IAAK,QACD,OAAOqB,EAAQ4mB,MACnB,IAAK,SACD,OAAO5mB,EAAQyD,OACnB,IAAK,SACD,OAAOzD,EAAQ6mB,OACnB,IAAK,OACD,OAAO7mB,EAAQ8mB,KACnB,IAAK,YACD,OAAO9mB,EAAQ+mB,UACnB,IAAK,QACD,OAAO/mB,EAAQgnB,MACnB,IAAK,OACD,OAAOhnB,EAAQinB,KACnB,IAAK,OACD,OAAOjnB,EAAQknB,KACnB,IAAK,OACD,OAAOlnB,EAAQmnB,KACnB,IAAK,UACD,OAAOnnB,EAAQonB,QACnB,IAAK,YACD,OAAOpnB,EAAQC,UACnB,IAAK,kBACD,OAAOD,EAAQqnB,gBACnB,IAAK,qBACD,OAAOrnB,EAAQsnB,mBACnB,IAAK,qBACD,OAAOtnB,EAAQunB,mBACnB,IAAK,YACD,OAAOvnB,EAAQwnB,UACnB,IAAK,aACD,OAAOxnB,EAAQynB,WACnB,IAAK,qBACD,OAAOznB,EAAQ0nB,mBACnB,IAAK,eACD,OAAO1nB,EAAQ2nB,aACnB,IAAK,gBACD,OAAO3nB,EAAQ4nB,cACnB,IAAK,YACD,OAAO5nB,EAAQ6nB,UACnB,IAAK,YACD,OAAO7nB,EAAQ8nB,UACnB,IAAK,sBACD,OAAO9nB,EAAQ+nB,oBACnB,IAAK,sBACD,OAAO/nB,EAAQgoB,oBACnB,IAAK,WACD,OAAOhoB,EAAQioB,SACnB,IAAK,eACD,OAAOjoB,EAAQ0D,aACnB,IAAK,mBACD,OAAO1D,EAAQ6D,iBACnB,IAAK,eACD,OAAO7D,EAAQ8D,aACnB,IAAK,cACD,OAAO9D,EAAQ2D,YACnB,IAAK,cACD,OAAO3D,EAAQ4D,YAGvB,OAAO5D,EAAQmD,KAIFglB,CAAkBxpB,IAGhB5V,EAAQ6V,aAAe,SAAsBD,GAC5D,OAAOA,EAAQ+J,MAAM,KAAK6N,OAAO2R,EAAe,K,kCC1GpDpjC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIqa,EAAQva,EAAQua,MAAQ,CACxBH,KAAM,EACNoE,KAAM,EACNC,MAAO,EACP4gB,aAAc,EACdC,WAAY,GAGIt/B,EAAQ4W,cAAgB,SAAuBD,GAC/D,OAAQA,GACJ,IAAK,OACD,OAAO4D,EAAMiE,KACjB,IAAK,QACD,OAAOjE,EAAMkE,MACjB,IAAK,eACD,OAAOlE,EAAM8kB,aACjB,IAAK,aACD,OAAO9kB,EAAM+kB,WAErB,OAAO/kB,EAAMH,O,kCCtBjBre,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAgBKF,EAAQ6W,UAAY,SAAmB1D,GAOnD,MAAO,CACH2f,WAPa3f,EAAM2f,WAQnBzf,SAPWF,EAAME,SAQjBsf,UAPYxf,EAAMwf,UAQlBC,YAPczf,EAAMyf,YAQpBC,WAxBc,SAAyB0M,GAC3C,OAAQA,GACJ,IAAK,SACD,OAAO,IACX,IAAK,OACD,OAAO,IAGf,IAAIr/B,EAAQyC,SAAS48B,EAAQ,IAC7B,OAAO1sB,MAAM3S,GAAS,IAAMA,EAQXs/B,CAAgBrsB,EAAM0f,e,kCCtB3C92B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEcF,EAAQ+W,mBAAqB,SAA4BD,GAC9E,GAAsB,WAAlBA,EACA,OAAO,EAEX,IAAI5W,EAAQ0S,WAAWkE,GACvB,OAAOjE,MAAM3S,GAAS,EAAIA,I,kCCR9BnE,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIu/B,EAAaz/B,EAAQy/B,WAAa,CAClC7K,OAAQ,SACR8K,OAAQ,UAGS1/B,EAAQqX,eAAiB,SAAwBuB,GAClE,OAAQA,GACJ,IAAK,SACD,OAAO6mB,EAAWC,OACtB,IAAK,SACL,QACI,OAAOD,EAAW7K,U,kCCd9B74B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQuX,iBAAc1W,EAEtB,IAIgC9D,EAJ5B23B,EAAU,EAAQ,MAElB1X,GAE4BjgB,EAFM23B,IAEe33B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAIgtB,EAAQ,CAAC,MAAO,QAAS,SAAU,QAErB/pB,EAAQuX,YAAc,SAAqBpE,GACzD,OAAO4W,EAAMzK,KAAI,SAAU2K,GACvB,OAAO,IAAIjN,EAAS/f,QAAQkW,EAAMgJ,iBAAiB,UAAY8N,S,kCCfvEluB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAI0X,EAAW5X,EAAQ4X,SAAW,CAC9BkC,QAAS,EACTjC,OAAQ,EACR8nB,OAAQ,EACRriB,KAAM,GAGUtd,EAAQ2X,cAAgB,SAAuBF,GAC/D,OAAQA,GACJ,IAAK,SACD,OAAOG,EAASC,OACpB,IAAK,SACD,OAAOD,EAAS+nB,OACpB,IAAK,OACD,OAAO/nB,EAAS0F,KACpB,IAAK,UACL,QACI,OAAO1F,EAASkC,W,kCCpB5B/d,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQqY,qBAAkBxX,EAE1B,IAIgC9D,EAJ5ByW,EAAS,EAAQ,MAEjBC,GAE4B1W,EAFKyW,IAEgBzW,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAI6iC,EAAS,kBAES5/B,EAAQqY,gBAAkB,SAAyBD,GACrE,GAAmB,SAAfA,GAA+C,iBAAfA,EAChC,OAAO,KAmCX,IAhCA,IAAIynB,EAAe,GACfC,GAAW,EACX3S,EAAS,GACT4S,EAAU,GACVC,EAAY,EACZvpB,EAAQ,KAERwpB,EAAc,WACVJ,EAAazjC,SACT0jC,EACA3S,EAAOjsB,KAAK0R,WAAWitB,IAEvBppB,EAAQ,IAAIhD,EAAQxW,QAAQ4iC,IAGpCC,GAAW,EACXD,EAAe,IAGfK,EAAe,WACX/S,EAAO/wB,QAAoB,OAAVqa,GACjBspB,EAAQ7+B,KAAK,CACTuV,MAAOA,EACP2b,QAASjF,EAAO,IAAM,EACtBkF,QAASlF,EAAO,IAAM,EACtBiG,KAAMjG,EAAO,IAAM,IAG3BA,EAAOgT,OAAO,EAAGhT,EAAO/wB,QACxBqa,EAAQ,MAGHva,EAAI,EAAGA,EAAIkc,EAAWhc,OAAQF,IAAK,CACxC,IAAIykB,EAAIvI,EAAWlc,GACnB,OAAQykB,GACJ,IAAK,IACDkf,GAAgBlf,EAChBqf,IACA,MACJ,IAAK,IACDH,GAAgBlf,EAChBqf,IACA,MACJ,IAAK,IACiB,IAAdA,GACAC,IACAC,KAEAL,GAAgBlf,EAEpB,MACJ,IAAK,IACiB,IAAdqf,EACAC,IAEAJ,GAAgBlf,EAEpB,MACJ,QACgC,IAAxBkf,EAAazjC,QAAgBwjC,EAAOhf,KAAKD,KACzCmf,GAAW,GAEfD,GAAgBlf,GAO5B,OAHAsf,IACAC,IAEuB,IAAnBH,EAAQ3jC,OACD,KAGJ2jC,I,kCC3FXhkC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQyY,oBAAiB5X,EAEzB,IAIgC9D,EAJ5B23B,EAAU,EAAQ,MAElB1X,GAE4BjgB,EAFM23B,IAEe33B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,IAAIqjC,EAAU,SAAiBllB,GAC3B,OAAOtI,WAAWsI,EAAEkE,SAGpBihB,EAAS,4BAuBTC,GArBiBtgC,EAAQyY,eAAiB,SAAwBtF,GAClE,IAAIqF,EAAY+nB,EAAqBptB,EAAMqF,WAAarF,EAAMqtB,iBAAmBrtB,EAAMstB,cAEvFttB,EAAMutB,aAENvtB,EAAMwtB,YACN,OAAkB,OAAdnoB,EACO,KAGJ,CACHA,UAAWA,EACXooB,gBAAiBN,EAAqBntB,EAAMytB,iBAAmBztB,EAAM0tB,uBAAyB1tB,EAAM2tB,oBAEpG3tB,EAAM4tB,mBAEN5tB,EAAM6tB,oBAKa,SAA8BC,GACrD,GAAsB,iBAAXA,EAAqB,CAC5B,IAAIjuB,EAAI,IAAIgK,EAAS/f,QAAQ,KAC7B,MAAO,CAAC+V,EAAGA,GAEf,IAAIma,EAAS8T,EAAOthB,MAAM,KAAKL,IAAItC,EAAS/f,QAAQwgC,QACpD,MAAO,CAACtQ,EAAO,GAAIA,EAAO,MAI1BoT,EAAuB,SAA8B/nB,GACrD,GAAkB,SAAdA,GAA6C,iBAAdA,EAC/B,OAAO,KAGX,IAAI9V,EAAQ8V,EAAU9V,MAAM29B,GAC5B,GAAI39B,EAAO,CACP,GAAiB,WAAbA,EAAM,GAAiB,CACvB,IAAIkxB,EAASlxB,EAAM,GAAGid,MAAM,KAAKL,IAAI8gB,GACrC,MAAO,CAACxM,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAEtE,IAAIsN,EAAWx+B,EAAM,GAAGid,MAAM,KAAKL,IAAI8gB,GACvC,MAAO,CAACc,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,GAAIA,EAAS,IAAKA,EAAS,KAG3F,OAAO,O,kCC/DXnlC,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIma,EAAara,EAAQqa,WAAa,CAClCP,QAAS,EACTjC,OAAQ,EACRspB,SAAU,GAGQnhC,EAAQ2Y,gBAAkB,SAAyBD,GACrE,OAAQA,GACJ,IAAK,SACD,OAAO2B,EAAWxC,OACtB,IAAK,WACD,OAAOwC,EAAW8mB,SACtB,IAAK,UACL,QACI,OAAO9mB,EAAWP,W,kCCjB9B/d,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAIkhC,EAAaphC,EAAQohC,WAAa,CAClCxM,OAAQ,SACRyM,UAAW,YACXC,SAAU,YAGOthC,EAAQ6Y,eAAiB,SAAwBD,GAClE,OAAQA,GACJ,IAAK,YACD,OAAOwoB,EAAWC,UACtB,IAAK,WACD,OAAOD,EAAWE,SACtB,IAAK,SACL,QACI,OAAOF,EAAWxM,U,kCCjB9B74B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEOF,EAAQ+Y,YAAc,SAAqBD,GACzD,IAAI2B,EAAkB,SAAX3B,EACX,MAAO,CACH2B,KAAMA,EACN8mB,MAAO9mB,EAAO,EAAI9X,SAASmW,EAAQ,O,kCCP3C/c,OAAOkE,eAAeD,EAAS,aAAc,CAC3CE,OAAO,IAGT,IAAIwT,EAAQ,EAAQ,MAEpB3X,OAAOkE,eAAeD,EAAS,eAAgB,CAC7C0B,YAAY,EACZm2B,IAAK,WACH,OAAOnkB,EAAM2W,gBAGjBtuB,OAAOkE,eAAeD,EAAS,gBAAiB,CAC9C0B,YAAY,EACZm2B,IAAK,WACH,OAAOnkB,EAAMgX,iBAIjB,IAAI8W,EAAa,EAAQ,MAEzBzlC,OAAOkE,eAAeD,EAAS,cAAe,CAC5C0B,YAAY,EACZm2B,IAAK,WACH,OAAO2J,EAAWzJ,gB,kCCxBtBh8B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ+3B,YAAc/3B,EAAQyhC,yBAA2BzhC,EAAQ0hC,iBAAmB1hC,EAAQ2hC,6BAA+B3hC,EAAQ4hC,YAAc5hC,EAAQ6hC,cAAgB7hC,EAAQ8hC,kBAAoB9hC,EAAQ+hC,gBAAkB/hC,EAAQgiC,QAAUhiC,EAAQiiC,4BAAyBphC,EAElR,IAYgC9D,EAZ5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf1B,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllB6gC,EAAQ,EAAQ,MAEhBC,EAAiB,EAAQ,MAEzBC,GAI4BrlC,EAJaolC,IAIQplC,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAFnF2W,EAAQ,EAAQ,MAQpB,IAAIuuB,EAAyBjiC,EAAQiiC,uBAAyB,GAY1DI,EAAK,GAILC,EAAK,GAELC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAGLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GACLC,EAAK,GACLC,EAAK,GACLC,EAAK,GAELC,EAAK,GAiDL7B,GA9CU/hC,EAAQgiC,QAAU,CAC5B6B,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLC,GAjDK,EAkDLhC,GAAIA,EACJiC,IAjDM,GAkDNC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJkC,GAhDK,GAiDLjC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJ6B,GA/CK,GAgDL5B,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJyB,GA7CK,GA8CLxB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJsB,GA7CK,GA8CLrB,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJmB,GA7CK,GA8CLlB,GAAIA,EACJmB,GA7CK,IAgDa/kC,EAAQ+hC,gBAAkB,KAC5CD,EAAoB9hC,EAAQ8hC,kBAAoB,IAChDD,EAAgB7hC,EAAQ6hC,cAAgB,IACxCD,EAAc5hC,EAAQ4hC,aAAc,EAAIM,EAAM8C,sBAAsB5C,EAAgBnlC,SAEpFgoC,EAAc,CAAC9B,EA5DV,IA6DL+B,EAAmB,CApGd,EACA,EACA,EAEA,GAiGLC,EAAQ,CAAC9C,EA9FJ,GA+FL+C,EAAiB,CAACnC,EAAID,GACtBqC,EAAcH,EAAiBnrB,OAAOorB,GACtCG,EAAwB,CAAC7B,EAAIC,EAAIC,EAAIL,EAAIC,GACzCgC,EAAS,CAAChD,EAAID,GAEdX,EAA+B3hC,EAAQ2hC,6BAA+B,SAAsCpX,GAC5G,IAAInT,EAAYjb,UAAUC,OAAS,QAAsByE,IAAjB1E,UAAU,GAAmBA,UAAU,GAAK,SAEhFqpC,EAAQ,GACRC,EAAW,GACXC,EAAa,GAgEjB,OA/DAnb,EAAW7J,SAAQ,SAAUoK,EAAWzV,GACpC,IAAIswB,EAAY/D,EAAY/J,IAAI/M,GAQhC,GAPI6a,EAAY1D,GACZyD,EAAWxkC,MAAK,GAChBykC,GAAa1D,GAEbyD,EAAWxkC,MAAK,IAGoC,IAApD,CAAC,SAAU,OAAQ,SAASwW,QAAQN,KAEyB,IAAzD,CAAC,KAAQ,KAAQ,MAAQ,OAAQM,QAAQoT,GAEzC,OADA2a,EAASvkC,KAAKmU,GACPmwB,EAAMtkC,KA9GpB,IAkHD,GA/HC,IA+HGykC,GAxHF,KAwHsBA,EAAmB,CAEvC,GAAc,IAAVtwB,EAEA,OADAowB,EAASvkC,KAAKmU,GACPmwB,EAAMtkC,KAAKiiC,GAKtB,IAAIyC,EAAOJ,EAAMnwB,EAAQ,GACzB,OAAmC,IAA/BgwB,EAAY3tB,QAAQkuB,IACpBH,EAASvkC,KAAKukC,EAASpwB,EAAQ,IACxBmwB,EAAMtkC,KAAK0kC,KAEtBH,EAASvkC,KAAKmU,GACPmwB,EAAMtkC,KAAKiiC,IAKtB,OAFAsC,EAASvkC,KAAKmU,GAlHb,KAoHGswB,EACOH,EAAMtkC,KAAmB,WAAdkW,EAAyBwrB,EAAKY,GAGhDmC,IAAc/B,GA1HjB,KA8HG+B,EAHOH,EAAMtkC,KAAKiiC,GA7GrB,KAuHGwC,EACI7a,GAAa,QAAWA,GAAa,QAAWA,GAAa,QAAWA,GAAa,OAC9E0a,EAAMtkC,KAAKsiC,GAEXgC,EAAMtkC,KAAKiiC,QAI1BqC,EAAMtkC,KAAKykC,MAGR,CAACF,EAAUD,EAAOE,IAGzBG,EAA6B,SAAoCriC,EAAGD,EAAGuiC,EAAcC,GACrF,IAAIC,EAAUD,EAAWD,GACzB,GAAIzlC,MAAMC,QAAQkD,IAA6B,IAAxBA,EAAEkU,QAAQsuB,GAAkBxiC,IAAMwiC,EAErD,IADA,IAAI9pC,EAAI4pC,EACD5pC,GAAK6pC,EAAW3pC,QAAQ,CAE3B,IAAI4E,EAAO+kC,IADX7pC,GAGA,GAAI8E,IAASuC,EACT,OAAO,EAGX,GAAIvC,IAASqhC,EACT,MAKZ,GAAI2D,IAAY3D,EAGZ,IAFA,IAAIthC,EAAK+kC,EAEF/kC,EAAK,GAAG,CAEX,IAAI6kC,EAAOG,IADXhlC,GAGA,GAAIV,MAAMC,QAAQkD,IAA0B,IAArBA,EAAEkU,QAAQkuB,GAAepiC,IAAMoiC,EAElD,IADA,IAAIK,EAAIH,EACDG,GAAKF,EAAW3pC,QAAQ,CAE3B,IAAI8pC,EAAQH,IADZE,GAGA,GAAIC,IAAU3iC,EACV,OAAO,EAGX,GAAI2iC,IAAU7D,EACV,MAKZ,GAAIuD,IAASvD,EACT,MAIZ,OAAO,GAGP8D,EAA4B,SAAmCL,EAAcC,GAE7E,IADA,IAAI7pC,EAAI4pC,EACD5pC,GAAK,GAAG,CACX,IAAIuW,EAAOszB,EAAW7pC,GACtB,GAAIuW,IAAS4vB,EAGT,OAAO5vB,EAFPvW,IAKR,OAAO,GAGPkqC,EAAoB,SAA2B7b,EAAYwb,EAAYN,EAAUpwB,EAAOgxB,GACxF,GAAwB,IAApBZ,EAASpwB,GACT,OAAOysB,EAGX,IAAIgE,EAAezwB,EAAQ,EAC3B,GAAIhV,MAAMC,QAAQ+lC,KAAsD,IAAlCA,EAAgBP,GAClD,OAAOhE,EAGX,IAAIwE,EAAcR,EAAe,EAC7BS,EAAaT,EAAe,EAC5BE,EAAUD,EAAWD,GAIrBU,EAASF,GAAe,EAAIP,EAAWO,GAAe,EACtDtlC,EAAO+kC,EAAWQ,GAEtB,GAnQK,IAmQDP,GAlQC,IAkQiBhlC,EAClB,OAAO8gC,EAGX,IAA2C,IAAvCoD,EAAiBxtB,QAAQsuB,GACzB,OAAOjE,EAIX,IAAwC,IAApCmD,EAAiBxtB,QAAQ1W,GACzB,OAAO8gC,EAIX,IAA6B,IAAzBqD,EAAMztB,QAAQ1W,GACd,OAAO8gC,EAIX,GAhRK,IAgRDqE,EAA0BL,EAAcC,GACxC,OAAOlE,EAIX,GAlRM,KAkRFD,EAAY/J,IAAItN,EAAWub,MAA2B9kC,IAASwiC,GAAMxiC,IAASoiC,GAAMpiC,IAASqiC,GAC7F,OAAOvB,EAIX,GA3RK,IA2RDkE,GA3RC,IA2RiBhlC,EAClB,OAAO8gC,EAIX,GA9RK,IA8RDkE,EACA,OAAOlE,EAIX,IAAuC,IAAnC,CAACO,EAAIC,EAAIC,GAAI7qB,QAAQsuB,IAnSpB,IAmSuChlC,EACxC,OAAO8gC,EAIX,IAA4C,IAAxC,CAACU,EAAIC,EAAIC,EAAII,EAAII,GAAIxrB,QAAQ1W,GAC7B,OAAO8gC,EAIX,GAAIqE,EAA0BL,EAAcC,KAAgBlD,EACxD,OAAOf,EAIX,GAAI+D,EAlSC,GAkS8BhD,EAAIiD,EAAcC,GACjD,OAAOjE,EAIX,GAAI+D,EAA2B,CAACrD,EAAIC,GAAKG,EAAIkD,EAAcC,GACvD,OAAOjE,EAIX,GAAI+D,EAxTC,MAwTkCC,EAAcC,GACjD,OAAOjE,EAIX,GAAIkE,IAAY3D,EACZ,OAAOR,EAIX,GAtTK,KAsTDmE,GAtTC,KAsTiBhlC,EAClB,OAAO8gC,EAIX,GAnUK,KAmUD9gC,GAnUC,KAmUcglC,EACf,OAAOnE,EAIX,IAAoC,IAAhC,CAACS,EAAIC,EAAIK,GAAIlrB,QAAQ1W,IA1UpB,KA0UoCglC,EACrC,OAAOlE,EAIX,GAtTK,KAsTD0E,IAA8C,IAA7BjB,EAAO7tB,QAAQsuB,GAChC,OAAOlE,EAIX,GAAIkE,IAAY9C,GA3TX,KA2TiBliC,EAClB,OAAO8gC,EAIX,GAAI9gC,IAAS2hC,IAAuE,IAAjEsC,EAAYlrB,OAAO4oB,EAAID,EAAIK,EAAIS,EAAIJ,EAAIC,GAAI3rB,QAAQsuB,GAClE,OAAOlE,EAIX,IAAmC,IAA/BmD,EAAYvtB,QAAQ1W,IAAgBglC,IAAYjD,IAAwC,IAAlCkC,EAAYvtB,QAAQsuB,IAAmBhlC,IAAS+hC,EACtG,OAAOjB,EAIX,GAAIkE,IAAY/C,IAAsC,IAAhC,CAACO,EAAIJ,EAAIC,GAAI3rB,QAAQ1W,KAAmD,IAAnC,CAACwiC,EAAIJ,EAAIC,GAAI3rB,QAAQsuB,IAAmBhlC,IAASgiC,EACxG,OAAOlB,EAIX,IAAsC,IAAlCmD,EAAYvtB,QAAQsuB,KAAqD,IAAlCZ,EAAe1tB,QAAQ1W,KAAqD,IAArCokC,EAAe1tB,QAAQsuB,KAAkD,IAA/Bf,EAAYvtB,QAAQ1W,GAC5I,OAAO8gC,EAIX,IAE+B,IAA/B,CAACmB,EAAID,GAAItrB,QAAQsuB,KAAoBhlC,IAAS+hC,IAAkC,IAA5B,CAACF,EAAIN,GAAI7qB,QAAQ1W,IAAgB+kC,EAAWQ,EAAa,KAAOxD,KAErF,IAA/B,CAACF,EAAIN,GAAI7qB,QAAQsuB,IAAmBhlC,IAAS+hC,GAE7CiD,IAAYjD,IAAsC,IAAhC,CAACA,EAAIG,EAAIJ,GAAIprB,QAAQ1W,GACnC,OAAO8gC,EAIX,IAA4C,IAAxC,CAACiB,EAAIG,EAAIJ,EAAIN,EAAIC,GAAI/qB,QAAQ1W,GAE7B,IADA,IAAIylC,EAAYX,EACTW,GAAa,GAAG,CACnB,IAAIh0B,EAAOszB,EAAWU,GACtB,GAAIh0B,IAASswB,EACT,OAAOjB,EACJ,IAAgC,IAA5B,CAACoB,EAAIJ,GAAIprB,QAAQjF,GAGxB,MAFAg0B,IAQZ,IAAgC,IAA5B,CAACxD,EAAID,GAAItrB,QAAQ1W,GAEjB,IADA,IAAI0lC,GAA4C,IAA/B,CAAClE,EAAIC,GAAI/qB,QAAQsuB,GAAkBM,EAAcR,EAC3DY,GAAc,GAAG,CACpB,IAAIC,EAAQZ,EAAWW,GACvB,GAAIC,IAAU5D,EACV,OAAOjB,EACJ,IAAiC,IAA7B,CAACoB,EAAIJ,GAAIprB,QAAQivB,GAGxB,MAFAD,IAQZ,GAAIjD,IAAOuC,IAA+C,IAApC,CAACvC,EAAIC,EAAIJ,EAAIC,GAAI7rB,QAAQ1W,KAA+C,IAA/B,CAAC0iC,EAAIJ,GAAI5rB,QAAQsuB,KAA+C,IAA5B,CAACtC,EAAIC,GAAIjsB,QAAQ1W,KAA+C,IAA/B,CAAC2iC,EAAIJ,GAAI7rB,QAAQsuB,IAAmBhlC,IAAS2iC,EAC7K,OAAO7B,EAIX,IAAgD,IAA5CwD,EAAsB5tB,QAAQsuB,KAA+C,IAA5B,CAACrD,EAAIK,GAAItrB,QAAQ1W,KAAyD,IAAzCskC,EAAsB5tB,QAAQ1W,IAAgBglC,IAAY/C,EAC5I,OAAOnB,EAIX,IAAsC,IAAlCmD,EAAYvtB,QAAQsuB,KAAkD,IAA/Bf,EAAYvtB,QAAQ1W,GAC3D,OAAO8gC,EAIX,GAAIkE,IAAYlD,IAAqC,IAA/BmC,EAAYvtB,QAAQ1W,GACtC,OAAO8gC,EAIX,IAAiD,IAA7CmD,EAAYlrB,OAAOgpB,GAAIrrB,QAAQsuB,IAAmBhlC,IAAS6hC,IAAgD,IAA1CoC,EAAYlrB,OAAOgpB,GAAIrrB,QAAQ1W,IAAgBglC,IAAYvD,EAC5H,OAAOX,EAKX,GAlZK,KAkZDkE,GAlZC,KAkZiBhlC,EAAa,CAG/B,IAFA,IAAI9E,EAAIupC,EAASK,GACbc,EAAQ,EACL1qC,EAAI,GArZV,KAuZO6pC,IADJ7pC,IAEI0qC,IAKR,GAAIA,EAAQ,GAAM,EACd,OAAO9E,EAKf,OAAIkE,IAAY5C,GAAMpiC,IAASqiC,EACpBvB,EAGJD,GAsBPgF,GAnBmB7mC,EAAQ0hC,iBAAmB,SAA0BnX,EAAYlV,GAEpF,GAAc,IAAVA,EACA,OAAOysB,EAIX,GAAIzsB,GAASkV,EAAWnuB,OACpB,OAAO2lC,EAGX,IAAI+E,EAAwBnF,EAA6BpX,GACrDwc,EAAyB5mC,EAAe2mC,EAAuB,GAC/DrB,EAAWsB,EAAuB,GAClChB,EAAagB,EAAuB,GAExC,OAAOX,EAAkB7b,EAAYwb,EAAYN,EAAUpwB,IAGrC,SAA6BkV,EAAYuF,GAC1DA,IACDA,EAAU,CAAE1Y,UAAW,SAAUwB,UAAW,WAGhD,IAAIouB,EAAyBrF,EAA6BpX,EAAYuF,EAAQ1Y,WAC1E6vB,EAAyB9mC,EAAe6mC,EAAwB,GAChEvB,EAAWwB,EAAuB,GAClClB,EAAakB,EAAuB,GACpCC,EAAiBD,EAAuB,GAY5C,MAV0B,cAAtBnX,EAAQlX,WAAmD,eAAtBkX,EAAQlX,YAC7CmtB,EAAaA,EAAWzmB,KAAI,SAAU7M,GAClC,OAAuC,IAAhC,CAACswB,EAAII,EAAIS,GAAIlsB,QAAQjF,GAAe+wB,EAAK/wB,MAQjD,CAACgzB,EAAUM,EAJ+B,aAAtBjW,EAAQlX,UAA2BsuB,EAAe5nB,KAAI,SAAU4nB,EAAgBhrC,GACvG,OAAOgrC,GAAkB3c,EAAWruB,IAAM,OAAUquB,EAAWruB,IAAM,SACpE,QAsBLirC,GAjB2BnnC,EAAQyhC,yBAA2B,SAAkCnX,EAAKwF,GACrG,IAAIvF,GAAa,EAAI7W,EAAM2W,cAAcC,GACrC8c,EAAStF,EAETuF,EAAuBR,EAAoBtc,EAAYuF,GACvDwX,EAAwBnnC,EAAeknC,EAAsB,GAC7D5B,EAAW6B,EAAsB,GACjCvB,EAAauB,EAAsB,GACnCC,EAAuBD,EAAsB,GAMjD,OAJA/c,EAAW7J,SAAQ,SAAUoK,EAAW5uB,GACpCkrC,IAAU,EAAI1zB,EAAMgX,eAAeI,IAAc5uB,GAAKquB,EAAWnuB,OAAS,EAAI2lC,EAAkBqE,EAAkB7b,EAAYwb,EAAYN,EAAUvpC,EAAI,EAAGqrC,OAGxJH,GAGC,WACR,SAASD,EAAM5c,EAAYnT,EAAW3B,EAAOub,IAxhBjD,SAAyB3uB,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAyhBxGiB,CAAgBC,KAAM4kC,GAEtB5kC,KAAKilC,YAAcjd,EACnBhoB,KAAKklC,SAAWrwB,IAAc2qB,EAC9Bx/B,KAAKkT,MAAQA,EACblT,KAAKyuB,IAAMA,EAUf,OAPA1vB,EAAa6lC,EAAO,CAAC,CACjB7qC,IAAK,QACL4D,MAAO,WACH,OAAOwT,EAAMgX,cAActZ,WAAMvQ,EAtiB7C,SAA4BT,GAAO,GAAIC,MAAMC,QAAQF,GAAM,CAAE,IAAK,IAAIlE,EAAI,EAAGwrC,EAAOrnC,MAAMD,EAAIhE,QAASF,EAAIkE,EAAIhE,OAAQF,IAAOwrC,EAAKxrC,GAAKkE,EAAIlE,GAAM,OAAOwrC,EAAe,OAAOrnC,MAAMsnB,KAAKvnB,GAsiBlIunC,CAAmBplC,KAAKilC,YAAY1b,MAAMvpB,KAAKkT,MAAOlT,KAAKyuB,WAIxGmW,EAjBC,IAoBMnnC,EAAQ+3B,YAAc,SAAqBzN,EAAKwF,GAC9D,IAAIvF,GAAa,EAAI7W,EAAM2W,cAAcC,GAErCsd,EAAwBf,EAAoBtc,EAAYuF,GACxD+X,EAAwB1nC,EAAeynC,EAAuB,GAC9DnC,EAAWoC,EAAsB,GACjC9B,EAAa8B,EAAsB,GACnCN,EAAuBM,EAAsB,GAE7CzrC,EAASmuB,EAAWnuB,OACpB0rC,EAAU,EACVC,EAAY,EAEhB,MAAO,CACH/mC,KAAM,WACF,GAAI+mC,GAAa3rC,EACb,MAAO,CAAE6E,MAAM,GAGnB,IADA,IAAImW,EAAY0qB,EACTiG,EAAY3rC,IAAWgb,EAAYgvB,EAAkB7b,EAAYwb,EAAYN,IAAYsC,EAAWR,MAA2BzF,IAEtI,GAAI1qB,IAAc0qB,GAAqBiG,IAAc3rC,EAAQ,CACzD,IAAI8D,EAAQ,IAAIinC,EAAM5c,EAAYnT,EAAW0wB,EAASC,GAEtD,OADAD,EAAUC,EACH,CAAE7nC,MAAOA,EAAOe,MAAM,GAGjC,MAAO,CAAEA,MAAM,O,kCC3lB3BlF,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQgoC,KAAOhoC,EAAQglC,qBAAuBhlC,EAAQioC,oBAAsBjoC,EAAQkoC,4BAA8BloC,EAAQmoC,kCAAoCnoC,EAAQooC,sBAAwBpoC,EAAQqoC,8BAAgCroC,EAAQsoC,8BAAgCtoC,EAAQuoC,0BAA4BvoC,EAAQwoC,2BAA6BxoC,EAAQyoC,iBAAmBzoC,EAAQ0oC,yBAA2B1oC,EAAQ2oC,2BAA6B3oC,EAAQ4oC,iBAAmB5oC,EAAQ6oC,mBAAqB7oC,EAAQ8oC,eAAiB9oC,EAAQ+oC,oBAAiBloC,EAEpiB,IAAIS,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf6R,EAAQ,EAAQ,MAKpB,IAAIq1B,EAAiB/oC,EAAQ+oC,eAAiB,EAG1CD,EAAiB9oC,EAAQ8oC,eAAiB,GAQ1CD,EAAqB7oC,EAAQ6oC,mBAAqB,EAMlDD,EAAmB5oC,EAAQ4oC,iBAAmBE,EAAiBC,EAQ/DJ,EAA6B3oC,EAAQ2oC,2BAA6B,OAAWI,EAG7EL,EAA2B1oC,EAAQ0oC,yBAA2B,GAAKK,EAEnEN,EAAmBzoC,EAAQyoC,iBAAmBC,EAA2B,EAEzEF,EAA6BxoC,EAAQwoC,2BAA6B,MAASO,EAE3ER,EAA4BvoC,EAAQuoC,0BAA4BI,EAA6BH,EAK7FF,EAAgCtoC,EAAQsoC,8BAAgCC,EACxEF,EAAgCroC,EAAQqoC,8BAAgC,GAaxED,EAAwBpoC,EAAQooC,sBAAwBE,EAAgCD,EAMxFF,EAAoCnoC,EAAQmoC,kCAAoC,OAAWW,EAG3FZ,EAA8BloC,EAAQkoC,4BAA8B,GAAKU,EAEzEX,EAAsBjoC,EAAQioC,oBAAsBC,EAA8B,EAclFF,GAZuBhoC,EAAQglC,qBAAuB,SAA8B5Z,GACpF,IAAIQ,GAAS,EAAIlY,EAAMyX,QAAQC,GAC3B4d,EAAS3oC,MAAMC,QAAQsrB,IAAU,EAAIlY,EAAMuY,iBAAiBL,GAAU,IAAIqd,YAAYrd,GACtFsd,EAAS7oC,MAAMC,QAAQsrB,IAAU,EAAIlY,EAAMsY,iBAAiBJ,GAAU,IAAIud,YAAYvd,GAGtFvW,EAAQ6zB,EAAOpd,MAAMsd,GAAkBJ,EAAO,GAAK,GACnD7jB,EAAqB,IAAd6jB,EAAO,GAAWE,EAAOpd,OAHjB,GAGuCkd,EAAO,IAAM,GAAKA,EAAOld,MAAMvsB,KAAKC,MAH3E,GAGgGwpC,EAAO,IAAM,IAEhI,OAAO,IAAIhB,EAAKgB,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAI3zB,EAAO8P,IAG5DnlB,EAAQgoC,KAAO,WACtB,SAASA,EAAKqB,EAAcC,EAAYC,EAAWC,EAAgBn0B,EAAO8P,IAlF9E,SAAyB9iB,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAmFxGiB,CAAgBC,KAAMylC,GAEtBzlC,KAAK8mC,aAAeA,EACpB9mC,KAAK+mC,WAAaA,EAClB/mC,KAAKgnC,UAAYA,EACjBhnC,KAAKinC,eAAiBA,EACtBjnC,KAAK8S,MAAQA,EACb9S,KAAK4iB,KAAOA,EAwDhB,OA7CA7jB,EAAa0mC,EAAM,CAAC,CAChB1rC,IAAK,MACL4D,MAAO,SAAa4qB,GAChB,IAAI2e,OAAK,EACT,GAAI3e,GAAa,EAAG,CAChB,GAAIA,EAAY,OAAWA,EAAY,OAAWA,GAAa,MAM3D,OADA2e,IADAA,EAAKlnC,KAAK8S,MAAMyV,GAAaie,KACjBF,IAAuB/d,EAAY2d,GACxClmC,KAAK4iB,KAAKskB,GAGrB,GAAI3e,GAAa,MASb,OADA2e,IADAA,EAAKlnC,KAAK8S,MAAMszB,GAA8B7d,EAAY,OAAUie,MACxDF,IAAuB/d,EAAY2d,GACxClmC,KAAK4iB,KAAKskB,GAGrB,GAAI3e,EAAYvoB,KAAKgnC,UAOjB,OALAE,EAAKrB,EAAwBD,GAAqCrd,GAAage,GAC/EW,EAAKlnC,KAAK8S,MAAMo0B,GAChBA,GAAM3e,GAAaie,EAAiBd,EAEpCwB,IADAA,EAAKlnC,KAAK8S,MAAMo0B,KACJZ,IAAuB/d,EAAY2d,GACxClmC,KAAK4iB,KAAKskB,GAErB,GAAI3e,GAAa,QACb,OAAOvoB,KAAK4iB,KAAK5iB,KAAKinC,gBAK9B,OAAOjnC,KAAK+mC,eAIbtB,EAjEe,K,kCC1F1BjoC,EAAOC,QAAU,o8iD,kCCAjBjE,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAAI+gB,EAAQ,EAAQ,MAwBpBjhB,EAAQ/C,QApBK,SAASysC,EAAOrqC,EAAGC,EAAG4xB,IAFnC,SAAyB7uB,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAG5GiB,CAAgBC,KAAMmnC,GAEtBnnC,KAAKkQ,KAAOwO,EAAMJ,KAAKG,OACvBze,KAAKlD,EAAIA,EACTkD,KAAKjD,EAAIA,EACTiD,KAAK2uB,OAASA,I,kCCdlBn1B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAGX,IAkBgCnD,EAlB5BoD,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf/E,EAAU,EAAQ,MAIlB6sC,GAFQ,EAAQ,MAEJ,EAAQ,OAEpBC,EAAiB,EAAQ,MAEzBtd,GAM4BvvB,EANa6sC,IAMQ7sC,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAJnF4W,EAAc,EAAQ,MAEtBC,EAAU,EAAQ,MAMtB,IAAIi2B,EAAW,WACX,SAASA,EAAS5tC,EAAQ6zB,IAH9B,SAAyBztB,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAIxGiB,CAAgBC,KAAMsnC,GAEtBtnC,KAAKtG,OAASA,EACdsG,KAAKutB,QAAUA,EACf7zB,EAAOu+B,OAAO1K,GA2OlB,OAxOAxuB,EAAauoC,EAAU,CAAC,CACpBvtC,IAAK,aACL4D,MAAO,SAAoB+S,GACnBA,EAAUyoB,cACVn5B,KAAKunC,+BAA+B72B,GACpC1Q,KAAKwnC,kBAAkB92B,MAGhC,CACC3W,IAAK,oBACL4D,MAAO,SAA2B+S,GAC9B,IAAIqC,EAAQ/S,KAER2tB,EAAW,WAYX,GAXIjd,EAAUsC,WAAWnZ,QACrB6W,EAAUsC,WAAWmL,SAAQ,SAAUspB,GACnC,GAAIA,aAAiB1d,EAAgBrvB,QAAS,CAC1C,IAAIkW,EAAQ62B,EAAMz2B,OAAOJ,MACzBmC,EAAMrZ,OAAOguC,eAAeD,EAAMj8B,OAAQoF,EAAMsD,MAAOtD,EAAMC,KAAMD,EAAM+E,eAAgB/E,EAAMiF,iBAE/F9C,EAAMrZ,OAAOiuC,UAAUF,EAAO/2B,EAAUE,MAAMsD,UAKtDxD,EAAUwG,MAAO,CACjB,IAAI0wB,EAAS70B,EAAMwa,QAAQgL,WAAWjD,IAAI5kB,EAAUwG,OACpD,GAAI0wB,EAAQ,CACR,IAAIC,GAAa,EAAIttC,EAAQqQ,qBAAqB8F,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMgD,QACzGk0B,EAAiC,iBAAjBF,EAAO9rC,OAAsB8rC,EAAO9rC,MAAQ,EAAI8rC,EAAO9rC,MAAQ+rC,EAAW/rC,MAC1FisC,EAAmC,iBAAlBH,EAAO7rC,QAAuB6rC,EAAO7rC,OAAS,EAAI6rC,EAAO7rC,OAAS8rC,EAAW9rC,OAC9F+rC,EAAS,GAAKC,EAAU,GACxBh1B,EAAMrZ,OAAOmiB,KAAK,EAAC,EAAIthB,EAAQkQ,yBAAyBiG,EAAUuG,gBAAgB,WAC9ElE,EAAMrZ,OAAO+qB,UAAUmjB,EAAQ,IAAIrtC,EAAQuQ,OAAO,EAAG,EAAGg9B,EAAQC,GAAUF,SAM1FG,EAAQt3B,EAAU4G,eAClB0wB,EAAMnuC,OACNmG,KAAKtG,OAAOmiB,KAAKmsB,EAAOra,GAExBA,MAGT,CACC5zB,IAAK,iCACL4D,MAAO,SAAwC+S,GAC3C,IAAIud,EAASjuB,KAETioC,GAAkBv3B,EAAUE,MAAM6C,WAAWpX,gBAAgBq7B,iBAAmBhnB,EAAUE,MAAM6C,WAAWgI,gBAAgB5hB,OAE3HquC,EAAuBx3B,EAAUE,MAAMgD,OAAOu0B,MAAK,SAAUv0B,GAC7D,OAAOA,EAAOgU,cAAgBvW,EAAQkW,aAAa1P,OAASjE,EAAO+T,YAAY+P,mBAG/E/J,EAAW,WACX,IAAIya,GAAyB,EAAIh3B,EAAY8I,iCAAiCxJ,EAAUuG,aAAcvG,EAAUE,MAAM6C,WAAWgJ,gBAE7HwrB,GACAha,EAAOv0B,OAAOmiB,KAAK,CAACusB,IAAyB,WACpC13B,EAAUE,MAAM6C,WAAWpX,gBAAgBq7B,iBAC5CzJ,EAAOv0B,OAAOq0B,KAAKrd,EAAUE,MAAM6C,WAAWpX,iBAGlD4xB,EAAOoa,sBAAsB33B,MAIrCA,EAAUE,MAAMgD,OAAOuK,SAAQ,SAAUvK,EAAQ8T,GACzC9T,EAAOgU,cAAgBvW,EAAQkW,aAAa1P,MAASjE,EAAO+T,YAAY+P,iBACxEzJ,EAAOqa,aAAa10B,EAAQ8T,EAAMhX,EAAUuG,kBAKxD,GAAIgxB,GAAkBC,EAAsB,CACxC,IAAIF,EAAQt3B,EAAUM,OAASN,EAAUM,OAAOsG,eAAiB,GAC7D0wB,EAAMnuC,OACNmG,KAAKtG,OAAOmiB,KAAKmsB,EAAOra,GAExBA,OAIb,CACC5zB,IAAK,wBACL4D,MAAO,SAA+B+S,GAClC,IAAI2e,EAASrvB,KAEb0Q,EAAUE,MAAM6C,WAAWgI,gBAAgB8N,MAAM,GAAG5b,UAAUwQ,SAAQ,SAAU1C,GACtC,QAAlCA,EAAgB3hB,OAAOkjB,QAAoBvB,EAAgB3hB,OAAOmjB,KAAKpjB,OACvEw1B,EAAOkZ,uBAAuB73B,EAAW+K,GAClC,YAAY4C,KAAK5C,EAAgB3hB,OAAOkjB,SAC/CqS,EAAOmZ,yBAAyB93B,EAAW+K,QAIxD,CACC1hB,IAAK,yBACL4D,MAAO,SAAgC+S,EAAW+C,GAC9C,IAAIyD,EAAQlX,KAAKutB,QAAQgL,WAAWjD,IAAI7hB,EAAW3Z,OAAOmjB,KAAK,IAC/D,GAAI/F,EAAO,CACP,IAAImF,GAA4B,EAAIjL,EAAY6I,oCAAoCvJ,EAAUE,MAAM6C,WAAWqI,iBAAkBpL,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMgD,QACxL60B,GAAsB,EAAIr3B,EAAYgJ,yBAAyB3G,EAAYyD,EAAOmF,GAClFhN,GAAW,EAAI+B,EAAY4I,6BAA6BvG,EAAWpE,SAAUo5B,EAAqBpsB,GAClG2R,GAAQ,EAAI5c,EAAY2I,+BAA+BtG,EAAYpE,EAAUo5B,EAAqBpsB,EAA2B3L,EAAUlF,QAEvIk9B,EAAW1rC,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAChE6rC,EAAW3rC,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,GACnEiD,KAAKtG,OAAOkvC,aAAa5a,EAAO9W,EAAOuxB,EAAqBC,EAAUC,MAG/E,CACC5uC,IAAK,2BACL4D,MAAO,SAAkC+S,EAAW+C,GAChD,IAAI4I,GAA4B,EAAIjL,EAAY6I,oCAAoCvJ,EAAUE,MAAM6C,WAAWqI,iBAAkBpL,EAAUlF,OAAQkF,EAAUE,MAAMjF,QAAS+E,EAAUE,MAAMgD,QACxL60B,GAAsB,EAAIr3B,EAAY+I,iCAAiC1G,EAAY4I,GACnFhN,GAAW,EAAI+B,EAAY4I,6BAA6BvG,EAAWpE,SAAUo5B,EAAqBpsB,GAClGwsB,EAAiB,IAAItuC,EAAQuQ,OAAO9N,KAAKuf,MAAMF,EAA0BrgB,KAAOqT,EAASvS,GAAIE,KAAKuf,MAAMF,EAA0BpgB,IAAMoT,EAAStS,GAAI0rC,EAAoB3sC,MAAO2sC,EAAoB1sC,QAEpM8wB,GAAW,EAAIua,EAAU0B,eAAep4B,EAAW+C,EAAW3Z,OAAQ+uC,GAC1E,GAAIhc,EACA,OAAQA,EAAS3c,MACb,KAAKk3B,EAAU2B,cAAcC,gBAEzBhpC,KAAKtG,OAAOuvC,qBAAqBJ,EAAgBhc,GACjD,MACJ,KAAKua,EAAU2B,cAAcG,gBAEzBlpC,KAAKtG,OAAOyvC,qBAAqBN,EAAgBhc,MAKlE,CACC9yB,IAAK,eACL4D,MAAO,SAAsBiW,EAAQ8T,EAAM0hB,GACvCppC,KAAKtG,OAAOiuC,WAAU,EAAIptC,EAAQoQ,oBAAoBy+B,EAAa1hB,GAAO9T,EAAO+T,eAEtF,CACC5tB,IAAK,cACL4D,MAAO,SAAqBy6B,GACxB,IAAIjI,EAASnwB,KAEb,GAAIo4B,EAAM1nB,UAAUyoB,YAAa,CAC7B,IAAIkQ,EAAWjR,EAAMgC,aACjBiP,IAAarpC,KAAKqpC,WAClBrpC,KAAKtG,OAAO4vC,WAAWlR,EAAMgC,cAC7Bp6B,KAAKqpC,SAAWA,GAGpB,IAAIh3B,EAAa+lB,EAAM1nB,UAAUE,MAAMqF,UACpB,OAAf5D,EACArS,KAAKtG,OAAOuc,UAAUmiB,EAAM1nB,UAAUlF,OAAOxP,KAAOqW,EAAWgsB,gBAAgB,GAAG1gC,MAAOy6B,EAAM1nB,UAAUlF,OAAOvP,IAAMoW,EAAWgsB,gBAAgB,GAAG1gC,MAAO0U,EAAW4D,WAAW,WAC7K,OAAOka,EAAOoZ,mBAAmBnR,MAGrCp4B,KAAKupC,mBAAmBnR,MAIrC,CACCr+B,IAAK,qBACL4D,MAAO,SAA4By6B,GAC/B,IAAIoR,EAAwBC,EAAsBrR,GAC9CsR,EAAyB9rC,EAAe4rC,EAAuB,GAC/DG,EAAiBD,EAAuB,GACxCE,EAAyCF,EAAuB,GAChEG,EAAiBH,EAAuB,GACxCI,EAAsBJ,EAAuB,GAC7CK,EAA2BL,EAAuB,GAElDM,EAAoBC,EAAiB7R,GACrC8R,EAAqBtsC,EAAeosC,EAAmB,GACvDG,EAAcD,EAAmB,GACjCE,EAAiBF,EAAmB,GAMxClqC,KAAKunC,+BAA+BnP,EAAM1nB,WAE1Ci5B,EAAeU,KAAKC,GAAcnsB,QAAQne,KAAKuqC,YAAavqC,MAE5DA,KAAKwnC,kBAAkBpP,EAAM1nB,WAC7B05B,EAAejsB,QAAQne,KAAKwqC,WAAYxqC,MAKxC8pC,EAAoB3rB,QAAQne,KAAKuqC,YAAavqC,MAE9C+pC,EAAyB5rB,QAAQne,KAAKuqC,YAAavqC,MACnDmqC,EAAYhsB,QAAQne,KAAKwqC,WAAYxqC,MAWrC4pC,EAAuCzrB,QAAQne,KAAKuqC,YAAavqC,MAGjE6pC,EAAeQ,KAAKC,GAAcnsB,QAAQne,KAAKuqC,YAAavqC,QAEjE,CACCjG,IAAK,SACL4D,MAAO,SAAgBy6B,GAcnB,OAXIp4B,KAAKutB,QAAQlxB,iBACb2D,KAAKtG,OAAOw3B,UAAUlxB,KAAKutB,QAAQzwB,EAAGkD,KAAKutB,QAAQxwB,EAAGiD,KAAKutB,QAAQzxB,MAAOkE,KAAKutB,QAAQxxB,OAAQiE,KAAKutB,QAAQlxB,iBAEhH2D,KAAKuqC,YAAYnS,GACJp4B,KAAKtG,OAAO+wC,gBAW1BnD,EAjPI,GAoPf7pC,EAAQ/C,QAAU4sC,EAGlB,IAAI2C,EAAmB,SAA0B7R,GAK7C,IAJA,IAAI+R,EAAc,GACdC,EAAiB,GAEjBvwC,EAASu+B,EAAMwB,SAAS//B,OACnBF,EAAI,EAAGA,EAAIE,EAAQF,IAAK,CAC7B,IAAI8tC,EAAQrP,EAAMwB,SAASjgC,GACvB8tC,EAAMiD,gBACNP,EAAYxrC,KAAK8oC,GAEjB2C,EAAezrC,KAAK8oC,GAG5B,MAAO,CAAC0C,EAAaC,IAGrBX,EAAwB,SAA+BrR,GAOvD,IANA,IAAIuR,EAAiB,GACjBC,EAAyC,GACzCC,EAAiB,GACjBC,EAAsB,GACtBC,EAA2B,GAC3BlwC,EAASu+B,EAAMuB,SAAS9/B,OACnBF,EAAI,EAAGA,EAAIE,EAAQF,IAAK,CAC7B,IAAI8tC,EAAQrP,EAAMuB,SAAShgC,GACvB8tC,EAAM/2B,UAAUuH,gBAAkBwvB,EAAM/2B,UAAUE,MAAMqE,QAAU,GAAKwyB,EAAM/2B,UAAUiG,gBACnF8wB,EAAM/2B,UAAUE,MAAM2F,OAAOyoB,MAAQ,EACrC2K,EAAehrC,KAAK8oC,GACbA,EAAM/2B,UAAUE,MAAM2F,OAAOyoB,MAAQ,EAC5C6K,EAAelrC,KAAK8oC,GAEpBmC,EAAuCjrC,KAAK8oC,GAG5CA,EAAM/2B,UAAUgH,aAChBoyB,EAAoBnrC,KAAK8oC,GAEzBsC,EAAyBprC,KAAK8oC,GAI1C,MAAO,CAACkC,EAAgBC,EAAwCC,EAAgBC,EAAqBC,IAGrGO,EAAe,SAAsBrpC,EAAGD,GACxC,OAAIC,EAAEyP,UAAUE,MAAM2F,OAAOyoB,MAAQh+B,EAAE0P,UAAUE,MAAM2F,OAAOyoB,MACnD,EACA/9B,EAAEyP,UAAUE,MAAM2F,OAAOyoB,MAAQh+B,EAAE0P,UAAUE,MAAM2F,OAAOyoB,OACzD,EAGL/9B,EAAEyP,UAAUoC,MAAQ9R,EAAE0P,UAAUoC,MAAQ,GAAK,I,kCCpUxDtZ,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQktC,kCAAoCltC,EAAQqrC,cAAgBrrC,EAAQmtC,eAAiBntC,EAAQotC,eAAiBptC,EAAQqtC,sBAAwBrtC,EAAQsrC,mBAAgBzqC,EAE9K,IAAIV,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAMllBisC,GAFkB3wC,EAFD,EAAQ,OAIhB,EAAQ,OAIjB8W,EAAU9W,EAFD,EAAQ,OAIjB+3B,EAAU,EAAQ,MAElB1X,EAAWrgB,EAAuB+3B,GAElChhB,EAAQ,EAAQ,MAEpB,SAAS/W,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAEvF,SAASuF,EAAgBD,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAEhH,IAAIksC,EAAiB,8DACjBC,EAAoB,yCACpBC,EAAmB,gBACnBC,EAAoB,wDACpBC,EAA0B,yPAE1BrC,EAAgBtrC,EAAQsrC,cAAgB,CACxCC,gBAAiB,EACjBE,gBAAiB,GAGjB4B,EAAwBrtC,EAAQqtC,sBAAwB,CACxDrsB,OAAQ,EACR4sB,QAAS,GAGTC,EAAsB,CACtBtvC,KAAM,IAAIye,EAAS/f,QAAQ,MAC3BuB,IAAK,IAAIwe,EAAS/f,QAAQ,MAC1B40B,OAAQ,IAAI7U,EAAS/f,QAAQ,OAC7B2vB,MAAO,IAAI5P,EAAS/f,QAAQ,QAC5B0vB,OAAQ,IAAI3P,EAAS/f,QAAQ,SAG7BmwC,EAAiBptC,EAAQotC,eAAiB,SAASA,EAAe7d,EAAYgC,GAC9EjvB,EAAgBC,KAAM6qC,GAEtB7qC,KAAKkQ,KAAO64B,EAAcC,gBAC1BhpC,KAAKgtB,WAAaA,EAClBhtB,KAAKgvB,UAAYA,GAGjB4b,EAAiBntC,EAAQmtC,eAAiB,SAASA,EAAe5d,EAAYue,EAAOjc,EAAQX,GAC7F5uB,EAAgBC,KAAM4qC,GAEtB5qC,KAAKkQ,KAAO64B,EAAcG,gBAC1BlpC,KAAKgtB,WAAaA,EAClBhtB,KAAKurC,MAAQA,EACbvrC,KAAKsvB,OAASA,EACdtvB,KAAK2uB,OAASA,GAoBd6c,GAjBgB/tC,EAAQqrC,cAAgB,SAAuBp4B,EAAWhV,EAAM8P,GAChF,IAAIyR,EAAOvhB,EAAKuhB,KACZD,EAASthB,EAAKshB,OACdiB,EAASviB,EAAKuiB,OAElB,MAAe,oBAAXjB,EACOyuB,EAAoBxuB,EAAMzR,IAAUyS,GACzB,aAAXjB,GAAqC,WAAZC,EAAK,GAE9BwuB,EAAoB,CAAC,aAAaj0B,OAAOk0B,EAA4BzuB,EAAKsM,MAAM,KAAM/d,IAAUyS,GACrF,oBAAXjB,EACA2uB,EAAoBj7B,EAAsB,aAAXuN,EAAwB0sB,EAAkC1tB,GAAQA,EAAMzR,GAC5F,aAAXwR,GAAqC,WAAZC,EAAK,GAC9B0uB,EAAoBj7B,EAAWg7B,EAA4Bf,EAAkC1tB,EAAKsM,MAAM,KAAM/d,QADlH,GAKW,SAAyByR,EAAM2uB,EAAqBC,GAGtE,IAFA,IAAI7e,EAAa,GAERrzB,EAAIiyC,EAAqBjyC,EAAIsjB,EAAKpjB,OAAQF,IAAK,CACpD,IAAIgE,EAAQsf,EAAKtjB,GACbmyC,EAAaZ,EAAiB7sB,KAAK1gB,GACnCouC,EAAiBpuC,EAAMquC,YAAY,KACnCC,EAAS,IAAI/6B,EAAQxW,QAAQoxC,EAAanuC,EAAMgD,UAAU,EAAGorC,GAAkBpuC,GAC/EuuC,EAAQJ,EAAa,IAAIrxB,EAAS/f,QAAQiD,EAAMgD,UAAUorC,EAAiB,IAAMpyC,IAAMiyC,EAAsB,IAAInxB,EAAS/f,QAAQ,MAAQf,IAAMsjB,EAAKpjB,OAAS,EAAI,IAAI4gB,EAAS/f,QAAQ,QAAU,KACrMsyB,EAAWruB,KAAK,CAAEuV,MAAO+3B,EAAQ/e,KAAMgf,IAiB3C,IAdA,IAAIC,EAA2Bnf,EAAWjQ,KAAI,SAAUlc,GACpD,IAAIqT,EAAQrT,EAAMqT,MACdgZ,EAAOrsB,EAAMqsB,KAIjB,MAAO,CACHhZ,MAAOA,EAEPgZ,KAL8B,IAAf2e,EAAmB,EAAI3e,EAAOA,EAAKlf,iBAAiB69B,GAAcA,EAAa,SASlGO,EAAoBD,EAAyB,GAAGjf,KAC3C1uB,EAAK,EAAGA,EAAK2tC,EAAyBtyC,OAAQ2E,IACnD,GAA0B,OAAtB4tC,EAA4B,CAC5B,IAAIC,EAASF,EAAyB3tC,GAAI0uB,KAC1C,GAAe,OAAXmf,EAAiB,CAEjB,IADA,IAAI3I,EAAIllC,EACoC,OAArC2tC,EAAyBzI,GAAGxW,MAC/BwW,IAKJ,IAHA,IAAI4I,EAAQ5I,EAAIllC,EAAK,EAEjB+tC,GADgBJ,EAAyBzI,GAAGxW,KAChBkf,GAAqBE,EAC9C9tC,EAAKklC,EAAGllC,IACX4tC,EAAoBD,EAAyB3tC,GAAI0uB,KAAOkf,EAAoBG,OAGhFH,EAAoBC,EAKhC,OAAOF,IAGPV,EAAsB,SAA6BxuB,EAAMzR,EAAQghC,GACjE,IAAIC,GAAQ,EAAI1B,EAAO2B,YAAYzvB,EAAK,IACpC0vB,EAAqB3B,EAAe3sB,KAAKpB,EAAK,IAC9C2vB,EAAgBD,GAAgC,OAAVF,GAAkBxB,EAAkB5sB,KAAKpB,EAAK,IACpF+R,EAAY4d,EAA0B,OAAVH,EAAiBI,EAEjDL,EAAYC,EAAkB,GAAVzvC,KAAK4xB,GAAW6d,EAAOjhC,GAAUmhC,EAAqBG,EAAkB7vB,EAAK,GAAIzR,GAAUuhC,EAAqB9vB,EAAK,GAAIzR,GAAUqhC,EAA2B7vC,KAAK4xB,GAAIpjB,GACvLogC,EAAsBgB,EAAgB,EAAI,EAG1Cf,EAAa7uC,KAAKkD,KAAI,EAAIiR,EAAMoI,UAAUvc,KAAK2uB,IAAIqD,EAAUG,IAAMnyB,KAAK2uB,IAAIqD,EAAUC,IAAKjyB,KAAK2uB,IAAIqD,EAAUI,IAAMpyB,KAAK2uB,IAAIqD,EAAUE,KAAqB,EAAf1jB,EAAO1P,MAA2B,EAAhB0P,EAAOzP,QAE1K,OAAO,IAAI8uC,EAAeW,EAAgBvuB,EAAM2uB,EAAqBC,GAAa7c,IAGlF2c,EAAsB,SAA6Bj7B,EAAWuM,EAAMzR,GACpE,IAAI6X,EAAIpG,EAAK,GAAG9c,MAAMirC,GAClBG,EAAQloB,IAAe,WAATA,EAAE,SACX/kB,IAAT+kB,EAAE,SAA6B/kB,IAAT+kB,EAAE,IACtBynB,EAAsBrsB,OAASqsB,EAAsBO,QACnD1c,EAAS,GACTW,EAAS,GAETjM,SAEa/kB,IAAT+kB,EAAE,KACFsL,EAAO7xB,GAAI,EAAIq1B,EAAQviB,kCAAkCc,EAAW2S,EAAE,GAAIA,EAAE,IAAIrV,iBAAiBxC,EAAO1P,aAG/FwC,IAAT+kB,EAAE,KACFsL,EAAO5xB,GAAI,EAAIo1B,EAAQviB,kCAAkCc,EAAW2S,EAAE,GAAIA,EAAE,IAAIrV,iBAAiBxC,EAAOzP,SAIxGsnB,EAAE,GACFiM,EAAOxyB,EAAIwuC,EAAoBjoB,EAAE,GAAG3iB,oBACpBpC,IAAT+kB,EAAE,KACTiM,EAAOxyB,GAAI,EAAIq1B,EAAQviB,kCAAkCc,EAAW2S,EAAE,GAAIA,EAAE,KAG5EA,EAAE,IACFiM,EAAOvyB,EAAIuuC,EAAoBjoB,EAAE,IAAI3iB,oBACpBpC,IAAV+kB,EAAE,MACTiM,EAAOvyB,GAAI,EAAIo1B,EAAQviB,kCAAkCc,EAAW2S,EAAE,IAAKA,EAAE,OAIrF,IAAI2pB,EAAiB,CACjBlwC,OAAgBwB,IAAbgxB,EAAOxyB,EAAkB0O,EAAO1P,MAAQ,EAAIwzB,EAAOxyB,EAAEkR,iBAAiBxC,EAAO1P,OAChFiB,OAAgBuB,IAAbgxB,EAAOvyB,EAAkByO,EAAOzP,OAAS,EAAIuzB,EAAOvyB,EAAEiR,iBAAiBxC,EAAOzP,SAEjFkxC,EAAiBC,EAAgB7pB,GAAKA,EAAE,IAAM,kBAAmBkoB,EAAOyB,EAAgBre,EAAQnjB,GAEpG,OAAO,IAAIo/B,EAAeY,EAAgBvuB,EAAMoG,EAAI,EAAI,EAAGrmB,KAAKkD,IAAI+sC,EAAenwC,EAAGmwC,EAAelwC,IAAKwuC,EAAOyB,EAAgBC,IAGjIJ,EAA6B,SAAoCM,EAAQ3hC,GACzE,IAAI1P,EAAQ0P,EAAO1P,MACfC,EAASyP,EAAOzP,OAChBqxC,EAAqB,GAARtxC,EACbuxC,EAAuB,GAATtxC,EAEduxC,GADatwC,KAAK2uB,IAAI7vB,EAAQkB,KAAKuwC,IAAIJ,IAAWnwC,KAAK2uB,IAAI5vB,EAASiB,KAAKwwC,IAAIL,KAC7C,EAEhChe,EAAKie,EAAapwC,KAAKuwC,IAAIJ,GAAUG,EACrCle,EAAKie,EAAcrwC,KAAKwwC,IAAIL,GAAUG,EAI1C,MAAO,CAAEne,GAAIA,EAAIF,GAHRnzB,EAAQqzB,EAGQC,GAAIA,EAAIF,GAFxBnzB,EAASqzB,IAKlBqe,EAAgB,SAAuBjiC,GACvC,OAAOxO,KAAK0wC,KAAKliC,EAAO1P,MAAQ,IAAK,EAAIqV,EAAMoI,UAAU/N,EAAO1P,MAAO0P,EAAOzP,QAAU,KAGxF+wC,EAAoB,SAA2BplB,EAAMlc,GACrD,OAAQkc,GACJ,IAAK,SACL,IAAK,SACD,OAAOmlB,EAA2B,EAAGrhC,GACzC,IAAK,OACL,IAAK,WACD,OAAOqhC,EAA2B7vC,KAAK4xB,GAAK,EAAGpjB,GACnD,IAAK,QACL,IAAK,UACD,OAAOqhC,EAA2B,EAAI7vC,KAAK4xB,GAAK,EAAGpjB,GACvD,IAAK,YACL,IAAK,YACL,IAAK,iBACL,IAAK,iBACD,OAAOqhC,EAA2B7vC,KAAK4xB,GAAK6e,EAAcjiC,GAASA,GACvE,IAAK,WACL,IAAK,WACL,IAAK,kBACL,IAAK,kBACD,OAAOqhC,EAA2B7vC,KAAK4xB,GAAK6e,EAAcjiC,GAASA,GACvE,IAAK,cACL,IAAK,cACL,IAAK,eACL,IAAK,eACD,OAAOqhC,EAA2BY,EAAcjiC,GAASA,GAC7D,IAAK,eACL,IAAK,eACL,IAAK,cACL,IAAK,cACD,OAAOqhC,EAA2B,EAAI7vC,KAAK4xB,GAAK6e,EAAcjiC,GAASA,GAC3E,IAAK,MACL,IAAK,YACL,QACI,OAAOqhC,EAA2B7vC,KAAK4xB,GAAIpjB,KAInDuhC,EAAuB,SAA8BN,EAAOjhC,GAC5D,IAAImiC,EAAmBlB,EAAMrvB,MAAM,KAAKL,IAAI1M,YACxCu9B,EAAoBhwC,EAAe+vC,EAAkB,GACrD3xC,EAAO4xC,EAAkB,GACzB3xC,EAAM2xC,EAAkB,GAExBC,EAAQ7xC,EAAO,IAAMwP,EAAO1P,OAASG,EAAM,IAAMuP,EAAOzP,QAE5D,OAAO8wC,EAA2B7vC,KAAK8wC,KAAKx9B,MAAMu9B,GAAS,EAAIA,GAAS7wC,KAAK4xB,GAAK,EAAGpjB,IAGrFuiC,EAAa,SAAoBviC,EAAQ1O,EAAGC,EAAGixC,GAI/C,MAHc,CAAC,CAAElxC,EAAG,EAAGC,EAAG,GAAK,CAAED,EAAG,EAAGC,EAAGyO,EAAOzP,QAAU,CAAEe,EAAG0O,EAAO1P,MAAOiB,EAAG,GAAK,CAAED,EAAG0O,EAAO1P,MAAOiB,EAAGyO,EAAOzP,SAGpGkvB,QAAO,SAAUgjB,EAAMC,GAClC,IAAIC,GAAI,EAAIh9B,EAAMoI,UAAUzc,EAAIoxC,EAAOpxC,EAAGC,EAAImxC,EAAOnxC,GACrD,OAAIixC,EAAUG,EAAIF,EAAKG,gBAAkBD,EAAIF,EAAKG,iBACvC,CACHC,cAAeH,EACfE,gBAAiBD,GAIlBF,IACR,CACCG,gBAAiBJ,EAAUM,KAAYA,IACvCD,cAAe,OAChBA,eAGHnB,EAAkB,SAAyBqB,EAAQhD,EAAOjc,EAAQX,EAAQnjB,GAC1E,IAAI1O,EAAIwyB,EAAOxyB,EACXC,EAAIuyB,EAAOvyB,EACXyxC,EAAK,EACLC,EAAK,EAET,OAAQF,GACJ,IAAK,eAGGhD,IAAUT,EAAsBrsB,OAChC+vB,EAAKC,EAAKzxC,KAAKkD,IAAIlD,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,OAAQkB,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,SACtFwvC,IAAUT,EAAsBO,UACvCmD,EAAKxxC,KAAKkD,IAAIlD,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,QAC/C2yC,EAAKzxC,KAAKkD,IAAIlD,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,UAEnD,MAEJ,IAAK,iBAGD,GAAIwvC,IAAUT,EAAsBrsB,OAChC+vB,EAAKC,EAAKzxC,KAAKkD,KAAI,EAAIiR,EAAMoI,UAAUzc,EAAGC,IAAI,EAAIoU,EAAMoI,UAAUzc,EAAGC,EAAIyO,EAAOzP,SAAS,EAAIoV,EAAMoI,UAAUzc,EAAI0O,EAAO1P,MAAOiB,IAAI,EAAIoU,EAAMoI,UAAUzc,EAAI0O,EAAO1P,MAAOiB,EAAIyO,EAAOzP,cACjL,GAAIwvC,IAAUT,EAAsBO,QAAS,CAEhD,IAAIjtB,EAAIphB,KAAKkD,IAAIlD,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,SAAWiB,KAAKkD,IAAIlD,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,QACnGoyC,EAASH,EAAWviC,EAAQ1O,EAAGC,GAAG,GAEtC0xC,EAAKrwB,GADLowB,GAAK,EAAIr9B,EAAMoI,UAAU20B,EAAOpxC,EAAIA,GAAIoxC,EAAOnxC,EAAIA,GAAKqhB,IAG5D,MAEJ,IAAK,gBAEGmtB,IAAUT,EAAsBrsB,OAChC+vB,EAAKC,EAAKzxC,KAAKoP,IAAIpP,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,OAAQkB,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,SACtFwvC,IAAUT,EAAsBO,UACvCmD,EAAKxxC,KAAKoP,IAAIpP,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,QAC/C2yC,EAAKzxC,KAAKoP,IAAIpP,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,UAEnD,MAEJ,IAAK,kBAGD,GAAIwvC,IAAUT,EAAsBrsB,OAChC+vB,EAAKC,EAAKzxC,KAAKoP,KAAI,EAAI+E,EAAMoI,UAAUzc,EAAGC,IAAI,EAAIoU,EAAMoI,UAAUzc,EAAGC,EAAIyO,EAAOzP,SAAS,EAAIoV,EAAMoI,UAAUzc,EAAI0O,EAAO1P,MAAOiB,IAAI,EAAIoU,EAAMoI,UAAUzc,EAAI0O,EAAO1P,MAAOiB,EAAIyO,EAAOzP,cACjL,GAAIwvC,IAAUT,EAAsBO,QAAS,CAEhD,IAAIqD,EAAK1xC,KAAKoP,IAAIpP,KAAK2uB,IAAI5uB,GAAIC,KAAK2uB,IAAI5uB,EAAIyO,EAAOzP,SAAWiB,KAAKoP,IAAIpP,KAAK2uB,IAAI7uB,GAAIE,KAAK2uB,IAAI7uB,EAAI0O,EAAO1P,QACpG6yC,EAAUZ,EAAWviC,EAAQ1O,EAAGC,GAAG,GAEvC0xC,EAAKC,GADLF,GAAK,EAAIr9B,EAAMoI,UAAUo1B,EAAQ7xC,EAAIA,GAAI6xC,EAAQ5xC,EAAIA,GAAK2xC,IAG9D,MAEJ,QAEIF,EAAK7f,EAAO7xB,GAAK,EACjB2xC,OAAkBnwC,IAAbqwB,EAAO5xB,EAAkB4xB,EAAO5xB,EAAIyxC,EAIjD,MAAO,CACH1xC,EAAG0xC,EACHzxC,EAAG0xC,IAIP9D,EAAoCltC,EAAQktC,kCAAoC,SAA2C1tB,GAC3H,IAAIsuB,EAAQ,GACR5c,EAAS,GACT4f,EAAS,GACTl/B,EAAW,GACXu/B,EAAM,EAENn4B,EAAW,wFAEXo4B,EAAS,4CAETC,EAAqB7xB,EAAK2xB,GAAKzuC,MAAMsW,GACrCq4B,GACAF,IAGJ,IAAIG,EAAmB9xB,EAAK2xB,GAAKzuC,MARV,qGASnB4uC,IACAxD,EAAQwD,EAAiB,IAAM,GAEhB,aADfR,EAASQ,EAAiB,IAAM,IAE5BR,EAAS,eACS,UAAXA,IACPA,EAAS,mBAEbK,KAGJ,IAAII,EAAmB/xB,EAAK2xB,GAAKzuC,MAAM0uC,GACnCG,GACAJ,IAGJ,IAAIK,EAAmBhyB,EAAK2xB,GAAKzuC,MAAMsW,GACnCw4B,GACAL,IAGJ,IAAIM,EAAiBjyB,EAAK2xB,GAAKzuC,MAAM0uC,GACjCK,GACAN,IAGJ,IAAIO,EAAgBF,GAAoBH,EACpCK,GAAiBA,EAAc,KAC/B9/B,EAAW8/B,EAAc,IAAM,QAAQ9wB,KAAK8wB,EAAc,IAAM,KAAO,IACnEA,EAAc,KACd9/B,GAAY,IAAM8/B,EAAc,IAAM,QAAQ9wB,KAAK8wB,EAAc,IAAM,KAAO,MAItF,IAAIC,EAAcF,GAAkBF,EAiBpC,OAhBII,IACAzgB,EAASygB,EAAY,GAChBA,EAAY,KACbzgB,GAAU,QAIdtf,GAAak8B,GAAU5c,GAAW4f,IAClC5f,EAAStf,EACTA,EAAW,IAGXA,IACAA,EAAW,MAAQA,GAGhB,CAAC,CAACk8B,EAAOgD,EAAQ5f,EAAQtf,GAAUggC,QAAO,SAAU12B,GACvD,QAASA,KACV6X,KAAK,MAAMhZ,OAAOyF,EAAKsM,MAAMqlB,KAGhClD,EAA8B,SAAqCzuB,GACnE,OAAOA,EAAKF,KAAI,SAAU7I,GACtB,OAAOA,EAAM/T,MAAMgrC,MAGtBpuB,KAAI,SAAUtM,EAAGqC,GACd,IAAKrC,EACD,OAAOwM,EAAKnK,GAGhB,OAAQrC,EAAE,IACN,IAAK,OACD,OAAOA,EAAE,GAAK,MAClB,IAAK,KACD,OAAOA,EAAE,GAAK,QAClB,IAAK,aACD,MAAa,MAATA,EAAE,GACKA,EAAE,GAAK,IAAMA,EAAE,GAEnBA,EAAE,GAAK,IAAyB,IAAnBJ,WAAWI,EAAE,IAAY,U,kCCzb7DjX,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEX,IAAI2xC,EAAQ,uCAEK7xC,EAAQivC,WAAa,SAAoBD,GACtD,IAAItsC,EAAQssC,EAAMtsC,MAAMmvC,GAExB,GAAInvC,EAAO,CACP,IAAIxC,EAAQ0S,WAAWlQ,EAAM,IAC7B,OAAQA,EAAM,GAAGO,eACb,IAAK,MACD,OAAO1D,KAAK4xB,GAAKjxB,EAAQ,IAC7B,IAAK,OACD,OAAOX,KAAK4xB,GAAK,IAAMjxB,EAC3B,IAAK,MACD,OAAOA,EACX,IAAK,OACD,OAAiB,EAAVX,KAAK4xB,GAASjxB,GAIjC,OAAO,O,kCCtBXnE,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQy6B,YAAcz6B,EAAQq6B,oBAAiBx5B,EAE/C,IAAIV,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllBC,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf/E,EAAU,EAAQ,MAElBg1C,EAAS,EAAQ,MAIjBC,EAAmBp1C,EAFD,EAAQ,OAI1B+W,EAAQ,EAAQ,MAEhBC,EAAc,EAAQ,MAItBjX,EAAmBC,EAFD,EAAQ,OAI1Bq1C,EAAqB,EAAQ,MAEjC,SAASr1C,EAAuBI,GAAO,OAAOA,GAAOA,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAIvF,IAEIs9B,EAAiBr6B,EAAQq6B,eAAiB,WAC1C,SAASA,EAAel9B,EAAS2yB,EAASxyB,EAAQ20C,EAAYC,IALlE,SAAyB7vC,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAMxGiB,CAAgBC,KAAM83B,GAEtB93B,KAAK4vC,iBAAmBh1C,EACxBoF,KAAK6vC,iBAAmB,GACxB7vC,KAAK8vC,WAAaJ,EAClB1vC,KAAK+vC,aAAeL,EACpB1vC,KAAKjF,OAASA,EACdiF,KAAKutB,QAAUA,EACfvtB,KAAK2vC,SAAWA,EAChB3vC,KAAK6S,eAAiB,IAAI28B,EAAiB90C,QAAQ6yB,EAASxyB,EAAQsqB,QACpErlB,KAAKgwC,kBAAoB,CACrBC,SAAU,GACVC,WAAY,GAGhBlwC,KAAKkM,gBAAkBlM,KAAK20B,UAAU/5B,EAAQM,cAAcgR,iBAiPhE,OA9OAnN,EAAa+4B,EAAgB,CAAC,CAC1B/9B,IAAK,kBACL4D,MAAO,SAAyB0N,GAC5B,IAAI0H,EAAQ/S,KAEZ,GAAIA,KAAK+vC,cAAgB1kC,EAAM,CAC3B,IAAIuF,EAAQvF,EAAKuF,MACjBzV,QAAQs9B,KAAI,EAAIrnB,EAAY0I,sBAAsBlJ,EAAM6K,iBAAiBsB,KAAI,SAAUtB,GACnF,MAA+B,QAA3BA,EAAgBuB,OACTjK,EAAMF,eAAes9B,YAAY10B,EAAgBwB,KAAK,IAAI4I,MAAK,SAAU7M,GAC5E,OAAOA,GAA0B,iBAAZA,EAAIE,IAAmB,QAAUF,EAAIE,IAAM,KAAO,UACxE6M,OAAM,SAAUpB,GACX,KAKLxpB,QAAQ2pB,QAAQ,GAAKrJ,EAAgBwC,OAASxC,EAAgBuB,OAAS,IAAMvB,EAAgBwB,KAAKuT,KAAK,KAAO,SACrH3K,MAAK,SAAUuqB,GACXA,EAAiBv2C,OAAS,IAE1B+W,EAAMvU,gBAAkB,IAE5BuU,EAAM6K,gBAAkB20B,EAAiB5f,KAAK,QAG9CnlB,aAAgBglC,kBAChBrwC,KAAK6S,eAAes9B,YAAY9kC,EAAK6N,KAAK2M,MAAK,SAAU7M,GACrD,GAAIA,GAAO3N,aAAgBglC,kBAAoBhlC,EAAKgpB,WAAY,CAC5D,IAAIA,EAAahpB,EAAKgpB,WAClBic,GAAc,EAAIn/B,EAAMqI,eAAenO,EAAKuF,MAAOoI,EAAI2b,WAAU,IACrEN,EAAWO,aAAa0b,EAAajlC,OAE1C0a,OAAM,SAAUpB,GACX,QAOrB,CACC5qB,IAAK,cACL4D,MAAO,SAAqBqO,GACxB,IAAIiiB,EAASjuB,KAEb,OAAO7E,QAAQs9B,IAAI36B,MAAMsnB,KAAKpZ,EAASukC,aAAaxzB,KAAI,SAAUyzB,GAC9D,OAAIA,EAAMC,KACCnrB,MAAMkrB,EAAMC,MAAM5qB,MAAK,SAAU6qB,GACpC,OAAOA,EAAI/tB,UACZkD,MAAK,SAAUlD,GACd,OAAOguB,EAA8BhuB,EAAM6tB,EAAMC,SAClD1qB,OAAM,SAAUpB,GAIf,MAAO,MAGRisB,EAAcJ,EAAOxkC,OAC5B6Z,MAAK,SAAUgrB,GACf,OAAOA,EAAM5lB,QAAO,SAAU6lB,EAAKjgC,GAC/B,OAAOigC,EAAIt5B,OAAO3G,KACnB,OACJgV,MAAK,SAAUgrB,GACd,OAAO11C,QAAQs9B,IAAIoY,EAAM9zB,KAAI,SAAUlM,GACnC,OAAOyU,MAAMzU,EAAKkgC,QAAQ,GAAG73B,KAAK2M,MAAK,SAAU4Q,GAC7C,OAAOA,EAASua,UACjBnrB,MAAK,SAAUmrB,GACd,OAAO,IAAI71C,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAIs7B,EAAS,IAAIC,WACjBD,EAAO1R,QAAU5pB,EACjBs7B,EAAO3R,OAAS,WAEZ,IAAIuD,EAASoO,EAAOpO,OACpBxD,EAAQwD,IAEZoO,EAAOE,cAAcoa,SAE1BnrB,MAAK,SAAUorB,GAEd,OADApgC,EAAKqgC,SAASv3B,YAAY,MAAO,QAAUs3B,EAAU,MAC9C,eAAiBpgC,EAAKqgC,SAASC,QAAU,cAGzDtrB,MAAK,SAAUurB,GACd,IAAIxgC,EAAQ5E,EAAS8X,cAAc,SACnClT,EAAMygC,YAAcD,EAAQ5gB,KAAK,MACjCvC,EAAO/hB,gBAAgB8X,YAAYpT,QAG5C,CACC7W,IAAK,qBACL4D,MAAO,SAA4B0N,GAC/B,IAAIgkB,EAASrvB,KAEb,GAAIA,KAAK8vC,YAAczkC,aAAgBimC,kBAAmB,CACtD,IAAIt4B,EAAM3N,EAAKnQ,cAAc4oB,cAAc,OAC3C,IAEI,OADA9K,EAAIE,IAAM7N,EAAKqZ,YACR1L,EACT,MAAO2L,GACD,GAMZ,GAAItZ,aAAgBkmC,kBAAmB,CACnC,IAAIC,EAAanmC,EAAKspB,WAAU,GAC5Bvb,EAAYq4B,IAChBD,EAAW94B,aAAa,uCAAwCU,GAEhE,IAAIs4B,GAAe,EAAIn3C,EAAQsB,aAAawP,EAAM,EAAG,GACjDvP,EAAQ41C,EAAa51C,MACrBC,EAAS21C,EAAa31C,OAsC1B,OApCAiE,KAAK6S,eAAe8+B,MAAMv4B,GAAaw4B,EAAyBvmC,EAAMrL,KAAKutB,SAAS1H,MAAK,SAAU3Z,GAC/F,OAAOmjB,EAAOsgB,SAASzjC,EAAiB,CACpC/P,MAAOkzB,EAAO9B,QAAQpxB,MACtBC,WAAYizB,EAAO9B,QAAQnxB,WAC3BC,gBAAiB,UACjBO,OAAQ,KACRN,aAAc+yB,EAAO9B,QAAQjxB,aAC7BtB,QAASq0B,EAAO9B,QAAQvyB,QACxBuB,MAAO8yB,EAAO9B,QAAQhxB,MACtBC,gBAAiB6yB,EAAO9B,QAAQ/wB,gBAChCE,MAAO2yB,EAAO9B,QAAQ7wB,MACtBD,uBAAwB4yB,EAAO9B,QAAQ9wB,uBACvCI,QAASwyB,EAAO9B,QAAQ1wB,QACxBnD,OAAQ,IAAIS,EAAiBO,QAC7BoB,MAAOA,EACPC,OAAQA,EACRe,EAAG,EACHC,EAAG,EACHG,YAAagP,EAAgBhR,cAAcG,YAAY8B,WACvDC,aAAc8O,EAAgBhR,cAAcG,YAAYgC,YACxD/B,QAAS4Q,EAAgBhR,cAAcG,YAAYE,YACnDC,QAAS0Q,EAAgBhR,cAAcG,YAAYI,aACpD4zB,EAAOt0B,OAAO0sC,MAAMruB,OACxByM,MAAK,SAAUjpB,GACd,OAAO,IAAIzB,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAIy2C,EAAe7lC,SAAS8X,cAAc,OAC1C+tB,EAAa9sB,OAAS,WAClB,OAAOD,EAAQloB,IAEnBi1C,EAAa7sB,QAAU5pB,EACvBy2C,EAAa34B,IAAMtc,EAAO8nB,YACtB8sB,EAAWnd,YACXmd,EAAWnd,WAAWO,cAAa,EAAIzjB,EAAMqI,eAAenO,EAAKnQ,cAAcG,YAAY+X,iBAAiB/H,GAAOwmC,GAAeL,SAIvIA,EAGX,GAAInmC,aAAgBymC,kBAAoBzmC,EAAKmlC,OAASnlC,EAAKmlC,MAAMuB,SAAU,CACvE,IAAIC,EAAM,GAAGzoB,MAAMrvB,KAAKmR,EAAKmlC,MAAMuB,SAAU,GAAG9mB,QAAO,SAAU+mB,EAAKC,GAClE,OAAOD,EAAMC,EAAKd,UACnB,IACCvgC,EAAQvF,EAAKspB,WAAU,GAE3B,OADA/jB,EAAMygC,YAAcW,EACbphC,EAGX,OAAOvF,EAAKspB,WAAU,KAE3B,CACC56B,IAAK,YACL4D,MAAO,SAAmB0N,GACtB,IAAI6mC,EAAQ7mC,EAAK8mC,WAAaC,KAAKC,UAAYrmC,SAASye,eAAepf,EAAKinC,WAAatyC,KAAKuyC,mBAAmBlnC,GAE7Gga,EAASha,EAAKnQ,cAAcG,YAC5BuV,EAAQvF,aAAgBga,EAAO4T,YAAc5T,EAAOjS,iBAAiB/H,GAAQ,KAC7EmnC,EAAcnnC,aAAgBga,EAAO4T,YAAc5T,EAAOjS,iBAAiB/H,EAAM,WAAa,KAC9FonC,EAAapnC,aAAgBga,EAAO4T,YAAc5T,EAAOjS,iBAAiB/H,EAAM,UAAY,KAE5FrL,KAAK4vC,mBAAqBvkC,GAAQ6mC,aAAiB7sB,EAAO4T,cAC1Dj5B,KAAK0yC,uBAAyBR,GAG9BA,aAAiB7sB,EAAOstB,iBACxBC,EAAuBV,GAM3B,IAHA,IAAIjC,GAAW,EAAIR,EAAmBoD,mBAAmBjiC,EAAO5Q,KAAKgwC,mBACjE8C,GAAgB,EAAIrD,EAAmBsD,sBAAsB1nC,EAAMmnC,EAAaxyC,KAAKgwC,mBAEhFvI,EAAQp8B,EAAKwpB,WAAY4S,EAAOA,EAAQA,EAAM1O,YAC/C0O,EAAM0K,WAAaC,KAAKY,eAAmC,WAAnBvL,EAAMvO,UAEjDuO,EAAM1wB,aApNA,4BAoN0E,mBAAhC/W,KAAKutB,QAAQ0lB,gBAE7DjzC,KAAKutB,QAAQ0lB,eAAexL,KACpBznC,KAAK8vC,YAAiC,UAAnBrI,EAAMvO,UAC1BgZ,EAAMluB,YAAYhkB,KAAK20B,UAAU8S,IAK7C,IAAIyL,GAAe,EAAIzD,EAAmBsD,sBAAsB1nC,EAAMonC,EAAYzyC,KAAKgwC,mBAGvF,IAFA,EAAIP,EAAmB0D,aAAalD,EAAUjwC,KAAKgwC,mBAE/C3kC,aAAgBga,EAAO4T,aAAeiZ,aAAiB7sB,EAAO4T,YAc9D,OAbIuZ,GACAxyC,KAAKozC,gBAAgBC,EAAoBhoC,EAAM6mC,EAAOM,EAAaM,EAAeQ,IAElFb,GACAzyC,KAAKozC,gBAAgBC,EAAoBhoC,EAAM6mC,EAAOO,EAAYS,EAAcK,KAEhF3iC,IAAS5Q,KAAK8vC,YAAgBzkC,aAAgBkmC,oBAC9C,EAAIpgC,EAAMqI,eAAe5I,EAAOshC,GAEpClyC,KAAKozC,gBAAgBlB,GACE,IAAnB7mC,EAAKmoC,WAAuC,IAApBnoC,EAAKooC,YAC7BzzC,KAAK6vC,iBAAiBlxC,KAAK,CAACuzC,EAAO7mC,EAAKooC,WAAYpoC,EAAKmoC,YAErDnoC,EAAK6tB,UACT,IAAK,SACIl5B,KAAK8vC,YACN4D,EAAoBroC,EAAM6mC,GAE9B,MACJ,IAAK,WACL,IAAK,SACDA,EAAMv0C,MAAQ0N,EAAK1N,MACnB,MACJ,IAAK,QACG0N,EAAKmoB,SAEL0e,EAAMx5B,aAAa,WAAW,GAK9C,OAAOw5B,MAIRpa,EAlQmC,GAqQ1C8Y,EAAgB,SAAuBJ,EAAOxkC,GAE9C,OAAQwkC,EAAMuB,SAAWj0C,MAAMsnB,KAAKorB,EAAMuB,UAAY,IAAI1C,QAAO,SAAU4C,GACvE,OAAOA,EAAK/hC,OAASyjC,QAAQC,kBAC9B72B,KAAI,SAAUk1B,GAGb,IAFA,IAAI/4B,GAAM,EAAI9H,EAAY0I,sBAAsBm4B,EAAKrhC,MAAMgJ,iBAAiB,QACxEm3B,EAAU,GACLp3C,EAAI,EAAGA,EAAIuf,EAAIrf,OAAQF,IAC5B,GAAsB,QAAlBuf,EAAIvf,GAAGqjB,QAAoB9D,EAAIvf,EAAI,IAA4B,WAAtBuf,EAAIvf,EAAI,GAAGqjB,OAAqB,CACzE,IAAI/b,EAAI+K,EAAS8X,cAAc,KAC/B7iB,EAAEwvC,KAAOv3B,EAAIvf,GAAGsjB,KAAK,GACjBjR,EAASC,MACTD,EAASC,KAAK+X,YAAY/iB,GAG9B,IAAI4P,EAAO,CACPqI,IAAKjY,EAAEwvC,KACPoD,OAAQ36B,EAAIvf,EAAI,GAAGsjB,KAAK,IAE5B8zB,EAAQpyC,KAAKkS,GAIrB,MAAO,CAGHkgC,QAASA,EAAQ1B,QAAO,SAAUx+B,GAC9B,MAAQ,SAASwN,KAAKxN,EAAKgjC,WAG/B3C,SAAUe,EAAKrhC,UAEpBy+B,QAAO,SAAUx+B,GAChB,OAAOA,EAAKkgC,QAAQl3C,WAIxB82C,EAAgC,SAAuChuB,EAAMmxB,GAC7E,IAAIC,EAAM/nC,SAASgoC,eAAeC,mBAAmB,IACjDC,EAAOloC,SAAS8X,cAAc,QAElCowB,EAAKzD,KAAOqD,EACZ,IAAIljC,EAAQ5E,SAAS8X,cAAc,SAUnC,OARAlT,EAAMygC,YAAc1uB,EAChBoxB,EAAII,MACJJ,EAAII,KAAKnwB,YAAYkwB,GAErBH,EAAI9nC,MACJ8nC,EAAI9nC,KAAK+X,YAAYpT,GAGlBA,EAAM4/B,MAAQI,EAAchgC,EAAM4/B,MAAOuD,GAAO,IASvDL,EAAsB,SAA6B92C,EAAQw3C,GAC3D,IACI,GAAIA,EAAc,CACdA,EAAat4C,MAAQc,EAAOd,MAC5Bs4C,EAAar4C,OAASa,EAAOb,OAC7B,IAAIwoB,EAAM3nB,EAAO4nB,WAAW,MACxB6vB,EAAYD,EAAa5vB,WAAW,MACpCD,EACA8vB,EAAUC,aAAa/vB,EAAIuB,aAAa,EAAG,EAAGlpB,EAAOd,MAAOc,EAAOb,QAAS,EAAG,GAE/Es4C,EAAU5vB,UAAU7nB,EAAQ,EAAG,IAGzC,MAAO+nB,MAGT0uB,EAAsB,SAA6BhoC,EAAM6mC,EAAOthC,EAAO2jC,EAAcC,GACrF,GAAK5jC,GAAUA,EAAM6jC,SAA6B,SAAlB7jC,EAAM6jC,SAAwC,qBAAlB7jC,EAAM6jC,SAAoD,SAAlB7jC,EAAMyC,QAA1G,CAIA,IAAIqhC,EAA2BxC,EAAMh3C,cAAc4oB,cAAc,4BAGjE,IAFA,EAAI3S,EAAMqI,eAAe5I,EAAO8jC,GAE5BH,EAEA,IADA,IAAIxrB,EAAMwrB,EAAa16C,OACdF,EAAI,EAAGA,EAAIovB,EAAKpvB,IAAK,CAC1B,IAAI+f,EAAO66B,EAAa56C,GACxB,OAAQ+f,EAAKxJ,MACT,KAAKu/B,EAAmBkF,yBAAyBC,MAC7C,IAAI57B,EAAMk5B,EAAMh3C,cAAc4oB,cAAc,OAC5C9K,EAAIE,KAAM,EAAI9H,EAAY0I,sBAAsB,OAASJ,EAAK/b,MAAQ,KAAK,GAAGsf,KAAK,GACnFjE,EAAIpI,MAAMqE,QAAU,IACpBy/B,EAAyB1wB,YAAYhL,GACrC,MACJ,KAAKy2B,EAAmBkF,yBAAyBE,KAC7CH,EAAyB1wB,YAAYkuB,EAAMh3C,cAAcuvB,eAAe/Q,EAAK/b,SAc7F,OARA+2C,EAAyBI,UAAYC,EAAmC,IAAMC,EAC9E9C,EAAM4C,WAAaN,IAAclB,EAAgB,IAAMyB,EAAmC,IAAMC,EAC5FR,IAAclB,EACdpB,EAAM+C,aAAaP,EAA0BxC,EAAMrd,YAEnDqd,EAAMluB,YAAY0wB,GAGfA,IAIPpB,EAAgB,UAChBC,EAAe,SACfwB,EAAmC,wCACnCC,EAAkC,uCAIlCpC,EAAyB,SAAgC3mC,GACzDipC,EAAajpC,EAAM,IAAM8oC,EAAmCzB,EAAzC,+EAAsG0B,EAAkCzB,EAH/H,qEAM5B2B,EAAe,SAAsBjpC,EAAMkpC,GAC3C,IAAIvkC,EAAQ3E,EAAK/Q,cAAc4oB,cAAc,SAC7ClT,EAAMwkC,UAAYD,EAClBlpC,EAAK+X,YAAYpT,IAGjBykC,EAAW,SAAkB35C,GAC7B,IAAImF,EAAQjD,EAAelC,EAAM,GAC7Bd,EAAUiG,EAAM,GAChB/D,EAAI+D,EAAM,GACV9D,EAAI8D,EAAM,GAEdjG,EAAQ64C,WAAa32C,EACrBlC,EAAQ44C,UAAYz2C,GAGpB00C,EAAoB,WACpB,OAAOz0C,KAAKC,KAAKw0B,KAAKC,MAAwB,IAAhB10B,KAAKs4C,UAAqBjoB,SAAS,KAGjEkoB,EAAkB,oCAElB3D,EAA2B,SAAkCvmC,EAAMkiB,GACnE,IACI,OAAOpyB,QAAQ2pB,QAAQzZ,EAAKmqC,cAAcxpC,SAASE,iBACrD,MAAOyY,GACL,OAAO4I,EAAQhxB,OAAQ,EAAIgzC,EAAOpZ,OAAO9qB,EAAK6N,IAAKqU,GAAS1H,MAAK,SAAU4vB,GACvE,IAAIt1C,EAAQs1C,EAAKt1C,MAAMo1C,GACvB,OAAKp1C,EAIe,WAAbA,EAAM,GAAkBklB,OAAOqwB,KAAKC,mBAAmBx1C,EAAM,KAAOw1C,mBAAmBx1C,EAAM,IAHzFhF,QAAQC,YAIpByqB,MAAK,SAAU4vB,GACd,OAAOG,EAAsBvqC,EAAKnQ,eAAe,EAAIX,EAAQsB,aAAawP,EAAM,EAAG,IAAIwa,MAAK,SAAUgwB,GAClG,IACIC,EADcD,EAAqBL,cACPxpC,SAEhC8pC,EAAchf,OACdgf,EAAcC,MAAMN,GACpB,IAAIO,EAAaC,EAAaJ,GAAsBhwB,MAAK,WACrD,OAAOiwB,EAAc5pC,mBAIzB,OADA4pC,EAAcI,QACPF,QAEV76C,QAAQC,WAIjBw6C,EAAwB,SAA+B16C,EAAesQ,GACtE,IAAIqqC,EAAuB36C,EAAc4oB,cAAc,UAYvD,OAVA+xB,EAAqBf,UAAY,wBACjCe,EAAqBjlC,MAAMuF,WAAa,SACxC0/B,EAAqBjlC,MAAMvB,SAAW,QACtCwmC,EAAqBjlC,MAAM5U,KAAO,WAClC65C,EAAqBjlC,MAAM3U,IAAM,MACjC45C,EAAqBjlC,MAAMgD,OAAS,IACpCiiC,EAAqB/5C,MAAQ0P,EAAO1P,MAAMuxB,WAC1CwoB,EAAqB95C,OAASyP,EAAOzP,OAAOsxB,WAC5CwoB,EAAqBM,UAAY,KACjCN,EAAqBn9B,aApcF,0BAociC,QAC/Cxd,EAAc+Q,MAInB/Q,EAAc+Q,KAAK+X,YAAY6xB,GAExB16C,QAAQ2pB,QAAQ+wB,IALZ16C,QAAQC,OAA+G,KAQlI66C,EAAe,SAAsBJ,GACrC,IAAI3d,EAAc2d,EAAqBL,cACnCM,EAAgB5d,EAAYlsB,SAEhC,OAAO,IAAI7Q,SAAQ,SAAU2pB,EAAS1pB,GAClC88B,EAAYnT,OAAS8wB,EAAqB9wB,OAAS+wB,EAAcM,mBAAqB,WAClF,IAAIC,EAAWC,aAAY,WACnBR,EAAc7pC,KAAK+G,WAAWnZ,OAAS,GAAkC,aAA7Bi8C,EAAcS,aAC1DC,cAAcH,GACdvxB,EAAQ+wB,MAEb,SAiDXY,GA5Cch5C,EAAQy6B,YAAc,SAAqBh9B,EAAesQ,EAAQokC,EAAkBriB,EAASxyB,EAAQ40C,GACnH,IAAI9X,EAAS,IAAIC,EAAe8X,EAAkBriB,EAASxyB,GAAQ,EAAO40C,GACtEr0C,EAAUJ,EAAcG,YAAYE,YACpCC,EAAUN,EAAcG,YAAYI,YAExC,OAAOm6C,EAAsB16C,EAAesQ,GAAQqa,MAAK,SAAUgwB,GAC/D,IAAI3d,EAAc2d,EAAqBL,cACnCM,EAAgB5d,EAAYlsB,SAM5BgqC,EAAaC,EAAaJ,GAAsBhwB,MAAK,WACrDgS,EAAOgY,iBAAiB1xB,QAAQk3B,GAChCnd,EAAYwe,SAASlrC,EAAOxP,KAAMwP,EAAOvP,MACrC,sBAAsBoiB,KAAKs4B,UAAUC,YAAe1e,EAAY18B,UAAYgQ,EAAOvP,KAAOi8B,EAAY58B,UAAYkQ,EAAOxP,OACzH85C,EAAc5pC,gBAAgB0E,MAAM3U,KAAOuP,EAAOvP,IAAM,KACxD65C,EAAc5pC,gBAAgB0E,MAAM5U,MAAQwP,EAAOxP,KAAO,KAC1D85C,EAAc5pC,gBAAgB0E,MAAMvB,SAAW,YAGnD,IAAIiZ,EAASntB,QAAQ2pB,QAAQ,CAAC+wB,EAAsBhe,EAAO6a,uBAAwB7a,EAAOhlB,iBAEtFgkC,EAAUtpB,EAAQspB,QAEtB,OAAOhf,EAAO6a,kCAAkCxa,EAAYe,aAAepB,EAAO6a,kCAAkCx3C,EAAcG,YAAY49B,aAAepB,EAAO6a,kCAAkCzZ,YAAiC,mBAAZ4d,EAAyB17C,QAAQ2pB,UAAUe,MAAK,WACvQ,OAAOgxB,EAAQf,MAChBjwB,MAAK,WACJ,OAAOyC,KACNA,EAASntB,QAAQC,OAA8H,OAUxJ,OAPA06C,EAAchf,OACdgf,EAAcC,MAAMU,EAAiBzqC,SAAS8qC,SAAW,iBAlMxC,SAA4B57C,EAAe4B,EAAGC,IAC/D7B,EAAcG,aAAgByB,IAAM5B,EAAcG,YAAYE,aAAewB,IAAM7B,EAAcG,YAAYI,aAC7GP,EAAcG,YAAYq7C,SAAS55C,EAAGC,GAkMtCg6C,CAAmBnH,EAAiB10C,cAAeI,EAASE,GAC5Ds6C,EAAclhB,aAAakhB,EAAckB,UAAUnf,EAAO3rB,iBAAkB4pC,EAAc5pC,iBAC1F4pC,EAAcI,QAEPF,MAIQ,SAA0Bc,GAC7C,IAAI/uB,EAAM,GAsBV,OArBI+uB,IACA/uB,GAAO,aACH+uB,EAAQG,OACRlvB,GAAO+uB,EAAQG,MAGfH,EAAQI,iBACRnvB,GAAO+uB,EAAQI,gBAGfJ,EAAQK,WACRpvB,GAAO,IAAM+uB,EAAQK,SAAW,KAGhCL,EAAQM,WACRrvB,GAAO,IAAM+uB,EAAQM,SAAW,KAGpCrvB,GAAO,KAGJA,K,kCChkBXvuB,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ45C,mBAAgB/4C,EAExB,IAQgC9D,EAR5BuE,EAAe,WAAc,SAASC,EAAiBtF,EAAQuF,GAAS,IAAK,IAAItF,EAAI,EAAGA,EAAIsF,EAAMpF,OAAQF,IAAK,CAAE,IAAIuF,EAAaD,EAAMtF,GAAIuF,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAM7F,OAAOkE,eAAehE,EAAQwF,EAAWnF,IAAKmF,IAAiB,OAAO,SAAUI,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYP,EAAiBM,EAAYtF,UAAWuF,GAAiBC,GAAaR,EAAiBM,EAAaE,GAAqBF,GAA7gB,GAEf00B,EAAW,EAAQ,MAEnBC,GAI4Bz5B,EAJOw5B,IAIcx5B,EAAIC,WAAaD,EAAM,CAAEE,QAASF,GAFnF+0C,EAAS,EAAQ,MAIrB,SAASxvC,EAAgBD,EAAUR,GAAe,KAAMQ,aAAoBR,GAAgB,MAAM,IAAIR,UAAU,qCAEhH,IAAIw4C,EAAiB,WACjB,SAASA,EAAe/pB,EAASxyB,EAAQsqB,GACrCtlB,EAAgBC,KAAMs3C,GAEtBt3C,KAAKutB,QAAUA,EACfvtB,KAAKu3C,QAAUlyB,EACfrlB,KAAK0+B,OAAS1+B,KAAKw3C,UAAUnyB,EAAOoyB,SAAShH,MAC7CzwC,KAAK2xC,MAAQ,GACb3xC,KAAKjF,OAASA,EACdiF,KAAK03C,OAAS,EAgLlB,OA7KA34C,EAAau4C,EAAgB,CAAC,CAC1Bv9C,IAAK,YACL4D,MAAO,SAAmBub,GACtB,IAAInG,EAAQ/S,KAEZ,GAAIA,KAAK23C,mBAAmBz+B,GACxB,OAAOA,EAGX,IAAK0+B,EAAM1+B,IAAQ+a,EAAUv5B,QAAQm9C,oBAAqB,CACtD,IAAgC,IAA5B73C,KAAKutB,QAAQnxB,YAAuB07C,EAAc5+B,IAAQlZ,KAAK+3C,aAAa7+B,GAC5E,OAAOlZ,KAAKg4C,SAAS9+B,EAAKA,GAAK,GAC5B,IAAKlZ,KAAK+3C,aAAa7+B,GAAM,CAChC,GAAkC,iBAAvBlZ,KAAKutB,QAAQhxB,MAIpB,OAHAyD,KAAK2xC,MAAMz4B,IAAO,EAAIq2B,EAAOpZ,OAAOjd,EAAKlZ,KAAKutB,SAAS1H,MAAK,SAAU3M,GAClE,OAAO++B,EAAW/+B,EAAKnG,EAAMwa,QAAQjxB,cAAgB,MAElD4c,EACJ,IAA6B,IAAzBlZ,KAAKutB,QAAQ1wB,SAAoBo3B,EAAUv5B,QAAQw9C,oBAC1D,OAAOl4C,KAAKg4C,SAAS9+B,EAAKA,GAAK,OAKhD,CACCnf,IAAK,cACL4D,MAAO,SAAqBub,GACxB,IAAI+U,EAASjuB,KAEb,OAAI83C,EAAc5+B,GACP++B,EAAW/+B,EAAKlZ,KAAKutB,QAAQjxB,cAAgB,GAEpD0D,KAAK23C,mBAAmBz+B,GACjBlZ,KAAK2xC,MAAMz4B,GAEjBlZ,KAAK+3C,aAAa7+B,IAAsC,iBAAvBlZ,KAAKutB,QAAQhxB,MAM5CyD,KAAKm4C,SAASj/B,GALVlZ,KAAK2xC,MAAMz4B,IAAO,EAAIq2B,EAAOpZ,OAAOjd,EAAKlZ,KAAKutB,SAAS1H,MAAK,SAAU3M,GACzE,OAAO++B,EAAW/+B,EAAK+U,EAAOV,QAAQjxB,cAAgB,QAMnE,CACCvC,IAAK,WACL4D,MAAO,SAAkBub,GACrB,IAAImW,EAASrvB,KAoCb,OAlCAA,KAAK2xC,MAAMz4B,GAAO,IAAI/d,SAAQ,SAAU2pB,EAAS1pB,GAC7C,IAAIk7B,EAAM,IAAIpQ,eAoBd,GAnBAoQ,EAAI8f,mBAAqB,WACrB,GAAuB,IAAnB9f,EAAIigB,WACJ,GAAmB,MAAfjgB,EAAIE,OACJp7B,EAAO,yBAA2B8d,EAAIvY,UAAU,EAAG,KAAO,qBAAuB21B,EAAIE,YAClF,CACH,IAAIE,EAAS,IAAIC,WACjBD,EAAO1f,iBAAiB,QAAQ,WAE5B,IAAIsR,EAASoO,EAAOpO,OACpBxD,EAAQwD,MACT,GACHoO,EAAO1f,iBAAiB,SAAS,SAAU2N,GACvC,OAAOvpB,EAAOupB,MACf,GACH+R,EAAOE,cAAcN,EAAIG,YAIrCH,EAAInQ,aAAe,OACfkJ,EAAO9B,QAAQjxB,aAAc,CAC7B,IAAIy6B,EAAU1H,EAAO9B,QAAQjxB,aAC7Bg6B,EAAIS,QAAUA,EACdT,EAAIU,UAAY,WACZ,OAAO57B,EAAmH,KAGlIk7B,EAAIQ,KAAK,MAAO5d,GAAK,GACrBod,EAAIW,UACLpR,MAAK,SAAU3M,GACd,OAAO++B,EAAW/+B,EAAKmW,EAAO9B,QAAQjxB,cAAgB,MAGnD0D,KAAK2xC,MAAMz4B,KAEvB,CACCnf,IAAK,aACL4D,MAAO,SAAoB0N,GACvB,IAAItR,EAAMquB,OAAOpoB,KAAK03C,UAEtB,OADA13C,KAAK2xC,MAAM53C,GAAOoB,QAAQ2pB,QAAQzZ,GAC3BtR,IAEZ,CACCA,IAAK,qBACL4D,MAAO,SAA4B5D,GAC/B,YAAkC,IAApBiG,KAAK2xC,MAAM53C,KAE9B,CACCA,IAAK,WACL4D,MAAO,SAAkB5D,EAAKmf,EAAKrc,GAC/B,IAAIszB,EAASnwB,KAMb,IAAIo4C,EAAmB,SAA0BC,GAC7C,OAAO,IAAIl9C,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAI4d,EAAM,IAAIsL,MAiBd,GAhBAtL,EAAI+L,OAAS,WACT,OAAOD,EAAQ9L,IAGdq/B,IAAsBx7C,IACvBmc,EAAIiN,YAAc,aAGtBjN,EAAIgM,QAAU5pB,EACd4d,EAAIE,IAAMA,GACW,IAAjBF,EAAIiM,UAEJC,YAAW,WACPJ,EAAQ9L,KACT,KAEHmX,EAAO5C,QAAQjxB,aAAc,CAC7B,IAAIy6B,EAAU5G,EAAO5C,QAAQjxB,aAC7B4oB,YAAW,WACP,OAAO9pB,EAAmH,MAC3H27B,QAOf,OAFA/2B,KAAK2xC,MAAM53C,GAAOu+C,EAAoBp/B,KAAS0+B,EAAM1+B,GACrD+a,EAAUv5B,QAAQ69C,uBAAuBr/B,GAAK2M,KAAKuyB,GAAoBA,GAAiB,GACjFr+C,IAEZ,CACCA,IAAK,eACL4D,MAAO,SAAsB66C,GACzB,OAAOx4C,KAAKw3C,UAAUgB,KAASx4C,KAAK0+B,SAEzC,CACC3kC,IAAK,YACL4D,MAAO,SAAmB66C,GACtB,IAAIC,EAAOz4C,KAAK04C,QAAU14C,KAAK04C,MAAQ14C,KAAKu3C,QAAQvrC,SAAS8X,cAAc,MAG3E,OAFA20B,EAAKhI,KAAO+H,EACZC,EAAKhI,KAAOgI,EAAKhI,KACVgI,EAAKE,SAAWF,EAAKG,SAAWH,EAAKI,OAEjD,CACC9+C,IAAK,QACL4D,MAAO,WACH,IAAIm7C,EAAS94C,KAETynB,EAAOjuB,OAAOiuB,KAAKznB,KAAK2xC,OACxB/mB,EAASnD,EAAK1K,KAAI,SAAUgL,GAC5B,OAAO+wB,EAAOnH,MAAM5pB,GAAKhC,OAAM,SAAUpB,GAIrC,OAAO,WAGf,OAAOxpB,QAAQs9B,IAAI7N,GAAQ/E,MAAK,SAAUkzB,GAItC,OAAO,IAAI1B,EAAc5vB,EAAMsxB,UAKpCzB,EAzLU,GA4LrB75C,EAAQ/C,QAAU48C,EAElB,IAAID,EAAgB55C,EAAQ45C,cAAgB,WACxC,SAASA,EAAc5vB,EAAMuxB,GACzBj5C,EAAgBC,KAAMq3C,GAEtBr3C,KAAKi5C,MAAQxxB,EACbznB,KAAKk5C,WAAaF,EAWtB,OARAj6C,EAAas4C,EAAe,CAAC,CACzBt9C,IAAK,MACL4D,MAAO,SAAa5D,GAChB,IAAI+Y,EAAQ9S,KAAKi5C,MAAM9jC,QAAQpb,GAC/B,OAAkB,IAAX+Y,EAAe,KAAO9S,KAAKk5C,WAAWpmC,OAI9CukC,EAhBiC,GAmBxC8B,EAAa,yBACbC,EAAgB,2BAChBC,EAAa,mBAEbvB,EAAgB,SAAuB5+B,GACvC,OAAOmgC,EAAWh7B,KAAKnF,IAEvBo/B,EAAsB,SAA6Bp/B,GACnD,OAAOkgC,EAAc/6B,KAAKnF,IAG1B0+B,EAAQ,SAAe1+B,GACvB,MAAwC,QAAjCA,EAAI/I,QAAQ,GAAGzP,eAA2By4C,EAAW96B,KAAKnF,IAGjE++B,EAAa,SAAoB/+B,EAAK6d,GACtC,OAAO,IAAI57B,SAAQ,SAAU2pB,EAAS1pB,GAClC,IAAI4d,EAAM,IAAIsL,MACdtL,EAAI+L,OAAS,WACT,OAAOD,EAAQ9L,IAEnBA,EAAIgM,QAAU5pB,EACd4d,EAAIE,IAAMA,GACW,IAAjBF,EAAIiM,UAEJC,YAAW,WACPJ,EAAQ9L,KACT,KAEH+d,GACA7R,YAAW,WACP,OAAO9pB,EAA+F,MACvG27B,Q,kCClQfv9B,OAAOkE,eAAeD,EAAS,aAAc,CACzCE,OAAO,IAEXF,EAAQ67C,aAAe77C,EAAQs1C,qBAAuBt1C,EAAQ01C,YAAc11C,EAAQo1C,kBAAoBp1C,EAAQ87C,WAAa97C,EAAQk3C,8BAA2Br2C,EAEhK,IAAIV,EAAgb,SAAUC,EAAKlE,GAAK,GAAImE,MAAMC,QAAQF,GAAQ,OAAOA,EAAY,GAAIG,OAAOC,YAAYzE,OAAOqE,GAAQ,OAAxf,SAAuBA,EAAKlE,GAAK,IAAIuE,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAWC,OAAKC,EAAW,IAAM,IAAK,IAAiCC,EAA7BC,EAAKX,EAAIG,OAAOC,cAAmBE,GAAMI,EAAKC,EAAGC,QAAQC,QAAoBR,EAAKS,KAAKJ,EAAGZ,QAAYhE,GAAKuE,EAAKrE,SAAWF,GAA3DwE,GAAK,IAAoE,MAAOS,GAAOR,GAAK,EAAMC,EAAKO,EAAO,QAAU,KAAWT,GAAMK,EAAW,QAAGA,EAAW,SAAO,QAAU,GAAIJ,EAAI,MAAMC,GAAQ,OAAOH,EAA6HW,CAAchB,EAAKlE,GAAa,MAAM,IAAImF,UAAU,yDAEllB4T,EAAY,EAAQ,MAEpBd,EAAa,EAAQ,MAErB+iC,EAA2Bl3C,EAAQk3C,yBAA2B,CAC9DE,KAAM,EACND,MAAO,GAGP2E,EAAa97C,EAAQ87C,WAAa,CAClCC,OAAQ,EACRC,UAAW,EACXC,IAAK,EACLC,QAAS,EACTC,SAAU,EACVC,UAAW,EACXC,WAAY,GAoHZR,GAjHoB77C,EAAQo1C,kBAAoB,SAA2BjiC,EAAOgS,GAClF,IAAKhS,IAAUA,EAAMmpC,cAAuC,SAAvBnpC,EAAMmpC,aACvC,MAAO,GAOX,IAJA,IAAIC,EAAe,GACfC,EAAgBrpC,EAAMmpC,aAAa38B,MAAM,WACzC88B,EAAmBD,EAAcpgD,OAE5BF,EAAI,EAAGA,EAAIugD,EAAkBvgD,IAAK,CACvC,IAAIwgD,EAAwBF,EAActgD,GAAGyjB,MAAM,OAC/Cg9B,EAAyBx8C,EAAeu8C,EAAuB,GAC/DE,EAAcD,EAAuB,GACrCtT,EAAesT,EAAuB,GAE1CJ,EAAar7C,KAAK07C,GAClB,IAAIC,EAAU13B,EAAKqtB,SAASoK,GACvBC,IACDA,EAAU13B,EAAKqtB,SAASoK,GAAe,IAE3CC,EAAQ37C,KAAKyB,SAAS0mC,GAAgB,EAAG,KAG7C,OAAOkT,GAGOv8C,EAAQ01C,YAAc,SAAqB6G,EAAcp3B,GAEvE,IADA,IAAI23B,EAAcP,EAAangD,OACtBF,EAAI,EAAGA,EAAI4gD,EAAa5gD,IAC7BipB,EAAKqtB,SAAS+J,EAAargD,IAAI6gD,OAIZ/8C,EAAQs1C,qBAAuB,SAA8B1nC,EAAMuF,EAAOgS,GACjG,IAAKhS,IAAUA,EAAM6jC,SAA6B,SAAlB7jC,EAAM6jC,SAAwC,qBAAlB7jC,EAAM6jC,SAAoD,SAAlB7jC,EAAMyC,QACtG,OAAO,KAGX,IAAIonC,EAASnB,EAAa1oC,EAAM6jC,SAE5B1rB,EAAM0xB,EAAO5gD,OACb06C,EAAe,GACf57B,EAAI,GAGJ+hC,EAAmB9pC,EAAM8pC,iBAC7B,GAAIA,GAAyC,SAArBA,EAA6B,CACjD,IAAIC,EAAwBD,EAAiBt9B,MAAM,OAC/Cw9B,EAAyBh9C,EAAe+8C,EAAuB,GAC/DN,EAAcO,EAAuB,GACrCC,EAAiBD,EAAuB,GAExCN,EAAU13B,EAAKqtB,SAASoK,GACxBC,IACAA,EAAQA,EAAQzgD,OAAS,SAAyByE,IAAnBu8C,EAA+B,EAAIz6C,SAASy6C,EAAgB,KAKnG,IAAK,IAAIlhD,EAAI,EAAGA,EAAIovB,EAAKpvB,IAAK,CAC1B,IAAImhD,EAAQL,EAAO9gD,GACnB,OAAQmhD,EAAM5qC,MACV,KAAKqpC,EAAWC,OACZ7gC,GAAKmiC,EAAMn9C,OAAS,GACpB,MAEJ,KAAK47C,EAAWE,UACRpuC,aAAgB4tB,aAAe6hB,EAAMn9C,QACrCgb,GAAKtN,EAAKgO,aAAayhC,EAAMn9C,QAAU,IAE3C,MAEJ,KAAK47C,EAAWI,QACZ,IAAIoB,EAAWn4B,EAAKqtB,SAAS6K,EAAM7D,MAAQ,IACvC8D,IACApiC,GAAKqiC,EAAmB,CAACD,EAASA,EAASlhD,OAAS,IAAK,GAAIihD,EAAMjH,SAEvE,MAEJ,KAAK0F,EAAWK,SACZ,IAAIqB,EAAYr4B,EAAKqtB,SAAS6K,EAAM7D,MAAQ,IACxCgE,IACAtiC,GAAKqiC,EAAmBC,EAAWH,EAAMI,KAAMJ,EAAMjH,SAEzD,MAEJ,KAAK0F,EAAWM,UACZlhC,GAAKwiC,EAASvqC,GAAO,EAAMgS,EAAKstB,YAChCttB,EAAKstB,aACL,MAEJ,KAAKqJ,EAAWO,WACZl3B,EAAKstB,aACLv3B,GAAKwiC,EAASvqC,GAAO,EAAOgS,EAAKstB,YACjC,MAEJ,KAAKqJ,EAAWG,IACR/gC,IACA47B,EAAa51C,KAAK,CAAEuR,KAAMykC,EAAyBE,KAAMl3C,MAAOgb,IAChEA,EAAI,IAER47B,EAAa51C,KAAK,CAAEuR,KAAMykC,EAAyBC,MAAOj3C,MAAOm9C,EAAMn9C,OAAS,MAS5F,OAJIgb,GACA47B,EAAa51C,KAAK,CAAEuR,KAAMykC,EAAyBE,KAAMl3C,MAAOgb,IAG7D47B,GAGQ92C,EAAQ67C,aAAe,SAAsB7E,EAAS9C,GACrE,GAAIA,GAASA,EAAM8C,GACf,OAAO9C,EAAM8C,GAajB,IAVA,IAAIgG,EAAS,GACT1xB,EAAM0rB,EAAQ56C,OAEduhD,GAAW,EACXC,GAAY,EACZC,GAAa,EACbvzB,EAAM,GACNwzB,EAAe,GACft+B,EAAO,GAEFtjB,EAAI,EAAGA,EAAIovB,EAAKpvB,IAAK,CAC1B,IAAIykB,EAAIq2B,EAAQ+G,OAAO7hD,GAEvB,OAAQykB,GACJ,IAAK,IACL,IAAK,IACGi9B,EACAtzB,GAAO3J,GAEPg9B,GAAYA,EACPE,GAAeF,IAChBX,EAAO97C,KAAK,CAAEuR,KAAMqpC,EAAWC,OAAQ77C,MAAOoqB,IAC9CA,EAAM,KAGd,MAEJ,IAAK,KACGszB,GACAtzB,GAAO3J,EACPi9B,GAAY,GAEZA,GAAY,EAEhB,MAEJ,IAAK,IACGD,EACArzB,GAAO3J,GAEPk9B,GAAa,EACbC,EAAexzB,EACfA,EAAM,GACN9K,EAAO,IAEX,MAEJ,IAAK,IACD,GAAIm+B,EACArzB,GAAO3J,OACJ,GAAIk9B,EAAY,CAKnB,OAJIvzB,GACA9K,EAAKte,KAAKopB,GAGNwzB,GACJ,IAAK,OACGt+B,EAAKpjB,OAAS,GACd4gD,EAAO97C,KAAK,CAAEuR,KAAMqpC,EAAWE,UAAW97C,MAAOsf,EAAK,KAE1D,MAEJ,IAAK,UACD,GAAIA,EAAKpjB,OAAS,EAAG,CACjB,IAAIygD,EAAU,CACVpqC,KAAMqpC,EAAWI,QACjB1C,KAAMh6B,EAAK,IAEXA,EAAKpjB,OAAS,IACdygD,EAAQzG,OAAS52B,EAAK,IAE1Bw9B,EAAO97C,KAAK27C,GAEhB,MAEJ,IAAK,WACD,GAAIr9B,EAAKpjB,OAAS,EAAG,CACjB,IAAI4hD,EAAa,CACbvrC,KAAMqpC,EAAWK,SACjB3C,KAAMh6B,EAAK,IAEXA,EAAKpjB,OAAS,IACd4hD,EAAWP,KAAOj+B,EAAK,IAEvBA,EAAKpjB,OAAS,IACd4hD,EAAW5H,OAAS52B,EAAK,IAE7Bw9B,EAAO97C,KAAK88C,GAEhB,MAEJ,IAAK,MACGx+B,EAAKpjB,OAAS,GACd4gD,EAAO97C,KAAK,CAAEuR,KAAMqpC,EAAWG,IAAK/7C,MAAOsf,EAAK,KAK5Dq+B,GAAa,EACbvzB,EAAM,GAEV,MAEJ,IAAK,IACGqzB,EACArzB,GAAO3J,EACAk9B,IACPr+B,EAAKte,KAAKopB,GACVA,EAAM,IAEV,MAEJ,IAAK,IACL,IAAK,KACGqzB,EACArzB,GAAO3J,EACA2J,IACP2zB,EAAcjB,EAAQ1yB,GACtBA,EAAM,IAEV,MAEJ,QACIA,GAAO3J,EAGL,OAANA,IACAi9B,GAAY,GAYpB,OARItzB,GACA2zB,EAAcjB,EAAQ1yB,GAGtB4pB,IACAA,EAAM8C,GAAWgG,GAGdA,IAGPiB,EAAgB,SAAuBjB,EAAQkB,GAC/C,OAAQA,GACJ,IAAK,aACDlB,EAAO97C,KAAK,CAAEuR,KAAMqpC,EAAWM,YAC/B,MACJ,IAAK,cACDY,EAAO97C,KAAK,CAAEuR,KAAMqpC,EAAWO,eAKvCqB,EAAW,SAAkBvqC,EAAOgrC,EAAW1L,GAC/C,IAAI2L,EAASjrC,EAAMirC,OAASjrC,EAAMirC,OAAOz+B,MAAM,OAAS,CAAC,OAAQ,QAC7DwxB,EAAmB,EAAbsB,EAOV,OANItB,GAAOiN,EAAOhiD,SACd+0C,EAAMiN,EAAOhiD,OAAS,GAErB+hD,KACChN,EAECiN,EAAOjN,GAAK3rB,QAAQ,eAAgB,KAG3C+3B,EAAqB,SAA4BV,EAASY,EAAMrH,GAIhE,IAHA,IAAI9qB,EAAMuxB,EAAQzgD,OACdyuB,EAAS,GAEJ3uB,EAAI,EAAGA,EAAIovB,EAAKpvB,IACjBA,EAAI,IACJ2uB,GAAU4yB,GAAQ,IAEtB5yB,IAAU,EAAI5V,EAAUkX,mBAAmB0wB,EAAQ3gD,IAAI,EAAIiY,EAAWgN,oBAAoBi1B,GAAU,YAAY,GAGpH,OAAOvrB", "file": "chunks/chunk.vendors.html2canvas.js", "sourcesContent": ["'use strict';\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _Window = require('./Window');\n\nvar _Bounds = require('./Bounds');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar html2canvas = function html2canvas(element, conf) {\n    var config = conf || {};\n    var logger = new _Logger2.default(typeof config.logging === 'boolean' ? config.logging : true);\n    logger.log('html2canvas ' + \"$npm_package_version\");\n\n    if (process.env.NODE_ENV !== 'production' && typeof config.onrendered === 'function') {\n        logger.error('onrendered option is deprecated, html2canvas returns a Promise with the canvas as the value');\n    }\n\n    var ownerDocument = element.ownerDocument;\n    if (!ownerDocument) {\n        return Promise.reject('Provided element is not within a Document');\n    }\n    var defaultView = ownerDocument.defaultView;\n\n    var scrollX = defaultView.pageXOffset;\n    var scrollY = defaultView.pageYOffset;\n\n    var isDocument = element.tagName === 'HTML' || element.tagName === 'BODY';\n\n    var _ref = isDocument ? (0, _Bounds.parseDocumentSize)(ownerDocument) : (0, _Bounds.parseBounds)(element, scrollX, scrollY),\n        width = _ref.width,\n        height = _ref.height,\n        left = _ref.left,\n        top = _ref.top;\n\n    var defaultOptions = {\n        async: true,\n        allowTaint: false,\n        backgroundColor: '#ffffff',\n        imageTimeout: 15000,\n        logging: true,\n        proxy: null,\n        removeContainer: true,\n        foreignObjectRendering: false,\n        scale: defaultView.devicePixelRatio || 1,\n        target: new _CanvasRenderer2.default(config.canvas),\n        useCORS: false,\n        x: left,\n        y: top,\n        width: Math.ceil(width),\n        height: Math.ceil(height),\n        windowWidth: defaultView.innerWidth,\n        windowHeight: defaultView.innerHeight,\n        scrollX: defaultView.pageXOffset,\n        scrollY: defaultView.pageYOffset\n    };\n\n    var result = (0, _Window.renderElement)(element, _extends({}, defaultOptions, config), logger);\n\n    if (process.env.NODE_ENV !== 'production') {\n        return result.catch(function (e) {\n            logger.error(e);\n            throw e;\n        });\n    }\n    return result;\n};\n\nhtml2canvas.CanvasRenderer = _CanvasRenderer2.default;\n\nmodule.exports = html2canvas;", "'use strict';\n\n// http://dev.w3.org/csswg/css-color/\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar HEX3 = /^#([a-f0-9]{3})$/i;\nvar hex3 = function hex3(value) {\n    var match = value.match(HEX3);\n    if (match) {\n        return [parseInt(match[1][0] + match[1][0], 16), parseInt(match[1][1] + match[1][1], 16), parseInt(match[1][2] + match[1][2], 16), null];\n    }\n    return false;\n};\n\nvar HEX6 = /^#([a-f0-9]{6})$/i;\nvar hex6 = function hex6(value) {\n    var match = value.match(HEX6);\n    if (match) {\n        return [parseInt(match[1].substring(0, 2), 16), parseInt(match[1].substring(2, 4), 16), parseInt(match[1].substring(4, 6), 16), null];\n    }\n    return false;\n};\n\nvar RGB = /^rgb\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*\\)$/;\nvar rgb = function rgb(value) {\n    var match = value.match(RGB);\n    if (match) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), null];\n    }\n    return false;\n};\n\nvar RGBA = /^rgba\\(\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d{1,3})\\s*,\\s*(\\d?\\.?\\d+)\\s*\\)$/;\nvar rgba = function rgba(value) {\n    var match = value.match(RGBA);\n    if (match && match.length > 4) {\n        return [Number(match[1]), Number(match[2]), Number(match[3]), Number(match[4])];\n    }\n    return false;\n};\n\nvar fromArray = function fromArray(array) {\n    return [Math.min(array[0], 255), Math.min(array[1], 255), Math.min(array[2], 255), array.length > 3 ? array[3] : null];\n};\n\nvar namedColor = function namedColor(name) {\n    var color = NAMED_COLORS[name.toLowerCase()];\n    return color ? color : false;\n};\n\nvar Color = function () {\n    function Color(value) {\n        _classCallCheck(this, Color);\n\n        var _ref = Array.isArray(value) ? fromArray(value) : hex3(value) || rgb(value) || rgba(value) || namedColor(value) || hex6(value) || [0, 0, 0, null],\n            _ref2 = _slicedToArray(_ref, 4),\n            r = _ref2[0],\n            g = _ref2[1],\n            b = _ref2[2],\n            a = _ref2[3];\n\n        this.r = r;\n        this.g = g;\n        this.b = b;\n        this.a = a;\n    }\n\n    _createClass(Color, [{\n        key: 'isTransparent',\n        value: function isTransparent() {\n            return this.a === 0;\n        }\n    }, {\n        key: 'toString',\n        value: function toString() {\n            return this.a !== null && this.a !== 1 ? 'rgba(' + this.r + ',' + this.g + ',' + this.b + ',' + this.a + ')' : 'rgb(' + this.r + ',' + this.g + ',' + this.b + ')';\n        }\n    }]);\n\n    return Color;\n}();\n\nexports.default = Color;\n\n\nvar NAMED_COLORS = {\n    transparent: [0, 0, 0, 0],\n    aliceblue: [240, 248, 255, null],\n    antiquewhite: [250, 235, 215, null],\n    aqua: [0, 255, 255, null],\n    aquamarine: [127, 255, 212, null],\n    azure: [240, 255, 255, null],\n    beige: [245, 245, 220, null],\n    bisque: [255, 228, 196, null],\n    black: [0, 0, 0, null],\n    blanchedalmond: [255, 235, 205, null],\n    blue: [0, 0, 255, null],\n    blueviolet: [138, 43, 226, null],\n    brown: [165, 42, 42, null],\n    burlywood: [222, 184, 135, null],\n    cadetblue: [95, 158, 160, null],\n    chartreuse: [127, 255, 0, null],\n    chocolate: [210, 105, 30, null],\n    coral: [255, 127, 80, null],\n    cornflowerblue: [100, 149, 237, null],\n    cornsilk: [255, 248, 220, null],\n    crimson: [220, 20, 60, null],\n    cyan: [0, 255, 255, null],\n    darkblue: [0, 0, 139, null],\n    darkcyan: [0, 139, 139, null],\n    darkgoldenrod: [184, 134, 11, null],\n    darkgray: [169, 169, 169, null],\n    darkgreen: [0, 100, 0, null],\n    darkgrey: [169, 169, 169, null],\n    darkkhaki: [189, 183, 107, null],\n    darkmagenta: [139, 0, 139, null],\n    darkolivegreen: [85, 107, 47, null],\n    darkorange: [255, 140, 0, null],\n    darkorchid: [153, 50, 204, null],\n    darkred: [139, 0, 0, null],\n    darksalmon: [233, 150, 122, null],\n    darkseagreen: [143, 188, 143, null],\n    darkslateblue: [72, 61, 139, null],\n    darkslategray: [47, 79, 79, null],\n    darkslategrey: [47, 79, 79, null],\n    darkturquoise: [0, 206, 209, null],\n    darkviolet: [148, 0, 211, null],\n    deeppink: [255, 20, 147, null],\n    deepskyblue: [0, 191, 255, null],\n    dimgray: [105, 105, 105, null],\n    dimgrey: [105, 105, 105, null],\n    dodgerblue: [30, 144, 255, null],\n    firebrick: [178, 34, 34, null],\n    floralwhite: [255, 250, 240, null],\n    forestgreen: [34, 139, 34, null],\n    fuchsia: [255, 0, 255, null],\n    gainsboro: [220, 220, 220, null],\n    ghostwhite: [248, 248, 255, null],\n    gold: [255, 215, 0, null],\n    goldenrod: [218, 165, 32, null],\n    gray: [128, 128, 128, null],\n    green: [0, 128, 0, null],\n    greenyellow: [173, 255, 47, null],\n    grey: [128, 128, 128, null],\n    honeydew: [240, 255, 240, null],\n    hotpink: [255, 105, 180, null],\n    indianred: [205, 92, 92, null],\n    indigo: [75, 0, 130, null],\n    ivory: [255, 255, 240, null],\n    khaki: [240, 230, 140, null],\n    lavender: [230, 230, 250, null],\n    lavenderblush: [255, 240, 245, null],\n    lawngreen: [124, 252, 0, null],\n    lemonchiffon: [255, 250, 205, null],\n    lightblue: [173, 216, 230, null],\n    lightcoral: [240, 128, 128, null],\n    lightcyan: [224, 255, 255, null],\n    lightgoldenrodyellow: [250, 250, 210, null],\n    lightgray: [211, 211, 211, null],\n    lightgreen: [144, 238, 144, null],\n    lightgrey: [211, 211, 211, null],\n    lightpink: [255, 182, 193, null],\n    lightsalmon: [255, 160, 122, null],\n    lightseagreen: [32, 178, 170, null],\n    lightskyblue: [135, 206, 250, null],\n    lightslategray: [119, 136, 153, null],\n    lightslategrey: [119, 136, 153, null],\n    lightsteelblue: [176, 196, 222, null],\n    lightyellow: [255, 255, 224, null],\n    lime: [0, 255, 0, null],\n    limegreen: [50, 205, 50, null],\n    linen: [250, 240, 230, null],\n    magenta: [255, 0, 255, null],\n    maroon: [128, 0, 0, null],\n    mediumaquamarine: [102, 205, 170, null],\n    mediumblue: [0, 0, 205, null],\n    mediumorchid: [186, 85, 211, null],\n    mediumpurple: [147, 112, 219, null],\n    mediumseagreen: [60, 179, 113, null],\n    mediumslateblue: [123, 104, 238, null],\n    mediumspringgreen: [0, 250, 154, null],\n    mediumturquoise: [72, 209, 204, null],\n    mediumvioletred: [199, 21, 133, null],\n    midnightblue: [25, 25, 112, null],\n    mintcream: [245, 255, 250, null],\n    mistyrose: [255, 228, 225, null],\n    moccasin: [255, 228, 181, null],\n    navajowhite: [255, 222, 173, null],\n    navy: [0, 0, 128, null],\n    oldlace: [253, 245, 230, null],\n    olive: [128, 128, 0, null],\n    olivedrab: [107, 142, 35, null],\n    orange: [255, 165, 0, null],\n    orangered: [255, 69, 0, null],\n    orchid: [218, 112, 214, null],\n    palegoldenrod: [238, 232, 170, null],\n    palegreen: [152, 251, 152, null],\n    paleturquoise: [175, 238, 238, null],\n    palevioletred: [219, 112, 147, null],\n    papayawhip: [255, 239, 213, null],\n    peachpuff: [255, 218, 185, null],\n    peru: [205, 133, 63, null],\n    pink: [255, 192, 203, null],\n    plum: [221, 160, 221, null],\n    powderblue: [176, 224, 230, null],\n    purple: [128, 0, 128, null],\n    rebeccapurple: [102, 51, 153, null],\n    red: [255, 0, 0, null],\n    rosybrown: [188, 143, 143, null],\n    royalblue: [65, 105, 225, null],\n    saddlebrown: [139, 69, 19, null],\n    salmon: [250, 128, 114, null],\n    sandybrown: [244, 164, 96, null],\n    seagreen: [46, 139, 87, null],\n    seashell: [255, 245, 238, null],\n    sienna: [160, 82, 45, null],\n    silver: [192, 192, 192, null],\n    skyblue: [135, 206, 235, null],\n    slateblue: [106, 90, 205, null],\n    slategray: [112, 128, 144, null],\n    slategrey: [112, 128, 144, null],\n    snow: [255, 250, 250, null],\n    springgreen: [0, 255, 127, null],\n    steelblue: [70, 130, 180, null],\n    tan: [210, 180, 140, null],\n    teal: [0, 128, 128, null],\n    thistle: [216, 191, 216, null],\n    tomato: [255, 99, 71, null],\n    turquoise: [64, 224, 208, null],\n    violet: [238, 130, 238, null],\n    wheat: [245, 222, 179, null],\n    white: [255, 255, 255, null],\n    whitesmoke: [245, 245, 245, null],\n    yellow: [255, 255, 0, null],\n    yellowgreen: [154, 205, 50, null]\n};\n\nvar TRANSPARENT = exports.TRANSPARENT = new Color([0, 0, 0, 0]);", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBoundCurves = exports.calculatePaddingBoxPath = exports.calculateBorderBoxPath = exports.parsePathForBorder = exports.parseDocumentSize = exports.calculateContentBox = exports.calculatePaddingBox = exports.parseBounds = exports.Bounds = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _BezierCurve = require('./drawing/BezierCurve');\n\nvar _BezierCurve2 = _interopRequireDefault(_BezierCurve);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TOP = 0;\nvar RIGHT = 1;\nvar BOTTOM = 2;\nvar LEFT = 3;\n\nvar H = 0;\nvar V = 1;\n\nvar Bounds = exports.Bounds = function () {\n    function Bounds(x, y, w, h) {\n        _classCallCheck(this, Bounds);\n\n        this.left = x;\n        this.top = y;\n        this.width = w;\n        this.height = h;\n    }\n\n    _createClass(Bounds, null, [{\n        key: 'fromClientRect',\n        value: function fromClientRect(clientRect, scrollX, scrollY) {\n            return new Bounds(clientRect.left + scrollX, clientRect.top + scrollY, clientRect.width, clientRect.height);\n        }\n    }]);\n\n    return Bounds;\n}();\n\nvar parseBounds = exports.parseBounds = function parseBounds(node, scrollX, scrollY) {\n    return Bounds.fromClientRect(node.getBoundingClientRect(), scrollX, scrollY);\n};\n\nvar calculatePaddingBox = exports.calculatePaddingBox = function calculatePaddingBox(bounds, borders) {\n    return new Bounds(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth));\n};\n\nvar calculateContentBox = exports.calculateContentBox = function calculateContentBox(bounds, padding, borders) {\n    // TODO support percentage paddings\n    var paddingTop = padding[TOP].value;\n    var paddingRight = padding[RIGHT].value;\n    var paddingBottom = padding[BOTTOM].value;\n    var paddingLeft = padding[LEFT].value;\n\n    return new Bounds(bounds.left + paddingLeft + borders[LEFT].borderWidth, bounds.top + paddingTop + borders[TOP].borderWidth, bounds.width - (borders[RIGHT].borderWidth + borders[LEFT].borderWidth + paddingLeft + paddingRight), bounds.height - (borders[TOP].borderWidth + borders[BOTTOM].borderWidth + paddingTop + paddingBottom));\n};\n\nvar parseDocumentSize = exports.parseDocumentSize = function parseDocumentSize(document) {\n    var body = document.body;\n    var documentElement = document.documentElement;\n\n    if (!body || !documentElement) {\n        throw new Error(process.env.NODE_ENV !== 'production' ? 'Unable to get document size' : '');\n    }\n    var width = Math.max(Math.max(body.scrollWidth, documentElement.scrollWidth), Math.max(body.offsetWidth, documentElement.offsetWidth), Math.max(body.clientWidth, documentElement.clientWidth));\n\n    var height = Math.max(Math.max(body.scrollHeight, documentElement.scrollHeight), Math.max(body.offsetHeight, documentElement.offsetHeight), Math.max(body.clientHeight, documentElement.clientHeight));\n\n    return new Bounds(0, 0, width, height);\n};\n\nvar parsePathForBorder = exports.parsePathForBorder = function parsePathForBorder(curves, borderSide) {\n    switch (borderSide) {\n        case TOP:\n            return createPathFromCurves(curves.topLeftOuter, curves.topLeftInner, curves.topRightOuter, curves.topRightInner);\n        case RIGHT:\n            return createPathFromCurves(curves.topRightOuter, curves.topRightInner, curves.bottomRightOuter, curves.bottomRightInner);\n        case BOTTOM:\n            return createPathFromCurves(curves.bottomRightOuter, curves.bottomRightInner, curves.bottomLeftOuter, curves.bottomLeftInner);\n        case LEFT:\n        default:\n            return createPathFromCurves(curves.bottomLeftOuter, curves.bottomLeftInner, curves.topLeftOuter, curves.topLeftInner);\n    }\n};\n\nvar createPathFromCurves = function createPathFromCurves(outer1, inner1, outer2, inner2) {\n    var path = [];\n    if (outer1 instanceof _BezierCurve2.default) {\n        path.push(outer1.subdivide(0.5, false));\n    } else {\n        path.push(outer1);\n    }\n\n    if (outer2 instanceof _BezierCurve2.default) {\n        path.push(outer2.subdivide(0.5, true));\n    } else {\n        path.push(outer2);\n    }\n\n    if (inner2 instanceof _BezierCurve2.default) {\n        path.push(inner2.subdivide(0.5, true).reverse());\n    } else {\n        path.push(inner2);\n    }\n\n    if (inner1 instanceof _BezierCurve2.default) {\n        path.push(inner1.subdivide(0.5, false).reverse());\n    } else {\n        path.push(inner1);\n    }\n\n    return path;\n};\n\nvar calculateBorderBoxPath = exports.calculateBorderBoxPath = function calculateBorderBoxPath(curves) {\n    return [curves.topLeftOuter, curves.topRightOuter, curves.bottomRightOuter, curves.bottomLeftOuter];\n};\n\nvar calculatePaddingBoxPath = exports.calculatePaddingBoxPath = function calculatePaddingBoxPath(curves) {\n    return [curves.topLeftInner, curves.topRightInner, curves.bottomRightInner, curves.bottomLeftInner];\n};\n\nvar parseBoundCurves = exports.parseBoundCurves = function parseBoundCurves(bounds, borders, borderRadius) {\n    var tlh = borderRadius[CORNER.TOP_LEFT][H].getAbsoluteValue(bounds.width);\n    var tlv = borderRadius[CORNER.TOP_LEFT][V].getAbsoluteValue(bounds.height);\n    var trh = borderRadius[CORNER.TOP_RIGHT][H].getAbsoluteValue(bounds.width);\n    var trv = borderRadius[CORNER.TOP_RIGHT][V].getAbsoluteValue(bounds.height);\n    var brh = borderRadius[CORNER.BOTTOM_RIGHT][H].getAbsoluteValue(bounds.width);\n    var brv = borderRadius[CORNER.BOTTOM_RIGHT][V].getAbsoluteValue(bounds.height);\n    var blh = borderRadius[CORNER.BOTTOM_LEFT][H].getAbsoluteValue(bounds.width);\n    var blv = borderRadius[CORNER.BOTTOM_LEFT][V].getAbsoluteValue(bounds.height);\n\n    var factors = [];\n    factors.push((tlh + trh) / bounds.width);\n    factors.push((blh + brh) / bounds.width);\n    factors.push((tlv + blv) / bounds.height);\n    factors.push((trv + brv) / bounds.height);\n    var maxFactor = Math.max.apply(Math, factors);\n\n    if (maxFactor > 1) {\n        tlh /= maxFactor;\n        tlv /= maxFactor;\n        trh /= maxFactor;\n        trv /= maxFactor;\n        brh /= maxFactor;\n        brv /= maxFactor;\n        blh /= maxFactor;\n        blv /= maxFactor;\n    }\n\n    var topWidth = bounds.width - trh;\n    var rightHeight = bounds.height - brv;\n    var bottomWidth = bounds.width - brh;\n    var leftHeight = bounds.height - blv;\n\n    return {\n        topLeftOuter: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left, bounds.top, tlh, tlv, CORNER.TOP_LEFT) : new _Vector2.default(bounds.left, bounds.top),\n        topLeftInner: tlh > 0 || tlv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth, Math.max(0, tlh - borders[LEFT].borderWidth), Math.max(0, tlv - borders[TOP].borderWidth), CORNER.TOP_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        topRightOuter: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + topWidth, bounds.top, trh, trv, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top),\n        topRightInner: trh > 0 || trv > 0 ? getCurvePoints(bounds.left + Math.min(topWidth, bounds.width + borders[LEFT].borderWidth), bounds.top + borders[TOP].borderWidth, topWidth > bounds.width + borders[LEFT].borderWidth ? 0 : trh - borders[LEFT].borderWidth, trv - borders[TOP].borderWidth, CORNER.TOP_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + borders[TOP].borderWidth),\n        bottomRightOuter: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + bottomWidth, bounds.top + rightHeight, brh, brv, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width, bounds.top + bounds.height),\n        bottomRightInner: brh > 0 || brv > 0 ? getCurvePoints(bounds.left + Math.min(bottomWidth, bounds.width - borders[LEFT].borderWidth), bounds.top + Math.min(rightHeight, bounds.height + borders[TOP].borderWidth), Math.max(0, brh - borders[RIGHT].borderWidth), brv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_RIGHT) : new _Vector2.default(bounds.left + bounds.width - borders[RIGHT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth),\n        bottomLeftOuter: blh > 0 || blv > 0 ? getCurvePoints(bounds.left, bounds.top + leftHeight, blh, blv, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left, bounds.top + bounds.height),\n        bottomLeftInner: blh > 0 || blv > 0 ? getCurvePoints(bounds.left + borders[LEFT].borderWidth, bounds.top + leftHeight, Math.max(0, blh - borders[LEFT].borderWidth), blv - borders[BOTTOM].borderWidth, CORNER.BOTTOM_LEFT) : new _Vector2.default(bounds.left + borders[LEFT].borderWidth, bounds.top + bounds.height - borders[BOTTOM].borderWidth)\n    };\n};\n\nvar CORNER = {\n    TOP_LEFT: 0,\n    TOP_RIGHT: 1,\n    BOTTOM_RIGHT: 2,\n    BOTTOM_LEFT: 3\n};\n\nvar getCurvePoints = function getCurvePoints(x, y, r1, r2, position) {\n    var kappa = 4 * ((Math.sqrt(2) - 1) / 3);\n    var ox = r1 * kappa; // control point offset horizontal\n    var oy = r2 * kappa; // control point offset vertical\n    var xm = x + r1; // x-middle\n    var ym = y + r2; // y-middle\n\n    switch (position) {\n        case CORNER.TOP_LEFT:\n            return new _BezierCurve2.default(new _Vector2.default(x, ym), new _Vector2.default(x, ym - oy), new _Vector2.default(xm - ox, y), new _Vector2.default(xm, y));\n        case CORNER.TOP_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(x, y), new _Vector2.default(x + ox, y), new _Vector2.default(xm, ym - oy), new _Vector2.default(xm, ym));\n        case CORNER.BOTTOM_RIGHT:\n            return new _BezierCurve2.default(new _Vector2.default(xm, y), new _Vector2.default(xm, y + oy), new _Vector2.default(x + ox, ym), new _Vector2.default(x, ym));\n        case CORNER.BOTTOM_LEFT:\n        default:\n            return new _BezierCurve2.default(new _Vector2.default(xm, ym), new _Vector2.default(xm - ox, ym), new _Vector2.default(x, y + oy), new _Vector2.default(x, y));\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.calculateLengthFromValueWithUnit = exports.LENGTH_TYPE = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LENGTH_WITH_UNIT = /([\\d.]+)(px|r?em|%)/i;\n\nvar LENGTH_TYPE = exports.LENGTH_TYPE = {\n    PX: 0,\n    PERCENTAGE: 1\n};\n\nvar Length = function () {\n    function Length(value) {\n        _classCallCheck(this, Length);\n\n        this.type = value.substr(value.length - 1) === '%' ? LENGTH_TYPE.PERCENTAGE : LENGTH_TYPE.PX;\n        var parsedValue = parseFloat(value);\n        if (process.env.NODE_ENV !== 'production' && isNaN(parsedValue)) {\n            console.error('Invalid value given for Length: \"' + value + '\"');\n        }\n        this.value = isNaN(parsedValue) ? 0 : parsedValue;\n    }\n\n    _createClass(Length, [{\n        key: 'isPercentage',\n        value: function isPercentage() {\n            return this.type === LENGTH_TYPE.PERCENTAGE;\n        }\n    }, {\n        key: 'getAbsoluteValue',\n        value: function getAbsoluteValue(parentLength) {\n            return this.isPercentage() ? parentLength * (this.value / 100) : this.value;\n        }\n    }], [{\n        key: 'create',\n        value: function create(v) {\n            return new Length(v);\n        }\n    }]);\n\n    return Length;\n}();\n\nexports.default = Length;\n\n\nvar getRootFontSize = function getRootFontSize(container) {\n    var parent = container.parent;\n    return parent ? getRootFontSize(parent) : parseFloat(container.style.font.fontSize);\n};\n\nvar calculateLengthFromValueWithUnit = exports.calculateLengthFromValueWithUnit = function calculateLengthFromValueWithUnit(container, value, unit) {\n    switch (unit) {\n        case 'px':\n        case '%':\n            return new Length(value + unit);\n        case 'em':\n        case 'rem':\n            var length = new Length(value);\n            length.value *= unit === 'em' ? parseFloat(container.style.font.fontSize) : getRootFontSize(container);\n            return length;\n        default:\n            // TODO: handle correctly if unknown unit is used\n            return new Length('0');\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _borderRadius = require('./parsing/borderRadius');\n\nvar _display = require('./parsing/display');\n\nvar _float = require('./parsing/float');\n\nvar _font = require('./parsing/font');\n\nvar _letterSpacing = require('./parsing/letterSpacing');\n\nvar _lineBreak = require('./parsing/lineBreak');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _margin = require('./parsing/margin');\n\nvar _overflow = require('./parsing/overflow');\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nvar _padding = require('./parsing/padding');\n\nvar _position = require('./parsing/position');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _textShadow = require('./parsing/textShadow');\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _transform = require('./parsing/transform');\n\nvar _visibility = require('./parsing/visibility');\n\nvar _wordBreak = require('./parsing/word-break');\n\nvar _zIndex = require('./parsing/zIndex');\n\nvar _Bounds = require('./Bounds');\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar INPUT_TAGS = ['INPUT', 'TEXTAREA', 'SELECT'];\n\nvar NodeContainer = function () {\n    function NodeContainer(node, parent, resourceLoader, index) {\n        var _this = this;\n\n        _classCallCheck(this, NodeContainer);\n\n        this.parent = parent;\n        this.tagName = node.tagName;\n        this.index = index;\n        this.childNodes = [];\n        this.listItems = [];\n        if (typeof node.start === 'number') {\n            this.listStart = node.start;\n        }\n        var defaultView = node.ownerDocument.defaultView;\n        var scrollX = defaultView.pageXOffset;\n        var scrollY = defaultView.pageYOffset;\n        var style = defaultView.getComputedStyle(node, null);\n        var display = (0, _display.parseDisplay)(style.display);\n\n        var IS_INPUT = node.type === 'radio' || node.type === 'checkbox';\n\n        var position = (0, _position.parsePosition)(style.position);\n\n        this.style = {\n            background: IS_INPUT ? _Input.INPUT_BACKGROUND : (0, _background.parseBackground)(style, resourceLoader),\n            border: IS_INPUT ? _Input.INPUT_BORDERS : (0, _border.parseBorder)(style),\n            borderRadius: (node instanceof defaultView.HTMLInputElement || node instanceof HTMLInputElement) && IS_INPUT ? (0, _Input.getInputBorderRadius)(node) : (0, _borderRadius.parseBorderRadius)(style),\n            color: IS_INPUT ? _Input.INPUT_COLOR : new _Color2.default(style.color),\n            display: display,\n            float: (0, _float.parseCSSFloat)(style.float),\n            font: (0, _font.parseFont)(style),\n            letterSpacing: (0, _letterSpacing.parseLetterSpacing)(style.letterSpacing),\n            listStyle: display === _display.DISPLAY.LIST_ITEM ? (0, _listStyle.parseListStyle)(style) : null,\n            lineBreak: (0, _lineBreak.parseLineBreak)(style.lineBreak),\n            margin: (0, _margin.parseMargin)(style),\n            opacity: parseFloat(style.opacity),\n            overflow: INPUT_TAGS.indexOf(node.tagName) === -1 ? (0, _overflow.parseOverflow)(style.overflow) : _overflow.OVERFLOW.HIDDEN,\n            overflowWrap: (0, _overflowWrap.parseOverflowWrap)(style.overflowWrap ? style.overflowWrap : style.wordWrap),\n            padding: (0, _padding.parsePadding)(style),\n            position: position,\n            textDecoration: (0, _textDecoration.parseTextDecoration)(style),\n            textShadow: (0, _textShadow.parseTextShadow)(style.textShadow),\n            textTransform: (0, _textTransform.parseTextTransform)(style.textTransform),\n            transform: (0, _transform.parseTransform)(style),\n            visibility: (0, _visibility.parseVisibility)(style.visibility),\n            wordBreak: (0, _wordBreak.parseWordBreak)(style.wordBreak),\n            zIndex: (0, _zIndex.parseZIndex)(position !== _position.POSITION.STATIC ? style.zIndex : 'auto')\n        };\n\n        if (this.isTransformed()) {\n            // getBoundingClientRect provides values post-transform, we want them without the transformation\n            node.style.transform = 'matrix(1,0,0,1,0,0)';\n        }\n\n        if (display === _display.DISPLAY.LIST_ITEM) {\n            var listOwner = (0, _ListItem.getListOwner)(this);\n            if (listOwner) {\n                var listIndex = listOwner.listItems.length;\n                listOwner.listItems.push(this);\n                this.listIndex = node.hasAttribute('value') && typeof node.value === 'number' ? node.value : listIndex === 0 ? typeof listOwner.listStart === 'number' ? listOwner.listStart : 1 : listOwner.listItems[listIndex - 1].listIndex + 1;\n            }\n        }\n\n        // TODO move bound retrieval for all nodes to a later stage?\n        if (node.tagName === 'IMG') {\n            node.addEventListener('load', function () {\n                _this.bounds = (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n                _this.curvedBounds = (0, _Bounds.parseBoundCurves)(_this.bounds, _this.style.border, _this.style.borderRadius);\n            });\n        }\n        this.image = getImage(node, resourceLoader);\n        this.bounds = IS_INPUT ? (0, _Input.reformatInputBounds)((0, _Bounds.parseBounds)(node, scrollX, scrollY)) : (0, _Bounds.parseBounds)(node, scrollX, scrollY);\n        this.curvedBounds = (0, _Bounds.parseBoundCurves)(this.bounds, this.style.border, this.style.borderRadius);\n\n        if (process.env.NODE_ENV !== 'production') {\n            this.name = '' + node.tagName.toLowerCase() + (node.id ? '#' + node.id : '') + node.className.toString().split(' ').map(function (s) {\n                return s.length ? '.' + s : '';\n            }).join('');\n        }\n    }\n\n    _createClass(NodeContainer, [{\n        key: 'getClipPaths',\n        value: function getClipPaths() {\n            var parentClips = this.parent ? this.parent.getClipPaths() : [];\n            var isClipped = this.style.overflow !== _overflow.OVERFLOW.VISIBLE;\n\n            return isClipped ? parentClips.concat([(0, _Bounds.calculatePaddingBoxPath)(this.curvedBounds)]) : parentClips;\n        }\n    }, {\n        key: 'isInFlow',\n        value: function isInFlow() {\n            return this.isRootElement() && !this.isFloating() && !this.isAbsolutelyPositioned();\n        }\n    }, {\n        key: 'isVisible',\n        value: function isVisible() {\n            return !(0, _Util.contains)(this.style.display, _display.DISPLAY.NONE) && this.style.opacity > 0 && this.style.visibility === _visibility.VISIBILITY.VISIBLE;\n        }\n    }, {\n        key: 'isAbsolutelyPositioned',\n        value: function isAbsolutelyPositioned() {\n            return this.style.position !== _position.POSITION.STATIC && this.style.position !== _position.POSITION.RELATIVE;\n        }\n    }, {\n        key: 'isPositioned',\n        value: function isPositioned() {\n            return this.style.position !== _position.POSITION.STATIC;\n        }\n    }, {\n        key: 'isFloating',\n        value: function isFloating() {\n            return this.style.float !== _float.FLOAT.NONE;\n        }\n    }, {\n        key: 'isRootElement',\n        value: function isRootElement() {\n            return this.parent === null;\n        }\n    }, {\n        key: 'isTransformed',\n        value: function isTransformed() {\n            return this.style.transform !== null;\n        }\n    }, {\n        key: 'isPositionedWithZIndex',\n        value: function isPositionedWithZIndex() {\n            return this.isPositioned() && !this.style.zIndex.auto;\n        }\n    }, {\n        key: 'isInlineLevel',\n        value: function isInlineLevel() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_FLEX) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_GRID) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_LIST_ITEM) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }, {\n        key: 'isInlineBlockOrInlineTable',\n        value: function isInlineBlockOrInlineTable() {\n            return (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_BLOCK) || (0, _Util.contains)(this.style.display, _display.DISPLAY.INLINE_TABLE);\n        }\n    }]);\n\n    return NodeContainer;\n}();\n\nexports.default = NodeContainer;\n\n\nvar getImage = function getImage(node, resourceLoader) {\n    if (node instanceof node.ownerDocument.defaultView.SVGSVGElement || node instanceof SVGSVGElement) {\n        var bounds = (0, _Bounds.parseBounds)(node, 0, 0);\n        node.setAttribute('width', bounds.width + 'px');\n        node.setAttribute('height', bounds.height + 'px');\n        var s = new XMLSerializer();\n        return resourceLoader.loadImage('data:image/svg+xml,' + encodeURIComponent(s.serializeToString(node)));\n    }\n    switch (node.tagName) {\n        case 'IMG':\n            // $FlowFixMe\n            var img = node;\n            return resourceLoader.loadImage(img.currentSrc || img.src);\n        case 'CANVAS':\n            // $FlowFixMe\n            var canvas = node;\n            return resourceLoader.loadCanvas(canvas);\n        case 'IFRAME':\n            var iframeKey = node.getAttribute('data-html2canvas-internal-iframe-key');\n            if (iframeKey) {\n                return iframeKey;\n            }\n            break;\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar contains = exports.contains = function contains(bit, value) {\n    return (bit & value) !== 0;\n};\n\nvar distance = exports.distance = function distance(a, b) {\n    return Math.sqrt(a * a + b * b);\n};\n\nvar copyCSSStyles = exports.copyCSSStyles = function copyCSSStyles(style, target) {\n    // Edge does not provide value for cssText\n    for (var i = style.length - 1; i >= 0; i--) {\n        var property = style.item(i);\n        // Safari shows pseudoelements if content is set\n        if (property !== 'content') {\n            target.style.setProperty(property, style.getPropertyValue(property));\n        }\n    }\n    return target;\n};\n\nvar SMALL_IMAGE = exports.SMALL_IMAGE = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBackgroundImage = exports.parseBackground = exports.calculateBackgroundRepeatPath = exports.calculateBackgroundPosition = exports.calculateBackgroungPositioningArea = exports.calculateBackgroungPaintingArea = exports.calculateGradientBackgroundSize = exports.calculateBackgroundSize = exports.BACKGROUND_ORIGIN = exports.BACKGROUND_CLIP = exports.BACKGROUND_SIZE = exports.BACKGROUND_REPEAT = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Size = require('../drawing/Size');\n\nvar _Size2 = _interopRequireDefault(_Size);\n\nvar _Vector = require('../drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Bounds = require('../Bounds');\n\nvar _padding = require('./padding');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar BACKGROUND_REPEAT = exports.BACKGROUND_REPEAT = {\n    REPEAT: 0,\n    NO_REPEAT: 1,\n    REPEAT_X: 2,\n    REPEAT_Y: 3\n};\n\nvar BACKGROUND_SIZE = exports.BACKGROUND_SIZE = {\n    AUTO: 0,\n    CONTAIN: 1,\n    COVER: 2,\n    LENGTH: 3\n};\n\nvar BACKGROUND_CLIP = exports.BACKGROUND_CLIP = {\n    BORDER_BOX: 0,\n    PADDING_BOX: 1,\n    CONTENT_BOX: 2\n};\n\nvar BACKGROUND_ORIGIN = exports.BACKGROUND_ORIGIN = BACKGROUND_CLIP;\n\nvar AUTO = 'auto';\n\nvar BackgroundSize = function BackgroundSize(size) {\n    _classCallCheck(this, BackgroundSize);\n\n    switch (size) {\n        case 'contain':\n            this.size = BACKGROUND_SIZE.CONTAIN;\n            break;\n        case 'cover':\n            this.size = BACKGROUND_SIZE.COVER;\n            break;\n        case 'auto':\n            this.size = BACKGROUND_SIZE.AUTO;\n            break;\n        default:\n            this.value = new _Length2.default(size);\n    }\n};\n\nvar calculateBackgroundSize = exports.calculateBackgroundSize = function calculateBackgroundSize(backgroundImage, image, bounds) {\n    var width = 0;\n    var height = 0;\n    var size = backgroundImage.size;\n    if (size[0].size === BACKGROUND_SIZE.CONTAIN || size[0].size === BACKGROUND_SIZE.COVER) {\n        var targetRatio = bounds.width / bounds.height;\n        var currentRatio = image.width / image.height;\n        return targetRatio < currentRatio !== (size[0].size === BACKGROUND_SIZE.COVER) ? new _Size2.default(bounds.width, bounds.width / currentRatio) : new _Size2.default(bounds.height * currentRatio, bounds.height);\n    }\n\n    if (size[0].value) {\n        width = size[0].value.getAbsoluteValue(bounds.width);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO && size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = image.height;\n    } else if (size[1].size === BACKGROUND_SIZE.AUTO) {\n        height = width / image.width * image.height;\n    } else if (size[1].value) {\n        height = size[1].value.getAbsoluteValue(bounds.height);\n    }\n\n    if (size[0].size === BACKGROUND_SIZE.AUTO) {\n        width = height / image.height * image.width;\n    }\n\n    return new _Size2.default(width, height);\n};\n\nvar calculateGradientBackgroundSize = exports.calculateGradientBackgroundSize = function calculateGradientBackgroundSize(backgroundImage, bounds) {\n    var size = backgroundImage.size;\n    var width = size[0].value ? size[0].value.getAbsoluteValue(bounds.width) : bounds.width;\n    var height = size[1].value ? size[1].value.getAbsoluteValue(bounds.height) : size[0].value ? width : bounds.height;\n\n    return new _Size2.default(width, height);\n};\n\nvar AUTO_SIZE = new BackgroundSize(AUTO);\n\nvar calculateBackgroungPaintingArea = exports.calculateBackgroungPaintingArea = function calculateBackgroungPaintingArea(curves, clip) {\n    switch (clip) {\n        case BACKGROUND_CLIP.BORDER_BOX:\n            return (0, _Bounds.calculateBorderBoxPath)(curves);\n        case BACKGROUND_CLIP.PADDING_BOX:\n        default:\n            return (0, _Bounds.calculatePaddingBoxPath)(curves);\n    }\n};\n\nvar calculateBackgroungPositioningArea = exports.calculateBackgroungPositioningArea = function calculateBackgroungPositioningArea(backgroundOrigin, bounds, padding, border) {\n    var paddingBox = (0, _Bounds.calculatePaddingBox)(bounds, border);\n\n    switch (backgroundOrigin) {\n        case BACKGROUND_ORIGIN.BORDER_BOX:\n            return bounds;\n        case BACKGROUND_ORIGIN.CONTENT_BOX:\n            var paddingLeft = padding[_padding.PADDING_SIDES.LEFT].getAbsoluteValue(bounds.width);\n            var paddingRight = padding[_padding.PADDING_SIDES.RIGHT].getAbsoluteValue(bounds.width);\n            var paddingTop = padding[_padding.PADDING_SIDES.TOP].getAbsoluteValue(bounds.width);\n            var paddingBottom = padding[_padding.PADDING_SIDES.BOTTOM].getAbsoluteValue(bounds.width);\n            return new _Bounds.Bounds(paddingBox.left + paddingLeft, paddingBox.top + paddingTop, paddingBox.width - paddingLeft - paddingRight, paddingBox.height - paddingTop - paddingBottom);\n        case BACKGROUND_ORIGIN.PADDING_BOX:\n        default:\n            return paddingBox;\n    }\n};\n\nvar calculateBackgroundPosition = exports.calculateBackgroundPosition = function calculateBackgroundPosition(position, size, bounds) {\n    return new _Vector2.default(position[0].getAbsoluteValue(bounds.width - size.width), position[1].getAbsoluteValue(bounds.height - size.height));\n};\n\nvar calculateBackgroundRepeatPath = exports.calculateBackgroundRepeatPath = function calculateBackgroundRepeatPath(background, position, size, backgroundPositioningArea, bounds) {\n    var repeat = background.repeat;\n    switch (repeat) {\n        case BACKGROUND_REPEAT.REPEAT_X:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(size.height + backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(bounds.left), Math.round(size.height + backgroundPositioningArea.top + position.y))];\n        case BACKGROUND_REPEAT.REPEAT_Y:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(bounds.height + bounds.top))];\n        case BACKGROUND_REPEAT.NO_REPEAT:\n            return [new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x + size.width), Math.round(backgroundPositioningArea.top + position.y + size.height)), new _Vector2.default(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y + size.height))];\n        default:\n            return [new _Vector2.default(Math.round(bounds.left), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.top)), new _Vector2.default(Math.round(bounds.left + bounds.width), Math.round(bounds.height + bounds.top)), new _Vector2.default(Math.round(bounds.left), Math.round(bounds.height + bounds.top))];\n    }\n};\n\nvar parseBackground = exports.parseBackground = function parseBackground(style, resourceLoader) {\n    return {\n        backgroundColor: new _Color2.default(style.backgroundColor),\n        backgroundImage: parseBackgroundImages(style, resourceLoader),\n        backgroundClip: parseBackgroundClip(style.backgroundClip),\n        backgroundOrigin: parseBackgroundOrigin(style.backgroundOrigin)\n    };\n};\n\nvar parseBackgroundClip = function parseBackgroundClip(backgroundClip) {\n    switch (backgroundClip) {\n        case 'padding-box':\n            return BACKGROUND_CLIP.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_CLIP.CONTENT_BOX;\n    }\n    return BACKGROUND_CLIP.BORDER_BOX;\n};\n\nvar parseBackgroundOrigin = function parseBackgroundOrigin(backgroundOrigin) {\n    switch (backgroundOrigin) {\n        case 'padding-box':\n            return BACKGROUND_ORIGIN.PADDING_BOX;\n        case 'content-box':\n            return BACKGROUND_ORIGIN.CONTENT_BOX;\n    }\n    return BACKGROUND_ORIGIN.BORDER_BOX;\n};\n\nvar parseBackgroundRepeat = function parseBackgroundRepeat(backgroundRepeat) {\n    switch (backgroundRepeat.trim()) {\n        case 'no-repeat':\n            return BACKGROUND_REPEAT.NO_REPEAT;\n        case 'repeat-x':\n        case 'repeat no-repeat':\n            return BACKGROUND_REPEAT.REPEAT_X;\n        case 'repeat-y':\n        case 'no-repeat repeat':\n            return BACKGROUND_REPEAT.REPEAT_Y;\n        case 'repeat':\n            return BACKGROUND_REPEAT.REPEAT;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n        console.error('Invalid background-repeat value \"' + backgroundRepeat + '\"');\n    }\n\n    return BACKGROUND_REPEAT.REPEAT;\n};\n\nvar parseBackgroundImages = function parseBackgroundImages(style, resourceLoader) {\n    var sources = parseBackgroundImage(style.backgroundImage).map(function (backgroundImage) {\n        if (backgroundImage.method === 'url') {\n            var key = resourceLoader.loadImage(backgroundImage.args[0]);\n            backgroundImage.args = key ? [key] : [];\n        }\n        return backgroundImage;\n    });\n    var positions = style.backgroundPosition.split(',');\n    var repeats = style.backgroundRepeat.split(',');\n    var sizes = style.backgroundSize.split(',');\n\n    return sources.map(function (source, index) {\n        var size = (sizes[index] || AUTO).trim().split(' ').map(parseBackgroundSize);\n        var position = (positions[index] || AUTO).trim().split(' ').map(parseBackgoundPosition);\n\n        return {\n            source: source,\n            repeat: parseBackgroundRepeat(typeof repeats[index] === 'string' ? repeats[index] : repeats[0]),\n            size: size.length < 2 ? [size[0], AUTO_SIZE] : [size[0], size[1]],\n            position: position.length < 2 ? [position[0], position[0]] : [position[0], position[1]]\n        };\n    });\n};\n\nvar parseBackgroundSize = function parseBackgroundSize(size) {\n    return size === 'auto' ? AUTO_SIZE : new BackgroundSize(size);\n};\n\nvar parseBackgoundPosition = function parseBackgoundPosition(position) {\n    switch (position) {\n        case 'bottom':\n        case 'right':\n            return new _Length2.default('100%');\n        case 'left':\n        case 'top':\n            return new _Length2.default('0%');\n        case 'auto':\n            return new _Length2.default('0');\n    }\n    return new _Length2.default(position);\n};\n\nvar parseBackgroundImage = exports.parseBackgroundImage = function parseBackgroundImage(image) {\n    var whitespace = /^\\s$/;\n    var results = [];\n\n    var args = [];\n    var method = '';\n    var quote = null;\n    var definition = '';\n    var mode = 0;\n    var numParen = 0;\n\n    var appendResult = function appendResult() {\n        var prefix = '';\n        if (method) {\n            if (definition.substr(0, 1) === '\"') {\n                definition = definition.substr(1, definition.length - 2);\n            }\n\n            if (definition) {\n                args.push(definition.trim());\n            }\n\n            var prefix_i = method.indexOf('-', 1) + 1;\n            if (method.substr(0, 1) === '-' && prefix_i > 0) {\n                prefix = method.substr(0, prefix_i).toLowerCase();\n                method = method.substr(prefix_i);\n            }\n            method = method.toLowerCase();\n            if (method !== 'none') {\n                results.push({\n                    prefix: prefix,\n                    method: method,\n                    args: args\n                });\n            }\n        }\n        args = [];\n        method = definition = '';\n    };\n\n    image.split('').forEach(function (c) {\n        if (mode === 0 && whitespace.test(c)) {\n            return;\n        }\n        switch (c) {\n            case '\"':\n                if (!quote) {\n                    quote = c;\n                } else if (quote === c) {\n                    quote = null;\n                }\n                break;\n            case '(':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    mode = 1;\n                    return;\n                } else {\n                    numParen++;\n                }\n                break;\n            case ')':\n                if (quote) {\n                    break;\n                } else if (mode === 1) {\n                    if (numParen === 0) {\n                        mode = 0;\n                        appendResult();\n                        return;\n                    } else {\n                        numParen--;\n                    }\n                }\n                break;\n\n            case ',':\n                if (quote) {\n                    break;\n                } else if (mode === 0) {\n                    appendResult();\n                    return;\n                } else if (mode === 1) {\n                    if (numParen === 0 && !method.match(/^url$/i)) {\n                        args.push(definition.trim());\n                        definition = '';\n                        return;\n                    }\n                }\n                break;\n        }\n\n        if (mode === 0) {\n            method += c;\n        } else {\n            definition += c;\n        }\n    });\n\n    appendResult();\n    return results;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar PATH = exports.PATH = {\n    VECTOR: 0,\n    BEZIER_CURVE: 1,\n    CIRCLE: 2\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Vector = function Vector(x, y) {\n    _classCallCheck(this, Vector);\n\n    this.type = _Path.PATH.VECTOR;\n    this.x = x;\n    this.y = y;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Vector');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Vector');\n        }\n    }\n};\n\nexports.default = Vector;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseListStyle = exports.parseListStyleType = exports.LIST_STYLE_TYPE = exports.LIST_STYLE_POSITION = undefined;\n\nvar _background = require('./background');\n\nvar LIST_STYLE_POSITION = exports.LIST_STYLE_POSITION = {\n    INSIDE: 0,\n    OUTSIDE: 1\n};\n\nvar LIST_STYLE_TYPE = exports.LIST_STYLE_TYPE = {\n    NONE: -1,\n    DISC: 0,\n    CIRCLE: 1,\n    SQUARE: 2,\n    DECIMAL: 3,\n    CJK_DECIMAL: 4,\n    DECIMAL_LEADING_ZERO: 5,\n    LOWER_ROMAN: 6,\n    UPPER_ROMAN: 7,\n    LOWER_GREEK: 8,\n    LOWER_ALPHA: 9,\n    UPPER_ALPHA: 10,\n    ARABIC_INDIC: 11,\n    ARMENIAN: 12,\n    BENGALI: 13,\n    CAMBODIAN: 14,\n    <PERSON><PERSON><PERSON>_EARTHLY_BRANCH: 15,\n    <PERSON><PERSON><PERSON>_HEAVENLY_STEM: 16,\n    <PERSON><PERSON><PERSON>_IDEOGRAPHIC: 17,\n    DEVANAGARI: 18,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>_NUMERIC: 19,\n    GEORGIAN: 20,\n    GUJARATI: 21,\n    GURMUKHI: 22,\n    HEBREW: 22,\n    HIRAGANA: 23,\n    HIRAGANA_IROHA: 24,\n    JAPANESE_FORMAL: 25,\n    JAPANESE_INFORMAL: 26,\n    KANNADA: 27,\n    KATAKANA: 28,\n    KATAKANA_IROHA: 29,\n    KHMER: 30,\n    KOREAN_HANGUL_FORMAL: 31,\n    KOREAN_HANJA_FORMAL: 32,\n    KOREAN_HANJA_INFORMAL: 33,\n    LAO: 34,\n    LOWER_ARMENIAN: 35,\n    MALAYALAM: 36,\n    MONGOLIAN: 37,\n    MYANMAR: 38,\n    ORIYA: 39,\n    PERSIAN: 40,\n    SIMP_CHINESE_FORMAL: 41,\n    SIMP_CHINESE_INFORMAL: 42,\n    TAMIL: 43,\n    TELUGU: 44,\n    THAI: 45,\n    TIBETAN: 46,\n    TRAD_CHINESE_FORMAL: 47,\n    TRAD_CHINESE_INFORMAL: 48,\n    UPPER_ARMENIAN: 49,\n    DISCLOSURE_OPEN: 50,\n    DISCLOSURE_CLOSED: 51\n};\n\nvar parseListStyleType = exports.parseListStyleType = function parseListStyleType(type) {\n    switch (type) {\n        case 'disc':\n            return LIST_STYLE_TYPE.DISC;\n        case 'circle':\n            return LIST_STYLE_TYPE.CIRCLE;\n        case 'square':\n            return LIST_STYLE_TYPE.SQUARE;\n        case 'decimal':\n            return LIST_STYLE_TYPE.DECIMAL;\n        case 'cjk-decimal':\n            return LIST_STYLE_TYPE.CJK_DECIMAL;\n        case 'decimal-leading-zero':\n            return LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO;\n        case 'lower-roman':\n            return LIST_STYLE_TYPE.LOWER_ROMAN;\n        case 'upper-roman':\n            return LIST_STYLE_TYPE.UPPER_ROMAN;\n        case 'lower-greek':\n            return LIST_STYLE_TYPE.LOWER_GREEK;\n        case 'lower-alpha':\n            return LIST_STYLE_TYPE.LOWER_ALPHA;\n        case 'upper-alpha':\n            return LIST_STYLE_TYPE.UPPER_ALPHA;\n        case 'arabic-indic':\n            return LIST_STYLE_TYPE.ARABIC_INDIC;\n        case 'armenian':\n            return LIST_STYLE_TYPE.ARMENIAN;\n        case 'bengali':\n            return LIST_STYLE_TYPE.BENGALI;\n        case 'cambodian':\n            return LIST_STYLE_TYPE.CAMBODIAN;\n        case 'cjk-earthly-branch':\n            return LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH;\n        case 'cjk-heavenly-stem':\n            return LIST_STYLE_TYPE.CJK_HEAVENLY_STEM;\n        case 'cjk-ideographic':\n            return LIST_STYLE_TYPE.CJK_IDEOGRAPHIC;\n        case 'devanagari':\n            return LIST_STYLE_TYPE.DEVANAGARI;\n        case 'ethiopic-numeric':\n            return LIST_STYLE_TYPE.ETHIOPIC_NUMERIC;\n        case 'georgian':\n            return LIST_STYLE_TYPE.GEORGIAN;\n        case 'gujarati':\n            return LIST_STYLE_TYPE.GUJARATI;\n        case 'gurmukhi':\n            return LIST_STYLE_TYPE.GURMUKHI;\n        case 'hebrew':\n            return LIST_STYLE_TYPE.HEBREW;\n        case 'hiragana':\n            return LIST_STYLE_TYPE.HIRAGANA;\n        case 'hiragana-iroha':\n            return LIST_STYLE_TYPE.HIRAGANA_IROHA;\n        case 'japanese-formal':\n            return LIST_STYLE_TYPE.JAPANESE_FORMAL;\n        case 'japanese-informal':\n            return LIST_STYLE_TYPE.JAPANESE_INFORMAL;\n        case 'kannada':\n            return LIST_STYLE_TYPE.KANNADA;\n        case 'katakana':\n            return LIST_STYLE_TYPE.KATAKANA;\n        case 'katakana-iroha':\n            return LIST_STYLE_TYPE.KATAKANA_IROHA;\n        case 'khmer':\n            return LIST_STYLE_TYPE.KHMER;\n        case 'korean-hangul-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL;\n        case 'korean-hanja-formal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL;\n        case 'korean-hanja-informal':\n            return LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL;\n        case 'lao':\n            return LIST_STYLE_TYPE.LAO;\n        case 'lower-armenian':\n            return LIST_STYLE_TYPE.LOWER_ARMENIAN;\n        case 'malayalam':\n            return LIST_STYLE_TYPE.MALAYALAM;\n        case 'mongolian':\n            return LIST_STYLE_TYPE.MONGOLIAN;\n        case 'myanmar':\n            return LIST_STYLE_TYPE.MYANMAR;\n        case 'oriya':\n            return LIST_STYLE_TYPE.ORIYA;\n        case 'persian':\n            return LIST_STYLE_TYPE.PERSIAN;\n        case 'simp-chinese-formal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL;\n        case 'simp-chinese-informal':\n            return LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL;\n        case 'tamil':\n            return LIST_STYLE_TYPE.TAMIL;\n        case 'telugu':\n            return LIST_STYLE_TYPE.TELUGU;\n        case 'thai':\n            return LIST_STYLE_TYPE.THAI;\n        case 'tibetan':\n            return LIST_STYLE_TYPE.TIBETAN;\n        case 'trad-chinese-formal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL;\n        case 'trad-chinese-informal':\n            return LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL;\n        case 'upper-armenian':\n            return LIST_STYLE_TYPE.UPPER_ARMENIAN;\n        case 'disclosure-open':\n            return LIST_STYLE_TYPE.DISCLOSURE_OPEN;\n        case 'disclosure-closed':\n            return LIST_STYLE_TYPE.DISCLOSURE_CLOSED;\n        case 'none':\n        default:\n            return LIST_STYLE_TYPE.NONE;\n    }\n};\n\nvar parseListStyle = exports.parseListStyle = function parseListStyle(style) {\n    var listStyleImage = (0, _background.parseBackgroundImage)(style.getPropertyValue('list-style-image'));\n    return {\n        listStyleType: parseListStyleType(style.getPropertyValue('list-style-type')),\n        listStyleImage: listStyleImage.length ? listStyleImage[0] : null,\n        listStylePosition: parseListStylePosition(style.getPropertyValue('list-style-position'))\n    };\n};\n\nvar parseListStylePosition = function parseListStylePosition(position) {\n    switch (position) {\n        case 'inside':\n            return LIST_STYLE_POSITION.INSIDE;\n        case 'outside':\n        default:\n            return LIST_STYLE_POSITION.OUTSIDE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _textTransform = require('./parsing/textTransform');\n\nvar _TextBounds = require('./TextBounds');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextContainer = function () {\n    function TextContainer(text, parent, bounds) {\n        _classCallCheck(this, TextContainer);\n\n        this.text = text;\n        this.parent = parent;\n        this.bounds = bounds;\n    }\n\n    _createClass(TextContainer, null, [{\n        key: 'fromTextNode',\n        value: function fromTextNode(node, parent) {\n            var text = transform(node.data, parent.style.textTransform);\n            return new TextContainer(text, parent, (0, _TextBounds.parseTextBounds)(text, parent, node));\n        }\n    }]);\n\n    return TextContainer;\n}();\n\nexports.default = TextContainer;\n\n\nvar CAPITALIZE = /(^|\\s|:|-|\\(|\\))([a-z])/g;\n\nvar transform = function transform(text, _transform) {\n    switch (_transform) {\n        case _textTransform.TEXT_TRANSFORM.LOWERCASE:\n            return text.toLowerCase();\n        case _textTransform.TEXT_TRANSFORM.CAPITALIZE:\n            return text.replace(CAPITALIZE, capitalize);\n        case _textTransform.TEXT_TRANSFORM.UPPERCASE:\n            return text.toUpperCase();\n        default:\n            return text;\n    }\n};\n\nfunction capitalize(m, p1, p2) {\n    if (m.length > 0) {\n        return p1 + p2.toUpperCase();\n    }\n\n    return m;\n}", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar testRangeBounds = function testRangeBounds(document) {\n    var TEST_HEIGHT = 123;\n\n    if (document.createRange) {\n        var range = document.createRange();\n        if (range.getBoundingClientRect) {\n            var testElement = document.createElement('boundtest');\n            testElement.style.height = TEST_HEIGHT + 'px';\n            testElement.style.display = 'block';\n            document.body.appendChild(testElement);\n\n            range.selectNode(testElement);\n            var rangeBounds = range.getBoundingClientRect();\n            var rangeHeight = Math.round(rangeBounds.height);\n            document.body.removeChild(testElement);\n            if (rangeHeight === TEST_HEIGHT) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n};\n\n// iOS 10.3 taints canvas with base64 images unless crossOrigin = 'anonymous'\nvar testBase64 = function testBase64(document, src) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n\n    return new Promise(function (resolve) {\n        // Single pixel base64 image renders fine on iOS 10.3???\n        img.src = src;\n\n        var onload = function onload() {\n            try {\n                ctx.drawImage(img, 0, 0);\n                canvas.toDataURL();\n            } catch (e) {\n                return resolve(false);\n            }\n\n            return resolve(true);\n        };\n\n        img.onload = onload;\n        img.onerror = function () {\n            return resolve(false);\n        };\n\n        if (img.complete === true) {\n            setTimeout(function () {\n                onload();\n            }, 500);\n        }\n    });\n};\n\nvar testCORS = function testCORS() {\n    return typeof new Image().crossOrigin !== 'undefined';\n};\n\nvar testResponseType = function testResponseType() {\n    return typeof new XMLHttpRequest().responseType === 'string';\n};\n\nvar testSVG = function testSVG(document) {\n    var img = new Image();\n    var canvas = document.createElement('canvas');\n    var ctx = canvas.getContext('2d');\n    img.src = 'data:image/svg+xml,<svg xmlns=\\'http://www.w3.org/2000/svg\\'></svg>';\n\n    try {\n        ctx.drawImage(img, 0, 0);\n        canvas.toDataURL();\n    } catch (e) {\n        return false;\n    }\n    return true;\n};\n\nvar isGreenPixel = function isGreenPixel(data) {\n    return data[0] === 0 && data[1] === 255 && data[2] === 0 && data[3] === 255;\n};\n\nvar testForeignObject = function testForeignObject(document) {\n    var canvas = document.createElement('canvas');\n    var size = 100;\n    canvas.width = size;\n    canvas.height = size;\n    var ctx = canvas.getContext('2d');\n    ctx.fillStyle = 'rgb(0, 255, 0)';\n    ctx.fillRect(0, 0, size, size);\n\n    var img = new Image();\n    var greenImageSrc = canvas.toDataURL();\n    img.src = greenImageSrc;\n    var svg = (0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, img);\n    ctx.fillStyle = 'red';\n    ctx.fillRect(0, 0, size, size);\n\n    return (0, _ForeignObjectRenderer.loadSerializedSVG)(svg).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        var data = ctx.getImageData(0, 0, size, size).data;\n        ctx.fillStyle = 'red';\n        ctx.fillRect(0, 0, size, size);\n\n        var node = document.createElement('div');\n        node.style.backgroundImage = 'url(' + greenImageSrc + ')';\n        node.style.height = size + 'px';\n        // Firefox 55 does not render inline <img /> tags\n        return isGreenPixel(data) ? (0, _ForeignObjectRenderer.loadSerializedSVG)((0, _ForeignObjectRenderer.createForeignObjectSVG)(size, size, 0, 0, node)) : Promise.reject(false);\n    }).then(function (img) {\n        ctx.drawImage(img, 0, 0);\n        // Edge does not render background-images\n        return isGreenPixel(ctx.getImageData(0, 0, size, size).data);\n    }).catch(function (e) {\n        return false;\n    });\n};\n\nvar FEATURES = {\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RANGE_BOUNDS() {\n        'use strict';\n\n        var value = testRangeBounds(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_RANGE_BOUNDS', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_SVG_DRAWING() {\n        'use strict';\n\n        var value = testSVG(document);\n        Object.defineProperty(FEATURES, 'SUPPORT_SVG_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_BASE64_DRAWING() {\n        'use strict';\n\n        return function (src) {\n            var _value = testBase64(document, src);\n            Object.defineProperty(FEATURES, 'SUPPORT_BASE64_DRAWING', { value: function value() {\n                    return _value;\n                } });\n            return _value;\n        };\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_FOREIGNOBJECT_DRAWING() {\n        'use strict';\n\n        var value = typeof Array.from === 'function' && typeof window.fetch === 'function' ? testForeignObject(document) : Promise.resolve(false);\n        Object.defineProperty(FEATURES, 'SUPPORT_FOREIGNOBJECT_DRAWING', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_IMAGES() {\n        'use strict';\n\n        var value = testCORS();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_IMAGES', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_RESPONSE_TYPE() {\n        'use strict';\n\n        var value = testResponseType();\n        Object.defineProperty(FEATURES, 'SUPPORT_RESPONSE_TYPE', { value: value });\n        return value;\n    },\n    // $FlowFixMe - get/set properties not yet supported\n    get SUPPORT_CORS_XHR() {\n        'use strict';\n\n        var value = 'withCredentials' in new XMLHttpRequest();\n        Object.defineProperty(FEATURES, 'SUPPORT_CORS_XHR', { value: value });\n        return value;\n    }\n};\n\nexports.default = FEATURES;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextDecoration = exports.TEXT_DECORATION_LINE = exports.TEXT_DECORATION = exports.TEXT_DECORATION_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar TEXT_DECORATION_STYLE = exports.TEXT_DECORATION_STYLE = {\n    SOLID: 0,\n    DOUBLE: 1,\n    DOTTED: 2,\n    DASHED: 3,\n    WAVY: 4\n};\n\nvar TEXT_DECORATION = exports.TEXT_DECORATION = {\n    NONE: null\n};\n\nvar TEXT_DECORATION_LINE = exports.TEXT_DECORATION_LINE = {\n    UNDERLINE: 1,\n    OVERLINE: 2,\n    LINE_THROUGH: 3,\n    BLINK: 4\n};\n\nvar parseLine = function parseLine(line) {\n    switch (line) {\n        case 'underline':\n            return TEXT_DECORATION_LINE.UNDERLINE;\n        case 'overline':\n            return TEXT_DECORATION_LINE.OVERLINE;\n        case 'line-through':\n            return TEXT_DECORATION_LINE.LINE_THROUGH;\n    }\n    return TEXT_DECORATION_LINE.BLINK;\n};\n\nvar parseTextDecorationLine = function parseTextDecorationLine(line) {\n    if (line === 'none') {\n        return null;\n    }\n\n    return line.split(' ').map(parseLine);\n};\n\nvar parseTextDecorationStyle = function parseTextDecorationStyle(style) {\n    switch (style) {\n        case 'double':\n            return TEXT_DECORATION_STYLE.DOUBLE;\n        case 'dotted':\n            return TEXT_DECORATION_STYLE.DOTTED;\n        case 'dashed':\n            return TEXT_DECORATION_STYLE.DASHED;\n        case 'wavy':\n            return TEXT_DECORATION_STYLE.WAVY;\n    }\n    return TEXT_DECORATION_STYLE.SOLID;\n};\n\nvar parseTextDecoration = exports.parseTextDecoration = function parseTextDecoration(style) {\n    var textDecorationLine = parseTextDecorationLine(style.textDecorationLine ? style.textDecorationLine : style.textDecoration);\n    if (textDecorationLine === null) {\n        return TEXT_DECORATION.NONE;\n    }\n\n    var textDecorationColor = style.textDecorationColor ? new _Color2.default(style.textDecorationColor) : null;\n    var textDecorationStyle = parseTextDecorationStyle(style.textDecorationStyle);\n\n    return {\n        textDecorationLine: textDecorationLine,\n        textDecorationColor: textDecorationColor,\n        textDecorationStyle: textDecorationStyle\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorder = exports.BORDER_SIDES = exports.BORDER_STYLE = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar BORDER_STYLE = exports.BORDER_STYLE = {\n    NONE: 0,\n    SOLID: 1\n};\n\nvar BORDER_SIDES = exports.BORDER_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = Object.keys(BORDER_SIDES).map(function (s) {\n    return s.toLowerCase();\n});\n\nvar parseBorderStyle = function parseBorderStyle(style) {\n    switch (style) {\n        case 'none':\n            return BORDER_STYLE.NONE;\n    }\n    return BORDER_STYLE.SOLID;\n};\n\nvar parseBorder = exports.parseBorder = function parseBorder(style) {\n    return SIDES.map(function (side) {\n        var borderColor = new _Color2.default(style.getPropertyValue('border-' + side + '-color'));\n        var borderStyle = parseBorderStyle(style.getPropertyValue('border-' + side + '-style'));\n        var borderWidth = parseFloat(style.getPropertyValue('border-' + side + '-width'));\n        return {\n            borderColor: borderColor,\n            borderStyle: borderStyle,\n            borderWidth: isNaN(borderWidth) ? 0 : borderWidth\n        };\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar toCodePoints = exports.toCodePoints = function toCodePoints(str) {\n    var codePoints = [];\n    var i = 0;\n    var length = str.length;\n    while (i < length) {\n        var value = str.charCodeAt(i++);\n        if (value >= 0xd800 && value <= 0xdbff && i < length) {\n            var extra = str.charCodeAt(i++);\n            if ((extra & 0xfc00) === 0xdc00) {\n                codePoints.push(((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000);\n            } else {\n                codePoints.push(value);\n                i--;\n            }\n        } else {\n            codePoints.push(value);\n        }\n    }\n    return codePoints;\n};\n\nvar fromCodePoint = exports.fromCodePoint = function fromCodePoint() {\n    if (String.fromCodePoint) {\n        return String.fromCodePoint.apply(String, arguments);\n    }\n\n    var length = arguments.length;\n    if (!length) {\n        return '';\n    }\n\n    var codeUnits = [];\n\n    var index = -1;\n    var result = '';\n    while (++index < length) {\n        var codePoint = arguments.length <= index ? undefined : arguments[index];\n        if (codePoint <= 0xffff) {\n            codeUnits.push(codePoint);\n        } else {\n            codePoint -= 0x10000;\n            codeUnits.push((codePoint >> 10) + 0xd800, codePoint % 0x400 + 0xdc00);\n        }\n        if (index + 1 === length || codeUnits.length > 0x4000) {\n            result += String.fromCharCode.apply(String, codeUnits);\n            codeUnits.length = 0;\n        }\n    }\n    return result;\n};\n\nvar chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n// Use a lookup table to find the index.\nvar lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (var i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\n\nvar decode = exports.decode = function decode(base64) {\n    var bufferLength = base64.length * 0.75,\n        len = base64.length,\n        i = void 0,\n        p = 0,\n        encoded1 = void 0,\n        encoded2 = void 0,\n        encoded3 = void 0,\n        encoded4 = void 0;\n\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = typeof ArrayBuffer !== 'undefined' && typeof Uint8Array !== 'undefined' && typeof Uint8Array.prototype.slice !== 'undefined' ? new ArrayBuffer(bufferLength) : new Array(bufferLength);\n    var bytes = Array.isArray(buffer) ? buffer : new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n\n    return buffer;\n};\n\nvar polyUint16Array = exports.polyUint16Array = function polyUint16Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i = 0; _i < length; _i += 2) {\n        bytes.push(buffer[_i + 1] << 8 | buffer[_i]);\n    }\n    return bytes;\n};\n\nvar polyUint32Array = exports.polyUint32Array = function polyUint32Array(buffer) {\n    var length = buffer.length;\n    var bytes = [];\n    for (var _i2 = 0; _i2 < length; _i2 += 4) {\n        bytes.push(buffer[_i2 + 3] << 24 | buffer[_i2 + 2] << 16 | buffer[_i2 + 1] << 8 | buffer[_i2]);\n    }\n    return bytes;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.createCounterText = exports.inlineListItemElement = exports.getListOwner = undefined;\n\nvar _Util = require('./Util');\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n// Margin between the enumeration and the list item content\nvar MARGIN_RIGHT = 7;\n\nvar ancestorTypes = ['OL', 'UL', 'MENU'];\n\nvar getListOwner = exports.getListOwner = function getListOwner(container) {\n    var parent = container.parent;\n    if (!parent) {\n        return null;\n    }\n\n    do {\n        var isAncestor = ancestorTypes.indexOf(parent.tagName) !== -1;\n        if (isAncestor) {\n            return parent;\n        }\n        parent = parent.parent;\n    } while (parent);\n\n    return container.parent;\n};\n\nvar inlineListItemElement = exports.inlineListItemElement = function inlineListItemElement(node, container, resourceLoader) {\n    var listStyle = container.style.listStyle;\n\n    if (!listStyle) {\n        return;\n    }\n\n    var style = node.ownerDocument.defaultView.getComputedStyle(node, null);\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    (0, _Util.copyCSSStyles)(style, wrapper);\n\n    wrapper.style.position = 'absolute';\n    wrapper.style.bottom = 'auto';\n    wrapper.style.display = 'block';\n    wrapper.style.letterSpacing = 'normal';\n\n    switch (listStyle.listStylePosition) {\n        case _listStyle.LIST_STYLE_POSITION.OUTSIDE:\n            wrapper.style.left = 'auto';\n            wrapper.style.right = node.ownerDocument.defaultView.innerWidth - container.bounds.left - container.style.margin[1].getAbsoluteValue(container.bounds.width) + MARGIN_RIGHT + 'px';\n            wrapper.style.textAlign = 'right';\n            break;\n        case _listStyle.LIST_STYLE_POSITION.INSIDE:\n            wrapper.style.left = container.bounds.left - container.style.margin[3].getAbsoluteValue(container.bounds.width) + 'px';\n            wrapper.style.right = 'auto';\n            wrapper.style.textAlign = 'left';\n            break;\n    }\n\n    var text = void 0;\n    var MARGIN_TOP = container.style.margin[0].getAbsoluteValue(container.bounds.width);\n    var styleImage = listStyle.listStyleImage;\n    if (styleImage) {\n        if (styleImage.method === 'url') {\n            var image = node.ownerDocument.createElement('img');\n            image.src = styleImage.args[0];\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n            wrapper.style.width = 'auto';\n            wrapper.style.height = 'auto';\n            wrapper.appendChild(image);\n        } else {\n            var size = parseFloat(container.style.font.fontSize) * 0.5;\n            wrapper.style.top = container.bounds.top - MARGIN_TOP + container.bounds.height - 1.5 * size + 'px';\n            wrapper.style.width = size + 'px';\n            wrapper.style.height = size + 'px';\n            wrapper.style.backgroundImage = style.listStyleImage;\n        }\n    } else if (typeof container.listIndex === 'number') {\n        text = node.ownerDocument.createTextNode(createCounterText(container.listIndex, listStyle.listStyleType, true));\n        wrapper.appendChild(text);\n        wrapper.style.top = container.bounds.top - MARGIN_TOP + 'px';\n    }\n\n    // $FlowFixMe\n    var body = node.ownerDocument.body;\n    body.appendChild(wrapper);\n\n    if (text) {\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    } else {\n        // $FlowFixMe\n        container.childNodes.push(new _NodeContainer2.default(wrapper, container, resourceLoader, 0));\n    }\n};\n\nvar ROMAN_UPPER = {\n    integers: [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1],\n    values: ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I']\n};\n\nvar ARMENIAN = {\n    integers: [9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['Ք', 'Փ', 'Ւ', 'Ց', 'Ր', 'Տ', 'Վ', 'Ս', 'Ռ', 'Ջ', 'Պ', 'Չ', 'Ո', 'Շ', 'Ն', 'Յ', 'Մ', 'Ճ', 'Ղ', 'Ձ', 'Հ', 'Կ', 'Ծ', 'Խ', 'Լ', 'Ի', 'Ժ', 'Թ', 'Ը', 'Է', 'Զ', 'Ե', 'Դ', 'Գ', 'Բ', 'Ա']\n};\n\nvar HEBREW = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 19, 18, 17, 16, 15, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['י׳', 'ט׳', 'ח׳', 'ז׳', 'ו׳', 'ה׳', 'ד׳', 'ג׳', 'ב׳', 'א׳', 'ת', 'ש', 'ר', 'ק', 'צ', 'פ', 'ע', 'ס', 'נ', 'מ', 'ל', 'כ', 'יט', 'יח', 'יז', 'טז', 'טו', 'י', 'ט', 'ח', 'ז', 'ו', 'ה', 'ד', 'ג', 'ב', 'א']\n};\n\nvar GEORGIAN = {\n    integers: [10000, 9000, 8000, 7000, 6000, 5000, 4000, 3000, 2000, 1000, 900, 800, 700, 600, 500, 400, 300, 200, 100, 90, 80, 70, 60, 50, 40, 30, 20, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],\n    values: ['ჵ', 'ჰ', 'ჯ', 'ჴ', 'ხ', 'ჭ', 'წ', 'ძ', 'ც', 'ჩ', 'შ', 'ყ', 'ღ', 'ქ', 'ფ', 'ჳ', 'ტ', 'ს', 'რ', 'ჟ', 'პ', 'ო', 'ჲ', 'ნ', 'მ', 'ლ', 'კ', 'ი', 'თ', 'ჱ', 'ზ', 'ვ', 'ე', 'დ', 'გ', 'ბ', 'ა']\n};\n\nvar createAdditiveCounter = function createAdditiveCounter(value, min, max, symbols, fallback, suffix) {\n    if (value < min || value > max) {\n        return createCounterText(value, fallback, suffix.length > 0);\n    }\n\n    return symbols.integers.reduce(function (string, integer, index) {\n        while (value >= integer) {\n            value -= integer;\n            string += symbols.values[index];\n        }\n        return string;\n    }, '') + suffix;\n};\n\nvar createCounterStyleWithSymbolResolver = function createCounterStyleWithSymbolResolver(value, codePointRangeLength, isNumeric, resolver) {\n    var string = '';\n\n    do {\n        if (!isNumeric) {\n            value--;\n        }\n        string = resolver(value) + string;\n        value /= codePointRangeLength;\n    } while (value * codePointRangeLength >= codePointRangeLength);\n\n    return string;\n};\n\nvar createCounterStyleFromRange = function createCounterStyleFromRange(value, codePointRangeStart, codePointRangeEnd, isNumeric, suffix) {\n    var codePointRangeLength = codePointRangeEnd - codePointRangeStart + 1;\n\n    return (value < 0 ? '-' : '') + (createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, isNumeric, function (codePoint) {\n        return (0, _Unicode.fromCodePoint)(Math.floor(codePoint % codePointRangeLength) + codePointRangeStart);\n    }) + suffix);\n};\n\nvar createCounterStyleFromSymbols = function createCounterStyleFromSymbols(value, symbols) {\n    var suffix = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '. ';\n\n    var codePointRangeLength = symbols.length;\n    return createCounterStyleWithSymbolResolver(Math.abs(value), codePointRangeLength, false, function (codePoint) {\n        return symbols[Math.floor(codePoint % codePointRangeLength)];\n    }) + suffix;\n};\n\nvar CJK_ZEROS = 1 << 0;\nvar CJK_TEN_COEFFICIENTS = 1 << 1;\nvar CJK_TEN_HIGH_COEFFICIENTS = 1 << 2;\nvar CJK_HUNDRED_COEFFICIENTS = 1 << 3;\n\nvar createCJKCounter = function createCJKCounter(value, numbers, multipliers, negativeSign, suffix, flags) {\n    if (value < -9999 || value > 9999) {\n        return createCounterText(value, _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL, suffix.length > 0);\n    }\n    var tmp = Math.abs(value);\n    var string = suffix;\n\n    if (tmp === 0) {\n        return numbers[0] + string;\n    }\n\n    for (var digit = 0; tmp > 0 && digit <= 4; digit++) {\n        var coefficient = tmp % 10;\n\n        if (coefficient === 0 && (0, _Util.contains)(flags, CJK_ZEROS) && string !== '') {\n            string = numbers[coefficient] + string;\n        } else if (coefficient > 1 || coefficient === 1 && digit === 0 || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_COEFFICIENTS) || coefficient === 1 && digit === 1 && (0, _Util.contains)(flags, CJK_TEN_HIGH_COEFFICIENTS) && value > 100 || coefficient === 1 && digit > 1 && (0, _Util.contains)(flags, CJK_HUNDRED_COEFFICIENTS)) {\n            string = numbers[coefficient] + (digit > 0 ? multipliers[digit - 1] : '') + string;\n        } else if (coefficient === 1 && digit > 0) {\n            string = multipliers[digit - 1] + string;\n        }\n        tmp = Math.floor(tmp / 10);\n    }\n\n    return (value < 0 ? negativeSign : '') + string;\n};\n\nvar CHINESE_INFORMAL_MULTIPLIERS = '十百千萬';\nvar CHINESE_FORMAL_MULTIPLIERS = '拾佰仟萬';\nvar JAPANESE_NEGATIVE = 'マイナス';\nvar KOREAN_NEGATIVE = '마이너스 ';\n\nvar createCounterText = exports.createCounterText = function createCounterText(value, type, appendSuffix) {\n    var defaultSuffix = appendSuffix ? '. ' : '';\n    var cjkSuffix = appendSuffix ? '、' : '';\n    var koreanSuffix = appendSuffix ? ', ' : '';\n    switch (type) {\n        case _listStyle.LIST_STYLE_TYPE.DISC:\n            return '•';\n        case _listStyle.LIST_STYLE_TYPE.CIRCLE:\n            return '◦';\n        case _listStyle.LIST_STYLE_TYPE.SQUARE:\n            return '◾';\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL_LEADING_ZERO:\n            var string = createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n            return string.length < 4 ? '0' + string : string;\n        case _listStyle.LIST_STYLE_TYPE.CJK_DECIMAL:\n            return createCounterStyleFromSymbols(value, '〇一二三四五六七八九', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ROMAN:\n            return createAdditiveCounter(value, 1, 3999, ROMAN_UPPER, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_GREEK:\n            return createCounterStyleFromRange(value, 945, 969, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ALPHA:\n            return createCounterStyleFromRange(value, 97, 122, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ALPHA:\n            return createCounterStyleFromRange(value, 65, 90, false, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARABIC_INDIC:\n            return createCounterStyleFromRange(value, 1632, 1641, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ARMENIAN:\n        case _listStyle.LIST_STYLE_TYPE.UPPER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LOWER_ARMENIAN:\n            return createAdditiveCounter(value, 1, 9999, ARMENIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix).toLowerCase();\n        case _listStyle.LIST_STYLE_TYPE.BENGALI:\n            return createCounterStyleFromRange(value, 2534, 2543, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CAMBODIAN:\n        case _listStyle.LIST_STYLE_TYPE.KHMER:\n            return createCounterStyleFromRange(value, 6112, 6121, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_EARTHLY_BRANCH:\n            return createCounterStyleFromSymbols(value, '子丑寅卯辰巳午未申酉戌亥', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_HEAVENLY_STEM:\n            return createCounterStyleFromSymbols(value, '甲乙丙丁戊己庚辛壬癸', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.CJK_IDEOGRAPHIC:\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.TRAD_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹貳參肆伍陸柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '負', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', CHINESE_INFORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.SIMP_CHINESE_FORMAL:\n            return createCJKCounter(value, '零壹贰叁肆伍陆柒捌玖', CHINESE_FORMAL_MULTIPLIERS, '负', cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS | CJK_HUNDRED_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_INFORMAL:\n            return createCJKCounter(value, '〇一二三四五六七八九', '十百千万', JAPANESE_NEGATIVE, cjkSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.JAPANESE_FORMAL:\n            return createCJKCounter(value, '零壱弐参四伍六七八九', '拾百千万', JAPANESE_NEGATIVE, cjkSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANGUL_FORMAL:\n            return createCJKCounter(value, '영일이삼사오육칠팔구', '십백천만', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_INFORMAL:\n            return createCJKCounter(value, '零一二三四五六七八九', '十百千萬', KOREAN_NEGATIVE, koreanSuffix, 0);\n        case _listStyle.LIST_STYLE_TYPE.KOREAN_HANJA_FORMAL:\n            return createCJKCounter(value, '零壹貳參四五六七八九', '拾百千', KOREAN_NEGATIVE, koreanSuffix, CJK_ZEROS | CJK_TEN_COEFFICIENTS | CJK_TEN_HIGH_COEFFICIENTS);\n        case _listStyle.LIST_STYLE_TYPE.DEVANAGARI:\n            return createCounterStyleFromRange(value, 0x966, 0x96f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GEORGIAN:\n            return createAdditiveCounter(value, 1, 19999, GEORGIAN, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GUJARATI:\n            return createCounterStyleFromRange(value, 0xae6, 0xaef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.GURMUKHI:\n            return createCounterStyleFromRange(value, 0xa66, 0xa6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HEBREW:\n            return createAdditiveCounter(value, 1, 10999, HEBREW, _listStyle.LIST_STYLE_TYPE.DECIMAL, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA:\n            return createCounterStyleFromSymbols(value, 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん');\n        case _listStyle.LIST_STYLE_TYPE.HIRAGANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす');\n        case _listStyle.LIST_STYLE_TYPE.KANNADA:\n            return createCounterStyleFromRange(value, 0xce6, 0xcef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA:\n            return createCounterStyleFromSymbols(value, 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.KATAKANA_IROHA:\n            return createCounterStyleFromSymbols(value, 'イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス', cjkSuffix);\n        case _listStyle.LIST_STYLE_TYPE.LAO:\n            return createCounterStyleFromRange(value, 0xed0, 0xed9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MONGOLIAN:\n            return createCounterStyleFromRange(value, 0x1810, 0x1819, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.MYANMAR:\n            return createCounterStyleFromRange(value, 0x1040, 0x1049, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.ORIYA:\n            return createCounterStyleFromRange(value, 0xb66, 0xb6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.PERSIAN:\n            return createCounterStyleFromRange(value, 0x6f0, 0x6f9, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TAMIL:\n            return createCounterStyleFromRange(value, 0xbe6, 0xbef, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TELUGU:\n            return createCounterStyleFromRange(value, 0xc66, 0xc6f, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.THAI:\n            return createCounterStyleFromRange(value, 0xe50, 0xe59, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.TIBETAN:\n            return createCounterStyleFromRange(value, 0xf20, 0xf29, true, defaultSuffix);\n        case _listStyle.LIST_STYLE_TYPE.DECIMAL:\n        default:\n            return createCounterStyleFromRange(value, 48, 57, true, defaultSuffix);\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('../drawing/Path');\n\nvar _textDecoration = require('../parsing/textDecoration');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar addColorStops = function addColorStops(gradient, canvasGradient) {\n    var maxStop = Math.max.apply(null, gradient.colorStops.map(function (colorStop) {\n        return colorStop.stop;\n    }));\n    var f = 1 / Math.max(1, maxStop);\n    gradient.colorStops.forEach(function (colorStop) {\n        canvasGradient.addColorStop(f * colorStop.stop, colorStop.color.toString());\n    });\n};\n\nvar CanvasRenderer = function () {\n    function CanvasRenderer(canvas) {\n        _classCallCheck(this, CanvasRenderer);\n\n        this.canvas = canvas ? canvas : document.createElement('canvas');\n        this.customCanvas = !!canvas;\n    }\n\n    _createClass(CanvasRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            this.ctx = this.canvas.getContext('2d');\n            this.options = options;\n            if (!this.customCanvas) {\n                this.canvas.width = Math.floor(options.width * options.scale);\n                this.canvas.height = Math.floor(options.height * options.scale);\n                this.canvas.style.width = options.width + 'px';\n                this.canvas.style.height = options.height + 'px';\n            }\n\n            this.ctx.scale(this.options.scale, this.options.scale);\n            this.ctx.translate(-options.x, -options.y);\n            this.ctx.textBaseline = 'bottom';\n            options.logger.log('Canvas renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + this.options.scale);\n        }\n    }, {\n        key: 'clip',\n        value: function clip(clipPaths, callback) {\n            var _this = this;\n\n            if (clipPaths.length) {\n                this.ctx.save();\n                clipPaths.forEach(function (path) {\n                    _this.path(path);\n                    _this.ctx.clip();\n                });\n            }\n\n            callback();\n\n            if (clipPaths.length) {\n                this.ctx.restore();\n            }\n        }\n    }, {\n        key: 'drawImage',\n        value: function drawImage(image, source, destination) {\n            this.ctx.drawImage(image, source.left, source.top, source.width, source.height, destination.left, destination.top, destination.width, destination.height);\n        }\n    }, {\n        key: 'drawShape',\n        value: function drawShape(path, color) {\n            this.path(path);\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'fill',\n        value: function fill(color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fill();\n        }\n    }, {\n        key: 'getTarget',\n        value: function getTarget() {\n            return Promise.resolve(this.canvas);\n        }\n    }, {\n        key: 'path',\n        value: function path(_path) {\n            var _this2 = this;\n\n            this.ctx.beginPath();\n            if (Array.isArray(_path)) {\n                _path.forEach(function (point, index) {\n                    var start = point.type === _Path.PATH.VECTOR ? point : point.start;\n                    if (index === 0) {\n                        _this2.ctx.moveTo(start.x, start.y);\n                    } else {\n                        _this2.ctx.lineTo(start.x, start.y);\n                    }\n\n                    if (point.type === _Path.PATH.BEZIER_CURVE) {\n                        _this2.ctx.bezierCurveTo(point.startControl.x, point.startControl.y, point.endControl.x, point.endControl.y, point.end.x, point.end.y);\n                    }\n                });\n            } else {\n                this.ctx.arc(_path.x + _path.radius, _path.y + _path.radius, _path.radius, 0, Math.PI * 2, true);\n            }\n\n            this.ctx.closePath();\n        }\n    }, {\n        key: 'rectangle',\n        value: function rectangle(x, y, width, height, color) {\n            this.ctx.fillStyle = color.toString();\n            this.ctx.fillRect(x, y, width, height);\n        }\n    }, {\n        key: 'renderLinearGradient',\n        value: function renderLinearGradient(bounds, gradient) {\n            var linearGradient = this.ctx.createLinearGradient(bounds.left + gradient.direction.x1, bounds.top + gradient.direction.y1, bounds.left + gradient.direction.x0, bounds.top + gradient.direction.y0);\n\n            addColorStops(gradient, linearGradient);\n            this.ctx.fillStyle = linearGradient;\n            this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n        }\n    }, {\n        key: 'renderRadialGradient',\n        value: function renderRadialGradient(bounds, gradient) {\n            var _this3 = this;\n\n            var x = bounds.left + gradient.center.x;\n            var y = bounds.top + gradient.center.y;\n\n            var radialGradient = this.ctx.createRadialGradient(x, y, 0, x, y, gradient.radius.x);\n            if (!radialGradient) {\n                return;\n            }\n\n            addColorStops(gradient, radialGradient);\n            this.ctx.fillStyle = radialGradient;\n\n            if (gradient.radius.x !== gradient.radius.y) {\n                // transforms for elliptical radial gradient\n                var midX = bounds.left + 0.5 * bounds.width;\n                var midY = bounds.top + 0.5 * bounds.height;\n                var f = gradient.radius.y / gradient.radius.x;\n                var invF = 1 / f;\n\n                this.transform(midX, midY, [1, 0, 0, f, 0, 0], function () {\n                    return _this3.ctx.fillRect(bounds.left, invF * (bounds.top - midY) + midY, bounds.width, bounds.height * invF);\n                });\n            } else {\n                this.ctx.fillRect(bounds.left, bounds.top, bounds.width, bounds.height);\n            }\n        }\n    }, {\n        key: 'renderRepeat',\n        value: function renderRepeat(path, image, imageSize, offsetX, offsetY) {\n            this.path(path);\n            this.ctx.fillStyle = this.ctx.createPattern(this.resizeImage(image, imageSize), 'repeat');\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.fill();\n            this.ctx.translate(-offsetX, -offsetY);\n        }\n    }, {\n        key: 'renderTextNode',\n        value: function renderTextNode(textBounds, color, font, textDecoration, textShadows) {\n            var _this4 = this;\n\n            this.ctx.font = [font.fontStyle, font.fontVariant, font.fontWeight, font.fontSize, font.fontFamily].join(' ');\n\n            textBounds.forEach(function (text) {\n                _this4.ctx.fillStyle = color.toString();\n                if (textShadows && text.text.trim().length) {\n                    textShadows.slice(0).reverse().forEach(function (textShadow) {\n                        _this4.ctx.shadowColor = textShadow.color.toString();\n                        _this4.ctx.shadowOffsetX = textShadow.offsetX * _this4.options.scale;\n                        _this4.ctx.shadowOffsetY = textShadow.offsetY * _this4.options.scale;\n                        _this4.ctx.shadowBlur = textShadow.blur;\n\n                        _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                    });\n                } else {\n                    _this4.ctx.fillText(text.text, text.bounds.left, text.bounds.top + text.bounds.height);\n                }\n\n                if (textDecoration !== null) {\n                    var textDecorationColor = textDecoration.textDecorationColor || color;\n                    textDecoration.textDecorationLine.forEach(function (textDecorationLine) {\n                        switch (textDecorationLine) {\n                            case _textDecoration.TEXT_DECORATION_LINE.UNDERLINE:\n                                // Draws a line at the baseline of the font\n                                // TODO As some browsers display the line as more than 1px if the font-size is big,\n                                // need to take that into account both in position and size\n                                var _options$fontMetrics$ = _this4.options.fontMetrics.getMetrics(font),\n                                    baseline = _options$fontMetrics$.baseline;\n\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top + baseline), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.OVERLINE:\n                                _this4.rectangle(text.bounds.left, Math.round(text.bounds.top), text.bounds.width, 1, textDecorationColor);\n                                break;\n                            case _textDecoration.TEXT_DECORATION_LINE.LINE_THROUGH:\n                                // TODO try and find exact position for line-through\n                                var _options$fontMetrics$2 = _this4.options.fontMetrics.getMetrics(font),\n                                    middle = _options$fontMetrics$2.middle;\n\n                                _this4.rectangle(text.bounds.left, Math.ceil(text.bounds.top + middle), text.bounds.width, 1, textDecorationColor);\n                                break;\n                        }\n                    });\n                }\n            });\n        }\n    }, {\n        key: 'resizeImage',\n        value: function resizeImage(image, size) {\n            if (image.width === size.width && image.height === size.height) {\n                return image;\n            }\n\n            var canvas = this.canvas.ownerDocument.createElement('canvas');\n            canvas.width = size.width;\n            canvas.height = size.height;\n            var ctx = canvas.getContext('2d');\n            ctx.drawImage(image, 0, 0, image.width, image.height, 0, 0, size.width, size.height);\n            return canvas;\n        }\n    }, {\n        key: 'setOpacity',\n        value: function setOpacity(opacity) {\n            this.ctx.globalAlpha = opacity;\n        }\n    }, {\n        key: 'transform',\n        value: function transform(offsetX, offsetY, matrix, callback) {\n            this.ctx.save();\n            this.ctx.translate(offsetX, offsetY);\n            this.ctx.transform(matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]);\n            this.ctx.translate(-offsetX, -offsetY);\n\n            callback();\n\n            this.ctx.restore();\n        }\n    }]);\n\n    return CanvasRenderer;\n}();\n\nexports.default = CanvasRenderer;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Logger = function () {\n    function Logger(enabled, id, start) {\n        _classCallCheck(this, Logger);\n\n        this.enabled = typeof window !== 'undefined' && enabled;\n        this.start = start ? start : Date.now();\n        this.id = id;\n    }\n\n    _createClass(Logger, [{\n        key: 'child',\n        value: function child(id) {\n            return new Logger(this.enabled, id, this.start);\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'log',\n        value: function log() {\n            if (this.enabled && window.console && window.console.log) {\n                for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n                    args[_key] = arguments[_key];\n                }\n\n                Function.prototype.bind.call(window.console.log, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n\n        // eslint-disable-next-line flowtype/no-weak-types\n\n    }, {\n        key: 'error',\n        value: function error() {\n            if (this.enabled && window.console && window.console.error) {\n                for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                    args[_key2] = arguments[_key2];\n                }\n\n                Function.prototype.bind.call(window.console.error, window.console).apply(window.console, [Date.now() - this.start + 'ms', this.id ? 'html2canvas (' + this.id + '):' : 'html2canvas:'].concat([].slice.call(args, 0)));\n            }\n        }\n    }]);\n\n    return Logger;\n}();\n\nexports.default = Logger;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parsePadding = exports.PADDING_SIDES = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar PADDING_SIDES = exports.PADDING_SIDES = {\n    TOP: 0,\n    RIGHT: 1,\n    BOTTOM: 2,\n    LEFT: 3\n};\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parsePadding = exports.parsePadding = function parsePadding(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('padding-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW_WRAP = exports.OVERFLOW_WRAP = {\n    NORMAL: 0,\n    BREAK_WORD: 1\n};\n\nvar parseOverflowWrap = exports.parseOverflowWrap = function parseOverflowWrap(overflow) {\n    switch (overflow) {\n        case 'break-word':\n            return OVERFLOW_WRAP.BREAK_WORD;\n        case 'normal':\n        default:\n            return OVERFLOW_WRAP.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar POSITION = exports.POSITION = {\n    STATIC: 0,\n    RELATIVE: 1,\n    ABSOLUTE: 2,\n    FIXED: 3,\n    STICKY: 4\n};\n\nvar parsePosition = exports.parsePosition = function parsePosition(position) {\n    switch (position) {\n        case 'relative':\n            return POSITION.RELATIVE;\n        case 'absolute':\n            return POSITION.ABSOLUTE;\n        case 'fixed':\n            return POSITION.FIXED;\n        case 'sticky':\n            return POSITION.STICKY;\n    }\n\n    return POSITION.STATIC;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar TEXT_TRANSFORM = exports.TEXT_TRANSFORM = {\n    NONE: 0,\n    LOWERCASE: 1,\n    UPPERCASE: 2,\n    CAPITALIZE: 3\n};\n\nvar parseTextTransform = exports.parseTextTransform = function parseTextTransform(textTransform) {\n    switch (textTransform) {\n        case 'uppercase':\n            return TEXT_TRANSFORM.UPPERCASE;\n        case 'lowercase':\n            return TEXT_TRANSFORM.LOWERCASE;\n        case 'capitalize':\n            return TEXT_TRANSFORM.CAPITALIZE;\n    }\n\n    return TEXT_TRANSFORM.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.reformatInputBounds = exports.inlineSelectElement = exports.inlineTextAreaElement = exports.inlineInputElement = exports.getInputBorderRadius = exports.INPUT_BACKGROUND = exports.INPUT_BORDERS = exports.INPUT_COLOR = undefined;\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nvar _Circle = require('./drawing/Circle');\n\nvar _Circle2 = _interopRequireDefault(_Circle);\n\nvar _Vector = require('./drawing/Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Bounds = require('./Bounds');\n\nvar _TextBounds = require('./TextBounds');\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar INPUT_COLOR = exports.INPUT_COLOR = new _Color2.default([42, 42, 42]);\nvar INPUT_BORDER_COLOR = new _Color2.default([165, 165, 165]);\nvar INPUT_BACKGROUND_COLOR = new _Color2.default([222, 222, 222]);\nvar INPUT_BORDER = {\n    borderWidth: 1,\n    borderColor: INPUT_BORDER_COLOR,\n    borderStyle: _border.BORDER_STYLE.SOLID\n};\nvar INPUT_BORDERS = exports.INPUT_BORDERS = [INPUT_BORDER, INPUT_BORDER, INPUT_BORDER, INPUT_BORDER];\nvar INPUT_BACKGROUND = exports.INPUT_BACKGROUND = {\n    backgroundColor: INPUT_BACKGROUND_COLOR,\n    backgroundImage: [],\n    backgroundClip: _background.BACKGROUND_CLIP.PADDING_BOX,\n    backgroundOrigin: _background.BACKGROUND_ORIGIN.PADDING_BOX\n};\n\nvar RADIO_BORDER_RADIUS = new _Length2.default('50%');\nvar RADIO_BORDER_RADIUS_TUPLE = [RADIO_BORDER_RADIUS, RADIO_BORDER_RADIUS];\nvar INPUT_RADIO_BORDER_RADIUS = [RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE, RADIO_BORDER_RADIUS_TUPLE];\n\nvar CHECKBOX_BORDER_RADIUS = new _Length2.default('3px');\nvar CHECKBOX_BORDER_RADIUS_TUPLE = [CHECKBOX_BORDER_RADIUS, CHECKBOX_BORDER_RADIUS];\nvar INPUT_CHECKBOX_BORDER_RADIUS = [CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE, CHECKBOX_BORDER_RADIUS_TUPLE];\n\nvar getInputBorderRadius = exports.getInputBorderRadius = function getInputBorderRadius(node) {\n    return node.type === 'radio' ? INPUT_RADIO_BORDER_RADIUS : INPUT_CHECKBOX_BORDER_RADIUS;\n};\n\nvar inlineInputElement = exports.inlineInputElement = function inlineInputElement(node, container) {\n    if (node.type === 'radio' || node.type === 'checkbox') {\n        if (node.checked) {\n            var size = Math.min(container.bounds.width, container.bounds.height);\n            container.childNodes.push(node.type === 'checkbox' ? [new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79), new _Vector2.default(container.bounds.left + size * 0.16, container.bounds.top + size * 0.5549), new _Vector2.default(container.bounds.left + size * 0.27347, container.bounds.top + size * 0.44071), new _Vector2.default(container.bounds.left + size * 0.39694, container.bounds.top + size * 0.5649), new _Vector2.default(container.bounds.left + size * 0.72983, container.bounds.top + size * 0.23), new _Vector2.default(container.bounds.left + size * 0.84, container.bounds.top + size * 0.34085), new _Vector2.default(container.bounds.left + size * 0.39363, container.bounds.top + size * 0.79)] : new _Circle2.default(container.bounds.left + size / 4, container.bounds.top + size / 4, size / 4));\n        }\n    } else {\n        inlineFormElement(getInputValue(node), node, container, false);\n    }\n};\n\nvar inlineTextAreaElement = exports.inlineTextAreaElement = function inlineTextAreaElement(node, container) {\n    inlineFormElement(node.value, node, container, true);\n};\n\nvar inlineSelectElement = exports.inlineSelectElement = function inlineSelectElement(node, container) {\n    var option = node.options[node.selectedIndex || 0];\n    inlineFormElement(option ? option.text || '' : '', node, container, false);\n};\n\nvar reformatInputBounds = exports.reformatInputBounds = function reformatInputBounds(bounds) {\n    if (bounds.width > bounds.height) {\n        bounds.left += (bounds.width - bounds.height) / 2;\n        bounds.width = bounds.height;\n    } else if (bounds.width < bounds.height) {\n        bounds.top += (bounds.height - bounds.width) / 2;\n        bounds.height = bounds.width;\n    }\n    return bounds;\n};\n\nvar inlineFormElement = function inlineFormElement(value, node, container, allowLinebreak) {\n    var body = node.ownerDocument.body;\n    if (value.length > 0 && body) {\n        var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n        (0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node, null), wrapper);\n        wrapper.style.position = 'absolute';\n        wrapper.style.left = container.bounds.left + 'px';\n        wrapper.style.top = container.bounds.top + 'px';\n        if (!allowLinebreak) {\n            wrapper.style.whiteSpace = 'nowrap';\n        }\n        var text = node.ownerDocument.createTextNode(value);\n        wrapper.appendChild(text);\n        body.appendChild(wrapper);\n        container.childNodes.push(_TextContainer2.default.fromTextNode(text, container));\n        body.removeChild(wrapper);\n    }\n};\n\nvar getInputValue = function getInputValue(node) {\n    var value = node.type === 'password' ? new Array(node.value.length + 1).join('\\u2022') : node.value;\n\n    return value.length === 0 ? node.placeholder || '' : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextBounds = exports.TextBounds = undefined;\n\nvar _Bounds = require('./Bounds');\n\nvar _textDecoration = require('./parsing/textDecoration');\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Unicode = require('./Unicode');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar TextBounds = exports.TextBounds = function TextBounds(text, bounds) {\n    _classCallCheck(this, TextBounds);\n\n    this.text = text;\n    this.bounds = bounds;\n};\n\nvar parseTextBounds = exports.parseTextBounds = function parseTextBounds(value, parent, node) {\n    var letterRendering = parent.style.letterSpacing !== 0;\n    var textList = letterRendering ? (0, _Unicode.toCodePoints)(value).map(function (i) {\n        return (0, _Unicode.fromCodePoint)(i);\n    }) : (0, _Unicode.breakWords)(value, parent);\n    var length = textList.length;\n    var defaultView = node.parentNode ? node.parentNode.ownerDocument.defaultView : null;\n    var scrollX = defaultView ? defaultView.pageXOffset : 0;\n    var scrollY = defaultView ? defaultView.pageYOffset : 0;\n    var textBounds = [];\n    var offset = 0;\n    for (var i = 0; i < length; i++) {\n        var text = textList[i];\n        if (parent.style.textDecoration !== _textDecoration.TEXT_DECORATION.NONE || text.trim().length > 0) {\n            if (_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n                textBounds.push(new TextBounds(text, getRangeBounds(node, offset, text.length, scrollX, scrollY)));\n            } else {\n                var replacementNode = node.splitText(text.length);\n                textBounds.push(new TextBounds(text, getWrapperBounds(node, scrollX, scrollY)));\n                node = replacementNode;\n            }\n        } else if (!_Feature2.default.SUPPORT_RANGE_BOUNDS) {\n            node = node.splitText(text.length);\n        }\n        offset += text.length;\n    }\n    return textBounds;\n};\n\nvar getWrapperBounds = function getWrapperBounds(node, scrollX, scrollY) {\n    var wrapper = node.ownerDocument.createElement('html2canvaswrapper');\n    wrapper.appendChild(node.cloneNode(true));\n    var parentNode = node.parentNode;\n    if (parentNode) {\n        parentNode.replaceChild(wrapper, node);\n        var bounds = (0, _Bounds.parseBounds)(wrapper, scrollX, scrollY);\n        if (wrapper.firstChild) {\n            parentNode.replaceChild(wrapper.firstChild, wrapper);\n        }\n        return bounds;\n    }\n    return new _Bounds.Bounds(0, 0, 0, 0);\n};\n\nvar getRangeBounds = function getRangeBounds(node, offset, length, scrollX, scrollY) {\n    var range = node.ownerDocument.createRange();\n    range.setStart(node, offset);\n    range.setEnd(node, offset + length);\n    return _Bounds.Bounds.fromClientRect(range.getBoundingClientRect(), scrollX, scrollY);\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ForeignObjectRenderer = function () {\n    function ForeignObjectRenderer(element) {\n        _classCallCheck(this, ForeignObjectRenderer);\n\n        this.element = element;\n    }\n\n    _createClass(ForeignObjectRenderer, [{\n        key: 'render',\n        value: function render(options) {\n            var _this = this;\n\n            this.options = options;\n            this.canvas = document.createElement('canvas');\n            this.ctx = this.canvas.getContext('2d');\n            this.canvas.width = Math.floor(options.width) * options.scale;\n            this.canvas.height = Math.floor(options.height) * options.scale;\n            this.canvas.style.width = options.width + 'px';\n            this.canvas.style.height = options.height + 'px';\n\n            options.logger.log('ForeignObject renderer initialized (' + options.width + 'x' + options.height + ' at ' + options.x + ',' + options.y + ') with scale ' + options.scale);\n            var svg = createForeignObjectSVG(Math.max(options.windowWidth, options.width) * options.scale, Math.max(options.windowHeight, options.height) * options.scale, options.scrollX * options.scale, options.scrollY * options.scale, this.element);\n\n            return loadSerializedSVG(svg).then(function (img) {\n                if (options.backgroundColor) {\n                    _this.ctx.fillStyle = options.backgroundColor.toString();\n                    _this.ctx.fillRect(0, 0, options.width * options.scale, options.height * options.scale);\n                }\n\n                _this.ctx.drawImage(img, -options.x * options.scale, -options.y * options.scale);\n                return _this.canvas;\n            });\n        }\n    }]);\n\n    return ForeignObjectRenderer;\n}();\n\nexports.default = ForeignObjectRenderer;\nvar createForeignObjectSVG = exports.createForeignObjectSVG = function createForeignObjectSVG(width, height, x, y, node) {\n    var xmlns = 'http://www.w3.org/2000/svg';\n    var svg = document.createElementNS(xmlns, 'svg');\n    var foreignObject = document.createElementNS(xmlns, 'foreignObject');\n    svg.setAttributeNS(null, 'width', width);\n    svg.setAttributeNS(null, 'height', height);\n\n    foreignObject.setAttributeNS(null, 'width', '100%');\n    foreignObject.setAttributeNS(null, 'height', '100%');\n    foreignObject.setAttributeNS(null, 'x', x);\n    foreignObject.setAttributeNS(null, 'y', y);\n    foreignObject.setAttributeNS(null, 'externalResourcesRequired', 'true');\n    svg.appendChild(foreignObject);\n\n    foreignObject.appendChild(node);\n\n    return svg;\n};\n\nvar loadSerializedSVG = exports.loadSerializedSVG = function loadSerializedSVG(svg) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n\n        img.src = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(new XMLSerializer().serializeToString(svg));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.breakWords = exports.fromCodePoint = exports.toCodePoints = undefined;\n\nvar _cssLineBreak = require('css-line-break');\n\nObject.defineProperty(exports, 'toCodePoints', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.toCodePoints;\n    }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n    enumerable: true,\n    get: function get() {\n        return _cssLineBreak.fromCodePoint;\n    }\n});\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _overflowWrap = require('./parsing/overflowWrap');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar breakWords = exports.breakWords = function breakWords(str, parent) {\n    var breaker = (0, _cssLineBreak.LineBreaker)(str, {\n        lineBreak: parent.style.lineBreak,\n        wordBreak: parent.style.overflowWrap === _overflowWrap.OVERFLOW_WRAP.BREAK_WORD ? 'break-word' : parent.style.wordBreak\n    });\n\n    var words = [];\n    var bk = void 0;\n\n    while (!(bk = breaker.next()).done) {\n        words.push(bk.value.slice());\n    }\n\n    return words;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.FontMetrics = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SAMPLE_TEXT = 'Hidden Text';\n\nvar FontMetrics = exports.FontMetrics = function () {\n    function FontMetrics(document) {\n        _classCallCheck(this, FontMetrics);\n\n        this._data = {};\n        this._document = document;\n    }\n\n    _createClass(FontMetrics, [{\n        key: '_parseMetrics',\n        value: function _parseMetrics(font) {\n            var container = this._document.createElement('div');\n            var img = this._document.createElement('img');\n            var span = this._document.createElement('span');\n\n            var body = this._document.body;\n            if (!body) {\n                throw new Error(process.env.NODE_ENV !== 'production' ? 'No document found for font metrics' : '');\n            }\n\n            container.style.visibility = 'hidden';\n            container.style.fontFamily = font.fontFamily;\n            container.style.fontSize = font.fontSize;\n            container.style.margin = '0';\n            container.style.padding = '0';\n\n            body.appendChild(container);\n\n            img.src = _Util.SMALL_IMAGE;\n            img.width = 1;\n            img.height = 1;\n\n            img.style.margin = '0';\n            img.style.padding = '0';\n            img.style.verticalAlign = 'baseline';\n\n            span.style.fontFamily = font.fontFamily;\n            span.style.fontSize = font.fontSize;\n            span.style.margin = '0';\n            span.style.padding = '0';\n\n            span.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n            container.appendChild(span);\n            container.appendChild(img);\n            var baseline = img.offsetTop - span.offsetTop + 2;\n\n            container.removeChild(span);\n            container.appendChild(this._document.createTextNode(SAMPLE_TEXT));\n\n            container.style.lineHeight = 'normal';\n            img.style.verticalAlign = 'super';\n\n            var middle = img.offsetTop - container.offsetTop + 2;\n\n            body.removeChild(container);\n\n            return { baseline: baseline, middle: middle };\n        }\n    }, {\n        key: 'getMetrics',\n        value: function getMetrics(font) {\n            var key = font.fontFamily + ' ' + font.fontSize;\n            if (this._data[key] === undefined) {\n                this._data[key] = this._parseMetrics(font);\n            }\n\n            return this._data[key];\n        }\n    }]);\n\n    return FontMetrics;\n}();", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Proxy = undefined;\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar Proxy = exports.Proxy = function Proxy(src, options) {\n    if (!options.proxy) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'No proxy defined' : null);\n    }\n    var proxy = options.proxy;\n\n    return new Promise(function (resolve, reject) {\n        var responseType = _Feature2.default.SUPPORT_CORS_XHR && _Feature2.default.SUPPORT_RESPONSE_TYPE ? 'blob' : 'text';\n        var xhr = _Feature2.default.SUPPORT_CORS_XHR ? new XMLHttpRequest() : new XDomainRequest();\n        xhr.onload = function () {\n            if (xhr instanceof XMLHttpRequest) {\n                if (xhr.status === 200) {\n                    if (responseType === 'text') {\n                        resolve(xhr.response);\n                    } else {\n                        var reader = new FileReader();\n                        // $FlowFixMe\n                        reader.addEventListener('load', function () {\n                            return resolve(reader.result);\n                        }, false);\n                        // $FlowFixMe\n                        reader.addEventListener('error', function (e) {\n                            return reject(e);\n                        }, false);\n                        reader.readAsDataURL(xhr.response);\n                    }\n                } else {\n                    reject(process.env.NODE_ENV !== 'production' ? 'Failed to proxy resource ' + src.substring(0, 256) + ' with status code ' + xhr.status : '');\n                }\n            } else {\n                resolve(xhr.responseText);\n            }\n        };\n\n        xhr.onerror = reject;\n        xhr.open('GET', proxy + '?url=' + encodeURIComponent(src) + '&responseType=' + responseType);\n\n        if (responseType !== 'text' && xhr instanceof XMLHttpRequest) {\n            xhr.responseType = responseType;\n        }\n\n        if (options.imageTimeout) {\n            var timeout = options.imageTimeout;\n            xhr.timeout = timeout;\n            xhr.ontimeout = function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) proxying ' + src.substring(0, 256) : '');\n            };\n        }\n\n        xhr.send();\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.renderElement = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Logger = require('./Logger');\n\nvar _Logger2 = _interopRequireDefault(_Logger);\n\nvar _NodeParser = require('./NodeParser');\n\nvar _Renderer = require('./Renderer');\n\nvar _Renderer2 = _interopRequireDefault(_Renderer);\n\nvar _ForeignObjectRenderer = require('./renderer/ForeignObjectRenderer');\n\nvar _ForeignObjectRenderer2 = _interopRequireDefault(_ForeignObjectRenderer);\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Bounds = require('./Bounds');\n\nvar _Clone = require('./Clone');\n\nvar _Font = require('./Font');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar renderElement = exports.renderElement = function renderElement(element, options, logger) {\n    var ownerDocument = element.ownerDocument;\n\n    var windowBounds = new _Bounds.Bounds(options.scrollX, options.scrollY, options.windowWidth, options.windowHeight);\n\n    // http://www.w3.org/TR/css3-background/#special-backgrounds\n    var documentBackgroundColor = ownerDocument.documentElement ? new _Color2.default(getComputedStyle(ownerDocument.documentElement).backgroundColor) : _Color.TRANSPARENT;\n    var bodyBackgroundColor = ownerDocument.body ? new _Color2.default(getComputedStyle(ownerDocument.body).backgroundColor) : _Color.TRANSPARENT;\n\n    var backgroundColor = element === ownerDocument.documentElement ? documentBackgroundColor.isTransparent() ? bodyBackgroundColor.isTransparent() ? options.backgroundColor ? new _Color2.default(options.backgroundColor) : null : bodyBackgroundColor : documentBackgroundColor : options.backgroundColor ? new _Color2.default(options.backgroundColor) : null;\n\n    return (options.foreignObjectRendering ? // $FlowFixMe\n    _Feature2.default.SUPPORT_FOREIGNOBJECT_DRAWING : Promise.resolve(false)).then(function (supportForeignObject) {\n        return supportForeignObject ? function (cloner) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using foreignObject rendering');\n            }\n\n            return cloner.inlineFonts(ownerDocument).then(function () {\n                return cloner.resourceLoader.ready();\n            }).then(function () {\n                var renderer = new _ForeignObjectRenderer2.default(cloner.documentElement);\n                return renderer.render({\n                    backgroundColor: backgroundColor,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height,\n                    windowWidth: options.windowWidth,\n                    windowHeight: options.windowHeight,\n                    scrollX: options.scrollX,\n                    scrollY: options.scrollY\n                });\n            });\n        }(new _Clone.DocumentCloner(element, options, logger, true, renderElement)) : (0, _Clone.cloneWindow)(ownerDocument, windowBounds, element, options, logger, renderElement).then(function (_ref) {\n            var _ref2 = _slicedToArray(_ref, 3),\n                container = _ref2[0],\n                clonedElement = _ref2[1],\n                resourceLoader = _ref2[2];\n\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log('Document cloned, using computed rendering');\n            }\n\n            var stack = (0, _NodeParser.NodeParser)(clonedElement, resourceLoader, logger);\n            var clonedDocument = clonedElement.ownerDocument;\n\n            if (backgroundColor === stack.container.style.background.backgroundColor) {\n                stack.container.style.background.backgroundColor = _Color.TRANSPARENT;\n            }\n\n            return resourceLoader.ready().then(function (imageStore) {\n                var fontMetrics = new _Font.FontMetrics(clonedDocument);\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Starting renderer');\n                }\n\n                var renderOptions = {\n                    backgroundColor: backgroundColor,\n                    fontMetrics: fontMetrics,\n                    imageStore: imageStore,\n                    logger: logger,\n                    scale: options.scale,\n                    x: options.x,\n                    y: options.y,\n                    width: options.width,\n                    height: options.height\n                };\n\n                if (Array.isArray(options.target)) {\n                    return Promise.all(options.target.map(function (target) {\n                        var renderer = new _Renderer2.default(target, renderOptions);\n                        return renderer.render(stack);\n                    }));\n                } else {\n                    var renderer = new _Renderer2.default(options.target, renderOptions);\n                    var canvas = renderer.render(stack);\n                    if (options.removeContainer === true) {\n                        if (container.parentNode) {\n                            container.parentNode.removeChild(container);\n                        } else if (process.env.NODE_ENV !== 'production') {\n                            logger.log('Cannot detach cloned iframe as it is not in the DOM anymore');\n                        }\n                    }\n\n                    return canvas;\n                }\n            });\n        });\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.NodeParser = undefined;\n\nvar _StackingContext = require('./StackingContext');\n\nvar _StackingContext2 = _interopRequireDefault(_StackingContext);\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _Input = require('./Input');\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NodeParser = exports.NodeParser = function NodeParser(node, resourceLoader, logger) {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Starting node parsing');\n    }\n\n    var index = 0;\n\n    var container = new _NodeContainer2.default(node, null, resourceLoader, index++);\n    var stack = new _StackingContext2.default(container, null, true);\n\n    parseNodeTree(node, container, stack, resourceLoader, index);\n\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished parsing node tree');\n    }\n\n    return stack;\n};\n\nvar IGNORED_NODE_NAMES = ['SCRIPT', 'HEAD', 'TITLE', 'OBJECT', 'BR', 'OPTION'];\n\nvar parseNodeTree = function parseNodeTree(node, parent, stack, resourceLoader, index) {\n    if (process.env.NODE_ENV !== 'production' && index > 50000) {\n        throw new Error('Recursion error while parsing node tree');\n    }\n\n    for (var childNode = node.firstChild, nextNode; childNode; childNode = nextNode) {\n        nextNode = childNode.nextSibling;\n        var defaultView = childNode.ownerDocument.defaultView;\n        if (childNode instanceof defaultView.Text || childNode instanceof Text || defaultView.parent && childNode instanceof defaultView.parent.Text) {\n            if (childNode.data.trim().length > 0) {\n                parent.childNodes.push(_TextContainer2.default.fromTextNode(childNode, parent));\n            }\n        } else if (childNode instanceof defaultView.HTMLElement || childNode instanceof HTMLElement || defaultView.parent && childNode instanceof defaultView.parent.HTMLElement) {\n            if (IGNORED_NODE_NAMES.indexOf(childNode.nodeName) === -1) {\n                var container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n                if (container.isVisible()) {\n                    if (childNode.tagName === 'INPUT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineInputElement)(childNode, container);\n                    } else if (childNode.tagName === 'TEXTAREA') {\n                        // $FlowFixMe\n                        (0, _Input.inlineTextAreaElement)(childNode, container);\n                    } else if (childNode.tagName === 'SELECT') {\n                        // $FlowFixMe\n                        (0, _Input.inlineSelectElement)(childNode, container);\n                    } else if (container.style.listStyle && container.style.listStyle.listStyleType !== _listStyle.LIST_STYLE_TYPE.NONE) {\n                        (0, _ListItem.inlineListItemElement)(childNode, container, resourceLoader);\n                    }\n\n                    var SHOULD_TRAVERSE_CHILDREN = childNode.tagName !== 'TEXTAREA';\n                    var treatAsRealStackingContext = createsRealStackingContext(container, childNode);\n                    if (treatAsRealStackingContext || createsStackingContext(container)) {\n                        // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                        // which actually create a new stacking context should be considered part of the parent stacking context\n                        var parentStack = treatAsRealStackingContext || container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                        var childStack = new _StackingContext2.default(container, parentStack, treatAsRealStackingContext);\n                        parentStack.contexts.push(childStack);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, childStack, resourceLoader, index);\n                        }\n                    } else {\n                        stack.children.push(container);\n                        if (SHOULD_TRAVERSE_CHILDREN) {\n                            parseNodeTree(childNode, container, stack, resourceLoader, index);\n                        }\n                    }\n                }\n            }\n        } else if (childNode instanceof defaultView.SVGSVGElement || childNode instanceof SVGSVGElement || defaultView.parent && childNode instanceof defaultView.parent.SVGSVGElement) {\n            var _container = new _NodeContainer2.default(childNode, parent, resourceLoader, index++);\n            var _treatAsRealStackingContext = createsRealStackingContext(_container, childNode);\n            if (_treatAsRealStackingContext || createsStackingContext(_container)) {\n                // for treatAsRealStackingContext:false, any positioned descendants and descendants\n                // which actually create a new stacking context should be considered part of the parent stacking context\n                var _parentStack = _treatAsRealStackingContext || _container.isPositioned() ? stack.getRealParentStackingContext() : stack;\n                var _childStack = new _StackingContext2.default(_container, _parentStack, _treatAsRealStackingContext);\n                _parentStack.contexts.push(_childStack);\n            } else {\n                stack.children.push(_container);\n            }\n        }\n    }\n};\n\nvar createsRealStackingContext = function createsRealStackingContext(container, node) {\n    return container.isRootElement() || container.isPositionedWithZIndex() || container.style.opacity < 1 || container.isTransformed() || isBodyWithTransparentRoot(container, node);\n};\n\nvar createsStackingContext = function createsStackingContext(container) {\n    return container.isPositioned() || container.isFloating();\n};\n\nvar isBodyWithTransparentRoot = function isBodyWithTransparentRoot(container, node) {\n    return node.nodeName === 'BODY' && container.parent instanceof _NodeContainer2.default && container.parent.style.background.backgroundColor.isTransparent();\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _position = require('./parsing/position');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar StackingContext = function () {\n    function StackingContext(container, parent, treatAsRealStackingContext) {\n        _classCallCheck(this, StackingContext);\n\n        this.container = container;\n        this.parent = parent;\n        this.contexts = [];\n        this.children = [];\n        this.treatAsRealStackingContext = treatAsRealStackingContext;\n    }\n\n    _createClass(StackingContext, [{\n        key: 'getOpacity',\n        value: function getOpacity() {\n            return this.parent ? this.container.style.opacity * this.parent.getOpacity() : this.container.style.opacity;\n        }\n    }, {\n        key: 'getRealParentStackingContext',\n        value: function getRealParentStackingContext() {\n            return !this.parent || this.treatAsRealStackingContext ? this : this.parent.getRealParentStackingContext();\n        }\n    }]);\n\n    return StackingContext;\n}();\n\nexports.default = StackingContext;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Size = function Size(width, height) {\n    _classCallCheck(this, Size);\n\n    this.width = width;\n    this.height = height;\n};\n\nexports.default = Size;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Path = require('./Path');\n\nvar _Vector = require('./Vector');\n\nvar _Vector2 = _interopRequireDefault(_Vector);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar lerp = function lerp(a, b, t) {\n    return new _Vector2.default(a.x + (b.x - a.x) * t, a.y + (b.y - a.y) * t);\n};\n\nvar BezierCurve = function () {\n    function BezierCurve(start, startControl, endControl, end) {\n        _classCallCheck(this, BezierCurve);\n\n        this.type = _Path.PATH.BEZIER_CURVE;\n        this.start = start;\n        this.startControl = startControl;\n        this.endControl = endControl;\n        this.end = end;\n    }\n\n    _createClass(BezierCurve, [{\n        key: 'subdivide',\n        value: function subdivide(t, firstHalf) {\n            var ab = lerp(this.start, this.startControl, t);\n            var bc = lerp(this.startControl, this.endControl, t);\n            var cd = lerp(this.endControl, this.end, t);\n            var abbc = lerp(ab, bc, t);\n            var bccd = lerp(bc, cd, t);\n            var dest = lerp(abbc, bccd, t);\n            return firstHalf ? new BezierCurve(this.start, ab, abbc, dest) : new BezierCurve(dest, bccd, cd, this.end);\n        }\n    }, {\n        key: 'reverse',\n        value: function reverse() {\n            return new BezierCurve(this.end, this.endControl, this.startControl, this.start);\n        }\n    }]);\n\n    return BezierCurve;\n}();\n\nexports.default = BezierCurve;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseBorderRadius = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top-left', 'top-right', 'bottom-right', 'bottom-left'];\n\nvar parseBorderRadius = exports.parseBorderRadius = function parseBorderRadius(style) {\n    return SIDES.map(function (side) {\n        var value = style.getPropertyValue('border-' + side + '-radius');\n\n        var _value$split$map = value.split(' ').map(_Length2.default.create),\n            _value$split$map2 = _slicedToArray(_value$split$map, 2),\n            horizontal = _value$split$map2[0],\n            vertical = _value$split$map2[1];\n\n        return typeof vertical === 'undefined' ? [horizontal, horizontal] : [horizontal, vertical];\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar DISPLAY = exports.DISPLAY = {\n    NONE: 1 << 0,\n    BLOCK: 1 << 1,\n    INLINE: 1 << 2,\n    RUN_IN: 1 << 3,\n    FLOW: 1 << 4,\n    FLOW_ROOT: 1 << 5,\n    TABLE: 1 << 6,\n    FLEX: 1 << 7,\n    GRID: 1 << 8,\n    RUBY: 1 << 9,\n    SUBGRID: 1 << 10,\n    LIST_ITEM: 1 << 11,\n    TABLE_ROW_GROUP: 1 << 12,\n    TABLE_HEADER_GROUP: 1 << 13,\n    TABLE_FOOTER_GROUP: 1 << 14,\n    TABLE_ROW: 1 << 15,\n    TABLE_CELL: 1 << 16,\n    TABLE_COLUMN_GROUP: 1 << 17,\n    TABLE_COLUMN: 1 << 18,\n    TABLE_CAPTION: 1 << 19,\n    RUBY_BASE: 1 << 20,\n    RUBY_TEXT: 1 << 21,\n    RUBY_BASE_CONTAINER: 1 << 22,\n    RUBY_TEXT_CONTAINER: 1 << 23,\n    CONTENTS: 1 << 24,\n    INLINE_BLOCK: 1 << 25,\n    INLINE_LIST_ITEM: 1 << 26,\n    INLINE_TABLE: 1 << 27,\n    INLINE_FLEX: 1 << 28,\n    INLINE_GRID: 1 << 29\n};\n\nvar parseDisplayValue = function parseDisplayValue(display) {\n    switch (display) {\n        case 'block':\n            return DISPLAY.BLOCK;\n        case 'inline':\n            return DISPLAY.INLINE;\n        case 'run-in':\n            return DISPLAY.RUN_IN;\n        case 'flow':\n            return DISPLAY.FLOW;\n        case 'flow-root':\n            return DISPLAY.FLOW_ROOT;\n        case 'table':\n            return DISPLAY.TABLE;\n        case 'flex':\n            return DISPLAY.FLEX;\n        case 'grid':\n            return DISPLAY.GRID;\n        case 'ruby':\n            return DISPLAY.RUBY;\n        case 'subgrid':\n            return DISPLAY.SUBGRID;\n        case 'list-item':\n            return DISPLAY.LIST_ITEM;\n        case 'table-row-group':\n            return DISPLAY.TABLE_ROW_GROUP;\n        case 'table-header-group':\n            return DISPLAY.TABLE_HEADER_GROUP;\n        case 'table-footer-group':\n            return DISPLAY.TABLE_FOOTER_GROUP;\n        case 'table-row':\n            return DISPLAY.TABLE_ROW;\n        case 'table-cell':\n            return DISPLAY.TABLE_CELL;\n        case 'table-column-group':\n            return DISPLAY.TABLE_COLUMN_GROUP;\n        case 'table-column':\n            return DISPLAY.TABLE_COLUMN;\n        case 'table-caption':\n            return DISPLAY.TABLE_CAPTION;\n        case 'ruby-base':\n            return DISPLAY.RUBY_BASE;\n        case 'ruby-text':\n            return DISPLAY.RUBY_TEXT;\n        case 'ruby-base-container':\n            return DISPLAY.RUBY_BASE_CONTAINER;\n        case 'ruby-text-container':\n            return DISPLAY.RUBY_TEXT_CONTAINER;\n        case 'contents':\n            return DISPLAY.CONTENTS;\n        case 'inline-block':\n            return DISPLAY.INLINE_BLOCK;\n        case 'inline-list-item':\n            return DISPLAY.INLINE_LIST_ITEM;\n        case 'inline-table':\n            return DISPLAY.INLINE_TABLE;\n        case 'inline-flex':\n            return DISPLAY.INLINE_FLEX;\n        case 'inline-grid':\n            return DISPLAY.INLINE_GRID;\n    }\n\n    return DISPLAY.NONE;\n};\n\nvar setDisplayBit = function setDisplayBit(bit, display) {\n    return bit | parseDisplayValue(display);\n};\n\nvar parseDisplay = exports.parseDisplay = function parseDisplay(display) {\n    return display.split(' ').reduce(setDisplayBit, 0);\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar FLOAT = exports.FLOAT = {\n    NONE: 0,\n    LEFT: 1,\n    RIGHT: 2,\n    INLINE_START: 3,\n    INLINE_END: 4\n};\n\nvar parseCSSFloat = exports.parseCSSFloat = function parseCSSFloat(float) {\n    switch (float) {\n        case 'left':\n            return FLOAT.LEFT;\n        case 'right':\n            return FLOAT.RIGHT;\n        case 'inline-start':\n            return FLOAT.INLINE_START;\n        case 'inline-end':\n            return FLOAT.INLINE_END;\n    }\n    return FLOAT.NONE;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\n\nvar parseFontWeight = function parseFontWeight(weight) {\n    switch (weight) {\n        case 'normal':\n            return 400;\n        case 'bold':\n            return 700;\n    }\n\n    var value = parseInt(weight, 10);\n    return isNaN(value) ? 400 : value;\n};\n\nvar parseFont = exports.parseFont = function parseFont(style) {\n    var fontFamily = style.fontFamily;\n    var fontSize = style.fontSize;\n    var fontStyle = style.fontStyle;\n    var fontVariant = style.fontVariant;\n    var fontWeight = parseFontWeight(style.fontWeight);\n\n    return {\n        fontFamily: fontFamily,\n        fontSize: fontSize,\n        fontStyle: fontStyle,\n        fontVariant: fontVariant,\n        fontWeight: fontWeight\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseLetterSpacing = exports.parseLetterSpacing = function parseLetterSpacing(letterSpacing) {\n    if (letterSpacing === 'normal') {\n        return 0;\n    }\n    var value = parseFloat(letterSpacing);\n    return isNaN(value) ? 0 : value;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar LINE_BREAK = exports.LINE_BREAK = {\n    NORMAL: 'normal',\n    STRICT: 'strict'\n};\n\nvar parseLineBreak = exports.parseLineBreak = function parseLineBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'strict':\n            return LINE_BREAK.STRICT;\n        case 'normal':\n        default:\n            return LINE_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseMargin = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar SIDES = ['top', 'right', 'bottom', 'left'];\n\nvar parseMargin = exports.parseMargin = function parseMargin(style) {\n    return SIDES.map(function (side) {\n        return new _Length2.default(style.getPropertyValue('margin-' + side));\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar OVERFLOW = exports.OVERFLOW = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    SCROLL: 2,\n    AUTO: 3\n};\n\nvar parseOverflow = exports.parseOverflow = function parseOverflow(overflow) {\n    switch (overflow) {\n        case 'hidden':\n            return OVERFLOW.HIDDEN;\n        case 'scroll':\n            return OVERFLOW.SCROLL;\n        case 'auto':\n            return OVERFLOW.AUTO;\n        case 'visible':\n        default:\n            return OVERFLOW.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTextShadow = undefined;\n\nvar _Color = require('../Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar NUMBER = /^([+-]|\\d|\\.)$/i;\n\nvar parseTextShadow = exports.parseTextShadow = function parseTextShadow(textShadow) {\n    if (textShadow === 'none' || typeof textShadow !== 'string') {\n        return null;\n    }\n\n    var currentValue = '';\n    var isLength = false;\n    var values = [];\n    var shadows = [];\n    var numParens = 0;\n    var color = null;\n\n    var appendValue = function appendValue() {\n        if (currentValue.length) {\n            if (isLength) {\n                values.push(parseFloat(currentValue));\n            } else {\n                color = new _Color2.default(currentValue);\n            }\n        }\n        isLength = false;\n        currentValue = '';\n    };\n\n    var appendShadow = function appendShadow() {\n        if (values.length && color !== null) {\n            shadows.push({\n                color: color,\n                offsetX: values[0] || 0,\n                offsetY: values[1] || 0,\n                blur: values[2] || 0\n            });\n        }\n        values.splice(0, values.length);\n        color = null;\n    };\n\n    for (var i = 0; i < textShadow.length; i++) {\n        var c = textShadow[i];\n        switch (c) {\n            case '(':\n                currentValue += c;\n                numParens++;\n                break;\n            case ')':\n                currentValue += c;\n                numParens--;\n                break;\n            case ',':\n                if (numParens === 0) {\n                    appendValue();\n                    appendShadow();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            case ' ':\n                if (numParens === 0) {\n                    appendValue();\n                } else {\n                    currentValue += c;\n                }\n                break;\n            default:\n                if (currentValue.length === 0 && NUMBER.test(c)) {\n                    isLength = true;\n                }\n                currentValue += c;\n        }\n    }\n\n    appendValue();\n    appendShadow();\n\n    if (shadows.length === 0) {\n        return null;\n    }\n\n    return shadows;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseTransform = undefined;\n\nvar _Length = require('../Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar toFloat = function toFloat(s) {\n    return parseFloat(s.trim());\n};\n\nvar MATRIX = /(matrix|matrix3d)\\((.+)\\)/;\n\nvar parseTransform = exports.parseTransform = function parseTransform(style) {\n    var transform = parseTransformMatrix(style.transform || style.webkitTransform || style.mozTransform ||\n    // $FlowFixMe\n    style.msTransform ||\n    // $FlowFixMe\n    style.oTransform);\n    if (transform === null) {\n        return null;\n    }\n\n    return {\n        transform: transform,\n        transformOrigin: parseTransformOrigin(style.transformOrigin || style.webkitTransformOrigin || style.mozTransformOrigin ||\n        // $FlowFixMe\n        style.msTransformOrigin ||\n        // $FlowFixMe\n        style.oTransformOrigin)\n    };\n};\n\n// $FlowFixMe\nvar parseTransformOrigin = function parseTransformOrigin(origin) {\n    if (typeof origin !== 'string') {\n        var v = new _Length2.default('0');\n        return [v, v];\n    }\n    var values = origin.split(' ').map(_Length2.default.create);\n    return [values[0], values[1]];\n};\n\n// $FlowFixMe\nvar parseTransformMatrix = function parseTransformMatrix(transform) {\n    if (transform === 'none' || typeof transform !== 'string') {\n        return null;\n    }\n\n    var match = transform.match(MATRIX);\n    if (match) {\n        if (match[1] === 'matrix') {\n            var matrix = match[2].split(',').map(toFloat);\n            return [matrix[0], matrix[1], matrix[2], matrix[3], matrix[4], matrix[5]];\n        } else {\n            var matrix3d = match[2].split(',').map(toFloat);\n            return [matrix3d[0], matrix3d[1], matrix3d[4], matrix3d[5], matrix3d[12], matrix3d[13]];\n        }\n    }\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar VISIBILITY = exports.VISIBILITY = {\n    VISIBLE: 0,\n    HIDDEN: 1,\n    COLLAPSE: 2\n};\n\nvar parseVisibility = exports.parseVisibility = function parseVisibility(visibility) {\n    switch (visibility) {\n        case 'hidden':\n            return VISIBILITY.HIDDEN;\n        case 'collapse':\n            return VISIBILITY.COLLAPSE;\n        case 'visible':\n        default:\n            return VISIBILITY.VISIBLE;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar WORD_BREAK = exports.WORD_BREAK = {\n    NORMAL: 'normal',\n    BREAK_ALL: 'break-all',\n    KEEP_ALL: 'keep-all'\n};\n\nvar parseWordBreak = exports.parseWordBreak = function parseWordBreak(wordBreak) {\n    switch (wordBreak) {\n        case 'break-all':\n            return WORD_BREAK.BREAK_ALL;\n        case 'keep-all':\n            return WORD_BREAK.KEEP_ALL;\n        case 'normal':\n        default:\n            return WORD_BREAK.NORMAL;\n    }\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar parseZIndex = exports.parseZIndex = function parseZIndex(zIndex) {\n    var auto = zIndex === 'auto';\n    return {\n        auto: auto,\n        order: auto ? 0 : parseInt(zIndex, 10)\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _Util = require('./Util');\n\nObject.defineProperty(exports, 'toCodePoints', {\n  enumerable: true,\n  get: function get() {\n    return _Util.toCodePoints;\n  }\n});\nObject.defineProperty(exports, 'fromCodePoint', {\n  enumerable: true,\n  get: function get() {\n    return _Util.fromCodePoint;\n  }\n});\n\nvar _LineBreak = require('./LineBreak');\n\nObject.defineProperty(exports, 'LineBreaker', {\n  enumerable: true,\n  get: function get() {\n    return _LineBreak.LineBreaker;\n  }\n});", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.LineBreaker = exports.inlineBreakOpportunities = exports.lineBreakAtIndex = exports.codePointsToCharacterClasses = exports.UnicodeTrie = exports.BREAK_ALLOWED = exports.BREAK_NOT_ALLOWED = exports.BREAK_MANDATORY = exports.classes = exports.LETTER_NUMBER_MODIFIER = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _Trie = require('./Trie');\n\nvar _linebreakTrie = require('./linebreak-trie');\n\nvar _linebreakTrie2 = _interopRequireDefault(_linebreakTrie);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _toConsumableArray(arr) { if (Array.isArray(arr)) { for (var i = 0, arr2 = Array(arr.length); i < arr.length; i++) { arr2[i] = arr[i]; } return arr2; } else { return Array.from(arr); } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar LETTER_NUMBER_MODIFIER = exports.LETTER_NUMBER_MODIFIER = 50;\n\n// Non-tailorable Line Breaking Classes\nvar BK = 1; //  Cause a line break (after)\nvar CR = 2; //  Cause a line break (after), except between CR and LF\nvar LF = 3; //  Cause a line break (after)\nvar CM = 4; //  Prohibit a line break between the character and the preceding character\nvar NL = 5; //  Cause a line break (after)\nvar SG = 6; //  Do not occur in well-formed text\nvar WJ = 7; //  Prohibit line breaks before and after\nvar ZW = 8; //  Provide a break opportunity\nvar GL = 9; //  Prohibit line breaks before and after\nvar SP = 10; // Enable indirect line breaks\nvar ZWJ = 11; // Prohibit line breaks within joiner sequences\n// Break Opportunities\nvar B2 = 12; //  Provide a line break opportunity before and after the character\nvar BA = 13; //  Generally provide a line break opportunity after the character\nvar BB = 14; //  Generally provide a line break opportunity before the character\nvar HY = 15; //  Provide a line break opportunity after the character, except in numeric context\nvar CB = 16; //   Provide a line break opportunity contingent on additional information\n// Characters Prohibiting Certain Breaks\nvar CL = 17; //  Prohibit line breaks before\nvar CP = 18; //  Prohibit line breaks before\nvar EX = 19; //  Prohibit line breaks before\nvar IN = 20; //  Allow only indirect line breaks between pairs\nvar NS = 21; //  Allow only indirect line breaks before\nvar OP = 22; //  Prohibit line breaks after\nvar QU = 23; //  Act like they are both opening and closing\n// Numeric Context\nvar IS = 24; //  Prevent breaks after any and before numeric\nvar NU = 25; //  Form numeric expressions for line breaking purposes\nvar PO = 26; //  Do not break following a numeric expression\nvar PR = 27; //  Do not break in front of a numeric expression\nvar SY = 28; //  Prevent a break before; and allow a break after\n// Other Characters\nvar AI = 29; //  Act like AL when the resolvedEAW is N; otherwise; act as ID\nvar AL = 30; //  Are alphabetic characters or symbols that are used with alphabetic characters\nvar CJ = 31; //  Treat as NS or ID for strict or normal breaking.\nvar EB = 32; //  Do not break from following Emoji Modifier\nvar EM = 33; //  Do not break from preceding Emoji Base\nvar H2 = 34; //  Form Korean syllable blocks\nvar H3 = 35; //  Form Korean syllable blocks\nvar HL = 36; //  Do not break around a following hyphen; otherwise act as Alphabetic\nvar ID = 37; //  Break before or after; except in some numeric context\nvar JL = 38; //  Form Korean syllable blocks\nvar JV = 39; //  Form Korean syllable blocks\nvar JT = 40; //  Form Korean syllable blocks\nvar RI = 41; //  Keep pairs together. For pairs; break before and after other classes\nvar SA = 42; //  Provide a line break opportunity contingent on additional, language-specific context analysis\nvar XX = 43; //  Have as yet unknown line breaking behavior or unassigned code positions\n\nvar classes = exports.classes = {\n    BK: BK,\n    CR: CR,\n    LF: LF,\n    CM: CM,\n    NL: NL,\n    SG: SG,\n    WJ: WJ,\n    ZW: ZW,\n    GL: GL,\n    SP: SP,\n    ZWJ: ZWJ,\n    B2: B2,\n    BA: BA,\n    BB: BB,\n    HY: HY,\n    CB: CB,\n    CL: CL,\n    CP: CP,\n    EX: EX,\n    IN: IN,\n    NS: NS,\n    OP: OP,\n    QU: QU,\n    IS: IS,\n    NU: NU,\n    PO: PO,\n    PR: PR,\n    SY: SY,\n    AI: AI,\n    AL: AL,\n    CJ: CJ,\n    EB: EB,\n    EM: EM,\n    H2: H2,\n    H3: H3,\n    HL: HL,\n    ID: ID,\n    JL: JL,\n    JV: JV,\n    JT: JT,\n    RI: RI,\n    SA: SA,\n    XX: XX\n};\n\nvar BREAK_MANDATORY = exports.BREAK_MANDATORY = '!';\nvar BREAK_NOT_ALLOWED = exports.BREAK_NOT_ALLOWED = '×';\nvar BREAK_ALLOWED = exports.BREAK_ALLOWED = '÷';\nvar UnicodeTrie = exports.UnicodeTrie = (0, _Trie.createTrieFromBase64)(_linebreakTrie2.default);\n\nvar ALPHABETICS = [AL, HL];\nvar HARD_LINE_BREAKS = [BK, CR, LF, NL];\nvar SPACE = [SP, ZW];\nvar PREFIX_POSTFIX = [PR, PO];\nvar LINE_BREAKS = HARD_LINE_BREAKS.concat(SPACE);\nvar KOREAN_SYLLABLE_BLOCK = [JL, JV, JT, H2, H3];\nvar HYPHEN = [HY, BA];\n\nvar codePointsToCharacterClasses = exports.codePointsToCharacterClasses = function codePointsToCharacterClasses(codePoints) {\n    var lineBreak = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'strict';\n\n    var types = [];\n    var indicies = [];\n    var categories = [];\n    codePoints.forEach(function (codePoint, index) {\n        var classType = UnicodeTrie.get(codePoint);\n        if (classType > LETTER_NUMBER_MODIFIER) {\n            categories.push(true);\n            classType -= LETTER_NUMBER_MODIFIER;\n        } else {\n            categories.push(false);\n        }\n\n        if (['normal', 'auto', 'loose'].indexOf(lineBreak) !== -1) {\n            // U+2010, – U+2013, 〜 U+301C, ゠ U+30A0\n            if ([0x2010, 0x2013, 0x301c, 0x30a0].indexOf(codePoint) !== -1) {\n                indicies.push(index);\n                return types.push(CB);\n            }\n        }\n\n        if (classType === CM || classType === ZWJ) {\n            // LB10 Treat any remaining combining mark or ZWJ as AL.\n            if (index === 0) {\n                indicies.push(index);\n                return types.push(AL);\n            }\n\n            // LB9 Do not break a combining character sequence; treat it as if it has the line breaking class of\n            // the base character in all of the following rules. Treat ZWJ as if it were CM.\n            var prev = types[index - 1];\n            if (LINE_BREAKS.indexOf(prev) === -1) {\n                indicies.push(indicies[index - 1]);\n                return types.push(prev);\n            }\n            indicies.push(index);\n            return types.push(AL);\n        }\n\n        indicies.push(index);\n\n        if (classType === CJ) {\n            return types.push(lineBreak === 'strict' ? NS : ID);\n        }\n\n        if (classType === SA) {\n            return types.push(AL);\n        }\n\n        if (classType === AI) {\n            return types.push(AL);\n        }\n\n        // For supplementary characters, a useful default is to treat characters in the range 10000..1FFFD as AL\n        // and characters in the ranges 20000..2FFFD and 30000..3FFFD as ID, until the implementation can be revised\n        // to take into account the actual line breaking properties for these characters.\n        if (classType === XX) {\n            if (codePoint >= 0x20000 && codePoint <= 0x2fffd || codePoint >= 0x30000 && codePoint <= 0x3fffd) {\n                return types.push(ID);\n            } else {\n                return types.push(AL);\n            }\n        }\n\n        types.push(classType);\n    });\n\n    return [indicies, types, categories];\n};\n\nvar isAdjacentWithSpaceIgnored = function isAdjacentWithSpaceIgnored(a, b, currentIndex, classTypes) {\n    var current = classTypes[currentIndex];\n    if (Array.isArray(a) ? a.indexOf(current) !== -1 : a === current) {\n        var i = currentIndex;\n        while (i <= classTypes.length) {\n            i++;\n            var next = classTypes[i];\n\n            if (next === b) {\n                return true;\n            }\n\n            if (next !== SP) {\n                break;\n            }\n        }\n    }\n\n    if (current === SP) {\n        var _i = currentIndex;\n\n        while (_i > 0) {\n            _i--;\n            var prev = classTypes[_i];\n\n            if (Array.isArray(a) ? a.indexOf(prev) !== -1 : a === prev) {\n                var n = currentIndex;\n                while (n <= classTypes.length) {\n                    n++;\n                    var _next = classTypes[n];\n\n                    if (_next === b) {\n                        return true;\n                    }\n\n                    if (_next !== SP) {\n                        break;\n                    }\n                }\n            }\n\n            if (prev !== SP) {\n                break;\n            }\n        }\n    }\n    return false;\n};\n\nvar previousNonSpaceClassType = function previousNonSpaceClassType(currentIndex, classTypes) {\n    var i = currentIndex;\n    while (i >= 0) {\n        var type = classTypes[i];\n        if (type === SP) {\n            i--;\n        } else {\n            return type;\n        }\n    }\n    return 0;\n};\n\nvar _lineBreakAtIndex = function _lineBreakAtIndex(codePoints, classTypes, indicies, index, forbiddenBreaks) {\n    if (indicies[index] === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var currentIndex = index - 1;\n    if (Array.isArray(forbiddenBreaks) && forbiddenBreaks[currentIndex] === true) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    var beforeIndex = currentIndex - 1;\n    var afterIndex = currentIndex + 1;\n    var current = classTypes[currentIndex];\n\n    // LB4 Always break after hard line breaks.\n    // LB5 Treat CR followed by LF, as well as CR, LF, and NL as hard line breaks.\n    var before = beforeIndex >= 0 ? classTypes[beforeIndex] : 0;\n    var next = classTypes[afterIndex];\n\n    if (current === CR && next === LF) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    if (HARD_LINE_BREAKS.indexOf(current) !== -1) {\n        return BREAK_MANDATORY;\n    }\n\n    // LB6 Do not break before hard line breaks.\n    if (HARD_LINE_BREAKS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB7 Do not break before spaces or zero width space.\n    if (SPACE.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB8 Break before any character following a zero-width space, even if one or more spaces intervene.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === ZW) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB8a Do not break between a zero width joiner and an ideograph, emoji base or emoji modifier.\n    if (UnicodeTrie.get(codePoints[currentIndex]) === ZWJ && (next === ID || next === EB || next === EM)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB11 Do not break before or after Word joiner and related characters.\n    if (current === WJ || next === WJ) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12 Do not break after NBSP and related characters.\n    if (current === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB12a Do not break before NBSP and related characters, except after spaces and hyphens.\n    if ([SP, BA, HY].indexOf(current) === -1 && next === GL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB13 Do not break before ‘]’ or ‘!’ or ‘;’ or ‘/’, even after spaces.\n    if ([CL, CP, EX, IS, SY].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB14 Do not break after ‘[’, even after spaces.\n    if (previousNonSpaceClassType(currentIndex, classTypes) === OP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB15 Do not break within ‘”[’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(QU, OP, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB16 Do not break between closing punctuation and a nonstarter (lb=NS), even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored([CL, CP], NS, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB17 Do not break within ‘——’, even with intervening spaces.\n    if (isAdjacentWithSpaceIgnored(B2, B2, currentIndex, classTypes)) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB18 Break after spaces.\n    if (current === SP) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB19 Do not break before or after quotation marks, such as ‘ ” ’.\n    if (current === QU || next === QU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB20 Break before and after unresolved CB.\n    if (next === CB || current === CB) {\n        return BREAK_ALLOWED;\n    }\n\n    // LB21 Do not break before hyphen-minus, other hyphens, fixed-width spaces, small kana, and other non-starters, or after acute accents.\n    if ([BA, HY, NS].indexOf(next) !== -1 || current === BB) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21a Don't break after Hebrew + Hyphen.\n    if (before === HL && HYPHEN.indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB21b Don’t break between Solidus and Hebrew letters.\n    if (current === SY && next === HL) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB22 Do not break between two ellipses, or between letters, numbers or exclamations and ellipsis.\n    if (next === IN && ALPHABETICS.concat(IN, EX, NU, ID, EB, EM).indexOf(current) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23 Do not break between digits and letters.\n    if (ALPHABETICS.indexOf(next) !== -1 && current === NU || ALPHABETICS.indexOf(current) !== -1 && next === NU) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB23a Do not break between numeric prefixes and ideographs, or between ideographs and numeric postfixes.\n    if (current === PR && [ID, EB, EM].indexOf(next) !== -1 || [ID, EB, EM].indexOf(current) !== -1 && next === PO) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB24 Do not break between numeric prefix/postfix and letters, or between letters and prefix/postfix.\n    if (ALPHABETICS.indexOf(current) !== -1 && PREFIX_POSTFIX.indexOf(next) !== -1 || PREFIX_POSTFIX.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB25 Do not break between the following pairs of classes relevant to numbers:\n    if (\n    // (PR | PO) × ( OP | HY )? NU\n    [PR, PO].indexOf(current) !== -1 && (next === NU || [OP, HY].indexOf(next) !== -1 && classTypes[afterIndex + 1] === NU) ||\n    // ( OP | HY ) × NU\n    [OP, HY].indexOf(current) !== -1 && next === NU ||\n    // NU ×\t(NU | SY | IS)\n    current === NU && [NU, SY, IS].indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // NU (NU | SY | IS)* × (NU | SY | IS | CL | CP)\n    if ([NU, SY, IS, CL, CP].indexOf(next) !== -1) {\n        var prevIndex = currentIndex;\n        while (prevIndex >= 0) {\n            var type = classTypes[prevIndex];\n            if (type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(type) !== -1) {\n                prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // NU (NU | SY | IS)* (CL | CP)? × (PO | PR))\n    if ([PR, PO].indexOf(next) !== -1) {\n        var _prevIndex = [CL, CP].indexOf(current) !== -1 ? beforeIndex : currentIndex;\n        while (_prevIndex >= 0) {\n            var _type = classTypes[_prevIndex];\n            if (_type === NU) {\n                return BREAK_NOT_ALLOWED;\n            } else if ([SY, IS].indexOf(_type) !== -1) {\n                _prevIndex--;\n            } else {\n                break;\n            }\n        }\n    }\n\n    // LB26 Do not break a Korean syllable.\n    if (JL === current && [JL, JV, H2, H3].indexOf(next) !== -1 || [JV, H2].indexOf(current) !== -1 && [JV, JT].indexOf(next) !== -1 || [JT, H3].indexOf(current) !== -1 && next === JT) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB27 Treat a Korean Syllable Block the same as ID.\n    if (KOREAN_SYLLABLE_BLOCK.indexOf(current) !== -1 && [IN, PO].indexOf(next) !== -1 || KOREAN_SYLLABLE_BLOCK.indexOf(next) !== -1 && current === PR) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB28 Do not break between alphabetics (“at”).\n    if (ALPHABETICS.indexOf(current) !== -1 && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB29 Do not break between numeric punctuation and alphabetics (“e.g.”).\n    if (current === IS && ALPHABETICS.indexOf(next) !== -1) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30 Do not break between letters, numbers, or ordinary symbols and opening or closing parentheses.\n    if (ALPHABETICS.concat(NU).indexOf(current) !== -1 && next === OP || ALPHABETICS.concat(NU).indexOf(next) !== -1 && current === CP) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB30a Break between two regional indicator symbols if and only if there are an even number of regional\n    // indicators preceding the position of the break.\n    if (current === RI && next === RI) {\n        var i = indicies[currentIndex];\n        var count = 1;\n        while (i > 0) {\n            i--;\n            if (classTypes[i] === RI) {\n                count++;\n            } else {\n                break;\n            }\n        }\n        if (count % 2 !== 0) {\n            return BREAK_NOT_ALLOWED;\n        }\n    }\n\n    // LB30b Do not break between an emoji base and an emoji modifier.\n    if (current === EB && next === EM) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    return BREAK_ALLOWED;\n};\n\nvar lineBreakAtIndex = exports.lineBreakAtIndex = function lineBreakAtIndex(codePoints, index) {\n    // LB2 Never break at the start of text.\n    if (index === 0) {\n        return BREAK_NOT_ALLOWED;\n    }\n\n    // LB3 Always break at the end of text.\n    if (index >= codePoints.length) {\n        return BREAK_MANDATORY;\n    }\n\n    var _codePointsToCharacte = codePointsToCharacterClasses(codePoints),\n        _codePointsToCharacte2 = _slicedToArray(_codePointsToCharacte, 2),\n        indicies = _codePointsToCharacte2[0],\n        classTypes = _codePointsToCharacte2[1];\n\n    return _lineBreakAtIndex(codePoints, classTypes, indicies, index);\n};\n\nvar cssFormattedClasses = function cssFormattedClasses(codePoints, options) {\n    if (!options) {\n        options = { lineBreak: 'normal', wordBreak: 'normal' };\n    }\n\n    var _codePointsToCharacte3 = codePointsToCharacterClasses(codePoints, options.lineBreak),\n        _codePointsToCharacte4 = _slicedToArray(_codePointsToCharacte3, 3),\n        indicies = _codePointsToCharacte4[0],\n        classTypes = _codePointsToCharacte4[1],\n        isLetterNumber = _codePointsToCharacte4[2];\n\n    if (options.wordBreak === 'break-all' || options.wordBreak === 'break-word') {\n        classTypes = classTypes.map(function (type) {\n            return [NU, AL, SA].indexOf(type) !== -1 ? ID : type;\n        });\n    }\n\n    var forbiddenBreakpoints = options.wordBreak === 'keep-all' ? isLetterNumber.map(function (isLetterNumber, i) {\n        return isLetterNumber && codePoints[i] >= 0x4e00 && codePoints[i] <= 0x9fff;\n    }) : null;\n\n    return [indicies, classTypes, forbiddenBreakpoints];\n};\n\nvar inlineBreakOpportunities = exports.inlineBreakOpportunities = function inlineBreakOpportunities(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n    var output = BREAK_NOT_ALLOWED;\n\n    var _cssFormattedClasses = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses2 = _slicedToArray(_cssFormattedClasses, 3),\n        indicies = _cssFormattedClasses2[0],\n        classTypes = _cssFormattedClasses2[1],\n        forbiddenBreakpoints = _cssFormattedClasses2[2];\n\n    codePoints.forEach(function (codePoint, i) {\n        output += (0, _Util.fromCodePoint)(codePoint) + (i >= codePoints.length - 1 ? BREAK_MANDATORY : _lineBreakAtIndex(codePoints, classTypes, indicies, i + 1, forbiddenBreakpoints));\n    });\n\n    return output;\n};\n\nvar Break = function () {\n    function Break(codePoints, lineBreak, start, end) {\n        _classCallCheck(this, Break);\n\n        this._codePoints = codePoints;\n        this.required = lineBreak === BREAK_MANDATORY;\n        this.start = start;\n        this.end = end;\n    }\n\n    _createClass(Break, [{\n        key: 'slice',\n        value: function slice() {\n            return _Util.fromCodePoint.apply(undefined, _toConsumableArray(this._codePoints.slice(this.start, this.end)));\n        }\n    }]);\n\n    return Break;\n}();\n\nvar LineBreaker = exports.LineBreaker = function LineBreaker(str, options) {\n    var codePoints = (0, _Util.toCodePoints)(str);\n\n    var _cssFormattedClasses3 = cssFormattedClasses(codePoints, options),\n        _cssFormattedClasses4 = _slicedToArray(_cssFormattedClasses3, 3),\n        indicies = _cssFormattedClasses4[0],\n        classTypes = _cssFormattedClasses4[1],\n        forbiddenBreakpoints = _cssFormattedClasses4[2];\n\n    var length = codePoints.length;\n    var lastEnd = 0;\n    var nextIndex = 0;\n\n    return {\n        next: function next() {\n            if (nextIndex >= length) {\n                return { done: true };\n            }\n            var lineBreak = BREAK_NOT_ALLOWED;\n            while (nextIndex < length && (lineBreak = _lineBreakAtIndex(codePoints, classTypes, indicies, ++nextIndex, forbiddenBreakpoints)) === BREAK_NOT_ALLOWED) {}\n\n            if (lineBreak !== BREAK_NOT_ALLOWED || nextIndex === length) {\n                var value = new Break(codePoints, lineBreak, lastEnd, nextIndex);\n                lastEnd = nextIndex;\n                return { value: value, done: false };\n            }\n\n            return { done: true };\n        }\n    };\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.Trie = exports.createTrieFromBase64 = exports.UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_DATA_MASK = exports.UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_SHIFT_1_2 = exports.UTRIE2_INDEX_SHIFT = exports.UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_2 = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Util = require('./Util');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n/** Shift size for getting the index-2 table offset. */\nvar UTRIE2_SHIFT_2 = exports.UTRIE2_SHIFT_2 = 5;\n\n/** Shift size for getting the index-1 table offset. */\nvar UTRIE2_SHIFT_1 = exports.UTRIE2_SHIFT_1 = 6 + 5;\n\n/**\n * Shift size for shifting left the index array values.\n * Increases possible data size with 16-bit index values at the cost\n * of compactability.\n * This requires data blocks to be aligned by UTRIE2_DATA_GRANULARITY.\n */\nvar UTRIE2_INDEX_SHIFT = exports.UTRIE2_INDEX_SHIFT = 2;\n\n/**\n * Difference between the two shift sizes,\n * for getting an index-1 offset from an index-2 offset. 6=11-5\n */\nvar UTRIE2_SHIFT_1_2 = exports.UTRIE2_SHIFT_1_2 = UTRIE2_SHIFT_1 - UTRIE2_SHIFT_2;\n\n/**\n * The part of the index-2 table for U+D800..U+DBFF stores values for\n * lead surrogate code _units_ not code _points_.\n * Values for lead surrogate code _points_ are indexed with this portion of the table.\n * Length=32=0x20=0x400>>UTRIE2_SHIFT_2. (There are 1024=0x400 lead surrogates.)\n */\nvar UTRIE2_LSCP_INDEX_2_OFFSET = exports.UTRIE2_LSCP_INDEX_2_OFFSET = 0x10000 >> UTRIE2_SHIFT_2;\n\n/** Number of entries in a data block. 32=0x20 */\nvar UTRIE2_DATA_BLOCK_LENGTH = exports.UTRIE2_DATA_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_2;\n/** Mask for getting the lower bits for the in-data-block offset. */\nvar UTRIE2_DATA_MASK = exports.UTRIE2_DATA_MASK = UTRIE2_DATA_BLOCK_LENGTH - 1;\n\nvar UTRIE2_LSCP_INDEX_2_LENGTH = exports.UTRIE2_LSCP_INDEX_2_LENGTH = 0x400 >> UTRIE2_SHIFT_2;\n/** Count the lengths of both BMP pieces. 2080=0x820 */\nvar UTRIE2_INDEX_2_BMP_LENGTH = exports.UTRIE2_INDEX_2_BMP_LENGTH = UTRIE2_LSCP_INDEX_2_OFFSET + UTRIE2_LSCP_INDEX_2_LENGTH;\n/**\n * The 2-byte UTF-8 version of the index-2 table follows at offset 2080=0x820.\n * Length 32=0x20 for lead bytes C0..DF, regardless of UTRIE2_SHIFT_2.\n */\nvar UTRIE2_UTF8_2B_INDEX_2_OFFSET = exports.UTRIE2_UTF8_2B_INDEX_2_OFFSET = UTRIE2_INDEX_2_BMP_LENGTH;\nvar UTRIE2_UTF8_2B_INDEX_2_LENGTH = exports.UTRIE2_UTF8_2B_INDEX_2_LENGTH = 0x800 >> 6; /* U+0800 is the first code point after 2-byte UTF-8 */\n/**\n * The index-1 table, only used for supplementary code points, at offset 2112=0x840.\n * Variable length, for code points up to highStart, where the last single-value range starts.\n * Maximum length 512=0x200=0x100000>>UTRIE2_SHIFT_1.\n * (For 0x100000 supplementary code points U+10000..U+10ffff.)\n *\n * The part of the index-2 table for supplementary code points starts\n * after this index-1 table.\n *\n * Both the index-1 table and the following part of the index-2 table\n * are omitted completely if there is only BMP data.\n */\nvar UTRIE2_INDEX_1_OFFSET = exports.UTRIE2_INDEX_1_OFFSET = UTRIE2_UTF8_2B_INDEX_2_OFFSET + UTRIE2_UTF8_2B_INDEX_2_LENGTH;\n\n/**\n * Number of index-1 entries for the BMP. 32=0x20\n * This part of the index-1 table is omitted from the serialized form.\n */\nvar UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = exports.UTRIE2_OMITTED_BMP_INDEX_1_LENGTH = 0x10000 >> UTRIE2_SHIFT_1;\n\n/** Number of entries in an index-2 block. 64=0x40 */\nvar UTRIE2_INDEX_2_BLOCK_LENGTH = exports.UTRIE2_INDEX_2_BLOCK_LENGTH = 1 << UTRIE2_SHIFT_1_2;\n/** Mask for getting the lower bits for the in-index-2-block offset. */\nvar UTRIE2_INDEX_2_MASK = exports.UTRIE2_INDEX_2_MASK = UTRIE2_INDEX_2_BLOCK_LENGTH - 1;\n\nvar createTrieFromBase64 = exports.createTrieFromBase64 = function createTrieFromBase64(base64) {\n    var buffer = (0, _Util.decode)(base64);\n    var view32 = Array.isArray(buffer) ? (0, _Util.polyUint32Array)(buffer) : new Uint32Array(buffer);\n    var view16 = Array.isArray(buffer) ? (0, _Util.polyUint16Array)(buffer) : new Uint16Array(buffer);\n    var headerLength = 24;\n\n    var index = view16.slice(headerLength / 2, view32[4] / 2);\n    var data = view32[5] === 2 ? view16.slice((headerLength + view32[4]) / 2) : view32.slice(Math.ceil((headerLength + view32[4]) / 4));\n\n    return new Trie(view32[0], view32[1], view32[2], view32[3], index, data);\n};\n\nvar Trie = exports.Trie = function () {\n    function Trie(initialValue, errorValue, highStart, highValueIndex, index, data) {\n        _classCallCheck(this, Trie);\n\n        this.initialValue = initialValue;\n        this.errorValue = errorValue;\n        this.highStart = highStart;\n        this.highValueIndex = highValueIndex;\n        this.index = index;\n        this.data = data;\n    }\n\n    /**\n     * Get the value for a code point as stored in the Trie.\n     *\n     * @param codePoint the code point\n     * @return the value\n     */\n\n\n    _createClass(Trie, [{\n        key: 'get',\n        value: function get(codePoint) {\n            var ix = void 0;\n            if (codePoint >= 0) {\n                if (codePoint < 0x0d800 || codePoint > 0x0dbff && codePoint <= 0x0ffff) {\n                    // Ordinary BMP code point, excluding leading surrogates.\n                    // BMP uses a single level lookup.  BMP index starts at offset 0 in the Trie2 index.\n                    // 16 bit data is stored in the index array itself.\n                    ix = this.index[codePoint >> UTRIE2_SHIFT_2];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint <= 0xffff) {\n                    // Lead Surrogate Code Point.  A Separate index section is stored for\n                    // lead surrogate code units and code points.\n                    //   The main index has the code unit data.\n                    //   For this function, we need the code point data.\n                    // Note: this expression could be refactored for slightly improved efficiency, but\n                    //       surrogate code points will be so rare in practice that it's not worth it.\n                    ix = this.index[UTRIE2_LSCP_INDEX_2_OFFSET + (codePoint - 0xd800 >> UTRIE2_SHIFT_2)];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n\n                if (codePoint < this.highStart) {\n                    // Supplemental code point, use two-level lookup.\n                    ix = UTRIE2_INDEX_1_OFFSET - UTRIE2_OMITTED_BMP_INDEX_1_LENGTH + (codePoint >> UTRIE2_SHIFT_1);\n                    ix = this.index[ix];\n                    ix += codePoint >> UTRIE2_SHIFT_2 & UTRIE2_INDEX_2_MASK;\n                    ix = this.index[ix];\n                    ix = (ix << UTRIE2_INDEX_SHIFT) + (codePoint & UTRIE2_DATA_MASK);\n                    return this.data[ix];\n                }\n                if (codePoint <= 0x10ffff) {\n                    return this.data[this.highValueIndex];\n                }\n            }\n\n            // Fall through.  The code point is outside of the legal range of 0..0x10ffff.\n            return this.errorValue;\n        }\n    }]);\n\n    return Trie;\n}();", "'use strict';\n\nmodule.exports = '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';", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _Path = require('./Path');\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Circle = function Circle(x, y, radius) {\n    _classCallCheck(this, Circle);\n\n    this.type = _Path.PATH.CIRCLE;\n    this.x = x;\n    this.y = y;\n    this.radius = radius;\n    if (process.env.NODE_ENV !== 'production') {\n        if (isNaN(x)) {\n            console.error('Invalid x value given for Circle');\n        }\n        if (isNaN(y)) {\n            console.error('Invalid y value given for Circle');\n        }\n        if (isNaN(radius)) {\n            console.error('Invalid radius value given for Circle');\n        }\n    }\n};\n\nexports.default = Circle;", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Font = require('./Font');\n\nvar _Gradient = require('./Gradient');\n\nvar _TextContainer = require('./TextContainer');\n\nvar _TextContainer2 = _interopRequireDefault(_TextContainer);\n\nvar _background = require('./parsing/background');\n\nvar _border = require('./parsing/border');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Renderer = function () {\n    function Renderer(target, options) {\n        _classCallCheck(this, Renderer);\n\n        this.target = target;\n        this.options = options;\n        target.render(options);\n    }\n\n    _createClass(Renderer, [{\n        key: 'renderNode',\n        value: function renderNode(container) {\n            if (container.isVisible()) {\n                this.renderNodeBackgroundAndBorders(container);\n                this.renderNodeContent(container);\n            }\n        }\n    }, {\n        key: 'renderNodeContent',\n        value: function renderNodeContent(container) {\n            var _this = this;\n\n            var callback = function callback() {\n                if (container.childNodes.length) {\n                    container.childNodes.forEach(function (child) {\n                        if (child instanceof _TextContainer2.default) {\n                            var style = child.parent.style;\n                            _this.target.renderTextNode(child.bounds, style.color, style.font, style.textDecoration, style.textShadow);\n                        } else {\n                            _this.target.drawShape(child, container.style.color);\n                        }\n                    });\n                }\n\n                if (container.image) {\n                    var _image = _this.options.imageStore.get(container.image);\n                    if (_image) {\n                        var contentBox = (0, _Bounds.calculateContentBox)(container.bounds, container.style.padding, container.style.border);\n                        var _width = typeof _image.width === 'number' && _image.width > 0 ? _image.width : contentBox.width;\n                        var _height = typeof _image.height === 'number' && _image.height > 0 ? _image.height : contentBox.height;\n                        if (_width > 0 && _height > 0) {\n                            _this.target.clip([(0, _Bounds.calculatePaddingBoxPath)(container.curvedBounds)], function () {\n                                _this.target.drawImage(_image, new _Bounds.Bounds(0, 0, _width, _height), contentBox);\n                            });\n                        }\n                    }\n                }\n            };\n            var paths = container.getClipPaths();\n            if (paths.length) {\n                this.target.clip(paths, callback);\n            } else {\n                callback();\n            }\n        }\n    }, {\n        key: 'renderNodeBackgroundAndBorders',\n        value: function renderNodeBackgroundAndBorders(container) {\n            var _this2 = this;\n\n            var HAS_BACKGROUND = !container.style.background.backgroundColor.isTransparent() || container.style.background.backgroundImage.length;\n\n            var hasRenderableBorders = container.style.border.some(function (border) {\n                return border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent();\n            });\n\n            var callback = function callback() {\n                var backgroundPaintingArea = (0, _background.calculateBackgroungPaintingArea)(container.curvedBounds, container.style.background.backgroundClip);\n\n                if (HAS_BACKGROUND) {\n                    _this2.target.clip([backgroundPaintingArea], function () {\n                        if (!container.style.background.backgroundColor.isTransparent()) {\n                            _this2.target.fill(container.style.background.backgroundColor);\n                        }\n\n                        _this2.renderBackgroundImage(container);\n                    });\n                }\n\n                container.style.border.forEach(function (border, side) {\n                    if (border.borderStyle !== _border.BORDER_STYLE.NONE && !border.borderColor.isTransparent()) {\n                        _this2.renderBorder(border, side, container.curvedBounds);\n                    }\n                });\n            };\n\n            if (HAS_BACKGROUND || hasRenderableBorders) {\n                var paths = container.parent ? container.parent.getClipPaths() : [];\n                if (paths.length) {\n                    this.target.clip(paths, callback);\n                } else {\n                    callback();\n                }\n            }\n        }\n    }, {\n        key: 'renderBackgroundImage',\n        value: function renderBackgroundImage(container) {\n            var _this3 = this;\n\n            container.style.background.backgroundImage.slice(0).reverse().forEach(function (backgroundImage) {\n                if (backgroundImage.source.method === 'url' && backgroundImage.source.args.length) {\n                    _this3.renderBackgroundRepeat(container, backgroundImage);\n                } else if (/gradient/i.test(backgroundImage.source.method)) {\n                    _this3.renderBackgroundGradient(container, backgroundImage);\n                }\n            });\n        }\n    }, {\n        key: 'renderBackgroundRepeat',\n        value: function renderBackgroundRepeat(container, background) {\n            var image = this.options.imageStore.get(background.source.args[0]);\n            if (image) {\n                var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n                var backgroundImageSize = (0, _background.calculateBackgroundSize)(background, image, backgroundPositioningArea);\n                var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n                var _path = (0, _background.calculateBackgroundRepeatPath)(background, position, backgroundImageSize, backgroundPositioningArea, container.bounds);\n\n                var _offsetX = Math.round(backgroundPositioningArea.left + position.x);\n                var _offsetY = Math.round(backgroundPositioningArea.top + position.y);\n                this.target.renderRepeat(_path, image, backgroundImageSize, _offsetX, _offsetY);\n            }\n        }\n    }, {\n        key: 'renderBackgroundGradient',\n        value: function renderBackgroundGradient(container, background) {\n            var backgroundPositioningArea = (0, _background.calculateBackgroungPositioningArea)(container.style.background.backgroundOrigin, container.bounds, container.style.padding, container.style.border);\n            var backgroundImageSize = (0, _background.calculateGradientBackgroundSize)(background, backgroundPositioningArea);\n            var position = (0, _background.calculateBackgroundPosition)(background.position, backgroundImageSize, backgroundPositioningArea);\n            var gradientBounds = new _Bounds.Bounds(Math.round(backgroundPositioningArea.left + position.x), Math.round(backgroundPositioningArea.top + position.y), backgroundImageSize.width, backgroundImageSize.height);\n\n            var gradient = (0, _Gradient.parseGradient)(container, background.source, gradientBounds);\n            if (gradient) {\n                switch (gradient.type) {\n                    case _Gradient.GRADIENT_TYPE.LINEAR_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderLinearGradient(gradientBounds, gradient);\n                        break;\n                    case _Gradient.GRADIENT_TYPE.RADIAL_GRADIENT:\n                        // $FlowFixMe\n                        this.target.renderRadialGradient(gradientBounds, gradient);\n                        break;\n                }\n            }\n        }\n    }, {\n        key: 'renderBorder',\n        value: function renderBorder(border, side, curvePoints) {\n            this.target.drawShape((0, _Bounds.parsePathForBorder)(curvePoints, side), border.borderColor);\n        }\n    }, {\n        key: 'renderStack',\n        value: function renderStack(stack) {\n            var _this4 = this;\n\n            if (stack.container.isVisible()) {\n                var _opacity = stack.getOpacity();\n                if (_opacity !== this._opacity) {\n                    this.target.setOpacity(stack.getOpacity());\n                    this._opacity = _opacity;\n                }\n\n                var _transform = stack.container.style.transform;\n                if (_transform !== null) {\n                    this.target.transform(stack.container.bounds.left + _transform.transformOrigin[0].value, stack.container.bounds.top + _transform.transformOrigin[1].value, _transform.transform, function () {\n                        return _this4.renderStackContent(stack);\n                    });\n                } else {\n                    this.renderStackContent(stack);\n                }\n            }\n        }\n    }, {\n        key: 'renderStackContent',\n        value: function renderStackContent(stack) {\n            var _splitStackingContext = splitStackingContexts(stack),\n                _splitStackingContext2 = _slicedToArray(_splitStackingContext, 5),\n                negativeZIndex = _splitStackingContext2[0],\n                zeroOrAutoZIndexOrTransformedOrOpacity = _splitStackingContext2[1],\n                positiveZIndex = _splitStackingContext2[2],\n                nonPositionedFloats = _splitStackingContext2[3],\n                nonPositionedInlineLevel = _splitStackingContext2[4];\n\n            var _splitDescendants = splitDescendants(stack),\n                _splitDescendants2 = _slicedToArray(_splitDescendants, 2),\n                inlineLevel = _splitDescendants2[0],\n                nonInlineLevel = _splitDescendants2[1];\n\n            // https://www.w3.org/TR/css-position-3/#painting-order\n            // 1. the background and borders of the element forming the stacking context.\n\n\n            this.renderNodeBackgroundAndBorders(stack.container);\n            // 2. the child stacking contexts with negative stack levels (most negative first).\n            negativeZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n            // 3. For all its in-flow, non-positioned, block-level descendants in tree order:\n            this.renderNodeContent(stack.container);\n            nonInlineLevel.forEach(this.renderNode, this);\n            // 4. All non-positioned floating descendants, in tree order. For each one of these,\n            // treat the element as if it created a new stacking context, but any positioned descendants and descendants\n            // which actually create a new stacking context should be considered part of the parent stacking context,\n            // not this new one.\n            nonPositionedFloats.forEach(this.renderStack, this);\n            // 5. the in-flow, inline-level, non-positioned descendants, including inline tables and inline blocks.\n            nonPositionedInlineLevel.forEach(this.renderStack, this);\n            inlineLevel.forEach(this.renderNode, this);\n            // 6. All positioned, opacity or transform descendants, in tree order that fall into the following categories:\n            //  All positioned descendants with 'z-index: auto' or 'z-index: 0', in tree order.\n            //  For those with 'z-index: auto', treat the element as if it created a new stacking context,\n            //  but any positioned descendants and descendants which actually create a new stacking context should be\n            //  considered part of the parent stacking context, not this new one. For those with 'z-index: 0',\n            //  treat the stacking context generated atomically.\n            //\n            //  All opacity descendants with opacity less than 1\n            //\n            //  All transform descendants with transform other than none\n            zeroOrAutoZIndexOrTransformedOrOpacity.forEach(this.renderStack, this);\n            // 7. Stacking contexts formed by positioned descendants with z-indices greater than or equal to 1 in z-index\n            // order (smallest first) then tree order.\n            positiveZIndex.sort(sortByZIndex).forEach(this.renderStack, this);\n        }\n    }, {\n        key: 'render',\n        value: function render(stack) {\n            var _this5 = this;\n\n            if (this.options.backgroundColor) {\n                this.target.rectangle(this.options.x, this.options.y, this.options.width, this.options.height, this.options.backgroundColor);\n            }\n            this.renderStack(stack);\n            var target = this.target.getTarget();\n            if (process.env.NODE_ENV !== 'production') {\n                return target.then(function (output) {\n                    _this5.options.logger.log('Render completed');\n                    return output;\n                });\n            }\n            return target;\n        }\n    }]);\n\n    return Renderer;\n}();\n\nexports.default = Renderer;\n\n\nvar splitDescendants = function splitDescendants(stack) {\n    var inlineLevel = [];\n    var nonInlineLevel = [];\n\n    var length = stack.children.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.children[i];\n        if (child.isInlineLevel()) {\n            inlineLevel.push(child);\n        } else {\n            nonInlineLevel.push(child);\n        }\n    }\n    return [inlineLevel, nonInlineLevel];\n};\n\nvar splitStackingContexts = function splitStackingContexts(stack) {\n    var negativeZIndex = [];\n    var zeroOrAutoZIndexOrTransformedOrOpacity = [];\n    var positiveZIndex = [];\n    var nonPositionedFloats = [];\n    var nonPositionedInlineLevel = [];\n    var length = stack.contexts.length;\n    for (var i = 0; i < length; i++) {\n        var child = stack.contexts[i];\n        if (child.container.isPositioned() || child.container.style.opacity < 1 || child.container.isTransformed()) {\n            if (child.container.style.zIndex.order < 0) {\n                negativeZIndex.push(child);\n            } else if (child.container.style.zIndex.order > 0) {\n                positiveZIndex.push(child);\n            } else {\n                zeroOrAutoZIndexOrTransformedOrOpacity.push(child);\n            }\n        } else {\n            if (child.container.isFloating()) {\n                nonPositionedFloats.push(child);\n            } else {\n                nonPositionedInlineLevel.push(child);\n            }\n        }\n    }\n    return [negativeZIndex, zeroOrAutoZIndexOrTransformedOrOpacity, positiveZIndex, nonPositionedFloats, nonPositionedInlineLevel];\n};\n\nvar sortByZIndex = function sortByZIndex(a, b) {\n    if (a.container.style.zIndex.order > b.container.style.zIndex.order) {\n        return 1;\n    } else if (a.container.style.zIndex.order < b.container.style.zIndex.order) {\n        return -1;\n    }\n\n    return a.container.index > b.container.index ? 1 : -1;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.transformWebkitRadialGradientArgs = exports.parseGradient = exports.RadialGradient = exports.LinearGradient = exports.RADIAL_GRADIENT_SHAPE = exports.GRADIENT_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _NodeContainer = require('./NodeContainer');\n\nvar _NodeContainer2 = _interopRequireDefault(_NodeContainer);\n\nvar _Angle = require('./Angle');\n\nvar _Color = require('./Color');\n\nvar _Color2 = _interopRequireDefault(_Color);\n\nvar _Length = require('./Length');\n\nvar _Length2 = _interopRequireDefault(_Length);\n\nvar _Util = require('./Util');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SIDE_OR_CORNER = /^(to )?(left|top|right|bottom)( (left|top|right|bottom))?$/i;\nvar PERCENTAGE_ANGLES = /^([+-]?\\d*\\.?\\d+)% ([+-]?\\d*\\.?\\d+)%$/i;\nvar ENDS_WITH_LENGTH = /(px)|%|( 0)$/i;\nvar FROM_TO_COLORSTOP = /^(from|to|color-stop)\\((?:([\\d.]+)(%)?,\\s*)?(.+?)\\)$/i;\nvar RADIAL_SHAPE_DEFINITION = /^\\s*(circle|ellipse)?\\s*((?:([\\d.]+)(px|r?em|%)\\s*(?:([\\d.]+)(px|r?em|%))?)|closest-side|closest-corner|farthest-side|farthest-corner)?\\s*(?:at\\s*(?:(left|center|right)|([\\d.]+)(px|r?em|%))\\s+(?:(top|center|bottom)|([\\d.]+)(px|r?em|%)))?(?:\\s|$)/i;\n\nvar GRADIENT_TYPE = exports.GRADIENT_TYPE = {\n    LINEAR_GRADIENT: 0,\n    RADIAL_GRADIENT: 1\n};\n\nvar RADIAL_GRADIENT_SHAPE = exports.RADIAL_GRADIENT_SHAPE = {\n    CIRCLE: 0,\n    ELLIPSE: 1\n};\n\nvar LENGTH_FOR_POSITION = {\n    left: new _Length2.default('0%'),\n    top: new _Length2.default('0%'),\n    center: new _Length2.default('50%'),\n    right: new _Length2.default('100%'),\n    bottom: new _Length2.default('100%')\n};\n\nvar LinearGradient = exports.LinearGradient = function LinearGradient(colorStops, direction) {\n    _classCallCheck(this, LinearGradient);\n\n    this.type = GRADIENT_TYPE.LINEAR_GRADIENT;\n    this.colorStops = colorStops;\n    this.direction = direction;\n};\n\nvar RadialGradient = exports.RadialGradient = function RadialGradient(colorStops, shape, center, radius) {\n    _classCallCheck(this, RadialGradient);\n\n    this.type = GRADIENT_TYPE.RADIAL_GRADIENT;\n    this.colorStops = colorStops;\n    this.shape = shape;\n    this.center = center;\n    this.radius = radius;\n};\n\nvar parseGradient = exports.parseGradient = function parseGradient(container, _ref, bounds) {\n    var args = _ref.args,\n        method = _ref.method,\n        prefix = _ref.prefix;\n\n    if (method === 'linear-gradient') {\n        return parseLinearGradient(args, bounds, !!prefix);\n    } else if (method === 'gradient' && args[0] === 'linear') {\n        // TODO handle correct angle\n        return parseLinearGradient(['to bottom'].concat(transformObsoleteColorStops(args.slice(3))), bounds, !!prefix);\n    } else if (method === 'radial-gradient') {\n        return parseRadialGradient(container, prefix === '-webkit-' ? transformWebkitRadialGradientArgs(args) : args, bounds);\n    } else if (method === 'gradient' && args[0] === 'radial') {\n        return parseRadialGradient(container, transformObsoleteColorStops(transformWebkitRadialGradientArgs(args.slice(1))), bounds);\n    }\n};\n\nvar parseColorStops = function parseColorStops(args, firstColorStopIndex, lineLength) {\n    var colorStops = [];\n\n    for (var i = firstColorStopIndex; i < args.length; i++) {\n        var value = args[i];\n        var HAS_LENGTH = ENDS_WITH_LENGTH.test(value);\n        var lastSpaceIndex = value.lastIndexOf(' ');\n        var _color = new _Color2.default(HAS_LENGTH ? value.substring(0, lastSpaceIndex) : value);\n        var _stop = HAS_LENGTH ? new _Length2.default(value.substring(lastSpaceIndex + 1)) : i === firstColorStopIndex ? new _Length2.default('0%') : i === args.length - 1 ? new _Length2.default('100%') : null;\n        colorStops.push({ color: _color, stop: _stop });\n    }\n\n    var absoluteValuedColorStops = colorStops.map(function (_ref2) {\n        var color = _ref2.color,\n            stop = _ref2.stop;\n\n        var absoluteStop = lineLength === 0 ? 0 : stop ? stop.getAbsoluteValue(lineLength) / lineLength : null;\n\n        return {\n            color: color,\n            // $FlowFixMe\n            stop: absoluteStop\n        };\n    });\n\n    var previousColorStop = absoluteValuedColorStops[0].stop;\n    for (var _i = 0; _i < absoluteValuedColorStops.length; _i++) {\n        if (previousColorStop !== null) {\n            var _stop2 = absoluteValuedColorStops[_i].stop;\n            if (_stop2 === null) {\n                var n = _i;\n                while (absoluteValuedColorStops[n].stop === null) {\n                    n++;\n                }\n                var steps = n - _i + 1;\n                var nextColorStep = absoluteValuedColorStops[n].stop;\n                var stepSize = (nextColorStep - previousColorStop) / steps;\n                for (; _i < n; _i++) {\n                    previousColorStop = absoluteValuedColorStops[_i].stop = previousColorStop + stepSize;\n                }\n            } else {\n                previousColorStop = _stop2;\n            }\n        }\n    }\n\n    return absoluteValuedColorStops;\n};\n\nvar parseLinearGradient = function parseLinearGradient(args, bounds, hasPrefix) {\n    var angle = (0, _Angle.parseAngle)(args[0]);\n    var HAS_SIDE_OR_CORNER = SIDE_OR_CORNER.test(args[0]);\n    var HAS_DIRECTION = HAS_SIDE_OR_CORNER || angle !== null || PERCENTAGE_ANGLES.test(args[0]);\n    var direction = HAS_DIRECTION ? angle !== null ? calculateGradientDirection(\n    // if there is a prefix, the 0° angle points due East (instead of North per W3C)\n    hasPrefix ? angle - Math.PI * 0.5 : angle, bounds) : HAS_SIDE_OR_CORNER ? parseSideOrCorner(args[0], bounds) : parsePercentageAngle(args[0], bounds) : calculateGradientDirection(Math.PI, bounds);\n    var firstColorStopIndex = HAS_DIRECTION ? 1 : 0;\n\n    // TODO: Fix some inaccuracy with color stops with px values\n    var lineLength = Math.min((0, _Util.distance)(Math.abs(direction.x0) + Math.abs(direction.x1), Math.abs(direction.y0) + Math.abs(direction.y1)), bounds.width * 2, bounds.height * 2);\n\n    return new LinearGradient(parseColorStops(args, firstColorStopIndex, lineLength), direction);\n};\n\nvar parseRadialGradient = function parseRadialGradient(container, args, bounds) {\n    var m = args[0].match(RADIAL_SHAPE_DEFINITION);\n    var shape = m && (m[1] === 'circle' || // explicit shape specification\n    m[3] !== undefined && m[5] === undefined) // only one radius coordinate\n    ? RADIAL_GRADIENT_SHAPE.CIRCLE : RADIAL_GRADIENT_SHAPE.ELLIPSE;\n    var radius = {};\n    var center = {};\n\n    if (m) {\n        // Radius\n        if (m[3] !== undefined) {\n            radius.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[3], m[4]).getAbsoluteValue(bounds.width);\n        }\n\n        if (m[5] !== undefined) {\n            radius.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[5], m[6]).getAbsoluteValue(bounds.height);\n        }\n\n        // Position\n        if (m[7]) {\n            center.x = LENGTH_FOR_POSITION[m[7].toLowerCase()];\n        } else if (m[8] !== undefined) {\n            center.x = (0, _Length.calculateLengthFromValueWithUnit)(container, m[8], m[9]);\n        }\n\n        if (m[10]) {\n            center.y = LENGTH_FOR_POSITION[m[10].toLowerCase()];\n        } else if (m[11] !== undefined) {\n            center.y = (0, _Length.calculateLengthFromValueWithUnit)(container, m[11], m[12]);\n        }\n    }\n\n    var gradientCenter = {\n        x: center.x === undefined ? bounds.width / 2 : center.x.getAbsoluteValue(bounds.width),\n        y: center.y === undefined ? bounds.height / 2 : center.y.getAbsoluteValue(bounds.height)\n    };\n    var gradientRadius = calculateRadius(m && m[2] || 'farthest-corner', shape, gradientCenter, radius, bounds);\n\n    return new RadialGradient(parseColorStops(args, m ? 1 : 0, Math.min(gradientRadius.x, gradientRadius.y)), shape, gradientCenter, gradientRadius);\n};\n\nvar calculateGradientDirection = function calculateGradientDirection(radian, bounds) {\n    var width = bounds.width;\n    var height = bounds.height;\n    var HALF_WIDTH = width * 0.5;\n    var HALF_HEIGHT = height * 0.5;\n    var lineLength = Math.abs(width * Math.sin(radian)) + Math.abs(height * Math.cos(radian));\n    var HALF_LINE_LENGTH = lineLength / 2;\n\n    var x0 = HALF_WIDTH + Math.sin(radian) * HALF_LINE_LENGTH;\n    var y0 = HALF_HEIGHT - Math.cos(radian) * HALF_LINE_LENGTH;\n    var x1 = width - x0;\n    var y1 = height - y0;\n\n    return { x0: x0, x1: x1, y0: y0, y1: y1 };\n};\n\nvar parseTopRight = function parseTopRight(bounds) {\n    return Math.acos(bounds.width / 2 / ((0, _Util.distance)(bounds.width, bounds.height) / 2));\n};\n\nvar parseSideOrCorner = function parseSideOrCorner(side, bounds) {\n    switch (side) {\n        case 'bottom':\n        case 'to top':\n            return calculateGradientDirection(0, bounds);\n        case 'left':\n        case 'to right':\n            return calculateGradientDirection(Math.PI / 2, bounds);\n        case 'right':\n        case 'to left':\n            return calculateGradientDirection(3 * Math.PI / 2, bounds);\n        case 'top right':\n        case 'right top':\n        case 'to bottom left':\n        case 'to left bottom':\n            return calculateGradientDirection(Math.PI + parseTopRight(bounds), bounds);\n        case 'top left':\n        case 'left top':\n        case 'to bottom right':\n        case 'to right bottom':\n            return calculateGradientDirection(Math.PI - parseTopRight(bounds), bounds);\n        case 'bottom left':\n        case 'left bottom':\n        case 'to top right':\n        case 'to right top':\n            return calculateGradientDirection(parseTopRight(bounds), bounds);\n        case 'bottom right':\n        case 'right bottom':\n        case 'to top left':\n        case 'to left top':\n            return calculateGradientDirection(2 * Math.PI - parseTopRight(bounds), bounds);\n        case 'top':\n        case 'to bottom':\n        default:\n            return calculateGradientDirection(Math.PI, bounds);\n    }\n};\n\nvar parsePercentageAngle = function parsePercentageAngle(angle, bounds) {\n    var _angle$split$map = angle.split(' ').map(parseFloat),\n        _angle$split$map2 = _slicedToArray(_angle$split$map, 2),\n        left = _angle$split$map2[0],\n        top = _angle$split$map2[1];\n\n    var ratio = left / 100 * bounds.width / (top / 100 * bounds.height);\n\n    return calculateGradientDirection(Math.atan(isNaN(ratio) ? 1 : ratio) + Math.PI / 2, bounds);\n};\n\nvar findCorner = function findCorner(bounds, x, y, closest) {\n    var corners = [{ x: 0, y: 0 }, { x: 0, y: bounds.height }, { x: bounds.width, y: 0 }, { x: bounds.width, y: bounds.height }];\n\n    // $FlowFixMe\n    return corners.reduce(function (stat, corner) {\n        var d = (0, _Util.distance)(x - corner.x, y - corner.y);\n        if (closest ? d < stat.optimumDistance : d > stat.optimumDistance) {\n            return {\n                optimumCorner: corner,\n                optimumDistance: d\n            };\n        }\n\n        return stat;\n    }, {\n        optimumDistance: closest ? Infinity : -Infinity,\n        optimumCorner: null\n    }).optimumCorner;\n};\n\nvar calculateRadius = function calculateRadius(extent, shape, center, radius, bounds) {\n    var x = center.x;\n    var y = center.y;\n    var rx = 0;\n    var ry = 0;\n\n    switch (extent) {\n        case 'closest-side':\n            // The ending shape is sized so that that it exactly meets the side of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, it exactly meets the closest side in each dimension.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.min(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'closest-corner':\n            // The ending shape is sized so that that it passes through the corner of the gradient box closest to the gradient’s center.\n            // If the shape is an ellipse, the ending shape is given the same aspect-ratio it would have if closest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.min((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"closest-side\")\n                var c = Math.min(Math.abs(y), Math.abs(y - bounds.height)) / Math.min(Math.abs(x), Math.abs(x - bounds.width));\n                var corner = findCorner(bounds, x, y, true);\n                rx = (0, _Util.distance)(corner.x - x, (corner.y - y) / c);\n                ry = c * rx;\n            }\n            break;\n\n        case 'farthest-side':\n            // Same as closest-side, except the ending shape is sized based on the farthest side(s)\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max(Math.abs(x), Math.abs(x - bounds.width), Math.abs(y), Math.abs(y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                rx = Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                ry = Math.max(Math.abs(y), Math.abs(y - bounds.height));\n            }\n            break;\n\n        case 'farthest-corner':\n            // Same as closest-corner, except the ending shape is sized based on the farthest corner.\n            // If the shape is an ellipse, the ending shape is given the same aspect ratio it would have if farthest-side were specified.\n            if (shape === RADIAL_GRADIENT_SHAPE.CIRCLE) {\n                rx = ry = Math.max((0, _Util.distance)(x, y), (0, _Util.distance)(x, y - bounds.height), (0, _Util.distance)(x - bounds.width, y), (0, _Util.distance)(x - bounds.width, y - bounds.height));\n            } else if (shape === RADIAL_GRADIENT_SHAPE.ELLIPSE) {\n                // Compute the ratio ry/rx (which is to be the same as for \"farthest-side\")\n                var _c = Math.max(Math.abs(y), Math.abs(y - bounds.height)) / Math.max(Math.abs(x), Math.abs(x - bounds.width));\n                var _corner = findCorner(bounds, x, y, false);\n                rx = (0, _Util.distance)(_corner.x - x, (_corner.y - y) / _c);\n                ry = _c * rx;\n            }\n            break;\n\n        default:\n            // pixel or percentage values\n            rx = radius.x || 0;\n            ry = radius.y !== undefined ? radius.y : rx;\n            break;\n    }\n\n    return {\n        x: rx,\n        y: ry\n    };\n};\n\nvar transformWebkitRadialGradientArgs = exports.transformWebkitRadialGradientArgs = function transformWebkitRadialGradientArgs(args) {\n    var shape = '';\n    var radius = '';\n    var extent = '';\n    var position = '';\n    var idx = 0;\n\n    var POSITION = /^(left|center|right|\\d+(?:px|r?em|%)?)(?:\\s+(top|center|bottom|\\d+(?:px|r?em|%)?))?$/i;\n    var SHAPE_AND_EXTENT = /^(circle|ellipse)?\\s*(closest-side|closest-corner|farthest-side|farthest-corner|contain|cover)?$/i;\n    var RADIUS = /^\\d+(px|r?em|%)?(?:\\s+\\d+(px|r?em|%)?)?$/i;\n\n    var matchStartPosition = args[idx].match(POSITION);\n    if (matchStartPosition) {\n        idx++;\n    }\n\n    var matchShapeExtent = args[idx].match(SHAPE_AND_EXTENT);\n    if (matchShapeExtent) {\n        shape = matchShapeExtent[1] || '';\n        extent = matchShapeExtent[2] || '';\n        if (extent === 'contain') {\n            extent = 'closest-side';\n        } else if (extent === 'cover') {\n            extent = 'farthest-corner';\n        }\n        idx++;\n    }\n\n    var matchStartRadius = args[idx].match(RADIUS);\n    if (matchStartRadius) {\n        idx++;\n    }\n\n    var matchEndPosition = args[idx].match(POSITION);\n    if (matchEndPosition) {\n        idx++;\n    }\n\n    var matchEndRadius = args[idx].match(RADIUS);\n    if (matchEndRadius) {\n        idx++;\n    }\n\n    var matchPosition = matchEndPosition || matchStartPosition;\n    if (matchPosition && matchPosition[1]) {\n        position = matchPosition[1] + (/^\\d+$/.test(matchPosition[1]) ? 'px' : '');\n        if (matchPosition[2]) {\n            position += ' ' + matchPosition[2] + (/^\\d+$/.test(matchPosition[2]) ? 'px' : '');\n        }\n    }\n\n    var matchRadius = matchEndRadius || matchStartRadius;\n    if (matchRadius) {\n        radius = matchRadius[0];\n        if (!matchRadius[1]) {\n            radius += 'px';\n        }\n    }\n\n    if (position && !shape && !radius && !extent) {\n        radius = position;\n        position = '';\n    }\n\n    if (position) {\n        position = 'at ' + position;\n    }\n\n    return [[shape, extent, radius, position].filter(function (s) {\n        return !!s;\n    }).join(' ')].concat(args.slice(idx));\n};\n\nvar transformObsoleteColorStops = function transformObsoleteColorStops(args) {\n    return args.map(function (color) {\n        return color.match(FROM_TO_COLORSTOP);\n    })\n    // $FlowFixMe\n    .map(function (v, index) {\n        if (!v) {\n            return args[index];\n        }\n\n        switch (v[1]) {\n            case 'from':\n                return v[4] + ' 0%';\n            case 'to':\n                return v[4] + ' 100%';\n            case 'color-stop':\n                if (v[3] === '%') {\n                    return v[4] + ' ' + v[2];\n                }\n                return v[4] + ' ' + parseFloat(v[2]) * 100 + '%';\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nvar ANGLE = /([+-]?\\d*\\.?\\d+)(deg|grad|rad|turn)/i;\n\nvar parseAngle = exports.parseAngle = function parseAngle(angle) {\n    var match = angle.match(ANGLE);\n\n    if (match) {\n        var value = parseFloat(match[1]);\n        switch (match[2].toLowerCase()) {\n            case 'deg':\n                return Math.PI * value / 180;\n            case 'grad':\n                return Math.PI / 200 * value;\n            case 'rad':\n                return value;\n            case 'turn':\n                return Math.PI * 2 * value;\n        }\n    }\n\n    return null;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.cloneWindow = exports.DocumentCloner = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Bounds = require('./Bounds');\n\nvar _Proxy = require('./Proxy');\n\nvar _ResourceLoader = require('./ResourceLoader');\n\nvar _ResourceLoader2 = _interopRequireDefault(_ResourceLoader);\n\nvar _Util = require('./Util');\n\nvar _background = require('./parsing/background');\n\nvar _CanvasRenderer = require('./renderer/CanvasRenderer');\n\nvar _CanvasRenderer2 = _interopRequireDefault(_CanvasRenderer);\n\nvar _PseudoNodeContent = require('./PseudoNodeContent');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar IGNORE_ATTRIBUTE = 'data-html2canvas-ignore';\n\nvar DocumentCloner = exports.DocumentCloner = function () {\n    function DocumentCloner(element, options, logger, copyInline, renderer) {\n        _classCallCheck(this, DocumentCloner);\n\n        this.referenceElement = element;\n        this.scrolledElements = [];\n        this.copyStyles = copyInline;\n        this.inlineImages = copyInline;\n        this.logger = logger;\n        this.options = options;\n        this.renderer = renderer;\n        this.resourceLoader = new _ResourceLoader2.default(options, logger, window);\n        this.pseudoContentData = {\n            counters: {},\n            quoteDepth: 0\n        };\n        // $FlowFixMe\n        this.documentElement = this.cloneNode(element.ownerDocument.documentElement);\n    }\n\n    _createClass(DocumentCloner, [{\n        key: 'inlineAllImages',\n        value: function inlineAllImages(node) {\n            var _this = this;\n\n            if (this.inlineImages && node) {\n                var style = node.style;\n                Promise.all((0, _background.parseBackgroundImage)(style.backgroundImage).map(function (backgroundImage) {\n                    if (backgroundImage.method === 'url') {\n                        return _this.resourceLoader.inlineImage(backgroundImage.args[0]).then(function (img) {\n                            return img && typeof img.src === 'string' ? 'url(\"' + img.src + '\")' : 'none';\n                        }).catch(function (e) {\n                            if (process.env.NODE_ENV !== 'production') {\n                                _this.logger.log('Unable to load image', e);\n                            }\n                        });\n                    }\n                    return Promise.resolve('' + backgroundImage.prefix + backgroundImage.method + '(' + backgroundImage.args.join(',') + ')');\n                })).then(function (backgroundImages) {\n                    if (backgroundImages.length > 1) {\n                        // TODO Multiple backgrounds somehow broken in Chrome\n                        style.backgroundColor = '';\n                    }\n                    style.backgroundImage = backgroundImages.join(',');\n                });\n\n                if (node instanceof HTMLImageElement) {\n                    this.resourceLoader.inlineImage(node.src).then(function (img) {\n                        if (img && node instanceof HTMLImageElement && node.parentNode) {\n                            var parentNode = node.parentNode;\n                            var clonedChild = (0, _Util.copyCSSStyles)(node.style, img.cloneNode(false));\n                            parentNode.replaceChild(clonedChild, node);\n                        }\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this.logger.log('Unable to load image', e);\n                        }\n                    });\n                }\n            }\n        }\n    }, {\n        key: 'inlineFonts',\n        value: function inlineFonts(document) {\n            var _this2 = this;\n\n            return Promise.all(Array.from(document.styleSheets).map(function (sheet) {\n                if (sheet.href) {\n                    return fetch(sheet.href).then(function (res) {\n                        return res.text();\n                    }).then(function (text) {\n                        return createStyleSheetFontsFromText(text, sheet.href);\n                    }).catch(function (e) {\n                        if (process.env.NODE_ENV !== 'production') {\n                            _this2.logger.log('Unable to load stylesheet', e);\n                        }\n                        return [];\n                    });\n                }\n                return getSheetFonts(sheet, document);\n            })).then(function (fonts) {\n                return fonts.reduce(function (acc, font) {\n                    return acc.concat(font);\n                }, []);\n            }).then(function (fonts) {\n                return Promise.all(fonts.map(function (font) {\n                    return fetch(font.formats[0].src).then(function (response) {\n                        return response.blob();\n                    }).then(function (blob) {\n                        return new Promise(function (resolve, reject) {\n                            var reader = new FileReader();\n                            reader.onerror = reject;\n                            reader.onload = function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            };\n                            reader.readAsDataURL(blob);\n                        });\n                    }).then(function (dataUri) {\n                        font.fontFace.setProperty('src', 'url(\"' + dataUri + '\")');\n                        return '@font-face {' + font.fontFace.cssText + ' ';\n                    });\n                }));\n            }).then(function (fontCss) {\n                var style = document.createElement('style');\n                style.textContent = fontCss.join('\\n');\n                _this2.documentElement.appendChild(style);\n            });\n        }\n    }, {\n        key: 'createElementClone',\n        value: function createElementClone(node) {\n            var _this3 = this;\n\n            if (this.copyStyles && node instanceof HTMLCanvasElement) {\n                var img = node.ownerDocument.createElement('img');\n                try {\n                    img.src = node.toDataURL();\n                    return img;\n                } catch (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        this.logger.log('Unable to clone canvas contents, canvas is tainted');\n                    }\n                }\n            }\n\n            if (node instanceof HTMLIFrameElement) {\n                var tempIframe = node.cloneNode(false);\n                var iframeKey = generateIframeKey();\n                tempIframe.setAttribute('data-html2canvas-internal-iframe-key', iframeKey);\n\n                var _parseBounds = (0, _Bounds.parseBounds)(node, 0, 0),\n                    width = _parseBounds.width,\n                    height = _parseBounds.height;\n\n                this.resourceLoader.cache[iframeKey] = getIframeDocumentElement(node, this.options).then(function (documentElement) {\n                    return _this3.renderer(documentElement, {\n                        async: _this3.options.async,\n                        allowTaint: _this3.options.allowTaint,\n                        backgroundColor: '#ffffff',\n                        canvas: null,\n                        imageTimeout: _this3.options.imageTimeout,\n                        logging: _this3.options.logging,\n                        proxy: _this3.options.proxy,\n                        removeContainer: _this3.options.removeContainer,\n                        scale: _this3.options.scale,\n                        foreignObjectRendering: _this3.options.foreignObjectRendering,\n                        useCORS: _this3.options.useCORS,\n                        target: new _CanvasRenderer2.default(),\n                        width: width,\n                        height: height,\n                        x: 0,\n                        y: 0,\n                        windowWidth: documentElement.ownerDocument.defaultView.innerWidth,\n                        windowHeight: documentElement.ownerDocument.defaultView.innerHeight,\n                        scrollX: documentElement.ownerDocument.defaultView.pageXOffset,\n                        scrollY: documentElement.ownerDocument.defaultView.pageYOffset\n                    }, _this3.logger.child(iframeKey));\n                }).then(function (canvas) {\n                    return new Promise(function (resolve, reject) {\n                        var iframeCanvas = document.createElement('img');\n                        iframeCanvas.onload = function () {\n                            return resolve(canvas);\n                        };\n                        iframeCanvas.onerror = reject;\n                        iframeCanvas.src = canvas.toDataURL();\n                        if (tempIframe.parentNode) {\n                            tempIframe.parentNode.replaceChild((0, _Util.copyCSSStyles)(node.ownerDocument.defaultView.getComputedStyle(node), iframeCanvas), tempIframe);\n                        }\n                    });\n                });\n                return tempIframe;\n            }\n\n            if (node instanceof HTMLStyleElement && node.sheet && node.sheet.cssRules) {\n                var css = [].slice.call(node.sheet.cssRules, 0).reduce(function (css, rule) {\n                    return css + rule.cssText;\n                }, '');\n                var style = node.cloneNode(false);\n                style.textContent = css;\n                return style;\n            }\n\n            return node.cloneNode(false);\n        }\n    }, {\n        key: 'cloneNode',\n        value: function cloneNode(node) {\n            var clone = node.nodeType === Node.TEXT_NODE ? document.createTextNode(node.nodeValue) : this.createElementClone(node);\n\n            var window = node.ownerDocument.defaultView;\n            var style = node instanceof window.HTMLElement ? window.getComputedStyle(node) : null;\n            var styleBefore = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':before') : null;\n            var styleAfter = node instanceof window.HTMLElement ? window.getComputedStyle(node, ':after') : null;\n\n            if (this.referenceElement === node && clone instanceof window.HTMLElement) {\n                this.clonedReferenceElement = clone;\n            }\n\n            if (clone instanceof window.HTMLBodyElement) {\n                createPseudoHideStyles(clone);\n            }\n\n            var counters = (0, _PseudoNodeContent.parseCounterReset)(style, this.pseudoContentData);\n            var contentBefore = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleBefore, this.pseudoContentData);\n\n            for (var child = node.firstChild; child; child = child.nextSibling) {\n                if (child.nodeType !== Node.ELEMENT_NODE || child.nodeName !== 'SCRIPT' &&\n                // $FlowFixMe\n                !child.hasAttribute(IGNORE_ATTRIBUTE) && (typeof this.options.ignoreElements !== 'function' ||\n                // $FlowFixMe\n                !this.options.ignoreElements(child))) {\n                    if (!this.copyStyles || child.nodeName !== 'STYLE') {\n                        clone.appendChild(this.cloneNode(child));\n                    }\n                }\n            }\n\n            var contentAfter = (0, _PseudoNodeContent.resolvePseudoContent)(node, styleAfter, this.pseudoContentData);\n            (0, _PseudoNodeContent.popCounters)(counters, this.pseudoContentData);\n\n            if (node instanceof window.HTMLElement && clone instanceof window.HTMLElement) {\n                if (styleBefore) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleBefore, contentBefore, PSEUDO_BEFORE));\n                }\n                if (styleAfter) {\n                    this.inlineAllImages(inlinePseudoElement(node, clone, styleAfter, contentAfter, PSEUDO_AFTER));\n                }\n                if (style && this.copyStyles && !(node instanceof HTMLIFrameElement)) {\n                    (0, _Util.copyCSSStyles)(style, clone);\n                }\n                this.inlineAllImages(clone);\n                if (node.scrollTop !== 0 || node.scrollLeft !== 0) {\n                    this.scrolledElements.push([clone, node.scrollLeft, node.scrollTop]);\n                }\n                switch (node.nodeName) {\n                    case 'CANVAS':\n                        if (!this.copyStyles) {\n                            cloneCanvasContents(node, clone);\n                        }\n                        break;\n                    case 'TEXTAREA':\n                    case 'SELECT':\n                        clone.value = node.value;\n                        break;\n                    case 'INPUT':\n                        if (node.checked) {\n                            // required for IE9 and 10\n                            clone.setAttribute('checked', true);\n                        }\n                        break;\n                }\n            }\n            return clone;\n        }\n    }]);\n\n    return DocumentCloner;\n}();\n\nvar getSheetFonts = function getSheetFonts(sheet, document) {\n    // $FlowFixMe\n    return (sheet.cssRules ? Array.from(sheet.cssRules) : []).filter(function (rule) {\n        return rule.type === CSSRule.FONT_FACE_RULE;\n    }).map(function (rule) {\n        var src = (0, _background.parseBackgroundImage)(rule.style.getPropertyValue('src'));\n        var formats = [];\n        for (var i = 0; i < src.length; i++) {\n            if (src[i].method === 'url' && src[i + 1] && src[i + 1].method === 'format') {\n                var a = document.createElement('a');\n                a.href = src[i].args[0];\n                if (document.body) {\n                    document.body.appendChild(a);\n                }\n\n                var font = {\n                    src: a.href,\n                    format: src[i + 1].args[0]\n                };\n                formats.push(font);\n            }\n        }\n\n        return {\n            // TODO select correct format for browser),\n\n            formats: formats.filter(function (font) {\n                return (/^woff/i.test(font.format)\n                );\n            }),\n            fontFace: rule.style\n        };\n    }).filter(function (font) {\n        return font.formats.length;\n    });\n};\n\nvar createStyleSheetFontsFromText = function createStyleSheetFontsFromText(text, baseHref) {\n    var doc = document.implementation.createHTMLDocument('');\n    var base = document.createElement('base');\n    // $FlowFixMe\n    base.href = baseHref;\n    var style = document.createElement('style');\n\n    style.textContent = text;\n    if (doc.head) {\n        doc.head.appendChild(base);\n    }\n    if (doc.body) {\n        doc.body.appendChild(style);\n    }\n\n    return style.sheet ? getSheetFonts(style.sheet, doc) : [];\n};\n\nvar restoreOwnerScroll = function restoreOwnerScroll(ownerDocument, x, y) {\n    if (ownerDocument.defaultView && (x !== ownerDocument.defaultView.pageXOffset || y !== ownerDocument.defaultView.pageYOffset)) {\n        ownerDocument.defaultView.scrollTo(x, y);\n    }\n};\n\nvar cloneCanvasContents = function cloneCanvasContents(canvas, clonedCanvas) {\n    try {\n        if (clonedCanvas) {\n            clonedCanvas.width = canvas.width;\n            clonedCanvas.height = canvas.height;\n            var ctx = canvas.getContext('2d');\n            var clonedCtx = clonedCanvas.getContext('2d');\n            if (ctx) {\n                clonedCtx.putImageData(ctx.getImageData(0, 0, canvas.width, canvas.height), 0, 0);\n            } else {\n                clonedCtx.drawImage(canvas, 0, 0);\n            }\n        }\n    } catch (e) {}\n};\n\nvar inlinePseudoElement = function inlinePseudoElement(node, clone, style, contentItems, pseudoElt) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return;\n    }\n\n    var anonymousReplacedElement = clone.ownerDocument.createElement('html2canvaspseudoelement');\n    (0, _Util.copyCSSStyles)(style, anonymousReplacedElement);\n\n    if (contentItems) {\n        var len = contentItems.length;\n        for (var i = 0; i < len; i++) {\n            var item = contentItems[i];\n            switch (item.type) {\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.IMAGE:\n                    var img = clone.ownerDocument.createElement('img');\n                    img.src = (0, _background.parseBackgroundImage)('url(' + item.value + ')')[0].args[0];\n                    img.style.opacity = '1';\n                    anonymousReplacedElement.appendChild(img);\n                    break;\n                case _PseudoNodeContent.PSEUDO_CONTENT_ITEM_TYPE.TEXT:\n                    anonymousReplacedElement.appendChild(clone.ownerDocument.createTextNode(item.value));\n                    break;\n            }\n        }\n    }\n\n    anonymousReplacedElement.className = PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    clone.className += pseudoElt === PSEUDO_BEFORE ? ' ' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE : ' ' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER;\n    if (pseudoElt === PSEUDO_BEFORE) {\n        clone.insertBefore(anonymousReplacedElement, clone.firstChild);\n    } else {\n        clone.appendChild(anonymousReplacedElement);\n    }\n\n    return anonymousReplacedElement;\n};\n\nvar URL_REGEXP = /^url\\((.+)\\)$/i;\nvar PSEUDO_BEFORE = ':before';\nvar PSEUDO_AFTER = ':after';\nvar PSEUDO_HIDE_ELEMENT_CLASS_BEFORE = '___html2canvas___pseudoelement_before';\nvar PSEUDO_HIDE_ELEMENT_CLASS_AFTER = '___html2canvas___pseudoelement_after';\n\nvar PSEUDO_HIDE_ELEMENT_STYLE = '{\\n    content: \"\" !important;\\n    display: none !important;\\n}';\n\nvar createPseudoHideStyles = function createPseudoHideStyles(body) {\n    createStyles(body, '.' + PSEUDO_HIDE_ELEMENT_CLASS_BEFORE + PSEUDO_BEFORE + PSEUDO_HIDE_ELEMENT_STYLE + '\\n         .' + PSEUDO_HIDE_ELEMENT_CLASS_AFTER + PSEUDO_AFTER + PSEUDO_HIDE_ELEMENT_STYLE);\n};\n\nvar createStyles = function createStyles(body, styles) {\n    var style = body.ownerDocument.createElement('style');\n    style.innerHTML = styles;\n    body.appendChild(style);\n};\n\nvar initNode = function initNode(_ref) {\n    var _ref2 = _slicedToArray(_ref, 3),\n        element = _ref2[0],\n        x = _ref2[1],\n        y = _ref2[2];\n\n    element.scrollLeft = x;\n    element.scrollTop = y;\n};\n\nvar generateIframeKey = function generateIframeKey() {\n    return Math.ceil(Date.now() + Math.random() * 10000000).toString(16);\n};\n\nvar DATA_URI_REGEXP = /^data:text\\/(.+);(base64)?,(.*)$/i;\n\nvar getIframeDocumentElement = function getIframeDocumentElement(node, options) {\n    try {\n        return Promise.resolve(node.contentWindow.document.documentElement);\n    } catch (e) {\n        return options.proxy ? (0, _Proxy.Proxy)(node.src, options).then(function (html) {\n            var match = html.match(DATA_URI_REGEXP);\n            if (!match) {\n                return Promise.reject();\n            }\n\n            return match[2] === 'base64' ? window.atob(decodeURIComponent(match[3])) : decodeURIComponent(match[3]);\n        }).then(function (html) {\n            return createIframeContainer(node.ownerDocument, (0, _Bounds.parseBounds)(node, 0, 0)).then(function (cloneIframeContainer) {\n                var cloneWindow = cloneIframeContainer.contentWindow;\n                var documentClone = cloneWindow.document;\n\n                documentClone.open();\n                documentClone.write(html);\n                var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n                    return documentClone.documentElement;\n                });\n\n                documentClone.close();\n                return iframeLoad;\n            });\n        }) : Promise.reject();\n    }\n};\n\nvar createIframeContainer = function createIframeContainer(ownerDocument, bounds) {\n    var cloneIframeContainer = ownerDocument.createElement('iframe');\n\n    cloneIframeContainer.className = 'html2canvas-container';\n    cloneIframeContainer.style.visibility = 'hidden';\n    cloneIframeContainer.style.position = 'fixed';\n    cloneIframeContainer.style.left = '-10000px';\n    cloneIframeContainer.style.top = '0px';\n    cloneIframeContainer.style.border = '0';\n    cloneIframeContainer.width = bounds.width.toString();\n    cloneIframeContainer.height = bounds.height.toString();\n    cloneIframeContainer.scrolling = 'no'; // ios won't scroll without it\n    cloneIframeContainer.setAttribute(IGNORE_ATTRIBUTE, 'true');\n    if (!ownerDocument.body) {\n        return Promise.reject(process.env.NODE_ENV !== 'production' ? 'Body element not found in Document that is getting rendered' : '');\n    }\n\n    ownerDocument.body.appendChild(cloneIframeContainer);\n\n    return Promise.resolve(cloneIframeContainer);\n};\n\nvar iframeLoader = function iframeLoader(cloneIframeContainer) {\n    var cloneWindow = cloneIframeContainer.contentWindow;\n    var documentClone = cloneWindow.document;\n\n    return new Promise(function (resolve, reject) {\n        cloneWindow.onload = cloneIframeContainer.onload = documentClone.onreadystatechange = function () {\n            var interval = setInterval(function () {\n                if (documentClone.body.childNodes.length > 0 && documentClone.readyState === 'complete') {\n                    clearInterval(interval);\n                    resolve(cloneIframeContainer);\n                }\n            }, 50);\n        };\n    });\n};\n\nvar cloneWindow = exports.cloneWindow = function cloneWindow(ownerDocument, bounds, referenceElement, options, logger, renderer) {\n    var cloner = new DocumentCloner(referenceElement, options, logger, false, renderer);\n    var scrollX = ownerDocument.defaultView.pageXOffset;\n    var scrollY = ownerDocument.defaultView.pageYOffset;\n\n    return createIframeContainer(ownerDocument, bounds).then(function (cloneIframeContainer) {\n        var cloneWindow = cloneIframeContainer.contentWindow;\n        var documentClone = cloneWindow.document;\n\n        /* Chrome doesn't detect relative background-images assigned in inline <style> sheets when fetched through getComputedStyle\n             if window url is about:blank, we can assign the url to current by writing onto the document\n             */\n\n        var iframeLoad = iframeLoader(cloneIframeContainer).then(function () {\n            cloner.scrolledElements.forEach(initNode);\n            cloneWindow.scrollTo(bounds.left, bounds.top);\n            if (/(iPad|iPhone|iPod)/g.test(navigator.userAgent) && (cloneWindow.scrollY !== bounds.top || cloneWindow.scrollX !== bounds.left)) {\n                documentClone.documentElement.style.top = -bounds.top + 'px';\n                documentClone.documentElement.style.left = -bounds.left + 'px';\n                documentClone.documentElement.style.position = 'absolute';\n            }\n\n            var result = Promise.resolve([cloneIframeContainer, cloner.clonedReferenceElement, cloner.resourceLoader]);\n\n            var onclone = options.onclone;\n\n            return cloner.clonedReferenceElement instanceof cloneWindow.HTMLElement || cloner.clonedReferenceElement instanceof ownerDocument.defaultView.HTMLElement || cloner.clonedReferenceElement instanceof HTMLElement ? typeof onclone === 'function' ? Promise.resolve().then(function () {\n                return onclone(documentClone);\n            }).then(function () {\n                return result;\n            }) : result : Promise.reject(process.env.NODE_ENV !== 'production' ? 'Error finding the ' + referenceElement.nodeName + ' in the cloned document' : '');\n        });\n\n        documentClone.open();\n        documentClone.write(serializeDoctype(document.doctype) + '<html></html>');\n        // Chrome scrolls the parent document for some reason after the write to the cloned window???\n        restoreOwnerScroll(referenceElement.ownerDocument, scrollX, scrollY);\n        documentClone.replaceChild(documentClone.adoptNode(cloner.documentElement), documentClone.documentElement);\n        documentClone.close();\n\n        return iframeLoad;\n    });\n};\n\nvar serializeDoctype = function serializeDoctype(doctype) {\n    var str = '';\n    if (doctype) {\n        str += '<!DOCTYPE ';\n        if (doctype.name) {\n            str += doctype.name;\n        }\n\n        if (doctype.internalSubset) {\n            str += doctype.internalSubset;\n        }\n\n        if (doctype.publicId) {\n            str += '\"' + doctype.publicId + '\"';\n        }\n\n        if (doctype.systemId) {\n            str += '\"' + doctype.systemId + '\"';\n        }\n\n        str += '>';\n    }\n\n    return str;\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.ResourceStore = undefined;\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Feature = require('./Feature');\n\nvar _Feature2 = _interopRequireDefault(_Feature);\n\nvar _Proxy = require('./Proxy');\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ResourceLoader = function () {\n    function ResourceLoader(options, logger, window) {\n        _classCallCheck(this, ResourceLoader);\n\n        this.options = options;\n        this._window = window;\n        this.origin = this.getOrigin(window.location.href);\n        this.cache = {};\n        this.logger = logger;\n        this._index = 0;\n    }\n\n    _createClass(ResourceLoader, [{\n        key: 'loadImage',\n        value: function loadImage(src) {\n            var _this = this;\n\n            if (this.hasResourceInCache(src)) {\n                return src;\n            }\n\n            if (!isSVG(src) || _Feature2.default.SUPPORT_SVG_DRAWING) {\n                if (this.options.allowTaint === true || isInlineImage(src) || this.isSameOrigin(src)) {\n                    return this.addImage(src, src, false);\n                } else if (!this.isSameOrigin(src)) {\n                    if (typeof this.options.proxy === 'string') {\n                        this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                            return _loadImage(src, _this.options.imageTimeout || 0);\n                        });\n                        return src;\n                    } else if (this.options.useCORS === true && _Feature2.default.SUPPORT_CORS_IMAGES) {\n                        return this.addImage(src, src, true);\n                    }\n                }\n            }\n        }\n    }, {\n        key: 'inlineImage',\n        value: function inlineImage(src) {\n            var _this2 = this;\n\n            if (isInlineImage(src)) {\n                return _loadImage(src, this.options.imageTimeout || 0);\n            }\n            if (this.hasResourceInCache(src)) {\n                return this.cache[src];\n            }\n            if (!this.isSameOrigin(src) && typeof this.options.proxy === 'string') {\n                return this.cache[src] = (0, _Proxy.Proxy)(src, this.options).then(function (src) {\n                    return _loadImage(src, _this2.options.imageTimeout || 0);\n                });\n            }\n\n            return this.xhrImage(src);\n        }\n    }, {\n        key: 'xhrImage',\n        value: function xhrImage(src) {\n            var _this3 = this;\n\n            this.cache[src] = new Promise(function (resolve, reject) {\n                var xhr = new XMLHttpRequest();\n                xhr.onreadystatechange = function () {\n                    if (xhr.readyState === 4) {\n                        if (xhr.status !== 200) {\n                            reject('Failed to fetch image ' + src.substring(0, 256) + ' with status code ' + xhr.status);\n                        } else {\n                            var reader = new FileReader();\n                            reader.addEventListener('load', function () {\n                                // $FlowFixMe\n                                var result = reader.result;\n                                resolve(result);\n                            }, false);\n                            reader.addEventListener('error', function (e) {\n                                return reject(e);\n                            }, false);\n                            reader.readAsDataURL(xhr.response);\n                        }\n                    }\n                };\n                xhr.responseType = 'blob';\n                if (_this3.options.imageTimeout) {\n                    var timeout = _this3.options.imageTimeout;\n                    xhr.timeout = timeout;\n                    xhr.ontimeout = function () {\n                        return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                    };\n                }\n                xhr.open('GET', src, true);\n                xhr.send();\n            }).then(function (src) {\n                return _loadImage(src, _this3.options.imageTimeout || 0);\n            });\n\n            return this.cache[src];\n        }\n    }, {\n        key: 'loadCanvas',\n        value: function loadCanvas(node) {\n            var key = String(this._index++);\n            this.cache[key] = Promise.resolve(node);\n            return key;\n        }\n    }, {\n        key: 'hasResourceInCache',\n        value: function hasResourceInCache(key) {\n            return typeof this.cache[key] !== 'undefined';\n        }\n    }, {\n        key: 'addImage',\n        value: function addImage(key, src, useCORS) {\n            var _this4 = this;\n\n            if (process.env.NODE_ENV !== 'production') {\n                this.logger.log('Added image ' + key.substring(0, 256));\n            }\n\n            var imageLoadHandler = function imageLoadHandler(supportsDataImages) {\n                return new Promise(function (resolve, reject) {\n                    var img = new Image();\n                    img.onload = function () {\n                        return resolve(img);\n                    };\n                    //ios safari 10.3 taints canvas with data urls unless crossOrigin is set to anonymous\n                    if (!supportsDataImages || useCORS) {\n                        img.crossOrigin = 'anonymous';\n                    }\n\n                    img.onerror = reject;\n                    img.src = src;\n                    if (img.complete === true) {\n                        // Inline XML images may fail to parse, throwing an Error later on\n                        setTimeout(function () {\n                            resolve(img);\n                        }, 500);\n                    }\n                    if (_this4.options.imageTimeout) {\n                        var timeout = _this4.options.imageTimeout;\n                        setTimeout(function () {\n                            return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) fetching ' + src.substring(0, 256) : '');\n                        }, timeout);\n                    }\n                });\n            };\n\n            this.cache[key] = isInlineBase64Image(src) && !isSVG(src) ? // $FlowFixMe\n            _Feature2.default.SUPPORT_BASE64_DRAWING(src).then(imageLoadHandler) : imageLoadHandler(true);\n            return key;\n        }\n    }, {\n        key: 'isSameOrigin',\n        value: function isSameOrigin(url) {\n            return this.getOrigin(url) === this.origin;\n        }\n    }, {\n        key: 'getOrigin',\n        value: function getOrigin(url) {\n            var link = this._link || (this._link = this._window.document.createElement('a'));\n            link.href = url;\n            link.href = link.href; // IE9, LOL! - http://jsfiddle.net/niklasvh/2e48b/\n            return link.protocol + link.hostname + link.port;\n        }\n    }, {\n        key: 'ready',\n        value: function ready() {\n            var _this5 = this;\n\n            var keys = Object.keys(this.cache);\n            var values = keys.map(function (str) {\n                return _this5.cache[str].catch(function (e) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        _this5.logger.log('Unable to load image', e);\n                    }\n                    return null;\n                });\n            });\n            return Promise.all(values).then(function (images) {\n                if (process.env.NODE_ENV !== 'production') {\n                    _this5.logger.log('Finished loading ' + images.length + ' images', images);\n                }\n                return new ResourceStore(keys, images);\n            });\n        }\n    }]);\n\n    return ResourceLoader;\n}();\n\nexports.default = ResourceLoader;\n\nvar ResourceStore = exports.ResourceStore = function () {\n    function ResourceStore(keys, resources) {\n        _classCallCheck(this, ResourceStore);\n\n        this._keys = keys;\n        this._resources = resources;\n    }\n\n    _createClass(ResourceStore, [{\n        key: 'get',\n        value: function get(key) {\n            var index = this._keys.indexOf(key);\n            return index === -1 ? null : this._resources[index];\n        }\n    }]);\n\n    return ResourceStore;\n}();\n\nvar INLINE_SVG = /^data:image\\/svg\\+xml/i;\nvar INLINE_BASE64 = /^data:image\\/.*;base64,/i;\nvar INLINE_IMG = /^data:image\\/.*/i;\n\nvar isInlineImage = function isInlineImage(src) {\n    return INLINE_IMG.test(src);\n};\nvar isInlineBase64Image = function isInlineBase64Image(src) {\n    return INLINE_BASE64.test(src);\n};\n\nvar isSVG = function isSVG(src) {\n    return src.substr(-3).toLowerCase() === 'svg' || INLINE_SVG.test(src);\n};\n\nvar _loadImage = function _loadImage(src, timeout) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        img.onload = function () {\n            return resolve(img);\n        };\n        img.onerror = reject;\n        img.src = src;\n        if (img.complete === true) {\n            // Inline XML images may fail to parse, throwing an Error later on\n            setTimeout(function () {\n                resolve(img);\n            }, 500);\n        }\n        if (timeout) {\n            setTimeout(function () {\n                return reject(process.env.NODE_ENV !== 'production' ? 'Timed out (' + timeout + 'ms) loading image' : '');\n            }, timeout);\n        }\n    });\n};", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.parseContent = exports.resolvePseudoContent = exports.popCounters = exports.parseCounterReset = exports.TOKEN_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = undefined;\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nvar _ListItem = require('./ListItem');\n\nvar _listStyle = require('./parsing/listStyle');\n\nvar PSEUDO_CONTENT_ITEM_TYPE = exports.PSEUDO_CONTENT_ITEM_TYPE = {\n    TEXT: 0,\n    IMAGE: 1\n};\n\nvar TOKEN_TYPE = exports.TOKEN_TYPE = {\n    STRING: 0,\n    ATTRIBUTE: 1,\n    URL: 2,\n    COUNTER: 3,\n    COUNTERS: 4,\n    OPENQUOTE: 5,\n    CLOSEQUOTE: 6\n};\n\nvar parseCounterReset = exports.parseCounterReset = function parseCounterReset(style, data) {\n    if (!style || !style.counterReset || style.counterReset === 'none') {\n        return [];\n    }\n\n    var counterNames = [];\n    var counterResets = style.counterReset.split(/\\s*,\\s*/);\n    var lenCounterResets = counterResets.length;\n\n    for (var i = 0; i < lenCounterResets; i++) {\n        var _counterResets$i$spli = counterResets[i].split(/\\s+/),\n            _counterResets$i$spli2 = _slicedToArray(_counterResets$i$spli, 2),\n            counterName = _counterResets$i$spli2[0],\n            initialValue = _counterResets$i$spli2[1];\n\n        counterNames.push(counterName);\n        var counter = data.counters[counterName];\n        if (!counter) {\n            counter = data.counters[counterName] = [];\n        }\n        counter.push(parseInt(initialValue || 0, 10));\n    }\n\n    return counterNames;\n};\n\nvar popCounters = exports.popCounters = function popCounters(counterNames, data) {\n    var lenCounters = counterNames.length;\n    for (var i = 0; i < lenCounters; i++) {\n        data.counters[counterNames[i]].pop();\n    }\n};\n\nvar resolvePseudoContent = exports.resolvePseudoContent = function resolvePseudoContent(node, style, data) {\n    if (!style || !style.content || style.content === 'none' || style.content === '-moz-alt-content' || style.display === 'none') {\n        return null;\n    }\n\n    var tokens = parseContent(style.content);\n\n    var len = tokens.length;\n    var contentItems = [];\n    var s = '';\n\n    // increment the counter (if there is a \"counter-increment\" declaration)\n    var counterIncrement = style.counterIncrement;\n    if (counterIncrement && counterIncrement !== 'none') {\n        var _counterIncrement$spl = counterIncrement.split(/\\s+/),\n            _counterIncrement$spl2 = _slicedToArray(_counterIncrement$spl, 2),\n            counterName = _counterIncrement$spl2[0],\n            incrementValue = _counterIncrement$spl2[1];\n\n        var counter = data.counters[counterName];\n        if (counter) {\n            counter[counter.length - 1] += incrementValue === undefined ? 1 : parseInt(incrementValue, 10);\n        }\n    }\n\n    // build the content string\n    for (var i = 0; i < len; i++) {\n        var token = tokens[i];\n        switch (token.type) {\n            case TOKEN_TYPE.STRING:\n                s += token.value || '';\n                break;\n\n            case TOKEN_TYPE.ATTRIBUTE:\n                if (node instanceof HTMLElement && token.value) {\n                    s += node.getAttribute(token.value) || '';\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTER:\n                var _counter = data.counters[token.name || ''];\n                if (_counter) {\n                    s += formatCounterValue([_counter[_counter.length - 1]], '', token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.COUNTERS:\n                var _counters = data.counters[token.name || ''];\n                if (_counters) {\n                    s += formatCounterValue(_counters, token.glue, token.format);\n                }\n                break;\n\n            case TOKEN_TYPE.OPENQUOTE:\n                s += getQuote(style, true, data.quoteDepth);\n                data.quoteDepth++;\n                break;\n\n            case TOKEN_TYPE.CLOSEQUOTE:\n                data.quoteDepth--;\n                s += getQuote(style, false, data.quoteDepth);\n                break;\n\n            case TOKEN_TYPE.URL:\n                if (s) {\n                    contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n                    s = '';\n                }\n                contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.IMAGE, value: token.value || '' });\n                break;\n        }\n    }\n\n    if (s) {\n        contentItems.push({ type: PSEUDO_CONTENT_ITEM_TYPE.TEXT, value: s });\n    }\n\n    return contentItems;\n};\n\nvar parseContent = exports.parseContent = function parseContent(content, cache) {\n    if (cache && cache[content]) {\n        return cache[content];\n    }\n\n    var tokens = [];\n    var len = content.length;\n\n    var isString = false;\n    var isEscaped = false;\n    var isFunction = false;\n    var str = '';\n    var functionName = '';\n    var args = [];\n\n    for (var i = 0; i < len; i++) {\n        var c = content.charAt(i);\n\n        switch (c) {\n            case \"'\":\n            case '\"':\n                if (isEscaped) {\n                    str += c;\n                } else {\n                    isString = !isString;\n                    if (!isFunction && !isString) {\n                        tokens.push({ type: TOKEN_TYPE.STRING, value: str });\n                        str = '';\n                    }\n                }\n                break;\n\n            case '\\\\':\n                if (isEscaped) {\n                    str += c;\n                    isEscaped = false;\n                } else {\n                    isEscaped = true;\n                }\n                break;\n\n            case '(':\n                if (isString) {\n                    str += c;\n                } else {\n                    isFunction = true;\n                    functionName = str;\n                    str = '';\n                    args = [];\n                }\n                break;\n\n            case ')':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    if (str) {\n                        args.push(str);\n                    }\n\n                    switch (functionName) {\n                        case 'attr':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.ATTRIBUTE, value: args[0] });\n                            }\n                            break;\n\n                        case 'counter':\n                            if (args.length > 0) {\n                                var counter = {\n                                    type: TOKEN_TYPE.COUNTER,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    counter.format = args[1];\n                                }\n                                tokens.push(counter);\n                            }\n                            break;\n\n                        case 'counters':\n                            if (args.length > 0) {\n                                var _counters2 = {\n                                    type: TOKEN_TYPE.COUNTERS,\n                                    name: args[0]\n                                };\n                                if (args.length > 1) {\n                                    _counters2.glue = args[1];\n                                }\n                                if (args.length > 2) {\n                                    _counters2.format = args[2];\n                                }\n                                tokens.push(_counters2);\n                            }\n                            break;\n\n                        case 'url':\n                            if (args.length > 0) {\n                                tokens.push({ type: TOKEN_TYPE.URL, value: args[0] });\n                            }\n                            break;\n                    }\n\n                    isFunction = false;\n                    str = '';\n                }\n                break;\n\n            case ',':\n                if (isString) {\n                    str += c;\n                } else if (isFunction) {\n                    args.push(str);\n                    str = '';\n                }\n                break;\n\n            case ' ':\n            case '\\t':\n                if (isString) {\n                    str += c;\n                } else if (str) {\n                    addOtherToken(tokens, str);\n                    str = '';\n                }\n                break;\n\n            default:\n                str += c;\n        }\n\n        if (c !== '\\\\') {\n            isEscaped = false;\n        }\n    }\n\n    if (str) {\n        addOtherToken(tokens, str);\n    }\n\n    if (cache) {\n        cache[content] = tokens;\n    }\n\n    return tokens;\n};\n\nvar addOtherToken = function addOtherToken(tokens, identifier) {\n    switch (identifier) {\n        case 'open-quote':\n            tokens.push({ type: TOKEN_TYPE.OPENQUOTE });\n            break;\n        case 'close-quote':\n            tokens.push({ type: TOKEN_TYPE.CLOSEQUOTE });\n            break;\n    }\n};\n\nvar getQuote = function getQuote(style, isOpening, quoteDepth) {\n    var quotes = style.quotes ? style.quotes.split(/\\s+/) : [\"'\\\"'\", \"'\\\"'\"];\n    var idx = quoteDepth * 2;\n    if (idx >= quotes.length) {\n        idx = quotes.length - 2;\n    }\n    if (!isOpening) {\n        ++idx;\n    }\n    return quotes[idx].replace(/^[\"']|[\"']$/g, '');\n};\n\nvar formatCounterValue = function formatCounterValue(counter, glue, format) {\n    var len = counter.length;\n    var result = '';\n\n    for (var i = 0; i < len; i++) {\n        if (i > 0) {\n            result += glue || '';\n        }\n        result += (0, _ListItem.createCounterText)(counter[i], (0, _listStyle.parseListStyleType)(format || 'decimal'), false);\n    }\n\n    return result;\n};"], "sourceRoot": ""}