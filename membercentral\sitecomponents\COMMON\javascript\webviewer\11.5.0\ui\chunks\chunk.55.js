(window.webpackJsonp = window.webpackJsonp || []).push([
	[55],
	{
		1574: function (e, t, n) {
			"use strict";
			var o = n(454);
			t.a = o.a;
		},
		1647: function (e, t, n) {
			var o = n(30),
				i = n(1648);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const i = t[o];
						if (0 === o)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(i, r);
			e.exports = i.locals || {};
		},
		1648: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1707: function (e, t, n) {
			"use strict";
			n.r(t);
			n(36),
				n(46),
				n(52),
				n(19),
				n(90),
				n(290),
				n(353),
				n(446),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22),
				n(32),
				n(29),
				n(47),
				n(23),
				n(24),
				n(49),
				n(48),
				n(58),
				n(44);
			var o = n(0),
				i = n.n(o),
				r = n(6),
				l = n(3),
				a = n.n(l),
				c = n(1574),
				u = n(1),
				d = n(2),
				s = n(4),
				p = (n(1647), n(5)),
				f = n(300),
				h = n(1542),
				m = n(347);
			function y(e) {
				return (y =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function v() {
				return (v = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function g(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function x(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? g(Object(n), !0).forEach(function (t) {
								b(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: g(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function b(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== y(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== y(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === y(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function w(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								i,
								r,
								l,
								a = [],
								c = !0,
								u = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = r.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (i = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return S(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return S(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function S(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var T = {
					annotation: a.a.object,
					editor: a.a.object,
					style: a.a.shape({
						TextColor: a.a.oneOfType([a.a.string, a.a.object]),
						RichTextStyle: a.a.any,
					}),
					isFreeTextAutoSize: a.a.bool,
					onFreeTextSizeToggle: a.a.func,
					onPropertyChange: a.a.func,
					onRichTextStyleChange: a.a.func,
					isRedaction: a.a.bool,
					isRichTextEditMode: a.a.bool,
					setIsRichTextEditMode: a.a.func,
					isWidget: a.a.bool,
				},
				E = function (e) {
					var t,
						n,
						l,
						a,
						y,
						g,
						S,
						T,
						E,
						O,
						R,
						A,
						j,
						z = e.annotation,
						C = e.editor,
						F = e.style,
						_ = e.isFreeTextAutoSize,
						P = e.onFreeTextSizeToggle,
						k = e.onPropertyChange,
						D = e.onRichTextStyleChange,
						M = e.isRichTextEditMode,
						L = e.setIsRichTextEditMode,
						N = e.isRedaction,
						I = e.isWidget,
						H = e.activeTool,
						W = w(
							Object(r.e)(function (e) {
								return [s.a.getFonts(e)];
							}, r.c),
							1,
						)[0],
						B = w(Object(o.useState)({}), 2),
						Y = B[0],
						q = B[1],
						U = Object(o.useRef)(null),
						V = Object(o.useRef)(null),
						J = Object(o.useRef)({}),
						G = Object(r.d)(),
						$ = Object(o.useRef)(),
						K = Object(o.useRef)();
					K.current = M;
					var Q = w(Object(m.a)(), 1)[0];
					Object(o.useEffect)(function () {
						var e = function (e, t) {
								!e &&
									t &&
									U.current &&
									U.current.setSelection(t.index, t.length),
									e && U.current && q(Z(e));
							},
							t = function () {
								var e;
								q(
									Z(
										null === (e = U.current) || void 0 === e
											? void 0
											: e.getSelection(),
									),
								);
							};
						return (
							u.a.addEventListener("editorSelectionChanged", e),
							u.a.addEventListener("editorTextChanged", t),
							G(d.a.disableElements([p.a.ANNOTATION_STYLE_POPUP])),
							function () {
								u.a.removeEventListener("editorSelectionChanged", e),
									u.a.removeEventListener("editorTextChanged", t),
									G(d.a.enableElements([p.a.ANNOTATION_STYLE_POPUP]));
							}
						);
					}, []),
						Object(o.useEffect)(
							function () {
								var e;
								if (((U.current = C), (V.current = z), M && z)) {
									var t,
										n,
										o,
										i,
										r,
										l,
										a = "solid";
									try {
										a =
											"dash" === z.Style
												? "".concat(z.Style, ",").concat(z.Dashes)
												: z.Style;
									} catch (e) {
										console.error(e);
									}
									var c = z.getRichTextStyle()[0];
									J.current = {
										Font: z.Font,
										FontSize: z.FontSize,
										TextAlign: z.TextAlign,
										TextVerticalAlign: z.TextVerticalAlign,
										bold:
											null !==
												(t =
													"bold" === (null == c ? void 0 : c["font-weight"])) &&
											void 0 !== t &&
											t,
										italic:
											null !==
												(n =
													"italic" ===
													(null == c ? void 0 : c["font-style"])) &&
											void 0 !== n &&
											n,
										underline:
											(null == c ||
											null === (o = c["text-decoration"]) ||
											void 0 === o
												? void 0
												: o.includes("underline")) ||
											(null == c ||
											null === (i = c["text-decoration"]) ||
											void 0 === i
												? void 0
												: i.includes("word")),
										strikeout:
											null !==
												(r =
													null == c ||
													null === (l = c["text-decoration"]) ||
													void 0 === l
														? void 0
														: l.includes("line-through")) &&
											void 0 !== r &&
											r,
										size: null == c ? void 0 : c["font-size"],
										font: null == c ? void 0 : c["font-family"],
										StrokeStyle: a,
										calculatedFontSize: z.getCalculatedFontSize(),
									};
								}
								q(
									Z(
										null === (e = U.current) || void 0 === e
											? void 0
											: e.getSelection(),
									),
								),
									$.current &&
										(U.current.setSelection($.current), ($.current = null));
							},
							[z, C, M],
						),
						Object(o.useEffect)(
							function () {
								var e = function () {
										(U.current = null), (V.current = null), L(!1);
									},
									t = function () {
										L(!0);
									};
								return (
									u.a.addEventListener("editorBlur", e),
									u.a.addEventListener("editorFocus", t),
									function () {
										u.a.removeEventListener("editorBlur", e),
											u.a.removeEventListener("editorFocus", t);
									}
								);
							},
							[G],
						);
					var X,
						Z = function (e) {
							if (!e) return {};
							var t = U.current.getFormat(e.index, e.length);
							if ("string" == typeof t.color)
								t.color = new window.Core.Annotations.Color(t.color);
							else if (Array.isArray(t.color)) {
								var n = new window.Core.Annotations.Color(
									t.color[t.color.length - 1],
								);
								t.color = n;
							} else t.color || (t.color = V.current.TextColor);
							for (
								var o = 0, i = ["font", "size", "originalSize"];
								o < i.length;
								o++
							) {
								var r = i[o];
								t[r] && Array.isArray(t[r]) && (t[r] = void 0);
							}
							return t;
						},
						ee = function (e, t) {
							var n, o;
							"size" === e
								? null === (n = U.current) ||
									void 0 === n ||
									n.format("applyCustomFontSize", t)
								: null === (o = U.current) || void 0 === o || o.format(e, t);
							"color" === e && (t = new window.Core.Annotations.Color(t)),
								q(x(x({}, Y), {}, b({}, e, t)));
						},
						te = function (e, t) {
							if (K.current) {
								var n = U.current.getSelection(),
									o = n.index,
									i = n.length,
									r = V.current;
								(r[e] = t),
									U.current.blur(),
									("FontSize" !== e && "Font" !== e) || Object(h.a)(r),
									setTimeout(function () {
										($.current = { index: o, length: i }),
											u.a
												.getAnnotationManager()
												.getEditBoxManager()
												.focusBox(r);
									}, 0);
							} else k(e, t);
						},
						ne = F.RichTextStyle,
						oe = {
							bold:
								null !==
									(t =
										"bold" ===
										(null == ne || null === (n = ne[0]) || void 0 === n
											? void 0
											: n["font-weight"])) &&
								void 0 !== t &&
								t,
							italic:
								null !==
									(l =
										"italic" ===
										(null == ne || null === (a = ne[0]) || void 0 === a
											? void 0
											: a["font-style"])) &&
								void 0 !== l &&
								l,
							underline:
								(null == ne ||
								null === (y = ne[0]) ||
								void 0 === y ||
								null === (g = y["text-decoration"]) ||
								void 0 === g
									? void 0
									: g.includes("underline")) ||
								(null == ne ||
								null === (S = ne[0]) ||
								void 0 === S ||
								null === (T = S["text-decoration"]) ||
								void 0 === T
									? void 0
									: T.includes("word")),
							strikeout:
								null !==
									(E =
										null == ne ||
										null === (O = ne[0]) ||
										void 0 === O ||
										null === (R = O["text-decoration"]) ||
										void 0 === R
											? void 0
											: R.includes("line-through")) &&
								void 0 !== E &&
								E,
							font:
								null == ne || null === (A = ne[0]) || void 0 === A
									? void 0
									: A["font-family"],
							size:
								null == ne || null === (j = ne[0]) || void 0 === j
									? void 0
									: j["font-size"],
							StrokeStyle: "solid",
						};
					(X = x(x({}, F), oe)),
						M &&
							z &&
							((J.current.bold = Y.bold),
							(J.current.italic = Y.italic),
							(J.current.underline = Y.underline),
							(J.current.strikeout = Y.strike),
							(J.current.quillFont = Y.font || J.current.Font),
							(J.current.quillFontSize = Y.originalSize || J.current.FontSize));
					var ie = {
							fonts: W,
							onPropertyChange: te,
							properties: X,
							stateless: !0,
							isFreeText: !N,
						},
						re = {
							onRichTextStyleChange: function (e, t) {
								if (K.current) {
									var n = {
										"font-weight": "bold",
										"font-style": "italic",
										underline: "underline",
										"line-through": "strike",
										"font-family": "font",
										"font-size": "size",
									};
									if ("font-family" === e || "font-size" === e) {
										ee(n[e], t);
										var o = V.current;
										if (o.isAutoSized())
											u.a
												.getAnnotationManager()
												.getEditBoxManager()
												.resizeAnnotation(o);
									} else
										!(function (e) {
											return function () {
												var t = U.current.getSelection(),
													n = t.index,
													o = t.length;
												if (0 === o) {
													$.current = { index: n, length: o };
													var i = U.current.getSelection();
													(n = i.index), (o = i.length);
												}
												var r = U.current.getFormat(n, o);
												ee(e, !r[e]);
											};
										})(n[e])();
								} else D(e, t);
							},
							properties: M ? J.current : X,
							isFreeTextAutoSize: _,
							isRichTextEditMode: M,
							isRedaction: N,
							onFreeTextSizeToggle: P,
						},
						le = {
							onRichTextStyleChange: te,
							isFreeTextAutoSize: !1,
							isRichTextEditMode: !1,
							isRedaction: !1,
							isWidget: I,
						};
					return i.a.createElement(
						"div",
						{
							className: "RichTextStyleEditor",
							onMouseDown: function (e) {
								"touchstart" !== e.type && M && e.preventDefault();
							},
						},
						i.a.createElement(
							"div",
							{ className: "menu-items" },
							i.a.createElement(f.a, v({}, ie, I ? le : re)),
						),
						i.a.createElement(c.a, {
							onColorChange: function (e) {
								!(function (e, t) {
									K.current ? ee("color", t.toHexString()) : k(e, t);
								})("TextColor", new window.Core.Annotations.Color(e));
							},
							color: M ? Y.color : F.TextColor,
							activeTool: H,
							type: "Text",
							ariaTypeLabel: Q("option.stylePopup.textStyle"),
						}),
					);
				};
			E.propTypes = T;
			var O = i.a.memo(E);
			t.default = O;
		},
	},
]);
//# sourceMappingURL=chunk.55.js.map
