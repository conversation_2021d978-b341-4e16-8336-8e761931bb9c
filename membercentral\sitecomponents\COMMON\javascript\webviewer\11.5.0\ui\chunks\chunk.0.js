(window.webpackJsonp = window.webpackJsonp || []).push([
	[0],
	{
		2007: function (e, a, t) {
			"use strict";
			t.d(a, "a", function () {
				return l;
			});
			var n = t(76),
				i = t(17),
				s = t.n(i),
				u = t(0),
				r = t.n(u),
				c = t(1507),
				_ = t(1510),
				l = Object(u.forwardRef)(function (e, a) {
					var t = e.message,
						i = void 0 === t ? "default" : t,
						l = e.messageText,
						p = e.fillWidth,
						o = e.wrapperClassName,
						d = e.padMessageText,
						m = e.className,
						f = e.onFocus,
						w = e.onBlur,
						g = e.rightElement,
						b = e.leftElement,
						v = e.type,
						E = void 0 === v ? "text" : v,
						N = Object(n.c)(e, [
							"message",
							"messageText",
							"fillWidth",
							"wrapperClassName",
							"padMessageText",
							"className",
							"onFocus",
							"onBlur",
							"rightElement",
							"leftElement",
							"type",
						]),
						h = Object(c.a)(f, w),
						O = h.focused,
						x = h.handleOnFocus,
						j = h.handleOnBlur,
						T = Object(u.useMemo)(
							function () {
								if (g) return g;
								var e = void 0;
								switch (i) {
									case "warning":
										e = "Warning";
										break;
									case "error":
										e = "Error";
								}
								return e
									? r.a.createElement(_.a, {
											className: "ui__input__icon",
											icon: e,
										})
									: void 0;
							},
							[i, g],
						),
						B = s()(
							"ui__base ui__input__wrapper",
							{
								"ui__input__wrapper--fill": p,
								"ui__input__wrapper--pad": d && !l,
							},
							o,
						),
						F = s()("ui__input", "ui__input--message-" + i, {
							"ui__input--focused": O,
						}),
						k = s()(
							"ui__input__input",
							{ "ui__input__input--disabled": N.disabled },
							m,
						);
					return r.a.createElement(
						"div",
						{ className: B },
						r.a.createElement(
							"div",
							{ className: F },
							b,
							r.a.createElement(
								"input",
								Object(n.a)({}, N, {
									type: E,
									onFocus: x,
									onBlur: j,
									className: k,
									ref: a,
								}),
							),
							T,
						),
						l
							? r.a.createElement(
									"div",
									{ className: "ui__input__messageText" },
									l,
								)
							: void 0,
					);
				});
		},
	},
]);
//# sourceMappingURL=chunk.0.js.map
