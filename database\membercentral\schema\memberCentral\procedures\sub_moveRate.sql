ALTER PROC dbo.sub_moveRate
@scheduleID int,
@rateID int,
@dir varchar(4),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @rateOrder int, @rateName varchar(200), @scheduleName varchar(200), @newPos int,
		@msg varchar(max), @subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);
	
	SELECT @siteID = srs.siteID, @orgID = s.orgID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, 
		@scheduleName = srs.scheduleName, @rateOrder = sr.rateOrder
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	INNER JOIN dbo.sites AS s ON s.siteID = srs.siteID
	WHERE rateID = @rateID;

	IF @rateOrder IS NULL
		RAISERROR('invalid rate',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE r
			SET r.rateOrder = r.rateOrder + 1
			FROM dbo.sub_rates as r
			WHERE r.scheduleID = @scheduleID
			AND r.[status] = 'A'
			AND r.rateOrder >= @rateOrder - 1;

			UPDATE dbo.sub_rates
			SET rateOrder = rateOrder  - 2
			WHERE rateID = @rateID;
		END
		ELSE BEGIN
			UPDATE r
			SET r.rateOrder = r.rateOrder - 1
			FROM dbo.sub_rates as r
			WHERE r.scheduleID = @scheduleID
			AND r.[status] = 'A'
			AND r.rateOrder <= @rateOrder + 1;
			
			UPDATE dbo.sub_rates
			SET rateOrder = rateOrder  + 2
			WHERE rateID = @rateID;
		END
	
		EXEC dbo.sub_reorderRates @rateID=@rateID;

		SELECT @newPos = rateOrder
		FROM dbo.sub_rates
		WHERE rateID = @rateID;

		IF @newPos <> @rateOrder BEGIN
			SET @subKeyMapJSON = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

			SET @msg =  'Rate ' + QUOTENAME(@rateName) + ' under the Rate Schedule ' + QUOTENAME(@scheduleName) + ' moved to position ' + CAST(@newPos AS varchar(10));
			SET @msg = STRING_ESCAPE(@msg,'json');

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
