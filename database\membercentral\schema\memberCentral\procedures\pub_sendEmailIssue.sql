ALTER PROC dbo.pub_sendEmailIssue
@siteID INT,
@issueID INT,
@emailSubject VARCHAR(200),
@rawContent VARCHAR(MAX),
@contentVersionID INT,
@overrideMemberID INT,
@overrideEmailAddress VARCHAR(MAX),
@recordedByMemberID INT,
@sendOnDate DATETIME,
@isTestMessage BIT = 0

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @messageTypeID INT, @messageStatusIDInserting INT, @messageStatusIDQueued INT, @orgID INT, @defaultOrgIdentityID int, 
		@sendingSiteResourceID INT, @fieldID INT, @fieldName VARCHAR(300), @messageID INT, @vwSQL VARCHAR(MAX), 
		@ParamDefinition NVARCHAR(100), @fieldValueString VARCHAR(200), @mcSQL NVARCHAR(MAX), @colDataType <PERSON>(40), 
		@colList VARCHAR(MAX), @supportProviderEmail VARCHAR(100), @supportProviderName VARCHAR(100), 
		@pubResourceTypeID INT, @functionID INT, @overrideFunctionID INT, @numRecipients INT, @publicationID INT, @emailConsentListID INT,
		@emailConsentListModeName varchar(50), @emailEditionSenderName VARCHAR(200), @emailEditionReplyToAddress VARCHAR(400), @publicationSiteResourceID INT, 
		@itemGroupUID UNIQUEIDENTIFIER, @queueTypeID INT, @insertingQueueStatusID INT, @readyQueueStatusID INT, @globalOptOutListID INT, 
		@consentListIDs VARCHAR(MAX), @outputMessage varchar(max), @consentListName VARCHAR(100), @tempConsentListName VARCHAR(100);
	SET @outputMessage = '';

	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpRecipientConsentLists') IS NOT NULL 
		DROP TABLE #tmpRecipientConsentLists;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpDeletedRecipients') IS NOT NULL
		DROP TABLE #tmpDeletedRecipients;
	IF OBJECT_ID('tempdb..#tmpOverrideEmails') IS NOT NULL
		DROP TABLE #tmpOverrideEmails;

	CREATE TABLE #tmpRecipientsMID (memberID int, recipientEmail varchar(255), recipientEmailTypeID int);
	CREATE TABLE #tmpRecipientDetails (recipientID INT, recipientMemberID INT, itemUID UNIQUEIDENTIFIER DEFAULT(NEWID()));
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName VARCHAR(300), fieldID INT, isExtMergeCode BIT, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpDeletedRecipients (recipientEmail varchar(255), consentListName varchar(100));
	CREATE TABLE #tmpOverrideEmails (emailAddress varchar(255));

	-- Parse multiple override email addresses if provided
	IF @overrideEmailAddress IS NOT NULL AND LEN(TRIM(@overrideEmailAddress)) > 0 BEGIN
		-- Split semicolon-separated email addresses
		DECLARE @emailAddress VARCHAR(255), @pos INT;
		SET @overrideEmailAddress = LTRIM(RTRIM(@overrideEmailAddress)) + ';';

		WHILE CHARINDEX(';', @overrideEmailAddress) > 0 BEGIN
			SET @pos = CHARINDEX(';', @overrideEmailAddress);
			SET @emailAddress = LTRIM(RTRIM(LEFT(@overrideEmailAddress, @pos - 1)));

			IF LEN(@emailAddress) > 0
				INSERT INTO #tmpOverrideEmails (emailAddress) VALUES (@emailAddress);

			SET @overrideEmailAddress = SUBSTRING(@overrideEmailAddress, @pos + 1, LEN(@overrideEmailAddress));
		END
	END

	SELECT @sendingSiteResourceID = st.siteResourceID, @orgID = o.orgID, @defaultOrgIdentityID = s.defaultOrgIdentityID
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	INNER JOIN dbo.admin_siteTools AS st ON st.siteID = s.siteID AND st.siteID = @siteID
	INNER JOIN dbo.admin_toolTypes AS tt ON tt.toolTypeID = st.toolTypeID AND tt.toolType = 'PublicationAdmin';

	SELECT TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	FROM dbo.networks AS net
	INNER JOIN dbo.networkSites AS ns ON net.networkID = ns.networkID
	INNER JOIN dbo.sites AS s ON s.siteID = ns.siteID
	WHERE s.siteID = @siteID 
	AND ns.isLoginNetwork = 1;

	IF @sendOnDate IS NULL OR @sendOnDate < GETDATE()
		SET @sendOnDate = GETDATE();

	SELECT @pubResourceTypeID = dbo.fn_getResourceTypeID('Publications');
	SELECT @functionID = dbo.fn_getResourceFunctionID('ReceiveEditionEmails', @pubResourceTypeID);
	SELECT @overrideFunctionID = dbo.fn_getResourceFunctionID('overrideReceiveEditionEmails', @pubResourceTypeID);

	SELECT @publicationID = p.publicationID, @emailConsentListID = p.emailConsentListID,
		@emailConsentListModeName = clm.modeName, @consentListName = cl.consentListName,
		@emailEditionSenderName = ISNULL(p.emailEditionSenderName, @supportProviderName), 
		@emailEditionReplyToAddress = ISNULL(p.emailEditionReplyToAddress, @supportProviderEmail),
		@publicationSiteResourceID = sr.siteResourceID
	FROM dbo.pub_volumes AS v
	INNER JOIN dbo.pub_issues AS i ON i.volumeID = v.volumeID
	INNER JOIN dbo.pub_publications AS p 
        ON p.publicationID = v.publicationID
        and i.issueID = @issueID
    INNER JOIN platformMail.dbo.email_consentLists cl
        on cl.consentListID = p.emailConsentListID
    INNER JOIN platformMail.dbo.email_consentListModes clm
        on clm.consentListModeID = cl.consentListModeID
	INNER JOIN dbo.cms_applicationInstances AS ai 
        ON ai.applicationInstanceID = p.applicationInstanceID 
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
	INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID 
		AND srs.siteResourceStatusDesc = 'Active';

	SELECT TOP 1 @globalOptOutListID = cl.consentListID
	FROM platformMail.dbo.email_consentLists cl
	INNER JOIN platformMail.dbo.email_consentListTypes clt ON clt.consentListTypeID = cl.consentListTypeID AND clt.orgID = @orgID AND clt.consentListTypeName = 'Global Lists'
	INNER JOIN platformMail.dbo.email_consentListModes clm ON clm.consentListModeID = cl.consentListModeID AND modeName = 'GlobalOptOut';

	-- check for override emails to receive edition
	IF @overrideMemberID IS NULL AND EXISTS (
		SELECT 1
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @overrideFunctionID
			AND srfrp.rightPrintID = gprp.rightPrintID
		WHERE srfrp.siteID = @siteID
	) BEGIN
		INSERT INTO #tmpRecipientsMID (memberID, recipientEmail, recipientEmailTypeID)
		SELECT mActive.memberID, me.email, me.emailTypeID
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON gprp.siteID = @siteID
			AND srfrp.rightPrintID = gprp.rightPrintID
			AND srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @overrideFunctionID
		INNER JOIN dbo.ams_members AS m ON m.groupPrintID = gprp.groupPrintID AND m.orgID in (1,@orgID)
		INNER JOIN dbo.ams_members AS mActive ON mActive.orgID in (1,@orgID) and mActive.memberID = m.activeMemberID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID IN (1,@orgID)
			AND me.memberID = mActive.memberID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID IN (1,@orgID)
			AND metag.memberID = me.memberID 
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.ams_memberEmailTagTypes AS mett ON mett.orgID IN (1,@orgID)
			AND mett.emailTagTypeID = metag.emailTagTypeID
			AND mett.emailTagType = 'Primary'
		WHERE srfrp.siteID = @siteID
		AND LEN(me.Email) > 0;
	END
	ELSE BEGIN
		INSERT INTO #tmpRecipientsMID (memberID, recipientEmail, recipientEmailTypeID)
		SELECT mActive.memberID, me.email, me.emailTypeID
		FROM dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
		INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp ON srfrp.rightPrintID = gprp.rightPrintID
			and srfrp.siteResourceID = @publicationSiteResourceID
			AND srfrp.functionID = @functionID 
		INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID
			AND m.groupPrintID = gprp.groupPrintID
		INNER JOIN dbo.ams_members AS mActive on mActive.orgID = @orgID
			AND mActive.memberID = m.activeMemberID
		INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
			AND me.memberID = mActive.memberID
		INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
			AND metag.memberID = me.memberID 
			AND metag.emailTypeID = me.emailTypeID
		INNER JOIN dbo.pub_publicationEmailTagTypes as pet on pet.emailTagTypeID = metag.emailTagTypeID and pet.publicationID = @publicationID
		WHERE srfrp.siteID = @siteID
		AND LEN(me.Email) > 0
		GROUP BY mActive.memberID, me.email, me.emailTypeID;
	END

    IF @overrideMemberID IS NOT NULL
        DELETE FROM #tmpRecipientsMID
        WHERE memberID <> @overrideMemberID;

	IF @emailConsentListID IS NULL
		SELECT @emailConsentListID = cast(defaultConsentListID as varchar(max))
		FROM sites s 
		WHERE s.siteID = @siteID;

	SET @consentListIDs = NULL;
    IF @emailConsentListModeName = 'Opt-Out' BEGIN
		IF @isTestMessage = 0 BEGIN
			-- Normal processing: check actual recipient emails
			delete temp
			OUTPUT DELETED.recipientEmail, cl.consentListName INTO #tmpDeletedRecipients (recipientEmail, consentListName)
			from #tmpRecipientsMID temp
			inner join platformMail.dbo.email_consentListMembers clm
				on clm.consentListID IN (@emailConsentListID, @globalOptOutListID)
				and clm.email = temp.recipientEmail
			inner join platformMail.dbo.email_consentLists cl
				on cl.consentListID = clm.consentListID;
		END
		ELSE BEGIN
			-- Test message: check override email addresses
			-- First, check if any override emails are in opt-out lists
			DECLARE @optOutEmails VARCHAR(MAX) = '';

			SELECT @optOutEmails = @optOutEmails + CASE WHEN @optOutEmails = '' THEN '' ELSE ', ' END + '"' + oe.emailAddress + '"'
			FROM #tmpOverrideEmails oe
			INNER JOIN platformMail.dbo.email_consentListMembers clm
				ON clm.consentListID IN (@emailConsentListID, @globalOptOutListID)
				AND clm.email = oe.emailAddress
			INNER JOIN platformMail.dbo.email_consentLists cl
				ON cl.consentListID = clm.consentListID;

			-- If any override emails are in opt-out lists, delete all recipients and set message
			IF @optOutEmails <> '' BEGIN
				DELETE FROM #tmpRecipientsMID;
				SET @outputMessage = 'The test message will not be sent as the following email(s) are in opt-out list(s): ' + @optOutEmails + ' (' + @consentListName + ').';
			END
		END

		SELECT @consentListIDs = cast(@emailConsentListID as varchar(10));
	END
    IF @emailConsentListModeName = 'Opt-In' BEGIN
		IF @isTestMessage = 0 BEGIN
			-- Normal processing: check actual recipient emails
			delete temp
			from #tmpRecipientsMID temp
			left outer join platformMail.dbo.email_consentListMembers clm
				on clm.consentListID = @emailConsentListID
				and clm.email = temp.recipientEmail
			where clm.consentListMemberID is null;
		END
		ELSE BEGIN
			-- Test message: check override email addresses
			-- Find override emails that are NOT in the opt-in list
			DECLARE @notInOptInEmails VARCHAR(MAX) = '';

			SELECT @notInOptInEmails = @notInOptInEmails + CASE WHEN @notInOptInEmails = '' THEN '' ELSE ', ' END + '"' + oe.emailAddress + '"'
			FROM #tmpOverrideEmails oe
			LEFT JOIN platformMail.dbo.email_consentListMembers clm
				ON clm.consentListID = @emailConsentListID
				AND clm.email = oe.emailAddress
			WHERE clm.consentListMemberID IS NULL;

			-- If any override emails are NOT in opt-in list, delete all recipients and set message
			IF @notInOptInEmails <> '' BEGIN
				DELETE FROM #tmpRecipientsMID;
				SET @outputMessage = 'The test message will not be sent as the following email(s) are NOT in the opt-in list "' + @consentListName + '": ' + @notInOptInEmails + '.';
			END
		END
    END

	SELECT @numRecipients = COUNT(*) FROM #tmpRecipientsMID;

	IF @numRecipients = 0 AND @isTestMessage = 0
		RAISERROR('No recipients for message.',16,1);

	SELECT @messageTypeID = messageTypeID FROM platformMail.dbo.email_messageTypes WHERE messageTypeCode = 'PUBLICATIONS';
	SELECT @messageStatusIDInserting = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'I';
	SELECT @messageStatusIDQueued = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'Q';

	SET @itemGroupUID = NEWID();

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='emailExtMergeCode', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

	-- member consent list details
	select clt.consentListTypeID, clt.consentListTypeName as consentListType, cl.consentListID, cl.consentListName, 
		cl.consentListDesc, clm.modeName as consentListMode, oi.organizationName as consentListOrganization, oi.address1 as consentListAddress1, oi.address2 as consentListAddress2, oi.city as consentListCity, s.Name as consentListState,
		oi.postalCode as consentListPostalCode, c.country as consentListCountry,
		oi.phone as consentListPhone, oi.fax as consentListFax, oi.website as consentListWebsite, oi.XUserName as consentListXUsername, oi.email as consentListEmail
	INTO #tmpRecipientConsentLists
	from platformMail.dbo.email_consentListTypes as clt
	inner join platformMail.dbo.email_consentLists as cl 
		on cl.consentListTypeID = clt.consentListTypeID
		and cl.consentListID = @emailConsentListID
	inner join platformMail.dbo.email_consentListModes as clm on clm.consentListModeID = cl.consentListModeID
		inner join membercentral.dbo.orgIdentities oi
		on oi.orgIdentityID = cl.orgIdentityID
	inner join membercentral.dbo.ams_states as s on s.stateID = oi.stateID
	inner join membercentral.dbo.ams_countries as c 
		on c.countryID = s.countryID
	order by clt.orderNum, cl.orderNum;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsMID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@rawContent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    IF OBJECT_ID('tempdb..##tmpEmailPubIssues') IS NOT NULL 
        DROP TABLE ##tmpEmailPubIssues;

	IF @colList IS NULL
		SELECT memberID
		INTO ##tmpEmailPubIssues
		FROM #tmpRecipientsMID;
	ELSE BEGIN
		SET @vwSQL = 'SELECT m.memberid, ' + @colList + ' 
			INTO ##tmpEmailPubIssues 
			FROM #tmpRecipientsMID AS m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	SELECT m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, m.membernumber, m.firstname + ' ' + m.lastname AS fullname, 
		m.firstname + ISNULL(' ' + NULLIF(m.middlename,''),'') + ' ' + m.lastname + ISNULL(' ' + NULLIF(m.suffix,''),'') AS extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername,
		tmp.recipientEmail, tmp.recipientEmailTypeID, o.orgcode, cl.consentListName, cl.consentListType, cl.consentListMode, 
		cl.consentListOrganization, cl.consentListAddress1, cl.consentListAddress2, cl.consentListCity, cl.consentListState, cl.consentListPostalCode, 
		cl.consentListCountry, cl.consentListPhone, cl.consentListFax, cl.consentListEmail, cl.consentListWebsite, vw.*
	INTO #tmpRecipients
	FROM #tmpRecipientsMID AS tmp
	INNER JOIN dbo.ams_members AS m ON m.memberID = tmp.memberID
	INNER JOIN dbo.organizations as o WITH(NOLOCK) on o.orgID = m.orgID
	INNER JOIN ##tmpEmailPubIssues AS vw ON vw.memberID = m.memberID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID
	CROSS JOIN #tmpRecipientConsentLists AS cl;

	IF OBJECT_ID('tempdb..##tmpEmailPubIssues') IS NOT NULL 
		DROP TABLE ##tmpEmailPubIssues;

	-- get tmpRecipients columns
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));

	INSERT INTO #tmpRecipientsCols
	SELECT c.column_id, c.name, t.name
	FROM tempdb.sys.columns AS c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	WHERE c.object_id = OBJECT_ID('tempdb..#tmpRecipients');
	
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	-- find/insert merge code fields
	EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@issueID, @referenceType='publicationIssue',
		@messageToParse=@rawContent, @extraMergeCodeList='consentListManagementURL';

	-- add email_message
	EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
		@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=@isTestMessage, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
		@fromName=@emailEditionSenderName, @fromEmail=@supportProviderEmail, @replyToEmail=@emailEditionReplyToAddress, 
		@senderEmail='', @subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper='', 
		@referenceType='publicationIssue', @referenceID=@issueID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	-- we do it this way because insert with OUTPUT INTO can only refer to columns of the inserted table
	IF @isTestMessage = 1 AND EXISTS(SELECT 1 FROM #tmpOverrideEmails) BEGIN
		-- For test messages with override emails, create recipients for each override email
		MERGE INTO platformMail.dbo.email_messageRecipientHistory AS t
		USING (
			SELECT tmp.memberID, tmp.fullname, tmp.recipientEmailTypeID, oe.emailAddress
			FROM #tmpRecipients AS tmp
			CROSS JOIN #tmpOverrideEmails AS oe
		) AS src ON 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID, queuePriority)
			VALUES (@messageID, src.memberID, GETDATE(), src.fullname, src.emailAddress, @messageStatusIDInserting, NULL, NULL, src.recipientEmailTypeID, @siteID, @initialQueuePriority)
			OUTPUT INSERTED.recipientID, INSERTED.memberID
			INTO #tmpRecipientDetails (recipientID, recipientMemberID);
	END
	ELSE BEGIN
		-- Normal processing: use actual recipient emails
		MERGE INTO platformMail.dbo.email_messageRecipientHistory AS t USING #tmpRecipients AS tmp ON 1 = 0
		WHEN NOT MATCHED THEN
			INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
			VALUES (@messageID, memberID, GETDATE(), fullname, recipientEmail, @messageStatusIDInserting, NULL, NULL, recipientEmailTypeID, @siteID,@initialQueuePriority)
			OUTPUT INSERTED.recipientID, INSERTED.memberID
			INTO #tmpRecipientDetails (recipientID, recipientMemberID);
	END

	
	-- add recipient metadata
	SET @ParamDefinition = N'@messageID int, @fieldID int';
	SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 0;
	WHILE @fieldID IS NOT NULL BEGIN
		SELECT @fieldName = fieldName FROM #tmpEmailMetaDataFields WHERE fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (SELECT ORDINAL_POSITION FROM #tmpRecipientsCols WHERE column_name = @fieldName) BEGIN
			SET @fieldValueString = 'ISNULL(CAST(tmp.[' + @fieldName + '] AS VARCHAR(MAX)),'''')';

			SET @mcSQL = 'INSERT INTO platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				SELECT @messageID, @fieldID, rcp.recipientMemberID, fieldValue = ' + @fieldValueString + ', rcp.recipientID
				FROM #tmpRecipientDetails AS rcp
				INNER JOIN #tmpRecipients AS tmp ON rcp.recipientMemberID = tmp.memberID;';
			EXEC sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		SELECT @fieldID = MIN(fieldID) FROM #tmpEmailMetaDataFields WHERE isExtMergeCode = 0 AND fieldID > @fieldID;
	END

	-- has extended merge codes
	IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where isExtMergeCode = 1) BEGIN
		-- skip queue for test message
		IF @overrideMemberID IS NOT NULL
			GOTO on_done;

		-- queue recipient details with extended merge codes
		INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
		select itemUID, @insertingQueueStatusID
		from #tmpRecipientDetails;

		-- recipientID and messageID
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
		from #tmpRecipientDetails as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCRecipientID'
			union
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
		from #tmpRecipientDetails as tmp
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCMessageID';

		-- consentListEmail
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueString)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmpR.consentListEmail
		from #tmpRecipientDetails as tmp
		inner join #tmpRecipients AS tmpR ON tmpR.memberID = tmp.recipientMemberID
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCConsentListEmail';

		-- ext merge code fields
		INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
		select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
		from #tmpEmailMetaDataFields as mdf
		inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
		cross join #tmpRecipientDetails as tmp
		where mdf.isExtMergeCode = 1;

		-- update queue item groups to show ready to process
		UPDATE qi WITH (UPDLOCK, HOLDLOCK)
		SET qi.queueStatusID = @readyQueueStatusID,
			qi.dateUpdated = GETDATE()
		FROM platformQueue.dbo.tblQueueItems as qi
		INNER JOIN #tmpRecipientDetails as tmp on tmp.itemUID = qi.itemUID;
	END
	-- mark recipients as queued
	ELSE BEGIN
		UPDATE mrh 
		SET emailStatusID = @messageStatusIDQueued
		FROM platformMail.dbo.email_messages m
		INNER JOIN platformMail.dbo.email_messageRecipientHistory mrh ON m.messageID = mrh.messageID
		WHERE m.messageID = @messageID;
	END

	on_done:
	-- return recipients
	IF @isTestMessage = 1
	BEGIN
		IF @numRecipients = 0
			SELECT @outputMessage as outputMessage, @numRecipients as numRecipients
		ELSE 
			SELECT rcp.recipientID, rcp.recipientMemberID, tmp.recipientEmail, tmp.memberNumber, tmp.consentListEmail, @issueID, @messageID as messageID, @outputMessage as outputMessage, @numRecipients as numRecipients
			FROM #tmpRecipientDetails AS rcp
			INNER JOIN #tmpRecipients AS tmp ON tmp.memberID = rcp.recipientMemberID;
	END
	ELSE
		SELECT rcp.recipientID, rcp.recipientMemberID, tmp.recipientEmail, tmp.memberNumber, tmp.consentListEmail, @issueID, @messageID as messageID, @outputMessage as outputMessage, @numRecipients as numRecipients
		FROM #tmpRecipientDetails AS rcp
		INNER JOIN #tmpRecipients AS tmp ON tmp.memberID = rcp.recipientMemberID;


	IF OBJECT_ID('tempdb..#tmpRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpRecipientConsentLists') IS NOT NULL 
		DROP TABLE #tmpRecipientConsentLists;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpDeletedRecipients') IS NOT NULL
		DROP TABLE #tmpDeletedRecipients;
	IF OBJECT_ID('tempdb..#tmpOverrideEmails') IS NOT NULL
		DROP TABLE #tmpOverrideEmails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN -1;
END CATCH
GO
