{"version": 3, "sources": ["webpack:///./src/ui/src/components/ContextMenuPopup/ContextMenuPopup.scss?ba97", "webpack:///./src/ui/src/components/ContextMenuPopup/ContextMenuPopup.scss", "webpack:///./src/ui/src/components/ContextMenuPopup/ContextMenuPopup.js", "webpack:///./src/ui/src/components/ContextMenuPopup/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "OfficeActionItem", "dataElement", "onClick", "img", "title", "shortcut", "disabled", "t", "useTranslation", "dispatch", "useDispatch", "className", "classNames", "e", "actions", "closeElement", "DataElements", "CONTEXT_MENU_POPUP", "stopPropagation", "tabIndex", "data-element", "onKeyDown", "key", "Icon", "glyph", "ContextMenuPopup", "clickPosition", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "isDisabled", "isElementDisabled", "isRightClickAnnotationPopupEnabled", "isMultiViewerMode", "activeDocumentViewerKey", "getActiveDocumentViewerKey", "isCursorInTable", "isSpreadsheetEditorModeEnabled", "spreadsheetEditorEditMode", "getSpreadsheetEditorEditMode", "isReadOnlyMode", "SpreadsheetEditorEditMode", "VIEW_ONLY", "useState", "isSpreadsheetAndReadOnlyMode", "setIsSpreadsheetAndReadOnlyMode", "store", "useStore", "left", "top", "position", "setPosition", "popupRef", "useRef", "isMobile", "isMobileDevice", "isMobileCSS", "useOnClickOutside", "useEffect", "closeElements", "ANNOTATION_POPUP", "TEXT_POPUP", "INLINE_COMMENT_POPUP", "useLayoutEffect", "current", "getBoundingClientRect", "width", "height", "documentContainerSelector", "documentContainer", "getRootNode", "querySelector", "containerBox", "adjustPopupPosition", "clickPos", "getOffsetAdjustments", "offsetLeft", "offsetTop", "right", "bottom", "host", "hostBoundingRect", "scrollLeft", "scrollTop", "modifierKey", "isMac", "modifierKeyShort", "handlePaste", "withFormatting", "isFirefox", "message", "keyboardShortcut", "confirmBtnText", "warning", "onConfirm", "setTimeout", "core", "getViewerElement", "focus", "onCancel", "showWarningMessage", "getOfficeEditor", "pasteText", "contextMenuPopup", "open", "closed", "isOfficeEditor", "isOfficeEditorMode", "ref", "style", "FocusTrap", "locked", "OFFICE_EDITOR_CUT", "cutSelectedText", "isTextSelected", "OFFICE_EDITOR_COPY", "copySelectedText", "OFFICE_EDITOR_PASTE", "OFFICE_EDITOR_PASTE_WITHOUT_FORMATTING", "OFFICE_EDITOR_DELETE", "removeSelection", "isImageSelected", "OFFICE_EDITOR_INSERT_ROW_ABOVE", "insertRows", "OFFICE_EDITOR_INSERT_ROW_BELOW", "OFFICE_EDITOR_INSERT_COLUMN_RIGHT", "insertColumns", "OFFICE_EDITOR_INSERT_COLUMN_LEFT", "OFFICE_EDITOR_DELETE_ROW", "removeRows", "OFFICE_EDITOR_DELETE_COLUMN", "removeColumns", "OFFICE_EDITOR_DELETE_TABLE", "removeTable", "CustomizablePopup", "childrenClassName", "ActionButton", "label", "setToolModeAndGroup", "isIE", "cancel", "React", "memo"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,+7HAAg8H,KAGz9H0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,goFCYvB,IAAMC,EAAmB,SAAH,GAA8E,IAAxEC,EAAW,EAAXA,YAAaC,EAAO,EAAPA,QAASC,EAAG,EAAHA,IAAKC,EAAK,EAALA,MAAK,IAAEC,gBAAQ,IAAG,KAAE,MAAEC,gBAAQ,IAAG,GAAK,EACpFC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cASjB,OACE,yBACEC,UAAWC,IAAW,qBAAsB,CAAEN,aAC9CJ,QAAS,SAACW,GACHP,IACHJ,IACAO,EAASK,IAAQC,aAAaC,IAAaC,sBAG7CJ,EAAEK,mBAEJC,SAAUb,GAAY,EAAI,EAC1Bc,eAAcnB,EACdoB,UApBc,SAACR,GACH,UAAVA,EAAES,KAAoBhB,IACxBJ,IACAO,EAASK,IAAQC,aAAaC,IAAaC,wBAmB3C,yBAAKN,UAAU,cACZR,GAAO,kBAACoB,EAAA,EAAI,CAACC,MAAOrB,EAAKG,SAAUA,KAClCH,GAAO,0BAAMQ,UAAU,SACzB,6BAAMJ,EAAEH,KAEV,yBAAKO,UAAU,YAAYN,KAK3BoB,EAAmB,SAAH,GAEhB,IADJC,EAAa,EAAbA,cAGMC,EAASC,aAAY,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOb,IAAaC,uBAC5Ee,EAAaJ,aAAY,SAACC,GAAK,OAAKC,IAAUG,kBAAkBJ,EAAOb,IAAaC,uBACpFiB,EAAqCN,YAAYE,IAAUI,oCAC3DC,EAAoBP,YAAYE,IAAUK,mBAC1CC,EAA0BR,YAAYE,IAAUO,4BAChDC,EAAkBV,YAAYE,IAAUQ,iBACxCC,EAAiCX,YAAYE,IAAUS,gCACvDC,EAA4BZ,YAAYE,IAAUW,8BAClDC,EAAiBF,IAA8BG,IAA0BC,UAEmD,IAA1DC,mBAASN,GAAkCG,GAAe,GAA3HI,EAA4B,KAAEC,EAA+B,KAE7DxC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAEXsC,EAAQC,cAC+C,IAA7BJ,mBAAS,CAAEK,KAAM,EAAGC,IAAK,IAAI,GAAtDC,EAAQ,KAAEC,EAAW,KACtBC,EAAWC,mBAGXC,IAAaC,KAAkBC,cAErCC,YAAkBL,GAAU,WAC1B7C,EAASK,IAAQC,aAAaC,IAAaC,wBAG7C2C,qBAAU,WACJjC,GACFlB,EACEK,IAAQ+C,cAAc,CACpB7C,IAAa8C,iBACb9C,IAAa+C,WACb/C,IAAagD,0BAIlB,CAACrC,IAEJiC,qBAAU,WACR,IAAMlB,EAAiBF,IAA8BG,IAA0BC,UAC/EG,EAAgCR,GAAkCG,KACjE,CAACH,EAAgCC,IAEpCyB,2BAAgB,WACd,IAAInB,EAAJ,CAIA,MAA0BQ,EAASY,QAAQC,wBAAnCC,EAAK,EAALA,MAAOC,EAAM,EAANA,OACTC,EAA4BnC,EAAoB,qBAAH,OAAwBC,GAA4B,qBACjGmC,EAAoBC,cAAcC,cAAcH,GACtD,GAAKC,EAAL,CAIA,IAAMG,EAAeH,EAAkBJ,wBACvC,EAAsBQ,EAAoBjD,EAAegD,EAAcN,EAAOC,GAAtEnB,EAAI,EAAJA,KAAMC,EAAG,EAAHA,IACdE,EAAY,CAAEH,OAAMC,YACnB,CAACzB,EAAeS,EAAmBC,EAAyBU,IAK/D,IAAM6B,EAAsB,SAACC,EAAUF,EAAcN,EAAOC,GAC1D,IAAMnB,EAAc0B,EAAd1B,KAAMC,EAAQyB,EAARzB,IACZ,EAAkC0B,IAA1BC,EAAU,EAAVA,WAAYC,EAAS,EAATA,UAGpB5B,GAAO4B,EAqBP,OAtBA7B,GAAQ4B,GAMGJ,EAAaxB,KAAO4B,IAC7B5B,EAAOwB,EAAaxB,KAJA,EAIuB4B,GAGzC5B,EAAOkB,EAAQM,EAAaM,MAAQF,IACtC5B,EAAOwB,EAAaM,MAAQZ,EARR,EAQgCU,GAGlD3B,EAAMuB,EAAavB,IAAM4B,IAC3B5B,EAAMuB,EAAavB,IAXD,EAWqB4B,GAGrC5B,EAAMkB,EAASK,EAAaO,OAASF,IACvC5B,EAAMuB,EAAaO,OAASZ,EAfV,GAkBb,CAAEnB,OAAMC,QAMX0B,EAAuB,WAC3B,IAAIC,EAAa,EACbC,EAAY,EAEhB,GAAIxG,OAAOC,8BAA+B,OAClC0G,EAAoB,QAAhB,EAAGV,qBAAa,aAAb,EAAeU,KACtBC,EAAmBD,aAAI,EAAJA,EAAMf,wBAE3BgB,IACFL,EAAaK,EAAiBjC,KAC9B6B,EAAYI,EAAiBhC,IAG7B2B,GAAcI,EAAKE,WACnBL,GAAaG,EAAKG,WAGtB,MAAO,CAAEP,aAAYC,cAGjBO,EAAcC,IAAQ,YAAc,OACpCC,EAAmBD,IAAQ,OAAS,OAEpCE,EAAc,WAA2B,IAA1BC,IAAiB,UAAH,+CACjC,GAAKC,IAAL,CAKA,IAAMvF,EAAyBG,EAAjBmF,EAAmB,4BAAiC,0CAC5DE,EAA2BrF,EAAjBmF,EAAmB,8BAAmC,4CAChEG,EAAoC,GAAH,OAAMP,EAApBI,EAA+B,OAAuB,gBACzEI,EAAiBvF,EAAE,gBAEnBwF,EAAU,CACdH,QAAS,GAAF,OAAKA,EAAO,gBAAQC,GAC3BzF,QACA0F,iBACAE,UAAW,WAETC,YAAW,WACTC,IAAKC,mBAAmBC,YAG5BC,SAAU,WACRJ,YAAW,WACTC,IAAKC,mBAAmBC,aAI9B3F,EAASK,IAAQwF,mBAAmBP,SAzBlCG,IAAKK,kBAAkBC,UAAUd,IA6BrC,GAAI1D,EACF,OAAO,KAGT,IAAMyE,EACJ,yBACE9F,UAAWC,IAAW,QAAS,mBAAoB,CACjD8F,KAAM/E,EACNgF,QAAShF,EACTiF,eAAgBC,cAChB,cAAe3E,IAAuC2E,cACtD,iBAAkB3E,IAAuC2E,gBAE3DC,IAAKxD,EACLlC,eAAcJ,IAAaC,mBAC3B8F,MAAK,KAAO3D,GACZlD,QAAS,kBAAMO,EAASK,IAAQC,aAAaC,IAAaC,uBAE1D,kBAAC+F,EAAA,EAAS,CAACC,OAAQtF,GAA2B,IAAjByB,EAASD,KAA+B,IAAlBC,EAASF,MAC1D,yBAAKvC,UAAU,aACZkG,cACC,oCACE,kBAAC,EAAgB,CACfzG,MAAM,aACND,IAAI,WACJF,YAAae,IAAakG,kBAC1BhH,QAAS,kBAAMgG,IAAKK,kBAAkBY,mBACtC9G,SAAQ,UAAKmF,EAAgB,MAC7BlF,UAAW4F,IAAKK,kBAAkBa,mBAEpC,kBAAC,EAAgB,CACfhH,MAAM,cACND,IAAI,YACJF,YAAae,IAAaqG,mBAC1BnH,QAAS,kBAAMgG,IAAKK,kBAAkBe,oBACtCjH,SAAQ,UAAKmF,EAAgB,MAC7BlF,UAAW4F,IAAKK,kBAAkBa,mBAEpC,kBAAC,EAAgB,CACfhH,MAAM,eACND,IAAI,aACJF,YAAae,IAAauG,oBAC1BrH,QAAS,kBAAMuF,KACfpF,SAAQ,UAAKmF,EAAgB,QAE/B,kBAAC,EAAgB,CACfpF,MAAM,gCACND,IAAI,gCACJF,YAAae,IAAawG,uCAC1BtH,QAAS,kBAAMuF,GAAY,IAC3BpF,SAAQ,UAAKmF,EAAgB,eAE7BlD,GACA,kBAAC,EAAgB,CACflC,MAAM,gBACND,IAAI,mBACJF,YAAae,IAAayG,qBAC1BvH,QAAS,kBAAMgG,IAAKK,kBAAkBmB,mBACtCpH,WAAY4F,IAAKK,kBAAkBa,kBAAoBlB,IAAKK,kBAAkBoB,qBAGjFrF,GACC,oCACE,yBAAK3B,UAAU,YACf,kBAAC,EAAgB,CACfP,MAAM,8BACNH,YAAae,IAAa4G,+BAC1B1H,QAAS,kBAAMgG,IAAKK,kBAAkBsB,WAAW,GAAG,MAEtD,kBAAC,EAAgB,CACfzH,MAAM,8BACNH,YAAae,IAAa8G,+BAC1B5H,QAAS,kBAAMgG,IAAKK,kBAAkBsB,WAAW,GAAG,MAEtD,kBAAC,EAAgB,CACfzH,MAAM,iCACNH,YAAae,IAAa+G,kCAC1B7H,QAAS,kBAAMgG,IAAKK,kBAAkByB,cAAc,GAAG,MAEzD,kBAAC,EAAgB,CACf5H,MAAM,gCACNH,YAAae,IAAaiH,iCAC1B/H,QAAS,kBAAMgG,IAAKK,kBAAkByB,cAAc,GAAG,MAEzD,kBAAC,EAAgB,CACf5H,MAAM,yBACNH,YAAae,IAAakH,yBAC1BhI,QAAS,kBAAMgG,IAAKK,kBAAkB4B,gBAExC,kBAAC,EAAgB,CACf/H,MAAM,4BACNH,YAAae,IAAaoH,4BAC1BlI,QAAS,kBAAMgG,IAAKK,kBAAkB8B,mBAExC,kBAAC,EAAgB,CACfjI,MAAM,2BACNH,YAAae,IAAasH,2BAC1BpI,QAAS,kBAAMgG,IAAKK,kBAAkBgC,mBAM9C,kBAACC,EAAA,EAAiB,CAChBvI,YAAae,IAAaC,mBAC1BwH,kBAAkB,oBAElB,kBAACC,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,gBACZ0I,MAAOzG,EAAqC,WAAa,GACzD9B,MAAQ8B,EAAkD,GAAb,WAC7C/B,IAAI,kBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,UAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,mBACZ0I,MAAOzG,EAAqC,wBAA0B,GACtE9B,MAAQ8B,EAA+D,GAA1B,wBAC7C/B,IAAI,yBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,6BAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,sBACZ0I,MAAOzG,EAAqC,uBAAyB,GACrE9B,MAAQ8B,EAA8D,GAAzB,uBAC7C/B,IAAI,sBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,oCAG5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,qBACZ0I,MAAOzG,EAAqC,sBAAwB,GACpE9B,MAAQ8B,EAA6D,GAAxB,sBAC7C/B,IAAI,qBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,+BAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,8BACZ0I,MAAOzG,EAAqC,+BAAiC,GAC7E9B,MAAQ8B,EAAsE,GAAjC,+BAC7C/B,IAAI,0BACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,wCAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,qBACZ0I,MAAOzG,EAAqC,sBAAwB,GACpE9B,MAAQ8B,EAA6D,GAAxB,sBAC7C/B,IAAI,2BACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,+BAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,2BACZ0I,MAAOzG,EAAqC,4BAA8B,GAC1E9B,MAAQ8B,EAAmE,GAA9B,4BAC7C/B,IAAI,iBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,qCAE5C,kBAAC0F,EAAA,EAAY,CACX/H,UAAU,mBACVV,YAAY,4BACZ0I,MAAOzG,EAAqC,6BAA+B,GAC3E9B,MAAQ8B,EAAoE,GAA/B,6BAC7C/B,IAAI,kBACJD,QAAS,kBAAM0I,YAAoB5F,EAAO,0CASxD,OAAIF,EACK,KAGF+F,KAAQrF,EACbiD,EAEA,kBAAC,IAAS,CAACqC,OAAO,iEAAiErC,IAIxEsC,MAAMC,KAAKvH,GC/YXA", "file": "chunks/chunk.60.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ContextMenuPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.ContextMenuPopup{visibility:visible}.closed.ContextMenuPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ContextMenuPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.ContextMenuPopup:empty{padding:0}.ContextMenuPopup .buttons{display:flex}.ContextMenuPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button{width:42px;height:42px}}.ContextMenuPopup .Button:hover{background:var(--popup-button-hover)}.ContextMenuPopup .Button:hover:disabled{background:none}.ContextMenuPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ContextMenuPopup .Button .Icon{width:24px;height:24px}}.is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.ContextMenuPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.ContextMenuPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.ContextMenuPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.ContextMenuPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.ContextMenuPopup{box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);border-radius:4px}.ContextMenuPopup.is-horizontal .container{display:inherit}.ContextMenuPopup.is-vertical{flex-direction:column;align-items:flex-start}.ContextMenuPopup.isOfficeEditor .container{display:block}.ContextMenuPopup.isOfficeEditor .container .office-action-item{width:300px;padding:8px;display:flex;justify-content:space-between;cursor:pointer}.ContextMenuPopup.isOfficeEditor .container .office-action-item:hover{background-color:var(--blue-4)}.ContextMenuPopup.isOfficeEditor .container .office-action-item.disabled{cursor:default;background-color:inherit;color:var(--disabled-text)}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title{display:flex;align-items:center}.ContextMenuPopup.isOfficeEditor .container .office-action-item .icon-title .Icon{margin-right:10px}.ContextMenuPopup.isOfficeEditor .container .office-action-item .shortcut{display:flex;align-items:center}.ContextMenuPopup .divider{height:1px;background:var(--divider);margin-top:8px;margin-bottom:8px;width:100%}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';\nimport classNames from 'classnames';\nimport Draggable from 'react-draggable';\nimport { useSelector, useDispatch, useStore } from 'react-redux';\nimport FocusTrap from 'components/FocusTrap';\nimport { useTranslation } from 'react-i18next';\nimport ActionButton from 'components/ActionButton';\nimport CustomizablePopup from 'components/CustomizablePopup';\nimport Icon from 'components/Icon';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport setToolModeAndGroup from 'helpers/setToolModeAndGroup';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { isMobile as isMobileCSS, isIE, isMobileDevice, isFirefox, isMac } from 'helpers/device';\nimport { isOfficeEditorMode } from 'helpers/officeEditor';\nimport getRootNode from 'helpers/getRootNode';\nimport DataElements from 'constants/dataElement';\nimport { SpreadsheetEditorEditMode } from 'constants/spreadsheetEditor';\n\nimport './ContextMenuPopup.scss';\n\nconst OfficeActionItem = ({ dataElement, onClick, img, title, shortcut = '', disabled = false }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const onKeyDown = (e) => {\n    if (e.key === 'Enter' && !disabled) {\n      onClick();\n      dispatch(actions.closeElement(DataElements.CONTEXT_MENU_POPUP));\n    }\n  };\n\n  return (\n    <div\n      className={classNames('office-action-item', { disabled })}\n      onClick={(e) => {\n        if (!disabled) {\n          onClick();\n          dispatch(actions.closeElement(DataElements.CONTEXT_MENU_POPUP));\n        }\n        // prevent bubbling up click event to control when context menu is closed within this component\n        e.stopPropagation();\n      }}\n      tabIndex={disabled ? -1 : 0}\n      data-element={dataElement}\n      onKeyDown={onKeyDown}\n    >\n      <div className=\"icon-title\">\n        {img && <Icon glyph={img} disabled={disabled} />}\n        {!img && <span className=\"Icon\"></span>}\n        <div>{t(title)}</div>\n      </div>\n      <div className=\"shortcut\">{shortcut}</div>\n    </div>\n  );\n};\n\nconst ContextMenuPopup = ({\n  clickPosition,\n}) => {\n\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.CONTEXT_MENU_POPUP));\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.CONTEXT_MENU_POPUP));\n  const isRightClickAnnotationPopupEnabled = useSelector(selectors.isRightClickAnnotationPopupEnabled);\n  const isMultiViewerMode = useSelector(selectors.isMultiViewerMode);\n  const activeDocumentViewerKey = useSelector(selectors.getActiveDocumentViewerKey);\n  const isCursorInTable = useSelector(selectors.isCursorInTable);\n  const isSpreadsheetEditorModeEnabled = useSelector(selectors.isSpreadsheetEditorModeEnabled);\n  const spreadsheetEditorEditMode = useSelector(selectors.getSpreadsheetEditorEditMode);\n  const isReadOnlyMode = spreadsheetEditorEditMode === SpreadsheetEditorEditMode.VIEW_ONLY;\n\n  const [isSpreadsheetAndReadOnlyMode, setIsSpreadsheetAndReadOnlyMode] = useState(isSpreadsheetEditorModeEnabled && isReadOnlyMode);\n\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  // this is hacky, hopefully we can remove this when tool group button is restructured\n  const store = useStore();\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const popupRef = useRef();\n  // if right click menu is not turned on, on tablet + phone, ContextMenuPopup won't be available\n  // if it's on, on tablet + phone, it will be available without being draggable\n  const isMobile = !!isMobileDevice || isMobileCSS();\n\n  useOnClickOutside(popupRef, () => {\n    dispatch(actions.closeElement(DataElements.CONTEXT_MENU_POPUP));\n  });\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(\n        actions.closeElements([\n          DataElements.ANNOTATION_POPUP,\n          DataElements.TEXT_POPUP,\n          DataElements.INLINE_COMMENT_POPUP,\n        ])\n      );\n    }\n  }, [isOpen]);\n\n  useEffect(() => {\n    const isReadOnlyMode = spreadsheetEditorEditMode === SpreadsheetEditorEditMode.VIEW_ONLY;\n    setIsSpreadsheetAndReadOnlyMode(isSpreadsheetEditorModeEnabled && isReadOnlyMode);\n  }, [isSpreadsheetEditorModeEnabled, spreadsheetEditorEditMode]);\n\n  useLayoutEffect(() => {\n    if (isSpreadsheetAndReadOnlyMode) {\n      return;\n    }\n\n    const { width, height } = popupRef.current.getBoundingClientRect();\n    const documentContainerSelector = isMultiViewerMode ? `#DocumentContainer${activeDocumentViewerKey}` : '.DocumentContainer';\n    const documentContainer = getRootNode().querySelector(documentContainerSelector);\n    if (!documentContainer) {\n      return;\n    }\n\n    const containerBox = documentContainer.getBoundingClientRect();\n    const { left, top } = adjustPopupPosition(clickPosition, containerBox, width, height);\n    setPosition({ left, top });\n  }, [clickPosition, isMultiViewerMode, activeDocumentViewerKey, isSpreadsheetAndReadOnlyMode]);\n\n  /**\n   * Adjusts the position of the popup relative to the container.\n   */\n  const adjustPopupPosition = (clickPos, containerBox, width, height) => {\n    let { left, top } = clickPos;\n    const { offsetLeft, offsetTop } = getOffsetAdjustments();\n\n    left -= offsetLeft;\n    top -= offsetTop;\n\n    const horizontalGap = 2;\n    const verticalGap = 2;\n\n    if (left < containerBox.left - offsetLeft) {\n      left = containerBox.left + horizontalGap - offsetLeft;\n    }\n\n    if (left + width > containerBox.right - offsetLeft) {\n      left = containerBox.right - width - horizontalGap - offsetLeft;\n    }\n\n    if (top < containerBox.top - offsetTop) {\n      top = containerBox.top + verticalGap - offsetTop;\n    }\n\n    if (top + height > containerBox.bottom - offsetTop) {\n      top = containerBox.bottom - height - verticalGap;\n    }\n\n    return { left, top };\n  };\n\n  /**\n   * Retrieves offset adjustments if the app is running inside a Web Component.\n   */\n  const getOffsetAdjustments = () => {\n    let offsetLeft = 0;\n    let offsetTop = 0;\n\n    if (window.isApryseWebViewerWebComponent) {\n      const host = getRootNode()?.host;\n      const hostBoundingRect = host?.getBoundingClientRect();\n\n      if (hostBoundingRect) {\n        offsetLeft = hostBoundingRect.left;\n        offsetTop = hostBoundingRect.top;\n\n        // Include host scroll offsets\n        offsetLeft += host.scrollLeft;\n        offsetTop += host.scrollTop;\n      }\n    }\n    return { offsetLeft, offsetTop };\n  };\n\n  const modifierKey = isMac ? '⌘ Command' : 'Ctrl';\n  const modifierKeyShort = isMac ? '⌘Cmd' : 'Ctrl';\n\n  const handlePaste = (withFormatting = true) => {\n    if (!isFirefox) {\n      core.getOfficeEditor().pasteText(withFormatting);\n      return;\n    }\n\n    const title = withFormatting ? t('officeEditor.pastingTitle') : t('officeEditor.pastingWithoutFormatTitle');\n    const message = withFormatting ? t('officeEditor.pastingMessage') : t('officeEditor.pastingWithoutFormatMessage');\n    const keyboardShortcut = withFormatting ? `${modifierKey} + V` : `${modifierKey} + Shift + V`;\n    const confirmBtnText = t('action.close');\n\n    const warning = {\n      message: `${message}:\\n\\n${keyboardShortcut}`,\n      title,\n      confirmBtnText,\n      onConfirm: () => {\n        // setTimeout needed because the focus can not be set immediately after closing the warning modal\n        setTimeout(() => {\n          core.getViewerElement().focus();\n        });\n      },\n      onCancel: () => {\n        setTimeout(() => {\n          core.getViewerElement().focus();\n        });\n      },\n    };\n    dispatch(actions.showWarningMessage(warning));\n\n  };\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const contextMenuPopup = (\n    <div\n      className={classNames('Popup', 'ContextMenuPopup', {\n        open: isOpen,\n        closed: !isOpen,\n        isOfficeEditor: isOfficeEditorMode(),\n        'is-vertical': isRightClickAnnotationPopupEnabled && !isOfficeEditorMode(),\n        'is-horizontal': !isRightClickAnnotationPopupEnabled && !isOfficeEditorMode(),\n      })}\n      ref={popupRef}\n      data-element={DataElements.CONTEXT_MENU_POPUP}\n      style={{ ...position }}\n      onClick={() => dispatch(actions.closeElement(DataElements.CONTEXT_MENU_POPUP))}\n    >\n      <FocusTrap locked={isOpen && position.top !== 0 && position.left !== 0}>\n        <div className=\"container\">\n          {isOfficeEditorMode() ? (\n            <>\n              <OfficeActionItem\n                title=\"action.cut\"\n                img=\"icon-cut\"\n                dataElement={DataElements.OFFICE_EDITOR_CUT}\n                onClick={() => core.getOfficeEditor().cutSelectedText()}\n                shortcut={`${modifierKeyShort}+X`}\n                disabled={!core.getOfficeEditor().isTextSelected()}\n              />\n              <OfficeActionItem\n                title=\"action.copy\"\n                img=\"icon-copy\"\n                dataElement={DataElements.OFFICE_EDITOR_COPY}\n                onClick={() => core.getOfficeEditor().copySelectedText()}\n                shortcut={`${modifierKeyShort}+C`}\n                disabled={!core.getOfficeEditor().isTextSelected()}\n              />\n              <OfficeActionItem\n                title=\"action.paste\"\n                img=\"icon-paste\"\n                dataElement={DataElements.OFFICE_EDITOR_PASTE}\n                onClick={() => handlePaste()}\n                shortcut={`${modifierKeyShort}+V`}\n              />\n              <OfficeActionItem\n                title=\"action.pasteWithoutFormatting\"\n                img=\"icon-paste-without-formatting\"\n                dataElement={DataElements.OFFICE_EDITOR_PASTE_WITHOUT_FORMATTING}\n                onClick={() => handlePaste(false)}\n                shortcut={`${modifierKeyShort}+Shift+V`}\n              />\n              {!isCursorInTable && (\n                <OfficeActionItem\n                  title=\"action.delete\"\n                  img=\"icon-delete-line\"\n                  dataElement={DataElements.OFFICE_EDITOR_DELETE}\n                  onClick={() => core.getOfficeEditor().removeSelection()}\n                  disabled={!(core.getOfficeEditor().isTextSelected() || core.getOfficeEditor().isImageSelected())}\n                />\n              )}\n              {isCursorInTable && (\n                <>\n                  <div className=\"divider\"></div>\n                  <OfficeActionItem\n                    title=\"officeEditor.insertRowAbove\"\n                    dataElement={DataElements.OFFICE_EDITOR_INSERT_ROW_ABOVE}\n                    onClick={() => core.getOfficeEditor().insertRows(1, true)}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.insertRowBelow\"\n                    dataElement={DataElements.OFFICE_EDITOR_INSERT_ROW_BELOW}\n                    onClick={() => core.getOfficeEditor().insertRows(1, false)}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.insertColumnRight\"\n                    dataElement={DataElements.OFFICE_EDITOR_INSERT_COLUMN_RIGHT}\n                    onClick={() => core.getOfficeEditor().insertColumns(1, true)}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.insertColumnLeft\"\n                    dataElement={DataElements.OFFICE_EDITOR_INSERT_COLUMN_LEFT}\n                    onClick={() => core.getOfficeEditor().insertColumns(1, false)}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.deleteRow\"\n                    dataElement={DataElements.OFFICE_EDITOR_DELETE_ROW}\n                    onClick={() => core.getOfficeEditor().removeRows()}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.deleteColumn\"\n                    dataElement={DataElements.OFFICE_EDITOR_DELETE_COLUMN}\n                    onClick={() => core.getOfficeEditor().removeColumns()}\n                  />\n                  <OfficeActionItem\n                    title=\"officeEditor.deleteTable\"\n                    dataElement={DataElements.OFFICE_EDITOR_DELETE_TABLE}\n                    onClick={() => core.getOfficeEditor().removeTable()}\n                  />\n                </>\n              )}\n            </>\n          ) : (\n            <CustomizablePopup\n              dataElement={DataElements.CONTEXT_MENU_POPUP}\n              childrenClassName='main-menu-button'\n            >\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"panToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'tool.pan' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'tool.pan' : ''}\n                img=\"icon-header-pan\"\n                onClick={() => setToolModeAndGroup(store, 'Pan')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"stickyToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.stickyNote' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.stickyNote' : ''}\n                img=\"icon-tool-comment-line\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateSticky')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"highlightToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.highlight' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.highlight' : ''}\n                img=\"icon-tool-highlight\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateTextHighlight')\n                }\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"freeHandToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.freehand' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.freehand' : ''}\n                img=\"icon-tool-pen-line\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateFreeHand')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"freeHandHighlightToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.freeHandHighlight' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.freeHandHighlight' : ''}\n                img=\"icon-tool-pen-highlight\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateFreeHandHighlight')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"freeTextToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.freetext' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.freetext' : ''}\n                img=\"icon-tool-text-free-text\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateFreeText')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"markInsertTextToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.markInsertText' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.markInsertText' : ''}\n                img=\"ic-insert text\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateMarkInsertText')}\n              />\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"markReplaceTextToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'annotation.markReplaceText' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'annotation.markReplaceText' : ''}\n                img=\"ic-replace text\"\n                onClick={() => setToolModeAndGroup(store, 'AnnotationCreateMarkReplaceText')}\n              />\n            </CustomizablePopup>\n          )}\n        </div>\n      </FocusTrap>\n    </div>\n  );\n\n  if (isSpreadsheetAndReadOnlyMode) {\n    return null;\n  }\n\n  return isIE || isMobile ? (\n    contextMenuPopup\n  ) : (\n    <Draggable cancel=\".Button, .cell, .sliders-container svg, select, button, input\">{contextMenuPopup}</Draggable>\n  );\n};\n\nexport default React.memo(ContextMenuPopup);\n", "import ContextMenuPopup from './ContextMenuPopup';\n\nexport default ContextMenuPopup;"], "sourceRoot": ""}