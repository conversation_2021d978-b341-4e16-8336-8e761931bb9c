{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ca.js"], "names": ["module", "exports", "e", "t", "default", "s", "_", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "weekStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "ll", "lll", "llll", "relativeTime", "future", "past", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,8DAA8DC,MAAM,KAAKC,cAAc,8BAA8BD,MAAM,KAAKE,YAAY,uBAAuBF,MAAM,KAAKG,OAAO,oFAAoFH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,UAAU,EAAEC,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,mBAAmBC,IAAI,gCAAgCC,KAAK,qCAAqCC,GAAG,aAAaC,IAAI,mBAAmBC,KAAK,wBAAwBC,aAAa,CAACC,OAAO,YAAYC,KAAK,QAAQtB,EAAE,aAAauB,EAAE,WAAWC,GAAG,YAAYC,EAAE,WAAWC,GAAG,WAAWC,EAAE,SAASC,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,WAAWC,QAAQ,SAASpC,GAAG,OAASA,GAAG,IAAIA,GAAG,IAAIA,EAAE,IAAI,IAAIA,EAAE,IAAI,IAAIA,EAAE,IAAI,OAAO,OAAOC,EAAEC,QAAQmC,OAAOjC,EAAE,MAAK,GAAIA,EAAlqCD,CAAE,EAAQ", "file": "chunks/chunk.123.js", "sourcesContent": ["!function(e,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ca=s(e.dayjs)}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=s(e),_={name:\"ca\",weekdays:\"Diumenge_Dilluns_Dimarts_Dimecres_Dijous_Divendres_Dissabte\".split(\"_\"),weekdaysShort:\"Dg._Dl._Dt._Dc._Dj._Dv._Ds.\".split(\"_\"),weekdaysMin:\"Dg_Dl_Dt_Dc_Dj_Dv_Ds\".split(\"_\"),months:\"Gener_Febrer_Març_Abril_Maig_Juny_Juliol_Agost_Setembre_Octubre_Novembre_Desembre\".split(\"_\"),monthsShort:\"Gen._Febr._Març_Abr._Maig_Juny_Jul._Ag._Set._Oct._Nov._Des.\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM [de] YYYY\",LLL:\"D MMMM [de] YYYY [a les] H:mm\",LLLL:\"dddd D MMMM [de] YYYY [a les] H:mm\",ll:\"D MMM YYYY\",lll:\"D MMM YYYY, H:mm\",llll:\"ddd D MMM YYYY, H:mm\"},relativeTime:{future:\"d'aquí %s\",past:\"fa %s\",s:\"uns segons\",m:\"un minut\",mm:\"%d minuts\",h:\"una hora\",hh:\"%d hores\",d:\"un dia\",dd:\"%d dies\",M:\"un mes\",MM:\"%d mesos\",y:\"un any\",yy:\"%d anys\"},ordinal:function(e){return\"\"+e+(1===e||3===e?\"r\":2===e?\"n\":4===e?\"t\":\"è\")}};return t.default.locale(_,null,!0),_}));"], "sourceRoot": ""}