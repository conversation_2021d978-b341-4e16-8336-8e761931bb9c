(window.webpackJsonp = window.webpackJsonp || []).push([
	[183],
	{
		1441: function (_, t, e) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					e = {
						name: "lo",
						weekdays: "ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),
						months:
							"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split(
								"_",
							),
						weekdaysShort: "ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),
						monthsShort:
							"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split(
								"_",
							),
						weekdaysMin: "ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),
						ordinal: function (_) {
							return _;
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY HH:mm",
							LLLL: "ວັນdddd D MMMM YYYY HH:mm",
						},
						relativeTime: {
							future: "ອີກ %s",
							past: "%sຜ່ານມາ",
							s: "ບໍ່ເທົ່າໃດວິນາທີ",
							m: "1 ນາທີ",
							mm: "%d ນາທີ",
							h: "1 ຊົ່ວໂມງ",
							hh: "%d ຊົ່ວໂມງ",
							d: "1 ມື້",
							dd: "%d ມື້",
							M: "1 ເດືອນ",
							MM: "%d ເດືອນ",
							y: "1 ປີ",
							yy: "%d ປີ",
						},
					};
				return t.default.locale(e, null, !0), e;
			})(e(105));
		},
	},
]);
//# sourceMappingURL=chunk.183.js.map
