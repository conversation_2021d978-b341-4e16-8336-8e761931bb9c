(window.webpackJsonp = window.webpackJsonp || []).push([
	[91],
	{
		2010: function (e, t, a) {
			"use strict";
			a.r(t);
			a(58), a(44);
			var r = a(0),
				o = a.n(r),
				n = a(82),
				s = a(3),
				l = a.n(s),
				i = a(68),
				c = a(70),
				u = a(7),
				d = a(1);
			function m() {
				return (m = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var a = arguments[t];
								for (var r in a)
									Object.prototype.hasOwnProperty.call(a, r) && (e[r] = a[r]);
							}
							return e;
						}).apply(this, arguments);
			}
			var E = {
					type: l.a.string,
					isFlyoutItem: l.a.bool,
					style: l.a.object,
					className: l.a.string,
					buttonType: l.a.string,
				},
				v = Object(r.forwardRef)(function (e, t) {
					var a = e.isFlyoutItem,
						r = e.type,
						s = e.style,
						l = e.className,
						E = e.buttonType,
						v = c.b[E],
						R = v.dataElement,
						T = v.icon,
						f = v.title,
						C = function () {
							var e,
								t,
								a =
									null === (e = d.a.getDocument()) ||
									void 0 === e ||
									null === (t = e.getSpreadsheetEditorDocument()) ||
									void 0 === t
										? void 0
										: t.getWorkbook(),
								r = null == a ? void 0 : a.getSheetAt(a.activeSheetIndex);
							if (!r)
								return console.error("SpreadsheetEditorDocument is not loaded");
							var o = d.a
								.getDocumentViewer()
								.getSpreadsheetEditorManager()
								.getSelectedCellRange();
							switch (E) {
								case u.a.INSERT_COLUMN_LEFT:
									return void r.createColumns(o.firstColumn, 1);
								case u.a.INSERT_COLUMN_RIGHT:
									return void r.createColumns(o.lastColumn + 1, 1);
								case u.a.INSERT_ROW_TOP:
									return void r.createRow(o.firstRow);
								case u.a.INSERT_ROW_BOTTOM:
									return void r.createRow(o.lastRow + 1);
								case u.a.INSERT_COLUMN_SHIFT_DOWN:
									return;
								case u.a.INSERT_COLUMN_SHIFT_RIGHT:
									for (var n = o.firstRow; n <= o.lastRow; n++)
										for (
											var s = r.getRowAt(n), l = o.firstColumn;
											l <= o.lastColumn;
											l++
										)
											s.createCell(l);
									return;
								case u.a.DELETE_COLUMN:
									return void r.removeColumns(
										o.firstColumn,
										o.lastColumn - o.firstColumn + 1,
									);
								case u.a.DELETE_ROW:
									return void r.removeRows(
										o.firstRow,
										o.lastRow - o.firstRow + 1,
									);
								case u.a.DELETE_COLUMN_SHIFT_UP:
								case u.a.DELETE_COLUMN_SHIFT_LEFT:
								default:
									return;
							}
						};
					return a
						? o.a.createElement(
								i.a,
								m({}, e, { ref: t, onClick: C, additionalClass: "" }),
							)
						: o.a.createElement(n.a, {
								key: r,
								isActive: !1,
								onClick: C,
								dataElement: R,
								title: f,
								img: T,
								ariaPressed: !1,
								style: s,
								className: l,
							});
				});
			(v.propTypes = E),
				(v.displayName = "CellAdjustmentButton"),
				(t.default = v);
		},
	},
]);
//# sourceMappingURL=chunk.91.js.map
