/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[22],
		{
			644: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(7),
					ma = r(2);
				ya = r(53);
				var ja = r(36),
					ia = r(19);
				r = (function () {
					function ca() {
						this.init();
					}
					ca.prototype.init = function () {
						this.pua = !1;
						this.Ug = this.Hp = this.connection = null;
						this.Ql = {};
						this.ga = this.eQ = null;
					};
					ca.prototype.YP = function (y) {
						for (var x = this, n = 0; n < y.length; ++n) {
							var h = y[n];
							switch (h.at) {
								case "create":
									this.Ql[h.author] || (this.Ql[h.author] = h.aName);
									this.iGa(h);
									break;
								case "modify":
									this.ga.ut(h.xfdf).then(function (b) {
										x.ga.Eb(b[0]);
									});
									break;
								case "delete":
									(h = "<delete><id>".concat(h.aId, "</id></delete>")),
										this.ga.ut(h);
							}
						}
					};
					ca.prototype.iGa = function (y) {
						var x = this;
						this.ga.ut(y.xfdf).then(function (n) {
							n = n[0];
							n.authorId = y.author;
							x.ga.Eb(n);
							x.ga.trigger(na.d.UPDATE_ANNOTATION_PERMISSION, [n]);
						});
					};
					ca.prototype.XEa = function (y, x, n) {
						this.Hp && this.Hp(y, x, n);
					};
					ca.prototype.preloadAnnotations = function (y) {
						this.addEventListener(
							"webViewerServerAnnotationsEnabled",
							this.XEa.bind(this, y, "add", { imported: !1 }),
							{ once: !0 },
						);
					};
					ca.prototype.initiateCollaboration = function (y, x, n) {
						var h = this;
						if (y) {
							h.Ug = x;
							h.ga = n.ia();
							n.addEventListener(na.i.DOCUMENT_UNLOADED, function () {
								h.disableCollaboration();
							});
							h.eWa(y);
							var b = new XMLHttpRequest();
							b.addEventListener("load", function () {
								if (200 === b.status && 0 < b.responseText.length)
									try {
										var e = JSON.parse(b.responseText);
										h.connection = exports.da.EXa(
											Object(ja.l)(h.Ug, "blackbox/"),
											"annot",
										);
										h.eQ = e.id;
										h.Ql[e.id] = e.user_name;
										h.ga.Y_(e.id);
										h.connection.n0a(
											function (f) {
												f.t && f.t.startsWith("a_") && f.data && h.YP(f.data);
											},
											function () {
												h.connection.send({ t: "a_retrieve", dId: y });
												h.trigger(
													ca.Events.WEBVIEWER_SERVER_ANNOTATIONS_ENABLED,
													[h.Ql[e.id], h.eQ],
												);
											},
											function () {
												h.disableCollaboration();
											},
										);
									} catch (f) {
										Object(ma.f)(f.message);
									}
							});
							b.open("GET", Object(ja.l)(this.Ug, "demo/SessionInfo.jsp"));
							b.withCredentials = !0;
							b.send();
							h.pua = !0;
							h.ga.pha(function (e) {
								return h.Ql[e.Author] || e.Author;
							});
						} else Object(ma.f)("Document ID required for collaboration");
					};
					ca.prototype.disableCollaboration = function () {
						this.Hp &&
							(this.ga.removeEventListener(
								ia.a.Events.ANNOTATION_CHANGED,
								this.Hp,
							),
							(this.Hp = null));
						this.connection && this.connection.Iv();
						this.ga && this.ga.Y_("Guest");
						this.init();
						this.trigger(ca.Events.WEBVIEWER_SERVER_ANNOTATIONS_DISABLED);
					};
					ca.prototype.eWa = function (y) {
						var x = this;
						this.Hp &&
							this.ga.removeEventListener(
								ia.a.Events.ANNOTATION_CHANGED,
								this.Hp,
							);
						this.Hp = function (n, h, b) {
							return Object(la.b)(this, void 0, void 0, function () {
								var e, f, a, w, z, aa, ea, ba, fa;
								return Object(la.d)(this, function (ha) {
									switch (ha.label) {
										case 0:
											if (b.imported) return [2];
											e = { t: "a_".concat(h), dId: y, annots: [] };
											return [4, x.ga.o9()];
										case 1:
											f = ha.aa();
											"delete" !== h &&
												((a = new DOMParser().parseFromString(f, "text/xml")),
												(w = new XMLSerializer()));
											for (z = 0; z < n.length; z++)
												(aa = n[z]),
													(ba = ea = void 0),
													"add" === h
														? ((ea = a.querySelector(
																'[name="'.concat(aa.Id, '"]'),
															)),
															(ba = w.serializeToString(ea)),
															(fa = null),
															aa.InReplyTo &&
																(fa =
																	x.ga.Hi(aa.InReplyTo).authorId || "default"),
															e.annots.push({
																at: "create",
																aId: aa.Id,
																author: x.eQ,
																aName: x.Ql[x.eQ],
																parent: fa,
																xfdf: "<add>".concat(ba, "</add>"),
															}))
														: "modify" === h
															? ((ea = a.querySelector(
																	'[name="'.concat(aa.Id, '"]'),
																)),
																(ba = w.serializeToString(ea)),
																e.annots.push({
																	at: "modify",
																	aId: aa.Id,
																	xfdf: "<modify>".concat(ba, "</modify>"),
																}))
															: "delete" === h &&
																e.annots.push({ at: "delete", aId: aa.Id });
											0 < e.annots.length && x.connection.send(e);
											return [2];
									}
								});
							});
						}.bind(x);
						this.ga.addEventListener(ia.a.Events.ANNOTATION_CHANGED, this.Hp);
					};
					ca.Events = {
						WEBVIEWER_SERVER_ANNOTATIONS_ENABLED:
							"webViewerServerAnnotationsEnabled",
						WEBVIEWER_SERVER_ANNOTATIONS_DISABLED:
							"webViewerServerAnnotationsDisabled",
					};
					return ca;
				})();
				Object(ya.a)(r);
				va["default"] = r;
			},
		},
	]);
}).call(this || window);
