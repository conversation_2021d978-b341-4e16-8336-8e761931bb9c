(window.webpackJsonp = window.webpackJsonp || []).push([
	[66],
	{
		1975: function (t, e, o) {
			var n = o(30),
				r = o(1976);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[t.i, r, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const r = e[n];
						if (0 === n)
							r.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							r.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(r, a);
			t.exports = r.locals || {};
		},
		1976: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				'.open.HeaderFooterOptionsModal{visibility:visible}.closed.HeaderFooterOptionsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.HeaderFooterOptionsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.HeaderFooterOptionsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.HeaderFooterOptionsModal .footer .modal-button.cancel:hover,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.HeaderFooterOptionsModal .footer .modal-button.cancel,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal .footer .modal-button.cancel.disabled span,.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.HeaderFooterOptionsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.HeaderFooterOptionsModal .modal-container .wrapper .modal-content{padding:10px}.HeaderFooterOptionsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.HeaderFooterOptionsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.HeaderFooterOptionsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.HeaderFooterOptionsModal .footer .modal-button.confirm{margin-left:4px}.HeaderFooterOptionsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .footer .modal-button{padding:23px 8px}}.HeaderFooterOptionsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .HeaderFooterOptionsModal .swipe-indicator{width:32px}}.HeaderFooterOptionsModal{flex-direction:column}.HeaderFooterOptionsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.HeaderFooterOptionsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.HeaderFooterOptionsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input:after{content:"cm";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.HeaderFooterOptionsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.HeaderFooterOptionsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.HeaderFooterOptionsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.HeaderFooterOptionsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.HeaderFooterOptionsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}',
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2058: function (t, e, o) {
			"use strict";
			o.r(e);
			o(10),
				o(8),
				o(54),
				o(9),
				o(11),
				o(289),
				o(90),
				o(41),
				o(19),
				o(12),
				o(13),
				o(14),
				o(16),
				o(15),
				o(20),
				o(18),
				o(22),
				o(61),
				o(62),
				o(63),
				o(64),
				o(37),
				o(39),
				o(23),
				o(24),
				o(40),
				o(60);
			var n = o(0),
				r = o.n(n),
				a = o(6),
				i = o(347),
				d = o(4),
				l = o(2),
				c = o(5),
				s = o(42),
				p = o(351),
				u = o(2007),
				f = o(1355),
				h = o(1),
				m = o(26),
				y = o(1793),
				b = o(72);
			o(1975);
			function v(t) {
				return (v =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function g() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ g =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					o = e.hasOwnProperty,
					n =
						Object.defineProperty ||
						function (t, e, o) {
							t[e] = o.value;
						},
					r = "function" == typeof Symbol ? Symbol : {},
					a = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					d = r.toStringTag || "@@toStringTag";
				function l(t, e, o) {
					return (
						Object.defineProperty(t, e, {
							value: o,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					l({}, "");
				} catch (t) {
					l = function (t, e, o) {
						return (t[e] = o);
					};
				}
				function c(t, e, o, r) {
					var a = e && e.prototype instanceof u ? e : u,
						i = Object.create(a.prototype),
						d = new k(r || []);
					return n(i, "_invoke", { value: E(t, o, d) }), i;
				}
				function s(t, e, o) {
					try {
						return { type: "normal", arg: t.call(e, o) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = c;
				var p = {};
				function u() {}
				function f() {}
				function h() {}
				var m = {};
				l(m, a, function () {
					return this;
				});
				var y = Object.getPrototypeOf,
					b = y && y(y(_([])));
				b && b !== e && o.call(b, a) && (m = b);
				var x = (h.prototype = u.prototype = Object.create(m));
				function w(t) {
					["next", "throw", "return"].forEach(function (e) {
						l(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function O(t, e) {
					var r;
					n(this, "_invoke", {
						value: function (n, a) {
							function i() {
								return new e(function (r, i) {
									!(function n(r, a, i, d) {
										var l = s(t[r], t, a);
										if ("throw" !== l.type) {
											var c = l.arg,
												p = c.value;
											return p && "object" == v(p) && o.call(p, "__await")
												? e.resolve(p.__await).then(
														function (t) {
															n("next", t, i, d);
														},
														function (t) {
															n("throw", t, i, d);
														},
													)
												: e.resolve(p).then(
														function (t) {
															(c.value = t), i(c);
														},
														function (t) {
															return n("throw", t, i, d);
														},
													);
										}
										d(l.arg);
									})(n, a, r, i);
								});
							}
							return (r = r ? r.then(i, i) : i());
						},
					});
				}
				function E(t, e, o) {
					var n = "suspendedStart";
					return function (r, a) {
						if ("executing" === n)
							throw new Error("Generator is already running");
						if ("completed" === n) {
							if ("throw" === r) throw a;
							return j();
						}
						for (o.method = r, o.arg = a; ; ) {
							var i = o.delegate;
							if (i) {
								var d = F(i, o);
								if (d) {
									if (d === p) continue;
									return d;
								}
							}
							if ("next" === o.method) o.sent = o._sent = o.arg;
							else if ("throw" === o.method) {
								if ("suspendedStart" === n) throw ((n = "completed"), o.arg);
								o.dispatchException(o.arg);
							} else "return" === o.method && o.abrupt("return", o.arg);
							n = "executing";
							var l = s(t, e, o);
							if ("normal" === l.type) {
								if (
									((n = o.done ? "completed" : "suspendedYield"), l.arg === p)
								)
									continue;
								return { value: l.arg, done: o.done };
							}
							"throw" === l.type &&
								((n = "completed"), (o.method = "throw"), (o.arg = l.arg));
						}
					};
				}
				function F(t, e) {
					var o = e.method,
						n = t.iterator[o];
					if (void 0 === n)
						return (
							(e.delegate = null),
							("throw" === o &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								F(t, e),
								"throw" === e.method)) ||
								("return" !== o &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + o + "' method",
									)))),
							p
						);
					var r = s(n, t.iterator, e.arg);
					if ("throw" === r.type)
						return (
							(e.method = "throw"), (e.arg = r.arg), (e.delegate = null), p
						);
					var a = r.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								p)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							p);
				}
				function M(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function H(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function k(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(M, this),
						this.reset(!0);
				}
				function _(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var n = -1,
								r = function e() {
									for (; ++n < t.length; )
										if (o.call(t, n)) return (e.value = t[n]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (r.next = r);
						}
					}
					return { next: j };
				}
				function j() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = h),
					n(x, "constructor", { value: h, configurable: !0 }),
					n(h, "constructor", { value: f, configurable: !0 }),
					(f.displayName = l(h, d, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), l(t, d, "GeneratorFunction")),
							(t.prototype = Object.create(x)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					w(O.prototype),
					l(O.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = O),
					(t.async = function (e, o, n, r, a) {
						void 0 === a && (a = Promise);
						var i = new O(c(e, o, n, r), a);
						return t.isGeneratorFunction(o)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					w(x),
					l(x, d, "Generator"),
					l(x, a, function () {
						return this;
					}),
					l(x, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							o = [];
						for (var n in e) o.push(n);
						return (
							o.reverse(),
							function t() {
								for (; o.length; ) {
									var n = o.pop();
									if (n in e) return (t.value = n), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = _),
					(k.prototype = {
						constructor: k,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(H),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										o.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function n(o, n) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = o),
									n && ((e.method = "next"), (e.arg = void 0)),
									!!n
								);
							}
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var a = this.tryEntries[r],
									i = a.completion;
								if ("root" === a.tryLoc) return n("end");
								if (a.tryLoc <= this.prev) {
									var d = o.call(a, "catchLoc"),
										l = o.call(a, "finallyLoc");
									if (d && l) {
										if (this.prev < a.catchLoc) return n(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return n(a.finallyLoc);
									} else if (d) {
										if (this.prev < a.catchLoc) return n(a.catchLoc, !0);
									} else {
										if (!l)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return n(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var n = this.tryEntries.length - 1; n >= 0; --n) {
								var r = this.tryEntries[n];
								if (
									r.tryLoc <= this.prev &&
									o.call(r, "finallyLoc") &&
									this.prev < r.finallyLoc
								) {
									var a = r;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), p)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								p
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.finallyLoc === t)
									return this.complete(o.completion, o.afterLoc), H(o), p;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.tryLoc === t) {
									var n = o.completion;
									if ("throw" === n.type) {
										var r = n.arg;
										H(o);
									}
									return r;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, o) {
							return (
								(this.delegate = { iterator: _(t), resultName: e, nextLoc: o }),
								"next" === this.method && (this.arg = void 0),
								p
							);
						},
					}),
					t
				);
			}
			function x(t, e, o, n, r, a, i) {
				try {
					var d = t[a](i),
						l = d.value;
				} catch (t) {
					return void o(t);
				}
				d.done ? e(l) : Promise.resolve(l).then(n, r);
			}
			function w(t) {
				return function () {
					var e = this,
						o = arguments;
					return new Promise(function (n, r) {
						var a = t.apply(e, o);
						function i(t) {
							x(a, n, r, i, d, "next", t);
						}
						function d(t) {
							x(a, n, r, i, d, "throw", t);
						}
						i(void 0);
					});
				};
			}
			function O(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								r,
								a,
								i,
								d = [],
								l = !0,
								c = !1;
							try {
								if (((a = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									l = !1;
								} else
									for (
										;
										!(l = (n = a.call(o)).done) &&
										(d.push(n.value), d.length !== e);
										l = !0
									);
							} catch (t) {
								(c = !0), (r = t);
							} finally {
								try {
									if (
										!l &&
										null != o.return &&
										((i = o.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (c) throw r;
								}
							}
							return d;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return E(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return E(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function E(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var F = function () {
				var t = O(Object(i.a)(), 1)[0],
					e = Object(a.d)(),
					o = Object(a.e)(function (t) {
						return d.a.isElementOpen(t, c.a.HEADER_FOOTER_OPTIONS_MODAL);
					}),
					v = O(Object(n.useState)(""), 2),
					x = v[0],
					E = v[1],
					F = O(Object(n.useState)(""), 2),
					M = F[0],
					H = F[1],
					k = O(Object(n.useState)(""), 2),
					_ = k[0],
					j = k[1],
					L = O(Object(n.useState)(""), 2),
					A = L[0],
					T = L[1],
					S = O(Object(n.useState)(!1), 2),
					N = S[0],
					P = S[1],
					D = O(Object(n.useState)(!1), 2),
					C = D[0],
					I = D[1],
					R = O(Object(n.useState)(0), 2),
					z = R[0],
					B = R[1],
					G = O(Object(n.useState)(m.p.CM), 2),
					W = G[0],
					q = G[1],
					J = (function () {
						var t = w(
							g().mark(function t() {
								var o, n;
								return g().wrap(function (t) {
									for (;;)
										switch ((t.prev = t.next)) {
											case 0:
												return (
													e(l.a.closeElement(c.a.HEADER_FOOTER_OPTIONS_MODAL)),
													(o = y.a.getPageNumber()),
													(n = x !== _ || M !== A),
													t.abrupt(
														"return",
														Promise.all([
															h.a.getOfficeEditor().setDifferentFirstPage(o, N),
															h.a.getOfficeEditor().setOddEven(C),
															n &&
																h.a
																	.getOfficeEditor()
																	.setHeaderFooterMargins(
																		o,
																		{
																			headerDistanceToTop: _,
																			footerDistanceToBottom: A,
																		},
																		W,
																	),
														]),
													)
												);
											case 4:
											case "end":
												return t.stop();
										}
								}, t);
							}),
						);
						return function () {
							return t.apply(this, arguments);
						};
					})();
				Object(n.useEffect)(
					w(
						g().mark(function t() {
							var e, n, r, a, i, d, l;
							return g().wrap(function (t) {
								for (;;)
									switch ((t.prev = t.next)) {
										case 0:
											if (!o) {
												t.next = 28;
												break;
											}
											return (
												q(m.p.CM),
												(e = y.a.getPageNumber()),
												(t.next = 5),
												h.a.getOfficeEditor().getHeaderFooterMargins(e, W)
											);
										case 5:
											return (
												(n = t.sent),
												(r = n.headerDistanceToTop),
												(a = n.footerDistanceToBottom),
												(i = r.toFixed(2)),
												(d = a.toFixed(2)),
												j(i),
												T(d),
												E(i),
												H(d),
												(t.t0 = P),
												(t.next = 17),
												h.a.getOfficeEditor().getDifferentFirstPage(e)
											);
										case 17:
											return (
												(t.t1 = t.sent),
												(0, t.t0)(t.t1),
												(t.t2 = I),
												(t.next = 22),
												h.a.getOfficeEditor().getOddEven()
											);
										case 22:
											return (
												(t.t3 = t.sent),
												(0, t.t2)(t.t3),
												(t.next = 26),
												h.a.getOfficeEditor().getMaxHeaderFooterDistance(e)
											);
										case 26:
											(l = t.sent), B(l);
										case 28:
										case "end":
											return t.stop();
									}
							}, t);
						}),
					),
					[o],
				);
				var Y = function () {
						e(l.a.closeElement(c.a.HEADER_FOOTER_OPTIONS_MODAL)),
							setTimeout(function () {
								h.a.getOfficeEditor().focusContent();
							}, 0);
					},
					U = [
						{
							id: "headerToTopInput",
							value: _,
							label: t("officeEditor.headerFooterOptionsModal.headerFromTop"),
							onChange: function (t) {
								var e = Object(b.k)(t.target.value, z, W);
								j(e);
							},
							onBlur: function (t) {
								"" === t.target.value && j("0");
							},
						},
						{
							id: "footerToBottomInput",
							value: A,
							label: t(
								"officeEditor.headerFooterOptionsModal.footerFromBottom",
							),
							onChange: function (t) {
								var e = Object(b.k)(t.target.value, z, W);
								T(e);
							},
							onBlur: function (t) {
								"" === t.target.value && T("0");
							},
						},
					];
				return (
					o &&
					r.a.createElement(
						"div",
						{
							className: "HeaderFooterOptionsModal",
							"data-element": c.a.HEADER_FOOTER_OPTIONS_MODAL,
						},
						r.a.createElement(
							p.a,
							{
								isOpen: o,
								title: t("officeEditor.headerFooterOptionsModal.title"),
								closehandler: Y,
								onCloseClick: Y,
								swipeToClose: !0,
							},
							r.a.createElement(
								"div",
								{ className: "modal-body" },
								r.a.createElement(
									"div",
									{ className: "title" },
									t("officeEditor.margins"),
								),
								U.map(function (t) {
									return r.a.createElement(
										"div",
										{ key: t.id, className: "input-container" },
										r.a.createElement(
											"label",
											{ htmlFor: t.id, className: "label" },
											t.label,
										),
										r.a.createElement(u.a, {
											type: "number",
											id: t.id,
											onChange: t.onChange,
											onBlur: t.onBlur,
											value: t.value,
											min: "0",
											step: "any",
										}),
									);
								}),
								r.a.createElement(
									"div",
									{ className: "title" },
									t("officeEditor.headerFooterOptionsModal.layouts.layout"),
								),
								r.a.createElement(f.a, {
									id: "different-first-page",
									label: t(
										"officeEditor.headerFooterOptionsModal.layouts.differentFirstPage",
									),
									"aria-label": t(
										"officeEditor.headerFooterOptionsModal.layouts.differentFirstPage",
									),
									checked: N,
									"aria-checked": N,
									onChange: function (t) {
										return P(t.target.checked);
									},
								}),
								r.a.createElement(f.a, {
									id: "different-odd-even",
									label: t(
										"officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages",
									),
									"aria-label": t(
										"officeEditor.headerFooterOptionsModal.layouts.differentEvenOddPages",
									),
									checked: C,
									"aria-checked": C,
									onChange: function (t) {
										return I(t.target.checked);
									},
								}),
							),
							r.a.createElement(
								"div",
								{ className: "footer" },
								r.a.createElement(s.a, { onClick: J, label: t("action.save") }),
							),
						),
					)
				);
			};
			e.default = F;
		},
	},
]);
//# sourceMappingURL=chunk.66.js.map
