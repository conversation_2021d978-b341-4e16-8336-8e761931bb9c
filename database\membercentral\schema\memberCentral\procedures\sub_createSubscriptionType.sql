ALTER PROC dbo.sub_createSubscriptionType 
@orgID int, 
@siteid int,
@typeName varchar(100),
@feRawContent varchar(max),
@feCompletedRawContent varchar(max),
@feEmailNotification varchar(max),
@isImport bit = 0,
@recordedByMemberID int,
@subTypeID INT OUTPUT

AS 

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @subAdminResourceID INT, @appCreatedContentResourceTypeID INT, @languageID INT, 
		@contentID INT, @contentSRID INT,  @completedContentID INT, @completedContentSRID INT, @subTypeResourceTypeID INT, 
		@subTypeSRID INT, @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);

	select @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
	from dbo.sites
	where siteID = @siteID;

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @subTypeResourceTypeID = dbo.fn_getResourceTypeId('subscriptionType');

	BEGIN TRAN;
		EXEC dbo.cms_createSiteResource @resourceTypeID=@subTypeResourceTypeID, @siteResourceStatusID=1,  
			@siteID=@siteID, @isVisible=1, @parentSiteResourceID=NULL, @siteResourceID=@subTypeSRID OUTPUT;

		INSERT INTO dbo.sub_Types (typeName, siteID, feEmailNotification, siteResourceID)
		VALUES(@typeName, @siteid, @feEmailNotification, @subTypeSRID);
			SELECT @subTypeID = SCOPE_IDENTITY();

		EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feCompletedRawContent, @memberID=null, @contentID=@completedContentID OUTPUT, @siteResourceID=@completedContentSRID OUTPUT;

		update dbo.sub_Types 
		set frontEndContentID = @contentID, 
			frontEndCompletedContentID = @completedContentID
		where typeID = @subTypeID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "TYPEID":'+CAST(@subTypeID AS varchar(10))+' }';
		SET @msgjson = 'New Subscription Type [' + @typeName + '] has been created.'
			+ CASE WHEN NULLIF(@feEmailNotification,'') IS NOT NULL THEN @crlf + 'Renewal Notification Email(s): ' + @feEmailNotification ELSE '' END;
		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBTYPE', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
