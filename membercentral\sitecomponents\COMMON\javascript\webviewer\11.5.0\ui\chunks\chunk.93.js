(window.webpackJsonp = window.webpackJsonp || []).push([
	[93],
	{
		2060: function (e, t, a) {
			"use strict";
			a.r(t);
			a(58), a(44);
			var n = a(0),
				r = a.n(n),
				o = a(82),
				s = a(3),
				i = a.n(s),
				l = a(68),
				c = a(70),
				u = a(6),
				f = a(4),
				p = a(1),
				y = a(100);
			var d = function (e) {
				var t = y.f[e];
				t &&
					p.a
						.getDocumentViewer()
						.getSpreadsheetEditorManager()
						.setSelectedCellRangeStyle({ formatString: t });
			};
			function m() {
				return (m = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var a = arguments[t];
								for (var n in a)
									Object.prototype.hasOwnProperty.call(a, n) && (e[n] = a[n]);
							}
							return e;
						}).apply(this, arguments);
			}
			var b = {
					formatType: i.a.string,
					isFlyoutItem: i.a.bool,
					secondaryLabel: i.a.string,
					style: i.a.object,
					className: i.a.string,
					selector: i.a.func,
				},
				g = Object(n.forwardRef)(function (e, t) {
					var a = e.isFlyoutItem,
						n = e.formatType,
						s = e.secondaryLabel,
						i = e.style,
						p = e.className,
						y = Object(u.e)(function (e) {
							return f.a.getActiveCellFormatType(e);
						}),
						b = n === y,
						g = c.b[n],
						v = g.dataElement,
						w = g.icon,
						j = g.title,
						C = function () {
							d(n);
						};
					return a
						? r.a.createElement(
								l.a,
								m({}, e, {
									ref: t,
									onClick: C,
									secondaryLabel: s,
									additionalClass: b ? "active" : "",
								}),
							)
						: r.a.createElement(o.a, {
								key: n,
								isActive: b,
								onClick: C,
								dataElement: v,
								title: j,
								img: w,
								ariaPressed: b,
								style: i,
								className: p,
							});
				});
			(g.propTypes = b), (g.displayName = "CellFormatButton");
			t.default = g;
		},
	},
]);
//# sourceMappingURL=chunk.93.js.map
