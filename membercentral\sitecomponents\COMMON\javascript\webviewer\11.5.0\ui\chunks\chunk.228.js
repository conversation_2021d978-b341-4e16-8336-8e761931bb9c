(window.webpackJsonp = window.webpackJsonp || []).push([
	[228],
	{
		1486: function (e, a, n) {
			e.exports = (function (e) {
				"use strict";
				var a = (function (e) {
						return e && "object" == typeof e && "default" in e
							? e
							: { default: e };
					})(e),
					n = {
						name: "tl-ph",
						weekdays:
							"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split(
								"_",
							),
						months:
							"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split(
								"_",
							),
						weekStart: 1,
						weekdaysShort: "Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),
						monthsShort:
							"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),
						weekdaysMin: "<PERSON>_<PERSON>_Ma_Mi_Hu_Bi_Sab".split("_"),
						ordinal: function (e) {
							return e;
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "MM/D/YYYY",
							LL: "MMMM D, YYYY",
							LLL: "MMMM D, YYYY HH:mm",
							LLLL: "dddd, MMMM DD, YYYY HH:mm",
						},
						relativeTime: {
							future: "sa loob ng %s",
							past: "%s ang nakalipas",
							s: "ilang segundo",
							m: "isang minuto",
							mm: "%d minuto",
							h: "isang oras",
							hh: "%d oras",
							d: "isang araw",
							dd: "%d araw",
							M: "isang buwan",
							MM: "%d buwan",
							y: "isang taon",
							yy: "%d taon",
						},
					};
				return a.default.locale(n, null, !0), n;
			})(n(105));
		},
	},
]);
//# sourceMappingURL=chunk.228.js.map
