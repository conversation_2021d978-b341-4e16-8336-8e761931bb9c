(function () {
	/*
 @license DOMPurify 3.2.5 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE  The buffer module from node.js, for the browser.

 <AUTHOR> <http://feross.org>
 @license  MIT
*/
	var $jscomp = $jscomp || {};
	$jscomp.scope = {};
	$jscomp.arrayIteratorImpl = function (t) {
		var u = 0;
		return function () {
			return u < t.length ? { done: !1, value: t[u++] } : { done: !0 };
		};
	};
	$jscomp.arrayIterator = function (t) {
		return { next: $jscomp.arrayIteratorImpl(t) };
	};
	$jscomp.ASSUME_ES5 = !1;
	$jscomp.ASSUME_NO_NATIVE_MAP = !1;
	$jscomp.ASSUME_NO_NATIVE_SET = !1;
	$jscomp.SIMPLE_FROUND_POLYFILL = !1;
	$jscomp.ISOLATE_POLYFILLS = !1;
	$jscomp.FORCE_POLYFILL_PROMISE = !1;
	$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION = !1;
	$jscomp.defineProperty =
		$jscomp.ASSUME_ES5 || "function" == typeof Object.defineProperties
			? Object.defineProperty
			: function (t, u, n) {
					if (t == Array.prototype || t == Object.prototype) return t;
					t[u] = n.value;
					return t;
				};
	$jscomp.getGlobal = function (t) {
		t = [
			"object" == typeof globalThis && globalThis,
			t,
			"object" == typeof window && window,
			"object" == typeof self && self,
			"object" == typeof global && global,
		];
		for (var u = 0; u < t.length; ++u) {
			var n = t[u];
			if (n && n.Math == Math) return n;
		}
		throw Error("Cannot find global object");
	};
	$jscomp.global = $jscomp.getGlobal(this);
	$jscomp.IS_SYMBOL_NATIVE =
		"function" === typeof Symbol && "symbol" === typeof Symbol("x");
	$jscomp.TRUST_ES6_POLYFILLS =
		!$jscomp.ISOLATE_POLYFILLS || $jscomp.IS_SYMBOL_NATIVE;
	$jscomp.polyfills = {};
	$jscomp.propertyToPolyfillSymbol = {};
	$jscomp.POLYFILL_PREFIX = "$jscp$";
	var $jscomp$lookupPolyfilledValue = function (t, u, n) {
		if (!n || null != t) {
			n = $jscomp.propertyToPolyfillSymbol[u];
			if (null == n) return t[u];
			n = t[n];
			return void 0 !== n ? n : t[u];
		}
	};
	$jscomp.polyfill = function (t, u, n, k) {
		u &&
			($jscomp.ISOLATE_POLYFILLS
				? $jscomp.polyfillIsolated(t, u, n, k)
				: $jscomp.polyfillUnisolated(t, u, n, k));
	};
	$jscomp.polyfillUnisolated = function (t, u, n, k) {
		n = $jscomp.global;
		t = t.split(".");
		for (k = 0; k < t.length - 1; k++) {
			var g = t[k];
			if (!(g in n)) return;
			n = n[g];
		}
		t = t[t.length - 1];
		k = n[t];
		u = u(k);
		u != k &&
			null != u &&
			$jscomp.defineProperty(n, t, {
				configurable: !0,
				writable: !0,
				value: u,
			});
	};
	$jscomp.polyfillIsolated = function (t, u, n, k) {
		var g = t.split(".");
		t = 1 === g.length;
		k = g[0];
		k = !t && k in $jscomp.polyfills ? $jscomp.polyfills : $jscomp.global;
		for (var r = 0; r < g.length - 1; r++) {
			var d = g[r];
			if (!(d in k)) return;
			k = k[d];
		}
		g = g[g.length - 1];
		n = $jscomp.IS_SYMBOL_NATIVE && "es6" === n ? k[g] : null;
		u = u(n);
		null != u &&
			(t
				? $jscomp.defineProperty($jscomp.polyfills, g, {
						configurable: !0,
						writable: !0,
						value: u,
					})
				: u !== n &&
					(void 0 === $jscomp.propertyToPolyfillSymbol[g] &&
						((n = (1e9 * Math.random()) >>> 0),
						($jscomp.propertyToPolyfillSymbol[g] = $jscomp.IS_SYMBOL_NATIVE
							? $jscomp.global.Symbol(g)
							: $jscomp.POLYFILL_PREFIX + n + "$" + g)),
					$jscomp.defineProperty(k, $jscomp.propertyToPolyfillSymbol[g], {
						configurable: !0,
						writable: !0,
						value: u,
					})));
	};
	$jscomp.initSymbol = function () {};
	$jscomp.polyfill(
		"Symbol",
		function (t) {
			if (t) return t;
			var u = function (r, d) {
				this.$jscomp$symbol$id_ = r;
				$jscomp.defineProperty(this, "description", {
					configurable: !0,
					writable: !0,
					value: d,
				});
			};
			u.prototype.toString = function () {
				return this.$jscomp$symbol$id_;
			};
			var n = "jscomp_symbol_" + ((1e9 * Math.random()) >>> 0) + "_",
				k = 0,
				g = function (r) {
					if (this instanceof g)
						throw new TypeError("Symbol is not a constructor");
					return new u(n + (r || "") + "_" + k++, r);
				};
			return g;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Symbol.iterator",
		function (t) {
			if (t) return t;
			t = Symbol("Symbol.iterator");
			for (
				var u =
						"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(
							" ",
						),
					n = 0;
				n < u.length;
				n++
			) {
				var k = $jscomp.global[u[n]];
				"function" === typeof k &&
					"function" != typeof k.prototype[t] &&
					$jscomp.defineProperty(k.prototype, t, {
						configurable: !0,
						writable: !0,
						value: function () {
							return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this));
						},
					});
			}
			return t;
		},
		"es6",
		"es3",
	);
	$jscomp.iteratorPrototype = function (t) {
		t = { next: t };
		t[Symbol.iterator] = function () {
			return this;
		};
		return t;
	};
	$jscomp.makeIterator = function (t) {
		var u =
			"undefined" != typeof Symbol && Symbol.iterator && t[Symbol.iterator];
		if (u) return u.call(t);
		if ("number" == typeof t.length) return $jscomp.arrayIterator(t);
		throw Error(String(t) + " is not an iterable or ArrayLike");
	};
	$jscomp.arrayFromIterator = function (t) {
		for (var u, n = []; !(u = t.next()).done; ) n.push(u.value);
		return n;
	};
	$jscomp.arrayFromIterable = function (t) {
		return t instanceof Array
			? t
			: $jscomp.arrayFromIterator($jscomp.makeIterator(t));
	};
	$jscomp.polyfill(
		"Promise",
		function (t) {
			function u() {
				this.batch_ = null;
			}
			function n(d) {
				return d instanceof g
					? d
					: new g(function (q, h) {
							q(d);
						});
			}
			if (
				t &&
				(!(
					$jscomp.FORCE_POLYFILL_PROMISE ||
					($jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION &&
						"undefined" === typeof $jscomp.global.PromiseRejectionEvent)
				) ||
					!$jscomp.global.Promise ||
					-1 === $jscomp.global.Promise.toString().indexOf("[native code]"))
			)
				return t;
			u.prototype.asyncExecute = function (d) {
				if (null == this.batch_) {
					this.batch_ = [];
					var q = this;
					this.asyncExecuteFunction(function () {
						q.executeBatch_();
					});
				}
				this.batch_.push(d);
			};
			var k = $jscomp.global.setTimeout;
			u.prototype.asyncExecuteFunction = function (d) {
				k(d, 0);
			};
			u.prototype.executeBatch_ = function () {
				for (; this.batch_ && this.batch_.length; ) {
					var d = this.batch_;
					this.batch_ = [];
					for (var q = 0; q < d.length; ++q) {
						var h = d[q];
						d[q] = null;
						try {
							h();
						} catch (m) {
							this.asyncThrow_(m);
						}
					}
				}
				this.batch_ = null;
			};
			u.prototype.asyncThrow_ = function (d) {
				this.asyncExecuteFunction(function () {
					throw d;
				});
			};
			var g = function (d) {
				this.state_ = 0;
				this.result_ = void 0;
				this.onSettledCallbacks_ = [];
				this.isRejectionHandled_ = !1;
				var q = this.createResolveAndReject_();
				try {
					d(q.resolve, q.reject);
				} catch (h) {
					q.reject(h);
				}
			};
			g.prototype.createResolveAndReject_ = function () {
				function d(m) {
					return function (p) {
						h || ((h = !0), m.call(q, p));
					};
				}
				var q = this,
					h = !1;
				return { resolve: d(this.resolveTo_), reject: d(this.reject_) };
			};
			g.prototype.resolveTo_ = function (d) {
				if (d === this)
					this.reject_(new TypeError("A Promise cannot resolve to itself"));
				else if (d instanceof g) this.settleSameAsPromise_(d);
				else {
					a: switch (typeof d) {
						case "object":
							var q = null != d;
							break a;
						case "function":
							q = !0;
							break a;
						default:
							q = !1;
					}
					q ? this.resolveToNonPromiseObj_(d) : this.fulfill_(d);
				}
			};
			g.prototype.resolveToNonPromiseObj_ = function (d) {
				var q = void 0;
				try {
					q = d.then;
				} catch (h) {
					this.reject_(h);
					return;
				}
				"function" == typeof q
					? this.settleSameAsThenable_(q, d)
					: this.fulfill_(d);
			};
			g.prototype.reject_ = function (d) {
				this.settle_(2, d);
			};
			g.prototype.fulfill_ = function (d) {
				this.settle_(1, d);
			};
			g.prototype.settle_ = function (d, q) {
				if (0 != this.state_)
					throw Error(
						"Cannot settle(" +
							d +
							", " +
							q +
							"): Promise already settled in state" +
							this.state_,
					);
				this.state_ = d;
				this.result_ = q;
				2 === this.state_ && this.scheduleUnhandledRejectionCheck_();
				this.executeOnSettledCallbacks_();
			};
			g.prototype.scheduleUnhandledRejectionCheck_ = function () {
				var d = this;
				k(function () {
					if (d.notifyUnhandledRejection_()) {
						var q = $jscomp.global.console;
						"undefined" !== typeof q && q.error(d.result_);
					}
				}, 1);
			};
			g.prototype.notifyUnhandledRejection_ = function () {
				if (this.isRejectionHandled_) return !1;
				var d = $jscomp.global.CustomEvent,
					q = $jscomp.global.Event,
					h = $jscomp.global.dispatchEvent;
				if ("undefined" === typeof h) return !0;
				"function" === typeof d
					? (d = new d("unhandledrejection", { cancelable: !0 }))
					: "function" === typeof q
						? (d = new q("unhandledrejection", { cancelable: !0 }))
						: ((d = $jscomp.global.document.createEvent("CustomEvent")),
							d.initCustomEvent("unhandledrejection", !1, !0, d));
				d.promise = this;
				d.reason = this.result_;
				return h(d);
			};
			g.prototype.executeOnSettledCallbacks_ = function () {
				if (null != this.onSettledCallbacks_) {
					for (var d = 0; d < this.onSettledCallbacks_.length; ++d)
						r.asyncExecute(this.onSettledCallbacks_[d]);
					this.onSettledCallbacks_ = null;
				}
			};
			var r = new u();
			g.prototype.settleSameAsPromise_ = function (d) {
				var q = this.createResolveAndReject_();
				d.callWhenSettled_(q.resolve, q.reject);
			};
			g.prototype.settleSameAsThenable_ = function (d, q) {
				var h = this.createResolveAndReject_();
				try {
					d.call(q, h.resolve, h.reject);
				} catch (m) {
					h.reject(m);
				}
			};
			g.prototype.then = function (d, q) {
				function h(B, A) {
					return "function" == typeof B
						? function (L) {
								try {
									m(B(L));
								} catch (y) {
									p(y);
								}
							}
						: A;
				}
				var m,
					p,
					x = new g(function (B, A) {
						m = B;
						p = A;
					});
				this.callWhenSettled_(h(d, m), h(q, p));
				return x;
			};
			g.prototype.catch = function (d) {
				return this.then(void 0, d);
			};
			g.prototype.callWhenSettled_ = function (d, q) {
				function h() {
					switch (m.state_) {
						case 1:
							d(m.result_);
							break;
						case 2:
							q(m.result_);
							break;
						default:
							throw Error("Unexpected state: " + m.state_);
					}
				}
				var m = this;
				null == this.onSettledCallbacks_
					? r.asyncExecute(h)
					: this.onSettledCallbacks_.push(h);
				this.isRejectionHandled_ = !0;
			};
			g.resolve = n;
			g.reject = function (d) {
				return new g(function (q, h) {
					h(d);
				});
			};
			g.race = function (d) {
				return new g(function (q, h) {
					for (
						var m = $jscomp.makeIterator(d), p = m.next();
						!p.done;
						p = m.next()
					)
						n(p.value).callWhenSettled_(q, h);
				});
			};
			g.all = function (d) {
				var q = $jscomp.makeIterator(d),
					h = q.next();
				return h.done
					? n([])
					: new g(function (m, p) {
							function x(L) {
								return function (y) {
									B[L] = y;
									A--;
									0 == A && m(B);
								};
							}
							var B = [],
								A = 0;
							do
								B.push(void 0),
									A++,
									n(h.value).callWhenSettled_(x(B.length - 1), p),
									(h = q.next());
							while (!h.done);
						});
			};
			return g;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Array.from",
		function (t) {
			return t
				? t
				: function (u, n, k) {
						n =
							null != n
								? n
								: function (q) {
										return q;
									};
						var g = [],
							r =
								"undefined" != typeof Symbol &&
								Symbol.iterator &&
								u[Symbol.iterator];
						if ("function" == typeof r) {
							u = r.call(u);
							for (var d = 0; !(r = u.next()).done; )
								g.push(n.call(k, r.value, d++));
						} else
							for (r = u.length, d = 0; d < r; d++) g.push(n.call(k, u[d], d));
						return g;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.checkStringArgs = function (t, u, n) {
		if (null == t)
			throw new TypeError(
				"The 'this' value for String.prototype." +
					n +
					" must not be null or undefined",
			);
		if (u instanceof RegExp)
			throw new TypeError(
				"First argument to String.prototype." +
					n +
					" must not be a regular expression",
			);
		return t + "";
	};
	$jscomp.polyfill(
		"String.prototype.endsWith",
		function (t) {
			return t
				? t
				: function (u, n) {
						var k = $jscomp.checkStringArgs(this, u, "endsWith");
						u += "";
						void 0 === n && (n = k.length);
						n = Math.max(0, Math.min(n | 0, k.length));
						for (var g = u.length; 0 < g && 0 < n; )
							if (k[--n] != u[--g]) return !1;
						return 0 >= g;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.checkEs6ConformanceViaProxy = function () {
		try {
			var t = {},
				u = Object.create(
					new $jscomp.global.Proxy(t, {
						get: function (n, k, g) {
							return n == t && "q" == k && g == u;
						},
					}),
				);
			return !0 === u.q;
		} catch (n) {
			return !1;
		}
	};
	$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS = !1;
	$jscomp.ES6_CONFORMANCE =
		$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS &&
		$jscomp.checkEs6ConformanceViaProxy();
	$jscomp.owns = function (t, u) {
		return Object.prototype.hasOwnProperty.call(t, u);
	};
	$jscomp.polyfill(
		"WeakMap",
		function (t) {
			function u() {
				if (!t || !Object.seal) return !1;
				try {
					var m = Object.seal({}),
						p = Object.seal({}),
						x = new t([
							[m, 2],
							[p, 3],
						]);
					if (2 != x.get(m) || 3 != x.get(p)) return !1;
					x.delete(m);
					x.set(p, 4);
					return !x.has(m) && 4 == x.get(p);
				} catch (B) {
					return !1;
				}
			}
			function n() {}
			function k(m) {
				var p = typeof m;
				return ("object" === p && null !== m) || "function" === p;
			}
			function g(m) {
				if (!$jscomp.owns(m, d)) {
					var p = new n();
					$jscomp.defineProperty(m, d, { value: p });
				}
			}
			function r(m) {
				if (!$jscomp.ISOLATE_POLYFILLS) {
					var p = Object[m];
					p &&
						(Object[m] = function (x) {
							if (x instanceof n) return x;
							Object.isExtensible(x) && g(x);
							return p(x);
						});
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (t && $jscomp.ES6_CONFORMANCE) return t;
			} else if (u()) return t;
			var d = "$jscomp_hidden_" + Math.random();
			r("freeze");
			r("preventExtensions");
			r("seal");
			var q = 0,
				h = function (m) {
					this.id_ = (q += Math.random() + 1).toString();
					if (m) {
						m = $jscomp.makeIterator(m);
						for (var p; !(p = m.next()).done; )
							(p = p.value), this.set(p[0], p[1]);
					}
				};
			h.prototype.set = function (m, p) {
				if (!k(m)) throw Error("Invalid WeakMap key");
				g(m);
				if (!$jscomp.owns(m, d)) throw Error("WeakMap key fail: " + m);
				m[d][this.id_] = p;
				return this;
			};
			h.prototype.get = function (m) {
				return k(m) && $jscomp.owns(m, d) ? m[d][this.id_] : void 0;
			};
			h.prototype.has = function (m) {
				return k(m) && $jscomp.owns(m, d) && $jscomp.owns(m[d], this.id_);
			};
			h.prototype.delete = function (m) {
				return k(m) && $jscomp.owns(m, d) && $jscomp.owns(m[d], this.id_)
					? delete m[d][this.id_]
					: !1;
			};
			return h;
		},
		"es6",
		"es3",
	);
	$jscomp.MapEntry = function () {};
	$jscomp.polyfill(
		"Map",
		function (t) {
			function u() {
				if (
					$jscomp.ASSUME_NO_NATIVE_MAP ||
					!t ||
					"function" != typeof t ||
					!t.prototype.entries ||
					"function" != typeof Object.seal
				)
					return !1;
				try {
					var h = Object.seal({ x: 4 }),
						m = new t($jscomp.makeIterator([[h, "s"]]));
					if (
						"s" != m.get(h) ||
						1 != m.size ||
						m.get({ x: 4 }) ||
						m.set({ x: 4 }, "t") != m ||
						2 != m.size
					)
						return !1;
					var p = m.entries(),
						x = p.next();
					if (x.done || x.value[0] != h || "s" != x.value[1]) return !1;
					x = p.next();
					return x.done ||
						4 != x.value[0].x ||
						"t" != x.value[1] ||
						!p.next().done
						? !1
						: !0;
				} catch (B) {
					return !1;
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (t && $jscomp.ES6_CONFORMANCE) return t;
			} else if (u()) return t;
			var n = new WeakMap(),
				k = function (h) {
					this.data_ = {};
					this.head_ = d();
					this.size = 0;
					if (h) {
						h = $jscomp.makeIterator(h);
						for (var m; !(m = h.next()).done; )
							(m = m.value), this.set(m[0], m[1]);
					}
				};
			k.prototype.set = function (h, m) {
				h = 0 === h ? 0 : h;
				var p = g(this, h);
				p.list || (p.list = this.data_[p.id] = []);
				p.entry
					? (p.entry.value = m)
					: ((p.entry = {
							next: this.head_,
							previous: this.head_.previous,
							head: this.head_,
							key: h,
							value: m,
						}),
						p.list.push(p.entry),
						(this.head_.previous.next = p.entry),
						(this.head_.previous = p.entry),
						this.size++);
				return this;
			};
			k.prototype.delete = function (h) {
				h = g(this, h);
				return h.entry && h.list
					? (h.list.splice(h.index, 1),
						h.list.length || delete this.data_[h.id],
						(h.entry.previous.next = h.entry.next),
						(h.entry.next.previous = h.entry.previous),
						(h.entry.head = null),
						this.size--,
						!0)
					: !1;
			};
			k.prototype.clear = function () {
				this.data_ = {};
				this.head_ = this.head_.previous = d();
				this.size = 0;
			};
			k.prototype.has = function (h) {
				return !!g(this, h).entry;
			};
			k.prototype.get = function (h) {
				return (h = g(this, h).entry) && h.value;
			};
			k.prototype.entries = function () {
				return r(this, function (h) {
					return [h.key, h.value];
				});
			};
			k.prototype.keys = function () {
				return r(this, function (h) {
					return h.key;
				});
			};
			k.prototype.values = function () {
				return r(this, function (h) {
					return h.value;
				});
			};
			k.prototype.forEach = function (h, m) {
				for (var p = this.entries(), x; !(x = p.next()).done; )
					(x = x.value), h.call(m, x[1], x[0], this);
			};
			k.prototype[Symbol.iterator] = k.prototype.entries;
			var g = function (h, m) {
					var p = m && typeof m;
					"object" == p || "function" == p
						? n.has(m)
							? (p = n.get(m))
							: ((p = "" + ++q), n.set(m, p))
						: (p = "p_" + m);
					var x = h.data_[p];
					if (x && $jscomp.owns(h.data_, p))
						for (h = 0; h < x.length; h++) {
							var B = x[h];
							if ((m !== m && B.key !== B.key) || m === B.key)
								return { id: p, list: x, index: h, entry: B };
						}
					return { id: p, list: x, index: -1, entry: void 0 };
				},
				r = function (h, m) {
					var p = h.head_;
					return $jscomp.iteratorPrototype(function () {
						if (p) {
							for (; p.head != h.head_; ) p = p.previous;
							for (; p.next != p.head; )
								return (p = p.next), { done: !1, value: m(p) };
							p = null;
						}
						return { done: !0, value: void 0 };
					});
				},
				d = function () {
					var h = {};
					return (h.previous = h.next = h.head = h);
				},
				q = 0;
			return k;
		},
		"es6",
		"es3",
	);
	$jscomp.underscoreProtoCanBeSet = function () {
		var t = { a: !0 },
			u = {};
		try {
			return (u.__proto__ = t), u.a;
		} catch (n) {}
		return !1;
	};
	$jscomp.setPrototypeOf =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.setPrototypeOf
			? Object.setPrototypeOf
			: $jscomp.underscoreProtoCanBeSet()
				? function (t, u) {
						t.__proto__ = u;
						if (t.__proto__ !== u)
							throw new TypeError(t + " is not extensible");
						return t;
					}
				: null;
	$jscomp.polyfill(
		"Object.setPrototypeOf",
		function (t) {
			return t || $jscomp.setPrototypeOf;
		},
		"es6",
		"es5",
	);
	$jscomp.assign =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.assign
			? Object.assign
			: function (t, u) {
					for (var n = 1; n < arguments.length; n++) {
						var k = arguments[n];
						if (k) for (var g in k) $jscomp.owns(k, g) && (t[g] = k[g]);
					}
					return t;
				};
	$jscomp.polyfill(
		"Object.assign",
		function (t) {
			return t || $jscomp.assign;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Array.prototype.fill",
		function (t) {
			return t
				? t
				: function (u, n, k) {
						var g = this.length || 0;
						0 > n && (n = Math.max(0, g + n));
						if (null == k || k > g) k = g;
						k = Number(k);
						0 > k && (k = Math.max(0, g + k));
						for (n = Number(n || 0); n < k; n++) this[n] = u;
						return this;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.typedArrayFill = function (t) {
		return t ? t : Array.prototype.fill;
	};
	$jscomp.polyfill(
		"Int8Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Uint8Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Uint8ClampedArray.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Int16Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Uint16Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Int32Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Uint32Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Float32Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	$jscomp.polyfill(
		"Float64Array.prototype.fill",
		$jscomp.typedArrayFill,
		"es6",
		"es5",
	);
	(function (t) {
		function u(k) {
			if (n[k]) return n[k].exports;
			var g = (n[k] = { i: k, l: !1, exports: {} });
			t[k].call(g.exports, g, g.exports, u);
			g.l = !0;
			return g.exports;
		}
		var n = {};
		u.m = t;
		u.c = n;
		u.d = function (k, g, r) {
			u.o(k, g) || Object.defineProperty(k, g, { enumerable: !0, get: r });
		};
		u.r = function (k) {
			"undefined" !== typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(k, Symbol.toStringTag, { value: "Module" });
			Object.defineProperty(k, "__esModule", { value: !0 });
		};
		u.t = function (k, g) {
			g & 1 && (k = u(k));
			if (g & 8 || (g & 4 && "object" === typeof k && k && k.__esModule))
				return k;
			var r = Object.create(null);
			u.r(r);
			Object.defineProperty(r, "default", { enumerable: !0, value: k });
			if (g & 2 && "string" != typeof k)
				for (var d in k)
					u.d(
						r,
						d,
						function (q) {
							return k[q];
						}.bind(null, d),
					);
			return r;
		};
		u.n = function (k) {
			var g =
				k && k.__esModule
					? function () {
							return k["default"];
						}
					: function () {
							return k;
						};
			u.d(g, "a", g);
			return g;
		};
		u.o = function (k, g) {
			return Object.prototype.hasOwnProperty.call(k, g);
		};
		u.p = "/core/contentEdit";
		return u((u.s = 11));
	})([
		function (t, u, n) {
			n.d(u, "b", function () {
				return g;
			});
			n.d(u, "a", function () {
				return r;
			});
			var k = n(2),
				g = function (d, q) {
					Object(k.a)("disableLogs") ||
						(q ? console.warn("".concat(d, ": ").concat(q)) : console.warn(d));
				},
				r = function (d, q) {};
		},
		function (t, u, n) {
			n.d(u, "a", function () {
				return la;
			});
			n.d(u, "b", function () {
				return ha;
			});
			var k = n(3),
				g = n(0),
				r = n(6),
				d = n(4),
				q = n(5),
				h = "undefined" === typeof window ? self : window,
				m = h.importScripts,
				p = !1,
				x = function (w, e) {
					p ||
						(m(Object(q.a)("".concat(h.basePath, "decode.min.js"))), (p = !0));
					w = Object(d.b)(w);
					w = self.BrotliDecode(w);
					return e ? w : Object(d.a)(w);
				},
				B = function (w, e) {
					return Object(k.b)(void 0, void 0, Promise, function () {
						var C;
						return Object(k.c)(this, function (D) {
							switch (D.label) {
								case 0:
									return p
										? [3, 2]
										: [
												4,
												Object(r.a)(
													"".concat(
														self.Core.getWorkerPath(),
														"external/decode.min.js",
													),
													"Failed to download decode.min.js",
													window,
												),
											];
								case 1:
									D.sent(), (p = !0), (D.label = 2);
								case 2:
									return (
										(C = self.BrotliDecode(Object(d.b)(w))),
										[2, e ? C : Object(d.a)(C)]
									);
							}
						});
					});
				};
			(function () {
				function w() {
					this.remainingDataArrays = [];
				}
				w.prototype.processRaw = function (e) {
					return e;
				};
				w.prototype.processBrotli = function (e) {
					this.remainingDataArrays.push(e);
					return null;
				};
				w.prototype.GetNextChunk = function (e) {
					this.decodeFunction ||
						(this.decodeFunction =
							0 === e[0] && 97 === e[1] && 115 === e[2] && 109 === e[3]
								? this.processRaw
								: this.processBrotli);
					return this.decodeFunction(e);
				};
				w.prototype.End = function () {
					if (this.remainingDataArrays.length) {
						for (var e = this.arrays, C = 0, D = 0; D < e.length; ++D)
							C += e[D].length;
						C = new Uint8Array(C);
						var I = 0;
						for (D = 0; D < e.length; ++D) {
							var O = e[D];
							C.set(O, I);
							I += O.length;
						}
						return x(C, !0);
					}
					return null;
				};
				return w;
			})();
			var A = function (w, e, C) {
					void 0 === e && (e = !0);
					void 0 === C && (C = !1);
					var D = new XMLHttpRequest();
					D.open("GET", w, e);
					w = C && D.overrideMimeType;
					D.responseType = w ? "text" : "arraybuffer";
					w && D.overrideMimeType("text/plain; charset=x-user-defined");
					return D;
				},
				L = function (w, e, C) {
					return new Promise(function (D, I) {
						var O = A(w, e, C);
						O.send();
						O.onload = function () {
							200 === this.status || 0 === this.status
								? D(O.response)
								: I(Error("Download Failed ".concat(w)));
						};
						O.onerror = function () {
							I(Error("Network error occurred ".concat(w)));
						};
					});
				},
				y = function (w, e) {
					var C = e.decompressFunction,
						D = e.shouldOutputArray,
						I = e.compressedMaximum,
						O = "undefined" !== typeof m ? Date.now() : null;
					try {
						var U = D ? Q(w) : w.join("");
						Object(g.a)("worker", "Result length is ".concat(U.length));
						if (U.length < I) {
							var W = C(U, D);
							Object(g.b)(
								"There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.",
							);
							m &&
								Object(g.a)(
									"worker",
									"Decompressed length is ".concat(W.length),
								);
							U = W;
						} else D || (U = Object(d.a)(U));
						if (m) {
							var R = e.paths.join(", ");
							Object(g.a)(
								"worker",
								""
									.concat(R, " Decompression took ")
									.concat(Date.now() - O, " ms"),
							);
						}
						return U;
					} catch (ia) {
						throw Error("Failed to decompress: ".concat(ia));
					}
				},
				Q = function (w) {
					w = w.reduce(function (e, C) {
						C = new Uint8Array(C);
						return e.concat(Array.from(C));
					}, []);
					return new Uint8Array(w);
				},
				ea = function (w) {
					var e = !w.shouldOutputArray,
						C = w.paths,
						D = w.isAsync;
					D
						? (C = Promise.all(
								C.map(function (I) {
									return L(I, D, e);
								}),
							)
								.then(function (I) {
									return y(I, w);
								})
								.catch(function (I) {
									throw Error(
										"Failed to fetch or decompress files: ".concat(I.message),
									);
								}))
						: ((C = C.map(function (I) {
								var O = A(I, D, e);
								O.send();
								if (200 === O.status || 0 === O.status) return O.response;
								throw Error("Failed to load ".concat(I));
							})),
							(C = y(C, w)));
					return C;
				},
				la = function (w) {
					var e = w.lastIndexOf("/");
					-1 === e && (e = 0);
					var C = w.slice(e).replace(".", ".br.");
					m ||
						(C.endsWith(".js.mem")
							? (C = C.replace(".js.mem", ".mem"))
							: C.endsWith(".js") && (C = C.concat(".mem")));
					return w.slice(0, e) + C;
				},
				M = function (w) {
					return w.map(function (e) {
						return la(e);
					});
				},
				Z = function (w, e) {
					e.decompressFunction = m ? x : B;
					e.paths = M(w);
					return ea(e);
				},
				qa = function (w, e, C, D) {
					return w.catch(function (I) {
						Object(g.b)(I);
						return D(e, C);
					});
				},
				ha = function (w, e, C, D) {
					w = Array.isArray(w) ? w : [w];
					a: {
						var I = [Z];
						e = { compressedMaximum: e, isAsync: C, shouldOutputArray: D };
						if (e.isAsync) {
							var O = I[0](w, e);
							for (C = 1; C < I.length; ++C) O = qa(O, w, e, I[C]);
						} else {
							for (C = 0; C < I.length; C++) {
								D = I[C];
								try {
									O = D(w, e);
									break a;
								} catch (U) {
									Object(g.b)(U.message);
								}
							}
							throw Error("None of the worker files were able to load. ");
						}
					}
					return O;
				};
		},
		function (t, u, n) {
			n.d(u, "a", function () {
				return r;
			});
			n.d(u, "b", function () {
				return d;
			});
			var k = {},
				g = {
					flattenedResources: !1,
					CANVAS_CACHE_SIZE: void 0,
					maxPagesBefore: void 0,
					maxPagesAhead: void 0,
					disableLogs: !1,
					wvsQueryParameters: {},
					_trnDebugMode: !1,
					_logFiltersEnabled: null,
				},
				r = function (q) {
					return g[q];
				},
				d = function (q, h) {
					var m;
					g[q] = h;
					null === (m = k[q]) || void 0 === m
						? void 0
						: m.forEach(function (p) {
								p(h);
							});
				};
		},
		function (t, u, n) {
			function k(d, q, h, m) {
				function p(x) {
					return x instanceof h
						? x
						: new h(function (B) {
								B(x);
							});
				}
				return new (h || (h = Promise))(function (x, B) {
					function A(Q) {
						try {
							y(m.next(Q));
						} catch (ea) {
							B(ea);
						}
					}
					function L(Q) {
						try {
							y(m["throw"](Q));
						} catch (ea) {
							B(ea);
						}
					}
					function y(Q) {
						Q.done ? x(Q.value) : p(Q.value).then(A, L);
					}
					y((m = m.apply(d, q || [])).next());
				});
			}
			function g(d, q) {
				function h(y) {
					return function (Q) {
						return m([y, Q]);
					};
				}
				function m(y) {
					if (x) throw new TypeError("Generator is already executing.");
					for (; L && ((L = 0), y[0] && (p = 0)), p; )
						try {
							if (
								((x = 1),
								B &&
									(A =
										y[0] & 2
											? B["return"]
											: y[0]
												? B["throw"] || ((A = B["return"]) && A.call(B), 0)
												: B.next) &&
									!(A = A.call(B, y[1])).done)
							)
								return A;
							if (((B = 0), A)) y = [y[0] & 2, A.value];
							switch (y[0]) {
								case 0:
								case 1:
									A = y;
									break;
								case 4:
									return p.label++, { value: y[1], done: !1 };
								case 5:
									p.label++;
									B = y[1];
									y = [0];
									continue;
								case 7:
									y = p.ops.pop();
									p.trys.pop();
									continue;
								default:
									if (
										!((A = p.trys), (A = 0 < A.length && A[A.length - 1])) &&
										(6 === y[0] || 2 === y[0])
									) {
										p = 0;
										continue;
									}
									if (3 === y[0] && (!A || (y[1] > A[0] && y[1] < A[3])))
										p.label = y[1];
									else if (6 === y[0] && p.label < A[1])
										(p.label = A[1]), (A = y);
									else if (A && p.label < A[2]) (p.label = A[2]), p.ops.push(y);
									else {
										A[2] && p.ops.pop();
										p.trys.pop();
										continue;
									}
							}
							y = q.call(d, p);
						} catch (Q) {
							(y = [6, Q]), (B = 0);
						} finally {
							x = A = 0;
						}
					if (y[0] & 5) throw y[1];
					return { value: y[0] ? y[1] : void 0, done: !0 };
				}
				var p = {
						label: 0,
						sent: function () {
							if (A[0] & 1) throw A[1];
							return A[1];
						},
						trys: [],
						ops: [],
					},
					x,
					B,
					A,
					L = Object.create(
						("function" === typeof Iterator ? Iterator : Object).prototype,
					);
				return (
					(L.next = h(0)),
					(L["throw"] = h(1)),
					(L["return"] = h(2)),
					"function" === typeof Symbol &&
						(L[Symbol.iterator] = function () {
							return this;
						}),
					L
				);
			}
			n.d(u, "a", function () {
				return r;
			});
			n.d(u, "b", function () {
				return k;
			});
			n.d(u, "c", function () {
				return g;
			});
			var r = function () {
				r =
					Object.assign ||
					function (d) {
						for (var q, h = 1, m = arguments.length; h < m; h++) {
							q = arguments[h];
							for (var p in q)
								Object.prototype.hasOwnProperty.call(q, p) && (d[p] = q[p]);
						}
						return d;
					};
				return r.apply(this, arguments);
			};
		},
		function (t, u, n) {
			n.d(u, "b", function () {
				return k;
			});
			n.d(u, "a", function () {
				return g;
			});
			var k = function (r) {
					if ("string" === typeof r) {
						for (
							var d = new Uint8Array(r.length), q = r.length, h = 0;
							h < q;
							h++
						)
							d[h] = r.charCodeAt(h);
						return d;
					}
					return r;
				},
				g = function (r) {
					if ("string" !== typeof r) {
						for (var d = "", q = 0, h = r.length, m; q < h; )
							(m = r.subarray(q, q + 1024)),
								(q += 1024),
								(d += String.fromCharCode.apply(null, m));
						return d;
					}
					return r;
				};
		},
		function (t, u, n) {
			n.d(u, "a", function () {
				return p;
			});
			var k = n(3);
			t = n(8);
			var g = n.n(t),
				r = new Map(),
				d = function () {
					return ("undefined" === typeof window ? self : window).trustedTypes;
				},
				q = function (x, B) {
					return d().createPolicy(x, {
						createHTML: function (A) {
							return g.a.sanitize(
								A,
								Object(k.a)(Object(k.a)({}, B), { RETURN_TRUSTED_TYPE: !1 }),
							);
						},
						createScript: function (A) {
							return A;
						},
						createScriptURL: function (A) {
							return A;
						},
					});
				},
				h = function (x) {
					return d().createPolicy(x, {
						createHTML: function (B) {
							return B;
						},
						createScript: function (B) {
							return B;
						},
						createScriptURL: function (B) {
							return B;
						},
					});
				},
				m = function (x, B) {
					var A,
						L = {
							createHTML: function (y) {
								return g.a.sanitize(y, Object(k.a)({}, B));
							},
							createScript: function (y) {
								return y;
							},
							createScriptURL: function (y) {
								return y;
							},
						};
					if (null === (A = d()) || void 0 === A ? 0 : A.createPolicy)
						(A = "".concat(x, "-po")),
							r.has(x) || ((L = h(A)), r.set(A, L), (L = q(x, B)), r.set(x, L)),
							(L = r.get(B.createPolicyOnly ? A : x));
					return L;
				},
				p = function (x, B) {
					void 0 === B &&
						(B = { createPolicyOnly: !0, trustedTypesPolicyName: "webviewer" });
					return m(B.trustedTypesPolicyName, B).createScriptURL(x.toString());
				};
		},
		function (t, u, n) {
			function k(r, d, q) {
				return new Promise(function (h) {
					if (!r) return h();
					var m = q.document.createElement("script");
					m.type = "text/javascript";
					m.onload = function () {
						h();
					};
					m.onerror = function () {
						d && Object(g.b)(d);
						h();
					};
					m.src = r;
					q.document.getElementsByTagName("head")[0].appendChild(m);
				});
			}
			n.d(u, "a", function () {
				return k;
			});
			var g = n(0);
		},
		function (t, u, n) {
			function k(h, m, p, x) {
				return g(
					h,
					m,
					p,
					x,
					!!WebAssembly.instantiateStreaming,
					void 0,
					void 0,
				).then(function (B) {
					Object(r.a)(
						"load",
						"WASM compilation took ".concat(Date.now() - NaN, " ms"),
					);
					return B;
				});
			}
			function g(h, m, p, x, B, A, L) {
				A = A || Date.now();
				if (B && !x)
					return (
						Object(r.a)("load", "Try instantiateStreaming"),
						fetch(Object(d.a)(h))
							.then(function (y) {
								return WebAssembly.instantiateStreaming(y, m);
							})
							.catch(function (y) {
								Object(r.a)(
									"load",
									"instantiateStreaming Failed "
										.concat(h, " message ")
										.concat(y.message),
								);
								return g(h, m, p, x, !1, A, L);
							})
					);
				B = x
					? x.map(function (y, Q) {
							return "".concat(y, "PDFNetCWasm-chunk-").concat(Q, ".wasm");
						})
					: h;
				return Object(d.b)(B, p, !0, !0).then(function (y) {
					L = Date.now();
					Object(r.a)("load", "Request took ".concat(L - A, " ms"));
					return WebAssembly.instantiate(y, m);
				});
			}
			n.d(u, "a", function () {
				return k;
			});
			var r = n(0),
				d = n(1),
				q = n(6);
			n.d(u, "b", function () {
				return q.a;
			});
		},
		function (t, u, n) {
			(function (k, g) {
				t.exports = g();
			})(this, function () {
				function k(N) {
					return function (H) {
						H instanceof RegExp && (H.lastIndex = 0);
						for (
							var K = arguments.length, P = Array(1 < K ? K - 1 : 0), S = 1;
							S < K;
							S++
						)
							P[S - 1] = arguments[S];
						return ea(N, H, P);
					};
				}
				function g(N, H) {
					var K =
						2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : e;
					p && p(N, null);
					for (var P = H.length; P--; ) {
						var S = H[P];
						if ("string" === typeof S) {
							var na = K(S);
							na !== S && (x(H) || (H[P] = na), (S = na));
						}
						N[S] = !0;
					}
					return N;
				}
				function r(N) {
					for (
						var H = Q(null), K = $jscomp.makeIterator(m(N)), P = K.next();
						!P.done;
						P = K.next()
					) {
						var S = $jscomp.makeIterator(P.value);
						P = S.next().value;
						S = S.next().value;
						if (W(N, P))
							if (Array.isArray(S)) {
								for (var na = 0; na < S.length; na++)
									W(S, na) || (S[na] = null);
								H[P] = S;
							} else
								H[P] =
									S && "object" === typeof S && S.constructor === Object
										? r(S)
										: S;
					}
					return H;
				}
				function d(N, H) {
					for (; null !== N; ) {
						var K = A(N, H);
						if (K) {
							if (K.get) return k(K.get);
							if ("function" === typeof K.value) return k(K.value);
						}
						N = B(N);
					}
					return function () {
						return null;
					};
				}
				function q() {
					function N(l, z, E) {
						M(l, function (G) {
							G.call(K, z, E, xa);
						});
					}
					var H =
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: "undefined" === typeof window
									? null
									: window,
						K = function (l) {
							return q(l);
						};
					K.version = "3.2.5";
					K.removed = [];
					if (
						!H ||
						!H.document ||
						H.document.nodeType !== Ka.document ||
						!H.Element
					)
						return (K.isSupported = !1), K;
					var P = H.document,
						S = P,
						na = S.currentScript,
						ra = H.DocumentFragment,
						Y = H.HTMLTemplateElement,
						Ta = H.Node,
						hb = H.Element,
						Ba = H.NodeFilter,
						Vb =
							void 0 === H.NamedNodeMap
								? H.NamedNodeMap || H.MozNamedAttrMap
								: H.NamedNodeMap,
						Ua = H.HTMLFormElement,
						Wb = H.DOMParser,
						Va = H.trustedTypes;
					H = hb.prototype;
					var ib = d(H, "cloneNode"),
						Xb = d(H, "remove"),
						Yb = d(H, "nextSibling"),
						Db = d(H, "childNodes"),
						La = d(H, "parentNode");
					"function" === typeof Y &&
						((Y = P.createElement("template")),
						Y.content &&
							Y.content.ownerDocument &&
							(P = Y.content.ownerDocument));
					var ka,
						ta = "";
					Y = P;
					var jb = Y.implementation,
						Zb = Y.createNodeIterator,
						Eb = Y.createDocumentFragment,
						$b = Y.getElementsByTagName,
						ac = S.importNode,
						ja = Fb();
					K.isSupported =
						"function" === typeof m &&
						"function" === typeof La &&
						jb &&
						void 0 !== jb.createHTMLDocument;
					Y = Gb;
					var Wa = Y.MUSTACHE_EXPR,
						kb = Y.ERB_EXPR,
						Ca = Y.TMPLIT_EXPR,
						bc = Y.DATA_ATTR,
						cc = Y.ARIA_ATTR,
						Hb = Y.IS_SCRIPT_OR_DATA,
						Ib = Y.ATTR_WHITESPACE,
						lb = Y.CUSTOM_ELEMENT,
						Xa = Gb.IS_ALLOWED_URI,
						ca = null,
						Ma = g(
							{},
							[].concat(
								$jscomp.arrayFromIterable(sa),
								$jscomp.arrayFromIterable(oa),
								$jscomp.arrayFromIterable(a),
								$jscomp.arrayFromIterable(c),
								$jscomp.arrayFromIterable(v),
							),
						),
						da = null,
						Jb = g(
							{},
							[].concat(
								$jscomp.arrayFromIterable(F),
								$jscomp.arrayFromIterable(T),
								$jscomp.arrayFromIterable(V),
								$jscomp.arrayFromIterable(fa),
							),
						),
						X = Object.seal(
							Q(null, {
								tagNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								attributeNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								allowCustomizedBuiltInElements: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: !1,
								},
							}),
						),
						Na = null,
						mb = null,
						nb = !0,
						Ya = !0,
						Kb = !1,
						Da = !0,
						ua = !1,
						ob = !0,
						ya = !1,
						Za = !1,
						pb = !1,
						Ea = !1,
						$a = !1,
						Oa = !1,
						qb = !0,
						rb = !1,
						sb = !0,
						Fa = !1,
						za = {},
						Aa = null,
						tb = g(
							{},
							"annotation-xml audio colgroup desc foreignobject head iframe math mi mn mo ms mtext noembed noframes noscript plaintext script style svg template thead title video xmp".split(
								" ",
							),
						),
						ub = null,
						vb = g({}, "audio video img source image track".split(" ")),
						ab = null,
						wb = g(
							{},
							"alt class for id label name pattern placeholder role summary title value style xmlns".split(
								" ",
							),
						),
						Ga = "http://www.w3.org/1999/xhtml",
						Lb = !1,
						bb = null,
						dc = g(
							{},
							[
								"http://www.w3.org/1998/Math/MathML",
								"http://www.w3.org/2000/svg",
								"http://www.w3.org/1999/xhtml",
							],
							C,
						),
						Pa = g({}, ["mi", "mo", "mn", "ms", "mtext"]),
						cb = g({}, ["annotation-xml"]),
						ec = g({}, ["title", "style", "font", "a", "script"]),
						Ha = null,
						Mb = ["application/xhtml+xml", "text/html"],
						aa = null,
						xa = null,
						Qa = P.createElement("form"),
						Nb = function (l) {
							return l instanceof RegExp || l instanceof Function;
						},
						db = function () {
							var l =
								0 < arguments.length && void 0 !== arguments[0]
									? arguments[0]
									: {};
							if (!xa || xa !== l) {
								(l && "object" === typeof l) || (l = {});
								l = r(l);
								Ha =
									-1 === Mb.indexOf(l.PARSER_MEDIA_TYPE)
										? "text/html"
										: l.PARSER_MEDIA_TYPE;
								aa = "application/xhtml+xml" === Ha ? C : e;
								ca = W(l, "ALLOWED_TAGS") ? g({}, l.ALLOWED_TAGS, aa) : Ma;
								da = W(l, "ALLOWED_ATTR") ? g({}, l.ALLOWED_ATTR, aa) : Jb;
								bb = W(l, "ALLOWED_NAMESPACES")
									? g({}, l.ALLOWED_NAMESPACES, C)
									: dc;
								ab = W(l, "ADD_URI_SAFE_ATTR")
									? g(r(wb), l.ADD_URI_SAFE_ATTR, aa)
									: wb;
								ub = W(l, "ADD_DATA_URI_TAGS")
									? g(r(vb), l.ADD_DATA_URI_TAGS, aa)
									: vb;
								Aa = W(l, "FORBID_CONTENTS")
									? g({}, l.FORBID_CONTENTS, aa)
									: tb;
								Na = W(l, "FORBID_TAGS") ? g({}, l.FORBID_TAGS, aa) : {};
								mb = W(l, "FORBID_ATTR") ? g({}, l.FORBID_ATTR, aa) : {};
								za = W(l, "USE_PROFILES") ? l.USE_PROFILES : !1;
								nb = !1 !== l.ALLOW_ARIA_ATTR;
								Ya = !1 !== l.ALLOW_DATA_ATTR;
								Kb = l.ALLOW_UNKNOWN_PROTOCOLS || !1;
								Da = !1 !== l.ALLOW_SELF_CLOSE_IN_ATTR;
								ua = l.SAFE_FOR_TEMPLATES || !1;
								ob = !1 !== l.SAFE_FOR_XML;
								ya = l.WHOLE_DOCUMENT || !1;
								Ea = l.RETURN_DOM || !1;
								$a = l.RETURN_DOM_FRAGMENT || !1;
								Oa = l.RETURN_TRUSTED_TYPE || !1;
								pb = l.FORCE_BODY || !1;
								qb = !1 !== l.SANITIZE_DOM;
								rb = l.SANITIZE_NAMED_PROPS || !1;
								sb = !1 !== l.KEEP_CONTENT;
								Fa = l.IN_PLACE || !1;
								Xa = l.ALLOWED_URI_REGEXP || Ob;
								Ga = l.NAMESPACE || "http://www.w3.org/1999/xhtml";
								Pa = l.MATHML_TEXT_INTEGRATION_POINTS || Pa;
								cb = l.HTML_INTEGRATION_POINTS || cb;
								X = l.CUSTOM_ELEMENT_HANDLING || {};
								l.CUSTOM_ELEMENT_HANDLING &&
									Nb(l.CUSTOM_ELEMENT_HANDLING.tagNameCheck) &&
									(X.tagNameCheck = l.CUSTOM_ELEMENT_HANDLING.tagNameCheck);
								l.CUSTOM_ELEMENT_HANDLING &&
									Nb(l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck) &&
									(X.attributeNameCheck =
										l.CUSTOM_ELEMENT_HANDLING.attributeNameCheck);
								l.CUSTOM_ELEMENT_HANDLING &&
									"boolean" ===
										typeof l.CUSTOM_ELEMENT_HANDLING
											.allowCustomizedBuiltInElements &&
									(X.allowCustomizedBuiltInElements =
										l.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements);
								ua && (Ya = !1);
								$a && (Ea = !0);
								za &&
									((ca = g({}, v)),
									(da = []),
									!0 === za.html && (g(ca, sa), g(da, F)),
									!0 === za.svg && (g(ca, oa), g(da, T), g(da, fa)),
									!0 === za.svgFilters && (g(ca, a), g(da, T), g(da, fa)),
									!0 === za.mathMl && (g(ca, c), g(da, V), g(da, fa)));
								l.ADD_TAGS &&
									(ca === Ma && (ca = r(ca)), g(ca, l.ADD_TAGS, aa));
								l.ADD_ATTR &&
									(da === Jb && (da = r(da)), g(da, l.ADD_ATTR, aa));
								l.ADD_URI_SAFE_ATTR && g(ab, l.ADD_URI_SAFE_ATTR, aa);
								l.FORBID_CONTENTS &&
									(Aa === tb && (Aa = r(Aa)), g(Aa, l.FORBID_CONTENTS, aa));
								sb && (ca["#text"] = !0);
								ya && g(ca, ["html", "head", "body"]);
								ca.table && (g(ca, ["tbody"]), delete Na.tbody);
								if (l.TRUSTED_TYPES_POLICY) {
									if ("function" !== typeof l.TRUSTED_TYPES_POLICY.createHTML)
										throw ia(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.',
										);
									if (
										"function" !== typeof l.TRUSTED_TYPES_POLICY.createScriptURL
									)
										throw ia(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.',
										);
									ka = l.TRUSTED_TYPES_POLICY;
									ta = ka.createHTML("");
								} else
									void 0 === ka && (ka = fc(Va, na)),
										null !== ka &&
											"string" === typeof ta &&
											(ta = ka.createHTML(""));
								y && y(l);
								xa = l;
							}
						},
						Pb = g(
							{},
							[].concat(
								$jscomp.arrayFromIterable(oa),
								$jscomp.arrayFromIterable(a),
								$jscomp.arrayFromIterable(b),
							),
						),
						Qb = g(
							{},
							[].concat(
								$jscomp.arrayFromIterable(c),
								$jscomp.arrayFromIterable(f),
							),
						),
						Rb = function (l) {
							var z = La(l);
							(z && z.tagName) ||
								(z = { namespaceURI: Ga, tagName: "template" });
							var E = e(l.tagName),
								G = e(z.tagName);
							return bb[l.namespaceURI]
								? "http://www.w3.org/2000/svg" === l.namespaceURI
									? "http://www.w3.org/1999/xhtml" === z.namespaceURI
										? "svg" === E
										: "http://www.w3.org/1998/Math/MathML" === z.namespaceURI
											? "svg" === E && ("annotation-xml" === G || Pa[G])
											: !!Pb[E]
									: "http://www.w3.org/1998/Math/MathML" === l.namespaceURI
										? "http://www.w3.org/1999/xhtml" === z.namespaceURI
											? "math" === E
											: "http://www.w3.org/2000/svg" === z.namespaceURI
												? "math" === E && cb[G]
												: !!Qb[E]
										: "http://www.w3.org/1999/xhtml" === l.namespaceURI
											? ("http://www.w3.org/2000/svg" === z.namespaceURI &&
													!cb[G]) ||
												("http://www.w3.org/1998/Math/MathML" ===
													z.namespaceURI &&
													!Pa[G])
												? !1
												: !Qb[E] && (ec[E] || !Pb[E])
											: "application/xhtml+xml" === Ha && bb[l.namespaceURI]
												? !0
												: !1
								: !1;
						},
						pa = function (l) {
							ha(K.removed, { element: l });
							try {
								La(l).removeChild(l);
							} catch (z) {
								Xb(l);
							}
						},
						Ra = function (l, z) {
							try {
								ha(K.removed, { attribute: z.getAttributeNode(l), from: z });
							} catch (E) {
								ha(K.removed, { attribute: null, from: z });
							}
							z.removeAttribute(l);
							if ("is" === l)
								if (Ea || $a)
									try {
										pa(z);
									} catch (E) {}
								else
									try {
										z.setAttribute(l, "");
									} catch (E) {}
						},
						xb = function (l) {
							var z = null,
								E = null;
							pb
								? (l = "<remove></remove>" + l)
								: (E = (E = D(l, /^[\r\n\t ]+/)) && E[0]);
							"application/xhtml+xml" === Ha &&
								"http://www.w3.org/1999/xhtml" === Ga &&
								(l =
									'<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' +
									l +
									"</body></html>");
							var G = ka ? ka.createHTML(l) : l;
							if ("http://www.w3.org/1999/xhtml" === Ga)
								try {
									z = new Wb().parseFromString(G, Ha);
								} catch (J) {}
							if (!z || !z.documentElement) {
								z = jb.createDocument(Ga, "template", null);
								try {
									z.documentElement.innerHTML = Lb ? ta : G;
								} catch (J) {}
							}
							G = z.body || z.documentElement;
							l &&
								E &&
								G.insertBefore(P.createTextNode(E), G.childNodes[0] || null);
							return "http://www.w3.org/1999/xhtml" === Ga
								? $b.call(z, ya ? "html" : "body")[0]
								: ya
									? z.documentElement
									: G;
						},
						Ia = function (l) {
							return Zb.call(
								l.ownerDocument || l,
								l,
								Ba.SHOW_ELEMENT |
									Ba.SHOW_COMMENT |
									Ba.SHOW_TEXT |
									Ba.SHOW_PROCESSING_INSTRUCTION |
									Ba.SHOW_CDATA_SECTION,
								null,
							);
						},
						eb = function (l) {
							return (
								l instanceof Ua &&
								("string" !== typeof l.nodeName ||
									"string" !== typeof l.textContent ||
									"function" !== typeof l.removeChild ||
									!(l.attributes instanceof Vb) ||
									"function" !== typeof l.removeAttribute ||
									"function" !== typeof l.setAttribute ||
									"string" !== typeof l.namespaceURI ||
									"function" !== typeof l.insertBefore ||
									"function" !== typeof l.hasChildNodes)
							);
						},
						Sb = function (l) {
							return "function" === typeof Ta && l instanceof Ta;
						},
						yb = function (l) {
							var z = null;
							N(ja.beforeSanitizeElements, l, null);
							if (eb(l)) return pa(l), !0;
							var E = aa(l.nodeName);
							N(ja.uponSanitizeElement, l, { tagName: E, allowedTags: ca });
							if (
								(l.hasChildNodes() &&
									!Sb(l.firstElementChild) &&
									R(/<[/\w!]/g, l.innerHTML) &&
									R(/<[/\w!]/g, l.textContent)) ||
								l.nodeType === Ka.progressingInstruction ||
								(ob && l.nodeType === Ka.comment && R(/<[/\w]/g, l.data))
							)
								return pa(l), !0;
							if (!ca[E] || Na[E]) {
								if (
									!Na[E] &&
									"annotation-xml" !== E &&
									D(E, lb) &&
									((X.tagNameCheck instanceof RegExp && R(X.tagNameCheck, E)) ||
										(X.tagNameCheck instanceof Function && X.tagNameCheck(E)))
								)
									return !1;
								if (sb && !Aa[E]) {
									E = La(l) || l.parentNode;
									var G = Db(l) || l.childNodes;
									if (G && E)
										for (var J = G.length - 1; 0 <= J; --J) {
											var ba = ib(G[J], !0);
											ba.__removalCount = (l.__removalCount || 0) + 1;
											E.insertBefore(ba, Yb(l));
										}
								}
								pa(l);
								return !0;
							}
							if (
								(l instanceof hb && !Rb(l)) ||
								(("noscript" === E || "noembed" === E || "noframes" === E) &&
									R(/<\/no(script|embed|frames)/i, l.innerHTML))
							)
								return pa(l), !0;
							ua &&
								l.nodeType === Ka.text &&
								((z = l.textContent),
								M([Wa, kb, Ca], function (ma) {
									z = I(z, ma, " ");
								}),
								l.textContent !== z &&
									(ha(K.removed, { element: l.cloneNode() }),
									(l.textContent = z)));
							N(ja.afterSanitizeElements, l, null);
							return !1;
						},
						zb = function (l, z, E) {
							if (qb && ("id" === z || "name" === z) && (E in P || E in Qa))
								return !1;
							if (!Ya || mb[z] || !R(bc, z))
								if (!nb || !R(cc, z))
									if (!da[z] || mb[z]) {
										if (
											!(
												("annotation-xml" !== l &&
													D(l, lb) &&
													((X.tagNameCheck instanceof RegExp &&
														R(X.tagNameCheck, l)) ||
														(X.tagNameCheck instanceof Function &&
															X.tagNameCheck(l))) &&
													((X.attributeNameCheck instanceof RegExp &&
														R(X.attributeNameCheck, z)) ||
														(X.attributeNameCheck instanceof Function &&
															X.attributeNameCheck(z)))) ||
												("is" === z &&
													X.allowCustomizedBuiltInElements &&
													((X.tagNameCheck instanceof RegExp &&
														R(X.tagNameCheck, E)) ||
														(X.tagNameCheck instanceof Function &&
															X.tagNameCheck(E))))
											)
										)
											return !1;
									} else if (
										!(
											ab[z] ||
											R(Xa, I(E, Ib, "")) ||
											(("src" === z || "xlink:href" === z || "href" === z) &&
												"script" !== l &&
												0 === O(E, "data:") &&
												ub[l]) ||
											(Kb && !R(Hb, I(E, Ib, "")))
										) &&
										E
									)
										return !1;
							return !0;
						},
						Ab = function (l) {
							N(ja.beforeSanitizeAttributes, l, null);
							var z = l.attributes;
							if (z && !eb(l)) {
								for (
									var E = {
											attrName: "",
											attrValue: "",
											keepAttr: !0,
											allowedAttributes: da,
											forceKeepAttr: void 0,
										},
										G = z.length,
										J = {};
									G--;
								) {
									var ba = z[G],
										ma = ba.name,
										fb = ba.namespaceURI,
										Ja = ba.value;
									ba = aa(ma);
									J.$jscomp$loop$prop$value$5 = "value" === ma ? Ja : U(Ja);
									E.attrName = ba;
									E.attrValue = J.$jscomp$loop$prop$value$5;
									E.keepAttr = !0;
									E.forceKeepAttr = void 0;
									N(ja.uponSanitizeAttribute, l, E);
									J.$jscomp$loop$prop$value$5 = E.attrValue;
									!rb ||
										("id" !== ba && "name" !== ba) ||
										(Ra(ma, l),
										(J.$jscomp$loop$prop$value$5 =
											"user-content-" + J.$jscomp$loop$prop$value$5));
									if (
										ob &&
										R(
											/((--!?|])>)|<\/(style|title)/i,
											J.$jscomp$loop$prop$value$5,
										)
									)
										Ra(ma, l);
									else if (!E.forceKeepAttr && (Ra(ma, l), E.keepAttr))
										if (!Da && R(/\/>/i, J.$jscomp$loop$prop$value$5))
											Ra(ma, l);
										else if (
											(ua &&
												M(
													[Wa, kb, Ca],
													(function (gb) {
														return function (gc) {
															gb.$jscomp$loop$prop$value$5 = I(
																gb.$jscomp$loop$prop$value$5,
																gc,
																" ",
															);
														};
													})(J),
												),
											(Ja = aa(l.nodeName)),
											zb(Ja, ba, J.$jscomp$loop$prop$value$5))
										) {
											if (
												ka &&
												"object" === typeof Va &&
												"function" === typeof Va.getAttributeType &&
												!fb
											)
												switch (Va.getAttributeType(Ja, ba)) {
													case "TrustedHTML":
														J.$jscomp$loop$prop$value$5 = ka.createHTML(
															J.$jscomp$loop$prop$value$5,
														);
														break;
													case "TrustedScriptURL":
														J.$jscomp$loop$prop$value$5 = ka.createScriptURL(
															J.$jscomp$loop$prop$value$5,
														);
												}
											try {
												fb
													? l.setAttributeNS(
															fb,
															ma,
															J.$jscomp$loop$prop$value$5,
														)
													: l.setAttribute(ma, J.$jscomp$loop$prop$value$5),
													eb(l) ? pa(l) : qa(K.removed);
											} catch (gb) {}
										}
									J = {
										$jscomp$loop$prop$value$5: J.$jscomp$loop$prop$value$5,
									};
								}
								N(ja.afterSanitizeAttributes, l, null);
							}
						},
						Tb = function E(z) {
							var G,
								J = Ia(z);
							for (N(ja.beforeSanitizeShadowDOM, z, null); (G = J.nextNode()); )
								N(ja.uponSanitizeShadowNode, G, null),
									yb(G),
									Ab(G),
									G.content instanceof ra && E(G.content);
							N(ja.afterSanitizeShadowDOM, z, null);
						};
					K.sanitize = function (z) {
						var E =
								1 < arguments.length && void 0 !== arguments[1]
									? arguments[1]
									: {},
							G = null,
							J = null;
						J = J = null;
						(Lb = !z) && (z = "\x3c!--\x3e");
						if ("string" !== typeof z && !Sb(z))
							if ("function" === typeof z.toString) {
								if (((z = z.toString()), "string" !== typeof z))
									throw ia("dirty is not a string, aborting");
							} else throw ia("toString is not a function");
						if (!K.isSupported) return z;
						Za || db(E);
						K.removed = [];
						"string" === typeof z && (Fa = !1);
						if (Fa) {
							if (z.nodeName && ((E = aa(z.nodeName)), !ca[E] || Na[E]))
								throw ia(
									"root node is forbidden and cannot be sanitized in-place",
								);
						} else if (z instanceof Ta)
							(G = xb("\x3c!----\x3e")),
								(J = G.ownerDocument.importNode(z, !0)),
								J.nodeType === Ka.element && "BODY" === J.nodeName
									? (G = J)
									: "HTML" === J.nodeName
										? (G = J)
										: G.appendChild(J);
						else {
							if (!Ea && !ua && !ya && -1 === z.indexOf("<"))
								return ka && Oa ? ka.createHTML(z) : z;
							G = xb(z);
							if (!G) return Ea ? null : Oa ? ta : "";
						}
						G && pb && pa(G.firstChild);
						for (E = Ia(Fa ? z : G); (J = E.nextNode()); )
							yb(J), Ab(J), J.content instanceof ra && Tb(J.content);
						if (Fa) return z;
						if (Ea) {
							if ($a)
								for (J = Eb.call(G.ownerDocument); G.firstChild; )
									J.appendChild(G.firstChild);
							else J = G;
							if (da.shadowroot || da.shadowrootmode) J = ac.call(S, J, !0);
							return J;
						}
						var ba = ya ? G.outerHTML : G.innerHTML;
						ya &&
							ca["!doctype"] &&
							G.ownerDocument &&
							G.ownerDocument.doctype &&
							G.ownerDocument.doctype.name &&
							R(Ub, G.ownerDocument.doctype.name) &&
							(ba = "<!DOCTYPE " + G.ownerDocument.doctype.name + ">\n" + ba);
						ua &&
							M([Wa, kb, Ca], function (ma) {
								ba = I(ba, ma, " ");
							});
						return ka && Oa ? ka.createHTML(ba) : ba;
					};
					K.setConfig = function () {
						db(
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: {},
						);
						Za = !0;
					};
					K.clearConfig = function () {
						xa = null;
						Za = !1;
					};
					K.isValidAttribute = function (z, E, G) {
						xa || db({});
						z = aa(z);
						E = aa(E);
						return zb(z, E, G);
					};
					K.addHook = function (z, E) {
						"function" === typeof E && ha(ja[z], E);
					};
					K.removeHook = function (z, E) {
						return void 0 !== E
							? ((E = Z(ja[z], E)), -1 === E ? void 0 : w(ja[z], E, 1)[0])
							: qa(ja[z]);
					};
					K.removeHooks = function (z) {
						ja[z] = [];
					};
					K.removeAllHooks = function () {
						ja = Fb();
					};
					return K;
				}
				var h = Object,
					m = h.entries,
					p = h.setPrototypeOf,
					x = h.isFrozen,
					B = h.getPrototypeOf,
					A = h.getOwnPropertyDescriptor,
					L = Object,
					y = L.freeze;
				h = L.seal;
				var Q = L.create;
				L = "undefined" !== typeof Reflect && Reflect;
				var ea = L.apply,
					la = L.construct;
				y ||
					(y = function (N) {
						return N;
					});
				h ||
					(h = function (N) {
						return N;
					});
				ea ||
					(ea = function (N, H, K) {
						return N.apply(H, K);
					});
				la ||
					(la = function (N, H) {
						return new (Function.prototype.bind.apply(
							N,
							[null].concat($jscomp.arrayFromIterable(H)),
						))();
					});
				var M = k(Array.prototype.forEach),
					Z = k(Array.prototype.lastIndexOf),
					qa = k(Array.prototype.pop),
					ha = k(Array.prototype.push),
					w = k(Array.prototype.splice),
					e = k(String.prototype.toLowerCase),
					C = k(String.prototype.toString),
					D = k(String.prototype.match),
					I = k(String.prototype.replace),
					O = k(String.prototype.indexOf),
					U = k(String.prototype.trim),
					W = k(Object.prototype.hasOwnProperty),
					R = k(RegExp.prototype.test),
					ia = (function (N) {
						return function () {
							for (var H = arguments.length, K = Array(H), P = 0; P < H; P++)
								K[P] = arguments[P];
							return la(N, K);
						};
					})(TypeError),
					sa = y(
						"a abbr acronym address area article aside audio b bdi bdo big blink blockquote body br button canvas caption center cite code col colgroup content data datalist dd decorator del details dfn dialog dir div dl dt element em fieldset figcaption figure font footer form h1 h2 h3 h4 h5 h6 head header hgroup hr html i img input ins kbd label legend li main map mark marquee menu menuitem meter nav nobr ol optgroup option output p picture pre progress q rp rt ruby s samp section select shadow small source spacer span strike strong style sub summary sup table tbody td template textarea tfoot th thead time tr track tt u ul var video wbr".split(
							" ",
						),
					),
					oa = y(
						"svg a altglyph altglyphdef altglyphitem animatecolor animatemotion animatetransform circle clippath defs desc ellipse filter font g glyph glyphref hkern image line lineargradient marker mask metadata mpath path pattern polygon polyline radialgradient rect stop style switch symbol text textpath title tref tspan view vkern".split(
							" ",
						),
					),
					a = y(
						"feBlend feColorMatrix feComponentTransfer feComposite feConvolveMatrix feDiffuseLighting feDisplacementMap feDistantLight feDropShadow feFlood feFuncA feFuncB feFuncG feFuncR feGaussianBlur feImage feMerge feMergeNode feMorphology feOffset fePointLight feSpecularLighting feSpotLight feTile feTurbulence".split(
							" ",
						),
					),
					b = y(
						"animate color-profile cursor discard font-face font-face-format font-face-name font-face-src font-face-uri foreignobject hatch hatchpath mesh meshgradient meshpatch meshrow missing-glyph script set solidcolor unknown use".split(
							" ",
						),
					),
					c = y(
						"math menclose merror mfenced mfrac mglyph mi mlabeledtr mmultiscripts mn mo mover mpadded mphantom mroot mrow ms mspace msqrt mstyle msub msup msubsup mtable mtd mtext mtr munder munderover mprescripts".split(
							" ",
						),
					),
					f = y(
						"maction maligngroup malignmark mlongdiv mscarries mscarry msgroup mstack msline msrow semantics annotation annotation-xml mprescripts none".split(
							" ",
						),
					),
					v = y(["#text"]),
					F = y(
						"accept action align alt autocapitalize autocomplete autopictureinpicture autoplay background bgcolor border capture cellpadding cellspacing checked cite class clear color cols colspan controls controlslist coords crossorigin datetime decoding default dir disabled disablepictureinpicture disableremoteplayback download draggable enctype enterkeyhint face for headers height hidden high href hreflang id inputmode integrity ismap kind label lang list loading loop low max maxlength media method min minlength multiple muted name nonce noshade novalidate nowrap open optimum pattern placeholder playsinline popover popovertarget popovertargetaction poster preload pubdate radiogroup readonly rel required rev reversed role rows rowspan spellcheck scope selected shape size sizes span srclang start src srcset step style summary tabindex title translate type usemap valign value width wrap xmlns slot".split(
							" ",
						),
					),
					T = y(
						"accent-height accumulate additive alignment-baseline amplitude ascent attributename attributetype azimuth basefrequency baseline-shift begin bias by class clip clippathunits clip-path clip-rule color color-interpolation color-interpolation-filters color-profile color-rendering cx cy d dx dy diffuseconstant direction display divisor dur edgemode elevation end exponent fill fill-opacity fill-rule filter filterunits flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight fx fy g1 g2 glyph-name glyphref gradientunits gradienttransform height href id image-rendering in in2 intercept k k1 k2 k3 k4 kerning keypoints keysplines keytimes lang lengthadjust letter-spacing kernelmatrix kernelunitlength lighting-color local marker-end marker-mid marker-start markerheight markerunits markerwidth maskcontentunits maskunits max mask media method mode min name numoctaves offset operator opacity order orient orientation origin overflow paint-order path pathlength patterncontentunits patterntransform patternunits points preservealpha preserveaspectratio primitiveunits r rx ry radius refx refy repeatcount repeatdur restart result rotate scale seed shape-rendering slope specularconstant specularexponent spreadmethod startoffset stddeviation stitchtiles stop-color stop-opacity stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke stroke-width style surfacescale systemlanguage tabindex tablevalues targetx targety transform transform-origin text-anchor text-decoration text-rendering textlength type u1 u2 unicode values viewbox visibility version vert-adv-y vert-origin-x vert-origin-y width word-spacing wrap writing-mode xchannelselector ychannelselector x x1 x2 xmlns y y1 y2 z zoomandpan".split(
							" ",
						),
					),
					V = y(
						"accent accentunder align bevelled close columnsalign columnlines columnspan denomalign depth dir display displaystyle encoding fence frame height href id largeop length linethickness lspace lquote mathbackground mathcolor mathsize mathvariant maxsize minsize movablelimits notation numalign open rowalign rowlines rowspacing rowspan rspace rquote scriptlevel scriptminsize scriptsizemultiplier selection separator separators stretchy subscriptshift supscriptshift symmetric voffset width xmlns".split(
							" ",
						),
					),
					fa = y([
						"xlink:href",
						"xml:id",
						"xlink:title",
						"xml:space",
						"xmlns:xlink",
					]);
				L = h(/\{\{[\w\W]*|[\w\W]*\}\}/gm);
				var va = h(/<%[\w\W]*|[\w\W]*%>/gm),
					Sa = h(/\$\{[\w\W]*/gm),
					hc = h(/^data-[\-\w.\u00B7-\uFFFF]+$/),
					wa = h(/^aria-[\-\w]+$/),
					Ob = h(
						/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
					),
					ic = h(/^(?:\w+script|data):/i),
					jc = h(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),
					Ub = h(/^html$/i);
				h = h(/^[a-z][.\w]*(-[.\w]+)+$/i);
				var Gb = Object.freeze({
						__proto__: null,
						ARIA_ATTR: wa,
						ATTR_WHITESPACE: jc,
						CUSTOM_ELEMENT: h,
						DATA_ATTR: hc,
						DOCTYPE_NAME: Ub,
						ERB_EXPR: va,
						IS_ALLOWED_URI: Ob,
						IS_SCRIPT_OR_DATA: ic,
						MUSTACHE_EXPR: L,
						TMPLIT_EXPR: Sa,
					}),
					Ka = {
						element: 1,
						attribute: 2,
						text: 3,
						cdataSection: 4,
						entityReference: 5,
						entityNode: 6,
						progressingInstruction: 7,
						comment: 8,
						document: 9,
						documentType: 10,
						documentFragment: 11,
						notation: 12,
					},
					fc = function (N, H) {
						if ("object" !== typeof N || "function" !== typeof N.createPolicy)
							return null;
						var K = null;
						H &&
							H.hasAttribute("data-tt-policy-suffix") &&
							(K = H.getAttribute("data-tt-policy-suffix"));
						H = "dompurify" + (K ? "#" + K : "");
						try {
							return N.createPolicy(H, {
								createHTML: function (P) {
									return P;
								},
								createScriptURL: function (P) {
									return P;
								},
							});
						} catch (P) {
							return (
								console.warn(
									"TrustedTypes policy " + H + " could not be created.",
								),
								null
							);
						}
					},
					Fb = function () {
						return {
							afterSanitizeAttributes: [],
							afterSanitizeElements: [],
							afterSanitizeShadowDOM: [],
							beforeSanitizeAttributes: [],
							beforeSanitizeElements: [],
							beforeSanitizeShadowDOM: [],
							uponSanitizeAttribute: [],
							uponSanitizeElement: [],
							uponSanitizeShadowNode: [],
						};
					};
				return q();
			});
		},
		function (t, u, n) {
			n.d(u, "a", function () {
				return h;
			});
			var k = n(1),
				g = n(7),
				r = n(10),
				d = n(5),
				q = (function () {
					function m(p) {
						var x = this;
						this.promise = p.then(function (B) {
							x.response = B;
							x.status = 200;
						});
					}
					m.prototype.addEventListener = function (p, x) {
						this.promise.then(x);
					};
					return m;
				})(),
				h = function (m, p, x, B) {
					if (Object(r.a)() && !x) {
						self.Module.instantiateWasm = function (L, y) {
							return Object(g.a)(
								"".concat(m, "Wasm.wasm"),
								L,
								p["Wasm.wasm"],
								B,
							).then(function (Q) {
								y(Q.instance);
							});
						};
						if (p.disableObjectURLBlobs) {
							importScripts("".concat(m, "Wasm.js"));
							return;
						}
						x = Object(k.b)(
							"".concat(m, "Wasm.js.mem"),
							p["Wasm.js.mem"],
							!1,
							!1,
						);
					} else {
						if (p.disableObjectURLBlobs) {
							importScripts(
								"".concat(
									(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + m,
									".js",
								),
							);
							return;
						}
						x = Object(k.b)(
							"".concat(
								(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + m,
								".js.mem",
							),
							p[".js.mem"],
							!1,
						);
						var A = Object(k.b)(
							"".concat(
								(self.Module.memoryInitializerPrefixURL
									? self.Module.memoryInitializerPrefixURL
									: "") + m,
								".mem",
							),
							p[".mem"],
							!0,
							!0,
						);
						self.Module.memoryInitializerRequest = new q(A);
					}
					x = new Blob([x], { type: "application/javascript" });
					importScripts(Object(d.a)(URL.createObjectURL(x)));
				};
		},
		function (t, u, n) {
			n.d(u, "a", function () {
				return L;
			});
			var k,
				g = "undefined" === typeof window ? self : window;
			t = (function () {
				var y = navigator.userAgent.toLowerCase();
				return (y =
					/(msie) ([\w.]+)/.exec(y) || /(trident)(?:.*? rv:([\w.]+)|)/.exec(y))
					? parseInt(y[2], 10)
					: y;
			})();
			var r = (function () {
				var y = g.navigator.userAgent.match(/OPR/),
					Q = g.navigator.userAgent.match(/Maxthon/),
					ea = g.navigator.userAgent.match(/Edge/);
				return g.navigator.userAgent.match(/Chrome\/(.*?) /) && !y && !Q && !ea;
			})();
			(function () {
				if (!r) return null;
				var y = g.navigator.userAgent.match(/Chrome\/([0-9]+)\./);
				return y ? parseInt(y[1], 10) : y;
			})();
			var d =
				!!navigator.userAgent.match(/Edge/i) ||
				(navigator.userAgent.match(/Edg\/(.*?)/) &&
					g.navigator.userAgent.match(/Chrome\/(.*?) /));
			(function () {
				if (!d) return null;
				var y = g.navigator.userAgent.match(/Edg\/([0-9]+)\./);
				return y ? parseInt(y[1], 10) : y;
			})();
			u =
				/iPad|iPhone|iPod/.test(g.navigator.platform) ||
				("MacIntel" === navigator.platform && 1 < navigator.maxTouchPoints) ||
				/iPad|iPhone|iPod/.test(g.navigator.userAgent);
			var q = (function () {
					var y = g.navigator.userAgent.match(
						/.*\/([0-9\.]+)\s(Safari|Mobile).*/i,
					);
					return y ? parseFloat(y[1]) : y;
				})(),
				h =
					/^((?!chrome|android).)*safari/i.test(g.navigator.userAgent) ||
					(/^((?!chrome|android).)*$/.test(g.navigator.userAgent) && u);
			h &&
				/^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
				parseInt(
					null === (k = navigator.userAgent.match(/Version\/(\d+)/)) ||
						void 0 === k
						? void 0
						: k[1],
					10,
				);
			var m = g.navigator.userAgent.match(/Firefox/);
			(function () {
				if (!m) return null;
				var y = g.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
				return y ? parseInt(y[1], 10) : y;
			})();
			t || /Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);
			navigator.userAgent.match(/(iPad|iPhone|iPod)/i);
			g.navigator.userAgent.indexOf("Android");
			var p = /Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(
					g.navigator.userAgent,
				),
				x = g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)
					? 14 <=
						parseInt(
							g.navigator.userAgent.match(
								/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i,
							)[3],
							10,
						)
					: !1,
				B = !(!self.WebAssembly || !self.WebAssembly.validate),
				A =
					-1 < g.navigator.userAgent.indexOf("Edge/16") ||
					-1 < g.navigator.userAgent.indexOf("MSAppHost"),
				L = function () {
					var y;
					if ((y = B && !A))
						(y = h && null !== q && 14 > q), (y = !(!x && (y || p)));
					return y;
				};
		},
		function (t, u, n) {
			t.exports = n(12);
		},
		function (t, u, n) {
			n.r(u);
			(function (k) {
				function g(w) {
					return w.split("<File>")[1].split("</File>")[0];
				}
				function r(w, e, C) {
					var D =
						3 < arguments.length && void 0 !== arguments[3]
							? arguments[3]
							: null;
					w = "<InfixServer>".concat(w, "</InfixServer>");
					null == D && (D = "importCommand" + C + ".xml");
					FS.writeFile(D, w);
					ha.ccall("wasmRunXML", "number", ["string", "string"], [D, e]);
					FS.unlink(D);
				}
				function d(w) {
					var e =
							1 < arguments.length && void 0 !== arguments[1]
								? arguments[1]
								: !0,
						C = FS.readFile(w).buffer;
					e && FS.unlink(w);
					C || console.warn("No buffer results found for: ".concat(w));
					return C;
				}
				function q(w) {
					1 == ea
						? postMessage({ cmd: "isReady", taskId: w })
						: setTimeout(function () {
								return q(w);
							}, 300);
				}
				function h(w, e) {
					var C = w.galleyId,
						D = w.cmd,
						I = w.subCmd,
						O = w.caretStart,
						U = w.caretEnd,
						W = w.resultsFile,
						R = w.commandXML,
						ia = w.commandFile,
						sa = w.pageNumber;
					w = w.taskId;
					if ("editText" === D) var oa = I;
					else if ("performUndoRedo" === D || "transformTextBox" === D) oa = D;
					r(R, W, 0, ia);
					D = d(W);
					postMessage(
						{
							cmd: "editText",
							subCmd: oa,
							caretStart: O,
							caretEnd: U,
							galleyId: C,
							commandXML: !1 === e ? null : R,
							resultsXML: D,
							pageNumber: sa,
							taskId: w,
						},
						[D],
					);
				}
				function m(w, e, C, D) {
					r(w, C, 0, e);
					e = d(C);
					D || (w = null);
					postMessage({ cmd: "insertTextBox", commandXML: w, resultsXML: e }, [
						e,
					]);
				}
				function p(w, e) {
					var C = w.galleyId,
						D = w.resultsFile,
						I = w.willTriggerTextContentUpdated,
						O = w.pageNumber;
					r(w.commandXML, D, 0, w.commandFile);
					D = d(D);
					postMessage(
						{
							pageNumber: O,
							cmd: e,
							resultsXML: D,
							galleyId: C,
							willTriggerTextContentUpdated: I,
							taskId: w.taskId,
						},
						[D],
					);
				}
				function x(w, e, C, D, I, O) {
					if (e) {
						C = new Uint8Array(C);
						e = "inputFile" + w + ".pdf";
						FS.writeFile(e, C);
						D = new Uint8Array(D);
						var U = new TextDecoder("utf-8").decode(D);
						C = "objects" + w + ".xml";
						D = "results" + w + ".xml";
						U = '\n  <Commands>\n    <Command Name="LoadPDF"><File>'
							.concat(
								e,
								'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    ',
							)
							.concat(
								U
									? '<Command Name="AddTableBoxes">'.concat(U, "</Command>")
									: "",
								'\n    <Command Name="Edit Page">\n      <WebFontURL>',
							)
							.concat(
								qa || "https://www.pdftron.com/webfonts/v2/",
								"</WebFontURL>\n      <Output>",
							)
							.concat(
								C,
								"</Output>\n      <ImagesAndText/>\n      <IgnoreRotation>false</IgnoreRotation>\n      ",
							)
							.concat(
								Q ? "<ForceReflow/>" : "",
								"\n    </Command>\n  </Commands>",
							);
						r(U, D, 1);
						M = w;
						I &&
							((I = d(e, !1)),
							(e = d(C)),
							(D = d(D)),
							postMessage(
								{
									cmd: "exportFile",
									pageNumber: w,
									exportPerformed: !0,
									pdfBuffer: I,
									objectXML: e,
									resultsXML: D,
									taskId: O,
								},
								[I, D],
							));
					} else
						postMessage({
							cmd: "exportFile",
							pageNumber: w,
							exportPerformed: !1,
						});
				}
				function B(w) {
					var e = w.pdfFile,
						C = w.tableData,
						D = w.subCmd,
						I = w.pageNumber,
						O = w.commandXML,
						U = w.objectID,
						W = w.isText,
						R = w.isUpdatingRect,
						ia = w.oid,
						sa = w.canUndoRedo,
						oa = w.outputFName;
					w = w.taskId;
					I !== M && x(I, !0, e, C, !1);
					var a = "results" + I + ".xml";
					e = W ? "transformTextBox" : "transformObject";
					r(O, a, I);
					C = d(oa);
					a = d(a);
					postMessage(
						{
							cmd: e,
							pageNumber: I,
							pdfBuffer: C,
							resultsXML: a,
							id: U,
							isUpdatingRect: R,
							isText: W,
							commandXML: O,
							subCmd: D,
							canUndoRedo: sa,
							oid: ia,
							outputFName: oa,
							taskId: w,
						},
						[C, a],
					);
				}
				n.d(u, "extractFileNameFromCommand", function () {
					return g;
				});
				var A = n(9),
					L = n(1),
					y = "undefined" === typeof window ? self : window;
				y.Core = y.Core || {};
				var Q = !0,
					ea = !1,
					la = null,
					M = -1,
					Z = !1,
					qa,
					ha = {
						noInitialRun: !0,
						onRuntimeInitialized: function () {
							ea = !0;
						},
						fetchSelf: function () {
							Object(A.a)(
								"InfixServer",
								{
									"Wasm.wasm": 1e8,
									"Wasm.js.mem": 1e5,
									".js.mem": 5e6,
									".mem": 3e6,
									disableObjectURLBlobs: Z,
								},
								!!navigator.userAgent.match(/Edge/i),
							);
						},
						locateFile: function (w) {
							return w;
						},
						getPreloadedPackage: function (w, e) {
							"InfixServerWasm.br.mem" == w && (w = "InfixServerWasm.mem");
							return Object(L.b)("".concat(la || "").concat(w), e, !1, !0)
								.buffer;
						},
					};
				self.Module = ha;
				self.basePath = "../external/";
				onmessage = function (w) {
					var e = w.data;
					switch (e.cmd) {
						case "isReady":
							la = e.resourcePath;
							ha.fetchSelf();
							q(e.taskId);
							break;
						case "initialiseInfixServer":
							var C = e.l,
								D = e.taskId;
							ha.callMain([""]);
							ha.ccall(
								"wasmInitInfixServer",
								"number",
								["string", "string", "string"],
								["infixcore.cfg", C, "results.xml"],
							);
							var I = d("results.xml");
							postMessage(
								{ cmd: "initialiseInfixServer", resultsXML: I, taskId: D },
								[I],
							);
							break;
						case "disableObjectURLBlobs":
							Z = e.disableURLBlobs;
							break;
						case "loadAvailableFonts":
							var O = e.commandXML,
								U = e.taskId;
							qa = e.webFontURL;
							r(O, "results0.xml", 0);
							var W = d("results0.xml");
							postMessage(
								{ cmd: "loadAvailableFonts", resultsXML: W, taskId: U },
								[W],
							);
							break;
						case "exportFile":
							x(
								e.pageNumber,
								e.performExport,
								e.pdfFile,
								e.tableData,
								!0,
								e.taskId,
							);
							break;
						case "applyTransformMatrix":
							var R = e.oid,
								ia = e.pageNumber,
								sa = e.taskId;
							r(e.commandXML, "results" + ia + ".xml", ia);
							postMessage({
								cmd: "applyTransformMatrix",
								pageNumber: ia,
								id: R,
								taskId: sa,
							});
							break;
						case "transformObject":
							B(e);
							break;
						case "deleteObject":
							var oa = e.pdfFile,
								a = e.pageNumber,
								b = e.objectID,
								c = e.tableData,
								f = e.isUndoRedo,
								v = e.isPageDeleted,
								F = e.taskId;
							a !== M && x(a, !0, oa, c, !1);
							var T = "outputFile" + a + ".pdf",
								V = "results" + a + ".xml";
							var fa =
								'<Commands><Command Name="DeleteObject">' +
								"<OID>".concat(b, "</OID></Command>");
							fa += '<Command Name="SavePDF"><File>'.concat(T, "</File>");
							r(fa + "</Command></Commands>", V, a);
							var va = d(T),
								Sa = d(V);
							postMessage(
								{
									cmd: "deleteObject",
									pageNumber: a,
									pdfBuffer: va,
									resultsXML: Sa,
									oid: b,
									isUndoRedo: f,
									isPageDeleted: v,
									taskId: F,
								},
								[va, Sa],
							);
							break;
						case "insertTextBox":
							m(e.commandXML, e.commandFile, e.resultsFile, !0);
							break;
						case "insertNewTextBox":
							var hc = e.pdfFile,
								wa = e.pageNumber,
								Ob = e.topVal,
								ic = e.leftVal,
								jc = e.bottomVal,
								Ub = e.rightVal,
								Gb = e.font,
								Ka = e.fontSize,
								fc = e.importData,
								Fb = e.content,
								N = e.canUndoRedo,
								H = e.taskId,
								K = new TextEncoder().encode("").buffer;
							wa != M && x(wa, !0, hc, K, !1);
							var P = "results" + wa + ".xml",
								S = "exported" + wa + ".xml",
								na = "outputFile" + wa + ".pdf";
							var ra =
								'<Commands><Command Name="Insert Text Box">' +
								"<Rect><Top>".concat(Ob, "</Top><Left>").concat(ic, "</Left>");
							ra += "<Bottom>"
								.concat(jc, "</Bottom><Right>")
								.concat(Ub, "</Right></Rect>");
							ra += "<Size>"
								.concat(Ka, "</Size><FontName>")
								.concat(Gb, "</FontName>");
							var Y = "editText" + wa + ".xml";
							FS.writeFile(Y, fc);
							ra += "<File>".concat(
								Y,
								"</File><TransXML>coreTransXML.cfg</TransXML>",
							);
							ra += "<ExportFile>".concat(
								S,
								"</ExportFile><TransXML>coreTransXML.cfg</TransXML>",
							);
							ra =
								ra +
								'<StartPage>1</StartPage><EndPage>LastPage</EndPage><AutoSubstitute/><AutoDeleteParas/><Fitting><Shrink><FontSize Min="0.65">true</FontSize><Leading>False</Leading></Shrink><Stretch><FontSize>False</FontSize><Leading>False</Leading></Stretch></Fitting><ResetLetterSpacing/><IgnoreFlightCheck/><MissingFont>Noto Sans Regular</MissingFont><SubstituteAllChars/><TargetLang>en</TargetLang></Command>' +
								'<Command Name="SavePDF"><File>'.concat(
									na,
									"</File></Command></Commands>",
								);
							r(ra, P, wa);
							var Ta = d(na),
								hb = d(P),
								Ba = d(S);
							postMessage(
								{
									cmd: "insertNewTextBox",
									pageNumber: wa,
									pdfBuffer: Ta,
									exportXML: Ba,
									resultsXML: hb,
									contentHTML: Fb,
									commandXML: ra,
									canUndoRedo: N,
									taskId: H,
								},
								[Ta, Ba, hb],
							);
							break;
						case "AlignContentBox":
							var Vb = e.pdfFile,
								Ua = e.pageNumber,
								Wb = e.galleyId,
								Va = e.tableData,
								ib = e.commandXML,
								Xb = e.taskId;
							Ua != M && x(Ua, !0, Vb, Va, !1);
							var Yb = g(ib);
							r(ib, "results.xml", Ua);
							var Db = d("results.xml"),
								La = d(Yb);
							postMessage(
								{
									cmd: "updateContentBox",
									subCmd: "Set Para Attribs",
									pageNumber: Ua,
									pdfBuffer: La,
									resultsXML: Db,
									galleyId: Wb,
									commandXML: ib,
									taskId: Xb,
								},
								[La, Db],
							);
							break;
						case "RenderContentBox":
							var ka = e.pdfFile,
								ta = e.pageNumber,
								jb = e.galleyId,
								Zb = e.tableData,
								Eb = e.commandXML,
								$b = e.taskId;
							ta != M && x(ta, !0, ka, Zb, !1);
							var ac = g(Eb);
							r(Eb, "results.xml", ta);
							var ja = d(ac),
								Wa = d("results.xml");
							postMessage(
								{
									cmd: "renderContentBox",
									pageNumber: ta,
									pdfBuffer: ja,
									resultsXML: Wa,
									galleyId: jb,
									taskId: $b,
								},
								[ja, Wa],
							);
							break;
						case "AlignParagraph":
							var kb = e.pdfFile,
								Ca = e.pageNumber,
								bc = e.galleyId,
								cc = e.tableData,
								Hb = e.commandXML,
								Ib = e.taskId;
							Ca != M && x(Ca, !0, kb, cc, !1);
							r(Hb, "results.xml", Ca);
							var lb = d("results.xml");
							postMessage(
								{
									cmd: "editText",
									subCmd: "Set Para Attribs",
									galleyId: bc,
									commandXML: Hb,
									pageNumber: Ca,
									taskId: Ib,
									resultsXML: lb,
								},
								[lb],
							);
							break;
						case "DecorateContentBox":
							var Xa = e.commandXML,
								ca = e.pdfFile,
								Ma = e.pageNumber,
								da = e.galleyId,
								Jb = e.tableData,
								X = e.taskId,
								Na = e.subCmd;
							Ma != M && x(Ma, !0, ca, Jb, !1);
							var mb = g(Xa);
							r(Xa, "results.xml", Ma);
							var nb = d("results.xml"),
								Ya = d(mb);
							postMessage(
								{
									cmd: "updateContentBox",
									pageNumber: Ma,
									pdfBuffer: Ya,
									commandXML: Xa,
									resultsXML: nb,
									subCmd: Na,
									id: da,
									galleyId: da,
									taskId: X,
								},
								[Ya, nb],
							);
							break;
						case "insertImage":
							var Kb = e.pdfFile,
								Da = e.pageNumber,
								ua = e.newImage,
								ob = e.canUndoRedo,
								ya = e.taskId,
								Za = e.commandXML,
								pb = e.imageFileName,
								Ea = e.outputFileName,
								$a = new TextEncoder().encode("").buffer;
							Da != M && x(Da, !0, Kb, $a, !1);
							var Oa = "results" + Da + ".xml";
							FS.writeFile(pb, k.from(ua));
							r(Za, Oa, Da);
							var qb = d(Ea),
								rb = d(Oa);
							postMessage(
								{
									cmd: "insertImage",
									pageNumber: Da,
									pdfBuffer: qb,
									resultsXML: rb,
									commandXML: Za,
									canUndoRedo: ob,
									newImage: ua,
									taskId: ya,
								},
								[qb, rb, ua],
							);
							break;
						case "runCommand":
							var sb = e.subCmd,
								Fa = e.resultsFile;
							r(e.commandXML, Fa, 0, e.commandFile);
							var za = d(Fa);
							postMessage({ cmd: "runCommand", subCmd: sb, resultsXML: za }, [
								za,
							]);
							break;
						case "renderEditGalley":
							var Aa = e.resultsFile;
							r(e.commandXML, Aa, 0, e.commandFile);
							var tb = d(Aa),
								ub = d(e.imageFName);
							postMessage(
								{
									cmd: "renderEditGalley",
									resultsXML: tb,
									imageData: ub,
									galleyId: e.galleyId,
									taskId: e.taskId,
								},
								[tb, ub],
							);
							break;
						case "renderFullPage":
							var vb = e.resultsFile;
							r(e.commandXML, vb, 0, e.commandFile);
							var ab = d(vb),
								wb = d(e.imageFName);
							postMessage(
								{
									cmd: "renderFullPage",
									resultsXML: ab,
									imageData: wb,
									outputWidth: e.width,
									outputHeight: e.height,
								},
								[ab, wb],
							);
							break;
						case "textAttributes":
							var Ga = e.id,
								Lb = e.numChars,
								bb = e.resultsFile,
								dc = e.taskId;
							r(e.commandXML, bb, 0, e.commandFile);
							var Pa = d(bb);
							postMessage(
								{
									cmd: "textAttributes",
									id: Ga,
									numChars: Lb,
									resultsXML: Pa,
									taskId: dc,
								},
								[Pa],
							);
							break;
						case "editText":
							h(e, !0);
							break;
						case "editObject":
							var cb = e.subCmd,
								ec = e.oid,
								Ha = e.resultsFile,
								Mb = e.commandXML;
							r(Mb, Ha, 0, e.commandFile);
							var aa = d(Ha);
							postMessage(
								{
									cmd: "editObject",
									subCmd: cb,
									oid: ec,
									commandXML: Mb,
									resultsXML: aa,
								},
								[aa],
							);
							break;
						case "performUndoRedo":
							switch (e.subCmd) {
								case "editText":
									h(e, !1);
									break;
								case "transformObject":
									e.subCmd = "performUndoRedo";
									B(e);
									break;
								case "insertTextBoxRedo":
									var xa = e.commandXML,
										Qa = e.pageNumber,
										Nb = e.taskId,
										db = "results" + Qa + ".xml",
										Pb = "exported" + Qa + ".xml",
										Qb = "outputFile" + Qa + ".pdf";
									r(xa, db, Qa);
									var Rb = d(Qb),
										pa = d(db),
										Ra = d(Pb);
									postMessage(
										{
											cmd: "insertNewTextBox",
											subCmd: "performUndoRedo",
											pageNumber: Qa,
											pdfBuffer: Rb,
											exportXML: Ra,
											resultsXML: pa,
											commandXML: xa,
											taskId: Nb,
										},
										[Rb, Ra, pa],
									);
									break;
								case "insertImageRedo":
									var xb = e.commandXML,
										Ia = e.pageNumber,
										eb = e.newImage,
										Sb = "outputFile" + Ia + ".pdf",
										yb = "results" + Ia + ".xml",
										zb = "imageFile" + Ia + ".jpg";
									FS.writeFile(zb, k.from(eb));
									r(xb, yb, Ia);
									var Ab = d(Sb),
										Tb = d(yb);
									FS.unlink(zb);
									postMessage(
										{
											cmd: "insertImage",
											pageNumber: Ia,
											pdfBuffer: Ab,
											resultsXML: Tb,
											commandXML: xb,
											newImage: eb,
										},
										[Ab, Tb],
									);
							}
							break;
						case "insertTextBoxRedo":
							m(e.commandXML, e.commandFile, e.resultsFile, !1);
							break;
						case "copyText":
							p(e, "copyText");
							break;
						case "getUpdatedText":
							p(e, "getUpdatedText");
							break;
						case "dumpTextBox":
							var l = e.galleyId,
								z = e.resultsFile,
								E = e.taskId;
							r(e.commandXML, z, 0, e.commandFile);
							var G = d(z);
							postMessage(
								{ cmd: "dumpTextBox", galleyId: l, resultsXML: G, taskId: E },
								[G],
							);
							break;
						case "transformTextBox":
							h(e, !1);
							break;
						case "savePDF":
							var J = e.resultsFile,
								ba = e.pdfFileName,
								ma = new Uint8Array(e.pdfFile);
							FS.writeFile(e.pdfFileName, ma);
							r(e.commandXML, J, 0, e.commandFile);
							var fb = d(ba),
								Ja = d(J);
							postMessage({ cmd: "savePDF", pdfBuffer: fb, resultsXML: Ja }, [
								fb,
								Ja,
							]);
							break;
						case "loadPDF":
							var gb = e.resultsFile,
								gc = new Uint8Array(e.pdfFile);
							FS.writeFile(e.pdfFileName, gc);
							r(e.commandXML, gb, 0, e.commandFile);
							var nc = d(gb);
							postMessage({ cmd: "loadPDF", resultsXML: nc }, [nc]);
							break;
						case "loadHyperlinkURL":
							var Ac = e.id,
								kc = e.resultsFile,
								Bc = e.taskId;
							r(e.commandXML, kc, 0, e.commandFile);
							var oc = FS.readFile(kc).buffer;
							FS.unlink(kc);
							postMessage(
								{ id: Ac, cmd: "loadHyperlinkURL", resultsXML: oc, taskId: Bc },
								[oc],
							);
							break;
						case "setTypographyContentBox":
							var Cc = e.pdfFile,
								Bb = e.pageNumber,
								pc = e.galleyId,
								Dc = e.subCmd,
								Ec = e.tableData,
								Fc = e.taskId,
								lc = e.commandXML;
							Bb != M && x(Bb, !0, Cc, Ec, !1);
							var qc = "results" + Bb + ".xml";
							r(lc, qc, Bb);
							var Gc = g(lc),
								rc = d(Gc),
								sc = d(qc);
							postMessage(
								{
									cmd: "setTypographyContentBox",
									subCmd: Dc,
									pageNumber: Bb,
									pdfBuffer: rc,
									commandXML: lc,
									resultsXML: sc,
									id: pc,
									galleyId: pc,
									taskId: Fc,
								},
								[rc, sc],
							);
							break;
						case "updateDocumentContent":
							var Cb = e.pageNumber,
								Hc = e.galleyId,
								Ic = e.outputFileName,
								tc = e.commandXML,
								Jc = e.isSearchReplace,
								Kc = e.callbackMapId,
								Lc = e.pdfPage,
								Mc = e.tableArray,
								Nc = e.taskId;
							Cb != M && x(Cb, !0, Lc, Mc, !1);
							var uc = "results" + Cb + ".xml";
							r(tc, uc, Cb);
							var vc = d(Ic),
								wc = d(uc);
							postMessage(
								{
									cmd: "updateContentBox",
									pageNumber: Cb,
									pdfBuffer: vc,
									commandXML: tc,
									resultsXML: wc,
									galleyId: Hc,
									callbackMapId: Kc,
									isSearchReplace: Jc,
									taskId: Nc,
								},
								[vc, wc],
							);
							break;
						case "getInfixVersion":
							var Oc = e.taskId,
								xc = e.commandXML;
							r(xc, "results1.xml", 1);
							var yc = d("results1.xml");
							postMessage(
								{
									cmd: "getInfixVersion",
									commandXML: xc,
									resultsXML: yc,
									taskId: Oc,
								},
								[yc],
							);
							break;
						case "reloadPage":
							var mc = e.pageNumber,
								Pc = new Uint8Array(e.pdfFile),
								zc = "inputFile" + mc + ".pdf";
							FS.writeFile(zc, Pc);
							var Qc = "objects" + mc + ".xml",
								Rc = "results" + mc + ".xml",
								Sc = '\n  <Commands>\n    <Command Name="LoadPDF"><File>'
									.concat(
										zc,
										'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    <Command Name="Edit Page">\n    <Output>',
									)
									.concat(Qc, "</Output><ImagesOnly/>")
									.concat(
										Q ? "<ForceReflow/>" : "",
										"</Command>\n  </Commands>",
									);
							r(Sc, Rc, 1);
							break;
						case "setTextReflow":
							Q = e.textReflow;
							postMessage({ taskId: e.taskId });
							break;
						case "getTextReflow":
							postMessage({ taskId: e.taskId, textReflow: Q });
					}
				};
			}).call(this, n(13).Buffer);
		},
		function (t, u, n) {
			(function (k) {
				function g() {
					try {
						var a = new Uint8Array(1);
						a.__proto__ = {
							__proto__: Uint8Array.prototype,
							foo: function () {
								return 42;
							},
						};
						return (
							42 === a.foo() &&
							"function" === typeof a.subarray &&
							0 === a.subarray(1, 1).byteLength
						);
					} catch (b) {
						return !1;
					}
				}
				function r(a, b) {
					if ((d.TYPED_ARRAY_SUPPORT ? 2147483647 : 1073741823) < b)
						throw new RangeError("Invalid typed array length");
					d.TYPED_ARRAY_SUPPORT
						? ((a = new Uint8Array(b)), (a.__proto__ = d.prototype))
						: (null === a && (a = new d(b)), (a.length = b));
					return a;
				}
				function d(a, b, c) {
					if (!(d.TYPED_ARRAY_SUPPORT || this instanceof d))
						return new d(a, b, c);
					if ("number" === typeof a) {
						if ("string" === typeof b)
							throw Error(
								"If encoding is specified then the first argument must be a string",
							);
						return m(this, a);
					}
					return q(this, a, b, c);
				}
				function q(a, b, c, f) {
					if ("number" === typeof b)
						throw new TypeError('"value" argument must not be a number');
					if ("undefined" !== typeof ArrayBuffer && b instanceof ArrayBuffer) {
						b.byteLength;
						if (0 > c || b.byteLength < c)
							throw new RangeError("'offset' is out of bounds");
						if (b.byteLength < c + (f || 0))
							throw new RangeError("'length' is out of bounds");
						b =
							void 0 === c && void 0 === f
								? new Uint8Array(b)
								: void 0 === f
									? new Uint8Array(b, c)
									: new Uint8Array(b, c, f);
						d.TYPED_ARRAY_SUPPORT
							? ((a = b), (a.__proto__ = d.prototype))
							: (a = p(a, b));
						return a;
					}
					if ("string" === typeof b) {
						f = a;
						a = c;
						if ("string" !== typeof a || "" === a) a = "utf8";
						if (!d.isEncoding(a))
							throw new TypeError('"encoding" must be a valid string encoding');
						c = A(b, a) | 0;
						f = r(f, c);
						b = f.write(b, a);
						b !== c && (f = f.slice(0, b));
						return f;
					}
					return x(a, b);
				}
				function h(a) {
					if ("number" !== typeof a)
						throw new TypeError('"size" argument must be a number');
					if (0 > a)
						throw new RangeError('"size" argument must not be negative');
				}
				function m(a, b) {
					h(b);
					a = r(a, 0 > b ? 0 : B(b) | 0);
					if (!d.TYPED_ARRAY_SUPPORT) for (var c = 0; c < b; ++c) a[c] = 0;
					return a;
				}
				function p(a, b) {
					var c = 0 > b.length ? 0 : B(b.length) | 0;
					a = r(a, c);
					for (var f = 0; f < c; f += 1) a[f] = b[f] & 255;
					return a;
				}
				function x(a, b) {
					if (d.isBuffer(b)) {
						var c = B(b.length) | 0;
						a = r(a, c);
						if (0 === a.length) return a;
						b.copy(a, 0, 0, c);
						return a;
					}
					if (b) {
						if (
							("undefined" !== typeof ArrayBuffer &&
								b.buffer instanceof ArrayBuffer) ||
							"length" in b
						)
							return (
								(c = "number" !== typeof b.length) ||
									((c = b.length), (c = c !== c)),
								c ? r(a, 0) : p(a, b)
							);
						if ("Buffer" === b.type && ia(b.data)) return p(a, b.data);
					}
					throw new TypeError(
						"First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.",
					);
				}
				function B(a) {
					if (a >= (d.TYPED_ARRAY_SUPPORT ? 2147483647 : 1073741823))
						throw new RangeError(
							"Attempt to allocate Buffer larger than maximum size: 0x" +
								(d.TYPED_ARRAY_SUPPORT ? 2147483647 : 1073741823).toString(16) +
								" bytes",
						);
					return a | 0;
				}
				function A(a, b) {
					if (d.isBuffer(a)) return a.length;
					if (
						"undefined" !== typeof ArrayBuffer &&
						"function" === typeof ArrayBuffer.isView &&
						(ArrayBuffer.isView(a) || a instanceof ArrayBuffer)
					)
						return a.byteLength;
					"string" !== typeof a && (a = "" + a);
					var c = a.length;
					if (0 === c) return 0;
					for (var f = !1; ; )
						switch (b) {
							case "ascii":
							case "latin1":
							case "binary":
								return c;
							case "utf8":
							case "utf-8":
							case void 0:
								return D(a).length;
							case "ucs2":
							case "ucs-2":
							case "utf16le":
							case "utf-16le":
								return 2 * c;
							case "hex":
								return c >>> 1;
							case "base64":
								return O(a).length;
							default:
								if (f) return D(a).length;
								b = ("" + b).toLowerCase();
								f = !0;
						}
				}
				function L(a, b, c) {
					var f = !1;
					if (void 0 === b || 0 > b) b = 0;
					if (b > this.length) return "";
					if (void 0 === c || c > this.length) c = this.length;
					if (0 >= c) return "";
					c >>>= 0;
					b >>>= 0;
					if (c <= b) return "";
					for (a || (a = "utf8"); ; )
						switch (a) {
							case "hex":
								a = b;
								b = c;
								c = this.length;
								if (!a || 0 > a) a = 0;
								if (!b || 0 > b || b > c) b = c;
								f = "";
								for (c = a; c < b; ++c)
									(a = f),
										(f = this[c]),
										(f = 16 > f ? "0" + f.toString(16) : f.toString(16)),
										(f = a + f);
								return f;
							case "utf8":
							case "utf-8":
								return la(this, b, c);
							case "ascii":
								a = "";
								for (c = Math.min(this.length, c); b < c; ++b)
									a += String.fromCharCode(this[b] & 127);
								return a;
							case "latin1":
							case "binary":
								a = "";
								for (c = Math.min(this.length, c); b < c; ++b)
									a += String.fromCharCode(this[b]);
								return a;
							case "base64":
								return (
									(b =
										0 === b && c === this.length
											? W.fromByteArray(this)
											: W.fromByteArray(this.slice(b, c))),
									b
								);
							case "ucs2":
							case "ucs-2":
							case "utf16le":
							case "utf-16le":
								b = this.slice(b, c);
								c = "";
								for (a = 0; a < b.length; a += 2)
									c += String.fromCharCode(b[a] + 256 * b[a + 1]);
								return c;
							default:
								if (f) throw new TypeError("Unknown encoding: " + a);
								a = (a + "").toLowerCase();
								f = !0;
						}
				}
				function y(a, b, c) {
					var f = a[b];
					a[b] = a[c];
					a[c] = f;
				}
				function Q(a, b, c, f, v) {
					if (0 === a.length) return -1;
					"string" === typeof c
						? ((f = c), (c = 0))
						: 2147483647 < c
							? (c = 2147483647)
							: -2147483648 > c && (c = -2147483648);
					c = +c;
					isNaN(c) && (c = v ? 0 : a.length - 1);
					0 > c && (c = a.length + c);
					if (c >= a.length) {
						if (v) return -1;
						c = a.length - 1;
					} else if (0 > c)
						if (v) c = 0;
						else return -1;
					"string" === typeof b && (b = d.from(b, f));
					if (d.isBuffer(b)) return 0 === b.length ? -1 : ea(a, b, c, f, v);
					if ("number" === typeof b)
						return (
							(b &= 255),
							d.TYPED_ARRAY_SUPPORT &&
							"function" === typeof Uint8Array.prototype.indexOf
								? v
									? Uint8Array.prototype.indexOf.call(a, b, c)
									: Uint8Array.prototype.lastIndexOf.call(a, b, c)
								: ea(a, [b], c, f, v)
						);
					throw new TypeError("val must be string, number or Buffer");
				}
				function ea(a, b, c, f, v) {
					function F(va, Sa) {
						return 1 === T ? va[Sa] : va.readUInt16BE(Sa * T);
					}
					var T = 1,
						V = a.length,
						fa = b.length;
					if (
						void 0 !== f &&
						((f = String(f).toLowerCase()),
						"ucs2" === f ||
							"ucs-2" === f ||
							"utf16le" === f ||
							"utf-16le" === f)
					) {
						if (2 > a.length || 2 > b.length) return -1;
						T = 2;
						V /= 2;
						fa /= 2;
						c /= 2;
					}
					if (v)
						for (f = -1; c < V; c++)
							if (F(a, c) === F(b, -1 === f ? 0 : c - f)) {
								if ((-1 === f && (f = c), c - f + 1 === fa)) return f * T;
							} else -1 !== f && (c -= c - f), (f = -1);
					else
						for (c + fa > V && (c = V - fa); 0 <= c; c--) {
							V = !0;
							for (f = 0; f < fa; f++)
								if (F(a, c + f) !== F(b, f)) {
									V = !1;
									break;
								}
							if (V) return c;
						}
					return -1;
				}
				function la(a, b, c) {
					c = Math.min(a.length, c);
					for (var f = []; b < c; ) {
						var v = a[b],
							F = null,
							T = 239 < v ? 4 : 223 < v ? 3 : 191 < v ? 2 : 1;
						if (b + T <= c)
							switch (T) {
								case 1:
									128 > v && (F = v);
									break;
								case 2:
									var V = a[b + 1];
									128 === (V & 192) &&
										((v = ((v & 31) << 6) | (V & 63)), 127 < v && (F = v));
									break;
								case 3:
									V = a[b + 1];
									var fa = a[b + 2];
									128 === (V & 192) &&
										128 === (fa & 192) &&
										((v = ((v & 15) << 12) | ((V & 63) << 6) | (fa & 63)),
										2047 < v && (55296 > v || 57343 < v) && (F = v));
									break;
								case 4:
									V = a[b + 1];
									fa = a[b + 2];
									var va = a[b + 3];
									128 === (V & 192) &&
										128 === (fa & 192) &&
										128 === (va & 192) &&
										((v =
											((v & 15) << 18) |
											((V & 63) << 12) |
											((fa & 63) << 6) |
											(va & 63)),
										65535 < v && 1114112 > v && (F = v));
							}
						null === F
							? ((F = 65533), (T = 1))
							: 65535 < F &&
								((F -= 65536),
								f.push(((F >>> 10) & 1023) | 55296),
								(F = 56320 | (F & 1023)));
						f.push(F);
						b += T;
					}
					a = f.length;
					if (a <= sa) f = String.fromCharCode.apply(String, f);
					else {
						c = "";
						for (b = 0; b < a; )
							c += String.fromCharCode.apply(String, f.slice(b, (b += sa)));
						f = c;
					}
					return f;
				}
				function M(a, b, c) {
					if (0 !== a % 1 || 0 > a) throw new RangeError("offset is not uint");
					if (a + b > c)
						throw new RangeError("Trying to access beyond buffer length");
				}
				function Z(a, b, c, f, v, F) {
					if (!d.isBuffer(a))
						throw new TypeError('"buffer" argument must be a Buffer instance');
					if (b > v || b < F)
						throw new RangeError('"value" argument is out of bounds');
					if (c + f > a.length) throw new RangeError("Index out of range");
				}
				function qa(a, b, c, f) {
					0 > b && (b = 65535 + b + 1);
					for (var v = 0, F = Math.min(a.length - c, 2); v < F; ++v)
						a[c + v] =
							(b & (255 << (8 * (f ? v : 1 - v)))) >>> (8 * (f ? v : 1 - v));
				}
				function ha(a, b, c, f) {
					0 > b && (b = 4294967295 + b + 1);
					for (var v = 0, F = Math.min(a.length - c, 4); v < F; ++v)
						a[c + v] = (b >>> (8 * (f ? v : 3 - v))) & 255;
				}
				function w(a, b, c, f, v, F) {
					if (c + f > a.length) throw new RangeError("Index out of range");
					if (0 > c) throw new RangeError("Index out of range");
				}
				function e(a, b, c, f, v) {
					v || w(a, b, c, 4, 3.4028234663852886e38, -3.4028234663852886e38);
					R.write(a, b, c, f, 23, 4);
					return c + 4;
				}
				function C(a, b, c, f, v) {
					v || w(a, b, c, 8, 1.7976931348623157e308, -1.7976931348623157e308);
					R.write(a, b, c, f, 52, 8);
					return c + 8;
				}
				function D(a, b) {
					b = b || Infinity;
					for (var c, f = a.length, v = null, F = [], T = 0; T < f; ++T) {
						c = a.charCodeAt(T);
						if (55295 < c && 57344 > c) {
							if (!v) {
								if (56319 < c) {
									-1 < (b -= 3) && F.push(239, 191, 189);
									continue;
								} else if (T + 1 === f) {
									-1 < (b -= 3) && F.push(239, 191, 189);
									continue;
								}
								v = c;
								continue;
							}
							if (56320 > c) {
								-1 < (b -= 3) && F.push(239, 191, 189);
								v = c;
								continue;
							}
							c = (((v - 55296) << 10) | (c - 56320)) + 65536;
						} else v && -1 < (b -= 3) && F.push(239, 191, 189);
						v = null;
						if (128 > c) {
							if (0 > --b) break;
							F.push(c);
						} else if (2048 > c) {
							if (0 > (b -= 2)) break;
							F.push((c >> 6) | 192, (c & 63) | 128);
						} else if (65536 > c) {
							if (0 > (b -= 3)) break;
							F.push((c >> 12) | 224, ((c >> 6) & 63) | 128, (c & 63) | 128);
						} else if (1114112 > c) {
							if (0 > (b -= 4)) break;
							F.push(
								(c >> 18) | 240,
								((c >> 12) & 63) | 128,
								((c >> 6) & 63) | 128,
								(c & 63) | 128,
							);
						} else throw Error("Invalid code point");
					}
					return F;
				}
				function I(a) {
					for (var b = [], c = 0; c < a.length; ++c)
						b.push(a.charCodeAt(c) & 255);
					return b;
				}
				function O(a) {
					var b = W,
						c = b.toByteArray;
					a = (a.trim ? a.trim() : a.replace(/^\s+|\s+$/g, "")).replace(oa, "");
					if (2 > a.length) a = "";
					else for (; 0 !== a.length % 4; ) a += "=";
					return c.call(b, a);
				}
				function U(a, b, c, f) {
					for (var v = 0; v < f && !(v + c >= b.length || v >= a.length); ++v)
						b[v + c] = a[v];
					return v;
				}
				var W = n(15),
					R = n(16),
					ia = n(17);
				u.Buffer = d;
				u.SlowBuffer = function (a) {
					+a != a && (a = 0);
					return d.alloc(+a);
				};
				u.INSPECT_MAX_BYTES = 50;
				d.TYPED_ARRAY_SUPPORT =
					void 0 !== k.TYPED_ARRAY_SUPPORT ? k.TYPED_ARRAY_SUPPORT : g();
				u.kMaxLength = d.TYPED_ARRAY_SUPPORT ? 2147483647 : 1073741823;
				d.poolSize = 8192;
				d._augment = function (a) {
					a.__proto__ = d.prototype;
					return a;
				};
				d.from = function (a, b, c) {
					return q(null, a, b, c);
				};
				d.TYPED_ARRAY_SUPPORT &&
					((d.prototype.__proto__ = Uint8Array.prototype),
					(d.__proto__ = Uint8Array),
					"undefined" !== typeof Symbol &&
						Symbol.species &&
						d[Symbol.species] === d &&
						Object.defineProperty(d, Symbol.species, {
							value: null,
							configurable: !0,
						}));
				d.alloc = function (a, b, c) {
					h(a);
					a =
						0 >= a
							? r(null, a)
							: void 0 !== b
								? "string" === typeof c
									? r(null, a).fill(b, c)
									: r(null, a).fill(b)
								: r(null, a);
					return a;
				};
				d.allocUnsafe = function (a) {
					return m(null, a);
				};
				d.allocUnsafeSlow = function (a) {
					return m(null, a);
				};
				d.isBuffer = function (a) {
					return !(null == a || !a._isBuffer);
				};
				d.compare = function (a, b) {
					if (!d.isBuffer(a) || !d.isBuffer(b))
						throw new TypeError("Arguments must be Buffers");
					if (a === b) return 0;
					for (
						var c = a.length, f = b.length, v = 0, F = Math.min(c, f);
						v < F;
						++v
					)
						if (a[v] !== b[v]) {
							c = a[v];
							f = b[v];
							break;
						}
					return c < f ? -1 : f < c ? 1 : 0;
				};
				d.isEncoding = function (a) {
					switch (String(a).toLowerCase()) {
						case "hex":
						case "utf8":
						case "utf-8":
						case "ascii":
						case "latin1":
						case "binary":
						case "base64":
						case "ucs2":
						case "ucs-2":
						case "utf16le":
						case "utf-16le":
							return !0;
						default:
							return !1;
					}
				};
				d.concat = function (a, b) {
					if (!ia(a))
						throw new TypeError('"list" argument must be an Array of Buffers');
					if (0 === a.length) return d.alloc(0);
					var c;
					if (void 0 === b) for (c = b = 0; c < a.length; ++c) b += a[c].length;
					b = d.allocUnsafe(b);
					var f = 0;
					for (c = 0; c < a.length; ++c) {
						var v = a[c];
						if (!d.isBuffer(v))
							throw new TypeError(
								'"list" argument must be an Array of Buffers',
							);
						v.copy(b, f);
						f += v.length;
					}
					return b;
				};
				d.byteLength = A;
				d.prototype._isBuffer = !0;
				d.prototype.swap16 = function () {
					var a = this.length;
					if (0 !== a % 2)
						throw new RangeError("Buffer size must be a multiple of 16-bits");
					for (var b = 0; b < a; b += 2) y(this, b, b + 1);
					return this;
				};
				d.prototype.swap32 = function () {
					var a = this.length;
					if (0 !== a % 4)
						throw new RangeError("Buffer size must be a multiple of 32-bits");
					for (var b = 0; b < a; b += 4)
						y(this, b, b + 3), y(this, b + 1, b + 2);
					return this;
				};
				d.prototype.swap64 = function () {
					var a = this.length;
					if (0 !== a % 8)
						throw new RangeError("Buffer size must be a multiple of 64-bits");
					for (var b = 0; b < a; b += 8)
						y(this, b, b + 7),
							y(this, b + 1, b + 6),
							y(this, b + 2, b + 5),
							y(this, b + 3, b + 4);
					return this;
				};
				d.prototype.toString = function () {
					var a = this.length | 0;
					return 0 === a
						? ""
						: 0 === arguments.length
							? la(this, 0, a)
							: L.apply(this, arguments);
				};
				d.prototype.equals = function (a) {
					if (!d.isBuffer(a)) throw new TypeError("Argument must be a Buffer");
					return this === a ? !0 : 0 === d.compare(this, a);
				};
				d.prototype.inspect = function () {
					var a = "",
						b = u.INSPECT_MAX_BYTES;
					0 < this.length &&
						((a = this.toString("hex", 0, b).match(/.{2}/g).join(" ")),
						this.length > b && (a += " ... "));
					return "<Buffer " + a + ">";
				};
				d.prototype.compare = function (a, b, c, f, v) {
					if (!d.isBuffer(a)) throw new TypeError("Argument must be a Buffer");
					void 0 === b && (b = 0);
					void 0 === c && (c = a ? a.length : 0);
					void 0 === f && (f = 0);
					void 0 === v && (v = this.length);
					if (0 > b || c > a.length || 0 > f || v > this.length)
						throw new RangeError("out of range index");
					if (f >= v && b >= c) return 0;
					if (f >= v) return -1;
					if (b >= c) return 1;
					b >>>= 0;
					c >>>= 0;
					f >>>= 0;
					v >>>= 0;
					if (this === a) return 0;
					var F = v - f,
						T = c - b,
						V = Math.min(F, T);
					f = this.slice(f, v);
					a = a.slice(b, c);
					for (b = 0; b < V; ++b)
						if (f[b] !== a[b]) {
							F = f[b];
							T = a[b];
							break;
						}
					return F < T ? -1 : T < F ? 1 : 0;
				};
				d.prototype.includes = function (a, b, c) {
					return -1 !== this.indexOf(a, b, c);
				};
				d.prototype.indexOf = function (a, b, c) {
					return Q(this, a, b, c, !0);
				};
				d.prototype.lastIndexOf = function (a, b, c) {
					return Q(this, a, b, c, !1);
				};
				d.prototype.write = function (a, b, c, f) {
					if (void 0 === b) (f = "utf8"), (c = this.length), (b = 0);
					else if (void 0 === c && "string" === typeof b)
						(f = b), (c = this.length), (b = 0);
					else if (isFinite(b))
						(b |= 0),
							isFinite(c)
								? ((c |= 0), void 0 === f && (f = "utf8"))
								: ((f = c), (c = void 0));
					else
						throw Error(
							"Buffer.write(string, encoding, offset[, length]) is no longer supported",
						);
					var v = this.length - b;
					if (void 0 === c || c > v) c = v;
					if ((0 < a.length && (0 > c || 0 > b)) || b > this.length)
						throw new RangeError("Attempt to write outside buffer bounds");
					f || (f = "utf8");
					for (v = !1; ; )
						switch (f) {
							case "hex":
								a: {
									b = Number(b) || 0;
									f = this.length - b;
									c ? ((c = Number(c)), c > f && (c = f)) : (c = f);
									f = a.length;
									if (0 !== f % 2) throw new TypeError("Invalid hex string");
									c > f / 2 && (c = f / 2);
									for (f = 0; f < c; ++f) {
										v = parseInt(a.substr(2 * f, 2), 16);
										if (isNaN(v)) {
											a = f;
											break a;
										}
										this[b + f] = v;
									}
									a = f;
								}
								return a;
							case "utf8":
							case "utf-8":
								return U(D(a, this.length - b), this, b, c);
							case "ascii":
								return U(I(a), this, b, c);
							case "latin1":
							case "binary":
								return U(I(a), this, b, c);
							case "base64":
								return U(O(a), this, b, c);
							case "ucs2":
							case "ucs-2":
							case "utf16le":
							case "utf-16le":
								f = a;
								v = this.length - b;
								for (var F = [], T = 0; T < f.length && !(0 > (v -= 2)); ++T) {
									var V = f.charCodeAt(T);
									a = V >> 8;
									V %= 256;
									F.push(V);
									F.push(a);
								}
								return U(F, this, b, c);
							default:
								if (v) throw new TypeError("Unknown encoding: " + f);
								f = ("" + f).toLowerCase();
								v = !0;
						}
				};
				d.prototype.toJSON = function () {
					return {
						type: "Buffer",
						data: Array.prototype.slice.call(this._arr || this, 0),
					};
				};
				var sa = 4096;
				d.prototype.slice = function (a, b) {
					var c = this.length;
					a = ~~a;
					b = void 0 === b ? c : ~~b;
					0 > a ? ((a += c), 0 > a && (a = 0)) : a > c && (a = c);
					0 > b ? ((b += c), 0 > b && (b = 0)) : b > c && (b = c);
					b < a && (b = a);
					if (d.TYPED_ARRAY_SUPPORT)
						(b = this.subarray(a, b)), (b.__proto__ = d.prototype);
					else {
						c = b - a;
						b = new d(c, void 0);
						for (var f = 0; f < c; ++f) b[f] = this[f + a];
					}
					return b;
				};
				d.prototype.readUIntLE = function (a, b, c) {
					a |= 0;
					b |= 0;
					c || M(a, b, this.length);
					c = this[a];
					for (var f = 1, v = 0; ++v < b && (f *= 256); ) c += this[a + v] * f;
					return c;
				};
				d.prototype.readUIntBE = function (a, b, c) {
					a |= 0;
					b |= 0;
					c || M(a, b, this.length);
					c = this[a + --b];
					for (var f = 1; 0 < b && (f *= 256); ) c += this[a + --b] * f;
					return c;
				};
				d.prototype.readUInt8 = function (a, b) {
					b || M(a, 1, this.length);
					return this[a];
				};
				d.prototype.readUInt16LE = function (a, b) {
					b || M(a, 2, this.length);
					return this[a] | (this[a + 1] << 8);
				};
				d.prototype.readUInt16BE = function (a, b) {
					b || M(a, 2, this.length);
					return (this[a] << 8) | this[a + 1];
				};
				d.prototype.readUInt32LE = function (a, b) {
					b || M(a, 4, this.length);
					return (
						(this[a] | (this[a + 1] << 8) | (this[a + 2] << 16)) +
						16777216 * this[a + 3]
					);
				};
				d.prototype.readUInt32BE = function (a, b) {
					b || M(a, 4, this.length);
					return (
						16777216 * this[a] +
						((this[a + 1] << 16) | (this[a + 2] << 8) | this[a + 3])
					);
				};
				d.prototype.readIntLE = function (a, b, c) {
					a |= 0;
					b |= 0;
					c || M(a, b, this.length);
					c = this[a];
					for (var f = 1, v = 0; ++v < b && (f *= 256); ) c += this[a + v] * f;
					c >= 128 * f && (c -= Math.pow(2, 8 * b));
					return c;
				};
				d.prototype.readIntBE = function (a, b, c) {
					a |= 0;
					b |= 0;
					c || M(a, b, this.length);
					c = b;
					for (var f = 1, v = this[a + --c]; 0 < c && (f *= 256); )
						v += this[a + --c] * f;
					v >= 128 * f && (v -= Math.pow(2, 8 * b));
					return v;
				};
				d.prototype.readInt8 = function (a, b) {
					b || M(a, 1, this.length);
					return this[a] & 128 ? -1 * (255 - this[a] + 1) : this[a];
				};
				d.prototype.readInt16LE = function (a, b) {
					b || M(a, 2, this.length);
					a = this[a] | (this[a + 1] << 8);
					return a & 32768 ? a | 4294901760 : a;
				};
				d.prototype.readInt16BE = function (a, b) {
					b || M(a, 2, this.length);
					a = this[a + 1] | (this[a] << 8);
					return a & 32768 ? a | 4294901760 : a;
				};
				d.prototype.readInt32LE = function (a, b) {
					b || M(a, 4, this.length);
					return (
						this[a] |
						(this[a + 1] << 8) |
						(this[a + 2] << 16) |
						(this[a + 3] << 24)
					);
				};
				d.prototype.readInt32BE = function (a, b) {
					b || M(a, 4, this.length);
					return (
						(this[a] << 24) |
						(this[a + 1] << 16) |
						(this[a + 2] << 8) |
						this[a + 3]
					);
				};
				d.prototype.readFloatLE = function (a, b) {
					b || M(a, 4, this.length);
					return R.read(this, a, !0, 23, 4);
				};
				d.prototype.readFloatBE = function (a, b) {
					b || M(a, 4, this.length);
					return R.read(this, a, !1, 23, 4);
				};
				d.prototype.readDoubleLE = function (a, b) {
					b || M(a, 8, this.length);
					return R.read(this, a, !0, 52, 8);
				};
				d.prototype.readDoubleBE = function (a, b) {
					b || M(a, 8, this.length);
					return R.read(this, a, !1, 52, 8);
				};
				d.prototype.writeUIntLE = function (a, b, c, f) {
					a = +a;
					b |= 0;
					c |= 0;
					f || Z(this, a, b, c, Math.pow(2, 8 * c) - 1, 0);
					f = 1;
					var v = 0;
					for (this[b] = a & 255; ++v < c && (f *= 256); )
						this[b + v] = (a / f) & 255;
					return b + c;
				};
				d.prototype.writeUIntBE = function (a, b, c, f) {
					a = +a;
					b |= 0;
					c |= 0;
					f || Z(this, a, b, c, Math.pow(2, 8 * c) - 1, 0);
					f = c - 1;
					var v = 1;
					for (this[b + f] = a & 255; 0 <= --f && (v *= 256); )
						this[b + f] = (a / v) & 255;
					return b + c;
				};
				d.prototype.writeUInt8 = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 1, 255, 0);
					d.TYPED_ARRAY_SUPPORT || (a = Math.floor(a));
					this[b] = a & 255;
					return b + 1;
				};
				d.prototype.writeUInt16LE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 2, 65535, 0);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a & 255), (this[b + 1] = a >>> 8))
						: qa(this, a, b, !0);
					return b + 2;
				};
				d.prototype.writeUInt16BE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 2, 65535, 0);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a >>> 8), (this[b + 1] = a & 255))
						: qa(this, a, b, !1);
					return b + 2;
				};
				d.prototype.writeUInt32LE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 4, 4294967295, 0);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b + 3] = a >>> 24),
							(this[b + 2] = a >>> 16),
							(this[b + 1] = a >>> 8),
							(this[b] = a & 255))
						: ha(this, a, b, !0);
					return b + 4;
				};
				d.prototype.writeUInt32BE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 4, 4294967295, 0);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a >>> 24),
							(this[b + 1] = a >>> 16),
							(this[b + 2] = a >>> 8),
							(this[b + 3] = a & 255))
						: ha(this, a, b, !1);
					return b + 4;
				};
				d.prototype.writeIntLE = function (a, b, c, f) {
					a = +a;
					b |= 0;
					f || ((f = Math.pow(2, 8 * c - 1)), Z(this, a, b, c, f - 1, -f));
					f = 0;
					var v = 1,
						F = 0;
					for (this[b] = a & 255; ++f < c && (v *= 256); )
						0 > a && 0 === F && 0 !== this[b + f - 1] && (F = 1),
							(this[b + f] = (((a / v) >> 0) - F) & 255);
					return b + c;
				};
				d.prototype.writeIntBE = function (a, b, c, f) {
					a = +a;
					b |= 0;
					f || ((f = Math.pow(2, 8 * c - 1)), Z(this, a, b, c, f - 1, -f));
					f = c - 1;
					var v = 1,
						F = 0;
					for (this[b + f] = a & 255; 0 <= --f && (v *= 256); )
						0 > a && 0 === F && 0 !== this[b + f + 1] && (F = 1),
							(this[b + f] = (((a / v) >> 0) - F) & 255);
					return b + c;
				};
				d.prototype.writeInt8 = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 1, 127, -128);
					d.TYPED_ARRAY_SUPPORT || (a = Math.floor(a));
					0 > a && (a = 255 + a + 1);
					this[b] = a & 255;
					return b + 1;
				};
				d.prototype.writeInt16LE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 2, 32767, -32768);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a & 255), (this[b + 1] = a >>> 8))
						: qa(this, a, b, !0);
					return b + 2;
				};
				d.prototype.writeInt16BE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 2, 32767, -32768);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a >>> 8), (this[b + 1] = a & 255))
						: qa(this, a, b, !1);
					return b + 2;
				};
				d.prototype.writeInt32LE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 4, 2147483647, -2147483648);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a & 255),
							(this[b + 1] = a >>> 8),
							(this[b + 2] = a >>> 16),
							(this[b + 3] = a >>> 24))
						: ha(this, a, b, !0);
					return b + 4;
				};
				d.prototype.writeInt32BE = function (a, b, c) {
					a = +a;
					b |= 0;
					c || Z(this, a, b, 4, 2147483647, -2147483648);
					0 > a && (a = 4294967295 + a + 1);
					d.TYPED_ARRAY_SUPPORT
						? ((this[b] = a >>> 24),
							(this[b + 1] = a >>> 16),
							(this[b + 2] = a >>> 8),
							(this[b + 3] = a & 255))
						: ha(this, a, b, !1);
					return b + 4;
				};
				d.prototype.writeFloatLE = function (a, b, c) {
					return e(this, a, b, !0, c);
				};
				d.prototype.writeFloatBE = function (a, b, c) {
					return e(this, a, b, !1, c);
				};
				d.prototype.writeDoubleLE = function (a, b, c) {
					return C(this, a, b, !0, c);
				};
				d.prototype.writeDoubleBE = function (a, b, c) {
					return C(this, a, b, !1, c);
				};
				d.prototype.copy = function (a, b, c, f) {
					c || (c = 0);
					f || 0 === f || (f = this.length);
					b >= a.length && (b = a.length);
					b || (b = 0);
					0 < f && f < c && (f = c);
					if (f === c || 0 === a.length || 0 === this.length) return 0;
					if (0 > b) throw new RangeError("targetStart out of bounds");
					if (0 > c || c >= this.length)
						throw new RangeError("sourceStart out of bounds");
					if (0 > f) throw new RangeError("sourceEnd out of bounds");
					f > this.length && (f = this.length);
					a.length - b < f - c && (f = a.length - b + c);
					var v = f - c;
					if (this === a && c < b && b < f)
						for (f = v - 1; 0 <= f; --f) a[f + b] = this[f + c];
					else if (1e3 > v || !d.TYPED_ARRAY_SUPPORT)
						for (f = 0; f < v; ++f) a[f + b] = this[f + c];
					else Uint8Array.prototype.set.call(a, this.subarray(c, c + v), b);
					return v;
				};
				d.prototype.fill = function (a, b, c, f) {
					if ("string" === typeof a) {
						"string" === typeof b
							? ((f = b), (b = 0), (c = this.length))
							: "string" === typeof c && ((f = c), (c = this.length));
						if (1 === a.length) {
							var v = a.charCodeAt(0);
							256 > v && (a = v);
						}
						if (void 0 !== f && "string" !== typeof f)
							throw new TypeError("encoding must be a string");
						if ("string" === typeof f && !d.isEncoding(f))
							throw new TypeError("Unknown encoding: " + f);
					} else "number" === typeof a && (a &= 255);
					if (0 > b || this.length < b || this.length < c)
						throw new RangeError("Out of range index");
					if (c <= b) return this;
					b >>>= 0;
					c = void 0 === c ? this.length : c >>> 0;
					a || (a = 0);
					if ("number" === typeof a) for (f = b; f < c; ++f) this[f] = a;
					else
						for (
							a = d.isBuffer(a) ? a : D(new d(a, f).toString()),
								v = a.length,
								f = 0;
							f < c - b;
							++f
						)
							this[f + b] = a[f % v];
					return this;
				};
				var oa = /[^+\/0-9A-Za-z-_]/g;
			}).call(this, n(14));
		},
		function (t, u) {
			u = (function () {
				return this;
			})();
			try {
				u = u || new Function("return this")();
			} catch (n) {
				"object" === typeof window && (u = window);
			}
			t.exports = u;
		},
		function (t, u, n) {
			function k(q) {
				var h = q.length;
				if (0 < h % 4)
					throw Error("Invalid string. Length must be a multiple of 4");
				q = q.indexOf("=");
				-1 === q && (q = h);
				return [q, q === h ? 0 : 4 - (q % 4)];
			}
			u.byteLength = function (q) {
				q = k(q);
				var h = q[1];
				return (3 * (q[0] + h)) / 4 - h;
			};
			u.toByteArray = function (q) {
				var h = k(q);
				var m = h[0];
				h = h[1];
				var p = new d((3 * (m + h)) / 4 - h),
					x = 0,
					B = 0 < h ? m - 4 : m,
					A;
				for (A = 0; A < B; A += 4)
					(m =
						(r[q.charCodeAt(A)] << 18) |
						(r[q.charCodeAt(A + 1)] << 12) |
						(r[q.charCodeAt(A + 2)] << 6) |
						r[q.charCodeAt(A + 3)]),
						(p[x++] = (m >> 16) & 255),
						(p[x++] = (m >> 8) & 255),
						(p[x++] = m & 255);
				2 === h &&
					((m = (r[q.charCodeAt(A)] << 2) | (r[q.charCodeAt(A + 1)] >> 4)),
					(p[x++] = m & 255));
				1 === h &&
					((m =
						(r[q.charCodeAt(A)] << 10) |
						(r[q.charCodeAt(A + 1)] << 4) |
						(r[q.charCodeAt(A + 2)] >> 2)),
					(p[x++] = (m >> 8) & 255),
					(p[x++] = m & 255));
				return p;
			};
			u.fromByteArray = function (q) {
				for (
					var h = q.length, m = h % 3, p = [], x = 0, B = h - m;
					x < B;
					x += 16383
				) {
					for (
						var A = p,
							L = A.push,
							y,
							Q = q,
							ea = x + 16383 > B ? B : x + 16383,
							la = [],
							M = x;
						M < ea;
						M += 3
					)
						(y =
							((Q[M] << 16) & 16711680) +
							((Q[M + 1] << 8) & 65280) +
							(Q[M + 2] & 255)),
							la.push(
								g[(y >> 18) & 63] +
									g[(y >> 12) & 63] +
									g[(y >> 6) & 63] +
									g[y & 63],
							);
					y = la.join("");
					L.call(A, y);
				}
				1 === m
					? ((q = q[h - 1]), p.push(g[q >> 2] + g[(q << 4) & 63] + "=="))
					: 2 === m &&
						((q = (q[h - 2] << 8) + q[h - 1]),
						p.push(g[q >> 10] + g[(q >> 4) & 63] + g[(q << 2) & 63] + "="));
				return p.join("");
			};
			var g = [],
				r = [],
				d = "undefined" !== typeof Uint8Array ? Uint8Array : Array;
			for (t = 0; 64 > t; ++t)
				(g[t] =
					"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[
						t
					]),
					(r[
						"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(
							t,
						)
					] = t);
			r[45] = 62;
			r[95] = 63;
		},
		function (t, u) {
			u.read = function (n, k, g, r, d) {
				var q = 8 * d - r - 1;
				var h = (1 << q) - 1,
					m = h >> 1,
					p = -7;
				d = g ? d - 1 : 0;
				var x = g ? -1 : 1,
					B = n[k + d];
				d += x;
				g = B & ((1 << -p) - 1);
				B >>= -p;
				for (p += q; 0 < p; g = 256 * g + n[k + d], d += x, p -= 8);
				q = g & ((1 << -p) - 1);
				g >>= -p;
				for (p += r; 0 < p; q = 256 * q + n[k + d], d += x, p -= 8);
				if (0 === g) g = 1 - m;
				else {
					if (g === h) return q ? NaN : Infinity * (B ? -1 : 1);
					q += Math.pow(2, r);
					g -= m;
				}
				return (B ? -1 : 1) * q * Math.pow(2, g - r);
			};
			u.write = function (n, k, g, r, d, q) {
				var h,
					m = 8 * q - d - 1,
					p = (1 << m) - 1,
					x = p >> 1,
					B = 23 === d ? Math.pow(2, -24) - Math.pow(2, -77) : 0;
				q = r ? 0 : q - 1;
				var A = r ? 1 : -1,
					L = 0 > k || (0 === k && 0 > 1 / k) ? 1 : 0;
				k = Math.abs(k);
				isNaN(k) || Infinity === k
					? ((k = isNaN(k) ? 1 : 0), (r = p))
					: ((r = Math.floor(Math.log(k) / Math.LN2)),
						1 > k * (h = Math.pow(2, -r)) && (r--, (h *= 2)),
						(k = 1 <= r + x ? k + B / h : k + B * Math.pow(2, 1 - x)),
						2 <= k * h && (r++, (h /= 2)),
						r + x >= p
							? ((k = 0), (r = p))
							: 1 <= r + x
								? ((k = (k * h - 1) * Math.pow(2, d)), (r += x))
								: ((k = k * Math.pow(2, x - 1) * Math.pow(2, d)), (r = 0)));
				for (; 8 <= d; n[g + q] = k & 255, q += A, k /= 256, d -= 8);
				r = (r << d) | k;
				for (m += d; 0 < m; n[g + q] = r & 255, q += A, r /= 256, m -= 8);
				n[g + q - A] |= 128 * L;
			};
		},
		function (t, u) {
			var n = {}.toString;
			t.exports =
				Array.isArray ||
				function (k) {
					return "[object Array]" == n.call(k);
				};
		},
	]);
}).call(this || window);
