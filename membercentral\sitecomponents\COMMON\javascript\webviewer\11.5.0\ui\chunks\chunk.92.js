(window.webpackJsonp = window.webpackJsonp || []).push([
	[92],
	{
		2062: function (t, e, n) {
			"use strict";
			n.r(e);
			n(97),
				n(27),
				n(28),
				n(12),
				n(13),
				n(8),
				n(25),
				n(22),
				n(58),
				n(44),
				n(14),
				n(10),
				n(9),
				n(11);
			var r = n(0),
				o = n.n(r),
				a = n(82),
				i = n(3),
				l = n.n(i),
				c = n(7),
				u = n(68),
				s = n(70),
				f = n(6),
				y = n(4),
				b = n(1);
			var m = function (t) {
				t &&
					b.a
						.getDocumentViewer()
						.getSpreadsheetEditorManager()
						.setSelectedCellRangeStyle({ font: t });
			};
			function p(t) {
				return (p =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function v() {
				return (v = Object.assign
					? Object.assign.bind()
					: function (t) {
							for (var e = 1; e < arguments.length; e++) {
								var n = arguments[e];
								for (var r in n)
									Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r]);
							}
							return t;
						}).apply(this, arguments);
			}
			function d(t, e, n) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== p(t) || null === t) return t;
							var n = t[Symbol.toPrimitive];
							if (void 0 !== n) {
								var r = n.call(t, e || "default");
								if ("object" !== p(r)) return r;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === p(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = n),
					t
				);
			}
			var g = {
					styleType: l.a.oneOf(Object.values(c.t)).isRequired,
					isFlyoutItem: l.a.bool,
					style: l.a.object,
					className: l.a.string,
				},
				w = Object(r.forwardRef)(function (t, e) {
					var n = t.isFlyoutItem,
						r = t.styleType,
						i = t.style,
						l = t.className,
						c = Object(f.e)(function (t) {
							return y.a.getActiveCellRangeFontStyle(t, r);
						}),
						b = s.b["".concat(r, "Button")],
						p = b.dataElement,
						g = b.icon,
						w = b.title,
						S = function () {
							m(d({}, r, !c));
						};
					return n
						? o.a.createElement(
								u.a,
								v({}, t, {
									ref: e,
									onClick: S,
									additionalClass: c ? "active" : "",
								}),
							)
						: o.a.createElement(a.a, {
								ariaCurrent: c,
								isActive: c,
								dataElement: p,
								title: w,
								img: g,
								onClick: S,
								style: i,
								className: l,
							});
				});
			(w.propTypes = g), (w.displayName = "CellDecoratorButton");
			e.default = w;
		},
	},
]);
//# sourceMappingURL=chunk.92.js.map
