/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[9],
		{
			638: function (ya, va, r) {
				function la(fa) {
					fa.$a();
					fa.advance();
					var ha = fa.current.textContent;
					fa.Ib();
					return ha;
				}
				function na(fa) {
					var ha = [];
					for (fa.$a(); fa.advance(); ) {
						var pa = fa.pb();
						"field" === pa
							? ha.push(String(fa.ka("name")))
							: Object(e.i)("unrecognised field list element: ".concat(pa));
					}
					fa.Ib();
					return ha;
				}
				function ma(fa, ha) {
					return ha ? "false" !== fa : "true" === fa;
				}
				function ja(fa, ha) {
					var pa = fa.pb();
					switch (pa) {
						case "javascript":
							return { name: "JavaScript", javascript: fa.current.textContent };
						case "uri":
							return { name: "URI", uri: fa.ka("uri") };
						case "goto":
							pa = null;
							fa.$a();
							if (fa.advance()) {
								var oa = fa.ka("fit");
								pa = { page: fa.ka("page"), fit: oa };
								if ("0" === pa.page)
									Object(e.i)("null page encountered in dest");
								else
									switch (((ha = ha(Number(pa.page))), oa)) {
										case "Fit":
										case "FitB":
											break;
										case "FitH":
										case "FitBH":
											pa.top = ha.Aa({ x: 0, y: fa.ka("top") || 0 }).y;
											break;
										case "FitV":
										case "FitBV":
											pa.left = ha.Aa({ x: fa.ka("left") || 0, y: 0 }).x;
											break;
										case "FitR":
											oa = ha.Aa({
												x: fa.ka("left") || 0,
												y: fa.ka("top") || 0,
											});
											ha = ha.Aa({
												x: fa.ka("right") || 0,
												y: fa.ka("bottom") || 0,
											});
											ha = new w.d(oa.x, oa.y, ha.x, ha.y);
											pa.top = ha.y1;
											pa.left = ha.x1;
											pa.bottom = ha.y2;
											pa.right = ha.x2;
											break;
										case "XYZ":
											oa = ha.Aa({
												x: fa.ka("left") || 0,
												y: fa.ka("top") || 0,
											});
											pa.top = oa.y;
											pa.left = oa.x;
											pa.zoom = fa.ka("zoom") || 0;
											break;
										default:
											Object(e.i)("unknown dest fit: ".concat(oa));
									}
								pa = { name: "GoTo", dest: pa };
							} else Object(e.i)("missing dest in GoTo action");
							fa.Ib();
							return pa;
						case "submit-form":
							pa = {
								name: "SubmitForm",
								url: fa.ka("url"),
								format: fa.ka("format"),
								method: fa.ka("method") || "POST",
								exclude: ma(fa.ka("exclude"), !1),
							};
							ha = fa.ka("flags");
							pa.flags = ha ? ha.split(" ") : [];
							for (fa.$a(); fa.advance(); )
								switch (((ha = fa.pb()), ha)) {
									case "fields":
										pa.fields = na(fa);
										break;
									default:
										Object(e.i)("unrecognised submit-form child: ".concat(ha));
								}
							fa.Ib();
							return pa;
						case "reset-form":
							pa = { name: "ResetForm", exclude: ma(fa.ka("exclude"), !1) };
							for (fa.$a(); fa.advance(); )
								switch (((ha = fa.pb()), ha)) {
									case "fields":
										pa.fields = na(fa);
										break;
									default:
										Object(e.i)("unrecognised reset-form child: ".concat(ha));
								}
							fa.Ib();
							return pa;
						case "hide":
							pa = { name: "Hide", hide: ma(fa.ka("hide"), !0) };
							for (fa.$a(); fa.advance(); )
								switch (((ha = fa.pb()), ha)) {
									case "fields":
										pa.fields = na(fa);
										break;
									default:
										Object(e.i)("unrecognised hide child: ".concat(ha));
								}
							fa.Ib();
							return pa;
						case "named":
							return { name: "Named", action: fa.ka("name") };
						default:
							Object(e.i)("Encountered unexpected action type: ".concat(pa));
					}
					return null;
				}
				function ia(fa, ha, pa) {
					var oa = {};
					for (fa.$a(); fa.advance(); ) {
						var ka = fa.pb();
						switch (ka) {
							case "action":
								ka = fa.ka("trigger");
								if (ha ? -1 !== ha.indexOf(ka) : 1) {
									oa[ka] = [];
									for (fa.$a(); fa.advance(); ) {
										var ra = ja(fa, pa);
										Object(f.isNull)(ra) || oa[ka].push(ra);
									}
									fa.Ib();
								} else
									Object(e.i)(
										"encountered unexpected trigger on field: ".concat(ka),
									);
								break;
							default:
								Object(e.i)("encountered unknown action child: ".concat(ka));
						}
					}
					fa.Ib();
					return oa;
				}
				function ca(fa) {
					return new z.a(
						fa.ka("r") || 0,
						fa.ka("g") || 0,
						fa.ka("b") || 0,
						fa.ka("a") || 1,
					);
				}
				function y(fa, ha) {
					var pa = fa.ka("name"),
						oa = fa.ka("type") || "Type1",
						ka = fa.ka("size"),
						ra = ha.Aa({ x: 0, y: 0 });
					ka = ha.Aa({ x: Number(ka), y: 0 });
					ha = ra.x - ka.x;
					ra = ra.y - ka.y;
					pa = {
						name: pa,
						type: oa,
						size: Math.sqrt(ha * ha + ra * ra) || 0,
						strokeColor: [0, 0, 0],
						fillColor: [0, 0, 0],
					};
					for (fa.$a(); fa.advance(); )
						switch (((oa = fa.pb()), oa)) {
							case "stroke-color":
								pa.strokeColor = ca(fa);
								break;
							case "fill-color":
								pa.fillColor = ca(fa);
								break;
							default:
								Object(e.i)("unrecognised font child: ".concat(oa));
						}
					fa.Ib();
					return pa;
				}
				function x(fa) {
					var ha = [];
					for (fa.$a(); fa.advance(); ) {
						var pa = fa.pb();
						switch (pa) {
							case "option":
								pa = ha;
								var oa = pa.push;
								var ka = fa;
								ka = {
									value: ka.ka("value"),
									displayValue: ka.ka("display-value") || void 0,
								};
								oa.call(pa, ka);
								break;
							default:
								Object(e.i)("unrecognised options child: ".concat(pa));
						}
					}
					fa.Ib();
					return ha;
				}
				function n(fa, ha) {
					var pa = fa.ka("name"),
						oa = {
							type: fa.ka("type"),
							quadding: fa.ka("quadding") || "Left-justified",
							maxLen: fa.ka("max-len") || -1,
						},
						ka = fa.ka("flags");
					Object(f.isString)(ka) && (oa.flags = ka.split(" "));
					for (fa.$a(); fa.advance(); )
						switch (((ka = fa.pb()), ka)) {
							case "actions":
								oa.actions = ia(fa, ["C", "F", "K", "V"], function () {
									return ha;
								});
								break;
							case "default-value":
								oa.defaultValue = la(fa);
								break;
							case "font":
								oa.font = y(fa, ha);
								break;
							case "options":
								oa.options = x(fa);
								break;
							default:
								Object(e.i)("unknown field child: ".concat(ka));
						}
					fa.Ib();
					return new window.da.Annotations.la.xa(pa, oa);
				}
				function h(fa, ha) {
					switch (fa.type) {
						case "Tx":
							try {
								if (Object(ea.c)(fa.actions))
									return new a.a.DatePickerWidgetAnnotation(fa, ha);
							} catch (pa) {
								Object(e.i)(pa);
							}
							return new a.a.TextWidgetAnnotation(fa, ha);
						case "Ch":
							return fa.flags.get(ba.WidgetFlags.COMBO)
								? new a.a.ChoiceWidgetAnnotation(fa, ha)
								: new a.a.ListWidgetAnnotation(fa, ha);
						case "Btn":
							return fa.flags.get(ba.WidgetFlags.PUSH_BUTTON)
								? new a.a.PushButtonWidgetAnnotation(fa, ha)
								: fa.flags.get(ba.WidgetFlags.RADIO)
									? new a.a.RadioButtonWidgetAnnotation(fa, ha)
									: new a.a.CheckButtonWidgetAnnotation(fa, ha);
						case "Sig":
							return new a.a.SignatureWidgetAnnotation(fa, ha);
						default:
							Object(e.i)("Unrecognised field type: ".concat(fa.type));
					}
					return null;
				}
				function b(fa, ha, pa, oa) {
					var ka = [],
						ra = {};
					fa.$a();
					var qa = [],
						sa = {},
						ta = [];
					Object(aa.a)(
						function () {
							if (fa.advance()) {
								var wa = fa.pb();
								switch (wa) {
									case "calculation-order":
										qa = "calculation-order" === fa.pb() ? na(fa) : [];
										break;
									case "document-actions":
										sa = ia(fa, ["Init", "Open"], ha);
										break;
									case "pages":
										wa = [];
										for (fa.$a(); fa.advance(); ) {
											var za = fa.pb();
											switch (za) {
												case "page":
													za = wa;
													var Aa = za.push,
														Ea = fa,
														Ga = ha,
														Ca = { number: Ea.ka("number") };
													for (Ea.$a(); Ea.advance(); ) {
														var La = Ea.pb();
														switch (La) {
															case "actions":
																Ca.actions = ia(Ea, ["O", "C"], Ga);
																break;
															default:
																Object(e.i)(
																	"unrecognised page child: ".concat(La),
																);
														}
													}
													Ea.Ib();
													Aa.call(za, Ca);
													break;
												default:
													Object(e.i)("unrecognised page child: ".concat(za));
											}
										}
										fa.Ib();
										ta = wa;
										break;
									case "field":
										za = n(fa, ha(1));
										ra[za.name] = za;
										break;
									case "widget":
										wa = {
											border: { style: "Solid", width: 1 },
											backgroundColor: [],
											fieldName: fa.ka("field"),
											page: fa.ka("page"),
											index: fa.ka("index") || 0,
											rotation: fa.ka("rotation") || 0,
											flags: [],
											isImporting: !0,
										};
										(za = fa.ka("appearance")) && (wa.appearance = za);
										(za = fa.ka("flags")) && (wa.flags = za.split(" "));
										for (fa.$a(); fa.advance(); )
											switch (((za = fa.pb()), za)) {
												case "rect":
													Aa = fa;
													Ea = ha(Number(wa.page));
													za = Ea.Aa({
														x: Aa.ka("x1") || 0,
														y: Aa.ka("y1") || 0,
													});
													Aa = Ea.Aa({
														x: Aa.ka("x2") || 0,
														y: Aa.ka("y2") || 0,
													});
													za = new w.d(za.x, za.y, Aa.x, Aa.y);
													za.normalize();
													wa.rect = {
														x1: za.x1,
														y1: za.y1,
														x2: za.x2,
														y2: za.y2,
													};
													break;
												case "border":
													za = fa;
													Aa = {
														style: za.ka("style") || "Solid",
														width: za.ka("width") || 1,
														color: [0, 0, 0],
													};
													for (za.$a(); za.advance(); )
														switch (((Ea = za.pb()), Ea)) {
															case "color":
																Aa.color = ca(za);
																break;
															default:
																Object(e.i)(
																	"unrecognised border child: ".concat(Ea),
																);
														}
													za.Ib();
													wa.border = Aa;
													break;
												case "background-color":
													wa.backgroundColor = ca(fa);
													break;
												case "actions":
													wa.actions = ia(
														fa,
														"E X D U Fo Bl PO PC PV PI".split(" "),
														ha,
													);
													break;
												case "appearances":
													za = fa;
													Aa = Object(ea.b)(wa, "appearances");
													for (za.$a(); za.advance(); )
														if (((Ea = za.pb()), "appearance" === Ea)) {
															Ea = za.ka("name");
															Ga = Object(ea.b)(Aa, Ea);
															Ea = za;
															for (Ea.$a(); Ea.advance(); )
																switch (((Ca = Ea.pb()), Ca)) {
																	case "Normal":
																		Object(ea.b)(Ga, "Normal").data =
																			Ea.current.textContent;
																		break;
																	default:
																		Object(e.i)(
																			"unexpected appearance state: ",
																			Ca,
																		);
																}
															Ea.Ib();
														} else
															Object(e.i)(
																"unexpected appearances child: ".concat(Ea),
															);
													za.Ib();
													break;
												case "extra":
													za = fa;
													Aa = ha;
													Ea = {};
													for (za.$a(); za.advance(); )
														switch (((Ga = za.pb()), Ga)) {
															case "font":
																Ea.font = y(za, Aa(1));
																break;
															default:
																Object(e.i)(
																	"unrecognised extra child: ".concat(Ga),
																);
														}
													za.Ib();
													za = Ea;
													za.font && (wa.font = za.font);
													break;
												case "captions":
													Aa = fa;
													za = {};
													(Ea = Aa.ka("Normal")) && (za.Normal = Ea);
													(Ea = Aa.ka("Rollover")) && (za.Rollover = Ea);
													(Aa = Aa.ka("Down")) && (za.Down = Aa);
													wa.captions = za;
													break;
												default:
													Object(e.i)("unrecognised widget child: ".concat(za));
											}
										fa.Ib();
										(za = ra[wa.fieldName])
											? ((wa = h(za, wa)), ka.push(wa))
											: Object(e.i)(
													"ignoring widget with no corresponding field data: ".concat(
														wa.fieldName,
													),
												);
										break;
									default:
										Object(e.i)(
											"Unknown element encountered in PDFInfo: ".concat(wa),
										);
								}
								return !0;
							}
							return !1;
						},
						function () {
							fa.Ib();
							pa({
								calculationOrder: qa,
								widgets: ka,
								fields: ra,
								documentActions: sa,
								pages: ta,
								custom: [],
							});
						},
						oa,
					);
				}
				r.r(va);
				r.d(va, "parse", function () {
					return b;
				});
				var e = r(2),
					f = r(1);
				r.n(f);
				var a = r(166),
					w = r(4),
					z = r(11),
					aa = r(26),
					ea = r(143),
					ba = r(18);
			},
		},
	]);
}).call(this || window);
