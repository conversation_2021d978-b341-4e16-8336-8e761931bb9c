{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/et.js"], "names": ["module", "exports", "e", "t", "default", "a", "u", "s", "m", "mm", "h", "hh", "d", "M", "MM", "y", "yy", "replace", "name", "weekdays", "split", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "relativeTime", "future", "past", "dd", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAG,SAASI,EAAEJ,EAAEG,EAAEF,EAAEG,GAAG,IAAIC,EAAE,CAACA,EAAE,CAAC,eAAe,cAAc,iBAAiBC,EAAE,CAAC,aAAa,aAAaC,GAAG,CAAC,YAAY,cAAcC,EAAE,CAAC,YAAY,YAAY,YAAYC,GAAG,CAAC,WAAW,YAAYC,EAAE,CAAC,YAAY,YAAYC,EAAE,CAAC,UAAU,WAAW,WAAWC,GAAG,CAAC,SAAS,WAAWC,EAAE,CAAC,YAAY,QAAQ,aAAaC,GAAG,CAAC,WAAW,cAAc,OAAOX,GAAGE,EAAEJ,GAAG,GAAGI,EAAEJ,GAAG,GAAGI,EAAEJ,GAAG,IAAIc,QAAQ,KAAKf,IAAII,EAAEC,EAAEJ,GAAG,GAAGI,EAAEJ,GAAG,IAAIc,QAAQ,KAAKf,GAAG,IAAIK,EAAE,CAACW,KAAK,KAAKC,SAAS,iEAAiEC,MAAM,KAAKC,cAAc,gBAAgBD,MAAM,KAAKE,YAAY,gBAAgBF,MAAM,KAAKG,OAAO,6FAA6FH,MAAM,KAAKI,YAAY,6DAA6DJ,MAAM,KAAKK,QAAQ,SAASvB,GAAG,OAAOA,EAAE,KAAKwB,UAAU,EAAEC,aAAa,CAACC,OAAO,YAAYC,KAAK,YAAYtB,EAAED,EAAEE,EAAEF,EAAEG,GAAGH,EAAEI,EAAEJ,EAAEK,GAAGL,EAAEM,EAAEN,EAAEwB,GAAG,WAAWjB,EAAEP,EAAEQ,GAAGR,EAAES,EAAET,EAAEU,GAAGV,GAAGyB,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,eAAeC,IAAI,oBAAoBC,KAAK,4BAA4B,OAAOlC,EAAEC,QAAQkC,OAAO/B,EAAE,MAAK,GAAIA,EAA33CF,CAAE,EAAQ", "file": "chunks/chunk.149.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_et=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var t=a(e);function u(e,a,t,u){var s={s:[\"mõne sekundi\",\"mõni sekund\",\"paar sekundit\"],m:[\"ühe minuti\",\"üks minut\"],mm:[\"%d minuti\",\"%d minutit\"],h:[\"ühe tunni\",\"tund aega\",\"üks tund\"],hh:[\"%d tunni\",\"%d tundi\"],d:[\"ühe päeva\",\"üks päev\"],M:[\"kuu aja\",\"kuu aega\",\"üks kuu\"],MM:[\"%d kuu\",\"%d kuud\"],y:[\"ühe aasta\",\"aasta\",\"üks aasta\"],yy:[\"%d aasta\",\"%d aastat\"]};return a?(s[t][2]?s[t][2]:s[t][1]).replace(\"%d\",e):(u?s[t][0]:s[t][1]).replace(\"%d\",e)}var s={name:\"et\",weekdays:\"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev\".split(\"_\"),weekdaysShort:\"P_E_T_K_N_R_L\".split(\"_\"),weekdaysMin:\"P_E_T_K_N_R_L\".split(\"_\"),months:\"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember\".split(\"_\"),monthsShort:\"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"%s pärast\",past:\"%s tagasi\",s:u,m:u,mm:u,h:u,hh:u,d:u,dd:\"%d päeva\",M:u,MM:u,y:u,yy:u},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D. MMMM YYYY\",LLL:\"D. MMMM YYYY H:mm\",LLLL:\"dddd, D. MMMM YYYY H:mm\"}};return t.default.locale(s,null,!0),s}));"], "sourceRoot": ""}