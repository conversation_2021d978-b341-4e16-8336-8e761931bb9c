ALTER PROCEDURE dbo.ref_updatePanelsByIds
@orgID INT,
@siteID INT,
@panelIDs VARCHAR(MAX),
@defaultLanguageID INT,
@recordedByMemberID INT,
@panelStatusID VARCHAR(10),
@feDspClientReferral VARCHAR(2),
@panelID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpReferralsToUpdate') IS NOT NULL
		DROP TABLE #tmpReferralsToUpdate;

	CREATE TABLE #tmpReferralsToUpdate (panelID INT);
	
	DECLARE  @Delimiter VARCHAR(2) = ',', @panelCount INT, @panelIdx INT = 0;
	
	DECLARE @Xml XML = CAST('<d>' + REPLACE(@panelIDs, @Delimiter, '</d><d>') + '</d>' AS xml);
	
	INSERT INTO #tmpReferralsToUpdate (panelID)
	SELECT  a.split.value('.', 'VARCHAR(MAX)') as panelID
	from    @Xml.nodes('/d') a(split);
	
	SELECT @panelCount = COUNT(trp.panelID) FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;

	IF @panelCount = 0
		SET @panelID = 0;
	ELSE BEGIN
		DECLARE @msg VARCHAR(MAX), @crlf VARCHAR(2), @oldPanelName VARCHAR(255);
		SET @crlf = char(13) + char(10);
		
		SELECT TOP 1 @panelIdx = rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;
		SET @panelID = 0;
		WHILE EXISTS(SELECT TOP 1 rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID)
		BEGIN
			
			IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;			
		
			CREATE TABLE #tmpAuditLogData ([rowCode] VARCHAR(20) PRIMARY KEY, [Status] VARCHAR(MAX),[Display Panel in Front-End?] VARCHAR(MAX));
			
			-- datatypecode
			INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
			VALUES ('DATATYPECODE', 'STRING','BIT');

			-- existing data
			INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
			SELECT 'OLDVAL', ps.statusName, p.feDspClientReferral
			FROM dbo.ref_panels AS P
			INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
			LEFT OUTER JOIN dbo.ref_panels AS pp ON pp.panelID = p.panelParentID
			LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pg on pg.GLAccountID = p.GLAccountID
			LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pc on pc.GLAccountID = p.clientFeeGLAccountID
			WHERE p.panelID = @panelIdx;		
			
			
			SELECT @oldPanelName = p.name FROM dbo.ref_panels AS P WHERE p.panelID = @panelIdx;			
			
			BEGIN TRAN;
			
				UPDATE p    
					SET  p.statusID = CASE @panelStatusID
										WHEN ''   THEN statusID
										ELSE COALESCE(@panelStatusID, statusID)
									  END,								  
						 p.feDspClientReferral = CASE @feDspClientReferral
										WHEN ''  THEN feDspClientReferral
										ELSE COALESCE(@feDspClientReferral, feDspClientReferral)
									  END  
									  
					FROM dbo.ref_panels AS P INNER JOIN #tmpReferralsToUpdate trp
						ON p.panelID = trp.panelID
				
				
				-- new data
				INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
				SELECT 'NEWVAL', ps.statusName, p.feDspClientReferral
				FROM dbo.ref_panels AS P
				INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
				LEFT OUTER JOIN dbo.ref_panels AS pp ON pp.panelID = p.panelParentID
				LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pg on pg.GLAccountID = p.GLAccountID
				LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pc on pc.GLAccountID = p.clientFeeGLAccountID
				WHERE p.panelID = @panelIdx;

				EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
				
				IF (@panelStatusID is not null AND @panelStatusID <> '' )
				BEGIN
					declare @parentPanelStatusID int, @statusName varchar(255), @parentPanelID int;

					SELECT @parentPanelStatusID = panelStatusID, @statusName = statusName 
					FROM dbo.ref_panelStatus 
					WHERE panelStatusID = @panelStatusID;
					
					IF @statusName = 'Active' BEGIN
						SELECT @msg = COALESCE(@msg + @crlf,'') + 'Sub-Panel [' + STRING_ESCAPE(p.[name],'json') + '] activated.'
						FROM dbo.ref_panels AS p
						INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
							AND ps.statusName = 'Inactive'
						WHERE p.panelParentID = @panelIdx;

						UPDATE p 
						SET p.statusID = @panelStatusID 
						FROM dbo.ref_panels AS p
						INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
							AND ps.statusName = 'Inactive'
						WHERE p.panelParentID = @panelIdx;
					END

					IF @statusName = 'Deleted' BEGIN
						SELECT @msg = COALESCE(@msg + @crlf,'') + 'Sub-Panel [' + STRING_ESCAPE([name],'json') + '] deleted.'
						FROM dbo.ref_panels
						WHERE panelParentID = @panelIdx 
						AND isActive = 1;

						UPDATE dbo.ref_panels 
						SET statusID = @panelStatusID 
						WHERE panelParentID = @panelIdx 
						AND isActive = 1;
					END		
				END
			COMMIT TRAN;
			-- audit log
			IF ISNULL(@msg,'') <> '' BEGIN
				SET @msg = 'Panel [' + STRING_ESCAPE(@oldPanelName,'json') + '] has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

				EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msg, @enteredByMemberID=@recordedByMemberID;
			END
			SET @panelID = @panelIdx;
			DELETE FROM #tmpReferralsToUpdate WHERE panelID = @panelIdx;			
			SELECT TOP 1 @panelIdx = rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;
			
		END
		
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
