(window.webpackJsonp = window.webpackJsonp || []).push([
	[38],
	{
		1878: function (t, e, n) {
			var r = n(30),
				o = n(1879);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[t.i, o, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const r = [];
								return (
									n.querySelectorAll(e).forEach((t) => r.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...t(e, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < e.length; r++) {
						const o = e[r];
						if (0 === r)
							o.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							o.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			t.exports = o.locals || {};
		},
		1879: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ink-signature{width:100%;height:100%;padding:16px}.ink-signature-canvas-container{display:flex;flex-direction:column;background:var(--signature-draw-background);border-radius:4px;border:1px solid var(--modal-stroke-and-border);padding:8px;height:200px}.ink-signature-canvas-container.full-signature{flex:1.5 0 0}.ink-signature-canvas-container.initials{flex:1 0 0}.ink-signature .ink-signature-canvas{height:144px;border-bottom:1px solid var(--gray-6)}@media(max-height:500px){.App:not(.is-web-component) .ink-signature .ink-signature-canvas{height:116px}}@container (max-height: 500px){.App.is-web-component .ink-signature .ink-signature-canvas{height:116px}}@media(max-height:320px){.App:not(.is-web-component) .ink-signature .ink-signature-canvas{height:86px}}@container (max-height: 320px){.App.is-web-component .ink-signature .ink-signature-canvas{height:86px}}.ink-signature-canvas{z-index:1;width:inherit;height:inherit;cursor:crosshair}.ink-signature-footer{display:flex;flex-direction:row;border-top:1px solid var(--modal-stroke-and-border);padding-top:7px}.ink-signature-sign-here{color:var(--faded-text);height:48px;text-align:center;font-size:10px;flex:2 0 0}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.ink-signature-sign-here{left:44.5%}}.ink-signature .canvas-colorpalette-container{padding:0;box-sizing:border-box;display:flex;flex-direction:column}.ink-signature .colorpalette-clear-container{display:flex;height:44px;justify-content:space-between}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1880: function (t, e, n) {
			var r = n(30),
				o = n(1881);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[t.i, o, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const r = [];
								return (
									n.querySelectorAll(e).forEach((t) => r.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...t(e, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < e.length; r++) {
						const o = e[r];
						if (0 === r)
							o.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							o.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			t.exports = o.locals || {};
		},
		1881: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.text-signature{width:100%;height:100%;padding:16px}.text-signature canvas{display:none}.text-signature-input{outline:none;width:100%;background:transparent;border:none;border-bottom:1px solid var(--gray-6);border-radius:0;text-align:center;height:85px}@media(max-height:500px){.App:not(.is-web-component) .text-signature-input{font-size:42px!important;height:50px}}@container (max-height: 500px){.App.is-web-component .text-signature-input{font-size:42px!important;height:50px}}@media(max-height:320px){.App:not(.is-web-component) .text-signature-input{font-size:34px!important}}@container (max-height: 320px){.App.is-web-component .text-signature-input{font-size:34px!important}}.text-signature-input:focus{border:none;border-bottom:1px solid var(--gray-6)}.text-signature-inner-container{position:absolute;height:100%;min-width:100%}.text-signature-text{display:flex;align-items:center;background:var(--signature-draw-background);white-space:nowrap;border-radius:4px;padding:0 8px;margin-top:10px;overflow-y:auto;flex-grow:1;visibility:hidden;position:fixed}.text-signature-text:hover{cursor:pointer}.text-signature-text .text-container{margin:auto;padding:0 20px}.text-signature label{margin-top:60px;width:100%}.text-signature .Dropdown__items{top:unset!important}.text-signature .Dropdown__items [data-testid=sig-no-result]{font-size:13px!important}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1882: function (t, e, n) {
			var r = n(30),
				o = n(1883);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[t.i, o, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const r = [];
								return (
									n.querySelectorAll(e).forEach((t) => r.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...t(e, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < e.length; r++) {
						const o = e[r];
						if (0 === r)
							o.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							o.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			t.exports = o.locals || {};
		},
		1883: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.image-signature{width:100%;height:100%;padding:16px}@media(max-height:500px){.App:not(.is-web-component) .image-signature{height:192px}}@container (max-height: 500px){.App.is-web-component .image-signature{height:192px}}@media(max-height:320px){.App:not(.is-web-component) .image-signature{height:162px}}@container (max-height: 320px){.App.is-web-component .image-signature{height:162px}}.image-signature .image-signature-modal-overlay{position:absolute;width:100%;height:480px;background:transparent;z-index:9999;left:0;top:-100px}.image-signature-image-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.image-signature-image-container img{max-height:100%;max-width:100%;width:auto;height:auto}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.image-signature-image-container img{flex-grow:1;flex-basis:0}}.image-signature-image-container .Button{position:absolute;bottom:0;right:0}.image-signature .mobile{background:var(--signature-draw-background);border:1px solid var(--modal-stroke-and-border)}.image-signature-upload-container{position:relative;border-radius:4px;border:1px dashed var(--modal-stroke-and-border);display:flex;flex-direction:column;justify-content:center;align-items:center;width:100%;height:100%}.image-signature-upload-container.dragging{background:var(--image-signature-drop-background);border:1px dashed var(--image-signature-drop-border)}.image-signature-dnd,.image-signature-separator{color:var(--faded-text)}.image-signature-upload input[type=file]{display:none}.image-signature-upload .pick-image-button{height:24px;border-radius:4px;border:1px solid var(--primary-button);color:var(--primary-button);display:flex;align-items:center;padding:0 11px;cursor:pointer}.image-signature-upload .pick-image-button:hover{border:1px solid var(--primary-button-hover);color:var(--primary-button-hover)}.image-signature-upload .pick-image-button.focus-visible,.image-signature-upload .pick-image-button:focus-visible{outline:var(--focus-visible-outline)}.image-signature-separator{margin:10px}.image-signature-error{position:absolute;color:red;bottom:0;right:0;margin:0 5px 5px 0}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1884: function (t, e, n) {
			var r = n(30),
				o = n(1885);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[t.i, o, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const r = [];
								return (
									n.querySelectorAll(e).forEach((t) => r.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...t(e, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < e.length; r++) {
						const o = e[r];
						if (0 === r)
							o.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							o.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			t.exports = o.locals || {};
		},
		1885: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SavedSignatures{width:100%;height:276px;padding:16px;overflow-y:auto;display:flex;grid-gap:16px;gap:16px;flex-direction:column}@media(max-height:500px){.App:not(.is-web-component) .SavedSignatures{height:192px}}@container (max-height: 500px){.App.is-web-component .SavedSignatures{height:192px}}@media(max-height:320px){.App:not(.is-web-component) .SavedSignatures{height:162px}}@container (max-height: 320px){.App.is-web-component .SavedSignatures{height:162px}}.SavedSignatures .signature-row{display:flex;flex-direction:column;justify-content:center;height:25%;width:100%}.SavedSignatures .signature-row .inputContainer{width:100%;display:grid;grid-gap:8px;gap:8px;align-items:center;grid-template-columns:20px 1fr}.SavedSignatures .signature-row .inputContainer input{width:16px;height:16px}.SavedSignatures .signature-row .inputContainer input[type=radio]:checked{accent-color:var(--blue-5)}.SavedSignatures .signature-row .inputContainer .contentContainer{padding-bottom:4px;margin-bottom:4px;border-bottom:1px solid var(--gray-5);display:grid;align-items:center;grid-template-columns:1fr 1fr 1fr}.SavedSignatures .signature-row .inputContainer .contentContainer .icon-button{justify-self:end;padding:0;border:none;background-color:transparent;display:flex;align-items:center;justify-content:center;width:34px;border-radius:4px;cursor:pointer}:host(:not([data-tabbing=true])) .SavedSignatures .signature-row .inputContainer .contentContainer .icon-button,html:not([data-tabbing=true]) .SavedSignatures .signature-row .inputContainer .contentContainer .icon-button{outline:none}.SavedSignatures .signature-row .inputContainer .contentContainer .icon-button:hover{background:var(--tools-overlay-button-hover)}.SavedSignatures .signature-row .inputContainer .contentContainer .radioButton{border-radius:100px;border:1px solid var(--blue-5);width:16px;height:16px;border-spacing:2px}.SavedSignatures .signature-row .inputContainer .contentContainer .radioButton.selected{background-color:var(--blue-5)}.SavedSignatures .signature-row .inputContainer .contentContainer .imgContainer{width:162px;height:26px}.SavedSignatures .signature-row .inputContainer .contentContainer .imgContainer img{max-width:100%;max-height:100%}.SavedSignatures .signature-row .labelContainer{display:grid;grid-template-columns:1fr 2fr;padding-left:28px;color:var(--gray-7)}.SavedSignatures .signature-row.no-initials .contentContainer{grid-template-columns:1fr 2fr}.SavedSignatures .emptyListContainer{height:100%;display:flex;align-items:center;justify-content:center;text-align:center}.SavedSignatures.empty{grid-template-rows:1fr}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1886: function (t, e, n) {
			var r = n(30),
				o = n(1887);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[t.i, o, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const r = [];
								return (
									n.querySelectorAll(e).forEach((t) => r.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...t(e, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < e.length; r++) {
						const o = e[r];
						if (0 === r)
							o.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							o.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			t.exports = o.locals || {};
		},
		1887: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				".open.SignatureModal{visibility:visible}.closed.SignatureModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignatureModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.SignatureModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.SignatureModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.SignatureModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.SignatureModal .footer .modal-button.cancel:hover,.SignatureModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.SignatureModal .footer .modal-button.cancel,.SignatureModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.SignatureModal .footer .modal-button.cancel.disabled,.SignatureModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.SignatureModal .footer .modal-button.cancel.disabled span,.SignatureModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.SignatureModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.SignatureModal .modal-container .wrapper .modal-content{padding:10px}.SignatureModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.SignatureModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.SignatureModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.SignatureModal .footer .modal-button.confirm{margin-left:4px}.SignatureModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .footer .modal-button{padding:23px 8px}}.SignatureModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .swipe-indicator{width:32px}}.SignatureModal .modal-container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.SignatureModal .modal-container .tab-list .tab-options-button{text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.SignatureModal .modal-container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.SignatureModal .modal-container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.SignatureModal .modal-container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.SignatureModal .modal-container .tab-list .tab-options-button.selected{cursor:default}.SignatureModal .modal-container .tab-list .tab-options-button.focus-visible,.SignatureModal .modal-container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .modal-container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.SignatureModal .modal-container .tab-panel.focus-visible,.SignatureModal .modal-container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.SignatureModal .modal-container{display:flex;flex-direction:column;justify-content:space-between;width:480px;padding:0;border-radius:4px;background:var(--component-background);overflow-y:visible}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container{width:100%}}.SignatureModal .modal-container.include-initials{width:664px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container{border-radius:0;height:440px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container{border-radius:0;height:440px}}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}.SignatureModal .modal-container .tabs-header-container{padding:16px 16px 0}.SignatureModal .modal-container .header{margin:0;display:flex;align-items:center;width:100%;height:24px}.SignatureModal .modal-container .header p{font-size:16px;font-weight:700;width:calc(100% - 32px);margin:0 16px 0 0}.SignatureModal .modal-container .header .signatureModalCloseButton{position:static;height:32px;width:32px}.SignatureModal .modal-container .header .signatureModalCloseButton:hover{background:var(--gray-2);border-radius:4px}.SignatureModal .modal-container .header .signatureModalCloseButton.selected{background:var(--view-header-button-active);cursor:default}.SignatureModal .modal-container .StylePopup{border-radius:0;box-shadow:none}.SignatureModal .modal-container .tab-panel{overflow-y:auto}.SignatureModal .modal-container .tab-list{font-size:14px}.SignatureModal .modal-container .tab-list .tab-options-button{padding:0;border:none;background-color:transparent}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .tab-list .tab-options-button,html:not([data-tabbing=true]) .SignatureModal .modal-container .tab-list .tab-options-button{outline:none}.SignatureModal .modal-container .signature-style-options{width:90%;display:flex}.SignatureModal .modal-container .signature-style-options .ColorPalette{margin:8px;grid-template-columns:repeat(3,1fr);width:100px}.SignatureModal .modal-container .signature-style-options .ColorPalette :not(:last-child){margin-right:8px}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:28px;height:28px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:22px;height:22px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container{width:22px;height:22px}}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:16px;height:16px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:14px;height:14px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer .cell{width:14px;height:14px}}.SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:28px;height:28px}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:22px;height:22px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .modal-container .signature-style-options .ColorPalette .cell-container .cell-outer.active{width:22px;height:22px}}.SignatureModal .modal-container .signature-clear{background-color:transparent;color:var(--secondary-button-text);padding:0 16px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:none;height:28px;width:63px;margin:auto 8px;font-size:13px}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .signature-clear,html:not([data-tabbing=true]) .SignatureModal .modal-container .signature-clear{outline:none}.SignatureModal .modal-container .signature-clear:enabled:hover{color:var(--secondary-button-hover)}.SignatureModal .modal-container .signature-clear:disabled{opacity:.5}.SignatureModal .modal-container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.SignatureModal .modal-container .footer .signature-create{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;padding:0 8px;height:32px;width:72px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .SignatureModal .modal-container .footer .signature-create,html:not([data-tabbing=true]) .SignatureModal .modal-container .footer .signature-create{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignatureModal .modal-container .footer .signature-create{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignatureModal .modal-container .footer .signature-create{font-size:13px}}.SignatureModal .modal-container .footer .signature-create:enabled:hover{background:var(--primary-button-hover)}.SignatureModal .modal-container .footer .signature-create:disabled{opacity:.5;cursor:default}.SignatureModal .modal-container .footer .signature-create.focus-visible,.SignatureModal .modal-container .footer .signature-create:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .modal-container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.SignatureModal .modal-container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.SignatureModal .modal-container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.SignatureModal .modal-container .tab-list .tab-options-button:hover+button,.SignatureModal .modal-container .tab-list .tab-options-button:hover+div{border-left:none}.SignatureModal .modal-container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.SignatureModal .modal-container .tab-list .tab-options-button.selected+button,.SignatureModal .modal-container .tab-list .tab-options-button.selected+div{border-left:none!important}.SignatureModal .modal-container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.SignatureModal .colorpalette-clear-container{display:flex;height:38px;box-sizing:border-box;justify-content:space-between;align-items:baseline;border-bottom-left-radius:4px;border-bottom-right-radius:4px}.SignatureModal .colorpalette-clear-container .signature-style-options{width:90%;display:flex}.SignatureModal .colorpalette-clear-container .signature-style-options .divider{display:inline-block;border-left:1px solid var(--modal-stroke-and-border);margin:auto 8px auto 16px;height:16px}.SignatureModal .colorpalette-clear-container .signature-style-options .placeholder-dropdown{width:160px;height:31px;margin:auto 0}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper{width:160px;height:31px;position:absolute;margin:8px 0 auto}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown{width:100%!important;height:100%;text-align:left}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .picked-option-text{margin-right:-18px;padding-left:2px;font-size:13px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .arrow{flex:unset}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items{bottom:auto;top:100%;width:100%;left:0;right:auto}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(n){font-size:15px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(2){font-size:13px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__items button:nth-child(4){font-size:18px}.SignatureModal .colorpalette-clear-container .signature-style-options .Dropdown__wrapper .Dropdown__item{display:block;padding-right:16px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;text-align:left}.SignatureModal .footer-signature-clear{padding:0;background-color:transparent;color:var(--secondary-button-text);display:flex;flex:0 0 0;align-items:baseline;justify-content:center;cursor:pointer;border:none;height:28px;width:63px;font-size:13px}:host(:not([data-tabbing=true])) .SignatureModal .footer-signature-clear,html:not([data-tabbing=true]) .SignatureModal .footer-signature-clear{outline:none}.SignatureModal .footer-signature-clear:enabled:hover{color:var(--secondary-button-hover)}.SignatureModal .footer-signature-clear:disabled{opacity:.5}.SignatureModal .footer-signature-clear.focus-visible,.SignatureModal .footer-signature-clear:focus-visible{outline:var(--focus-visible-outline)}.SignatureModal .signature-input{background:var(--signature-draw-background);border:1px solid var(--modal-stroke-and-border);display:flex;flex-direction:column;grid-gap:8px;gap:8px;align-items:center;justify-content:center;padding:8px;width:100%;height:200px;border-radius:4px}.SignatureModal .signature-input.full-signature{flex:1.5 0 0}.SignatureModal .signature-input.initials{flex:1 0 0}@media(max-height:500px){.App:not(.is-web-component) .SignatureModal .signature-input{height:116px}}@container (max-height: 500px){.App.is-web-component .SignatureModal .signature-input{height:116px}}@media(max-height:320px){.App:not(.is-web-component) .SignatureModal .signature-input{height:86px}}@container (max-height: 320px){.App.is-web-component .SignatureModal .signature-input{height:86px}}.SignatureModal .signature-input.image{height:244px}.SignatureModal .signature-input-footer{display:flex;flex-direction:row;width:100%}.SignatureModal .signature-input .signature-prompt{font-size:10px;color:var(--faded-text);flex:3 0 0;text-align:center}.SignatureModal .signature-and-initials-container{display:flex;flex-direction:row;grid-gap:8px;gap:8px}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2016: function (t, e, n) {
			"use strict";
			n.r(e);
			n(19),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(54),
				n(22),
				n(61),
				n(62),
				n(63),
				n(64),
				n(37),
				n(39),
				n(23),
				n(24),
				n(40),
				n(60);
			var r = n(0),
				o = n.n(r),
				a = n(17),
				i = n.n(a),
				l = n(6),
				c = n(347),
				u = n(136),
				s = n(196),
				d = n(3),
				p = n.n(d),
				f = n(200),
				h = n(144),
				g = n(184),
				m = n(78),
				b = n(118),
				v = n(1),
				y = n(106);
			n(1878);
			function x(t) {
				return (x =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function w() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ w =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					o = "function" == typeof Symbol ? Symbol : {},
					a = o.iterator || "@@iterator",
					i = o.asyncIterator || "@@asyncIterator",
					l = o.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, o) {
					var a = e && e.prototype instanceof p ? e : p,
						i = Object.create(a.prototype),
						l = new O(o || []);
					return r(i, "_invoke", { value: E(t, n, l) }), i;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var d = {};
				function p() {}
				function f() {}
				function h() {}
				var g = {};
				c(g, a, function () {
					return this;
				});
				var m = Object.getPrototypeOf,
					b = m && m(m(C([])));
				b && b !== e && n.call(b, a) && (g = b);
				var v = (h.prototype = p.prototype = Object.create(g));
				function y(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function S(t, e) {
					var o;
					r(this, "_invoke", {
						value: function (r, a) {
							function i() {
								return new e(function (o, i) {
									!(function r(o, a, i, l) {
										var c = s(t[o], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == x(d) && n.call(d, "__await")
												? e.resolve(d.__await).then(
														function (t) {
															r("next", t, i, l);
														},
														function (t) {
															r("throw", t, i, l);
														},
													)
												: e.resolve(d).then(
														function (t) {
															(u.value = t), i(u);
														},
														function (t) {
															return r("throw", t, i, l);
														},
													);
										}
										l(c.arg);
									})(r, a, o, i);
								});
							}
							return (o = o ? o.then(i, i) : i());
						},
					});
				}
				function E(t, e, n) {
					var r = "suspendedStart";
					return function (o, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === o) throw a;
							return j();
						}
						for (n.method = o, n.arg = a; ; ) {
							var i = n.delegate;
							if (i) {
								var l = k(i, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function k(t, e) {
					var n = e.method,
						r = t.iterator[n];
					if (void 0 === r)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								k(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var o = s(r, t.iterator, e.arg);
					if ("throw" === o.type)
						return (
							(e.method = "throw"), (e.arg = o.arg), (e.delegate = null), d
						);
					var a = o.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								d)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							d);
				}
				function A(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function M(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function O(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(A, this),
						this.reset(!0);
				}
				function C(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length; )
										if (n.call(t, r)) return (e.value = t[r]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (o.next = o);
						}
					}
					return { next: j };
				}
				function j() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = h),
					r(v, "constructor", { value: h, configurable: !0 }),
					r(h, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(h, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					y(S.prototype),
					c(S.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = S),
					(t.async = function (e, n, r, o, a) {
						void 0 === a && (a = Promise);
						var i = new S(u(e, n, r, o), a);
						return t.isGeneratorFunction(n)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					y(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var r in e) n.push(r);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in e) return (t.value = r), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = C),
					(O.prototype = {
						constructor: O,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(M),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function r(n, r) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = n),
									r && ((e.method = "next"), (e.arg = void 0)),
									!!r
								);
							}
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var a = this.tryEntries[o],
									i = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var o = this.tryEntries[r];
								if (
									o.tryLoc <= this.prev &&
									n.call(o, "finallyLoc") &&
									this.prev < o.finallyLoc
								) {
									var a = o;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								d
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), M(n), d;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var r = n.completion;
									if ("throw" === r.type) {
										var o = r.arg;
										M(n);
									}
									return o;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: C(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					t
				);
			}
			function S(t, e) {
				var n =
					("undefined" != typeof Symbol && t[Symbol.iterator]) ||
					t["@@iterator"];
				if (!n) {
					if (
						Array.isArray(t) ||
						(n = M(t)) ||
						(e && t && "number" == typeof t.length)
					) {
						n && (t = n);
						var r = 0,
							o = function () {};
						return {
							s: o,
							n: function () {
								return r >= t.length
									? { done: !0 }
									: { done: !1, value: t[r++] };
							},
							e: function (t) {
								throw t;
							},
							f: o,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var a,
					i = !0,
					l = !1;
				return {
					s: function () {
						n = n.call(t);
					},
					n: function () {
						var t = n.next();
						return (i = t.done), t;
					},
					e: function (t) {
						(l = !0), (a = t);
					},
					f: function () {
						try {
							i || null == n.return || n.return();
						} finally {
							if (l) throw a;
						}
					},
				};
			}
			function E(t, e, n, r, o, a, i) {
				try {
					var l = t[a](i),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(r, o);
			}
			function k(t) {
				return function () {
					var e = this,
						n = arguments;
					return new Promise(function (r, o) {
						var a = t.apply(e, n);
						function i(t) {
							E(a, r, o, i, l, "next", t);
						}
						function l(t) {
							E(a, r, o, i, l, "throw", t);
						}
						i(void 0);
					});
				};
			}
			function A(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (o = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(t, e) ||
					M(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function M(t, e) {
				if (t) {
					if ("string" == typeof t) return O(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return (
						"Object" === n && t.constructor && (n = t.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(t)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? O(t, e)
								: void 0
					);
				}
			}
			function O(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r;
			}
			var C = {
					isModalOpen: p.a.bool,
					isTabPanelSelected: p.a.bool,
					disableCreateButton: p.a.func,
					enableCreateButton: p.a.func,
					isInitialsModeEnabled: p.a.bool,
				},
				j = function (t) {
					var e,
						n = t.isModalOpen,
						a = t.isTabPanelSelected,
						i = t.disableCreateButton,
						l = t.enableCreateButton,
						u = t.isInitialsModeEnabled,
						d = void 0 !== u && u,
						p = Object(r.useRef)(),
						x = Object(r.useRef)(),
						E = Object(r.useRef)(),
						M = Object(r.useRef)(),
						O = A(Object(c.a)(), 1)[0],
						C = A(Object(r.useState)({}), 2),
						j = C[0],
						L = C[1],
						I = A(Object(r.useState)(!1), 2),
						_ = I[0],
						N = I[1],
						T = A(Object(r.useState)(!1), 2),
						D = T[0],
						P = T[1],
						R =
							((e = A(Object(r.useState)(!1), 2)[1]),
							function () {
								return e(function (t) {
									return !t;
								});
							});
					Object(r.useEffect)(function () {
						var t = v.a.getTool("AnnotationCreateSignature"),
							e = p.current;
						t.setSignatureCanvas(e);
						var n = window.Core.getCanvasMultiplier();
						e.getContext("2d").scale(n, n);
						var r = x.current;
						t.setInitialsCanvas(r), r.getContext("2d").scale(n, n);
					}, []),
						Object(r.useEffect)(
							function () {
								n && (F(), G());
							},
							[n],
						),
						Object(r.useEffect)(
							function () {
								function t() {
									return (t = k(
										w().mark(function t() {
											var e, r, o, i;
											return w().wrap(
												function (t) {
													for (;;)
														switch ((t.prev = t.next)) {
															case 0:
																if (!n || !a) {
																	t.next = 21;
																	break;
																}
																(e = v.a.getToolsFromAllDocumentViewers(
																	"AnnotationCreateSignature",
																)),
																	(r = S(e)),
																	(t.prev = 3),
																	r.s();
															case 5:
																if ((o = r.n()).done) {
																	t.next = 13;
																	break;
																}
																return (
																	(i = o.value).setSignature(E.current),
																	i.setInitials(M.current),
																	(t.next = 11),
																	i.resizeCanvas(b.a.FULL_SIGNATURE)
																);
															case 11:
																t.next = 5;
																break;
															case 13:
																t.next = 18;
																break;
															case 15:
																(t.prev = 15), (t.t0 = t.catch(3)), r.e(t.t0);
															case 18:
																return (t.prev = 18), r.f(), t.finish(18);
															case 21:
															case "end":
																return t.stop();
														}
												},
												t,
												null,
												[[3, 15, 18, 21]],
											);
										}),
									)).apply(this, arguments);
								}
								function e() {
									return (e = k(
										w().mark(function t() {
											var e, r, o, i;
											return w().wrap(
												function (t) {
													for (;;)
														switch ((t.prev = t.next)) {
															case 0:
																if (!(n && a && d)) {
																	t.next = 20;
																	break;
																}
																(e = v.a.getToolsFromAllDocumentViewers(
																	"AnnotationCreateSignature",
																)),
																	(r = S(e)),
																	(t.prev = 3),
																	r.s();
															case 5:
																if ((o = r.n()).done) {
																	t.next = 12;
																	break;
																}
																return (
																	(i = o.value).setInitials(M.current),
																	(t.next = 10),
																	i.resizeCanvas(b.a.INITIALS)
																);
															case 10:
																t.next = 5;
																break;
															case 12:
																t.next = 17;
																break;
															case 14:
																(t.prev = 14), (t.t0 = t.catch(3)), r.e(t.t0);
															case 17:
																return (t.prev = 17), r.f(), t.finish(17);
															case 20:
															case "end":
																return t.stop();
														}
												},
												t,
												null,
												[[3, 14, 17, 20]],
											);
										}),
									)).apply(this, arguments);
								}
								!(function () {
									t.apply(this, arguments);
								})(),
									(function () {
										e.apply(this, arguments);
									})(),
									d
										? E.current && M.current
											? l()
											: i()
										: E.current
											? l()
											: i();
							},
							[a, n, d],
						),
						Object(r.useEffect)(
							function () {
								function t() {
									return (t = k(
										w().mark(function t() {
											var e;
											return w().wrap(function (t) {
												for (;;)
													switch ((t.prev = t.next)) {
														case 0:
															if (!j.height || !j.width) {
																t.next = 7;
																break;
															}
															return (
																(e = v.a.getTool("AnnotationCreateSignature")),
																(t.next = 4),
																e.resizeCanvas()
															);
														case 4:
															if (!d) {
																t.next = 7;
																break;
															}
															return (t.next = 7), e.resizeCanvas(b.a.INITIALS);
														case 7:
														case "end":
															return t.stop();
													}
											}, t);
										}),
									)).apply(this, arguments);
								}
								!(function () {
									t.apply(this, arguments);
								})();
							},
							[j, d],
						),
						Object(r.useEffect)(
							function () {
								!_ || (d && !D) ? i() : l();
							},
							[D, _, d],
						);
					var F = Object(r.useCallback)(function () {
							v.a.getTool("AnnotationCreateSignature").clearSignatureCanvas(),
								(E.current = null),
								N(!1);
						}, []),
						G = Object(r.useCallback)(function () {
							v.a.getTool("AnnotationCreateSignature").clearInitialsCanvas(),
								(M.current = null),
								P(!1);
						}, []),
						z = (function () {
							var t = k(
								w().mark(function t() {
									var e;
									return w().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													return (
														(e = v.a.getTool("AnnotationCreateSignature")),
														(t.next = 3),
														e.isEmptySignature()
													);
												case 3:
													if (t.sent) {
														t.next = 6;
														break;
													}
													(E.current = B(
														e.getFullSignatureAnnotation().getPaths(),
													)),
														N(!0);
												case 6:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							);
							return function () {
								return t.apply(this, arguments);
							};
						})(),
						H = (function () {
							var t = k(
								w().mark(function t() {
									var e;
									return w().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													(e = v.a.getTool("AnnotationCreateSignature")),
														e.getInitialsAnnotation() &&
															((M.current = B(
																e.getInitialsAnnotation().getPaths(),
															)),
															P(!0));
												case 3:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							);
							return function () {
								return t.apply(this, arguments);
							};
						})(),
						B = function (t) {
							for (var e = [], n = 0; n < t.length; ++n)
								for (var r = 0; r < t[n].length; ++r)
									e[n] || (e[n] = []),
										(e[n][r] = new window.Core.Math.Point(
											t[n][r].x,
											t[n][r].y,
										));
							return e;
						},
						U = v.a.getTool("AnnotationCreateSignature").defaults,
						W = d ? {} : { display: "none" };
					return o.a.createElement(
						s.a,
						{
							bounds: !0,
							onResize: function (t) {
								var e = t.bounds;
								return L(e);
							},
						},
						function (t) {
							var e = t.measureRef;
							return o.a.createElement(
								"div",
								{ className: "ink-signature", ref: e },
								o.a.createElement(
									h.a,
									{
										onSwiping: function (t) {
											return t.event.stopPropagation();
										},
										className: "canvas-colorpalette-container",
									},
									o.a.createElement(
										"div",
										{ className: "signature-and-initials-container" },
										o.a.createElement(
											"div",
											{ className: "signature-input full-signature" },
											o.a.createElement("canvas", {
												className: "ink-signature-canvas",
												onMouseUp: z,
												onTouchEnd: z,
												onMouseLeave: z,
												ref: p,
											}),
											o.a.createElement(
												"div",
												{ className: "signature-input-footer" },
												o.a.createElement(
													"div",
													{ className: "signature-prompt" },
													O("option.signatureModal.drawSignature"),
												),
												o.a.createElement(
													"button",
													{
														className: "footer-signature-clear",
														onClick: F,
														disabled: !_,
														"aria-label": O("action.clearSignature"),
													},
													O("action.clear"),
												),
											),
										),
										o.a.createElement(
											"div",
											{ className: "signature-input initials", style: W },
											o.a.createElement("canvas", {
												className: "ink-signature-canvas",
												onMouseUp: H,
												onTouchEnd: H,
												onMouseLeave: H,
												ref: x,
											}),
											o.a.createElement(
												"div",
												{ className: "signature-input-footer" },
												o.a.createElement(
													"div",
													{ className: "signature-prompt" },
													O("option.signatureModal.drawInitial"),
												),
												o.a.createElement(
													"button",
													{
														className: "footer-signature-clear",
														onClick: G,
														disabled: !D,
														"aria-label": O("action.clearInitial"),
													},
													O("action.clear"),
												),
											),
										),
									),
									o.a.createElement(
										"div",
										{ className: "colorpalette-clear-container" },
										o.a.createElement(
											"div",
											{ className: "signature-style-options" },
											o.a.createElement(m.a, {
												id: "ink-signature-font-dropdown",
												disabled: !0,
												placeholder: "Text Styles",
											}),
											o.a.createElement("div", {
												className: "placeholder-dropdown",
											}),
											o.a.createElement("div", { className: "divider" }),
											o.a.createElement(g.a, {
												color: U.StrokeColor,
												property: "StrokeColor",
												onStyleChange: function (t, e) {
													return (function (t, e) {
														Object(f.a)("AnnotationCreateSignature", t, e);
														var n = v.a.getTool("AnnotationCreateSignature");
														n.getFullSignatureAnnotation() &&
															((n.getFullSignatureAnnotation().StrokeColor = e),
															n.resizeCanvas(b.a.FULL_SIGNATURE)),
															n.getInitialsAnnotation() &&
																((n.getInitialsAnnotation().StrokeColor = e),
																n.resizeCanvas(b.a.INITIALS)),
															R();
													})(t, e);
												},
												overridePalette2: [y.b.black, y.a[12], y.a[7]],
											}),
										),
									),
								),
							);
						},
					);
				};
			j.propTypes = C;
			var L = j,
				I = (n(84), n(41), n(36), n(46), n(52), n(154), n(109), n(38));
			n(130);
			var _ = function (t) {
					var e,
						n,
						r,
						o = t.getContext("2d"),
						a = t.width,
						i = t.height,
						l = { x: [], y: [] },
						c = o.getImageData(0, 0, t.width, t.height);
					for (n = 0; n < i; n++)
						for (e = 0; e < a; e++)
							(r = 4 * (n * a + e)),
								c.data[r + 3] > 0 && (l.x.push(e), l.y.push(n));
					l.x.sort(function (t, e) {
						return t - e;
					}),
						l.y.sort(function (t, e) {
							return t - e;
						});
					var u = l.x.length - 1;
					(a = 1 + l.x[u] - l.x[0]), (i = 1 + l.y[u] - l.y[0]);
					var s = o.getImageData(l.x[0], l.y[0], a, i);
					return (
						(t.width = a),
						(t.height = i),
						o.putImageData(s, 0, 0),
						t.toDataURL()
					);
				},
				N = n(4),
				T = (n(1880), n(21));
			function D(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (o = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return P(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return P(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function P(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r;
			}
			var R = {
					isModalOpen: p.a.bool,
					isTabPanelSelected: p.a.bool,
					disableCreateButton: p.a.func,
					enableCreateButton: p.a.func,
					isInitialsModeEnabled: p.a.bool,
				},
				F = y.b.black,
				G = window.Core.getCanvasMultiplier(),
				z = function (t) {
					return null == t
						? void 0
						: t
								.split(" ")
								.map(function (t) {
									return t[0];
								})
								.join("")
								.toUpperCase();
				},
				H = function (t, e, n) {
					var r = "".concat(e, "px ").concat(n),
						o = document.createElement("span");
					(o.id = "textSpan"),
						(o.style.display = "inline-block"),
						(o.style.visibility = "hidden"),
						(o.style.font = r);
					var a = Object(T.a)();
					window.isApryseWebViewerWebComponent
						? a.appendChild(o)
						: a.getElementsByTagName("body")[0].appendChild(o),
						(o.textContent = t);
					var i = o.getBoundingClientRect().width;
					return o.remove(), i;
				},
				B = function (t, e) {
					for (var n, r = 0, o = 48; r <= o; ) {
						(n = Math.floor((r + o) / 2)),
							H(t, n, e) > 350 ? (o = n - 1) : (r = n + 1);
					}
					return n;
				},
				U = function (t, e) {
					var n =
							arguments.length > 2 && void 0 !== arguments[2]
								? arguments[2]
								: 0,
						r = e.current.getBoundingClientRect(),
						o = r.width,
						a = r.height;
					(o += 100),
						(a += 100),
						(t.style.width = "".concat(o, "px")),
						(t.style.height = "".concat(a, "px")),
						(t.width = o * Math.max(n, G)),
						(t.height = a * Math.max(n, G));
				},
				W = function (t) {
					var e = t.canvas,
						n = t.text,
						r = t.selectedFontFamily,
						o = t.fontColor,
						a = t.overrideMultiplier,
						i = void 0 === a ? 0 : a,
						l = e.getContext("2d"),
						c = B(n, r);
					(l.fillStyle = o),
						(l.textAlign = "center"),
						(l.textBaseline = "middle"),
						(l.font = "".concat(c * Math.max(i, G), "px ").concat(r));
				},
				V = function (t, e) {
					var n = t.getContext("2d"),
						r = t.width,
						o = t.height;
					n.clearRect(0, 0, r, o), n.fillText(e, r / 2, o / 2);
				},
				q = function (t) {
					var e,
						n = t.isModalOpen,
						a = t.isTabPanelSelected,
						u = t.disableCreateButton,
						s = t.enableCreateButton,
						d = t.isInitialsModeEnabled,
						p = void 0 !== d && d,
						f = Object(l.e)(function (t) {
							return N.a.getSignatureFonts(t);
						}),
						h = Object(l.e)(function (t) {
							return N.a.getTextSignatureQuality(t);
						}),
						b = D(Object(r.useState)(""), 2),
						x = b[0],
						w = b[1],
						S = D(Object(r.useState)(""), 2),
						E = S[0],
						k = S[1],
						A = D(Object(r.useState)(!0), 2),
						M = A[0],
						O = A[1],
						C = D(Object(r.useState)(new window.Core.Annotations.Color(F)), 2),
						j = C[0],
						L = C[1],
						T = D(Object(r.useState)(48), 2),
						P = T[0],
						R = T[1],
						G = Object(r.useRef)(),
						H = Object(r.useRef)(),
						q = Object(r.useRef)(),
						Y = Object(r.useRef)(),
						$ = Object(r.useRef)(),
						K = D(Object(c.a)(), 1)[0],
						J = D(Object(r.useState)(f[0]), 2),
						Q = J[0],
						Z = J[1],
						X =
							((e = D(Object(r.useState)(!1), 2)[1]),
							function () {
								return e(function (t) {
									return !t;
								});
							});
					Object(r.useEffect)(
						function () {
							(null == x ? void 0 : x.length) > 0 && (!p || E) ? s() : u();
						},
						[E, x, p],
					),
						Object(r.useEffect)(
							function () {
								f.includes(Q) || Z(f[0]);
							},
							[Q, f],
						),
						Object(r.useEffect)(
							function () {
								var t = q.current;
								a &&
									(U(t, $),
									W({
										canvas: t,
										text: E,
										selectedFontFamily: Q,
										fontColor: j,
									}),
									V(t, E),
									n && et());
							},
							[a, E, f, j, Q],
						),
						Object(r.useEffect)(
							function () {
								var t = H.current;
								a &&
									(U(t, Y, h),
									W({
										canvas: t,
										text: x,
										selectedFontFamily: Q,
										fontColor: j,
										overrideMultiplier: h,
									}),
									V(t, x),
									n && tt());
							},
							[a, x, f, j, Q],
						),
						Object(r.useEffect)(
							function () {
								if ((L(j), n && a)) {
									var t = v.a.getDisplayAuthor(v.a.getCurrentUser());
									w(t), k(z(t)), tt();
								}
							},
							[n, a],
						),
						Object(r.useEffect)(
							function () {
								var t;
								a &&
									(null === (t = G.current) || void 0 === t || t.focus(),
									I.h
										? G.current.setSelectionRange(0, 9999)
										: G.current.select());
							},
							[a],
						),
						Object(r.useEffect)(
							function () {
								var t = function () {
									if (M) {
										var t = v.a.getDisplayAuthor(v.a.getCurrentUser());
										w(t), k(z(t)), s();
									}
								};
								return (
									v.a.addEventListener("updateAnnotationPermission", t),
									function () {
										v.a.removeEventListener("updateAnnotationPermission", t);
									}
								);
							},
							[M],
						);
					var tt = function () {
							var t = v.a.getToolsFromAllDocumentViewers(
									"AnnotationCreateSignature",
								),
								e = H.current;
							if ((x || "").trim()) {
								var n = _(e);
								t.forEach(function (t) {
									return t.setSignature(n);
								});
							} else
								t.forEach(function (t) {
									return t.setSignature(null);
								});
						},
						et = function () {
							var t = v.a.getToolsFromAllDocumentViewers(
									"AnnotationCreateSignature",
								),
								e = q.current;
							if ((E || "").trim()) {
								var n = _(e);
								t.forEach(function (t) {
									return t.setInitials(n);
								});
							} else
								t.forEach(function (t) {
									return t.setInitials(null);
								});
						},
						nt = function (t) {
							Z(t);
							var e = B(x, t);
							R(e);
						},
						rt = !(n && a),
						ot = p ? {} : { display: "none" };
					return o.a.createElement(
						"div",
						{ className: "text-signature" },
						o.a.createElement(
							"div",
							{ className: "signature-and-initials-container" },
							o.a.createElement(
								"div",
								{ className: "signature-input full-signature" },
								o.a.createElement(
									"label",
									null,
									o.a.createElement("input", {
										className: "text-signature-input",
										ref: G,
										"aria-label": K("option.signatureModal.typeSignature"),
										type: "text",
										value: x,
										onChange: function (t) {
											O(!1);
											var e = t.target.value.replace(/^\s+/g, "");
											tt(), w(e), k(z(e));
											var n = B(e, Q);
											R(n);
										},
										style: {
											fontFamily: Q || f,
											fontSize: P,
											color: j.toHexString(),
										},
										disabled: rt,
									}),
								),
								o.a.createElement(
									"div",
									{ className: "signature-input-footer" },
									o.a.createElement(
										"div",
										{ className: "signature-prompt" },
										K("option.signatureModal.typeSignature"),
									),
									o.a.createElement(
										"button",
										{
											className: "footer-signature-clear",
											"aria-label": K("action.clearSignature"),
											onClick: function () {
												w(""), G.current.focus();
											},
											disabled: rt || 0 === x.length,
										},
										K("action.clear"),
									),
								),
							),
							o.a.createElement(
								"div",
								{ className: "signature-input initials", style: ot },
								o.a.createElement(
									"label",
									null,
									o.a.createElement("input", {
										className: "text-signature-input",
										type: "text",
										value: E,
										"aria-label": K("option.signatureModal.typeInitial"),
										onChange: function (t) {
											O(!1);
											var e = t.target.value.replace(/^\s+/g, "");
											tt(), k(e);
										},
										style: {
											fontFamily: Q || f,
											fontSize: P,
											color: j.toHexString(),
										},
										disabled: rt,
									}),
								),
								o.a.createElement(
									"div",
									{ className: "signature-input-footer" },
									o.a.createElement(
										"div",
										{ className: "signature-prompt" },
										K("option.signatureModal.typeInitial"),
									),
									o.a.createElement(
										"button",
										{
											className: "footer-signature-clear",
											"aria-label": K("action.clearInitial"),
											onClick: function () {
												return k("");
											},
											disabled: rt || 0 === E.length,
										},
										K("action.clear"),
									),
								),
							),
						),
						o.a.createElement(
							"div",
							{
								className: i()({ "text-signature-text": !0 }),
								style: { fontFamily: Q, fontSize: 72, color: j.toHexString() },
							},
							o.a.createElement(
								"div",
								{ className: "text-container", ref: Y },
								x,
							),
							o.a.createElement(
								"div",
								{ className: "text-container", ref: $ },
								E,
							),
						),
						o.a.createElement("canvas", { ref: H }),
						o.a.createElement("canvas", { ref: q }),
						o.a.createElement(
							"div",
							{ className: "colorpalette-clear-container" },
							o.a.createElement(
								"div",
								{ className: "signature-style-options" },
								"" !== x || ((!p || "" !== E) && p)
									? o.a.createElement(m.a, {
											id: "text-signature-font-dropdown",
											items: f.map(function (t) {
												return {
													font: t,
													value: "".concat(x, " ").concat(p ? E : ""),
												};
											}),
											getCustomItemStyle: function (t) {
												return { fontFamily: t.font };
											},
											getKey: function (t) {
												return t.font;
											},
											translationPrefix: "option.signatureModal.textSignature",
											showLabelInList: !0,
											getDisplayValue: function (t) {
												return t.value || t.font;
											},
											onClickItem: nt,
											currentSelectionKey: Q || f[0],
											maxHeight: Object(I.k)() ? 80 : null,
											dataElement: "text-signature-font-dropdown",
										})
									: o.a.createElement(m.a, {
											id: "text-signature-font-dropdown",
											disabled: !0,
											placeholder: K("option.signatureModal.fontStyle"),
										}),
								o.a.createElement("div", { className: "placeholder-dropdown" }),
								o.a.createElement("div", { className: "divider" }),
								o.a.createElement(g.a, {
									color: j,
									property: "fontColor",
									onStyleChange: function (t, e) {
										return (function (t, e) {
											L(e), X();
										})(0, e);
									},
									overridePalette2: [y.b.black, y.a[12], y.a[7]],
								}),
							),
						),
					);
				};
			q.propTypes = R;
			var Y = q,
				$ = (n(102), n(79), n(33));
			n(1882);
			function K(t) {
				return (K =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function J() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ J =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					o = "function" == typeof Symbol ? Symbol : {},
					a = o.iterator || "@@iterator",
					i = o.asyncIterator || "@@asyncIterator",
					l = o.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, o) {
					var a = e && e.prototype instanceof p ? e : p,
						i = Object.create(a.prototype),
						l = new A(o || []);
					return r(i, "_invoke", { value: w(t, n, l) }), i;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var d = {};
				function p() {}
				function f() {}
				function h() {}
				var g = {};
				c(g, a, function () {
					return this;
				});
				var m = Object.getPrototypeOf,
					b = m && m(m(M([])));
				b && b !== e && n.call(b, a) && (g = b);
				var v = (h.prototype = p.prototype = Object.create(g));
				function y(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function x(t, e) {
					var o;
					r(this, "_invoke", {
						value: function (r, a) {
							function i() {
								return new e(function (o, i) {
									!(function r(o, a, i, l) {
										var c = s(t[o], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == K(d) && n.call(d, "__await")
												? e.resolve(d.__await).then(
														function (t) {
															r("next", t, i, l);
														},
														function (t) {
															r("throw", t, i, l);
														},
													)
												: e.resolve(d).then(
														function (t) {
															(u.value = t), i(u);
														},
														function (t) {
															return r("throw", t, i, l);
														},
													);
										}
										l(c.arg);
									})(r, a, o, i);
								});
							}
							return (o = o ? o.then(i, i) : i());
						},
					});
				}
				function w(t, e, n) {
					var r = "suspendedStart";
					return function (o, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === o) throw a;
							return O();
						}
						for (n.method = o, n.arg = a; ; ) {
							var i = n.delegate;
							if (i) {
								var l = S(i, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function S(t, e) {
					var n = e.method,
						r = t.iterator[n];
					if (void 0 === r)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								S(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var o = s(r, t.iterator, e.arg);
					if ("throw" === o.type)
						return (
							(e.method = "throw"), (e.arg = o.arg), (e.delegate = null), d
						);
					var a = o.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								d)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							d);
				}
				function E(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function k(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function A(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(E, this),
						this.reset(!0);
				}
				function M(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length; )
										if (n.call(t, r)) return (e.value = t[r]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (o.next = o);
						}
					}
					return { next: O };
				}
				function O() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = h),
					r(v, "constructor", { value: h, configurable: !0 }),
					r(h, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(h, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					y(x.prototype),
					c(x.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = x),
					(t.async = function (e, n, r, o, a) {
						void 0 === a && (a = Promise);
						var i = new x(u(e, n, r, o), a);
						return t.isGeneratorFunction(n)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					y(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var r in e) n.push(r);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in e) return (t.value = r), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = M),
					(A.prototype = {
						constructor: A,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(k),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function r(n, r) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = n),
									r && ((e.method = "next"), (e.arg = void 0)),
									!!r
								);
							}
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var a = this.tryEntries[o],
									i = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var o = this.tryEntries[r];
								if (
									o.tryLoc <= this.prev &&
									n.call(o, "finallyLoc") &&
									this.prev < o.finallyLoc
								) {
									var a = o;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								d
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), k(n), d;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var r = n.completion;
									if ("throw" === r.type) {
										var o = r.arg;
										k(n);
									}
									return o;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: M(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					t
				);
			}
			function Q(t, e, n, r, o, a, i) {
				try {
					var l = t[a](i),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(r, o);
			}
			function Z(t) {
				return function () {
					var e = this,
						n = arguments;
					return new Promise(function (r, o) {
						var a = t.apply(e, n);
						function i(t) {
							Q(a, r, o, i, l, "next", t);
						}
						function l(t) {
							Q(a, r, o, i, l, "throw", t);
						}
						i(void 0);
					});
				};
			}
			function X(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (o = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return tt(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return tt(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function tt(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r;
			}
			var et = {
					isModalOpen: p.a.bool,
					isTabPanelSelected: p.a.bool,
					disableCreateButton: p.a.func,
					enableCreateButton: p.a.func,
					isInitialsModeEnabled: p.a.bool,
				},
				nt = "fullSignature",
				rt = "initials",
				ot = ["png", "jpg", "jpeg"],
				at = null;
			function it(t) {
				return new Promise(function (e, n) {
					var r = new FileReader();
					(r.onload = function (r) {
						var o = r.target.result;
						ot.some(function (t) {
							return -1 !== o.indexOf("image/".concat(t));
						})
							? e({ imageSource: o, fileSize: t.size })
							: n(
									$.a.t("message.imageSignatureAcceptedFileTypes", {
										acceptedFileTypes: ot.join(", "),
									}),
								);
					}),
						r.readAsDataURL(t);
				});
			}
			var lt = function (t) {
				var e = t.isModalOpen,
					n = t.isTabPanelSelected,
					a = t.disableCreateButton,
					l = t.enableCreateButton,
					u = t.isInitialsModeEnabled,
					s = void 0 !== u && u,
					d = X(Object(r.useState)(null), 2),
					p = d[0],
					f = d[1],
					h = X(Object(r.useState)(null), 2),
					g = h[0],
					m = h[1],
					b = X(Object(r.useState)(null), 2),
					y = b[0],
					x = b[1],
					w = X(Object(r.useState)(null), 2),
					S = w[0],
					E = w[1],
					k = X(Object(r.useState)(""), 2),
					A = k[0],
					M = k[1],
					O = X(Object(r.useState)(null), 2),
					C = O[0],
					j = O[1],
					L = X(Object(r.useState)(null), 2),
					_ = L[0],
					N = L[1],
					T = Object(r.useRef)(),
					D = Object(r.useRef)(),
					P = X(Object(c.a)(), 1)[0];
				Object(r.useEffect)(
					function () {
						var t = v.a.getToolsFromAllDocumentViewers(
							"AnnotationCreateSignature",
						);
						(at = t[0].ACCEPTED_FILE_SIZE),
							e
								? e &&
									n &&
									(t.forEach(function (t) {
										return t.setSignature(p, g);
									}),
									t.forEach(function (t) {
										return t.setInitials(y, S);
									}),
									!p || (s && !y) ? a() : l())
								: (f(null), x(null), E(null), E(null));
					},
					[p, y, n, e, S, g, s],
				);
				var R = function (t) {
						W(t.target.files[0]);
					},
					F = function (t) {
						q(t.target.files[0]);
					},
					G = Object(r.useCallback)(function (t, e) {
						t.preventDefault(), M(e);
					}, []),
					z = Object(r.useCallback)(function (t) {
						t.preventDefault();
					}, []),
					H = Object(r.useCallback)(function (t) {
						t.preventDefault(),
							t.target.parentNode.contains(t.relatedTarget) || M(null);
					}, []),
					B = Object(r.useCallback)(function (t) {
						t.preventDefault(), M(null);
					}, []),
					U = function (t) {
						t.preventDefault(), M(null);
						var e = t.dataTransfer.files;
						e.length && W(e[0]);
					},
					W = (function () {
						var t = Z(
							J().mark(function t(e) {
								var n, r, o;
								return J().wrap(
									function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													return (t.prev = 0), (t.next = 3), it(e);
												case 3:
													(n = t.sent),
														(r = n.imageSource),
														(o = n.fileSize),
														j(""),
														f(r),
														m(o),
														(t.next = 13);
													break;
												case 10:
													(t.prev = 10), (t.t0 = t.catch(0)), j(t.t0);
												case 13:
												case "end":
													return t.stop();
											}
									},
									t,
									null,
									[[0, 10]],
								);
							}),
						);
						return function (e) {
							return t.apply(this, arguments);
						};
					})(),
					V = function (t) {
						t.preventDefault(), M(null);
						var e = t.dataTransfer.files;
						e.length && q(e[0]);
					},
					q = (function () {
						var t = Z(
							J().mark(function t(e) {
								var n, r, o;
								return J().wrap(
									function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													return (t.prev = 0), (t.next = 3), it(e);
												case 3:
													(n = t.sent),
														(r = n.imageSource),
														(o = n.fileSize),
														N(""),
														x(r),
														E(o),
														(t.next = 13);
													break;
												case 10:
													(t.prev = 10), (t.t0 = t.catch(0)), N(t.t0);
												case 13:
												case "end":
													return t.stop();
											}
									},
									t,
									null,
									[[0, 10]],
								);
							}),
						);
						return function (e) {
							return t.apply(this, arguments);
						};
					})(),
					Y = Object(r.useCallback)(
						function (t) {
							G(t, nt);
						},
						[G],
					),
					$ = Object(r.useCallback)(
						function (t) {
							H(t, nt);
						},
						[H],
					),
					K = Object(r.useCallback)(
						function (t) {
							G(t, rt);
						},
						[G],
					),
					Q = Object(r.useCallback)(
						function (t) {
							H(t, rt);
						},
						[H],
					),
					tt = function () {
						return Object(I.k)()
							? o.a.createElement(
									"div",
									{ className: "image-signature-separator" },
									P("option.signatureModal.selectImage"),
								)
							: o.a.createElement(
									o.a.Fragment,
									null,
									o.a.createElement(
										"div",
										{ className: "image-signature-dnd" },
										P("option.signatureModal.dragAndDrop"),
									),
									o.a.createElement(
										"div",
										{ className: "image-signature-separator" },
										P("option.signatureModal.or"),
									),
								);
					},
					et = "number" == typeof at && at > 0,
					lt = !et || g < at,
					ct = !et || S < at,
					ut = i()("image-signature-upload-container", {
						mobile: Object(I.k)(),
						dragging: A === nt,
					}),
					st = i()("image-signature-upload-container", {
						mobile: Object(I.k)(),
						dragging: A === rt,
					}),
					dt = s ? {} : { display: "none" };
				return o.a.createElement(
					"div",
					{ className: "image-signature" },
					o.a.createElement(
						"div",
						{ className: "signature-and-initials-container" },
						o.a.createElement(
							"div",
							{ className: "signature-input image full-signature" },
							p && lt
								? o.a.createElement("img", {
										src: p,
										alt: P("option.signatureModal.imageSignature"),
										style: { maxWidth: "100%", maxHeight: "100%" },
									})
								: o.a.createElement(
										"div",
										{
											className: ut,
											onDragEnter: Y,
											onDragLeave: $,
											onDragOver: z,
											onDrop: U,
											onDragExit: B,
										},
										tt(),
										o.a.createElement(
											"div",
											{ className: "image-signature-upload" },
											o.a.createElement("input", {
												ref: T,
												id: "upload",
												type: "file",
												accept: ot
													.map(function (t) {
														return ".".concat(t);
													})
													.join(","),
												onChange: R,
												disabled: !(e && n),
											}),
											o.a.createElement(
												"button",
												{
													onClick: function () {
														return T.current.click();
													},
													className: "pick-image-button",
												},
												P("option.signatureModal.pickImage"),
											),
										),
										C &&
											o.a.createElement(
												"div",
												{ className: "image-signature-error" },
												C,
											),
									),
						),
						o.a.createElement(
							"div",
							{ className: "signature-input image initials", style: dt },
							y && ct
								? o.a.createElement("img", {
										src: y,
										alt: P("option.signatureModal.imageInitial"),
										style: { maxWidth: "100%", maxHeight: "100%" },
									})
								: o.a.createElement(
										"div",
										{
											className: st,
											onDragEnter: K,
											onDragLeave: Q,
											onDragOver: z,
											onDrop: V,
											onDragExit: B,
										},
										tt(),
										o.a.createElement(
											"div",
											{ className: "image-signature-upload" },
											o.a.createElement("input", {
												ref: D,
												id: "upload",
												type: "file",
												accept: ot
													.map(function (t) {
														return ".".concat(t);
													})
													.join(","),
												onChange: F,
												disabled: !(e && n),
											}),
											o.a.createElement(
												"button",
												{
													onClick: function () {
														return D.current.click();
													},
													className: "pick-image-button",
												},
												P("option.signatureModal.pickInitialsFile"),
											),
										),
										_ &&
											o.a.createElement(
												"div",
												{ className: "image-signature-error" },
												_,
											),
									),
						),
					),
				);
			};
			lt.propTypes = et;
			var ct = lt,
				ut = n(2),
				st = (n(1884), n(73)),
				dt = n(43);
			function pt(t) {
				return (pt =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function ft() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ ft =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					o = "function" == typeof Symbol ? Symbol : {},
					a = o.iterator || "@@iterator",
					i = o.asyncIterator || "@@asyncIterator",
					l = o.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, o) {
					var a = e && e.prototype instanceof p ? e : p,
						i = Object.create(a.prototype),
						l = new A(o || []);
					return r(i, "_invoke", { value: w(t, n, l) }), i;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var d = {};
				function p() {}
				function f() {}
				function h() {}
				var g = {};
				c(g, a, function () {
					return this;
				});
				var m = Object.getPrototypeOf,
					b = m && m(m(M([])));
				b && b !== e && n.call(b, a) && (g = b);
				var v = (h.prototype = p.prototype = Object.create(g));
				function y(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function x(t, e) {
					var o;
					r(this, "_invoke", {
						value: function (r, a) {
							function i() {
								return new e(function (o, i) {
									!(function r(o, a, i, l) {
										var c = s(t[o], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == pt(d) && n.call(d, "__await")
												? e.resolve(d.__await).then(
														function (t) {
															r("next", t, i, l);
														},
														function (t) {
															r("throw", t, i, l);
														},
													)
												: e.resolve(d).then(
														function (t) {
															(u.value = t), i(u);
														},
														function (t) {
															return r("throw", t, i, l);
														},
													);
										}
										l(c.arg);
									})(r, a, o, i);
								});
							}
							return (o = o ? o.then(i, i) : i());
						},
					});
				}
				function w(t, e, n) {
					var r = "suspendedStart";
					return function (o, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === o) throw a;
							return O();
						}
						for (n.method = o, n.arg = a; ; ) {
							var i = n.delegate;
							if (i) {
								var l = S(i, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function S(t, e) {
					var n = e.method,
						r = t.iterator[n];
					if (void 0 === r)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								S(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var o = s(r, t.iterator, e.arg);
					if ("throw" === o.type)
						return (
							(e.method = "throw"), (e.arg = o.arg), (e.delegate = null), d
						);
					var a = o.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								d)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							d);
				}
				function E(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function k(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function A(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(E, this),
						this.reset(!0);
				}
				function M(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length; )
										if (n.call(t, r)) return (e.value = t[r]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (o.next = o);
						}
					}
					return { next: O };
				}
				function O() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = h),
					r(v, "constructor", { value: h, configurable: !0 }),
					r(h, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(h, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					y(x.prototype),
					c(x.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = x),
					(t.async = function (e, n, r, o, a) {
						void 0 === a && (a = Promise);
						var i = new x(u(e, n, r, o), a);
						return t.isGeneratorFunction(n)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					y(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var r in e) n.push(r);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in e) return (t.value = r), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = M),
					(A.prototype = {
						constructor: A,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(k),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function r(n, r) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = n),
									r && ((e.method = "next"), (e.arg = void 0)),
									!!r
								);
							}
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var a = this.tryEntries[o],
									i = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var o = this.tryEntries[r];
								if (
									o.tryLoc <= this.prev &&
									n.call(o, "finallyLoc") &&
									this.prev < o.finallyLoc
								) {
									var a = o;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								d
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), k(n), d;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var r = n.completion;
									if ("throw" === r.type) {
										var o = r.arg;
										k(n);
									}
									return o;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: M(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					t
				);
			}
			function ht(t, e) {
				var n =
					("undefined" != typeof Symbol && t[Symbol.iterator]) ||
					t["@@iterator"];
				if (!n) {
					if (
						Array.isArray(t) ||
						(n = bt(t)) ||
						(e && t && "number" == typeof t.length)
					) {
						n && (t = n);
						var r = 0,
							o = function () {};
						return {
							s: o,
							n: function () {
								return r >= t.length
									? { done: !0 }
									: { done: !1, value: t[r++] };
							},
							e: function (t) {
								throw t;
							},
							f: o,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var a,
					i = !0,
					l = !1;
				return {
					s: function () {
						n = n.call(t);
					},
					n: function () {
						var t = n.next();
						return (i = t.done), t;
					},
					e: function (t) {
						(l = !0), (a = t);
					},
					f: function () {
						try {
							i || null == n.return || n.return();
						} finally {
							if (l) throw a;
						}
					},
				};
			}
			function gt(t, e, n, r, o, a, i) {
				try {
					var l = t[a](i),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(r, o);
			}
			function mt(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (o = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(t, e) ||
					bt(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function bt(t, e) {
				if (t) {
					if ("string" == typeof t) return vt(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return (
						"Object" === n && t.constructor && (n = t.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(t)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? vt(t, e)
								: void 0
					);
				}
			}
			function vt(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r;
			}
			var yt = function (t) {
					var e = t.selectedIndex,
						n = t.setSelectedIndex,
						r = Object(l.d)(),
						a = Object(c.a)().t,
						u = mt(
							Object(l.e)(function (t) {
								return [
									N.a.getDisplayedSignatures(t),
									N.a.getSelectedDisplayedSignatureIndex(t),
									N.a.isElementDisabled(t, "defaultSignatureDeleteButton"),
									N.a.getSavedInitials(t),
									N.a.getSelectedDisplayedInitialsIndex(t),
									N.a.getSignatureMode(t),
									N.a.getInitialsOffset(t),
								];
							}),
							7,
						),
						s = u[0],
						d = u[1],
						p = u[2],
						f = u[3],
						h = u[4],
						g = u[5],
						m = u[6],
						y = v.a.getToolsFromAllDocumentViewers("AnnotationCreateSignature"),
						x = (function () {
							var t,
								e =
									((t = ft().mark(function t(e) {
										var n, o, a, i, l, c, u;
										return ft().wrap(function (t) {
											for (;;)
												switch ((t.prev = t.next)) {
													case 0:
														if (
															((n = g === b.a.FULL_SIGNATURE),
															(a = (o = e - m) < 0))
														)
															r(ut.a.setInitialsOffset(m - 1));
														else if (
															(y[0].deleteSavedInitials(o), h === o && !a)
														) {
															r(ut.a.setSelectedDisplayedInitialsIndex(0)),
																(i = ht(y));
															try {
																for (i.s(); !(l = i.n()).done; )
																	l.value.hidePreview();
															} catch (t) {
																i.e(t);
															} finally {
																i.f();
															}
															v.a.setToolMode(st.a);
														}
														if ((y[0].deleteSavedSignature(e), d === e)) {
															r(ut.a.setSelectedDisplayedSignatureIndex(0)),
																(c = ht(y));
															try {
																for (c.s(); !(u = c.n()).done; )
																	u.value.hidePreview();
															} catch (t) {
																c.e(t);
															} finally {
																c.f();
															}
															v.a.setToolMode(st.a);
														} else
															(n ? e < d : o < h) &&
																r(
																	ut.a.setSelectedDisplayedSignatureIndex(
																		d - 1,
																	),
																);
													case 7:
													case "end":
														return t.stop();
												}
										}, t);
									})),
									function () {
										var e = this,
											n = arguments;
										return new Promise(function (r, o) {
											var a = t.apply(e, n);
											function i(t) {
												gt(a, r, o, i, l, "next", t);
											}
											function l(t) {
												gt(a, r, o, i, l, "throw", t);
											}
											i(void 0);
										});
									});
							return function (t) {
								return e.apply(this, arguments);
							};
						})(),
						w = new Array(m).concat(f);
					return o.a.createElement(
						"div",
						{
							className: i()("SavedSignatures", { empty: !s || s.length < 1 }),
						},
						s.length
							? s.map(function (t, r) {
									return o.a.createElement(
										"div",
										{
											key: r,
											className: i()("signature-row", {
												active: e === r,
												"no-initials": !(null != w && w[r]),
											}),
											onClick: function () {
												return n(r);
											},
										},
										o.a.createElement(
											"div",
											{ className: "inputContainer" },
											o.a.createElement("input", {
												type: "radio",
												onChange: function () {
													return n(r);
												},
												checked: r === e,
											}),
											o.a.createElement(
												"div",
												{ className: "contentContainer" },
												o.a.createElement(
													"div",
													{ className: "imgContainer" },
													o.a.createElement("img", {
														alt: a("option.toolsOverlay.signatureAltText"),
														src: s[r].imgSrc,
													}),
												),
												(null == w ? void 0 : w[r]) &&
													o.a.createElement(
														"div",
														{ className: "imgContainer" },
														o.a.createElement("img", {
															alt: a("option.toolsOverlay.signatureAltText"),
															src: w[r].imgSrc,
														}),
													),
												!p &&
													o.a.createElement(
														"button",
														{
															className: "icon-button",
															"data-element": "defaultSignatureDeleteButton",
															onClick: function () {
																return x(r);
															},
														},
														o.a.createElement(dt.a, {
															glyph: "icon-delete-line",
														}),
													),
											),
										),
										o.a.createElement(
											"div",
											{ className: "labelContainer" },
											o.a.createElement(
												"div",
												{ className: "signatureLabel" },
												a("option.type.signature"),
											),
											(null == w ? void 0 : w[r]) &&
												o.a.createElement(
													"div",
													{ className: "intialsLabel" },
													a("option.type.initials"),
												),
										),
									);
								})
							: o.a.createElement(
									"div",
									{ className: "emptyListContainer" },
									a("option.signatureModal.noSignatures"),
								),
					);
				},
				xt = n(5),
				wt = n(222),
				St = n(351),
				Et = n(173);
			n(1886);
			function kt(t) {
				return (kt =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function At() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ At =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					o = "function" == typeof Symbol ? Symbol : {},
					a = o.iterator || "@@iterator",
					i = o.asyncIterator || "@@asyncIterator",
					l = o.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, o) {
					var a = e && e.prototype instanceof p ? e : p,
						i = Object.create(a.prototype),
						l = new A(o || []);
					return r(i, "_invoke", { value: w(t, n, l) }), i;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var d = {};
				function p() {}
				function f() {}
				function h() {}
				var g = {};
				c(g, a, function () {
					return this;
				});
				var m = Object.getPrototypeOf,
					b = m && m(m(M([])));
				b && b !== e && n.call(b, a) && (g = b);
				var v = (h.prototype = p.prototype = Object.create(g));
				function y(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function x(t, e) {
					var o;
					r(this, "_invoke", {
						value: function (r, a) {
							function i() {
								return new e(function (o, i) {
									!(function r(o, a, i, l) {
										var c = s(t[o], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == kt(d) && n.call(d, "__await")
												? e.resolve(d.__await).then(
														function (t) {
															r("next", t, i, l);
														},
														function (t) {
															r("throw", t, i, l);
														},
													)
												: e.resolve(d).then(
														function (t) {
															(u.value = t), i(u);
														},
														function (t) {
															return r("throw", t, i, l);
														},
													);
										}
										l(c.arg);
									})(r, a, o, i);
								});
							}
							return (o = o ? o.then(i, i) : i());
						},
					});
				}
				function w(t, e, n) {
					var r = "suspendedStart";
					return function (o, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === o) throw a;
							return O();
						}
						for (n.method = o, n.arg = a; ; ) {
							var i = n.delegate;
							if (i) {
								var l = S(i, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function S(t, e) {
					var n = e.method,
						r = t.iterator[n];
					if (void 0 === r)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								S(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var o = s(r, t.iterator, e.arg);
					if ("throw" === o.type)
						return (
							(e.method = "throw"), (e.arg = o.arg), (e.delegate = null), d
						);
					var a = o.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								d)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							d);
				}
				function E(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function k(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function A(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(E, this),
						this.reset(!0);
				}
				function M(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var r = -1,
								o = function e() {
									for (; ++r < t.length; )
										if (n.call(t, r)) return (e.value = t[r]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (o.next = o);
						}
					}
					return { next: O };
				}
				function O() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = h),
					r(v, "constructor", { value: h, configurable: !0 }),
					r(h, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(h, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					y(x.prototype),
					c(x.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = x),
					(t.async = function (e, n, r, o, a) {
						void 0 === a && (a = Promise);
						var i = new x(u(e, n, r, o), a);
						return t.isGeneratorFunction(n)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					y(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var r in e) n.push(r);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in e) return (t.value = r), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = M),
					(A.prototype = {
						constructor: A,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(k),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function r(n, r) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = n),
									r && ((e.method = "next"), (e.arg = void 0)),
									!!r
								);
							}
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var a = this.tryEntries[o],
									i = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var o = this.tryEntries[r];
								if (
									o.tryLoc <= this.prev &&
									n.call(o, "finallyLoc") &&
									this.prev < o.finallyLoc
								) {
									var a = o;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								d
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), k(n), d;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var r = n.completion;
									if ("throw" === r.type) {
										var o = r.arg;
										k(n);
									}
									return o;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: M(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					t
				);
			}
			function Mt(t, e, n, r, o, a, i) {
				try {
					var l = t[a](i),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(r, o);
			}
			function Ot(t) {
				return function () {
					var e = this,
						n = arguments;
					return new Promise(function (r, o) {
						var a = t.apply(e, n);
						function i(t) {
							Mt(a, r, o, i, l, "next", t);
						}
						function l(t) {
							Mt(a, r, o, i, l, "throw", t);
						}
						i(void 0);
					});
				};
			}
			function Ct(t, e) {
				var n =
					("undefined" != typeof Symbol && t[Symbol.iterator]) ||
					t["@@iterator"];
				if (!n) {
					if (
						Array.isArray(t) ||
						(n = Lt(t)) ||
						(e && t && "number" == typeof t.length)
					) {
						n && (t = n);
						var r = 0,
							o = function () {};
						return {
							s: o,
							n: function () {
								return r >= t.length
									? { done: !0 }
									: { done: !1, value: t[r++] };
							},
							e: function (t) {
								throw t;
							},
							f: o,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var a,
					i = !0,
					l = !1;
				return {
					s: function () {
						n = n.call(t);
					},
					n: function () {
						var t = n.next();
						return (i = t.done), t;
					},
					e: function (t) {
						(l = !0), (a = t);
					},
					f: function () {
						try {
							i || null == n.return || n.return();
						} finally {
							if (l) throw a;
						}
					},
				};
			}
			function jt(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (o = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(t, e) ||
					Lt(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Lt(t, e) {
				if (t) {
					if ("string" == typeof t) return It(t, e);
					var n = Object.prototype.toString.call(t).slice(8, -1);
					return (
						"Object" === n && t.constructor && (n = t.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(t)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? It(t, e)
								: void 0
					);
				}
			}
			function It(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
				return r;
			}
			var _t = function () {
				var t = jt(
						Object(l.e)(function (t) {
							return [
								N.a.isElementDisabled(t, xt.a.SIGNATURE_MODAL),
								N.a.isElementOpen(t, xt.a.SIGNATURE_MODAL),
								N.a.getActiveToolName(t),
								N.a.getSignatureMode(t),
								N.a.getActiveDocumentViewerKey(t),
								N.a.getIsInitialsModeEnabled(t),
								N.a.isElementDisabled(t, xt.a.SAVED_SIGNATURES_TAB),
								N.a.getSelectedTab(t, xt.a.SIGNATURE_MODAL),
								N.a.getDisplayedSignatures(t),
								N.a.getSavedInitials(t),
							];
						}),
						10,
					),
					e = t[0],
					n = t[1],
					a = t[2],
					s = t[3],
					d = t[4],
					p = t[5],
					f = t[6],
					h = t[7],
					g = t[8],
					m = t[9],
					y = v.a.getToolsFromAllDocumentViewers("AnnotationCreateSignature"),
					x = jt(Object(r.useState)(!0), 2),
					w = x[0],
					S = x[1],
					E = jt(Object(r.useState)(0), 2),
					k = E[0],
					A = E[1],
					M = Object(l.d)(),
					O = jt(Object(c.a)(), 1)[0];
				Object(wt.a)(
					function () {
						"AnnotationCreateSignature" !== a &&
							M(
								ut.a.closeElements([
									xt.a.SIGNATURE_MODAL,
									xt.a.SIGNATURE_OVERLAY,
								]),
							);
					},
					[M, a],
				),
					Object(r.useEffect)(
						function () {
							n &&
								M(
									ut.a.closeElements([
										xt.a.PRINT_MODAL,
										xt.a.LOADING_MODAL,
										xt.a.PROGRESS_MODAL,
										xt.a.ERROR_MODAL,
									]),
								);
						},
						[M, n],
					);
				var C = function () {
						var t,
							e = Ct(y);
						try {
							for (e.s(); !(t = e.n()).done; ) {
								var n = t.value;
								n.clearLocation(), n.setSignature(null), n.setInitials(null);
							}
						} catch (t) {
							e.e(t);
						} finally {
							e.f();
						}
						M(ut.a.closeElement(xt.a.SIGNATURE_MODAL));
					},
					j = (function () {
						var t = Ot(
							At().mark(function t() {
								return At().wrap(function (t) {
									for (;;)
										switch ((t.prev = t.next)) {
											case 0:
												I(), p && _();
											case 2:
											case "end":
												return t.stop();
										}
								}, t);
							}),
						);
						return function () {
							return t.apply(this, arguments);
						};
					})(),
					I = (function () {
						var t = Ot(
							At().mark(function t() {
								var e, n, r, o, a;
								return At().wrap(
									function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													y[0].saveSignatures(
														y[0].getFullSignatureAnnotation(),
													),
														(e = 1);
												case 2:
													if (!(e < y.length)) {
														t.next = 8;
														break;
													}
													return (
														(t.next = 5),
														y[e].setSignature(y[0].getFullSignatureAnnotation())
													);
												case 5:
													e++, (t.next = 2);
													break;
												case 8:
													return (
														(n = y[d - 1]), (t.next = 11), n.isEmptySignature()
													);
												case 11:
													if (t.sent) {
														t.next = 37;
														break;
													}
													if (
														(v.a.setToolMode("AnnotationCreateSignature"),
														s !== b.a.FULL_SIGNATURE)
													) {
														t.next = 37;
														break;
													}
													if (!n.hasLocation()) {
														t.next = 19;
														break;
													}
													return (t.next = 17), n.addSignature();
												case 17:
													t.next = 36;
													break;
												case 19:
													(r = Ct(y)), (t.prev = 20), r.s();
												case 22:
													if ((o = r.n()).done) {
														t.next = 28;
														break;
													}
													return (a = o.value), (t.next = 26), a.showPreview();
												case 26:
													t.next = 22;
													break;
												case 28:
													t.next = 33;
													break;
												case 30:
													(t.prev = 30), (t.t0 = t.catch(20)), r.e(t.t0);
												case 33:
													return (t.prev = 33), r.f(), t.finish(33);
												case 36:
													M(ut.a.closeElement(xt.a.SIGNATURE_MODAL));
												case 37:
												case "end":
													return t.stop();
											}
									},
									t,
									null,
									[[20, 30, 33, 36]],
								);
							}),
						);
						return function () {
							return t.apply(this, arguments);
						};
					})(),
					_ = (function () {
						var t = Ot(
							At().mark(function t() {
								var e, n, r, o, a;
								return At().wrap(
									function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													y[0].saveInitials(y[0].getInitialsAnnotation()),
														(e = 1);
												case 2:
													if (!(e < y.length)) {
														t.next = 8;
														break;
													}
													return (
														(t.next = 5),
														y[e].saveInitials(y[0].getInitialsAnnotation())
													);
												case 5:
													e++, (t.next = 2);
													break;
												case 8:
													return (
														(n = y[d - 1]),
														(t.next = 11),
														n.isEmptyInitialsSignature()
													);
												case 11:
													if (t.sent) {
														t.next = 38;
														break;
													}
													if (
														(v.a.setToolMode("AnnotationCreateSignature"),
														s !== b.a.INITIALS)
													) {
														t.next = 36;
														break;
													}
													if (!n.hasLocation()) {
														t.next = 19;
														break;
													}
													return (t.next = 17), n.addInitials();
												case 17:
													t.next = 36;
													break;
												case 19:
													(r = Ct(y)), (t.prev = 20), r.s();
												case 22:
													if ((o = r.n()).done) {
														t.next = 28;
														break;
													}
													return (
														(a = o.value),
														(t.next = 26),
														a.showInitialsPreview()
													);
												case 26:
													t.next = 22;
													break;
												case 28:
													t.next = 33;
													break;
												case 30:
													(t.prev = 30), (t.t0 = t.catch(20)), r.e(t.t0);
												case 33:
													return (t.prev = 33), r.f(), t.finish(33);
												case 36:
													M(ut.a.closeElement(xt.a.SIGNATURE_MODAL)),
														M(ut.a.setSignatureMode(b.a.FULL_SIGNATURE));
												case 38:
												case "end":
													return t.stop();
											}
									},
									t,
									null,
									[[20, 30, 33, 36]],
								);
							}),
						);
						return function () {
							return t.apply(this, arguments);
						};
					})(),
					T = (function () {
						var t = Ot(
							At().mark(function t(e) {
								var n, r, o, a, i, l;
								return At().wrap(
									function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													(n = s === b.a.FULL_SIGNATURE),
														M(
															ut.a[
																n
																	? "setSelectedDisplayedSignatureIndex"
																	: "setSelectedDisplayedInitialsIndex"
															](e),
														),
														(r = n ? g[e] : m[e]),
														(o = r.annotation),
														v.a.setToolMode("AnnotationCreateSignature"),
														(a = Ct(y)),
														(t.prev = 5),
														a.s();
												case 7:
													if ((i = a.n()).done) {
														t.next = 20;
														break;
													}
													return (
														(l = i.value),
														(t.next = 11),
														l[n ? "setSignature" : "setInitials"](o)
													);
												case 11:
													if (!l.hasLocation()) {
														t.next = 16;
														break;
													}
													return (
														(t.next = 14),
														l[n ? "addSignature" : "addInitials"]()
													);
												case 14:
													t.next = 18;
													break;
												case 16:
													return (
														(t.next = 18),
														l[n ? "showPreview" : "showInitialsPreview"]()
													);
												case 18:
													t.next = 7;
													break;
												case 20:
													t.next = 25;
													break;
												case 22:
													(t.prev = 22), (t.t0 = t.catch(5)), a.e(t.t0);
												case 25:
													return (t.prev = 25), a.f(), t.finish(25);
												case 28:
													M(ut.a.closeElement(xt.a.SIGNATURE_MODAL));
												case 29:
												case "end":
													return t.stop();
											}
									},
									t,
									null,
									[[5, 22, 25, 28]],
								);
							}),
						);
						return function (e) {
							return t.apply(this, arguments);
						};
					})(),
					D = Object(r.useCallback)(
						function () {
							S(!0);
						},
						[w],
					),
					P = Object(r.useCallback)(
						function () {
							S(!1);
						},
						[w],
					),
					R = i()({ Modal: !0, SignatureModal: !0, open: n, closed: !n }),
					F = "savedSignaturePanelButton" === h;
				return e
					? null
					: o.a.createElement(
							"div",
							{ className: R, "data-element": xt.a.SIGNATURE_MODAL },
							o.a.createElement(
								St.a,
								{
									title: O("option.signatureModal.modalName"),
									closeHandler: C,
									onCloseClick: C,
									isOpen: n,
									swipeToClose: !0,
								},
								o.a.createElement(
									"div",
									{
										className: i()("container", { "include-initials": p }),
										onMouseDown: function (t) {
											return t.stopPropagation();
										},
									},
									o.a.createElement("div", { className: "swipe-indicator" }),
									o.a.createElement(
										u.d,
										{ id: "signatureModal" },
										o.a.createElement(
											"div",
											{ className: "tabs-header-container" },
											o.a.createElement(
												"div",
												{ className: "tab-list" },
												!f &&
													o.a.createElement(
														o.a.Fragment,
														null,
														o.a.createElement(
															u.a,
															{ dataElement: "savedSignaturePanelButton" },
															o.a.createElement(
																"button",
																{ className: "tab-options-button" },
																O("option.type.saved"),
															),
														),
														o.a.createElement("div", {
															className: "tab-options-divider",
														}),
													),
												o.a.createElement(
													u.a,
													{ dataElement: "inkSignaturePanelButton" },
													o.a.createElement(
														"button",
														{ className: "tab-options-button" },
														O("action.draw"),
													),
												),
												o.a.createElement("div", {
													className: "tab-options-divider",
												}),
												o.a.createElement(
													u.a,
													{ dataElement: "textSignaturePanelButton" },
													o.a.createElement(
														"button",
														{ className: "tab-options-button" },
														O("action.type"),
													),
												),
												o.a.createElement("div", {
													className: "tab-options-divider",
												}),
												o.a.createElement(
													u.a,
													{ dataElement: "imageSignaturePanelButton" },
													o.a.createElement(
														"button",
														{ className: "tab-options-button" },
														O("action.upload"),
													),
												),
											),
										),
										!f &&
											o.a.createElement(
												u.c,
												{ dataElement: "savedSignaturePanel" },
												o.a.createElement(yt, {
													selectedIndex: k,
													setSelectedIndex: A,
												}),
											),
										o.a.createElement(
											u.c,
											{ dataElement: "inkSignaturePanel" },
											o.a.createElement(L, {
												isModalOpen: n,
												enableCreateButton: P,
												disableCreateButton: D,
												isInitialsModeEnabled: p,
											}),
										),
										o.a.createElement(
											u.c,
											{ dataElement: "textSignaturePanel" },
											o.a.createElement(Y, {
												isModalOpen: n,
												enableCreateButton: P,
												disableCreateButton: D,
												isInitialsModeEnabled: p,
											}),
										),
										o.a.createElement(
											u.c,
											{ dataElement: "imageSignaturePanel" },
											o.a.createElement(ct, {
												isModalOpen: n,
												enableCreateButton: P,
												disableCreateButton: D,
												isInitialsModeEnabled: p,
											}),
										),
										o.a.createElement(
											"div",
											{ className: "footer" },
											o.a.createElement(
												"button",
												{
													className: "signature-create",
													onClick: Object(Et.a)(
														F
															? function () {
																	return T(k);
																}
															: j,
													),
													disabled: F ? !F || !g.length || !n : !n || w,
													title: p ? O("message.signatureRequired") : "",
												},
												O(F ? "action.apply" : "action.create"),
											),
										),
									),
								),
							),
						);
			};
			e.default = _t;
		},
	},
]);
//# sourceMappingURL=chunk.38.js.map
