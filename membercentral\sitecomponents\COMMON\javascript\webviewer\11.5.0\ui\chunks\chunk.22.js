(window.webpackJsonp = window.webpackJsonp || []).push([
	[22],
	{
		1584: function (e, t, n) {
			"use strict";
			n.d(t, "a", function () {
				return jn;
			});
			var r = n(0);
			function o(e, t) {
				return function (n) {
					return e(t(n));
				};
			}
			function i(e, t) {
				return t(e);
			}
			function l(e, t) {
				return function (n) {
					return e(t, n);
				};
			}
			function u(e, t) {
				return function () {
					return e(t);
				};
			}
			function a(e) {
				return function (t) {
					return t[e];
				};
			}
			function c(e, t) {
				return t(e), e;
			}
			function s() {
				for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
					t[n] = arguments[n];
				return t;
			}
			function f(e) {
				e();
			}
			function d(e) {
				return function () {
					return e;
				};
			}
			function m() {
				for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
					t[n] = arguments[n];
				return function () {
					t.map(f);
				};
			}
			function h() {}
			function p(e, t) {
				return e(1, t);
			}
			function v(e, t) {
				e(0, t);
			}
			function g(e) {
				e(2);
			}
			function I(e) {
				return e(4);
			}
			function C(e, t) {
				return p(e, l(t, 0));
			}
			function S(e, t) {
				var n = e(1, function (e) {
					n(), t(e);
				});
				return n;
			}
			function w() {
				var e = [];
				return function (t, n) {
					switch (t) {
						case 2:
							return void e.splice(0, e.length);
						case 1:
							return (
								e.push(n),
								function () {
									var t = e.indexOf(n);
									t > -1 && e.splice(t, 1);
								}
							);
						case 0:
							return void e.slice().forEach(function (e) {
								e(n);
							});
						default:
							throw new Error("unrecognized action " + t);
					}
				};
			}
			function T(e) {
				var t = e,
					n = w();
				return function (e, r) {
					switch (e) {
						case 1:
							r(t);
							break;
						case 0:
							t = r;
							break;
						case 4:
							return t;
					}
					return n(e, r);
				};
			}
			function x(e) {
				return c(w(), function (t) {
					return C(e, t);
				});
			}
			function y(e, t) {
				return c(T(t), function (t) {
					return C(e, t);
				});
			}
			function b() {
				for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
					t[n] = arguments[n];
				return function (e) {
					return t.reduceRight(i, e);
				};
			}
			function E(e) {
				for (
					var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), r = 1;
					r < t;
					r++
				)
					n[r - 1] = arguments[r];
				var o = b.apply(void 0, n);
				return function (t, n) {
					switch (t) {
						case 1:
							return p(e, o(n));
						case 2:
							return void g(e);
						default:
							throw new Error("unrecognized action " + t);
					}
				};
			}
			function H(e, t) {
				return e === t;
			}
			function R(e) {
				var t;
				return (
					void 0 === e && (e = H),
					function (n) {
						return function (r) {
							e(t, r) || ((t = r), n(r));
						};
					}
				);
			}
			function k(e) {
				return function (t) {
					return function (n) {
						e(n) && t(n);
					};
				};
			}
			function z(e) {
				return function (t) {
					return o(t, e);
				};
			}
			function O(e) {
				return function (t) {
					return function () {
						return t(e);
					};
				};
			}
			function B(e, t) {
				return function (n) {
					return function (r) {
						return n((t = e(t, r)));
					};
				};
			}
			function L(e) {
				return function (t) {
					return function (n) {
						e > 0 ? e-- : t(n);
					};
				};
			}
			function P(e) {
				var t, n;
				return function (r) {
					return function (o) {
						(t = o),
							n ||
								(n = setTimeout(function () {
									(n = void 0), r(t);
								}, e));
					};
				};
			}
			function j(e) {
				var t, n;
				return function (r) {
					return function (o) {
						(t = o),
							n && clearTimeout(n),
							(n = setTimeout(function () {
								r(t);
							}, e));
					};
				};
			}
			function M() {
				for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
					t[n] = arguments[n];
				var r = new Array(t.length),
					o = 0,
					i = null,
					l = Math.pow(2, t.length) - 1;
				return (
					t.forEach(function (e, t) {
						var n = Math.pow(2, t);
						p(e, function (e) {
							var u = o;
							(o |= n),
								(r[t] = e),
								u !== l && o === l && i && (i(), (i = null));
						});
					}),
					function (e) {
						return function (t) {
							var n = function () {
								return e([t].concat(r));
							};
							o === l ? n() : (i = n);
						};
					}
				);
			}
			function A() {
				for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
					t[n] = arguments[n];
				return function (e, n) {
					switch (e) {
						case 1:
							return m.apply(
								void 0,
								t.map(function (e) {
									return p(e, n);
								}),
							);
						case 2:
							return;
						default:
							throw new Error("unrecognized action " + e);
					}
				};
			}
			function V(e, t) {
				return void 0 === t && (t = H), E(e, R(t));
			}
			function W() {
				for (
					var e = w(), t = arguments.length, n = new Array(t), r = 0;
					r < t;
					r++
				)
					n[r] = arguments[r];
				var o = new Array(n.length),
					i = 0,
					l = Math.pow(2, n.length) - 1;
				return (
					n.forEach(function (t, n) {
						var r = Math.pow(2, n);
						p(t, function (t) {
							(o[n] = t), (i |= r) === l && v(e, o);
						});
					}),
					function (t, n) {
						switch (t) {
							case 1:
								return i === l && n(o), p(e, n);
							case 2:
								return g(e);
							default:
								throw new Error("unrecognized action " + t);
						}
					}
				);
			}
			function F(e, t, n) {
				void 0 === t && (t = []);
				var r = (void 0 === n ? { singleton: !0 } : n).singleton;
				return { id: D(), constructor: e, dependencies: t, singleton: r };
			}
			var D = function () {
				return Symbol();
			};
			function G(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			function N(e, t) {
				var n =
					("undefined" != typeof Symbol && e[Symbol.iterator]) ||
					e["@@iterator"];
				if (n) return (n = n.call(e)).next.bind(n);
				if (
					Array.isArray(e) ||
					(n = (function (e, t) {
						if (e) {
							if ("string" == typeof e) return G(e, t);
							var n = Object.prototype.toString.call(e).slice(8, -1);
							return (
								"Object" === n && e.constructor && (n = e.constructor.name),
								"Map" === n || "Set" === n
									? Array.from(e)
									: "Arguments" === n ||
											/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
										? G(e, t)
										: void 0
							);
						}
					})(e)) ||
					(t && e && "number" == typeof e.length)
				) {
					n && (e = n);
					var r = 0;
					return function () {
						return r >= e.length ? { done: !0 } : { done: !1, value: e[r++] };
					};
				}
				throw new TypeError(
					"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
				);
			}
			var U = ["children"];
			var _ = "undefined" != typeof document ? r.useLayoutEffect : r.useEffect;
			function K(e, t, n) {
				var o = Object.keys(t.required || {}),
					i = Object.keys(t.optional || {}),
					a = Object.keys(t.methods || {}),
					s = Object.keys(t.events || {}),
					f = Object(r.createContext)({});
				function m(e, n) {
					e.propsReady && v(e.propsReady, !1);
					for (var r, l = N(o); !(r = l()).done; ) {
						var u = r.value;
						v(e[t.required[u]], n[u]);
					}
					for (var a, c = N(i); !(a = c()).done; ) {
						var s = a.value;
						if (s in n) v(e[t.optional[s]], n[s]);
					}
					e.propsReady && v(e.propsReady, !0);
				}
				function C(e) {
					return s.reduce(function (n, r) {
						var o, i, l, u;
						return (
							(n[r] =
								((o = e[t.events[r]]),
								(u = function () {
									return i && i();
								}),
								function (e, t) {
									switch (e) {
										case 1:
											if (t) {
												if (l === t) return;
												return u(), (l = t), (i = p(o, t));
											}
											return u(), h;
										case 2:
											return u(), void (l = null);
										default:
											throw new Error("unrecognized action " + e);
									}
								})),
							n
						);
					}, {});
				}
				return {
					Component: Object(r.forwardRef)(function (l, h) {
						var I = l.children,
							S = (function (e, t) {
								if (null == e) return {};
								var n,
									r,
									o = {},
									i = Object.keys(e);
								for (r = 0; r < i.length; r++)
									(n = i[r]), t.indexOf(n) >= 0 || (o[n] = e[n]);
								return o;
							})(l, U),
							w = Object(r.useState)(function () {
								return c(
									(function (e) {
										var t = new Map();
										return (function e(n) {
											var r = n.id,
												o = n.constructor,
												i = n.dependencies,
												l = n.singleton;
											if (l && t.has(r)) return t.get(r);
											var u = o(
												i.map(function (t) {
													return e(t);
												}),
											);
											return l && t.set(r, u), u;
										})(e);
									})(e),
									function (e) {
										return m(e, S);
									},
								);
							})[0],
							T = Object(r.useState)(u(C, w))[0];
						return (
							_(
								function () {
									for (var e, t = N(s); !(e = t()).done; ) {
										var n = e.value;
										n in S && p(T[n], S[n]);
									}
									return function () {
										Object.values(T).map(g);
									};
								},
								[S, T, w],
							),
							_(function () {
								m(w, S);
							}),
							Object(r.useImperativeHandle)(
								h,
								d(
									(function (e) {
										return a.reduce(function (n, r) {
											return (
												(n[r] = function (n) {
													v(e[t.methods[r]], n);
												}),
												n
											);
										}, {});
									})(w),
								),
							),
							Object(r.createElement)(
								f.Provider,
								{ value: w },
								n
									? Object(r.createElement)(
											n,
											(function (e, t) {
												for (var n = {}, r = {}, o = 0, i = e.length; o < i; )
													(r[e[o]] = 1), (o += 1);
												for (var l in t) r.hasOwnProperty(l) || (n[l] = t[l]);
												return n;
											})([].concat(o, i, s), S),
											I,
										)
									: I,
							)
						);
					}),
					usePublisher: function (e) {
						return Object(r.useCallback)(l(v, Object(r.useContext)(f)[e]), [e]);
					},
					useEmitterValue: function (e) {
						var t = Object(r.useContext)(f)[e],
							n = Object(r.useState)(u(I, t)),
							o = n[0],
							i = n[1];
						return (
							_(
								function () {
									return p(t, function (e) {
										e !== o && i(d(e));
									});
								},
								[t, o],
							),
							o
						);
					},
					useEmitter: function (e, t) {
						var n = Object(r.useContext)(f)[e];
						_(
							function () {
								return p(n, t);
							},
							[t, n],
						);
					},
				};
			}
			function Y() {
				return (Y =
					Object.assign ||
					function (e) {
						for (var t = 1; t < arguments.length; t++) {
							var n = arguments[t];
							for (var r in n)
								Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]);
						}
						return e;
					}).apply(this, arguments);
			}
			function q(e, t) {
				if (null == e) return {};
				var n,
					r,
					o = {},
					i = Object.keys(e);
				for (r = 0; r < i.length; r++)
					t.indexOf((n = i[r])) >= 0 || (o[n] = e[n]);
				return o;
			}
			function J(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			function Z(e, t) {
				var n =
					("undefined" != typeof Symbol && e[Symbol.iterator]) ||
					e["@@iterator"];
				if (n) return (n = n.call(e)).next.bind(n);
				if (
					Array.isArray(e) ||
					(n = (function (e, t) {
						if (e) {
							if ("string" == typeof e) return J(e, t);
							var n = Object.prototype.toString.call(e).slice(8, -1);
							return (
								"Object" === n && e.constructor && (n = e.constructor.name),
								"Map" === n || "Set" === n
									? Array.from(e)
									: "Arguments" === n ||
											/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
										? J(e, t)
										: void 0
							);
						}
					})(e)) ||
					(t && e && "number" == typeof e.length)
				) {
					n && (e = n);
					var r = 0;
					return function () {
						return r >= e.length ? { done: !0 } : { done: !1, value: e[r++] };
					};
				}
				throw new TypeError(
					"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
				);
			}
			var $,
				Q,
				X,
				ee = "undefined" != typeof document ? r.useLayoutEffect : r.useEffect;
			((X = Q || (Q = {}))[(X.DEBUG = 0)] = "DEBUG"),
				(X[(X.INFO = 1)] = "INFO"),
				(X[(X.WARN = 2)] = "WARN"),
				(X[(X.ERROR = 3)] = "ERROR");
			var te =
					((($ = {})[Q.DEBUG] = "debug"),
					($[Q.INFO] = "log"),
					($[Q.WARN] = "warn"),
					($[Q.ERROR] = "error"),
					$),
				ne = F(
					function () {
						var e = T(Q.ERROR);
						return {
							log: T(function (t, n, r) {
								var o;
								void 0 === r && (r = Q.INFO),
									r >=
										(null !=
										(o = (
											"undefined" == typeof globalThis ? window : globalThis
										).VIRTUOSO_LOG_LEVEL)
											? o
											: I(e)) &&
										console[te[r]](
											"%creact-virtuoso: %c%s %o",
											"color: #0253b3; font-weight: bold",
											"color: initial",
											t,
											n,
										);
							}),
							logLevel: e,
						};
					},
					[],
					{ singleton: !0 },
				);
			function re(e, t) {
				void 0 === t && (t = !0);
				var n = Object(r.useRef)(null),
					o = function (e) {};
				if ("undefined" != typeof ResizeObserver) {
					var i = new ResizeObserver(function (t) {
						var n = t[0].target;
						null !== n.offsetParent && e(n);
					});
					o = function (e) {
						e && t
							? (i.observe(e), (n.current = e))
							: (n.current && i.unobserve(n.current), (n.current = null));
					};
				}
				return { ref: n, callbackRef: o };
			}
			function oe(e, t) {
				return void 0 === t && (t = !0), re(e, t).callbackRef;
			}
			function ie(e, t, n, r, o, i) {
				return re(function (n) {
					for (
						var l = (function (e, t, n, r) {
								var o = e.length;
								if (0 === o) return null;
								for (var i = [], l = 0; l < o; l++) {
									var u = e.item(l);
									if (u && void 0 !== u.dataset.index) {
										var a = parseInt(u.dataset.index),
											c = parseFloat(u.dataset.knownSize),
											s = t(u, "offsetHeight");
										if (
											(0 === s &&
												r(
													"Zero-sized element, this should not happen",
													{ child: u },
													Q.ERROR,
												),
											s !== c)
										) {
											var f = i[i.length - 1];
											0 === i.length || f.size !== s || f.endIndex !== a - 1
												? i.push({ startIndex: a, endIndex: a, size: s })
												: i[i.length - 1].endIndex++;
										}
									}
								}
								return i;
							})(n.children, t, 0, o),
							u = n.parentElement;
						!u.dataset.virtuosoScroller;
					)
						u = u.parentElement;
					var a = i
						? i.scrollTop
						: "window" === u.firstElementChild.dataset.viewportType
							? window.pageYOffset || document.documentElement.scrollTop
							: u.scrollTop;
					r({
						scrollTop: Math.max(a, 0),
						scrollHeight: (null != i ? i : u).scrollHeight,
						viewportHeight: (null != i ? i : u).offsetHeight,
					}),
						null !== l && e(l);
				}, n);
			}
			function le(e, t) {
				return Math.round(e.getBoundingClientRect()[t]);
			}
			function ue(e, t, n, o, i) {
				void 0 === o && (o = h);
				var l = Object(r.useRef)(null),
					u = Object(r.useRef)(null),
					a = Object(r.useRef)(null),
					c = Object(r.useCallback)(
						function (n) {
							var r = n.target,
								o =
									r === window || r === document
										? window.pageYOffset || document.documentElement.scrollTop
										: r.scrollTop,
								i =
									r === window
										? document.documentElement.scrollHeight
										: r.scrollHeight,
								l = r === window ? window.innerHeight : r.offsetHeight;
							e({
								scrollTop: Math.max(o, 0),
								scrollHeight: i,
								viewportHeight: l,
							}),
								null !== u.current &&
									(o === u.current ||
										o <= 0 ||
										o === r.scrollHeight - le(r, "height")) &&
									((u.current = null),
									t(!0),
									a.current && (clearTimeout(a.current), (a.current = null)));
						},
						[e, t],
					);
				return (
					Object(r.useEffect)(
						function () {
							var e = i || l.current;
							return (
								o(i || l.current),
								c({ target: e }),
								e.addEventListener("scroll", c, { passive: !0 }),
								function () {
									o(null), e.removeEventListener("scroll", c);
								}
							);
						},
						[l, c, n, o, i],
					),
					{
						scrollerRef: l,
						scrollByCallback: function (e) {
							l.current.scrollBy(e);
						},
						scrollToCallback: function (n) {
							var r = l.current;
							if (r && (!("offsetHeight" in r) || 0 !== r.offsetHeight)) {
								var o,
									i,
									c,
									s = "smooth" === n.behavior;
								if (
									(r === window
										? ((i = Math.max(
												le(document.documentElement, "height"),
												document.documentElement.scrollHeight,
											)),
											(o = window.innerHeight),
											(c = document.documentElement.scrollTop))
										: ((i = r.scrollHeight),
											(o = le(r, "height")),
											(c = r.scrollTop)),
									(n.top = Math.ceil(Math.max(Math.min(i - o, n.top), 0))),
									Math.abs(o - i) < 1.01 || n.top === c)
								)
									return (
										e({ scrollTop: c, scrollHeight: i, viewportHeight: o }),
										void (s && t(!0))
									);
								s
									? ((u.current = n.top),
										a.current && clearTimeout(a.current),
										(a.current = setTimeout(function () {
											(a.current = null), (u.current = null), t(!0);
										}, 1e3)))
									: (u.current = null),
									r.scrollTo(n);
							}
						},
					}
				);
			}
			var ae = F(
					function () {
						var e = w(),
							t = w(),
							n = T(0),
							r = w(),
							o = T(0),
							i = w(),
							l = w(),
							u = T(0),
							a = T(0),
							c = w(),
							s = w(),
							f = T(!1),
							d = T(!1);
						return (
							C(
								E(
									e,
									z(function (e) {
										return e.scrollTop;
									}),
								),
								t,
							),
							C(
								E(
									e,
									z(function (e) {
										return e.scrollHeight;
									}),
								),
								l,
							),
							C(t, o),
							{
								scrollContainerState: e,
								scrollTop: t,
								viewportHeight: i,
								headerHeight: u,
								footerHeight: a,
								scrollHeight: l,
								smoothScrollTargetReached: r,
								react18ConcurrentRendering: d,
								scrollTo: c,
								scrollBy: s,
								statefulScrollTop: o,
								deviation: n,
								scrollingInProgress: f,
							}
						);
					},
					[],
					{ singleton: !0 },
				),
				ce = { lvl: 0 };
			function se(e, t, n, r, o) {
				return (
					void 0 === r && (r = ce),
					void 0 === o && (o = ce),
					{ k: e, v: t, lvl: n, l: r, r: o }
				);
			}
			function fe(e) {
				return e === ce;
			}
			function de() {
				return ce;
			}
			function me(e, t) {
				if (fe(e)) return ce;
				var n = e.k,
					r = e.l,
					o = e.r;
				if (t === n) {
					if (fe(r)) return o;
					if (fe(o)) return r;
					var i = (function e(t) {
						return fe(t.r) ? [t.k, t.v] : e(t.r);
					})(r);
					return Se(Ie(e, { k: i[0], v: i[1], l: ge(r) }));
				}
				return Se(Ie(e, t < n ? { l: me(r, t) } : { r: me(o, t) }));
			}
			function he(e, t, n) {
				if ((void 0 === n && (n = "k"), fe(e))) return [-1 / 0, void 0];
				if (e[n] === t) return [e.k, e.v];
				if (e[n] < t) {
					var r = he(e.r, t, n);
					return -1 / 0 === r[0] ? [e.k, e.v] : r;
				}
				return he(e.l, t, n);
			}
			function pe(e, t, n) {
				return fe(e)
					? se(t, n, 1)
					: t === e.k
						? Ie(e, { k: t, v: n })
						: (function (e) {
								return xe(ye(e));
							})(Ie(e, t < e.k ? { l: pe(e.l, t, n) } : { r: pe(e.r, t, n) }));
			}
			function ve(e) {
				return fe(e) ? [] : [].concat(ve(e.l), [{ k: e.k, v: e.v }], ve(e.r));
			}
			function ge(e) {
				return fe(e.r) ? e.l : Se(Ie(e, { r: ge(e.r) }));
			}
			function Ie(e, t) {
				return se(
					void 0 !== t.k ? t.k : e.k,
					void 0 !== t.v ? t.v : e.v,
					void 0 !== t.lvl ? t.lvl : e.lvl,
					void 0 !== t.l ? t.l : e.l,
					void 0 !== t.r ? t.r : e.r,
				);
			}
			function Ce(e) {
				return fe(e) || e.lvl > e.r.lvl;
			}
			function Se(e) {
				var t = e.l,
					n = e.r,
					r = e.lvl;
				if (n.lvl >= r - 1 && t.lvl >= r - 1) return e;
				if (r > n.lvl + 1) {
					if (Ce(t)) return ye(Ie(e, { lvl: r - 1 }));
					if (fe(t) || fe(t.r)) throw new Error("Unexpected empty nodes");
					return Ie(t.r, {
						l: Ie(t, { r: t.r.l }),
						r: Ie(e, { l: t.r.r, lvl: r - 1 }),
						lvl: r,
					});
				}
				if (Ce(e)) return xe(Ie(e, { lvl: r - 1 }));
				if (fe(n) || fe(n.l)) throw new Error("Unexpected empty nodes");
				var o = n.l,
					i = Ce(o) ? n.lvl - 1 : n.lvl;
				return Ie(o, {
					l: Ie(e, { r: o.l, lvl: r - 1 }),
					r: xe(Ie(n, { l: o.r, lvl: i })),
					lvl: o.lvl + 1,
				});
			}
			function we(e, t, n) {
				return fe(e)
					? []
					: Te(
							(function e(t, n, r) {
								if (fe(t)) return [];
								var o = t.k,
									i = t.v,
									l = t.r,
									u = [];
								return (
									o > n && (u = u.concat(e(t.l, n, r))),
									o >= n && o <= r && u.push({ k: o, v: i }),
									o <= r && (u = u.concat(e(l, n, r))),
									u
								);
							})(e, he(e, t)[0], n),
							function (e) {
								return { index: e.k, value: e.v };
							},
						);
			}
			function Te(e, t) {
				var n = e.length;
				if (0 === n) return [];
				for (
					var r = t(e[0]), o = r.index, i = r.value, l = [], u = 1;
					u < n;
					u++
				) {
					var a = t(e[u]),
						c = a.index,
						s = a.value;
					l.push({ start: o, end: c - 1, value: i }), (o = c), (i = s);
				}
				return l.push({ start: o, end: 1 / 0, value: i }), l;
			}
			function xe(e) {
				var t = e.r,
					n = e.lvl;
				return fe(t) || fe(t.r) || t.lvl !== n || t.r.lvl !== n
					? e
					: Ie(t, { l: Ie(e, { r: t.l }), lvl: n + 1 });
			}
			function ye(e) {
				var t = e.l;
				return fe(t) || t.lvl !== e.lvl ? e : Ie(t, { r: Ie(e, { l: t.r }) });
			}
			function be(e, t, n, r) {
				void 0 === r && (r = 0);
				for (var o = e.length - 1; r <= o; ) {
					var i = Math.floor((r + o) / 2),
						l = n(e[i], t);
					if (0 === l) return i;
					if (-1 === l) {
						if (o - r < 2) return i - 1;
						o = i - 1;
					} else {
						if (o === r) return i;
						r = i + 1;
					}
				}
				throw new Error(
					"Failed binary finding record in array - " +
						e.join(",") +
						", searched for " +
						t,
				);
			}
			function Ee(e, t, n) {
				return e[be(e, t, n)];
			}
			function He(e) {
				var t = e.size,
					n = e.startIndex,
					r = e.endIndex;
				return function (e) {
					return (
						e.start === n && (e.end === r || 1 / 0 === e.end) && e.value === t
					);
				};
			}
			function Re(e, t) {
				var n = e.index;
				return t === n ? 0 : t < n ? -1 : 1;
			}
			function ke(e, t) {
				var n = e.offset;
				return t === n ? 0 : t < n ? -1 : 1;
			}
			function ze(e) {
				return { index: e.index, value: e };
			}
			function Oe(e, t, n) {
				var r = e,
					o = 0,
					i = 0,
					l = 0,
					u = 0;
				if (0 !== t) {
					l = r[(u = be(r, t - 1, Re))].offset;
					var a = he(n, t - 1);
					(o = a[0]),
						(i = a[1]),
						r.length && r[u].size === he(n, t)[1] && (u -= 1),
						(r = r.slice(0, u + 1));
				} else r = [];
				for (var c, s = Z(we(n, t, 1 / 0)); !(c = s()).done; ) {
					var f = c.value,
						d = f.start,
						m = f.value,
						h = (d - o) * i + l;
					r.push({ offset: h, size: m, index: d }), (o = d), (l = h), (i = m);
				}
				return { offsetTree: r, lastIndex: o, lastOffset: l, lastSize: i };
			}
			function Be(e, t) {
				var n = t[0],
					r = t[1];
				n.length > 0 && (0, t[2])("received item sizes", n, Q.DEBUG);
				var o = e.sizeTree,
					i = o,
					l = 0;
				if (r.length > 0 && fe(o) && 2 === n.length) {
					var u = n[0].size,
						a = n[1].size;
					i = r.reduce(function (e, t) {
						return pe(pe(e, t, u), t + 1, a);
					}, i);
				} else {
					var c = (function (e, t) {
						for (var n, r = fe(e) ? 0 : 1 / 0, o = Z(t); !(n = o()).done; ) {
							var i = n.value,
								l = i.size,
								u = i.startIndex,
								a = i.endIndex;
							if (((r = Math.min(r, u)), fe(e))) e = pe(e, 0, l);
							else {
								var c = we(e, u - 1, a + 1);
								if (!c.some(He(i))) {
									for (var s, f = !1, d = !1, m = Z(c); !(s = m()).done; ) {
										var h = s.value,
											p = h.start,
											v = h.end,
											g = h.value;
										f
											? (a >= p || l === g) && (e = me(e, p))
											: ((d = g !== l), (f = !0)),
											v > a && a >= p && g !== l && (e = pe(e, a + 1, g));
									}
									d && (e = pe(e, u, l));
								}
							}
						}
						return [e, r];
					})(i, n);
					(i = c[0]), (l = c[1]);
				}
				if (i === o) return e;
				var s = Oe(e.offsetTree, l, i),
					f = s.offsetTree;
				return {
					sizeTree: i,
					offsetTree: f,
					lastIndex: s.lastIndex,
					lastOffset: s.lastOffset,
					lastSize: s.lastSize,
					groupOffsetTree: r.reduce(function (e, t) {
						return pe(e, t, Le(t, f));
					}, de()),
					groupIndices: r,
				};
			}
			function Le(e, t) {
				if (0 === t.length) return 0;
				var n = Ee(t, e, Re);
				return n.size * (e - n.index) + n.offset;
			}
			function Pe(e, t) {
				if (!je(t)) return e;
				for (var n = 0; t.groupIndices[n] <= e + n; ) n++;
				return e + n;
			}
			function je(e) {
				return !fe(e.groupOffsetTree);
			}
			var Me = { offsetHeight: "height", offsetWidth: "width" },
				Ae = F(
					function (e) {
						var t = e[0].log,
							n = w(),
							r = w(),
							o = y(r, 0),
							i = w(),
							l = w(),
							u = T(0),
							a = T([]),
							c = T(void 0),
							s = T(void 0),
							f = T(function (e, t) {
								return le(e, Me[t]);
							}),
							d = T(void 0),
							m = {
								offsetTree: [],
								sizeTree: de(),
								groupOffsetTree: de(),
								lastIndex: 0,
								lastOffset: 0,
								lastSize: 0,
								groupIndices: [],
							},
							h = y(E(n, M(a, t), B(Be, m), R()), m);
						C(
							E(
								a,
								k(function (e) {
									return e.length > 0;
								}),
								M(h),
								z(function (e) {
									var t = e[0],
										n = e[1],
										r = t.reduce(function (e, t, r) {
											return pe(e, t, Le(t, n.offsetTree) || r);
										}, de());
									return Y({}, n, { groupIndices: t, groupOffsetTree: r });
								}),
							),
							h,
						),
							C(
								E(
									r,
									M(h),
									k(function (e) {
										return e[0] < e[1].lastIndex;
									}),
									z(function (e) {
										var t = e[1];
										return [
											{
												startIndex: e[0],
												endIndex: t.lastIndex,
												size: t.lastSize,
											},
										];
									}),
								),
								n,
							),
							C(c, s);
						var g = y(
							E(
								c,
								z(function (e) {
									return void 0 === e;
								}),
							),
							!0,
						);
						C(
							E(
								s,
								k(function (e) {
									return void 0 !== e && fe(I(h).sizeTree);
								}),
								z(function (e) {
									return [{ startIndex: 0, endIndex: 0, size: e }];
								}),
							),
							n,
						);
						var S = x(
							E(
								n,
								M(h),
								B(
									function (e, t) {
										var n = t[1];
										return { changed: n !== e.sizes, sizes: n };
									},
									{ changed: !1, sizes: m },
								),
								z(function (e) {
									return e.changed;
								}),
							),
						);
						p(
							E(
								u,
								B(
									function (e, t) {
										return { diff: e.prev - t, prev: t };
									},
									{ diff: 0, prev: 0 },
								),
								z(function (e) {
									return e.diff;
								}),
							),
							function (e) {
								e > 0 ? v(i, e) : e < 0 && v(l, e);
							},
						),
							p(E(u, M(t)), function (e) {
								e[0] < 0 &&
									(0, e[1])(
										"`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",
										{ firstItemIndex: u },
										Q.ERROR,
									);
							});
						var b = x(i);
						C(
							E(
								i,
								M(h),
								z(function (e) {
									var t = e[0],
										n = e[1];
									if (n.groupIndices.length > 0)
										throw new Error(
											"Virtuoso: prepending items does not work with groups",
										);
									return ve(n.sizeTree).reduce(
										function (e, n) {
											var r = n.k,
												o = n.v;
											return {
												ranges: [].concat(e.ranges, [
													{
														startIndex: e.prevIndex,
														endIndex: r + t - 1,
														size: e.prevSize,
													},
												]),
												prevIndex: r + t,
												prevSize: o,
											};
										},
										{ ranges: [], prevIndex: 0, prevSize: n.lastSize },
									).ranges;
								}),
							),
							n,
						);
						var H = x(
							E(
								l,
								M(h),
								z(function (e) {
									return Le(-e[0], e[1].offsetTree);
								}),
							),
						);
						return (
							C(
								E(
									l,
									M(h),
									z(function (e) {
										var t = e[0],
											n = e[1];
										if (n.groupIndices.length > 0)
											throw new Error(
												"Virtuoso: shifting items does not work with groups",
											);
										var r = ve(n.sizeTree).reduce(function (e, n) {
											var r = n.v;
											return pe(e, Math.max(0, n.k + t), r);
										}, de());
										return Y({}, n, { sizeTree: r }, Oe(n.offsetTree, 0, r));
									}),
								),
								h,
							),
							{
								data: d,
								totalCount: r,
								sizeRanges: n,
								groupIndices: a,
								defaultItemSize: s,
								fixedItemSize: c,
								unshiftWith: i,
								shiftWith: l,
								shiftWithOffset: H,
								beforeUnshiftWith: b,
								firstItemIndex: u,
								sizes: h,
								listRefresh: S,
								statefulTotalCount: o,
								trackItemSizes: g,
								itemSize: f,
							}
						);
					},
					s(ne),
					{ singleton: !0 },
				),
				Ve =
					"undefined" != typeof document &&
					"scrollBehavior" in document.documentElement.style;
			function We(e) {
				var t = "number" == typeof e ? { index: e } : e;
				return (
					t.align || (t.align = "start"),
					(t.behavior && Ve) || (t.behavior = "auto"),
					t.offset || (t.offset = 0),
					t
				);
			}
			var Fe = F(
					function (e) {
						var t = e[0],
							n = t.sizes,
							r = t.totalCount,
							o = t.listRefresh,
							i = e[1],
							l = i.scrollingInProgress,
							u = i.viewportHeight,
							a = i.scrollTo,
							c = i.smoothScrollTargetReached,
							s = i.headerHeight,
							f = i.footerHeight,
							d = e[2].log,
							m = w(),
							h = T(0),
							g = null,
							I = null,
							x = null;
						function y() {
							g && (g(), (g = null)),
								x && (x(), (x = null)),
								I && (clearTimeout(I), (I = null)),
								v(l, !1);
						}
						return (
							C(
								E(
									m,
									M(n, u, r, h, s, f, d),
									z(function (e) {
										var t = e[0],
											n = e[1],
											r = e[2],
											i = e[3],
											u = e[4],
											a = e[5],
											s = e[6],
											f = e[7],
											d = We(t),
											h = d.align,
											C = d.behavior,
											w = d.offset,
											T = i - 1,
											b = d.index;
										"LAST" === b && (b = T), (b = Pe(b, n));
										var H =
											Le((b = Math.max(0, b, Math.min(T, b))), n.offsetTree) +
											a;
										"end" === h
											? ((H = H - r + he(n.sizeTree, b)[1]),
												b === T && (H += s))
											: "center" === h
												? (H = H - r / 2 + he(n.sizeTree, b)[1] / 2)
												: (H -= u),
											w && (H += w);
										var R = function (e) {
											y(),
												e
													? (f(
															"retrying to scroll to",
															{ location: t },
															Q.DEBUG,
														),
														v(m, t))
													: f(
															"list did not change, scroll successful",
															{},
															Q.DEBUG,
														);
										};
										if ((y(), "smooth" === C)) {
											var k = !1;
											(x = p(o, function (e) {
												k = k || e;
											})),
												(g = S(c, function () {
													R(k);
												}));
										} else
											g = S(
												E(o, function (e) {
													var t = setTimeout(function () {
														e(!1);
													}, 50);
													return function (n) {
														n && (e(!0), clearTimeout(t));
													};
												}),
												R,
											);
										return (
											(I = setTimeout(function () {
												y();
											}, 1200)),
											v(l, !0),
											f(
												"scrolling from index to",
												{ index: b, top: H, behavior: C },
												Q.DEBUG,
											),
											{ top: H, behavior: C }
										);
									}),
								),
								a,
							),
							{ scrollToIndex: m, topListHeight: h }
						);
					},
					s(Ae, ae, ne),
					{ singleton: !0 },
				),
				De = "up",
				Ge = {
					atBottom: !1,
					notAtBottomBecause: "NOT_SHOWING_LAST_ITEM",
					state: {
						offsetBottom: 0,
						scrollTop: 0,
						viewportHeight: 0,
						scrollHeight: 0,
					},
				},
				Ne = F(function (e) {
					var t = e[0],
						n = t.scrollContainerState,
						r = t.scrollTop,
						o = t.viewportHeight,
						i = t.headerHeight,
						l = t.footerHeight,
						u = t.scrollBy,
						a = T(!1),
						c = T(!0),
						s = w(),
						f = w(),
						d = T(4),
						m = T(0),
						h = x(
							E(A(E(V(r), L(1), O(!0)), E(V(r), L(1), O(!1), j(100))), R()),
						),
						p = y(E(A(E(u, O(!0)), E(u, O(!1), j(200))), R()), !1);
					C(
						E(
							W(V(r), V(m)),
							z(function (e) {
								return e[0] <= e[1];
							}),
							R(),
						),
						c,
					),
						C(E(c, P(50)), f);
					var v = x(
							E(
								W(n, V(o), V(i), V(l), V(d)),
								B(function (e, t) {
									var n,
										r,
										o = t[0],
										i = o.scrollTop,
										l = o.scrollHeight,
										u = t[1],
										a = { viewportHeight: u, scrollTop: i, scrollHeight: l };
									return i + u - l > -t[4]
										? (i > e.state.scrollTop
												? ((n = "SCROLLED_DOWN"), (r = e.state.scrollTop - i))
												: ((n = "SIZE_DECREASED"),
													(r = e.state.scrollTop - i || e.scrollTopDelta)),
											{
												atBottom: !0,
												state: a,
												atBottomBecause: n,
												scrollTopDelta: r,
											})
										: {
												atBottom: !1,
												notAtBottomBecause:
													a.scrollHeight > e.state.scrollHeight
														? "SIZE_INCREASED"
														: u < e.state.viewportHeight
															? "VIEWPORT_HEIGHT_DECREASING"
															: i < e.state.scrollTop
																? "SCROLLING_UPWARDS"
																: "NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM",
												state: a,
											};
								}, Ge),
								R(function (e, t) {
									return e && e.atBottom === t.atBottom;
								}),
							),
						),
						g = y(
							E(
								n,
								B(
									function (e, t) {
										var n = t.scrollTop,
											r = t.scrollHeight;
										return e.scrollHeight !== r
											? e.scrollTop !== n && n === r - t.viewportHeight
												? {
														scrollHeight: r,
														scrollTop: n,
														jump: e.scrollTop - n,
														changed: !0,
													}
												: {
														scrollHeight: r,
														scrollTop: n,
														jump: 0,
														changed: !0,
													}
											: { scrollTop: n, scrollHeight: r, jump: 0, changed: !1 };
									},
									{ scrollHeight: 0, jump: 0, scrollTop: 0, changed: !1 },
								),
								k(function (e) {
									return e.changed;
								}),
								z(function (e) {
									return e.jump;
								}),
							),
							0,
						);
					C(
						E(
							v,
							z(function (e) {
								return e.atBottom;
							}),
						),
						a,
					),
						C(E(a, P(50)), s);
					var S = T("down");
					C(
						E(
							n,
							z(function (e) {
								return e.scrollTop;
							}),
							R(),
							B(
								function (e, t) {
									return I(p)
										? { direction: e.direction, prevScrollTop: t }
										: {
												direction: t < e.prevScrollTop ? De : "down",
												prevScrollTop: t,
											};
								},
								{ direction: "down", prevScrollTop: 0 },
							),
							z(function (e) {
								return e.direction;
							}),
						),
						S,
					),
						C(E(n, P(50), O("none")), S);
					var b = T(0);
					return (
						C(
							E(
								h,
								k(function (e) {
									return !e;
								}),
								O(0),
							),
							b,
						),
						C(
							E(
								r,
								P(100),
								M(h),
								k(function (e) {
									return !!e[1];
								}),
								B(
									function (e, t) {
										return [e[1], t[0]];
									},
									[0, 0],
								),
								z(function (e) {
									return e[1] - e[0];
								}),
							),
							b,
						),
						{
							isScrolling: h,
							isAtTop: c,
							isAtBottom: a,
							atBottomState: v,
							atTopStateChange: f,
							atBottomStateChange: s,
							scrollDirection: S,
							atBottomThreshold: d,
							atTopThreshold: m,
							scrollVelocity: b,
							lastJumpDueToItemResize: g,
						}
					);
				}, s(ae)),
				Ue = F(
					function (e) {
						var t = e[0].log,
							n = T(!1),
							r = x(
								E(
									n,
									k(function (e) {
										return e;
									}),
									R(),
								),
							);
						return (
							p(n, function (e) {
								e && I(t)("props updated", {}, Q.DEBUG);
							}),
							{ propsReady: n, didMount: r }
						);
					},
					s(ne),
					{ singleton: !0 },
				),
				_e = F(
					function (e) {
						var t = e[0],
							n = t.sizes,
							r = t.listRefresh,
							o = t.defaultItemSize,
							i = e[1].scrollTop,
							l = e[2].scrollToIndex,
							u = e[3].didMount,
							a = T(!0),
							c = T(0);
						return (
							C(
								E(
									u,
									M(c),
									k(function (e) {
										return !!e[1];
									}),
									O(!1),
								),
								a,
							),
							p(
								E(
									W(r, u),
									M(a, n, o),
									k(function (e) {
										var t = e[1],
											n = e[3];
										return (
											e[0][1] && (!fe(e[2].sizeTree) || void 0 !== n) && !t
										);
									}),
									M(c),
								),
								function (e) {
									var t = e[1];
									setTimeout(function () {
										S(i, function () {
											v(a, !0);
										}),
											v(l, t);
									});
								},
							),
							{ scrolledToInitialItem: a, initialTopMostItemIndex: c }
						);
					},
					s(Ae, ae, Fe, Ue),
					{ singleton: !0 },
				);
			function Ke(e) {
				return !!e && ("smooth" === e ? "smooth" : "auto");
			}
			var Ye = F(
				function (e) {
					var t = e[0],
						n = t.totalCount,
						r = t.listRefresh,
						o = e[1],
						i = o.isAtBottom,
						l = o.atBottomState,
						u = e[2].scrollToIndex,
						a = e[3].scrolledToInitialItem,
						c = e[4],
						s = c.propsReady,
						f = c.didMount,
						d = e[5].log,
						m = e[6].scrollingInProgress,
						h = T(!1),
						g = null;
					function C(e) {
						v(u, { index: "LAST", align: "end", behavior: e });
					}
					return (
						p(
							E(
								W(E(V(n), L(1)), f),
								M(V(h), i, a, m),
								z(function (e) {
									var t = e[0],
										n = t[0],
										r = t[1] && e[3],
										o = "auto";
									return (
										r &&
											((o = (function (e, t) {
												return "function" == typeof e ? Ke(e(t)) : t && Ke(e);
											})(e[1], e[2] || e[4])),
											(r = r && !!o)),
										{ totalCount: n, shouldFollow: r, followOutputBehavior: o }
									);
								}),
								k(function (e) {
									return e.shouldFollow;
								}),
							),
							function (e) {
								var t = e.totalCount,
									n = e.followOutputBehavior;
								g && (g(), (g = null)),
									(g = S(r, function () {
										I(d)("following output to ", { totalCount: t }, Q.DEBUG),
											C(n),
											(g = null);
									}));
							},
						),
						p(
							E(
								W(V(h), n, s),
								k(function (e) {
									return e[0] && e[2];
								}),
								B(
									function (e, t) {
										var n = t[1];
										return { refreshed: e.value === n, value: n };
									},
									{ refreshed: !1, value: 0 },
								),
								k(function (e) {
									return e.refreshed;
								}),
								M(h, n),
							),
							function (e) {
								var t = e[1],
									n = S(l, function (e) {
										!t ||
											e.atBottom ||
											"SIZE_INCREASED" !== e.notAtBottomBecause ||
											g ||
											(I(d)(
												"scrolling to bottom due to increased size",
												{},
												Q.DEBUG,
											),
											C("auto"));
									});
								setTimeout(n, 100);
							},
						),
						p(W(V(h), l), function (e) {
							var t = e[1];
							e[0] &&
								!t.atBottom &&
								"VIEWPORT_HEIGHT_DECREASING" === t.notAtBottomBecause &&
								C("auto");
						}),
						{ followOutput: h }
					);
				},
				s(Ae, Ne, Fe, _e, Ue, ne, ae),
			);
			function qe(e) {
				return e.reduce(
					function (e, t) {
						return (
							e.groupIndices.push(e.totalCount), (e.totalCount += t + 1), e
						);
					},
					{ totalCount: 0, groupIndices: [] },
				);
			}
			var Je = F(
				function (e) {
					var t = e[0],
						n = t.totalCount,
						r = t.groupIndices,
						o = t.sizes,
						i = e[1],
						l = i.scrollTop,
						u = i.headerHeight,
						c = w(),
						s = w(),
						f = x(E(c, z(qe)));
					return (
						C(E(f, z(a("totalCount"))), n),
						C(E(f, z(a("groupIndices"))), r),
						C(
							E(
								W(l, o, u),
								k(function (e) {
									return je(e[1]);
								}),
								z(function (e) {
									return he(
										e[1].groupOffsetTree,
										Math.max(e[0] - e[2], 0),
										"v",
									)[0];
								}),
								R(),
								z(function (e) {
									return [e];
								}),
							),
							s,
						),
						{ groupCounts: c, topItemsIndexes: s }
					);
				},
				s(Ae, ae),
			);
			function Ze(e, t) {
				return !(!e || e[0] !== t[0] || e[1] !== t[1]);
			}
			function $e(e, t) {
				return !(
					!e ||
					e.startIndex !== t.startIndex ||
					e.endIndex !== t.endIndex
				);
			}
			function Qe(e, t, n) {
				return "number" == typeof e
					? (n === De && "top" === t) || ("down" === n && "bottom" === t)
						? e
						: 0
					: n === De
						? "top" === t
							? e.main
							: e.reverse
						: "bottom" === t
							? e.main
							: e.reverse;
			}
			function Xe(e, t) {
				return "number" == typeof e ? e : e[t] || 0;
			}
			var et = F(
					function (e) {
						var t = e[0],
							n = t.scrollTop,
							r = t.viewportHeight,
							o = t.deviation,
							i = t.headerHeight,
							l = w(),
							u = T(0),
							a = T(0),
							c = T(0),
							s = T(0);
						return {
							listBoundary: l,
							overscan: s,
							topListHeight: u,
							fixedHeaderHeight: a,
							increaseViewportBy: c,
							visibleRange: y(
								E(
									W(V(n), V(r), V(i), V(l, Ze), V(s), V(u), V(a), V(o), V(c)),
									z(function (e) {
										var t = e[0],
											n = e[1],
											r = e[2],
											o = e[3],
											i = o[0],
											l = o[1],
											u = e[4],
											a = e[6],
											c = e[7],
											s = e[8],
											f = t - c,
											d = e[5] + a,
											m = Math.max(r - f, 0),
											h = "none",
											p = Xe(s, "top"),
											v = Xe(s, "bottom");
										return (
											(i -= c),
											(l += r + a),
											(i += r + a) > t + d - p && (h = De),
											(l -= c) < t - m + n + v && (h = "down"),
											"none" !== h
												? [
														Math.max(f - r - Qe(u, "top", h) - p, 0),
														f - m - a + n + Qe(u, "bottom", h) + v,
													]
												: null
										);
									}),
									k(function (e) {
										return null != e;
									}),
									R(Ze),
								),
								[0, 0],
							),
						};
					},
					s(ae),
					{ singleton: !0 },
				),
				tt = {
					items: [],
					topItems: [],
					offsetTop: 0,
					offsetBottom: 0,
					top: 0,
					bottom: 0,
					topListHeight: 0,
					totalCount: 0,
				};
			function nt(e, t, n) {
				if (0 === e.length) return [];
				if (!je(t))
					return e.map(function (e) {
						return Y({}, e, { index: e.index + n, originalIndex: e.index });
					});
				for (
					var r,
						o = [],
						i = we(t.groupOffsetTree, e[0].index, e[e.length - 1].index),
						l = void 0,
						u = 0,
						a = Z(e);
					!(r = a()).done;
				) {
					var c = r.value;
					(!l || l.end < c.index) &&
						((l = i.shift()), (u = t.groupIndices.indexOf(l.start))),
						o.push(
							Y(
								{},
								c.index === l.start
									? { type: "group", index: u }
									: { index: c.index - (u + 1) + n, groupIndex: u },
								{
									size: c.size,
									offset: c.offset,
									originalIndex: c.index,
									data: c.data,
								},
							),
						);
				}
				return o;
			}
			function rt(e, t, n, r, o) {
				var i = 0,
					l = 0;
				if (e.length > 0) {
					i = e[0].offset;
					var u = e[e.length - 1];
					l = u.offset + u.size;
				}
				var a = i,
					c = r.lastOffset + (n - r.lastIndex) * r.lastSize - l;
				return {
					items: nt(e, r, o),
					topItems: nt(t, r, o),
					topListHeight: t.reduce(function (e, t) {
						return t.size + e;
					}, 0),
					offsetTop: i,
					offsetBottom: c,
					top: a,
					bottom: l,
					totalCount: n,
				};
			}
			var ot,
				it,
				lt,
				ut = F(
					function (e) {
						var t = e[0],
							n = t.sizes,
							r = t.totalCount,
							o = t.data,
							i = t.firstItemIndex,
							l = e[1],
							u = e[2],
							s = u.visibleRange,
							f = u.listBoundary,
							d = u.topListHeight,
							m = e[3],
							h = m.scrolledToInitialItem,
							p = m.initialTopMostItemIndex,
							v = e[4].topListHeight,
							g = e[5],
							I = e[6].didMount,
							S = T([]),
							b = w();
						C(l.topItemsIndexes, S);
						var H = y(
							E(
								W(I, V(s), V(r), V(n), V(p), h, V(S), V(i), o),
								k(function (e) {
									return e[0];
								}),
								z(function (e) {
									var t = e[1],
										n = t[0],
										r = t[1],
										o = e[2],
										i = e[4],
										l = e[5],
										u = e[6],
										a = e[7],
										s = e[8],
										f = e[3],
										d = f.sizeTree,
										m = f.offsetTree;
									if (0 === o || (0 === n && 0 === r)) return tt;
									if (fe(d))
										return rt(
											(function (e, t, n) {
												if (je(t)) {
													var r = Pe(e, t);
													return [
														{
															index: he(t.groupOffsetTree, r)[0],
															size: 0,
															offset: 0,
														},
														{ index: r, size: 0, offset: 0, data: n && n[0] },
													];
												}
												return [
													{ index: e, size: 0, offset: 0, data: n && n[0] },
												];
											})(
												(function (e, t) {
													return "number" == typeof e
														? e
														: "LAST" === e.index
															? t - 1
															: e.index;
												})(i, o),
												f,
												s,
											),
											[],
											o,
											f,
											a,
										);
									var h = [];
									if (u.length > 0)
										for (
											var p,
												v = u[0],
												g = u[u.length - 1],
												I = 0,
												C = Z(we(d, v, g));
											!(p = C()).done;
										)
											for (
												var S = p.value,
													w = S.value,
													T = Math.max(S.start, v),
													x = Math.min(S.end, g),
													y = T;
												y <= x;
												y++
											)
												h.push({
													index: y,
													size: w,
													offset: I,
													data: s && s[y],
												}),
													(I += w);
									if (!l) return rt([], h, o, f, a);
									var b = u.length > 0 ? u[u.length - 1] + 1 : 0,
										E = (function (e, t, n, r) {
											return (
												void 0 === r && (r = 0),
												r > 0 && (t = Math.max(t, Ee(e, r, Re).offset)),
												Te(
													((i = n),
													(u = be((o = e), t, (l = ke))),
													(a = be(o, i, l, u)),
													o.slice(u, a + 1)),
													ze,
												)
											);
											var o, i, l, u, a;
										})(m, n, r, b);
									if (0 === E.length) return null;
									var H = o - 1;
									return rt(
										c([], function (e) {
											for (var t, o = Z(E); !(t = o()).done; ) {
												var i = t.value,
													l = i.value,
													u = l.offset,
													a = i.start,
													c = l.size;
												l.offset < n &&
													(u +=
														((a += Math.floor((n - l.offset) / c)) - i.start) *
														c),
													a < b && ((u += (b - a) * c), (a = b));
												for (
													var f = Math.min(i.end, H), d = a;
													d <= f && !(u >= r);
													d++
												)
													e.push({
														index: d,
														size: c,
														offset: u,
														data: s && s[d],
													}),
														(u += c);
											}
										}),
										h,
										o,
										f,
										a,
									);
								}),
								k(function (e) {
									return null !== e;
								}),
								R(),
							),
							tt,
						);
						return (
							C(
								E(
									o,
									k(function (e) {
										return void 0 !== e;
									}),
									z(function (e) {
										return e.length;
									}),
								),
								r,
							),
							C(E(H, z(a("topListHeight"))), v),
							C(v, d),
							C(
								E(
									H,
									z(function (e) {
										return [e.top, e.bottom];
									}),
								),
								f,
							),
							C(
								E(
									H,
									z(function (e) {
										return e.items;
									}),
								),
								b,
							),
							Y(
								{
									listState: H,
									topItemsIndexes: S,
									endReached: x(
										E(
											H,
											k(function (e) {
												return e.items.length > 0;
											}),
											M(r, o),
											k(function (e) {
												var t = e[0].items;
												return t[t.length - 1].originalIndex === e[1] - 1;
											}),
											z(function (e) {
												return [e[1] - 1, e[2]];
											}),
											R(Ze),
											z(function (e) {
												return e[0];
											}),
										),
									),
									startReached: x(
										E(
											H,
											P(200),
											k(function (e) {
												var t = e.items;
												return (
													t.length > 0 &&
													t[0].originalIndex === e.topItems.length
												);
											}),
											z(function (e) {
												return e.items[0].index;
											}),
											R(),
										),
									),
									rangeChanged: x(
										E(
											H,
											k(function (e) {
												return e.items.length > 0;
											}),
											z(function (e) {
												var t = e.items;
												return {
													startIndex: t[0].index,
													endIndex: t[t.length - 1].index,
												};
											}),
											R($e),
										),
									),
									itemsRendered: b,
								},
								g,
							)
						);
					},
					s(Ae, Je, et, _e, Fe, Ne, Ue),
					{ singleton: !0 },
				),
				at = F(
					function (e) {
						var t = e[0],
							n = t.sizes,
							r = t.firstItemIndex,
							o = t.data,
							i = e[1].listState,
							l = e[2].didMount,
							u = T(0);
						return (
							C(
								E(
									l,
									M(u),
									k(function (e) {
										return 0 !== e[1];
									}),
									M(n, r, o),
									z(function (e) {
										var t = e[0][1],
											n = e[1],
											r = e[2],
											o = e[3],
											i = void 0 === o ? [] : o,
											l = 0;
										if (n.groupIndices.length > 0)
											for (
												var u, a = Z(n.groupIndices);
												!((u = a()).done || u.value - l >= t);
											)
												l++;
										var c = t + l;
										return rt(
											Array.from({ length: c }).map(function (e, t) {
												return { index: t, size: 0, offset: 0, data: i[t] };
											}),
											[],
											c,
											n,
											r,
										);
									}),
								),
								i,
							),
							{ initialItemCount: u }
						);
					},
					s(Ae, ut, Ue),
					{ singleton: !0 },
				),
				ct = F(
					function (e) {
						var t = e[0].scrollVelocity,
							n = T(!1),
							r = w(),
							o = T(!1);
						return (
							C(
								E(
									t,
									M(o, n, r),
									k(function (e) {
										return !!e[1];
									}),
									z(function (e) {
										var t = e[0],
											n = e[1],
											r = e[2],
											o = e[3],
											i = n.enter;
										if (r) {
											if ((0, n.exit)(t, o)) return !1;
										} else if (i(t, o)) return !0;
										return r;
									}),
									R(),
								),
								n,
							),
							p(E(W(n, t, r), M(o)), function (e) {
								var t = e[0],
									n = e[1];
								return t[0] && n && n.change && n.change(t[1], t[2]);
							}),
							{
								isSeeking: n,
								scrollSeekConfiguration: o,
								scrollVelocity: t,
								scrollSeekRangeChanged: r,
							}
						);
					},
					s(Ne),
					{ singleton: !0 },
				),
				st = F(function (e) {
					var t = e[0].topItemsIndexes,
						n = T(0);
					return (
						C(
							E(
								n,
								k(function (e) {
									return e > 0;
								}),
								z(function (e) {
									return Array.from({ length: e }).map(function (e, t) {
										return t;
									});
								}),
							),
							t,
						),
						{ topItemCount: n }
					);
				}, s(ut)),
				ft = F(
					function (e) {
						var t = e[0],
							n = t.footerHeight,
							r = t.headerHeight,
							o = e[1].listState,
							i = w(),
							l = y(
								E(
									W(n, r, o),
									z(function (e) {
										var t = e[2];
										return e[0] + e[1] + t.offsetBottom + t.bottom;
									}),
								),
								0,
							);
						return (
							C(V(l), i), { totalListHeight: l, totalListHeightChanged: i }
						);
					},
					s(ae, ut),
					{ singleton: !0 },
				),
				dt = F(
					function (e) {
						var t = e[0],
							n = t.scrollBy,
							r = t.scrollTop,
							o = t.deviation,
							i = t.scrollingInProgress,
							l = e[1],
							u = l.isScrolling,
							a = l.isAtBottom,
							c = l.atBottomState,
							s = l.scrollDirection,
							f = e[3],
							d = f.beforeUnshiftWith,
							m = f.shiftWithOffset,
							h = f.sizes,
							g = e[4].log,
							I = x(
								E(
									e[2].listState,
									M(l.lastJumpDueToItemResize),
									B(
										function (e, t) {
											var n = e[1],
												r = t[0],
												o = r.items,
												i = r.totalCount,
												l = r.bottom + r.offsetBottom,
												u = 0;
											return (
												e[2] === i &&
													n.length > 0 &&
													o.length > 0 &&
													((0 === o[0].originalIndex &&
														0 === n[0].originalIndex) ||
														(0 != (u = l - e[3]) && (u += t[1]))),
												[u, o, i, l]
											);
										},
										[0, [], 0, 0],
									),
									k(function (e) {
										return 0 !== e[0];
									}),
									M(r, s, i, g, a, c),
									k(function (e) {
										return !e[3] && 0 !== e[1] && e[2] === De;
									}),
									z(function (e) {
										var t = e[0][0];
										return (
											(0, e[4])(
												"Upward scrolling compensation",
												{ amount: t },
												Q.DEBUG,
											),
											t
										);
									}),
								),
							);
						return (
							C(
								E(
									I,
									M(o),
									z(function (e) {
										return e[1] - e[0];
									}),
								),
								o,
							),
							p(
								E(
									W(y(u, !1), o),
									k(function (e) {
										return !e[0] && 0 !== e[1];
									}),
									z(function (e) {
										return e[1];
									}),
									P(1),
								),
								function (e) {
									e > 0
										? (v(n, { top: -e, behavior: "auto" }), v(o, 0))
										: (v(o, 0), v(n, { top: -e, behavior: "auto" }));
								},
							),
							C(
								E(
									m,
									z(function (e) {
										return { top: -e };
									}),
								),
								n,
							),
							C(
								E(
									d,
									M(h),
									z(function (e) {
										return e[0] * e[1].lastSize;
									}),
								),
								I,
							),
							{ deviation: o }
						);
					},
					s(ae, Ne, ut, Ae, ne),
				),
				mt = F(
					function (e) {
						var t = e[0].totalListHeight,
							n = e[1].didMount,
							r = e[2].scrollTo,
							o = T(0);
						return (
							p(
								E(
									n,
									M(o),
									k(function (e) {
										return 0 !== e[1];
									}),
									z(function (e) {
										return { top: e[1] };
									}),
								),
								function (e) {
									S(
										E(
											t,
											k(function (e) {
												return 0 !== e;
											}),
										),
										function () {
											setTimeout(function () {
												v(r, e);
											});
										},
									);
								},
							),
							{ initialScrollTop: o }
						);
					},
					s(ft, Ue, ae),
					{ singleton: !0 },
				),
				ht = F(
					function (e) {
						var t = e[0].viewportHeight,
							n = e[1].totalListHeight,
							r = T(!1);
						return {
							alignToBottom: r,
							paddingTopAddition: y(
								E(
									W(r, t, n),
									k(function (e) {
										return e[0];
									}),
									z(function (e) {
										return Math.max(0, e[1] - e[2]);
									}),
									R(),
								),
								0,
							),
						};
					},
					s(ae, ft),
					{ singleton: !0 },
				),
				pt = F(function (e) {
					var t = e[0],
						n = t.scrollTo,
						r = t.scrollContainerState,
						o = w(),
						i = w(),
						l = w(),
						u = T(!1),
						a = T(void 0);
					return (
						C(
							E(
								W(o, i),
								z(function (e) {
									var t = e[0],
										n = t.viewportHeight,
										r = t.scrollHeight;
									return {
										scrollTop: Math.max(0, t.scrollTop - e[1].offsetTop),
										scrollHeight: r,
										viewportHeight: n,
									};
								}),
							),
							r,
						),
						C(
							E(
								n,
								M(i),
								z(function (e) {
									var t = e[0];
									return Y({}, t, { top: t.top + e[1].offsetTop });
								}),
							),
							l,
						),
						{
							useWindowScroll: u,
							customScrollParent: a,
							windowScrollContainerState: o,
							windowViewportRect: i,
							windowScrollTo: l,
						}
					);
				}, s(ae)),
				vt = F(
					function (e) {
						var t = e[0],
							n = t.sizes,
							r = t.totalCount,
							o = e[1],
							i = o.scrollTop,
							l = o.viewportHeight,
							u = o.headerHeight,
							a = o.scrollingInProgress,
							c = e[2].scrollToIndex,
							s = w();
						return (
							C(
								E(
									s,
									M(n, l, r, u, i),
									z(function (e) {
										var t = e[0],
											n = t.index,
											r = t.behavior,
											o = void 0 === r ? "auto" : r,
											i = t.done,
											l = e[1],
											u = e[2],
											c = e[4],
											s = e[5],
											f = e[3] - 1,
											d = null;
										n = Pe(n, l);
										var m =
											Le((n = Math.max(0, n, Math.min(f, n))), l.offsetTree) +
											c;
										return (
											m < s
												? (d = { index: n, behavior: o, align: "start" })
												: m + he(l.sizeTree, n)[1] > s + u &&
													(d = { index: n, behavior: o, align: "end" }),
											d
												? i &&
													S(
														E(
															a,
															L(1),
															k(function (e) {
																return !1 === e;
															}),
														),
														i,
													)
												: i && i(),
											d
										);
									}),
									k(function (e) {
										return null !== e;
									}),
								),
								c,
							),
							{ scrollIntoView: s }
						);
					},
					s(Ae, ae, Fe, ut, ne),
					{ singleton: !0 },
				),
				gt = ["listState", "topItemsIndexes"],
				It = F(
					function (e) {
						return Y({}, e[0], e[1], e[2], e[3], e[4], e[5], e[6], e[7], e[8]);
					},
					s(et, at, Ue, ct, ft, mt, ht, pt, vt),
				),
				Ct = F(
					function (e) {
						var t = e[0],
							n = t.totalCount,
							r = t.sizeRanges,
							o = t.fixedItemSize,
							i = t.defaultItemSize,
							l = t.trackItemSizes,
							u = t.itemSize,
							c = t.data,
							s = t.firstItemIndex,
							f = t.groupIndices,
							d = t.statefulTotalCount,
							m = e[1],
							h = m.initialTopMostItemIndex,
							p = m.scrolledToInitialItem,
							v = e[2],
							g = e[3],
							I = e[4],
							S = I.listState,
							w = I.topItemsIndexes,
							T = q(I, gt),
							x = e[5].scrollToIndex,
							y = e[7].topItemCount,
							b = e[8].groupCounts,
							H = e[9],
							R = e[10];
						return (
							C(T.rangeChanged, H.scrollSeekRangeChanged),
							C(
								E(H.windowViewportRect, z(a("visibleHeight"))),
								v.viewportHeight,
							),
							Y(
								{
									totalCount: n,
									data: c,
									firstItemIndex: s,
									sizeRanges: r,
									initialTopMostItemIndex: h,
									scrolledToInitialItem: p,
									topItemsIndexes: w,
									topItemCount: y,
									groupCounts: b,
									fixedItemHeight: o,
									defaultItemHeight: i,
								},
								g,
								{
									statefulTotalCount: d,
									listState: S,
									scrollToIndex: x,
									trackItemSizes: l,
									itemSize: u,
									groupIndices: f,
								},
								T,
								H,
								v,
								R,
							)
						);
					},
					s(Ae, _e, ae, Ye, ut, Fe, dt, st, Je, It, ne),
				),
				St =
					((ot = function () {
						if ("undefined" == typeof document) return "sticky";
						var e = document.createElement("div");
						return (
							(e.style.position = "-webkit-sticky"),
							"-webkit-sticky" === e.style.position
								? "-webkit-sticky"
								: "sticky"
						);
					}),
					(lt = !1),
					function () {
						return lt || ((lt = !0), (it = ot())), it;
					});
			function wt(e, t) {
				var n = Object(r.useRef)(null),
					o = Object(r.useCallback)(
						function (r) {
							if (null !== r) {
								var o,
									i,
									l = r.getBoundingClientRect(),
									u = l.width;
								if (t) {
									var a = t.getBoundingClientRect(),
										c = l.top - a.top;
									(o = a.height - Math.max(0, c)), (i = c + t.scrollTop);
								} else
									(o = window.innerHeight - Math.max(0, l.top)),
										(i = l.top + window.pageYOffset);
								(n.current = {
									offsetTop: i,
									visibleHeight: o,
									visibleWidth: u,
								}),
									e(n.current);
							}
						},
						[e, t],
					),
					i = re(o),
					l = i.callbackRef,
					u = i.ref,
					a = Object(r.useCallback)(
						function () {
							o(u.current);
						},
						[o, u],
					);
				return (
					Object(r.useEffect)(
						function () {
							if (t) {
								t.addEventListener("scroll", a);
								var e = new ResizeObserver(a);
								return (
									e.observe(t),
									function () {
										t.removeEventListener("scroll", a), e.unobserve(t);
									}
								);
							}
							return (
								window.addEventListener("scroll", a),
								window.addEventListener("resize", a),
								function () {
									window.removeEventListener("scroll", a),
										window.removeEventListener("resize", a);
								}
							);
						},
						[a, t],
					),
					l
				);
			}
			var Tt = ["placeholder"],
				xt = ["style", "children"],
				yt = ["style", "children"];
			function bt(e) {
				return e;
			}
			var Et = F(function () {
				var e = T(function (e) {
						return "Item " + e;
					}),
					t = T(null),
					n = T(function (e) {
						return "Group " + e;
					}),
					r = T({}),
					o = T(bt),
					i = T("div"),
					l = T(h),
					u = function (e, t) {
						return (
							void 0 === t && (t = null),
							y(
								E(
									r,
									z(function (t) {
										return t[e];
									}),
									R(),
								),
								t,
							)
						);
					};
				return {
					context: t,
					itemContent: e,
					groupContent: n,
					components: r,
					computeItemKey: o,
					headerFooterTag: i,
					scrollerRef: l,
					FooterComponent: u("Footer"),
					HeaderComponent: u("Header"),
					TopItemListComponent: u("TopItemList"),
					ListComponent: u("List", "div"),
					ItemComponent: u("Item", "div"),
					GroupComponent: u("Group", "div"),
					ScrollerComponent: u("Scroller", "div"),
					EmptyPlaceholder: u("EmptyPlaceholder"),
					ScrollSeekPlaceholder: u("ScrollSeekPlaceholder"),
				};
			});
			function Ht(e, t) {
				var n = w();
				return (
					p(n, function () {
						return console.warn(
							"react-virtuoso: You are using a deprecated property. " + t,
							"color: red;",
							"color: inherit;",
							"color: blue;",
						);
					}),
					C(n, e),
					n
				);
			}
			var Rt = F(
					function (e) {
						var t = e[0],
							n = e[1],
							r = {
								item: Ht(
									n.itemContent,
									"Rename the %citem%c prop to %citemContent.",
								),
								group: Ht(
									n.groupContent,
									"Rename the %cgroup%c prop to %cgroupContent.",
								),
								topItems: Ht(
									t.topItemCount,
									"Rename the %ctopItems%c prop to %ctopItemCount.",
								),
								itemHeight: Ht(
									t.fixedItemHeight,
									"Rename the %citemHeight%c prop to %cfixedItemHeight.",
								),
								scrollingStateChange: Ht(
									t.isScrolling,
									"Rename the %cscrollingStateChange%c prop to %cisScrolling.",
								),
								adjustForPrependedItems: w(),
								maxHeightCacheSize: w(),
								footer: w(),
								header: w(),
								HeaderContainer: w(),
								FooterContainer: w(),
								ItemContainer: w(),
								ScrollContainer: w(),
								GroupContainer: w(),
								ListContainer: w(),
								emptyComponent: w(),
								scrollSeek: w(),
							};
						function o(e, t, r) {
							C(
								E(
									e,
									M(n.components),
									z(function (e) {
										var n,
											o = e[0],
											i = e[1];
										return (
											console.warn(
												"react-virtuoso: " +
													r +
													" property is deprecated. Pass components." +
													t +
													" instead.",
											),
											Y({}, i, (((n = {})[t] = o), n))
										);
									}),
								),
								n.components,
							);
						}
						return (
							p(r.adjustForPrependedItems, function () {
								console.warn(
									"react-virtuoso: adjustForPrependedItems is no longer supported. Use the firstItemIndex property instead - https://virtuoso.dev/prepend-items.",
									"color: red;",
									"color: inherit;",
									"color: blue;",
								);
							}),
							p(r.maxHeightCacheSize, function () {
								console.warn(
									"react-virtuoso: maxHeightCacheSize is no longer necessary. Setting it has no effect - remove it from your code.",
								);
							}),
							p(r.HeaderContainer, function () {
								console.warn(
									"react-virtuoso: HeaderContainer is deprecated. Use headerFooterTag if you want to change the wrapper of the header component and pass components.Header to change its contents.",
								);
							}),
							p(r.FooterContainer, function () {
								console.warn(
									"react-virtuoso: FooterContainer is deprecated. Use headerFooterTag if you want to change the wrapper of the footer component and pass components.Footer to change its contents.",
								);
							}),
							p(r.scrollSeek, function (e) {
								var r = e.placeholder,
									o = q(e, Tt);
								console.warn(
									"react-virtuoso: scrollSeek property is deprecated. Pass scrollSeekConfiguration and specify the placeholder in components.ScrollSeekPlaceholder instead.",
								),
									v(
										n.components,
										Y({}, I(n.components), { ScrollSeekPlaceholder: r }),
									),
									v(t.scrollSeekConfiguration, o);
							}),
							o(r.footer, "Footer", "footer"),
							o(r.header, "Header", "header"),
							o(r.ItemContainer, "Item", "ItemContainer"),
							o(r.ListContainer, "List", "ListContainer"),
							o(r.ScrollContainer, "Scroller", "ScrollContainer"),
							o(r.emptyComponent, "EmptyPlaceholder", "emptyComponent"),
							o(r.GroupContainer, "Group", "GroupContainer"),
							Y({}, t, n, r)
						);
					},
					s(Ct, Et),
				),
				kt = function (e) {
					return r.createElement("div", { style: { height: e.height } });
				},
				zt = { position: St(), zIndex: 1, overflowAnchor: "none" },
				Ot = { overflowAnchor: "none" },
				Bt = r.memo(function (e) {
					var t = e.showTopList,
						n = void 0 !== t && t,
						o = Yt("listState"),
						i = Kt("sizeRanges"),
						l = Yt("useWindowScroll"),
						u = Yt("customScrollParent"),
						a = Kt("windowScrollContainerState"),
						c = Kt("scrollContainerState"),
						s = u || l ? a : c,
						f = Yt("itemContent"),
						d = Yt("context"),
						m = Yt("groupContent"),
						p = Yt("trackItemSizes"),
						v = ie(i, Yt("itemSize"), p, n ? h : s, Yt("log"), u),
						g = v.callbackRef,
						I = v.ref,
						C = r.useState(0),
						S = C[0],
						w = C[1];
					qt("deviation", function (e) {
						S !== e && ((I.current.style.marginTop = e + "px"), w(e));
					});
					var T = Yt("EmptyPlaceholder"),
						x = Yt("ScrollSeekPlaceholder") || kt,
						y = Yt("ListComponent"),
						b = Yt("ItemComponent"),
						E = Yt("GroupComponent"),
						H = Yt("computeItemKey"),
						R = Yt("isSeeking"),
						k = Yt("groupIndices").length > 0,
						z = Yt("paddingTopAddition"),
						O = Yt("firstItemIndex"),
						B = Yt("statefulTotalCount"),
						L = n
							? {}
							: {
									boxSizing: "border-box",
									paddingTop: o.offsetTop + z,
									paddingBottom: o.offsetBottom,
									marginTop: S,
								};
					return !n && 0 === B && T
						? Object(r.createElement)(T, Mt(T, d))
						: Object(r.createElement)(
								y,
								Y({}, Mt(y, d), {
									ref: g,
									style: L,
									"data-test-id": n
										? "virtuoso-top-item-list"
										: "virtuoso-item-list",
								}),
								(n ? o.topItems : o.items).map(function (e) {
									var t = e.originalIndex,
										n = H(t + O, e.data, d);
									return R
										? Object(r.createElement)(
												x,
												Y(
													{},
													Mt(x, d),
													{
														key: n,
														index: e.index,
														height: e.size,
														type: e.type || "item",
													},
													"group" === e.type
														? {}
														: { groupIndex: e.groupIndex },
												),
											)
										: "group" === e.type
											? Object(r.createElement)(
													E,
													Y({}, Mt(E, d), {
														key: n,
														"data-index": t,
														"data-known-size": e.size,
														"data-item-index": e.index,
														style: zt,
													}),
													m(e.index),
												)
											: Object(r.createElement)(
													b,
													Y({}, Mt(b, d), {
														key: n,
														"data-index": t,
														"data-known-size": e.size,
														"data-item-index": e.index,
														"data-item-group-index": e.groupIndex,
														style: Ot,
													}),
													k
														? f(e.index, e.groupIndex, e.data, d)
														: f(e.index, e.data, d),
												);
								}),
							);
				}),
				Lt = {
					height: "100%",
					outline: "none",
					overflowY: "auto",
					position: "relative",
					WebkitOverflowScrolling: "touch",
				},
				Pt = { width: "100%", height: "100%", position: "absolute", top: 0 },
				jt = { width: "100%", position: St(), top: 0 };
			function Mt(e, t) {
				if ("string" != typeof e) return { context: t };
			}
			var At = r.memo(function () {
					var e = Yt("HeaderComponent"),
						t = Kt("headerHeight"),
						n = Yt("headerFooterTag"),
						o = oe(function (e) {
							return t(le(e, "height"));
						}),
						i = Yt("context");
					return e
						? Object(r.createElement)(
								n,
								{ ref: o },
								Object(r.createElement)(e, Mt(e, i)),
							)
						: null;
				}),
				Vt = r.memo(function () {
					var e = Yt("FooterComponent"),
						t = Kt("footerHeight"),
						n = Yt("headerFooterTag"),
						o = oe(function (e) {
							return t(le(e, "height"));
						}),
						i = Yt("context");
					return e
						? Object(r.createElement)(
								n,
								{ ref: o },
								Object(r.createElement)(e, Mt(e, i)),
							)
						: null;
				});
			function Wt(e) {
				var t = e.usePublisher,
					n = e.useEmitter,
					o = e.useEmitterValue;
				return r.memo(function (e) {
					var i = e.style,
						l = e.children,
						u = q(e, xt),
						a = t("scrollContainerState"),
						c = o("ScrollerComponent"),
						s = t("smoothScrollTargetReached"),
						f = o("scrollerRef"),
						d = o("context"),
						m = ue(a, s, c, f),
						h = m.scrollerRef,
						p = m.scrollByCallback;
					return (
						n("scrollTo", m.scrollToCallback),
						n("scrollBy", p),
						Object(r.createElement)(
							c,
							Y(
								{
									ref: h,
									style: Y({}, Lt, i),
									"data-test-id": "virtuoso-scroller",
									"data-virtuoso-scroller": !0,
									tabIndex: 0,
								},
								u,
								Mt(c, d),
							),
							l,
						)
					);
				});
			}
			function Ft(e) {
				var t = e.usePublisher,
					n = e.useEmitter,
					o = e.useEmitterValue;
				return r.memo(function (e) {
					var i = e.style,
						l = e.children,
						u = q(e, yt),
						a = t("windowScrollContainerState"),
						c = o("ScrollerComponent"),
						s = t("smoothScrollTargetReached"),
						f = o("totalListHeight"),
						d = o("deviation"),
						m = o("customScrollParent"),
						p = o("context"),
						v = ue(a, s, c, h, m),
						g = v.scrollerRef,
						I = v.scrollByCallback,
						C = v.scrollToCallback;
					return (
						ee(
							function () {
								return (
									(g.current = m || window),
									function () {
										g.current = null;
									}
								);
							},
							[g, m],
						),
						n("windowScrollTo", C),
						n("scrollBy", I),
						Object(r.createElement)(
							c,
							Y(
								{
									style: Y(
										{ position: "relative" },
										i,
										0 !== f ? { height: f + d } : {},
									),
									"data-virtuoso-scroller": !0,
								},
								u,
								Mt(c, p),
							),
							l,
						)
					);
				});
			}
			var Dt = function (e) {
					var t = e.children,
						n = oe(
							o(Kt("viewportHeight"), function (e) {
								return le(e, "height");
							}),
						);
					return r.createElement(
						"div",
						{ style: Pt, ref: n, "data-viewport-type": "element" },
						t,
					);
				},
				Gt = function (e) {
					var t = e.children,
						n = wt(Kt("windowViewportRect"), Yt("customScrollParent"));
					return r.createElement(
						"div",
						{ ref: n, style: Pt, "data-viewport-type": "window" },
						t,
					);
				},
				Nt = function (e) {
					var t = e.children,
						n = Yt("TopItemListComponent"),
						o = Yt("headerHeight"),
						i = Y({}, jt, { marginTop: o + "px" }),
						l = Yt("context");
					return Object(r.createElement)(
						n || "div",
						{ style: i, context: l },
						t,
					);
				},
				Ut = K(
					Rt,
					{
						required: {},
						optional: {
							context: "context",
							followOutput: "followOutput",
							firstItemIndex: "firstItemIndex",
							itemContent: "itemContent",
							groupContent: "groupContent",
							overscan: "overscan",
							increaseViewportBy: "increaseViewportBy",
							totalCount: "totalCount",
							topItemCount: "topItemCount",
							initialTopMostItemIndex: "initialTopMostItemIndex",
							components: "components",
							groupCounts: "groupCounts",
							atBottomThreshold: "atBottomThreshold",
							atTopThreshold: "atTopThreshold",
							computeItemKey: "computeItemKey",
							defaultItemHeight: "defaultItemHeight",
							fixedItemHeight: "fixedItemHeight",
							itemSize: "itemSize",
							scrollSeekConfiguration: "scrollSeekConfiguration",
							headerFooterTag: "headerFooterTag",
							data: "data",
							initialItemCount: "initialItemCount",
							initialScrollTop: "initialScrollTop",
							alignToBottom: "alignToBottom",
							useWindowScroll: "useWindowScroll",
							customScrollParent: "customScrollParent",
							scrollerRef: "scrollerRef",
							logLevel: "logLevel",
							react18ConcurrentRendering: "react18ConcurrentRendering",
							item: "item",
							group: "group",
							topItems: "topItems",
							itemHeight: "itemHeight",
							scrollingStateChange: "scrollingStateChange",
							maxHeightCacheSize: "maxHeightCacheSize",
							footer: "footer",
							header: "header",
							ItemContainer: "ItemContainer",
							ScrollContainer: "ScrollContainer",
							ListContainer: "ListContainer",
							GroupContainer: "GroupContainer",
							emptyComponent: "emptyComponent",
							HeaderContainer: "HeaderContainer",
							FooterContainer: "FooterContainer",
							scrollSeek: "scrollSeek",
						},
						methods: {
							scrollToIndex: "scrollToIndex",
							scrollIntoView: "scrollIntoView",
							scrollTo: "scrollTo",
							scrollBy: "scrollBy",
							adjustForPrependedItems: "adjustForPrependedItems",
						},
						events: {
							isScrolling: "isScrolling",
							endReached: "endReached",
							startReached: "startReached",
							rangeChanged: "rangeChanged",
							atBottomStateChange: "atBottomStateChange",
							atTopStateChange: "atTopStateChange",
							totalListHeightChanged: "totalListHeightChanged",
							itemsRendered: "itemsRendered",
							groupIndices: "groupIndices",
						},
					},
					r.memo(function (e) {
						var t = Yt("useWindowScroll"),
							n = Yt("topItemsIndexes").length > 0,
							o = Yt("customScrollParent"),
							i = o || t ? Gt : Dt;
						return r.createElement(
							o || t ? Zt : Jt,
							Y({}, e),
							r.createElement(
								i,
								null,
								r.createElement(At, null),
								r.createElement(Bt, null),
								r.createElement(Vt, null),
							),
							n &&
								r.createElement(
									Nt,
									null,
									r.createElement(Bt, { showTopList: !0 }),
								),
						);
					}),
				),
				_t = Ut.Component,
				Kt = Ut.usePublisher,
				Yt = Ut.useEmitterValue,
				qt = Ut.useEmitter,
				Jt = Wt({ usePublisher: Kt, useEmitterValue: Yt, useEmitter: qt }),
				Zt = Ft({ usePublisher: Kt, useEmitterValue: Yt, useEmitter: qt }),
				$t = {
					items: [],
					offsetBottom: 0,
					offsetTop: 0,
					top: 0,
					bottom: 0,
					itemHeight: 0,
					itemWidth: 0,
				},
				Qt = {
					items: [{ index: 0 }],
					offsetBottom: 0,
					offsetTop: 0,
					top: 0,
					bottom: 0,
					itemHeight: 0,
					itemWidth: 0,
				},
				Xt = Math.round,
				en = Math.ceil,
				tn = Math.floor,
				nn = Math.min,
				rn = Math.max;
			function on(e, t) {
				return Array.from({ length: t - e + 1 }).map(function (t, n) {
					return { index: n + e };
				});
			}
			var ln = F(
				function (e) {
					var t = e[0],
						n = t.overscan,
						r = t.visibleRange,
						o = t.listBoundary,
						i = e[1],
						l = i.scrollTop,
						u = i.viewportHeight,
						a = i.scrollBy,
						c = i.scrollTo,
						s = i.smoothScrollTargetReached,
						f = i.scrollContainerState,
						d = e[2],
						m = e[3],
						h = e[4],
						p = h.propsReady,
						v = h.didMount,
						g = e[5],
						I = g.windowViewportRect,
						S = g.windowScrollTo,
						b = g.useWindowScroll,
						H = g.customScrollParent,
						B = g.windowScrollContainerState,
						L = T(0),
						P = T(0),
						j = T($t),
						A = T({ height: 0, width: 0 }),
						F = T({ height: 0, width: 0 }),
						D = w(),
						G = w(),
						N = T(0);
					C(
						E(
							v,
							M(P),
							k(function (e) {
								return 0 !== e[1];
							}),
							z(function (e) {
								return {
									items: on(0, e[1] - 1),
									top: 0,
									bottom: 0,
									offsetBottom: 0,
									offsetTop: 0,
									itemHeight: 0,
									itemWidth: 0,
								};
							}),
						),
						j,
					),
						C(
							E(
								W(
									V(L),
									r,
									V(F, function (e, t) {
										return e && e.width === t.width && e.height === t.height;
									}),
								),
								M(A),
								z(function (e) {
									var t = e[0],
										n = t[0],
										r = t[1],
										o = r[0],
										i = r[1],
										l = t[2],
										u = e[1],
										a = l.height,
										c = l.width,
										s = u.width;
									if (0 === n || 0 === s) return $t;
									if (0 === c) return Qt;
									var f = cn(s, c),
										d = f * tn(o / a),
										m = f * en(i / a) - 1;
									m = nn(n - 1, m);
									var h = on((d = nn(m, rn(0, d))), m),
										p = un(u, l, h),
										v = p.top,
										g = p.bottom;
									return {
										items: h,
										offsetTop: v,
										offsetBottom: en(n / f) * a - g,
										top: v,
										bottom: g,
										itemHeight: a,
										itemWidth: c,
									};
								}),
							),
							j,
						),
						C(
							E(
								A,
								z(function (e) {
									return e.height;
								}),
							),
							u,
						),
						C(
							E(
								W(A, F, j),
								z(function (e) {
									var t = un(e[0], e[1], e[2].items);
									return [t.top, t.bottom];
								}),
								R(Ze),
							),
							o,
						);
					var U = x(
							E(
								V(j),
								k(function (e) {
									return e.items.length > 0;
								}),
								M(L),
								k(function (e) {
									var t = e[0].items;
									return t[t.length - 1].index === e[1] - 1;
								}),
								z(function (e) {
									return e[1] - 1;
								}),
								R(),
							),
						),
						_ = x(
							E(
								V(j),
								k(function (e) {
									var t = e.items;
									return t.length > 0 && 0 === t[0].index;
								}),
								O(0),
								R(),
							),
						),
						K = x(
							E(
								V(j),
								k(function (e) {
									return e.items.length > 0;
								}),
								z(function (e) {
									var t = e.items;
									return {
										startIndex: t[0].index,
										endIndex: t[t.length - 1].index,
									};
								}),
								R($e),
							),
						);
					C(K, m.scrollSeekRangeChanged),
						C(
							E(
								D,
								M(A, F, L),
								z(function (e) {
									var t = e[1],
										n = e[2],
										r = e[3],
										o = We(e[0]),
										i = o.align,
										l = o.behavior,
										u = o.offset,
										a = o.index;
									"LAST" === a && (a = r - 1);
									var c = an(t, n, (a = rn(0, a, nn(r - 1, a))));
									return (
										"end" === i
											? (c = Xt(c - t.height + n.height))
											: "center" === i &&
												(c = Xt(c - t.height / 2 + n.height / 2)),
										u && (c += u),
										{ top: c, behavior: l }
									);
								}),
							),
							c,
						);
					var q = y(
						E(
							j,
							z(function (e) {
								return e.offsetBottom + e.bottom;
							}),
						),
						0,
					);
					return (
						C(
							E(
								I,
								z(function (e) {
									return { width: e.visibleWidth, height: e.visibleHeight };
								}),
							),
							A,
						),
						Y(
							{
								totalCount: L,
								viewportDimensions: A,
								itemDimensions: F,
								scrollTop: l,
								scrollHeight: G,
								overscan: n,
								scrollBy: a,
								scrollTo: c,
								scrollToIndex: D,
								smoothScrollTargetReached: s,
								windowViewportRect: I,
								windowScrollTo: S,
								useWindowScroll: b,
								customScrollParent: H,
								windowScrollContainerState: B,
								deviation: N,
								scrollContainerState: f,
								initialItemCount: P,
							},
							m,
							{ gridState: j, totalListHeight: q },
							d,
							{
								startReached: _,
								endReached: U,
								rangeChanged: K,
								propsReady: p,
							},
						)
					);
				},
				s(et, ae, Ne, ct, Ue, pt),
			);
			function un(e, t, n) {
				var r = t.height;
				return void 0 === r || 0 === n.length
					? { top: 0, bottom: 0 }
					: {
							top: an(e, t, n[0].index),
							bottom: an(e, t, n[n.length - 1].index) + r,
						};
			}
			function an(e, t, n) {
				var r = cn(e.width, t.width);
				return tn(n / r) * t.height;
			}
			function cn(e, t) {
				return rn(1, tn(e / t));
			}
			var sn = ["placeholder"],
				fn = F(function () {
					var e = T(function (e) {
							return "Item " + e;
						}),
						t = T({}),
						n = T(null),
						r = T("virtuoso-grid-item"),
						o = T("virtuoso-grid-list"),
						i = T(bt),
						l = T(h),
						u = function (e, n) {
							return (
								void 0 === n && (n = null),
								y(
									E(
										t,
										z(function (t) {
											return t[e];
										}),
										R(),
									),
									n,
								)
							);
						};
					return {
						context: n,
						itemContent: e,
						components: t,
						computeItemKey: i,
						itemClassName: r,
						listClassName: o,
						scrollerRef: l,
						ListComponent: u("List", "div"),
						ItemComponent: u("Item", "div"),
						ScrollerComponent: u("Scroller", "div"),
						ScrollSeekPlaceholder: u("ScrollSeekPlaceholder", "div"),
					};
				}),
				dn = F(
					function (e) {
						var t = e[0],
							n = e[1],
							r = {
								item: Ht(
									n.itemContent,
									"Rename the %citem%c prop to %citemContent.",
								),
								ItemContainer: w(),
								ScrollContainer: w(),
								ListContainer: w(),
								emptyComponent: w(),
								scrollSeek: w(),
							};
						function o(e, t, r) {
							C(
								E(
									e,
									M(n.components),
									z(function (e) {
										var n,
											o = e[0],
											i = e[1];
										return (
											console.warn(
												"react-virtuoso: " +
													r +
													" property is deprecated. Pass components." +
													t +
													" instead.",
											),
											Y({}, i, (((n = {})[t] = o), n))
										);
									}),
								),
								n.components,
							);
						}
						return (
							p(r.scrollSeek, function (e) {
								var r = e.placeholder,
									o = q(e, sn);
								console.warn(
									"react-virtuoso: scrollSeek property is deprecated. Pass scrollSeekConfiguration and specify the placeholder in components.ScrollSeekPlaceholder instead.",
								),
									v(
										n.components,
										Y({}, I(n.components), { ScrollSeekPlaceholder: r }),
									),
									v(t.scrollSeekConfiguration, o);
							}),
							o(r.ItemContainer, "Item", "ItemContainer"),
							o(r.ListContainer, "List", "ListContainer"),
							o(r.ScrollContainer, "Scroller", "ScrollContainer"),
							Y({}, t, n, r)
						);
					},
					s(ln, fn),
				),
				mn = r.memo(function () {
					var e = In("gridState"),
						t = In("listClassName"),
						n = In("itemClassName"),
						o = In("itemContent"),
						i = In("computeItemKey"),
						l = In("isSeeking"),
						u = gn("scrollHeight"),
						a = In("ItemComponent"),
						c = In("ListComponent"),
						s = In("ScrollSeekPlaceholder"),
						f = In("context"),
						d = gn("itemDimensions"),
						m = oe(function (e) {
							u(e.parentElement.parentElement.scrollHeight);
							var t = e.firstChild;
							t && d(t.getBoundingClientRect());
						});
					return Object(r.createElement)(
						c,
						Y({ ref: m, className: t }, Mt(c, f), {
							style: { paddingTop: e.offsetTop, paddingBottom: e.offsetBottom },
						}),
						e.items.map(function (t) {
							var u = i(t.index);
							return l
								? Object(r.createElement)(
										s,
										Y({ key: u }, Mt(s, f), {
											index: t.index,
											height: e.itemHeight,
											width: e.itemWidth,
										}),
									)
								: Object(r.createElement)(
										a,
										Y({}, Mt(a, f), {
											className: n,
											"data-index": t.index,
											key: u,
										}),
										o(t.index, f),
									);
						}),
					);
				}),
				hn = function (e) {
					var t = e.children,
						n = gn("viewportDimensions"),
						o = oe(function (e) {
							n(e.getBoundingClientRect());
						});
					return r.createElement("div", { style: Pt, ref: o }, t);
				},
				pn = function (e) {
					var t = e.children,
						n = wt(gn("windowViewportRect"), In("customScrollParent"));
					return r.createElement("div", { ref: n, style: Pt }, t);
				},
				vn = K(
					dn,
					{
						optional: {
							totalCount: "totalCount",
							overscan: "overscan",
							itemContent: "itemContent",
							components: "components",
							computeItemKey: "computeItemKey",
							initialItemCount: "initialItemCount",
							scrollSeekConfiguration: "scrollSeekConfiguration",
							listClassName: "listClassName",
							itemClassName: "itemClassName",
							useWindowScroll: "useWindowScroll",
							customScrollParent: "customScrollParent",
							scrollerRef: "scrollerRef",
							item: "item",
							ItemContainer: "ItemContainer",
							ScrollContainer: "ScrollContainer",
							ListContainer: "ListContainer",
							scrollSeek: "scrollSeek",
						},
						methods: {
							scrollTo: "scrollTo",
							scrollBy: "scrollBy",
							scrollToIndex: "scrollToIndex",
						},
						events: {
							isScrolling: "isScrolling",
							endReached: "endReached",
							startReached: "startReached",
							rangeChanged: "rangeChanged",
							atBottomStateChange: "atBottomStateChange",
							atTopStateChange: "atTopStateChange",
						},
					},
					r.memo(function (e) {
						var t = Y({}, e),
							n = In("useWindowScroll"),
							o = In("customScrollParent"),
							i = o || n ? pn : hn;
						return r.createElement(
							o || n ? wn : Sn,
							Y({}, t),
							r.createElement(i, null, r.createElement(mn, null)),
						);
					}),
				),
				gn = (vn.Component, vn.usePublisher),
				In = vn.useEmitterValue,
				Cn = vn.useEmitter,
				Sn = Wt({ usePublisher: gn, useEmitterValue: In, useEmitter: Cn }),
				wn = Ft({ usePublisher: gn, useEmitterValue: In, useEmitter: Cn }),
				Tn = F(function () {
					var e = T(function (e) {
							return r.createElement("td", null, "Item $", e);
						}),
						t = T(null),
						n = T(null),
						o = T({}),
						i = T(bt),
						l = T(h),
						u = function (e, t) {
							return (
								void 0 === t && (t = null),
								y(
									E(
										o,
										z(function (t) {
											return t[e];
										}),
										R(),
									),
									t,
								)
							);
						};
					return {
						context: t,
						itemContent: e,
						fixedHeaderContent: n,
						components: o,
						computeItemKey: i,
						scrollerRef: l,
						TableComponent: u("Table", "table"),
						TableHeadComponent: u("TableHead", "thead"),
						TableBodyComponent: u("TableBody", "tbody"),
						TableRowComponent: u("TableRow", "tr"),
						ScrollerComponent: u("Scroller", "div"),
						EmptyPlaceholder: u("EmptyPlaceholder"),
						ScrollSeekPlaceholder: u("ScrollSeekPlaceholder"),
						FillerRow: u("FillerRow"),
					};
				}),
				xn = F(
					function (e) {
						return Y({}, e[0], e[1]);
					},
					s(Ct, Tn),
				),
				yn = function (e) {
					return r.createElement(
						"tr",
						null,
						r.createElement("td", { style: { height: e.height } }),
					);
				},
				bn = function (e) {
					return r.createElement(
						"tr",
						null,
						r.createElement("td", {
							style: { height: e.height, padding: 0, border: 0 },
						}),
					);
				},
				En = r.memo(function () {
					var e = On("listState"),
						t = zn("sizeRanges"),
						n = On("useWindowScroll"),
						o = On("customScrollParent"),
						i = zn("windowScrollContainerState"),
						l = zn("scrollContainerState"),
						u = o || n ? i : l,
						a = On("itemContent"),
						c = On("trackItemSizes"),
						s = ie(t, On("itemSize"), c, u, On("log"), o),
						f = s.callbackRef,
						d = s.ref,
						m = r.useState(0),
						h = m[0],
						p = m[1];
					Bn("deviation", function (e) {
						h !== e && ((d.current.style.marginTop = e + "px"), p(e));
					});
					var v = On("EmptyPlaceholder"),
						g = On("ScrollSeekPlaceholder") || yn,
						I = On("FillerRow") || bn,
						C = On("TableBodyComponent"),
						S = On("TableRowComponent"),
						w = On("computeItemKey"),
						T = On("isSeeking"),
						x = On("paddingTopAddition"),
						y = On("firstItemIndex"),
						b = On("statefulTotalCount"),
						E = On("context");
					if (0 === b && v) return Object(r.createElement)(v, Mt(v, E));
					var H = e.offsetTop + x + h,
						R = e.offsetBottom,
						k =
							H > 0
								? r.createElement(I, { height: H, key: "padding-top" })
								: null,
						z =
							R > 0
								? r.createElement(I, { height: R, key: "padding-bottom" })
								: null,
						O = e.items.map(function (e) {
							var t = e.originalIndex,
								n = w(t + y, e.data, E);
							return T
								? Object(r.createElement)(
										g,
										Y({}, Mt(g, E), {
											key: n,
											index: e.index,
											height: e.size,
											type: e.type || "item",
										}),
									)
								: Object(r.createElement)(
										S,
										Y({}, Mt(S, E), {
											key: n,
											"data-index": t,
											"data-known-size": e.size,
											"data-item-index": e.index,
											style: { overflowAnchor: "none" },
										}),
										a(e.index, e.data, E),
									);
						});
					return Object(r.createElement)(
						C,
						Y({ ref: f, "data-test-id": "virtuoso-item-list" }, Mt(C, E)),
						[k].concat(O, [z]),
					);
				}),
				Hn = function (e) {
					var t = e.children,
						n = oe(
							o(zn("viewportHeight"), function (e) {
								return le(e, "height");
							}),
						);
					return r.createElement(
						"div",
						{ style: Pt, ref: n, "data-viewport-type": "element" },
						t,
					);
				},
				Rn = function (e) {
					var t = e.children,
						n = wt(zn("windowViewportRect"), On("customScrollParent"));
					return r.createElement(
						"div",
						{ ref: n, style: Pt, "data-viewport-type": "window" },
						t,
					);
				},
				kn = K(
					xn,
					{
						required: {},
						optional: {
							context: "context",
							followOutput: "followOutput",
							firstItemIndex: "firstItemIndex",
							itemContent: "itemContent",
							fixedHeaderContent: "fixedHeaderContent",
							overscan: "overscan",
							increaseViewportBy: "increaseViewportBy",
							totalCount: "totalCount",
							topItemCount: "topItemCount",
							initialTopMostItemIndex: "initialTopMostItemIndex",
							components: "components",
							groupCounts: "groupCounts",
							atBottomThreshold: "atBottomThreshold",
							atTopThreshold: "atTopThreshold",
							computeItemKey: "computeItemKey",
							defaultItemHeight: "defaultItemHeight",
							fixedItemHeight: "fixedItemHeight",
							itemSize: "itemSize",
							scrollSeekConfiguration: "scrollSeekConfiguration",
							data: "data",
							initialItemCount: "initialItemCount",
							initialScrollTop: "initialScrollTop",
							alignToBottom: "alignToBottom",
							useWindowScroll: "useWindowScroll",
							customScrollParent: "customScrollParent",
							scrollerRef: "scrollerRef",
							logLevel: "logLevel",
							react18ConcurrentRendering: "react18ConcurrentRendering",
						},
						methods: {
							scrollToIndex: "scrollToIndex",
							scrollIntoView: "scrollIntoView",
							scrollTo: "scrollTo",
							scrollBy: "scrollBy",
						},
						events: {
							isScrolling: "isScrolling",
							endReached: "endReached",
							startReached: "startReached",
							rangeChanged: "rangeChanged",
							atBottomStateChange: "atBottomStateChange",
							atTopStateChange: "atTopStateChange",
							totalListHeightChanged: "totalListHeightChanged",
							itemsRendered: "itemsRendered",
							groupIndices: "groupIndices",
						},
					},
					r.memo(function (e) {
						var t = On("useWindowScroll"),
							n = On("customScrollParent"),
							i = zn("fixedHeaderHeight"),
							l = On("fixedHeaderContent"),
							u = On("context"),
							a = oe(
								o(i, function (e) {
									return le(e, "height");
								}),
							),
							c = n || t ? Pn : Ln,
							s = n || t ? Rn : Hn,
							f = On("TableComponent"),
							d = On("TableHeadComponent"),
							m = l
								? r.createElement(
										d,
										Y(
											{
												key: "TableHead",
												style: { zIndex: 1, position: "sticky", top: 0 },
												ref: a,
											},
											Mt(d, u),
										),
										l(),
									)
								: null;
						return r.createElement(
							c,
							Y({}, e),
							r.createElement(
								s,
								null,
								r.createElement(
									f,
									Y({ style: { borderSpacing: 0 } }, Mt(f, u)),
									[m, r.createElement(En, { key: "TableBody" })],
								),
							),
						);
					}),
				),
				zn = (kn.Component, kn.usePublisher),
				On = kn.useEmitterValue,
				Bn = kn.useEmitter,
				Ln = Wt({ usePublisher: zn, useEmitterValue: On, useEmitter: Bn }),
				Pn = Ft({ usePublisher: zn, useEmitterValue: On, useEmitter: Bn }),
				jn = _t;
		},
	},
]);
//# sourceMappingURL=chunk.22.js.map
