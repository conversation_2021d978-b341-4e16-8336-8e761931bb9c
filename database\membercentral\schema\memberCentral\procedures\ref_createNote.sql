ALTER PROC dbo.ref_createNote
@referralNote varchar(max),
@noteType char(1),
@clientReferralID int,
@createdDate datetime,
@createdBy int,
@followUpDate datetime,
@followUpStatus char(1),
@referralNoteID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @crlf varchar(10), @message varchar(max);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_clientReferrals AS cr
	INNER JOIN dbo.ref_referrals AS r ON r.referralID = cr.referralID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE cr.clientReferralID = @clientReferralID;

	INSERT INTO dbo.ref_notes(referralNote, noteType, clientReferralID, createdDate, createdBy, followUpDate, followUpStatus)
	VALUES (@referralNote, @noteType, @clientReferralID, @createdDate, @createdBy, @followUpDate, @followUpStatus);

	SELECT @referralNoteID = SCOPE_IDENTITY();

	SET @message = 'New ' + CASE @noteType WHEN 'C' THEN 'Counselor Note' ELSE 'Attorney Note' END + ' has been added.'
		+ @crlf + 'Date Created: ' + convert(varchar(20),@createdDate,120)
		+ @crlf + 'Description: ' + CASE WHEN LEN(@referralNote) <= 30 THEN @referralNote ELSE LEFT(@referralNote, CHARINDEX(' ', @referralNote + ' ', 30) - 1) + '...' END
		+ CASE WHEN @followUpDate IS NOT NULL THEN @crlf + 'Follow Up Date: ' + convert(varchar(20),@followUpDate,23) ELSE '' END
		+ CASE WHEN @followUpStatus IS NOT NULL THEN @crlf + 'Follow Up Status: ' + CASE @followUpStatus WHEN 'P' THEN 'Pending' ELSE 'Completed' END ELSE '' END;

	SET @message = STRING_ESCAPE(@message,'json');
	
	EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
		@message=@message, @enteredByMemberID=@createdBy;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
