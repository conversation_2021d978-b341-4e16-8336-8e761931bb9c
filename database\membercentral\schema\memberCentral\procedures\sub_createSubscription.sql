ALTER PROCEDURE dbo.sub_createSubscription
@orgID int,
@siteid INT,
@typeID INT,
@subName varchar(300),
@reportCode varchar(15),
@scheduleID INT,
@rateTermDateFlag varchar(1),
@autoExpire BIT,
@status varchar(1),
@soldSeparately BIT,
@paymentOrder INT,
@GLAccountID INT,
@allowRateGLOverride BIT,
@activationOptionCode varchar(1),
@alternateActivationOptionCode varchar(1),
@feRawContent varchar(max),
@feCompletedRawContent varchar(max),
@feParentSubRawContent varchar(max),
@isImport bit = 0,
@recordedByMemberID int,
@subID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;
	CREATE TABLE #tblMCQSubCond (conditionID int);

	DECLARE @activationOptionID int, @alternateActivationOptionID int, @subAdminResourceID INT, 
		@appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT, @completedContentID INT, 
		@completedContentSRID INT, @parentSubContentID INT, @parentSubContentSRID INT, @conditionKeyID INT, 
		@typeName varchar(100), @crlf varchar(10), @msgjson varchar(max), @GLAccountName varchar(max), @scheduleName varchar(200),
		@subActivationName varchar(100), @alternateSubActivationName varchar(100);

	SET @crlf = char(13) + char(10);

	SELECT @typeName = typeName
	from dbo.sub_Types
	where typeID = @typeID
	and siteID = @siteid;
	
	IF @typeName IS NULL
		RAISERROR('Invalid Subscription Type',16,1);

	SELECT @activationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode;

	SELECT @alternateActivationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @alternateActivationOptionCode;

	select @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
	from dbo.sites
	where siteID = @siteID;

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	SELECT @conditionKeyID = conditionKeyID 
	FROM dbo.ams_virtualGroupConditionKeys 
	WHERE conditionKey = 'subSubscription';

	-- get conditions that target all subscriptions for this subtype
	INSERT INTO #tblMCQSubCond (conditionID)
	SELECT DISTINCT c.conditionID
	FROM dbo.ams_virtualGroupConditions AS c
	INNER JOIN dbo.cache_members_conditions_subTypes AS t 
		ON t.orgID = @orgID
		AND t.conditionID = c.conditionID
		AND t.typeID = @typeID
	INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
		AND cv.conditionKeyID = @conditionKeyID
		AND cv.conditionValue = ''
	WHERE c.orgID = @orgID
	AND c.fieldCode ='sub_entry';

	select @GLAccountName = thePathExpanded
	from dbo.fn_getRecursiveGLAccounts(@orgID)
	where GLAccountID = @GLAccountID;

	select @scheduleName = scheduleName
	from dbo.sub_rateSchedules
	where scheduleID = @scheduleID
	and siteID = @siteID;

	select @subActivationName = subActivationName
	from dbo.sub_activationOptions
	where subActivationID = @activationOptionID;

	select @alternateSubActivationName = subActivationName
	from dbo.sub_activationOptions
	where subActivationID = @alternateActivationOptionID;

	BEGIN TRAN;	
		insert into dbo.sub_subscriptions (typeID, subscriptionName, reportCode, scheduleID, autoExpire, [status], 
			soldSeparately, paymentOrder, GLAccountID, rateTermDateFlag, subActivationID, 
			subAlternateActivationID, allowRateGLAccountOverride, orgID)
		values(@typeID, @subName, @reportCode, @scheduleID, @autoExpire, @status,
			@soldSeparately, @paymentOrder, @GLAccountID, @rateTermDateFlag, @activationOptionID, 
			@alternateActivationOptionID, @allowRateGLOverride, @orgID);
			SELECT @subID = SCOPE_IDENTITY();

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feCompletedRawContent, @memberID=null, @contentID=@completedContentID OUTPUT, @siteResourceID=@completedContentSRID OUTPUT;

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feParentSubRawContent, @memberID=null, @contentID=@parentSubContentID OUTPUT, @siteResourceID=@parentSubContentSRID OUTPUT;

		update dbo.sub_subscriptions 
		set frontEndContentID = @contentID, 
			frontEndCompletedContentID = @completedContentID,
			frontEndParentSubContentID = @parentSubContentID
		where subscriptionID = @subID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';

		SET @msgjson = 'New Subscription ' + QUOTENAME(@subName) + ' has been created under the Subscription Type ' + QUOTENAME(@typeName) + '.' +
			CASE WHEN NULLIF(@reportCode,'') IS NOT NULL THEN @crlf + 'Report Code: ' + @reportCode ELSE '' END +
			CASE WHEN NULLIF(@scheduleName,'') IS NOT NULL THEN @crlf + 'Rate Schedule: ' + @scheduleName ELSE '' END +
			CASE 
				WHEN @rateTermDateFlag IS NOT NULL THEN @crlf + 'Term Dates: ' + 
					CASE 
						WHEN @rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
						WHEN @rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
						WHEN @rateTermDateFlag = 'C' THEN 'Calculate term end date for term length (term start date is current day)'
						ELSE ''
					END
				ELSE ''
			END +
			CASE WHEN @autoExpire IS NOT NULL THEN @crlf + 'Auto Expire Subscribers: ' + CASE WHEN @autoExpire = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN NULLIF(@status,'') IS NOT NULL THEN @crlf + 'Status: ' + CASE WHEN @status = 'A' THEN 'Active' ELSE 'Inactive' END ELSE '' END +
			CASE WHEN @soldSeparately IS NOT NULL THEN @crlf + 'Sold Separately: ' + CASE WHEN @soldSeparately = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN @paymentOrder IS NOT NULL THEN @crlf + 'Payment Order: ' + CAST(@paymentOrder AS VARCHAR(10)) ELSE '' END +
			CASE WHEN NULLIF(@GLAccountName,'') IS NOT NULL THEN @crlf + 'GL Account Name: ' + @GLAccountName ELSE '' END +
			CASE WHEN @allowRateGLOverride IS NOT NULL THEN @crlf + 'Allow Rate GL Override: ' + CASE WHEN @allowRateGLOverride = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN @subActivationName IS NOT NULL THEN @crlf + 'Activation Option: ' + @subActivationName ELSE '' END +
			CASE WHEN @alternateSubActivationName IS NOT NULL THEN @crlf + 'Alternate Activation Option: ' + @alternateSubActivationName ELSE '' END;
		
		SET @msgjson = STRING_ESCAPE(@msgjson,'json');
		
		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	IF EXISTS (SELECT 1 FROM #tblMCQSubCond)
		EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
