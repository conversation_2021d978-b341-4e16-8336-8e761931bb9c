(window.webpackJsonp = window.webpackJsonp || []).push([
	[90],
	{
		2012: function (t, e, a) {
			"use strict";
			a.r(e);
			a(58), a(44);
			var n = a(0),
				r = a.n(n),
				s = a(82),
				o = a(3),
				i = a.n(o),
				l = a(68),
				c = a(70);
			function p() {
				return (p = Object.assign
					? Object.assign.bind()
					: function (t) {
							for (var e = 1; e < arguments.length; e++) {
								var a = arguments[e];
								for (var n in a)
									Object.prototype.hasOwnProperty.call(a, n) && (t[n] = a[n]);
							}
							return t;
						}).apply(this, arguments);
			}
			var u = {
					borderStyle: i.a.string,
					isFlyoutItem: i.a.bool,
					style: i.a.object,
					className: i.a.string,
					buttonType: i.a.string,
				},
				y = Object(n.forwardRef)(function (t, e) {
					var a = t.isFlyoutItem,
						n = t.borderStyle,
						o = t.style,
						i = t.className,
						u = t.buttonType,
						y = c.b[u],
						b = y.dataElement,
						d = y.icon,
						f = y.title,
						m = function () {};
					return a
						? r.a.createElement(
								l.a,
								p({}, t, { ref: e, onClick: m, additionalClass: "" }),
							)
						: r.a.createElement(s.a, {
								key: n,
								isActive: !1,
								onClick: m,
								dataElement: b,
								title: f,
								img: d,
								ariaPressed: !1,
								style: o,
								className: i,
							});
				});
			(y.propTypes = u), (y.displayName = "BorderStyleButton"), (e.default = y);
		},
	},
]);
//# sourceMappingURL=chunk.90.js.map
