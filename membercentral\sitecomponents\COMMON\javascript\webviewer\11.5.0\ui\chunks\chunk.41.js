(window.webpackJsonp = window.webpackJsonp || []).push([
	[41],
	{
		1594: function (t, e, n) {
			"use strict";
			n(8), n(54);
			var o = n(1),
				i = n(33),
				a = n(2),
				r = n(67),
				l = n(178);
			function c() {}
			e.a = function (t) {
				var e =
						arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : c,
					n =
						arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;
				return function (i) {
					return o.a.isWebViewerServerDocument() ? u(t, i, n) : s(t, e, i, n);
				};
			};
			var u = function (t, e, n) {
					return o.a.applyRedactions(t, n).then(function (t) {
						if (t && t.url)
							return Object(l.a)(e, {
								filename: "redacted.pdf",
								includeAnnotations: !0,
								externalURL: t.url,
							});
						console.warn("WebViewer Server did not return a valid result");
					});
				},
				s = function (t, e, n, l) {
					var c = {
						message: i.a.t("warning.redaction.applyMessage"),
						title: i.a.t("warning.redaction.applyTile"),
						confirmBtnText: i.a.t("action.apply"),
						onConfirm: function () {
							return (
								o.a
									.applyRedactions(t, l)
									.then(function () {
										e();
									})
									.catch(function (t) {
										return Object(r.b)(t);
									}),
								Promise.resolve()
							);
						},
					};
					return n(a.a.showWarningMessage(c));
				};
		},
		1595: function (t, e, n) {
			var o = n(30),
				i = n(1596);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const o = [];
								return (
									n.querySelectorAll(e).forEach((t) => o.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...t(e, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < e.length; o++) {
						const i = e[o];
						if (0 === o)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			o(i, a);
			t.exports = i.locals || {};
		},
		1596: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup{width:100%;margin:0;position:relative;max-width:none;border-radius:4px 4px 0 0;border:0;padding-bottom:24px;box-shadow:none}}.AnnotationStylePopup .back-to-menu-container{margin-top:var(--padding-medium);margin-right:var(--padding-medium);margin-left:var(--padding-medium);padding-bottom:var(--padding-small);border-bottom:1px solid var(--border)}.AnnotationStylePopup .Button.back-to-menu-button{margin:0;width:100%;height:32px;border-radius:0;justify-content:flex-start}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationStylePopup .Button.back-to-menu-button{width:100%;height:32px}}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1628: function (t, e, n) {
			"use strict";
			n(23),
				n(8),
				n(24),
				n(19),
				n(12),
				n(13),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22);
			var o = n(0),
				i = n.n(o),
				a = n(3),
				r = n.n(a),
				l = n(6),
				c = n(196),
				u = n(347),
				s = n(299),
				p = n(82),
				d = n(1),
				f = n(1548),
				m = n(200),
				h = n(38),
				y = n(1542),
				b = n(2),
				v = n(4),
				g = n(5),
				w = n(1564);
			n(1595);
			function A(t) {
				return (A =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function E(t, e, n) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== A(t) || null === t) return t;
							var n = t[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(t, e || "default");
								if ("object" !== A(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === A(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = n),
					t
				);
			}
			function O(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var o,
								i,
								a,
								r,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (i = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((r = n.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return x(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return x(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function x(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, o = new Array(e); n < e; n++) o[n] = t[n];
				return o;
			}
			var P = {
					annotations: r.a.array.isRequired,
					style: r.a.object.isRequired,
					properties: r.a.object.isRequired,
					isRedaction: r.a.bool,
					isFreeText: r.a.bool,
					isEllipse: r.a.bool,
					hasBackToMenu: r.a.bool,
					onBackToMenu: r.a.func,
				},
				S = function (t) {
					var e = t.annotations,
						n = t.style,
						a = t.isRedaction,
						r = t.isFreeText,
						A = t.isEllipse,
						x = t.isMeasure,
						P = t.colorMapKey,
						S = t.showLineStyleOptions,
						T = t.properties,
						C = t.hideSnapModeCheckbox,
						N = t.onResize,
						k = t.hasBackToMenu,
						L = t.onBackToMenu,
						j = O(
							Object(l.e)(function (t) {
								return [
									v.a.isElementDisabled(t, g.a.ANNOTATION_STYLE_POPUP),
									v.a.isToolDefaultStyleUpdateFromAnnotationPopupEnabled(t),
									v.a.getActiveDocumentViewerKey(t),
								];
							}),
							3,
						),
						_ = j[0],
						F = j[1],
						D = j[2],
						I = Object(l.d)(),
						M = O(Object(u.a)(), 1)[0],
						R = O(Object(o.useState)(T.isAutoSizeFont), 2),
						B = R[0],
						U = R[1],
						z = function (t, n) {
							var o = d.a.getAnnotationManager(D);
							e.forEach(function (e) {
								(e[t] = n),
									"StrokeThickness" === t && Object(y.a)(e),
									o.redrawAnnotation(e),
									e instanceof window.Core.Annotations.WidgetAnnotation &&
										e.refresh();
							});
						},
						W = function (t, n) {
							e.forEach(function (e) {
								d.a.setAnnotationStyles(e, E({}, t, n), D),
									F && Object(m.a)(e.ToolName, t, n),
									("FontSize" !== t && "Font" !== t) || Object(y.a)(e);
							});
						},
						G = function (t, n) {
							e.forEach(function (e) {
								d.a.setAnnotationStyles(e, E({}, t, n), D),
									F && Object(m.a)(e.ToolName, t, n);
							});
						},
						Y = function (t, n) {
							e.forEach(function (e) {
								d.a.updateAnnotationRichTextStyle(e, E({}, t, n), D);
							});
						},
						V = function (t, n) {
							e.forEach(function (e) {
								var o = "";
								if ("start" === t) e.setStartStyle(n), (o = "StartLineStyle");
								else if ("end" === t) e.setEndStyle(n), (o = "EndLineStyle");
								else if ("middle" === t) {
									var i = n.split(","),
										a = i.shift();
									(e.Style = a), (e.Dashes = i), (o = "StrokeStyle");
								}
								F && Object(m.a)(e.ToolName, o, n),
									d.a.getAnnotationManager(D).redrawAnnotation(e);
							}),
								d.a
									.getAnnotationManager(D)
									.trigger("annotationChanged", [e, "modify", {}]);
						},
						H = function (t) {
							Object(h.k)() &&
								t.target === t.currentTarget &&
								I(b.a.closeElement(g.a.ANNOTATION_POPUP));
						},
						K = Object(f.a)("Popup AnnotationStylePopup", t);
					return _
						? null
						: i.a.createElement(
								c.a,
								{
									onResize: function () {
										N && N();
									},
								},
								function (t) {
									var o = t.measureRef;
									return i.a.createElement(
										"div",
										{
											className: K,
											"data-element": g.a.ANNOTATION_STYLE_POPUP,
											onClick: H,
											ref: o,
										},
										k &&
											i.a.createElement(
												"div",
												{
													className: "back-to-menu-container",
													"data-element":
														g.a.ANNOTATION_STYLE_POPUP_BACK_BUTTON_CONTAINER,
												},
												i.a.createElement(p.a, {
													className: "back-to-menu-button",
													dataElement: g.a.ANNOTATION_STYLE_POPUP_BACK_BUTTON,
													label: M("action.backToMenu"),
													img: "icon-chevron-left",
													onClick: L,
												}),
											),
										i.a.createElement(s.a, {
											hideSnapModeCheckbox: C,
											colorMapKey: P,
											style: n,
											isFreeText: r,
											isFreeTextAutoSize: B,
											onFreeTextSizeToggle: function () {
												return Object(w.a)(e[0], U, B);
											},
											isEllipse: A,
											isMeasure: x,
											onStyleChange: G,
											onSliderChange: z,
											onPropertyChange: W,
											disableSeparator: !0,
											properties: T,
											onRichTextStyleChange: Y,
											isRedaction: a,
											showLineStyleOptions: S,
											onLineStyleChange: V,
										}),
									);
								},
							);
				};
			S.propTypes = P;
			var T = S;
			e.a = T;
		},
		1794: function (t, e, n) {
			var o = n(30),
				i = n(1857);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const o = [];
								return (
									n.querySelectorAll(e).forEach((t) => o.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...t(e, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < e.length; o++) {
						const i = e[o];
						if (0 === o)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			o(i, a);
			t.exports = i.locals || {};
		},
		1855: function (t, e, n) {
			var o = n(30),
				i = n(1856);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const o = [];
								return (
									n.querySelectorAll(e).forEach((t) => o.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...t(e, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < e.length; o++) {
						const i = e[o];
						if (0 === o)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			o(i, a);
			t.exports = i.locals || {};
		},
		1856: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.CalibrationPopup{display:flex;flex-direction:column;align-items:flex-start;padding:var(--padding-medium);width:220px;background:var(--gray-0);box-shadow:0 0 3px var(--gray-7);border-radius:4px}.CalibrationPopup .calibration-popup-label{font-weight:700;margin-bottom:var(--padding-medium)}.CalibrationPopup .pop-switch{margin-top:var(--padding-medium)}.CalibrationPopup .pop-switch.ui__choice--disabled .ui__choice__label{color:var(--gray-5)}.CalibrationPopup .input-container{display:flex;flex-direction:row;justify-content:space-between;align-items:flex-start;grid-gap:var(--padding-small);gap:var(--padding-small);height:32px}.CalibrationPopup .input-container .input-field{width:94px;height:32px}.CalibrationPopup .input-container .input-field.invalid-value{border-color:red}.CalibrationPopup .input-container .input-field:focus{border:1px solid var(--blue-5)}.CalibrationPopup .input-container .input-field .Dropdown__wrapper{width:100%;height:100%}.CalibrationPopup .input-container .input-field .Dropdown__wrapper .Dropdown{height:100%;width:100%!important;text-align:left}.CalibrationPopup .input-container .input-field .Dropdown__wrapper .Dropdown__items{width:100%}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1857: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				".open.AnnotationPopup{visibility:visible}.closed.AnnotationPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.AnnotationPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.AnnotationPopup:empty{padding:0}.AnnotationPopup .buttons{display:flex}.AnnotationPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationPopup .Button{width:42px;height:42px}}.AnnotationPopup .Button:hover{background:var(--popup-button-hover)}.AnnotationPopup .Button:hover:disabled{background:none}.AnnotationPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .AnnotationPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .AnnotationPopup .Button .Icon{width:24px;height:24px}}.is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.AnnotationPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.AnnotationPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.AnnotationPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.AnnotationPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.AnnotationPopup{position:fixed;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.AnnotationPopup.is-horizontal .container{display:inherit}.AnnotationPopup.is-vertical{flex-direction:column;align-items:flex-start}.shortCuts3D{position:relative}.shortCuts3D .closeButton{position:absolute;right:4px;padding:2px;cursor:pointer;width:20px;text-align:center}.shortCuts3D .row{padding:4px 0;margin:8px 26px 8px 16px}.shortCuts3D .row span{background:#e7ebee;padding:4px 8px;border-radius:4px}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2020: function (t, e, n) {
			"use strict";
			n.r(e);
			n(46),
				n(52),
				n(88),
				n(8),
				n(32),
				n(23),
				n(24),
				n(19),
				n(12),
				n(13),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(54),
				n(22),
				n(61),
				n(62),
				n(63),
				n(64),
				n(37),
				n(39),
				n(40),
				n(60);
			var o = n(0),
				i = n.n(o),
				a = n(6),
				r = n(3),
				l = n.n(r),
				c = n(111),
				u = n.n(c),
				s = n(165),
				p = n(1),
				d = n(291),
				f = n(1594),
				m = n(38),
				h = n(347),
				y = n(210),
				b = n(463),
				v = n(35),
				g = n(140),
				w = n(2),
				A = n(4),
				E = n(5),
				O = n(59),
				x = n(21),
				P = (n(36), n(27), n(28), n(25), n(29), n(47), n(49), n(48), n(177)),
				S = n.n(P),
				T = n(17),
				C = n.n(T),
				N = n(302),
				k = n(82),
				L = n(1628);
			function j(t) {
				return (j =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function _() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _ =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					o =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					i = "function" == typeof Symbol ? Symbol : {},
					a = i.iterator || "@@iterator",
					r = i.asyncIterator || "@@asyncIterator",
					l = i.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, i) {
					var a = e && e.prototype instanceof d ? e : d,
						r = Object.create(a.prototype),
						l = new P(i || []);
					return o(r, "_invoke", { value: A(t, n, l) }), r;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var p = {};
				function d() {}
				function f() {}
				function m() {}
				var h = {};
				c(h, a, function () {
					return this;
				});
				var y = Object.getPrototypeOf,
					b = y && y(y(S([])));
				b && b !== e && n.call(b, a) && (h = b);
				var v = (m.prototype = d.prototype = Object.create(h));
				function g(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function w(t, e) {
					var i;
					o(this, "_invoke", {
						value: function (o, a) {
							function r() {
								return new e(function (i, r) {
									!(function o(i, a, r, l) {
										var c = s(t[i], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												p = u.value;
											return p && "object" == j(p) && n.call(p, "__await")
												? e.resolve(p.__await).then(
														function (t) {
															o("next", t, r, l);
														},
														function (t) {
															o("throw", t, r, l);
														},
													)
												: e.resolve(p).then(
														function (t) {
															(u.value = t), r(u);
														},
														function (t) {
															return o("throw", t, r, l);
														},
													);
										}
										l(c.arg);
									})(o, a, i, r);
								});
							}
							return (i = i ? i.then(r, r) : r());
						},
					});
				}
				function A(t, e, n) {
					var o = "suspendedStart";
					return function (i, a) {
						if ("executing" === o)
							throw new Error("Generator is already running");
						if ("completed" === o) {
							if ("throw" === i) throw a;
							return T();
						}
						for (n.method = i, n.arg = a; ; ) {
							var r = n.delegate;
							if (r) {
								var l = E(r, n);
								if (l) {
									if (l === p) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === o) throw ((o = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							o = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((o = n.done ? "completed" : "suspendedYield"), c.arg === p)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((o = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function E(t, e) {
					var n = e.method,
						o = t.iterator[n];
					if (void 0 === o)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								E(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							p
						);
					var i = s(o, t.iterator, e.arg);
					if ("throw" === i.type)
						return (
							(e.method = "throw"), (e.arg = i.arg), (e.delegate = null), p
						);
					var a = i.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								p)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							p);
				}
				function O(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function x(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function P(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(O, this),
						this.reset(!0);
				}
				function S(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var o = -1,
								i = function e() {
									for (; ++o < t.length; )
										if (n.call(t, o)) return (e.value = t[o]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (i.next = i);
						}
					}
					return { next: T };
				}
				function T() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = m),
					o(v, "constructor", { value: m, configurable: !0 }),
					o(m, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(m, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, m)
								: ((t.__proto__ = m), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					g(w.prototype),
					c(w.prototype, r, function () {
						return this;
					}),
					(t.AsyncIterator = w),
					(t.async = function (e, n, o, i, a) {
						void 0 === a && (a = Promise);
						var r = new w(u(e, n, o, i), a);
						return t.isGeneratorFunction(n)
							? r
							: r.next().then(function (t) {
									return t.done ? t.value : r.next();
								});
					}),
					g(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var o in e) n.push(o);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var o = n.pop();
									if (o in e) return (t.value = o), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = S),
					(P.prototype = {
						constructor: P,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(x),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function o(n, o) {
								return (
									(r.type = "throw"),
									(r.arg = t),
									(e.next = n),
									o && ((e.method = "next"), (e.arg = void 0)),
									!!o
								);
							}
							for (var i = this.tryEntries.length - 1; i >= 0; --i) {
								var a = this.tryEntries[i],
									r = a.completion;
								if ("root" === a.tryLoc) return o("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var i = this.tryEntries[o];
								if (
									i.tryLoc <= this.prev &&
									n.call(i, "finallyLoc") &&
									this.prev < i.finallyLoc
								) {
									var a = i;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var r = a ? a.completion : {};
							return (
								(r.type = t),
								(r.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), p)
									: this.complete(r)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								p
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), x(n), p;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var o = n.completion;
									if ("throw" === o.type) {
										var i = o.arg;
										x(n);
									}
									return i;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: S(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								p
							);
						},
					}),
					t
				);
			}
			function F(t, e, n, o, i, a, r) {
				try {
					var l = t[a](r),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(o, i);
			}
			var D = function (t) {
				var e = t.onClick,
					n = t.annotation,
					a = t.onDatePickerShow,
					r = Object(o.useRef)(null),
					l = Object(o.useRef)(null);
				return (
					Object(o.useEffect)(function () {
						var t;
						return (
							(function () {
								var o,
									i =
										((o = _().mark(function o() {
											return _().wrap(function (o) {
												for (;;)
													switch ((o.prev = o.next)) {
														case 0:
															return (
																(o.next = 2),
																window.Core.createDatePicker({
																	field: r.current,
																	onClick: e,
																	container: l.current,
																	format: n.getDateFormat(),
																})
															);
														case 2:
															(t = o.sent), a(!0);
														case 4:
														case "end":
															return o.stop();
													}
											}, o);
										})),
										function () {
											var t = this,
												e = arguments;
											return new Promise(function (n, i) {
												var a = o.apply(t, e);
												function r(t) {
													F(a, n, i, r, l, "next", t);
												}
												function l(t) {
													F(a, n, i, r, l, "throw", t);
												}
												r(void 0);
											});
										});
								return function () {
									return i.apply(this, arguments);
								};
							})()(),
							function () {
								t.destroy(), (t = null), a(!1);
							}
						);
					}, []),
					i.a.createElement(
						"div",
						{ "data-element": "datePickerContainer" },
						i.a.createElement("div", { ref: r }),
						i.a.createElement("div", { ref: l }),
					)
				);
			};
			D.propTypes = {
				onClick: l.a.func.isRequired,
				annotation: l.a.object.isRequired,
				onDatePickerShow: l.a.func.isRequired,
			};
			var I = D,
				M = n(1778),
				R = (n(154), n(139), n(1355)),
				B = n(78),
				U = n(117),
				z = n(384);
			n(1855);
			function W(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var o,
								i,
								a,
								r,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (i = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((r = n.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return G(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return G(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function G(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, o = new Array(e); n < e; n++) o[n] = t[n];
				return o;
			}
			var Y = window.Core.Scale,
				V = function (t) {
					var e = t.Measure.axis[0].factor;
					switch (t.Scale[1][1]) {
						case "ft-in":
							return (t.getLineLength() * e) / 12;
						case "in":
						default:
							return t.getLineLength() * e;
					}
				},
				H = { annotation: l.a.shape({ Scale: l.a.arrayOf(l.a.array) }) },
				K = function (t) {
					var e,
						n,
						r = t.annotation,
						l = W(Object(h.a)(), 1)[0],
						c = Object(a.d)(),
						u = W(
							Object(a.e)(function (t) {
								return [A.a.getMeasurementUnits(t), A.a.getCalibrationInfo(t)];
							}, a.c),
							2,
						),
						s = u[0],
						d = u[1],
						f = d.tempScale,
						m = d.isFractionalUnit,
						y = d.defaultUnit,
						b = W(Object(o.useState)(""), 2),
						v = b[0],
						g = b[1],
						E = Object(o.useRef)(null),
						O =
							(null === (e = new Y(f).worldScale) || void 0 === e
								? void 0
								: e.unit) || "mm",
						x = m
							? s.to.filter(function (t) {
									return z.e.includes(t);
								})
							: s.to,
						P = !z.e.includes(O),
						S = m || "ft-in" === O ? "text" : "number",
						T = C()("input-field", {
							"invalid-value": !(
								f &&
								(null === (n = new Y(f).worldScale) || void 0 === n
									? void 0
									: n.value) > 0
							),
						}),
						N = function (t, e) {
							var n,
								o = V(r),
								i = r.Scale,
								a = o / i[1][0],
								l = [i[0][0] * a, i[0][1]],
								u = "pt" === (n = e) ? "pt" : z.m.includes(n) ? "mm" : "in",
								s = Object(z.c)(l[0], l[1], u);
							c(
								w.a.updateCalibrationInfo({
									tempScale: ""
										.concat(s, " ")
										.concat(u, " = ")
										.concat(t, " ")
										.concat(e),
									isFractionalUnit: m,
								}),
							);
						},
						k = function (t) {
							var e;
							N(
								t,
								null === (e = new Y(f).worldScale) || void 0 === e
									? void 0
									: e.unit,
							);
						},
						L = function () {
							var t,
								e,
								n =
									null === (t = new Y(f).worldScale) || void 0 === t
										? void 0
										: t.value;
							(e =
								m || "ft-in" === O
									? Y.getFormattedValue(n, O, m ? 1 / 64 : 1e-4, !1, !0)
									: "".concat(n)),
								g(e || "");
						},
						j = Object(o.useRef)(f);
					return (
						Object(o.useEffect)(
							function () {
								j.current = f;
							},
							[f],
						),
						Object(o.useEffect)(
							function () {
								if (r) {
									var t = V(r),
										e = r.Scale[1][1];
									y ? N(Object(z.c)(t, e, y), y) : N(t, e);
								}
								var n = function (t, e) {
									if ("modify" === e && 1 === t.length && t[0] === r) {
										var n,
											o = V(r),
											i = r.Scale[1][1],
											a =
												null === (n = new Y(j.current).worldScale) ||
												void 0 === n
													? void 0
													: n.unit;
										a ? N(Object(z.c)(o, i, a), a) : N(o, i);
									}
								};
								return (
									p.a.addEventListener("annotationChanged", n),
									function () {
										p.a.removeEventListener("annotationChanged", n),
											p.a.deleteAnnotations([r]);
									}
								);
							},
							[r],
						),
						Object(o.useEffect)(
							function () {
								(null == E ? void 0 : E.current) !== document.activeElement &&
									L();
							},
							[f, m],
						),
						i.a.createElement(
							"div",
							{
								className: "CalibrationPopup",
								"data-element": "calibrationPopup",
							},
							i.a.createElement(
								"label",
								{
									className: "calibration-popup-label",
									id: "calibration-popup-label",
									htmlFor: "calibration-popup-value",
								},
								l("option.measurement.scaleModal.units"),
							),
							i.a.createElement(
								"div",
								{ className: "input-container" },
								i.a.createElement("input", {
									id: "calibration-popup-value",
									className: T,
									ref: E,
									type: S,
									value: v,
									min: "0",
									onChange: function (t) {
										g(t.target.value);
										var e = t.target.value.trim();
										if (m) {
											if ("in" === O) {
												if (z.k.test(e)) {
													var n = Object(z.p)(e);
													if (n > 0) return void k(n);
												}
											} else if ("ft-in" === O && z.g.test(e)) {
												var o = Object(z.o)(e);
												if (o > 0) return void k(o);
											}
										} else if ("ft-in" === O && z.f.test(e)) {
											var i = Object(z.n)(e);
											if (i > 0) return void k(i);
										} else if (z.d.test(e)) {
											var a = parseFloat(e) || 0;
											return void k(a);
										}
										k(0);
									},
									onBlur: function () {
										L();
									},
									placeholder: m
										? z.i[O]
										: "ft-in" === O
											? z.i["ft-in decimal"]
											: "",
								}),
								i.a.createElement(
									U.a,
									{ content: "option.measurement.scaleModal.displayUnits" },
									i.a.createElement(
										"div",
										{ className: "input-field" },
										i.a.createElement(B.a, {
											id: "calibration-popup-units",
											dataElement: "calibrationUnits",
											items: x,
											currentSelectionKey: O,
											onClickItem: function (t) {
												var e;
												N(
													null === (e = new Y(f).worldScale) || void 0 === e
														? void 0
														: e.value,
													t,
												);
											},
											labelledById: "calibration-popup-label",
										}),
									),
								),
							),
							i.a.createElement(
								U.a,
								{
									content: l(
										"option.measurement.scaleModal.fractionUnitsTooltip",
									),
								},
								i.a.createElement(
									"div",
									null,
									i.a.createElement(R.a, {
										isSwitch: !0,
										leftLabel: !0,
										label: l("option.measurement.scaleModal.fractionalUnits"),
										disabled: P,
										checked: m,
										id: "calibration-popup-fractional-units",
										className: "pop-switch",
										onChange: function () {
											c(
												w.a.updateCalibrationInfo({
													tempScale: f,
													isFractionalUnit: !m,
												}),
											);
										},
									}),
								),
							),
						)
					);
				};
			K.propTypes = H;
			var q = K,
				$ = n(50);
			n(1794);
			function J(t) {
				return (J =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function X(t, e) {
				var n = Object.keys(t);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(t);
					e &&
						(o = o.filter(function (e) {
							return Object.getOwnPropertyDescriptor(t, e).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function Q(t) {
				for (var e = 1; e < arguments.length; e++) {
					var n = null != arguments[e] ? arguments[e] : {};
					e % 2
						? X(Object(n), !0).forEach(function (e) {
								Z(t, e, n[e]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n))
							: X(Object(n)).forEach(function (e) {
									Object.defineProperty(
										t,
										e,
										Object.getOwnPropertyDescriptor(n, e),
									);
								});
				}
				return t;
			}
			function Z(t, e, n) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== J(t) || null === t) return t;
							var n = t[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(t, e || "default");
								if ("object" !== J(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === J(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = n),
					t
				);
			}
			function tt(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var o,
								i,
								a,
								r,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (i = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((r = n.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return et(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return et(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function et(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, o = new Array(e); n < e; n++) o[n] = t[n];
				return o;
			}
			var nt = {
					isMobile: l.a.bool,
					isIE: l.a.bool,
					isOpen: l.a.bool,
					isRightClickMenu: l.a.bool,
					isNotesPanelOpenOrActive: l.a.bool,
					isRichTextPopupOpen: l.a.bool,
					isLinkModalOpen: l.a.bool,
					isWarningModalOpen: l.a.bool,
					isContextMenuPopupOpen: l.a.bool,
					isVisible: l.a.bool,
					focusedAnnotation: l.a.object,
					multipleAnnotationsSelected: l.a.bool,
					popupRef: l.a.any,
					position: l.a.object,
					showViewFileButton: l.a.bool,
					onViewFile: l.a.func,
					showCommentButton: l.a.bool,
					onCommentAnnotation: l.a.func,
					isDateFreeTextCanEdit: l.a.bool,
					isDatePickerOpen: l.a.bool,
					handleDateChange: l.a.func,
					onDatePickerShow: l.a.func,
					isCalibrationPopupOpen: l.a.bool,
					showEditStyleButton: l.a.bool,
					isStylePopupOpen: l.a.bool,
					hideSnapModeCheckbox: l.a.bool,
					openEditStylePopup: l.a.func,
					closeEditStylePopup: l.a.func,
					annotationStyle: l.a.object,
					onResize: l.a.func,
					showContentEditButton: l.a.bool,
					onEditContent: l.a.func,
					openContentEditDeleteWarningModal: l.a.func,
					isAppearanceSignature: l.a.bool,
					onClearAppearanceSignature: l.a.func,
					showRedactionButton: l.a.bool,
					onApplyRedaction: l.a.func,
					showGroupButton: l.a.bool,
					onGroupAnnotations: l.a.func,
					showUngroupButton: l.a.bool,
					onUngroupAnnotations: l.a.func,
					showFormFieldButton: l.a.bool,
					onOpenFormField: l.a.func,
					showDeleteButton: l.a.bool,
					onDeleteAnnotation: l.a.func,
					showLinkButton: l.a.bool,
					hasAssociatedLink: l.a.bool,
					linkAnnotationToURL: l.a.func,
					showFileDownloadButton: l.a.bool,
					downloadFileAttachment: l.a.func,
					showAudioPlayButton: l.a.bool,
					handlePlaySound: l.a.func,
					showCalibrateButton: l.a.bool,
					onOpenCalibration: l.a.func,
					customizableUI: l.a.bool,
					openStylePanel: l.a.func,
					isInReadOnlyMode: l.a.bool,
					onOpenAlignmentModal: l.a.func,
				},
				ot = function (t) {
					var e,
						n,
						a = t.isMobile,
						r = t.isIE,
						l = t.isOpen,
						c = t.isRightClickMenu,
						u = t.isNotesPanelOpenOrActive,
						s = t.isRichTextPopupOpen,
						p = t.isLinkModalOpen,
						d = t.isWarningModalOpen,
						f = t.isContextMenuPopupOpen,
						m = t.isVisible,
						y = t.focusedAnnotation,
						b = t.popupRef,
						v = t.position,
						g = t.multipleAnnotationsSelected,
						w = t.showViewFileButton,
						A = t.onViewFile,
						O = t.showCommentButton,
						P = t.onCommentAnnotation,
						T = t.isDateFreeTextCanEdit,
						j = t.isDatePickerOpen,
						_ = t.handleDateChange,
						F = t.onDatePickerShow,
						D = t.isCalibrationPopupOpen,
						R = t.showEditStyleButton,
						B = t.isStylePopupOpen,
						U = t.hideSnapModeCheckbox,
						z = t.openEditStylePopup,
						W = t.closeEditStylePopup,
						G = t.annotationStyle,
						Y = t.onResize,
						V = t.showContentEditButton,
						H = t.onEditContent,
						K = t.openContentEditDeleteWarningModal,
						J = t.isAppearanceSignature,
						X = t.onClearAppearanceSignature,
						Z = t.showRedactionButton,
						et = t.onApplyRedaction,
						nt = t.showGroupButton,
						ot = t.onGroupAnnotations,
						it = t.showUngroupButton,
						at = t.onUngroupAnnotations,
						rt = t.showFormFieldButton,
						lt = t.onOpenFormField,
						ct = t.showDeleteButton,
						ut = t.onDeleteAnnotation,
						st = t.showLinkButton,
						pt = t.hasAssociatedLink,
						dt = t.linkAnnotationToURL,
						ft = t.showFileDownloadButton,
						mt = t.downloadFileAttachment,
						ht = t.showAudioPlayButton,
						yt = t.handlePlaySound,
						bt = t.showCalibrateButton,
						vt = t.onOpenCalibration,
						gt = t.customizableUI,
						wt = t.openStylePanel,
						At = t.isInReadOnlyMode,
						Et = t.onOpenAlignmentModal,
						Ot = tt(Object(h.a)(), 1)[0],
						xt = tt(Object(o.useState)(!1), 2),
						Pt = xt[0],
						St = xt[1],
						Tt = T ? "action.changeDate" : "action.comment",
						Ct = T
							? "icon-tool-fill-and-sign-calendar"
							: "icon-header-chat-line",
						Nt =
							!At &&
							y instanceof window.Core.Annotations.Model3DAnnotation &&
							!a,
						kt = y instanceof window.Core.Annotations.RectangleAnnotation,
						Lt = y instanceof window.Core.Annotations.EllipseAnnotation,
						jt = y instanceof window.Core.Annotations.PolygonAnnotation,
						_t =
							y instanceof window.Core.Annotations.FreeTextAnnotation &&
							(y.getIntent() ===
								window.Core.Annotations.FreeTextAnnotation.Intent.FreeText ||
								y.getIntent() ===
									window.Core.Annotations.FreeTextAnnotation.Intent
										.FreeTextCallout),
						Ft = y instanceof window.Core.Annotations.RedactionAnnotation,
						Dt = Object($.g)(y),
						It = !!y.Measure,
						Mt = Object($.e)(Object($.j)(y.ToolName)).hasLineEndings,
						Rt =
							!window.isApryseWebViewerWebComponent ||
							(null === (e = document.activeElement) || void 0 === e
								? void 0
								: e.shadowRoot) === Object(x.a)(),
						Bt = "solid",
						Ut =
							null === (n = y.isContentEditPlaceholder) || void 0 === n
								? void 0
								: n.call(y),
						zt =
							y instanceof window.Core.Annotations.SignatureWidgetAnnotation &&
							y.fieldFlags.get(window.Core.Annotations.WidgetFlags.READ_ONLY),
						Wt = J && !rt;
					try {
						Bt =
							"dash" === y.Style
								? "".concat(y.Style, ",").concat(y.Dashes)
								: y.Style;
					} catch (t) {
						console.error(t);
					}
					var Gt = {};
					if (
						(Mt &&
							(Gt = {
								StartLineStyle: y.getStartStyle(),
								EndLineStyle: y.getEndStyle(),
								StrokeStyle: Bt,
							}),
						(kt || Lt || jt) && (Gt = { StrokeStyle: Bt }),
						_t)
					) {
						var Yt,
							Vt,
							Ht,
							Kt,
							qt,
							$t,
							Jt,
							Xt,
							Qt,
							Zt,
							te,
							ee = y.getRichTextStyle(),
							ne = y.isAutoSizeFont(),
							oe = y.getCalculatedFontSize();
						Gt = {
							Font: y.Font,
							FontSize: y.FontSize,
							TextAlign: y.TextAlign,
							TextVerticalAlign: y.TextVerticalAlign,
							bold:
								null !==
									(Yt =
										"bold" ===
										(null == ee || null === (Vt = ee[0]) || void 0 === Vt
											? void 0
											: Vt["font-weight"])) &&
								void 0 !== Yt &&
								Yt,
							italic:
								null !==
									(Ht =
										"italic" ===
										(null == ee || null === (Kt = ee[0]) || void 0 === Kt
											? void 0
											: Kt["font-style"])) &&
								void 0 !== Ht &&
								Ht,
							underline:
								(null == ee ||
								null === (qt = ee[0]) ||
								void 0 === qt ||
								null === ($t = qt["text-decoration"]) ||
								void 0 === $t
									? void 0
									: $t.includes("underline")) ||
								(null == ee ||
								null === (Jt = ee[0]) ||
								void 0 === Jt ||
								null === (Xt = Jt["text-decoration"]) ||
								void 0 === Xt
									? void 0
									: Xt.includes("word")),
							strikeout:
								null !==
									(Qt =
										null == ee ||
										null === (Zt = ee[0]) ||
										void 0 === Zt ||
										null === (te = Zt["text-decoration"]) ||
										void 0 === te
											? void 0
											: te.includes("line-through")) &&
								void 0 !== Qt &&
								Qt,
							StrokeStyle: Bt,
							isAutoSizeFont: ne,
							calculatedFontSize: oe,
						};
					}
					Ft &&
						(Gt = {
							OverlayText: y.OverlayText,
							Font: y.Font,
							FontSize: y.FontSize,
							TextAlign: y.TextAlign,
						});
					var ie = i.a.createElement(
						"div",
						{
							className: C()({
								Popup: !0,
								AnnotationPopup: !0,
								open: l,
								closed: !l,
								stylePopupOpen: B,
								"is-vertical": c,
								"is-horizontal": !c,
							}),
							ref: b,
							"data-element": E.a.ANNOTATION_POPUP,
							style: Q(
								Q({}, v),
								{},
								{ visibility: m || void 0 === m ? "visible" : "hidden" },
							),
						},
						(function () {
							switch (!0) {
								case B:
									return i.a.createElement(L.a, {
										annotations: [y],
										style: G,
										isOpen: l,
										onResize: Y,
										isFreeText: _t,
										isEllipse: Lt,
										isRedaction: Ft,
										isMeasure: It,
										colorMapKey: Dt,
										showLineStyleOptions: Mt,
										properties: Gt,
										hideSnapModeCheckbox: U,
										hasBackToMenu: c,
										onBackToMenu: W,
									});
								case j:
									return i.a.createElement(I, {
										onClick: _,
										annotation: y,
										onDatePickerShow: F,
									});
								case D:
									return i.a.createElement(q, { annotation: y });
								case Pt &&
									y instanceof window.Core.Annotations.Model3DAnnotation:
									return i.a.createElement(
										"div",
										{ className: "shortCuts3D" },
										i.a.createElement(
											"div",
											{
												className: "closeButton",
												onClick: function () {
													return St(!1);
												},
											},
											"x",
										),
										i.a.createElement(
											"div",
											{ className: "row" },
											Ot("action.rotate3D"),
											" ",
											i.a.createElement("span", null, Ot("shortcut.rotate3D")),
										),
										i.a.createElement(
											"div",
											{ className: "row" },
											Ot("action.zoom"),
											" ",
											i.a.createElement("span", null, Ot("shortcut.zoom3D")),
										),
									);
								default:
									return i.a.createElement(
										N.a,
										{ locked: l && Rt && !s && !u && !p && !d && !_t && !f },
										i.a.createElement(
											"div",
											{ className: "container" },
											i.a.createElement(
												M.a,
												{
													dataElement: E.a.ANNOTATION_POPUP,
													childrenClassName: "main-menu-button",
												},
												w &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "viewFileButton",
														label: c ? "action.viewFile" : "",
														title: c ? "" : "action.viewFile",
														img: "icon-view",
														onClick: A,
													}),
												O &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationCommentButton",
														label: c ? Tt : "",
														title: c ? "" : Tt,
														img: Ct,
														onClick: P,
													}),
												R &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationStyleEditButton",
														label: c ? "action.style" : "",
														title: c ? "" : "action.style",
														img: "icon-menu-style-line",
														onClick: gt ? wt : z,
													}),
												V &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationContentEditButton",
														label: c ? "action.edit" : "",
														title: c ? "" : "action.edit",
														img: "ic_edit_page_24px",
														onClick: H,
													}),
												Wt &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationClearSignatureButton",
														label: !zt && c ? "action.clearSignature" : "",
														title: zt
															? "action.readOnlySignature"
															: c
																? ""
																: "action.clearSignature",
														img: "icon-delete-line",
														onClick: X,
														isNotClickableSelector: function () {
															return zt;
														},
													}),
												Z &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationRedactButton",
														label: c ? "action.apply" : "",
														title: c ? "" : "action.apply",
														img: "ic_check_black_24px",
														onClick: et,
													}),
												nt &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationGroupButton",
														label: c ? "action.group" : "",
														title: c ? "" : "action.group",
														img: "group-annotations-icon",
														onClick: ot,
													}),
												it &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationUngroupButton",
														label: c ? "action.ungroup" : "",
														title: c ? "" : "action.ungroup",
														img: "ungroup-annotations-icon",
														onClick: at,
													}),
												g &&
													!a &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "openAlignmentButton",
														label: c ? "alignment" : "",
														title: c ? "" : "Align",
														img: "ic-alignment-main",
														onClick: Et,
													}),
												rt &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "formFieldEditButton",
														label: c ? "action.formFieldEdit" : "",
														title: c ? "" : "action.formFieldEdit",
														img: "icon-edit-form-field",
														onClick: lt,
													}),
												ct &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "annotationDeleteButton",
														label: c ? "action.delete" : "",
														title: c ? "" : "action.delete",
														img: "icon-delete-line",
														onClick: Ut ? K : ut,
													}),
												bt &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: E.a.CALIBRATION_POPUP_BUTTON,
														label: c ? "action.calibrate" : "",
														title: c ? "" : "action.calibrate",
														img: "calibrate",
														onClick: vt,
													}),
												st &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "linkButton",
														label: c ? "tool.Link" : "",
														title: c ? "" : "tool.Link",
														img: pt ? "icon-tool-unlink" : "icon-tool-link",
														onClick: dt,
													}),
												ft &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "fileAttachmentDownload",
														label: c ? "action.fileAttachmentDownload" : "",
														title: c ? "" : "action.fileAttachmentDownload",
														img: "icon-download",
														onClick: function () {
															return mt(y);
														},
													}),
												Nt &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "shortCutKeysFor3D",
														label: c ? "action.viewShortCutKeysFor3D" : "",
														title: c ? "" : "action.viewShortCutKeysFor3D",
														img: "icon-keyboard",
														onClick: function () {
															return St(!0);
														},
													}),
												ht &&
													i.a.createElement(k.a, {
														className: "main-menu-button",
														dataElement: "playSoundButton",
														label: c ? "action.playAudio" : "",
														title: c ? "" : "action.playAudio",
														img: "ic_play_24px",
														onClick: function () {
															return yt(y);
														},
													}),
											),
										),
									);
							}
						})(),
					);
					return r || a
						? ie
						: i.a.createElement(
								S.a,
								{
									cancel:
										".Button, .cell, .sliders-container svg, select, button, input",
								},
								ie,
							);
				};
			ot.propTypes = nt;
			var it = ot;
			function at(t) {
				return (at =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function rt() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ rt =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					n = e.hasOwnProperty,
					o =
						Object.defineProperty ||
						function (t, e, n) {
							t[e] = n.value;
						},
					i = "function" == typeof Symbol ? Symbol : {},
					a = i.iterator || "@@iterator",
					r = i.asyncIterator || "@@asyncIterator",
					l = i.toStringTag || "@@toStringTag";
				function c(t, e, n) {
					return (
						Object.defineProperty(t, e, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					c({}, "");
				} catch (t) {
					c = function (t, e, n) {
						return (t[e] = n);
					};
				}
				function u(t, e, n, i) {
					var a = e && e.prototype instanceof d ? e : d,
						r = Object.create(a.prototype),
						l = new P(i || []);
					return o(r, "_invoke", { value: A(t, n, l) }), r;
				}
				function s(t, e, n) {
					try {
						return { type: "normal", arg: t.call(e, n) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = u;
				var p = {};
				function d() {}
				function f() {}
				function m() {}
				var h = {};
				c(h, a, function () {
					return this;
				});
				var y = Object.getPrototypeOf,
					b = y && y(y(S([])));
				b && b !== e && n.call(b, a) && (h = b);
				var v = (m.prototype = d.prototype = Object.create(h));
				function g(t) {
					["next", "throw", "return"].forEach(function (e) {
						c(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function w(t, e) {
					var i;
					o(this, "_invoke", {
						value: function (o, a) {
							function r() {
								return new e(function (i, r) {
									!(function o(i, a, r, l) {
										var c = s(t[i], t, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												p = u.value;
											return p && "object" == at(p) && n.call(p, "__await")
												? e.resolve(p.__await).then(
														function (t) {
															o("next", t, r, l);
														},
														function (t) {
															o("throw", t, r, l);
														},
													)
												: e.resolve(p).then(
														function (t) {
															(u.value = t), r(u);
														},
														function (t) {
															return o("throw", t, r, l);
														},
													);
										}
										l(c.arg);
									})(o, a, i, r);
								});
							}
							return (i = i ? i.then(r, r) : r());
						},
					});
				}
				function A(t, e, n) {
					var o = "suspendedStart";
					return function (i, a) {
						if ("executing" === o)
							throw new Error("Generator is already running");
						if ("completed" === o) {
							if ("throw" === i) throw a;
							return T();
						}
						for (n.method = i, n.arg = a; ; ) {
							var r = n.delegate;
							if (r) {
								var l = E(r, n);
								if (l) {
									if (l === p) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === o) throw ((o = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							o = "executing";
							var c = s(t, e, n);
							if ("normal" === c.type) {
								if (
									((o = n.done ? "completed" : "suspendedYield"), c.arg === p)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((o = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function E(t, e) {
					var n = e.method,
						o = t.iterator[n];
					if (void 0 === o)
						return (
							(e.delegate = null),
							("throw" === n &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								E(t, e),
								"throw" === e.method)) ||
								("return" !== n &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							p
						);
					var i = s(o, t.iterator, e.arg);
					if ("throw" === i.type)
						return (
							(e.method = "throw"), (e.arg = i.arg), (e.delegate = null), p
						);
					var a = i.arg;
					return a
						? a.done
							? ((e[t.resultName] = a.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								p)
							: a
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							p);
				}
				function O(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function x(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function P(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(O, this),
						this.reset(!0);
				}
				function S(t) {
					if (t) {
						var e = t[a];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var o = -1,
								i = function e() {
									for (; ++o < t.length; )
										if (n.call(t, o)) return (e.value = t[o]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (i.next = i);
						}
					}
					return { next: T };
				}
				function T() {
					return { value: void 0, done: !0 };
				}
				return (
					(f.prototype = m),
					o(v, "constructor", { value: m, configurable: !0 }),
					o(m, "constructor", { value: f, configurable: !0 }),
					(f.displayName = c(m, l, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === f || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, m)
								: ((t.__proto__ = m), c(t, l, "GeneratorFunction")),
							(t.prototype = Object.create(v)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					g(w.prototype),
					c(w.prototype, r, function () {
						return this;
					}),
					(t.AsyncIterator = w),
					(t.async = function (e, n, o, i, a) {
						void 0 === a && (a = Promise);
						var r = new w(u(e, n, o, i), a);
						return t.isGeneratorFunction(n)
							? r
							: r.next().then(function (t) {
									return t.done ? t.value : r.next();
								});
					}),
					g(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							n = [];
						for (var o in e) n.push(o);
						return (
							n.reverse(),
							function t() {
								for (; n.length; ) {
									var o = n.pop();
									if (o in e) return (t.value = o), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = S),
					(P.prototype = {
						constructor: P,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(x),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										n.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function o(n, o) {
								return (
									(r.type = "throw"),
									(r.arg = t),
									(e.next = n),
									o && ((e.method = "next"), (e.arg = void 0)),
									!!o
								);
							}
							for (var i = this.tryEntries.length - 1; i >= 0; --i) {
								var a = this.tryEntries[i],
									r = a.completion;
								if ("root" === a.tryLoc) return o("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var i = this.tryEntries[o];
								if (
									i.tryLoc <= this.prev &&
									n.call(i, "finallyLoc") &&
									this.prev < i.finallyLoc
								) {
									var a = i;
									break;
								}
							}
							a &&
								("break" === t || "continue" === t) &&
								a.tryLoc <= e &&
								e <= a.finallyLoc &&
								(a = null);
							var r = a ? a.completion : {};
							return (
								(r.type = t),
								(r.arg = e),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), p)
									: this.complete(r)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								p
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.finallyLoc === t)
									return this.complete(n.completion, n.afterLoc), x(n), p;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var n = this.tryEntries[e];
								if (n.tryLoc === t) {
									var o = n.completion;
									if ("throw" === o.type) {
										var i = o.arg;
										x(n);
									}
									return i;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, n) {
							return (
								(this.delegate = { iterator: S(t), resultName: e, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								p
							);
						},
					}),
					t
				);
			}
			function lt(t, e, n, o, i, a, r) {
				try {
					var l = t[a](r),
						c = l.value;
				} catch (t) {
					return void n(t);
				}
				l.done ? e(c) : Promise.resolve(c).then(o, i);
			}
			function ct(t) {
				return function () {
					var e = this,
						n = arguments;
					return new Promise(function (o, i) {
						var a = t.apply(e, n);
						function r(t) {
							lt(a, o, i, r, l, "next", t);
						}
						function l(t) {
							lt(a, o, i, r, l, "throw", t);
						}
						r(void 0);
					});
				};
			}
			function ut(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var o,
								i,
								a,
								r,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== e);
										c = !0
									);
							} catch (t) {
								(u = !0), (i = t);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((r = n.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return st(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return st(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function st(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, o = new Array(e); n < e; n++) o[n] = t[n];
				return o;
			}
			var pt = window.Core.Tools.ToolNames,
				dt = window.Core.Annotations,
				ft = {
					focusedAnnotation: l.a.object,
					selectedMultipleAnnotations: l.a.bool,
					canModify: l.a.bool,
					focusedAnnotationStyle: l.a.object,
					isStylePopupOpen: l.a.bool,
					setIsStylePopupOpen: l.a.func,
					isDatePickerOpen: l.a.bool,
					setDatePickerOpen: l.a.func,
					isDatePickerMount: l.a.bool,
					setDatePickerMount: l.a.func,
					hasAssociatedLink: l.a.bool,
					includesFormFieldAnnotation: l.a.bool,
					stylePopupRepositionFlag: l.a.bool,
					setStylePopupRepositionFlag: l.a.func,
					closePopup: l.a.func,
				},
				mt = function t(e) {
					var n = e.focusedAnnotation,
						r = e.selectedMultipleAnnotations,
						l = e.canModify,
						c = e.focusedAnnotationStyle,
						P = e.isStylePopupOpen,
						S = e.setIsStylePopupOpen,
						T = e.isDatePickerOpen,
						C = e.setDatePickerOpen,
						N = e.isDatePickerMount,
						k = e.setDatePickerMount,
						L = e.hasAssociatedLink,
						j = e.includesFormFieldAnnotation,
						_ = e.stylePopupRepositionFlag,
						F = e.setStylePopupRepositionFlag,
						D = e.closePopup,
						I = ut(
							Object(a.e)(function (t) {
								return [
									A.a.isElementDisabled(t, E.a.ANNOTATION_POPUP),
									A.a.isElementOpen(t, E.a.ANNOTATION_POPUP),
									A.a.isElementOpen(t, E.a.CONTEXT_MENU_POPUP),
									A.a.isRightClickAnnotationPopupEnabled(t),
									A.a.isElementDisabled(t, E.a.NOTES_PANEL),
									A.a.isElementDisabled(t, E.a.ANNOTATION_STYLE_POPUP),
									A.a.isElementDisabled(t, E.a.INLINE_COMMENT_POPUP),
									A.a.isElementOpen(t, E.a.NOTES_PANEL),
									A.a.isElementOpen(t, E.a.LINK_MODAL),
									A.a.isElementOpen(t, E.a.WARNING_MODAL),
									A.a.isElementOpen(t, "richTextPopup"),
									A.a.getIsMultiTab(t),
									A.a.getTabManager(t),
									A.a.getTabs(t),
									A.a.getNotesInLeftPanel(t),
									A.a.isElementOpen(t, E.a.LEFT_PANEL),
									A.a.getActiveLeftPanel(t),
									A.a.getActiveDocumentViewerKey(t),
									A.a.isAnyCustomPanelOpen(t),
									A.a.getFeatureFlags(t),
									A.a.isElementOpen(t, E.a.STYLE_PANEL),
									A.a.isElementDisabled(t, E.a.STYLE_PANEL),
								];
							}, a.c),
							22,
						),
						M = I[0],
						R = I[1],
						B = I[2],
						U = I[3],
						z = I[4],
						W = I[5],
						G = I[6],
						Y = I[7],
						V = I[8],
						H = I[9],
						K = I[10],
						q = I[11],
						$ = I[12],
						J = I[13],
						X = I[14],
						Q = I[15],
						Z = I[16],
						tt = I[17],
						et = I[18],
						nt = I[19],
						ot = I[20],
						at = I[21],
						lt = ut(Object(h.a)(), 1)[0],
						st = Object(a.d)(),
						ft = ut(Object(o.useState)({ left: 0, top: 0 }), 2),
						mt = ft[0],
						ht = ft[1],
						yt = ut(Object(o.useState)(!1), 2),
						bt = yt[0],
						vt = yt[1],
						gt = ut(Object(o.useState)(!1), 2),
						wt = gt[0],
						At = gt[1],
						Et = Object(o.useRef)(),
						Ot = !U || p.a.isAnnotationSelected(n, tt),
						xt = p.a.getAnnotationManager(tt),
						Pt = nt.customizableUI;
					Object(g.a)(
						Et,
						Object(o.useCallback)(
							function (t) {
								var e,
									n = Object(x.a)().querySelector(
										'[data-element="'.concat(E.a.NOTES_PANEL, '"]'),
									),
									o = null == n ? void 0 : n.contains(t.target),
									i =
										null ===
											(e = Object(x.a)().querySelector(".LinkModal.open")) ||
										void 0 === e
											? void 0
											: e.contains(t.target),
									a = Object(y.b)(),
									r = Object(y.d)(),
									l = Object(y.c)();
								o ||
									i ||
									r ||
									l ||
									a ||
									(U ? D() : st(w.a.closeElement(E.a.ANNOTATION_POPUP)));
							},
							[U],
						),
					);
					var St = u()(
							function () {
								t && Et.current && ht(Object(d.c)(n, Et, tt));
							},
							16,
							{ trailing: !0, leading: !1 },
						),
						Tt = u()(
							function () {
								t && vt(Ot);
							},
							32,
							{ trailing: !0, leading: !1 },
						);
					Object(o.useEffect)(function () {
						return (
							window.addEventListener("resize", St),
							function () {
								window.removeEventListener("resize", St);
							}
						);
					}, []),
						Object(o.useLayoutEffect)(
							function () {
								(n || P || N) && (St(), vt(!1), Tt());
							},
							[n, P, N, l, tt],
						),
						Object(o.useEffect)(
							function () {
								n && n.ToolName === pt.CALIBRATION_MEASUREMENT && At(!0);
							},
							[n],
						),
						Object(o.useEffect)(
							function () {
								(R || K) && st(w.a.closeElement(E.a.INLINE_COMMENT_POPUP));
							},
							[R, K],
						);
					var Ct = !!Object(v.a)("webviewerServerURL", null)
							? window.Core.SupportedFileFormats.SERVER
							: window.Core.SupportedFileFormats.CLIENT,
						Nt =
							n instanceof dt.FileAttachmentAnnotation &&
							q &&
							Ct.includes(
								window.Core.mimeTypeToExtension[n.getFileMetadata().mimeType],
							),
						kt = Object(o.useCallback)(
							ct(
								rt().mark(function t() {
									var e, o;
									return rt().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													if ($ && q) {
														t.next = 2;
														break;
													}
													return t.abrupt(
														"return",
														console.warn(
															"Can't open file in non-multi-tab mode",
														),
													);
												case 2:
													if (
														((e = n.getFileMetadata()),
														!(o = J.find(function (t) {
															return t.options.filename === e.filename;
														})))
													) {
														t.next = 8;
														break;
													}
													return (t.next = 7), $.setActiveTab(o.id, !0);
												case 7:
													return t.abrupt("return");
												case 8:
													return (t.t0 = $), (t.next = 11), n.getFileData();
												case 11:
													return (
														(t.t1 = t.sent),
														(t.t2 = {
															extension:
																window.Core.mimeTypeToExtension[e.mimeType],
															filename: e.filename,
															saveCurrentActiveTabState: !0,
															setActive: !0,
														}),
														(t.next = 15),
														t.t0.addTab.call(t.t0, t.t1, t.t2)
													);
												case 15:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							),
							[$, n, J, q],
						);
					if (M || !n) return null;
					var Lt = p.a.getSelectedAnnotations(tt),
						jt = Lt.length,
						_t = jt > 1,
						Ft =
							n instanceof dt.FreeTextAnnotation &&
							!!n.getDateFormat() &&
							p.a.canModifyContents(n, tt),
						Dt = p.a.getNumberOfGroups(Lt, tt),
						It = Dt > 1,
						Mt = 1 === Dt && jt > 1 && Ot,
						Rt =
							n instanceof dt.SignatureWidgetAnnotation &&
							n.isSignedByAppearance(),
						Bt = p.a.getIsReadOnly(),
						Ut =
							(!z || !G) &&
							(!_t || (_t && !Ot)) &&
							n.ToolName !== pt.CROP &&
							!j &&
							!n.isContentEditPlaceholder() &&
							!Rt,
						zt = Object.keys(c).length > 0,
						Wt = [
							pt.CROP,
							pt.VIDEO_REDACTION,
							pt.VIDEO_AND_AUDIO_REDACTION,
							pt.AUDIO_REDACTION,
						],
						Gt =
							l &&
							zt &&
							(!W || Pt) &&
							(!_t || Mt || (_t && !Ot)) &&
							!Wt.includes(n.ToolName) &&
							!(n instanceof dt.Model3DAnnotation) &&
							!n.isContentEditPlaceholder() &&
							!Rt &&
							!(n instanceof dt.PushButtonWidgetAnnotation),
						Yt = n instanceof dt.EllipseAnnotation || !p.a.isFullPDFEnabled(),
						Vt =
							n.isContentEditPlaceholder() &&
							n.getContentEditType() === window.Core.ContentEdit.Types.TEXT,
						Ht = (function () {
							var t = ct(
								rt().mark(function t() {
									return rt().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													xt.trigger("annotationDoubleClicked", n),
														st(w.a.closeElement(E.a.ANNOTATION_POPUP));
												case 2:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							);
							return function () {
								return t.apply(this, arguments);
							};
						})(),
						Kt = p.a.isAnnotationRedactable(n, tt) && !_t && !j,
						qt = Lt.find(function (t) {
							return !t.InReplyTo;
						}),
						$t = l && Ot && It && !j,
						Jt = Mt,
						Xt = p.a
							.getFormFieldCreationManager(tt)
							.isInFormFieldCreationMode(),
						Qt = j && Xt && !(n instanceof dt.PushButtonWidgetAnnotation),
						Zt = l,
						te = function () {
							Ot
								? p.a.deleteAnnotations(
										p.a.getSelectedAnnotations(tt),
										void 0,
										tt,
									)
								: p.a.deleteAnnotations([n], void 0, tt),
								D();
						},
						ee = !(
							[
								pt.CROP,
								pt.SIGNATURE,
								pt.REDACTION,
								pt.REDACTION2,
								pt.REDACTION3,
								pt.REDACTION4,
								pt.STICKY,
								pt.STICKY2,
								pt.STICKY3,
								pt.STICKY4,
							].includes(n.ToolName) ||
							j ||
							n.isContentEditPlaceholder() ||
							n instanceof dt.SoundAnnotation ||
							Rt
						),
						ne = l && n.Measure && n instanceof dt.LineAnnotation,
						oe = n instanceof dt.FileAttachmentAnnotation,
						ie = (function () {
							var t = ct(
								rt().mark(function t(e) {
									var n, o, i;
									return rt().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													return (t.next = 2), e.getFullFileMetadata();
												case 2:
													(n = t.sent),
														(o = n.fileData),
														(i = n.fileName),
														Object(s.saveAs)(o, i);
												case 6:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							);
							return function (e) {
								return t.apply(this, arguments);
							};
						})(),
						ae =
							!m.e && !r && n instanceof dt.SoundAnnotation && n.hasAudioData(),
						re = Y || (X && Q && "notesPanel" === Z) || et || Xt;
					return i.a.createElement(it, {
						isMobile: Object(m.k)(),
						isIE: m.e,
						isOpen: R,
						isRightClickMenu: U,
						isNotesPanelOpenOrActive: re,
						isRichTextPopupOpen: K,
						isLinkModalOpen: V,
						isWarningModalOpen: H,
						isContextMenuPopupOpen: B,
						isVisible: bt,
						popupRef: Et,
						position: mt,
						focusedAnnotation: n,
						multipleAnnotationsSelected: _t,
						showViewFileButton: Nt,
						onViewFile: kt,
						showCommentButton: Ut,
						onCommentAnnotation: function () {
							if (Ft) C(!0);
							else {
								st(w.a.closeElement("searchPanel")),
									st(w.a.closeElement("redactionPanel"));
								var t = p.a.getContentEditManager(tt);
								t.isInContentEditMode() &&
									(st(w.a.closeElement("textEditingPanel")),
									t.endContentEditMode()),
									st(w.a.triggerNoteEditing()),
									G
										? (st(w.a.openElement(E.a.NOTES_PANEL)),
											st(w.a.closeElement(E.a.ANNOTATION_POPUP)))
										: (Y || st(w.a.openElement(E.a.INLINE_COMMENT_POPUP)), D());
							}
						},
						isDateFreeTextCanEdit: Ft,
						isDatePickerOpen: T,
						handleDateChange: function (t) {
							xt.setNoteContents(n, t), xt.updateAnnotation(n);
						},
						onDatePickerShow: function (t) {
							k(t);
						},
						isCalibrationPopupOpen: wt,
						showEditStyleButton: Gt,
						isStylePopupOpen: P,
						hideSnapModeCheckbox: Yt,
						openEditStylePopup: function () {
							return S(!0);
						},
						closeEditStylePopup: function () {
							return S(!1);
						},
						annotationStyle: c,
						onResize: function () {
							F(!_);
						},
						showContentEditButton: Vt,
						onEditContent: Ht,
						openContentEditDeleteWarningModal: function () {
							var t = {
								message: lt("option.contentEdit.deletionModal.message"),
								title: lt("option.contentEdit.deletionModal.title"),
								confirmBtnText: lt("action.ok"),
								onConfirm: function () {
									return te();
								},
							};
							st(w.a.showWarningMessage(t));
						},
						isAppearanceSignature: Rt,
						onClearAppearanceSignature: function () {
							n.clearSignature(xt), st(w.a.closeElement(E.a.ANNOTATION_POPUP));
						},
						showRedactionButton: Kt,
						onApplyRedaction: function () {
							st(Object(f.a)(n, void 0, tt)),
								st(w.a.closeElement(E.a.ANNOTATION_POPUP));
						},
						showGroupButton: $t,
						onGroupAnnotations: function () {
							p.a.groupAnnotations(qt, Lt, tt);
						},
						showUngroupButton: Jt,
						onUngroupAnnotations: function () {
							p.a.ungroupAnnotations(Lt, tt);
						},
						showFormFieldButton: Qt,
						onOpenFormField: function () {
							D(),
								Pt
									? (st(w.a.disableElement(O.b)),
										st(w.a.closeElement(E.a.FORM_FIELD_EDIT_POPUP)))
									: st(w.a.disableElement(E.a.ANNOTATION_POPUP, O.b)),
								st(w.a.openElement(E.a.FORM_FIELD_EDIT_POPUP)),
								st(w.a.openElement(E.a.FORM_FIELD_PANEL));
						},
						showDeleteButton: Zt,
						onDeleteAnnotation: te,
						showLinkButton: ee,
						hasAssociatedLink: L,
						linkAnnotationToURL: function () {
							L
								? ((Ot ? Lt : [n]).forEach(function (t) {
										Object(b.a)(t).forEach(function (e, n) {
											xt.ungroupAnnotations([e]),
												t instanceof dt.TextHighlightAnnotation &&
												0 === t.Opacity &&
												0 === n
													? xt.deleteAnnotations([t, e], null, !0)
													: xt.deleteAnnotation(e, null, !0);
										});
									}),
									D())
								: st(w.a.openElement(E.a.LINK_MODAL));
						},
						showFileDownloadButton: oe,
						downloadFileAttachment: ie,
						showAudioPlayButton: ae,
						handlePlaySound: function (t) {
							st(w.a.setActiveSoundAnnotation(t)),
								st(w.a.triggerResetAudioPlaybackPosition(!0)),
								st(w.a.openElement("audioPlaybackPopup"));
						},
						showCalibrateButton: ne,
						onOpenCalibration: function () {
							st(w.a.closeElement(E.a.ANNOTATION_POPUP)),
								st(w.a.openElement(E.a.CALIBRATION_MODAL));
						},
						customizableUI: Pt,
						openStylePanel: function () {
							ot || at || st(w.a.openElement(E.a.STYLE_PANEL)), D();
						},
						isStylePanelOpen: ot,
						isInReadOnlyMode: Bt,
						onOpenAlignmentModal: function () {
							st(w.a.openElement(E.a.ANNOTATION_ALIGNMENT_POPUP)), D();
						},
					});
				};
			mt.propTypes = ft;
			var ht = mt;
			e.default = ht;
		},
	},
]);
//# sourceMappingURL=chunk.41.js.map
