(window.webpackJsonp = window.webpackJsonp || []).push([
	[42],
	{
		1928: function (e, t, n) {
			var o = n(30),
				a = n(1929);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[e.i, a, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const a = t[o];
						if (0 === o)
							a.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							a.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(a, r);
			e.exports = a.locals || {};
		},
		1929: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".dimension-input-container{display:flex;align-items:center;border:1px solid var(--border);background:var(--component-background);color:var(--text-color);border-radius:4px;width:100%;max-width:80px;min-width:64px;height:28px;padding:1px 2px}.dimension-input-container input::-webkit-inner-spin-button,.dimension-input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.dimension-input-container input[type=number]{-moz-appearance:textfield}.dimension-input-container input,.dimension-input-container input:focus{border:0}.dimension-input{border:0;border-radius:0;padding:0;margin-right:0;margin-left:4px;min-width:1ch}.dimension-unit{color:var(--text-color);font-size:13px}",
				"",
			]);
		},
		1930: function (e, t, n) {
			var o = n(30),
				a = n(1931);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[e.i, a, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const a = t[o];
						if (0 === o)
							a.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							a.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(a, r);
			e.exports = a.locals || {};
		},
		1931: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.incrementNumberInput .increment-buttons .increment-arrow-button:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.insert-blank-page-panel{width:100%}.insert-blank-page-panel .dimension-input-container{min-width:100%;margin:0;height:32px}.insert-blank-page-panel .dimension-input-container[focus-within]{border:1px solid var(--focus-border)}.insert-blank-page-panel .dimension-input-container:focus-within{border:1px solid var(--focus-border)}.insert-blank-page-panel .subheader{font-size:13px;font-weight:700;margin-bottom:8px}.insert-blank-page-panel .panel-container .section{display:flex;padding-bottom:16px;grid-gap:16px;gap:16px}.insert-blank-page-panel .panel-container .section.extra-space-section{height:98px}.insert-blank-page-panel .panel-container .section.page-dimensions-section,.insert-blank-page-panel .panel-container .section:last-of-type{padding-bottom:0}.insert-blank-page-panel .panel-container .section .input-container{display:flex;flex-direction:column}.insert-blank-page-panel .panel-container .section .input-container label{margin:0;padding-bottom:8px;font-size:13px}.insert-blank-page-panel .panel-container .section .input-container .page-number-input{width:100%;height:32px;margin:0}.insert-blank-page-panel .panel-container .section .input-container .customSelector{margin-left:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .input-container .customSelector ul{top:auto;bottom:calc(100% + 4px)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .input-container .customSelector ul{top:auto;bottom:calc(100% + 4px)}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .input-container .customSelector li:first-child{display:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .input-container .customSelector li:first-child{display:none}}.insert-blank-page-panel .panel-container .section .input-container select{height:28px;width:100%}.insert-blank-page-panel .panel-container .section .input-container .Dropdown{height:32px;min-width:150px;width:100%!important}.insert-blank-page-panel .panel-container .section .input-container .Dropdown .arrow{flex:0 1 auto}.insert-blank-page-panel .panel-container .section .input-container .Dropdown .picked-option .picked-option-text{text-align:left}.insert-blank-page-panel .panel-container .section .input-container .Dropdown__items{top:-52px;z-index:80;width:100%}.insert-blank-page-panel .panel-container .section .input-container .page-number-error{margin-top:8px;font-size:13px;color:var(--color-message-error)}.insert-blank-page-panel .panel-container .section .input-container .specify-pages-wrapper{display:flex;justify-content:space-between}.insert-blank-page-panel .panel-container .section .input-container .specify-pages-wrapper .input-sub-text{color:var(--faded-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section .ui__choice__label,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section button,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-blank-page-panel .panel-container .section input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section .ui__choice__label,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section button,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-blank-page-panel .panel-container .section input{font-size:13px}}.insert-blank-page-panel .panel-container .section>*{flex:1;margin:8px 0}.incrementNumberInput{border:1px solid var(--border);border-radius:4px;display:flex;height:32px}.incrementNumberInput input[type=number]::-webkit-inner-spin-button,.incrementNumberInput input[type=number]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.incrementNumberInput input[type=number]{-moz-appearance:textfield}.incrementNumberInput .ui__input{border:0;height:100%}.incrementNumberInput .ui__input .ui__input__input{width:100%;height:100%;padding:6px;line-height:normal}.incrementNumberInput .ui__input--message-default.ui__input--focused{outline:none;box-shadow:none}.incrementNumberInput .increment-buttons{display:flex;flex-direction:column;grid-gap:2px;gap:2px;justify-content:center;padding:2px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .incrementNumberInput .increment-buttons{display:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .incrementNumberInput .increment-buttons{display:none}}.incrementNumberInput .increment-buttons .increment-arrow-button{border:0;border-radius:2px;height:10px;width:20px;display:flex;justify-content:center;align-items:center;line-height:10px;padding:0;background:none;cursor:pointer}.incrementNumberInput .increment-buttons .increment-arrow-button .Icon{height:12px;width:12px}.incrementNumberInput .increment-buttons .increment-arrow-button:active{box-shadow:0 0 1px 0 var(--document-box-shadow)}.incrementNumberInput[focus-within]{border:1px solid var(--focus-border)}.incrementNumberInput:focus-within{border:1px solid var(--focus-border)}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1932: function (e, t, n) {
			var o = n(30),
				a = n(1933);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[e.i, a, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const a = t[o];
						if (0 === o)
							a.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							a.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(a, r);
			e.exports = a.locals || {};
		},
		1933: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled span{color:var(--primary-button-text)}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.insert-uploaded-page-panel{overflow-y:auto;max-height:100%}@media(max-height:500px){.App:not(.is-web-component) .insert-uploaded-page-panel{overflow:auto;max-height:100%}}@container (max-height: 500px){.App.is-web-component .insert-uploaded-page-panel{overflow:auto;max-height:100%}}.insert-uploaded-page-panel{box-sizing:border-box;display:flex;flex-direction:column;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);width:791px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel{width:100%;height:100vh;left:0;bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel{width:100%;height:100vh;left:0;bottom:0}}.insert-uploaded-page-panel .header{display:flex;justify-content:space-between;width:100%;font-size:16px;line-height:24px;color:var(--gray-9);font-weight:700;box-shadow:inset 0 -1px 0 var(--divider);padding:20px 16px}.insert-uploaded-page-panel .header .left-header{display:flex;align-items:center;grid-gap:4px;gap:4px}.insert-uploaded-page-panel .header .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.insert-uploaded-page-panel .insert-page-divider{border-top:1px solid var(--divider);margin:18px -16px 16px;width:inherit}.insert-uploaded-page-panel .insert-blank-page-controls{padding-bottom:32px;display:flex;grid-gap:48px;gap:48px;height:90px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls{height:auto;flex-direction:column;grid-gap:20px;gap:20px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls input,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-blank-page-controls label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls{height:auto;flex-direction:column;grid-gap:20px;gap:20px}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls input,.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-blank-page-controls label{font-size:13px}}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-titles{font-size:13px;font-weight:700;color:var(--text-color)}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location-options{display:flex;flex-direction:column}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location-options .insert-page-options{display:flex;flex-direction:row;align-items:baseline;grid-gap:20px;gap:20px;padding-top:11px}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location{display:flex;flex-direction:column}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location .insert-page-input{display:flex;padding-top:8px;grid-gap:8px;gap:8px;align-items:baseline;font-size:13px;font-weight:400;-webkit-font-smoothing:auto}.insert-uploaded-page-panel .insert-blank-page-controls .insert-page-location .page-number-error{color:var(--color-message-error)}.insert-uploaded-page-panel .modal-body{height:unset;display:flex;flex-direction:column;box-shadow:inset 0 -1px 0 var(--divider);padding:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body{flex:1}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body{flex:1}}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{height:409px;width:100%;padding:16px 0 16px 16px;border-radius:4px;overflow:auto;background-color:var(--gray-2);display:flex;flex-wrap:wrap;grid-gap:16px;gap:16px}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.isLoading{display:flex;justify-content:center;align-items:center}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card{border:none;box-shadow:inset 0 0 0 1px var(--lighter-border)}.insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container.modular-ui .thumb-card:hover{background:var(--gray-1)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{flex-direction:row;flex:1;grid-gap:16px;gap:16px;padding:16px;max-height:calc(100vh - 328px)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card{flex:0 0 calc(50% - 8px);box-sizing:border-box}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card-img{height:110px;width:83px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-body{align-items:center}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-image{width:100%}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-checkbox{position:absolute;top:2px;right:2px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container{flex-direction:row;flex:1;grid-gap:16px;gap:16px;padding:16px;max-height:calc(100vh - 328px)}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card{flex:0 0 calc(50% - 8px);box-sizing:border-box}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-card-img{height:110px;width:83px}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-body{align-items:center}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-image{width:100%}.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .modal-body .modal-body-thumbnail-container .thumb-checkbox{position:absolute;top:2px;right:2px}}.insert-uploaded-page-panel .insert-page-footer{display:flex;padding:16px;justify-content:flex-end}.insert-uploaded-page-panel .insert-page-footer.isFileSelected{justify-content:space-between}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails{border:none;background-color:transparent;color:var(--secondary-button-text);padding:8px 16px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails,html:not([data-tabbing=true]) .insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails{outline:none}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails:hover{color:var(--secondary-button-hover)}.insert-uploaded-page-panel .insert-page-footer .deselect-thumbnails.disabled{visibility:hidden}.insert-uploaded-page-panel .insert-page-footer .modal-btn{border:none;background-color:transparent;border-radius:4px;padding:8px 16px;height:32px;width:100px;display:flex;align-items:center;justify-content:center;position:relative;font-weight:400;cursor:pointer}:host(:not([data-tabbing=true])) .insert-uploaded-page-panel .insert-page-footer .modal-btn,html:not([data-tabbing=true]) .insert-uploaded-page-panel .insert-page-footer .modal-btn{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-page-footer .modal-btn{height:32px;width:100px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-page-footer .modal-btn{height:32px;width:100px}}.insert-uploaded-page-panel .insert-page-footer .modal-btn:not(:disabled):not(.disabled){background:var(--primary-button);color:var(--primary-button-text)}.insert-uploaded-page-panel .insert-page-footer .modal-btn:not(:disabled):not(.disabled):hover{background:var(--primary-button-hover)}.insert-uploaded-page-panel .insert-page-footer .modal-btn.disabled{border:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .insert-uploaded-page-panel .insert-page-footer button{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .insert-uploaded-page-panel .insert-page-footer button{font-size:13px}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1934: function (e, t, n) {
			var o = n(30),
				a = n(1935);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[e.i, a, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const a = t[o];
						if (0 === o)
							a.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							a.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(a, r);
			e.exports = a.locals || {};
		},
		1935: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				".open.InsertPageModal{visibility:visible}.closed.InsertPageModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.InsertPageModal .footer .modal-button.confirm:hover,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton:enabled:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.InsertPageModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.confirm.disabled,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.confirm.disabled span,.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled span{color:var(--primary-button-text)}.InsertPageModal .footer .modal-button.cancel:hover,.InsertPageModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.InsertPageModal .footer .modal-button.cancel,.InsertPageModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.InsertPageModal .footer .modal-button.cancel.disabled,.InsertPageModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.InsertPageModal .footer .modal-button.cancel.disabled span,.InsertPageModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.InsertPageModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.InsertPageModal .modal-container .wrapper .modal-content{padding:10px}.InsertPageModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.InsertPageModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.InsertPageModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.InsertPageModal .footer .modal-button.confirm{margin-left:4px}.InsertPageModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .footer .modal-button{padding:23px 8px}}.InsertPageModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .swipe-indicator{width:32px}}.InsertPageModal .modal-container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.InsertPageModal .modal-container .tab-list .tab-options-button{text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.InsertPageModal .modal-container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.InsertPageModal .modal-container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.InsertPageModal .modal-container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.InsertPageModal .modal-container .tab-list .tab-options-button.selected{cursor:default}.InsertPageModal .modal-container .tab-list .tab-options-button.focus-visible,.InsertPageModal .modal-container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.InsertPageModal .modal-container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.InsertPageModal .modal-container .tab-panel.focus-visible,.InsertPageModal .modal-container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.InsertPageModal .modal-container{overflow-y:visible;display:flex;flex-direction:column;justify-content:space-between;width:480px;padding:0;border-radius:4px;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .modal-container{border-radius:0;width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .modal-container{border-radius:0;width:100%}}@media(max-height:320px){.App:not(.is-web-component) .InsertPageModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}@container (max-height: 320px){.App.is-web-component .InsertPageModal .modal-container{display:grid;height:100%;position:fixed;top:0;grid-template-rows:100px auto 70px;justify-content:normal}}.InsertPageModal .modal-container .tabs-header-container{padding:16px}.InsertPageModal .modal-container .header{margin:0;display:flex;align-items:center;width:100%}.InsertPageModal .modal-container .header p{font-size:16px;font-weight:700;width:89.286%;margin:0 16px 0 0}.InsertPageModal .modal-container .header .insertPageModalCloseButton{position:static;height:32px;width:32px;border-radius:4px}.InsertPageModal .modal-container .header .insertPageModalCloseButton:hover{background:var(--tools-button-hover)}.InsertPageModal .modal-container .header .insertPageModalCloseButton.selected{background:var(--view-header-button-active);cursor:default}.InsertPageModal .modal-container .tab-panel{overflow-y:visible}.InsertPageModal .modal-container .tab-panel .panel-container{padding:0 16px 16px}.InsertPageModal .modal-container .tab-panel .panel-body{width:100%;height:240px;position:relative;padding:0 16px 16px}.InsertPageModal .modal-container .tab-list{font-size:14px}.InsertPageModal .modal-container .tab-list .tab-options-button{padding:0;border:none;background-color:transparent}:host(:not([data-tabbing=true])) .InsertPageModal .modal-container .tab-list .tab-options-button,html:not([data-tabbing=true]) .InsertPageModal .modal-container .tab-list .tab-options-button{outline:none}.InsertPageModal .modal-container .footer{display:flex;padding:16px;align-items:center;justify-content:flex-end;width:100%;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border);margin:0}.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;padding:0 8px;height:32px;width:92px;display:flex;align-items:center;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton,html:not([data-tabbing=true]) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton{font-size:13px}}.InsertPageModal .modal-container .footer .Button.insertPageModalConfirmButton.disabled{border:none}.InsertPageModal .modal-container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.InsertPageModal .modal-container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.InsertPageModal .modal-container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.InsertPageModal .modal-container .tab-list .tab-options-button:hover+button,.InsertPageModal .modal-container .tab-list .tab-options-button:hover+div{border-left:none}.InsertPageModal .modal-container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.InsertPageModal .modal-container .tab-list .tab-options-button.selected+button,.InsertPageModal .modal-container .tab-list .tab-options-button.selected+div{border-left:none!important}.InsertPageModal .modal-container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2017: function (e, t, n) {
			"use strict";
			n.r(t);
			n(19),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18);
			var o = n(0),
				a = n.n(o),
				r =
					(n(41),
					n(54),
					n(22),
					n(61),
					n(62),
					n(63),
					n(64),
					n(37),
					n(39),
					n(23),
					n(24),
					n(40),
					n(60),
					n(6)),
				i = n(2),
				l = n(4),
				s = n(347),
				p = n(5),
				d = n(136),
				c = n(42),
				u = n(21),
				m = n(351),
				b = n(1),
				g = n(123),
				f = (n(32), n(602), n(36), n(3)),
				h = n.n(f),
				y = n(1355),
				x = n(450),
				w = n(78),
				v = (n(116), n(27), n(28), n(25), n(2007)),
				P = n(43),
				k = n(17),
				E = n.n(k);
			function I(e) {
				return (I =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function A(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== I(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== I(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === I(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function M(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return N(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return N(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function N(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var S = function (e) {
				var t = e.id,
					n = e.className,
					r = e.min,
					i = e.onChange,
					l = e.value,
					s = e.fillWidth,
					p = M(Object(o.useState)(l), 2),
					d = p[0],
					c = p[1];
				return a.a.createElement(
					"div",
					{ className: E()(A({ incrementNumberInput: !0 }, n, !!n)) },
					a.a.createElement(v.a, {
						id: t,
						type: "number",
						min: r,
						onChange: function (e) {
							i(parseInt(e.target.value)), c(parseInt(e.target.value));
						},
						value: d,
						fillWidth: s,
						onBlur: function (e) {
							var t = parseInt(e.target.value);
							t || ((t = parseInt(r)), i(t), c(t));
						},
					}),
					a.a.createElement(
						"div",
						{ className: "increment-buttons" },
						a.a.createElement(
							"button",
							{
								className: "increment-arrow-button",
								onClick: function () {
									i(d + 1), c(d + 1);
								},
							},
							a.a.createElement(P.a, {
								className: "up-arrow",
								glyph: "icon-chevron-up",
							}),
						),
						a.a.createElement(
							"button",
							{
								className: "increment-arrow-button",
								onClick: function () {
									var e = d - 1;
									e < r || (i(e), c(e));
								},
							},
							a.a.createElement(P.a, {
								className: "down-arrow",
								glyph: "icon-chevron-down",
							}),
						),
					),
				);
			};
			S.propTypes = {
				id: h.a.string,
				className: h.a.string,
				min: h.a.string,
				onChange: h.a.func.isRequired,
				value: h.a.number,
				fillWidth: h.a.bool,
			};
			var j = S,
				O = (n(101), n(112), n(46), n(52), n(38));
			n(1928);
			function _(e) {
				return (_ =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function C(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== _(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== _(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === _(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function T(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return L(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return L(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function L(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var B = function (e) {
				var t = e.id,
					n = e.className,
					r = e.label,
					i = e.initialValue,
					l = e.onChange,
					s = e.unit,
					p = e.maxLength,
					d = void 0 === p ? 10 : p,
					c = e.disabled,
					u = T(Object(o.useState)(i), 2),
					m = u[0],
					b = u[1];
				return a.a.createElement(
					"div",
					{ className: E()(C({ dimensionInput: !0 }, n, !!n)) },
					a.a.createElement(
						"label",
						{ className: "dimension-input-label" },
						r,
						a.a.createElement(
							"div",
							{ className: "dimension-input-container" },
							a.a.createElement("input", {
								id: t,
								className: "dimension-input",
								type: "number",
								min: "0",
								step: 0.01,
								onChange: function (e) {
									b(e.target.value), l(e.target.value);
								},
								disabled: c,
								style: {
									width: "".concat(
										(function (e) {
											var t = e.toString().length,
												n = 0.3;
											if (O.f) {
												(t *= 1.25), (d *= 1.25), (n *= 1.25);
											}
											return (
												e.toString().includes(".") ? (t -= n) : (t += n),
												t > d ? d : t
											);
										})(m),
										"ch",
									),
								},
								value: m,
							}),
							a.a.createElement(
								"span",
								{ className: "dimension-unit" },
								m > 0 && s,
							),
						),
					),
				);
			};
			B.propTypes = {
				id: h.a.string.isRequired,
				className: h.a.string,
				label: h.a.string,
				initialValue: h.a.number.isRequired,
				onChange: h.a.func.isRequired,
				unit: h.a.string.isRequired,
				maxLength: h.a.number,
				disabled: h.a.bool,
			};
			var D = B;
			n(1930);
			function R(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return F(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return F(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function F(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var H = function (e) {
				var t = e.insertNewPageBelow,
					n = e.insertNewPageIndexes,
					i = e.numberOfBlankPagesToInsert,
					p = e.pageNumberError,
					d = e.setInsertNewPageBelow,
					c = e.setInsertNewPageIndexes,
					u = e.setNumberOfBlankPagesToInsert,
					m = e.setInsertPageHeight,
					b = e.setInsertPageWidth,
					g = e.setPageNumberError,
					f = e.loadedDocumentPageCount,
					h = R(
						Object(r.e)(function (e) {
							return [l.a.getPresetNewPageDimensions(e)];
						}),
						1,
					)[0],
					v = R(Object(s.a)(), 1)[0],
					P = Object.keys(h),
					k = "above",
					E = "below",
					I = {
						"Inches (in)": '"',
						"Centimeters (cm)": "cm",
						"Millimeters (mm)": "mm",
					},
					A = { '"': 1, cm: 2.54, mm: 25.4, pt: 72 },
					M = R(Object(o.useState)(P[0]), 2),
					N = M[0],
					S = M[1],
					O = R(Object(o.useState)(!1), 2),
					_ = O[0],
					C = O[1],
					T = R(Object(o.useState)(Object.getOwnPropertyNames(I)[0]), 2),
					L = T[0],
					B = T[1],
					F = R(Object(o.useState)(h[P[0]].width), 2),
					H = F[0],
					z = F[1],
					W = R(Object(o.useState)(h[P[0]].height), 2),
					G = W[0],
					q = W[1];
				Object(o.useEffect)(function () {
					b(72 * h[N].width), m(72 * h[N].height);
				}, []);
				var U = function (e) {
					e.length > 0
						? g(null)
						: g("".concat(v("message.errorBlankPageNumber"))),
						c(e);
				};
				return a.a.createElement(
					"div",
					{ className: "insert-blank-page-panel" },
					a.a.createElement(
						"div",
						{ className: "panel-container" },
						a.a.createElement(
							"div",
							{ className: "subheader" },
							v("insertPageModal.pagePlacements.header"),
						),
						a.a.createElement(
							"div",
							{ className: "section" },
							a.a.createElement(y.a, {
								label: v("insertPageModal.pagePlacements.".concat(k)),
								name: "PAGE_PLACEMENT",
								onChange: function () {
									d(!1);
								},
								checked: !t,
								radio: !0,
							}),
							a.a.createElement(y.a, {
								label: v("insertPageModal.pagePlacements.".concat(E)),
								name: "PAGE_PLACEMENT",
								onChange: function () {
									d(!0);
								},
								checked: t,
								radio: !0,
							}),
						),
						a.a.createElement(
							"div",
							{ className: "subheader" },
							v("insertPageModal.pageLocations.header"),
						),
						a.a.createElement(
							"div",
							{ className: "section extra-space-section" },
							a.a.createElement(
								"div",
								{ className: "input-container" },
								a.a.createElement(
									"label",
									{
										className: "specify-pages-wrapper",
										htmlFor: "specifyPagesInput",
									},
									v("insertPageModal.pageLocations.specify"),
									a.a.createElement(
										"span",
										{ className: "input-sub-text" },
										v("insertPageModal.pageLocations.total"),
										": ",
										f,
										" ",
										v("insertPageModal.pageLocations.pages"),
									),
								),
								a.a.createElement(x.a, {
									id: "specifyPagesInput",
									selectedPageNumbers: n,
									pageCount: f,
									onSelectedPageNumbersChange: U,
									onBlurHandler: U,
									pageNumberError: p,
								}),
							),
							a.a.createElement(
								"div",
								{ className: "input-container" },
								a.a.createElement(
									"label",
									{ htmlFor: "numberOfPagesInput" },
									v("insertPageModal.pageLocations.amount"),
								),
								a.a.createElement(j, {
									id: "numberOfPagesInput",
									type: "number",
									min: "1",
									onChange: function (e) {
										u(e);
									},
									value: i,
									fillWidth: !0,
								}),
							),
						),
						a.a.createElement(
							"div",
							{ className: "subheader" },
							v("insertPageModal.pageDimensions.header"),
						),
						a.a.createElement(
							"div",
							{ className: "section page-dimensions-section" },
							a.a.createElement(
								"div",
								{ className: "input-container" },
								a.a.createElement(
									"label",
									{
										id: "insert-blank-pages-preset-label",
										htmlFor: "pagesPreset",
									},
									v("insertPageModal.pageDimensions.subHeader"),
								),
								a.a.createElement(w.a, {
									id: "pagesPreset",
									labelledById: "insert-blank-pages-preset-label",
									dataElement: "presetSelector",
									currentSelectionKey: N,
									onClickItem: function (e) {
										"Custom" === e
											? (C(!0), b(H * (72 / A[I[L]])), m(G * (72 / A[I[L]])))
											: (b(72 * h[e].width), m(72 * h[e].height), C(!1)),
											S(e);
									},
									items: [].concat(P, ["Custom"]),
								}),
							),
							a.a.createElement(
								"div",
								{
									className: "input-container",
									style: { visibility: _ ? "visible" : "hidden" },
								},
								a.a.createElement(
									"label",
									{
										id: "insert-blank-pages-dimensions-label",
										htmlFor: "pageDimensionsUnit",
									},
									v("insertPageModal.pageDimensions.units"),
								),
								a.a.createElement(w.a, {
									id: "pageDimensionsUnit",
									labelledById: "insert-blank-pages-dimensions-label",
									dataElement: "unitSelector",
									currentSelectionKey: L,
									onClickItem: function (e) {
										B(e), b(H * (72 / A[I[e]])), m(G * (72 / A[I[e]]));
									},
									items: Object.keys(I),
								}),
							),
						),
						a.a.createElement(
							"div",
							{ className: "section", style: { display: _ ? "flex" : "none" } },
							a.a.createElement(
								"div",
								{ className: "input-container" },
								a.a.createElement(
									"label",
									{ htmlFor: "pageWidthInput" },
									v("formField.formFieldPopup.width"),
								),
								a.a.createElement(D, {
									id: "pageWidthInput",
									className: "customWidthInput",
									initialValue: H,
									onChange: function (e) {
										z(e), b(e * (72 / A[I[L]]));
									},
									unit: I[L],
								}),
							),
							a.a.createElement(
								"div",
								{ className: "input-container" },
								a.a.createElement(
									"label",
									{ htmlFor: "pageHeightInput" },
									v("formField.formFieldPopup.height"),
								),
								a.a.createElement(D, {
									id: "pageHeightInput",
									className: "customHeightInput",
									initialValue: G,
									onChange: function (e) {
										q(e), m(e * (72 / A[I[L]]));
									},
									unit: I[L],
								}),
							),
						),
					),
				);
			};
			H.propTypes = {
				insertNewPageBelow: h.a.bool,
				insertNewPageIndexes: h.a.array,
				numberOfBlankPagesToInsert: h.a.number,
				pageNumberError: h.a.string,
				setInsertNewPageBelow: h.a.func,
				setInsertNewPageIndexes: h.a.func,
				setNumberOfBlankPagesToInsert: h.a.func,
				setInsertPageHeight: h.a.func,
				setInsertPageWidth: h.a.func,
				setPageNumberError: h.a.func,
				loadedDocumentPageCount: h.a.number,
			};
			var z = H,
				W =
					(n(58), n(44), n(120), n(29), n(47), n(49), n(48), n(1932), n(1808));
			function G(e) {
				return (G =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function q(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function U(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== G(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== G(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === G(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function V(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return $(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return $(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function $(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var K = a.a.forwardRef(function (e, t) {
				var n = e.sourceDocument,
					i = e.closeModal,
					p = e.clearLoadedFile,
					d = e.insertPages,
					u = e.loadedDocumentPageCount,
					m = e.closeModalWarning,
					b = e.insertNewPageIndexes,
					g = void 0 === b ? [1] : b,
					f = V(Object(s.a)(), 1)[0],
					h = V(Object(o.useState)({}), 2),
					w = h[0],
					v = h[1],
					P = V(Object(o.useState)(!0), 2),
					k = P[0],
					I = P[1],
					A = V(Object(o.useState)([g[0]]), 2),
					M = A[0],
					N = A[1],
					S = V(Object(o.useState)(!0), 2),
					j = S[0],
					O = S[1],
					_ = V(Object(o.useState)(""), 2),
					C = _[0],
					T = _[1],
					L = Object(r.e)(function (e) {
						var t;
						return null === (t = l.a.getFeatureFlags(e)) || void 0 === t
							? void 0
							: t.customizableUI;
					});
				Object(o.useEffect)(
					function () {
						for (var e = n.getPageCount(), t = {}, o = 1; o <= e; o++)
							t[o] = !0;
						v(t);
					},
					[n],
				);
				var B = function () {
						return Object.keys(w).reduce(function (e, t) {
							return w[t] && e.push(parseInt(t)), e;
						}, []);
					},
					D = function () {
						O(!j);
					};
				return a.a.createElement(
					"div",
					{
						className: "insert-uploaded-page-panel",
						onMouseDown: function (e) {
							return e.stopPropagation();
						},
						ref: t,
					},
					a.a.createElement(
						"div",
						{ className: "header" },
						a.a.createElement(
							"div",
							{ className: "left-header" },
							a.a.createElement(c.a, {
								img: "icon-arrow-back",
								onClick: p,
								dataElement: "insertFromFileBackButton",
								title: f("action.back"),
							}),
							f("insertPageModal.selectPages"),
							" ",
							"(".concat(k ? 0 : B().length, ")"),
						),
						a.a.createElement(c.a, {
							className: "insertPageModalCloseButton",
							img: "icon-close",
							onClick: function () {
								m();
							},
							title: "action.cancel",
						}),
					),
					a.a.createElement(
						"div",
						{ className: "modal-body" },
						a.a.createElement(
							"div",
							{ className: "insert-blank-page-controls" },
							a.a.createElement(
								"div",
								{ className: "insert-page-location-options" },
								a.a.createElement(
									"span",
									{ className: "insert-page-titles " },
									f("insertPageModal.pagePlacements.header"),
								),
								a.a.createElement(
									"div",
									{ className: "insert-page-options" },
									a.a.createElement(y.a, {
										label: f("insertPageModal.pagePlacements.above"),
										radio: !0,
										name: "insertPagePosition",
										checked: j,
										onChange: D,
									}),
									a.a.createElement(y.a, {
										label: f("insertPageModal.pagePlacements.below"),
										radio: !0,
										name: "insertPagePosition",
										checked: !j,
										onChange: D,
									}),
								),
							),
							a.a.createElement(
								"div",
								{ className: "insert-page-location" },
								a.a.createElement(
									"span",
									{ className: "insert-page-titles " },
									f("insertPageModal.pageLocations.specifyLocation"),
								),
								a.a.createElement(
									"div",
									{ className: "insert-page-input" },
									f("insertPageModal.page"),
									":",
									a.a.createElement(x.a, {
										selectedPageNumbers: M,
										pageCount: u,
										onSelectedPageNumbersChange: function (e) {
											e.length > 0 && (T(null), N(e));
										},
										onBlurHandler: N,
										onError: function (e) {
											e &&
												T(
													""
														.concat(f("message.errorPageNumber"), " ")
														.concat(u),
												);
										},
										pageNumberError: C,
									}),
								),
							),
						),
						a.a.createElement(
							"div",
							{
								className: E()("modal-body-thumbnail-container", {
									isLoading: k,
									"modular-ui": L,
								}),
							},
							a.a.createElement(W.a, {
								document: n,
								onThumbnailSelected: function (e) {
									void 0 === w[e] ? (w[e] = !0) : (w[e] = !w[e]),
										v(
											(function (e) {
												for (var t = 1; t < arguments.length; t++) {
													var n = null != arguments[t] ? arguments[t] : {};
													t % 2
														? q(Object(n), !0).forEach(function (t) {
																U(e, t, n[t]);
															})
														: Object.getOwnPropertyDescriptors
															? Object.defineProperties(
																	e,
																	Object.getOwnPropertyDescriptors(n),
																)
															: q(Object(n)).forEach(function (t) {
																	Object.defineProperty(
																		e,
																		t,
																		Object.getOwnPropertyDescriptor(n, t),
																	);
																});
												}
												return e;
											})({}, w),
										);
								},
								selectedThumbnails: w,
								onfileLoadedHandler: I,
							}),
						),
					),
					a.a.createElement(
						"div",
						{ className: E()("insert-page-footer", { isFileSelected: !k }) },
						a.a.createElement(
							"button",
							{
								className: E()("deselect-thumbnails", { disabled: k }),
								onClick: function () {
									v({});
								},
								disabled: k,
							},
							f("action.deselectAll"),
						),
						a.a.createElement(c.a, {
							className: "modal-btn",
							onClick: function () {
								var e = M[0];
								if (e) {
									var t = j ? e : ++e;
									t > u && (t = null), d(n, B(), t);
								}
								i();
							},
							label: f("insertPageModal.button"),
							disabled: 0 === B().length || k || 0 === M.length || C,
						}),
					),
				);
			});
			K.displayName = K;
			var J = K;
			function Y() {
				return (Y = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			var Q = a.a.forwardRef(function (e, t) {
				var n = Object(r.d)(),
					o = e.closeModal;
				return a.a.createElement(
					J,
					Y({}, e, {
						insertPages: g.h,
						closeModalWarning: function () {
							return Object(g.c)(o, n);
						},
						ref: t,
					}),
				);
			});
			Q.displayName = "InsertUploadedPagePanelContainer";
			var X = Q,
				Z = n(456);
			n(1934);
			function ee(e) {
				return (ee =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function te() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ te =
					function () {
						return e;
					};
				var e = {},
					t = Object.prototype,
					n = t.hasOwnProperty,
					o =
						Object.defineProperty ||
						function (e, t, n) {
							e[t] = n.value;
						},
					a = "function" == typeof Symbol ? Symbol : {},
					r = a.iterator || "@@iterator",
					i = a.asyncIterator || "@@asyncIterator",
					l = a.toStringTag || "@@toStringTag";
				function s(e, t, n) {
					return (
						Object.defineProperty(e, t, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[t]
					);
				}
				try {
					s({}, "");
				} catch (e) {
					s = function (e, t, n) {
						return (e[t] = n);
					};
				}
				function p(e, t, n, a) {
					var r = t && t.prototype instanceof u ? t : u,
						i = Object.create(r.prototype),
						l = new I(a || []);
					return o(i, "_invoke", { value: v(e, n, l) }), i;
				}
				function d(e, t, n) {
					try {
						return { type: "normal", arg: e.call(t, n) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = p;
				var c = {};
				function u() {}
				function m() {}
				function b() {}
				var g = {};
				s(g, r, function () {
					return this;
				});
				var f = Object.getPrototypeOf,
					h = f && f(f(A([])));
				h && h !== t && n.call(h, r) && (g = h);
				var y = (b.prototype = u.prototype = Object.create(g));
				function x(e) {
					["next", "throw", "return"].forEach(function (t) {
						s(e, t, function (e) {
							return this._invoke(t, e);
						});
					});
				}
				function w(e, t) {
					var a;
					o(this, "_invoke", {
						value: function (o, r) {
							function i() {
								return new t(function (a, i) {
									!(function o(a, r, i, l) {
										var s = d(e[a], e, r);
										if ("throw" !== s.type) {
											var p = s.arg,
												c = p.value;
											return c && "object" == ee(c) && n.call(c, "__await")
												? t.resolve(c.__await).then(
														function (e) {
															o("next", e, i, l);
														},
														function (e) {
															o("throw", e, i, l);
														},
													)
												: t.resolve(c).then(
														function (e) {
															(p.value = e), i(p);
														},
														function (e) {
															return o("throw", e, i, l);
														},
													);
										}
										l(s.arg);
									})(o, r, a, i);
								});
							}
							return (a = a ? a.then(i, i) : i());
						},
					});
				}
				function v(e, t, n) {
					var o = "suspendedStart";
					return function (a, r) {
						if ("executing" === o)
							throw new Error("Generator is already running");
						if ("completed" === o) {
							if ("throw" === a) throw r;
							return M();
						}
						for (n.method = a, n.arg = r; ; ) {
							var i = n.delegate;
							if (i) {
								var l = P(i, n);
								if (l) {
									if (l === c) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === o) throw ((o = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							o = "executing";
							var s = d(e, t, n);
							if ("normal" === s.type) {
								if (
									((o = n.done ? "completed" : "suspendedYield"), s.arg === c)
								)
									continue;
								return { value: s.arg, done: n.done };
							}
							"throw" === s.type &&
								((o = "completed"), (n.method = "throw"), (n.arg = s.arg));
						}
					};
				}
				function P(e, t) {
					var n = t.method,
						o = e.iterator[n];
					if (void 0 === o)
						return (
							(t.delegate = null),
							("throw" === n &&
								e.iterator.return &&
								((t.method = "return"),
								(t.arg = void 0),
								P(e, t),
								"throw" === t.method)) ||
								("return" !== n &&
									((t.method = "throw"),
									(t.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							c
						);
					var a = d(o, e.iterator, t.arg);
					if ("throw" === a.type)
						return (
							(t.method = "throw"), (t.arg = a.arg), (t.delegate = null), c
						);
					var r = a.arg;
					return r
						? r.done
							? ((t[e.resultName] = r.value),
								(t.next = e.nextLoc),
								"return" !== t.method &&
									((t.method = "next"), (t.arg = void 0)),
								(t.delegate = null),
								c)
							: r
						: ((t.method = "throw"),
							(t.arg = new TypeError("iterator result is not an object")),
							(t.delegate = null),
							c);
				}
				function k(e) {
					var t = { tryLoc: e[0] };
					1 in e && (t.catchLoc = e[1]),
						2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
						this.tryEntries.push(t);
				}
				function E(e) {
					var t = e.completion || {};
					(t.type = "normal"), delete t.arg, (e.completion = t);
				}
				function I(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(k, this),
						this.reset(!0);
				}
				function A(e) {
					if (e) {
						var t = e[r];
						if (t) return t.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var o = -1,
								a = function t() {
									for (; ++o < e.length; )
										if (n.call(e, o)) return (t.value = e[o]), (t.done = !1), t;
									return (t.value = void 0), (t.done = !0), t;
								};
							return (a.next = a);
						}
					}
					return { next: M };
				}
				function M() {
					return { value: void 0, done: !0 };
				}
				return (
					(m.prototype = b),
					o(y, "constructor", { value: b, configurable: !0 }),
					o(b, "constructor", { value: m, configurable: !0 }),
					(m.displayName = s(b, l, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var t = "function" == typeof e && e.constructor;
						return (
							!!t &&
							(t === m || "GeneratorFunction" === (t.displayName || t.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, b)
								: ((e.__proto__ = b), s(e, l, "GeneratorFunction")),
							(e.prototype = Object.create(y)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					x(w.prototype),
					s(w.prototype, i, function () {
						return this;
					}),
					(e.AsyncIterator = w),
					(e.async = function (t, n, o, a, r) {
						void 0 === r && (r = Promise);
						var i = new w(p(t, n, o, a), r);
						return e.isGeneratorFunction(n)
							? i
							: i.next().then(function (e) {
									return e.done ? e.value : i.next();
								});
					}),
					x(y),
					s(y, l, "Generator"),
					s(y, r, function () {
						return this;
					}),
					s(y, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var t = Object(e),
							n = [];
						for (var o in t) n.push(o);
						return (
							n.reverse(),
							function e() {
								for (; n.length; ) {
									var o = n.pop();
									if (o in t) return (e.value = o), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = A),
					(I.prototype = {
						constructor: I,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(E),
								!e)
							)
								for (var t in this)
									"t" === t.charAt(0) &&
										n.call(this, t) &&
										!isNaN(+t.slice(1)) &&
										(this[t] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var t = this;
							function o(n, o) {
								return (
									(i.type = "throw"),
									(i.arg = e),
									(t.next = n),
									o && ((t.method = "next"), (t.arg = void 0)),
									!!o
								);
							}
							for (var a = this.tryEntries.length - 1; a >= 0; --a) {
								var r = this.tryEntries[a],
									i = r.completion;
								if ("root" === r.tryLoc) return o("end");
								if (r.tryLoc <= this.prev) {
									var l = n.call(r, "catchLoc"),
										s = n.call(r, "finallyLoc");
									if (l && s) {
										if (this.prev < r.catchLoc) return o(r.catchLoc, !0);
										if (this.prev < r.finallyLoc) return o(r.finallyLoc);
									} else if (l) {
										if (this.prev < r.catchLoc) return o(r.catchLoc, !0);
									} else {
										if (!s)
											throw new Error("try statement without catch or finally");
										if (this.prev < r.finallyLoc) return o(r.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, t) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var a = this.tryEntries[o];
								if (
									a.tryLoc <= this.prev &&
									n.call(a, "finallyLoc") &&
									this.prev < a.finallyLoc
								) {
									var r = a;
									break;
								}
							}
							r &&
								("break" === e || "continue" === e) &&
								r.tryLoc <= t &&
								t <= r.finallyLoc &&
								(r = null);
							var i = r ? r.completion : {};
							return (
								(i.type = e),
								(i.arg = t),
								r
									? ((this.method = "next"), (this.next = r.finallyLoc), c)
									: this.complete(i)
							);
						},
						complete: function (e, t) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && t && (this.next = t),
								c
							);
						},
						finish: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.finallyLoc === e)
									return this.complete(n.completion, n.afterLoc), E(n), c;
							}
						},
						catch: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.tryLoc === e) {
									var o = n.completion;
									if ("throw" === o.type) {
										var a = o.arg;
										E(n);
									}
									return a;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, t, n) {
							return (
								(this.delegate = { iterator: A(e), resultName: t, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								c
							);
						},
					}),
					e
				);
			}
			function ne(e, t, n, o, a, r, i) {
				try {
					var l = e[r](i),
						s = l.value;
				} catch (e) {
					return void n(e);
				}
				l.done ? t(s) : Promise.resolve(s).then(o, a);
			}
			function oe(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return ae(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return ae(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function ae(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var re = { loadAsPDF: !0 },
				ie = function (e) {
					var t = e.loadedDocumentPageCount,
						n = oe(
							Object(r.e)(function (e) {
								return [
									l.a.getSelectedThumbnailPageIndexes(e),
									l.a.getCurrentPage(e),
									l.a.getSelectedTab(e, p.a.INSERT_PAGE_MODAL),
								];
							}),
							3,
						),
						f = n[0],
						h = n[1],
						y = n[2],
						x = oe(Object(o.useState)(null), 2),
						w = x[0],
						v = x[1],
						P = oe(Object(o.useState)(!1), 2),
						k = P[0],
						E = P[1],
						I = oe(Object(o.useState)([]), 2),
						A = I[0],
						M = I[1],
						N = oe(Object(o.useState)(1), 2),
						S = N[0],
						j = N[1],
						O = oe(Object(o.useState)(0), 2),
						_ = O[0],
						C = O[1],
						T = oe(Object(o.useState)(0), 2),
						L = T[0],
						B = T[1],
						D = oe(Object(o.useState)(""), 2),
						R = D[0],
						F = D[1];
					Object(o.useEffect)(
						function () {
							var e =
								f.length > 0
									? f.map(function (e) {
											return e + 1;
										})
									: [h];
							M(e);
						},
						[f],
					);
					var H,
						W,
						G = Object(r.d)(),
						q = oe(Object(s.a)(), 1)[0],
						U = function () {
							G(i.a.closeElement(p.a.INSERT_PAGE_MODAL));
						},
						V = function () {
							if (k)
								for (
									var e = function (e) {
											Object(g.g)(
												A.map(function (t, n) {
													return t + (n + 1) * e;
												}),
												L,
												_,
											);
										},
										t = 0;
									t < S;
									++t
								)
									e(t);
							else
								for (
									var n = function (e) {
											Object(g.f)(
												A.map(function (t, n) {
													return t + (n + 1) * e;
												}),
												L,
												_,
											);
										},
										o = 0;
									o < S;
									++o
								)
									n(o);
							U();
						},
						$ = (function () {
							var e,
								t =
									((e = te().mark(function e(t) {
										var n;
										return te().wrap(
											function (e) {
												for (;;)
													switch ((e.prev = e.next)) {
														case 0:
															if (
																!(
																	t instanceof
																	Object(u.c)().instance.Core.Document
																)
															) {
																e.next = 4;
																break;
															}
															(n = t), (e.next = 13);
															break;
														case 4:
															return (
																(e.prev = 4),
																(e.next = 7),
																b.a.createDocument(t, re)
															);
														case 7:
															(n = e.sent), (e.next = 13);
															break;
														case 10:
															(e.prev = 10),
																(e.t0 = e.catch(4)),
																console.error("File type not supported");
														case 13:
															v(n);
														case 14:
														case "end":
															return e.stop();
													}
											},
											e,
											null,
											[[4, 10]],
										);
									})),
									function () {
										var t = this,
											n = arguments;
										return new Promise(function (o, a) {
											var r = e.apply(t, n);
											function i(e) {
												ne(r, o, a, i, l, "next", e);
											}
											function l(e) {
												ne(r, o, a, i, l, "throw", e);
											}
											i(void 0);
										});
									});
							return function (e) {
								return t.apply(this, arguments);
							};
						})(),
						K = function () {
							v(null);
						};
					return a.a.createElement(
						"div",
						{
							className: "Modal open InsertPageModal",
							"data-element": p.a.INSERT_PAGE_MODAL,
							onMouseDown: w
								? function () {
										Object(g.c)(U, G);
									}
								: U,
						},
						a.a.createElement(
							m.a,
							{
								title: w ? null : q("insertPageModal.title"),
								isOpen: !0,
								closeHandler: U,
								onCloseClick: U,
								swipeToClose: !0,
							},
							w
								? a.a.createElement(X, {
										sourceDocument: w,
										closeModal: U,
										clearLoadedFile: K,
										loadedDocumentPageCount: t,
										insertNewPageIndexes: A,
									})
								: ((H = y === p.a.INSERT_FROM_FILE_TAB),
									(W = {
										insertNewPageBelow: k,
										insertNewPageIndexes: A,
										numberOfBlankPagesToInsert: S,
										pageNumberError: R,
										setInsertNewPageBelow: E,
										setInsertNewPageIndexes: M,
										setNumberOfBlankPagesToInsert: j,
										setInsertPageHeight: C,
										setInsertPageWidth: B,
										setPageNumberError: F,
										loadedDocumentPageCount: t,
									}),
									a.a.createElement(
										"div",
										{
											className: "container tabs",
											onClick: function (e) {
												return e.stopPropagation();
											},
											onMouseDown: function (e) {
												return e.stopPropagation();
											},
										},
										a.a.createElement(
											d.d,
											{
												className: "insert-page-tabs",
												id: p.a.INSERT_PAGE_MODAL,
											},
											a.a.createElement(
												"div",
												{ className: "tabs-header-container" },
												a.a.createElement(
													"div",
													{ className: "tab-list" },
													a.a.createElement(
														d.a,
														{ dataElement: p.a.INSERT_BLANK_PAGE_TAB },
														a.a.createElement(
															"button",
															{ className: "tab-options-button" },
															q("insertPageModal.tabs.blank"),
														),
													),
													a.a.createElement("div", {
														className: "tab-options-divider",
													}),
													a.a.createElement(
														d.a,
														{ dataElement: p.a.INSERT_FROM_FILE_TAB },
														a.a.createElement(
															"button",
															{ className: "tab-options-button" },
															q("insertPageModal.tabs.upload"),
														),
													),
												),
											),
											a.a.createElement(
												d.c,
												{ dataElement: p.a.INSERT_BLANK_PAGE_PANEL },
												a.a.createElement(z, W),
											),
											a.a.createElement(
												d.c,
												{ dataElement: p.a.INSERT_FROM_FILE_PANEL },
												a.a.createElement(
													"div",
													{ className: "panel-body" },
													a.a.createElement(Z.a, { onFileProcessed: $ }),
												),
											),
										),
										a.a.createElement("div", { className: "divider" }),
										a.a.createElement(
											"div",
											{ className: "footer" },
											a.a.createElement(c.a, {
												className: "insertPageModalConfirmButton",
												label: "insertPageModal.button",
												onClick: V,
												disabled: L <= 0 || _ <= 0 || H || 0 === A.length || R,
											}),
										),
									)),
						),
					);
				};
			function le(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								a,
								r,
								i,
								l = [],
								s = !0,
								p = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									s = !1;
								} else
									for (
										;
										!(s = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										s = !0
									);
							} catch (e) {
								(p = !0), (a = e);
							} finally {
								try {
									if (
										!s &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw a;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return se(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return se(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function se(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var pe = function () {
				var e = le(
						Object(r.e)(function (e) {
							return [
								l.a.isElementDisabled(e, p.a.INSERT_PAGE_MODAL),
								l.a.isElementOpen(e, p.a.INSERT_PAGE_MODAL),
							];
						}),
						2,
					),
					t = e[0],
					n = e[1];
				if (!t && n) {
					var o = b.a.getDocumentViewer().getDocument()
						? b.a.getTotalPages()
						: null;
					return a.a.createElement(ie, { loadedDocumentPageCount: o });
				}
				return null;
			};
			t.default = pe;
		},
	},
]);
//# sourceMappingURL=chunk.42.js.map
