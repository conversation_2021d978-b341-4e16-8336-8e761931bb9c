{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/se.js"], "names": ["module", "exports", "e", "n", "default", "a", "t", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,6EAA6EC,MAAM,KAAKC,OAAO,mJAAmJD,MAAM,KAAKE,UAAU,EAAEC,cAAc,mCAAmCH,MAAM,KAAKI,YAAY,6DAA6DJ,MAAM,KAAKK,YAAY,gBAAgBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,oBAAoBC,IAAI,gCAAgCC,KAAK,uCAAuCC,aAAa,CAACC,OAAO,YAAYC,KAAK,WAAWC,EAAE,mBAAmBC,EAAE,eAAeC,GAAG,cAAcC,EAAE,cAAcC,GAAG,aAAaC,EAAE,cAAcC,GAAG,cAAcC,EAAE,aAAaC,GAAG,WAAWC,EAAE,aAAaC,GAAG,aAAa,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAvqCD,CAAE,EAAQ", "file": "chunks/chunk.211.js", "sourcesContent": ["!function(e,a){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=a(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],a):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_se=a(e.dayjs)}(this,(function(e){\"use strict\";function a(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var n=a(e),t={name:\"se\",weekdays:\"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat\".split(\"_\"),months:\"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu\".split(\"_\"),weekStart:1,weekdaysShort:\"sotn_vuos_maŋ_gask_duor_bear_láv\".split(\"_\"),monthsShort:\"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov\".split(\"_\"),weekdaysMin:\"s_v_m_g_d_b_L\".split(\"_\"),ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"MMMM D. [b.] YYYY\",LLL:\"MMMM D. [b.] YYYY [ti.] HH:mm\",LLLL:\"dddd, MMMM D. [b.] YYYY [ti.] HH:mm\"},relativeTime:{future:\"%s geažes\",past:\"maŋit %s\",s:\"moadde sekunddat\",m:\"okta minuhta\",mm:\"%d minuhtat\",h:\"okta diimmu\",hh:\"%d diimmut\",d:\"okta beaivi\",dd:\"%d beaivvit\",M:\"okta mánnu\",MM:\"%d mánut\",y:\"okta jahki\",yy:\"%d jagit\"}};return n.default.locale(t,null,!0),t}));"], "sourceRoot": ""}