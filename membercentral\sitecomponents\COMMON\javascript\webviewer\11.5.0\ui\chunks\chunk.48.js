(window.webpackJsonp = window.webpackJsonp || []).push([
	[48],
	{
		1562: function (e, t, n) {
			"use strict";
			var o = n(0),
				i = n.n(o),
				r =
					(n(1579),
					function (e) {
						var t = e.height,
							n = void 0 === t ? "50px" : t,
							o = e.width,
							r = { height: n, width: void 0 === o ? "54px" : o };
						return i.a.createElement("div", { className: "spinner", style: r });
					});
			t.a = r;
		},
		1579: function (e, t, n) {
			var o = n(30),
				i = n(1580);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const i = t[o];
						if (0 === o)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(i, r);
			e.exports = i.locals || {};
		},
		1580: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1661: function (e, t, n) {
			var o = n(30),
				i = n(1662);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var r = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const i = t[o];
						if (0 === o)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(i, r);
			e.exports = i.locals || {};
		},
		1662: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.fileAttachmentPanel{z-index:65;display:flex;flex-direction:column;transition:transform .3s ease,visibility 0s ease .3s}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel{top:0;width:100%;height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel{top:0;width:100%;height:100%}}.open.fileAttachmentPanel{transform:none;visibility:visible;transition:transform .3s ease,visibility 0s ease 0s}.fileAttachmentPanel .empty-panel-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;padding:36px;grid-gap:8px;gap:8px}.fileAttachmentPanel .empty-panel-container .empty-icon{width:60px;height:60px;color:var(--gray-6);fill:var(--gray-6)}.fileAttachmentPanel .empty-panel-container .empty-icon svg{width:60px;height:60px}.fileAttachmentPanel .empty-panel-container .empty-message{text-align:center;max-width:131px;font-size:13px}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel .empty-panel-container .empty-message{line-height:15px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel .empty-panel-container .empty-message{line-height:15px}}.fileAttachmentPanel{display:block;font-size:var(--font-size-default)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileAttachmentPanel{margin:16px;width:auto;flex-grow:1;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileAttachmentPanel{margin:16px;width:auto;flex-grow:1;overflow-y:auto}}.fileAttachmentPanel .section{margin-bottom:8px}.fileAttachmentPanel .section h2.title{font-weight:700;font-size:13px;padding-left:var(--fileAttachment-title-padding);margin:12px 0}.fileAttachmentPanel .section ul.downloadable{padding-left:var(--fileAttachment-list-padding)}.fileAttachmentPanel .section ul.downloadable>li{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding:4px 0}.fileAttachmentPanel .section ul.downloadable>li>button{cursor:pointer;color:var(--secondary-button-text);text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding:4px 0}.fileAttachmentPanel .section ul.downloadable>li>button:hover{color:var(--secondary-button-hover)}.fileAttachmentPanel .section ul.downloadable .embedSpinner{display:flex;flex-direction:row;justify-content:space-between}.fileAttachmentPanel .section ul.downloadable .embedSpinner .spinner{margin:0}.fileAttachmentPanel .section ul ul{padding-left:10px;list-style-type:none}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1708: function (e, t, n) {
			"use strict";
			n.r(t);
			n(18),
				n(176),
				n(36),
				n(226),
				n(90),
				n(41),
				n(8),
				n(54),
				n(1577),
				n(19),
				n(12),
				n(13),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(22),
				n(61),
				n(62),
				n(63),
				n(64),
				n(37),
				n(39),
				n(23),
				n(24),
				n(40),
				n(60);
			var o = n(0),
				i = n.n(o),
				r = n(347),
				a = n(275),
				l = n(1562),
				c = n(165),
				s = n(43),
				p = n(1),
				d = n(6),
				u = n(4),
				h = n(2),
				f = n(5),
				m = (n(1661), n(34));
			function y(e) {
				return (y =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function w() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ w =
					function () {
						return e;
					};
				var e = {},
					t = Object.prototype,
					n = t.hasOwnProperty,
					o =
						Object.defineProperty ||
						function (e, t, n) {
							e[t] = n.value;
						},
					i = "function" == typeof Symbol ? Symbol : {},
					r = i.iterator || "@@iterator",
					a = i.asyncIterator || "@@asyncIterator",
					l = i.toStringTag || "@@toStringTag";
				function c(e, t, n) {
					return (
						Object.defineProperty(e, t, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[t]
					);
				}
				try {
					c({}, "");
				} catch (e) {
					c = function (e, t, n) {
						return (e[t] = n);
					};
				}
				function s(e, t, n, i) {
					var r = t && t.prototype instanceof u ? t : u,
						a = Object.create(r.prototype),
						l = new O(i || []);
					return o(a, "_invoke", { value: E(e, n, l) }), a;
				}
				function p(e, t, n) {
					try {
						return { type: "normal", arg: e.call(t, n) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = s;
				var d = {};
				function u() {}
				function h() {}
				function f() {}
				var m = {};
				c(m, r, function () {
					return this;
				});
				var v = Object.getPrototypeOf,
					b = v && v(v(_([])));
				b && b !== t && n.call(b, r) && (m = b);
				var g = (f.prototype = u.prototype = Object.create(m));
				function x(e) {
					["next", "throw", "return"].forEach(function (t) {
						c(e, t, function (e) {
							return this._invoke(t, e);
						});
					});
				}
				function A(e, t) {
					var i;
					o(this, "_invoke", {
						value: function (o, r) {
							function a() {
								return new t(function (i, a) {
									!(function o(i, r, a, l) {
										var c = p(e[i], e, r);
										if ("throw" !== c.type) {
											var s = c.arg,
												d = s.value;
											return d && "object" == y(d) && n.call(d, "__await")
												? t.resolve(d.__await).then(
														function (e) {
															o("next", e, a, l);
														},
														function (e) {
															o("throw", e, a, l);
														},
													)
												: t.resolve(d).then(
														function (e) {
															(s.value = e), a(s);
														},
														function (e) {
															return o("throw", e, a, l);
														},
													);
										}
										l(c.arg);
									})(o, r, i, a);
								});
							}
							return (i = i ? i.then(a, a) : a());
						},
					});
				}
				function E(e, t, n) {
					var o = "suspendedStart";
					return function (i, r) {
						if ("executing" === o)
							throw new Error("Generator is already running");
						if ("completed" === o) {
							if ("throw" === i) throw r;
							return j();
						}
						for (n.method = i, n.arg = r; ; ) {
							var a = n.delegate;
							if (a) {
								var l = L(a, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === o) throw ((o = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							o = "executing";
							var c = p(e, t, n);
							if ("normal" === c.type) {
								if (
									((o = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((o = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function L(e, t) {
					var n = t.method,
						o = e.iterator[n];
					if (void 0 === o)
						return (
							(t.delegate = null),
							("throw" === n &&
								e.iterator.return &&
								((t.method = "return"),
								(t.arg = void 0),
								L(e, t),
								"throw" === t.method)) ||
								("return" !== n &&
									((t.method = "throw"),
									(t.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var i = p(o, e.iterator, t.arg);
					if ("throw" === i.type)
						return (
							(t.method = "throw"), (t.arg = i.arg), (t.delegate = null), d
						);
					var r = i.arg;
					return r
						? r.done
							? ((t[e.resultName] = r.value),
								(t.next = e.nextLoc),
								"return" !== t.method &&
									((t.method = "next"), (t.arg = void 0)),
								(t.delegate = null),
								d)
							: r
						: ((t.method = "throw"),
							(t.arg = new TypeError("iterator result is not an object")),
							(t.delegate = null),
							d);
				}
				function k(e) {
					var t = { tryLoc: e[0] };
					1 in e && (t.catchLoc = e[1]),
						2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
						this.tryEntries.push(t);
				}
				function P(e) {
					var t = e.completion || {};
					(t.type = "normal"), delete t.arg, (e.completion = t);
				}
				function O(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(k, this),
						this.reset(!0);
				}
				function _(e) {
					if (e) {
						var t = e[r];
						if (t) return t.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var o = -1,
								i = function t() {
									for (; ++o < e.length; )
										if (n.call(e, o)) return (t.value = e[o]), (t.done = !1), t;
									return (t.value = void 0), (t.done = !0), t;
								};
							return (i.next = i);
						}
					}
					return { next: j };
				}
				function j() {
					return { value: void 0, done: !0 };
				}
				return (
					(h.prototype = f),
					o(g, "constructor", { value: f, configurable: !0 }),
					o(f, "constructor", { value: h, configurable: !0 }),
					(h.displayName = c(f, l, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var t = "function" == typeof e && e.constructor;
						return (
							!!t &&
							(t === h || "GeneratorFunction" === (t.displayName || t.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, f)
								: ((e.__proto__ = f), c(e, l, "GeneratorFunction")),
							(e.prototype = Object.create(g)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					x(A.prototype),
					c(A.prototype, a, function () {
						return this;
					}),
					(e.AsyncIterator = A),
					(e.async = function (t, n, o, i, r) {
						void 0 === r && (r = Promise);
						var a = new A(s(t, n, o, i), r);
						return e.isGeneratorFunction(n)
							? a
							: a.next().then(function (e) {
									return e.done ? e.value : a.next();
								});
					}),
					x(g),
					c(g, l, "Generator"),
					c(g, r, function () {
						return this;
					}),
					c(g, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var t = Object(e),
							n = [];
						for (var o in t) n.push(o);
						return (
							n.reverse(),
							function e() {
								for (; n.length; ) {
									var o = n.pop();
									if (o in t) return (e.value = o), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = _),
					(O.prototype = {
						constructor: O,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(P),
								!e)
							)
								for (var t in this)
									"t" === t.charAt(0) &&
										n.call(this, t) &&
										!isNaN(+t.slice(1)) &&
										(this[t] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var t = this;
							function o(n, o) {
								return (
									(a.type = "throw"),
									(a.arg = e),
									(t.next = n),
									o && ((t.method = "next"), (t.arg = void 0)),
									!!o
								);
							}
							for (var i = this.tryEntries.length - 1; i >= 0; --i) {
								var r = this.tryEntries[i],
									a = r.completion;
								if ("root" === r.tryLoc) return o("end");
								if (r.tryLoc <= this.prev) {
									var l = n.call(r, "catchLoc"),
										c = n.call(r, "finallyLoc");
									if (l && c) {
										if (this.prev < r.catchLoc) return o(r.catchLoc, !0);
										if (this.prev < r.finallyLoc) return o(r.finallyLoc);
									} else if (l) {
										if (this.prev < r.catchLoc) return o(r.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < r.finallyLoc) return o(r.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, t) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var i = this.tryEntries[o];
								if (
									i.tryLoc <= this.prev &&
									n.call(i, "finallyLoc") &&
									this.prev < i.finallyLoc
								) {
									var r = i;
									break;
								}
							}
							r &&
								("break" === e || "continue" === e) &&
								r.tryLoc <= t &&
								t <= r.finallyLoc &&
								(r = null);
							var a = r ? r.completion : {};
							return (
								(a.type = e),
								(a.arg = t),
								r
									? ((this.method = "next"), (this.next = r.finallyLoc), d)
									: this.complete(a)
							);
						},
						complete: function (e, t) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && t && (this.next = t),
								d
							);
						},
						finish: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.finallyLoc === e)
									return this.complete(n.completion, n.afterLoc), P(n), d;
							}
						},
						catch: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.tryLoc === e) {
									var o = n.completion;
									if ("throw" === o.type) {
										var i = o.arg;
										P(n);
									}
									return i;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, t, n) {
							return (
								(this.delegate = { iterator: _(e), resultName: t, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					e
				);
			}
			function v(e, t, n, o, i, r, a) {
				try {
					var l = e[r](a),
						c = l.value;
				} catch (e) {
					return void n(e);
				}
				l.done ? t(c) : Promise.resolve(c).then(o, i);
			}
			function b(e) {
				return function () {
					var t = this,
						n = arguments;
					return new Promise(function (o, i) {
						var r = e.apply(t, n);
						function a(e) {
							v(r, o, i, a, l, "next", e);
						}
						function l(e) {
							v(r, o, i, a, l, "throw", e);
						}
						a(void 0);
					});
				};
			}
			function g(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								i,
								r,
								a,
								l = [],
								c = !0,
								s = !1;
							try {
								if (((r = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = r.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (i = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((a = n.return()), Object(a) !== a)
									)
										return;
								} finally {
									if (s) throw i;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return x(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return x(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function x(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var A = function (e) {
					return e.match(/[^\\\/]+$/g)[0];
				},
				E = function (e, t, n, o) {
					var r = (e = A(e)).split(".").pop().toUpperCase(),
						a = function (e) {
							("Enter" !== e.key && " " !== e.key) || t();
						};
					return o === n
						? i.a.createElement(
								"li",
								{ key: n },
								i.a.createElement(
									"button",
									{
										className: "embedSpinner",
										onClick: t,
										onKeyDown: a,
										style: {
											cursor: "pointer",
											background: "none",
											border: "none",
											padding: 0,
										},
										type: "button",
									},
									"[".concat(r, "] ").concat(e),
									i.a.createElement(l.a, { height: 15, width: 15 }),
								),
							)
						: i.a.createElement(
								"li",
								{ key: n },
								i.a.createElement(
									"button",
									{
										className: "embedSpinner",
										onClick: t,
										onKeyDown: a,
										style: {
											cursor: "pointer",
											background: "none",
											border: "none",
											padding: 0,
										},
										type: "button",
									},
									"[".concat(r, "] ").concat(e),
								),
							);
				},
				L = { embeddedFiles: [], fileAttachmentAnnotations: [] },
				k = function (e) {
					var t = e.initialFiles,
						n = void 0 === t ? L : t,
						l = g(Object(r.a)(), 1)[0],
						y = Object(d.d)(),
						v = g(Object(o.useState)(n), 2),
						x = v[0],
						k = v[1],
						P = Object(d.e)(u.a.getIsMultiTab),
						O = Object(d.e)(u.a.getTabManager),
						_ = g(Object(o.useState)(null), 2),
						j = _[0],
						N = _[1];
					if (
						(Object(o.useEffect)(function () {
							var e = (function () {
								var e = b(
									w().mark(function e() {
										var t;
										return w().wrap(function (e) {
											for (;;)
												switch ((e.prev = e.next)) {
													case 0:
														return (e.next = 2), Object(a.b)();
													case 2:
														(t = e.sent), k(t);
													case 4:
													case "end":
														return e.stop();
												}
										}, e);
									}),
								);
								return function () {
									return e.apply(this, arguments);
								};
							})();
							return (
								p.a.addEventListener("annotationChanged", e),
								p.a.addEventListener("documentLoaded", e),
								e(),
								function () {
									p.a.removeEventListener("annotationChanged", e),
										p.a.removeEventListener("documentLoaded", e);
								}
							);
						}, []),
						0 === x.embeddedFiles.length &&
							0 === Object.entries(x.fileAttachmentAnnotations).length)
					)
						return i.a.createElement(
							"div",
							{ className: "fileAttachmentPanel" },
							i.a.createElement(
								"div",
								{ className: "empty-panel-container" },
								i.a.createElement(s.a, {
									className: "empty-icon",
									glyph: m.c[m.e.FILE_ATTACHMENT].icon,
								}),
								i.a.createElement(
									"div",
									{ className: "empty-message" },
									l("message.noAttachments"),
								),
							),
						);
					var T = (function () {
						var e = b(
							w().mark(function e(t) {
								return w().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												if (!P) {
													e.next = 5;
													break;
												}
												y(h.a.openElement(f.a.LOADING_MODAL)),
													setTimeout(
														b(
															w().mark(function e() {
																var n, o, i;
																return w().wrap(function (e) {
																	for (;;)
																		switch ((e.prev = e.next)) {
																			case 0:
																				return (e.next = 2), t.getFileData();
																			case 2:
																				return (
																					(n = e.sent),
																					(o = A(t.filename)),
																					(e.next = 6),
																					O.addTab(n, { filename: o })
																				);
																			case 6:
																				return (
																					(i = e.sent),
																					y(
																						h.a.closeElement(f.a.LOADING_MODAL),
																					),
																					y(h.a.closeElement(f.a.LEFT_PANEL)),
																					(e.next = 11),
																					O.setActiveTab(i)
																				);
																			case 11:
																			case "end":
																				return e.stop();
																		}
																}, e);
															}),
														),
														100,
													),
													(e.next = 6);
												break;
											case 5:
												return e.abrupt(
													"return",
													p.a
														.getAnnotationManager()
														.trigger("annotationDoubleClicked", t),
												);
											case 6:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function (t) {
							return e.apply(this, arguments);
						};
					})();
					return i.a.createElement(
						"div",
						{ className: "fileAttachmentPanel" },
						i.a.createElement(
							"div",
							{ className: "section" },
							x.embeddedFiles.length
								? i.a.createElement(
										"h2",
										{ className: "title" },
										l("message.embeddedFiles"),
									)
								: null,
							i.a.createElement(
								"ul",
								{ className: "downloadable" },
								x.embeddedFiles.map(function (e, t) {
									return E(
										A(e.filename),
										function () {
											N("embeddedFile_".concat(t)),
												Object(a.a)(e.fileObject)
													.then(function (t) {
														Object(c.saveAs)(t, A(e.filename));
													})
													.finally(function () {
														N(null);
													});
										},
										"embeddedFile_".concat(t),
										j,
									);
								}),
							),
						),
						Object.entries(x.fileAttachmentAnnotations).map(function (e) {
							var t = g(e, 2),
								n = t[0],
								o = t[1];
							return i.a.createElement(
								"div",
								{ key: n, className: "section" },
								i.a.createElement(
									"h2",
									{ className: "title" },
									l("message.pageNum"),
									" ",
									n,
								),
								i.a.createElement(
									"ul",
									{ className: "downloadable" },
									o.map(function (e, t) {
										return E(
											A(e.filename),
											b(
												w().mark(function t() {
													return w().wrap(function (t) {
														for (;;)
															switch ((t.prev = t.next)) {
																case 0:
																	return (
																		p.a.setCurrentPage(e.PageNumber),
																		p.a.selectAnnotation(e),
																		(t.next = 4),
																		T(e)
																	);
																case 4:
																case "end":
																	return t.stop();
															}
													}, t);
												}),
											),
											"fileAttachmentAnnotation_".concat(t),
										);
									}),
								),
							);
						}),
					);
				};
			t.default = k;
		},
	},
]);
//# sourceMappingURL=chunk.48.js.map
