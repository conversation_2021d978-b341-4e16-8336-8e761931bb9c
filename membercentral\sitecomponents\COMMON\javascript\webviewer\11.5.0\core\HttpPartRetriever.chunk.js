/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[0],
		{
			624: function (ya, va, r) {
				r.r(va);
				r.d(va, "ByteRangeRequest", function () {
					return w;
				});
				var la = r(0),
					na = r(1);
				r.n(na);
				var ma = r(2),
					ja = r(220);
				ya = r(129);
				var ia = r(367),
					ca = r(112),
					y = r(107),
					x = r(366),
					n = r(246);
				r = r(547);
				var h = [],
					b = [],
					e = window,
					f = (function () {
						return function () {
							this.$q = 1;
						};
					})(),
					a;
				(function (aa) {
					aa[(aa.UNSENT = 0)] = "UNSENT";
					aa[(aa.DONE = 4)] = "DONE";
				})(a || (a = {}));
				var w = (function () {
						function aa(ea, ba, fa, ha) {
							var pa = this;
							this.url = ea;
							this.range = ba;
							this.Sf = fa;
							this.withCredentials = ha;
							this.Zna = a;
							this.request = new XMLHttpRequest();
							this.request.open("GET", this.url, !0);
							e.Uint8Array && (this.request.responseType = "arraybuffer");
							ha && (this.request.withCredentials = ha);
							z.DISABLE_RANGE_HEADER ||
								(Object(na.isUndefined)(ba.stop)
									? this.request.setRequestHeader(
											"Range",
											"bytes=".concat(ba.start),
										)
									: this.request.setRequestHeader(
											"Range",
											["bytes=", ba.start, "-", ba.stop - 1].join(""),
										));
							fa &&
								Object.keys(fa).forEach(function (oa) {
									pa.request.setRequestHeader(oa, fa[oa]);
								});
							this.request.overrideMimeType
								? this.request.overrideMimeType(
										"text/plain; charset=x-user-defined",
									)
								: this.request.setRequestHeader(
										"Accept-Charset",
										"x-user-defined",
									);
							this.status = x.a.NOT_STARTED;
						}
						aa.prototype.start = function (ea) {
							var ba = this,
								fa = this.request;
							fa.onreadystatechange = function () {
								if (ba.aborted)
									return (ba.status = x.a.ABORTED), ea({ code: x.a.ABORTED });
								if (this.readyState === ba.Zna.DONE) {
									ba.oM();
									var ha = 0 === window.document.URL.indexOf("file:///");
									200 === fa.status ||
									206 === fa.status ||
									(ha && 0 === fa.status)
										? ((ha = e.raa(this)), ba.O0(ha, ea))
										: ((ba.status = x.a.ERROR),
											ea({ code: ba.status, status: ba.status }));
								}
							};
							this.request.send(null);
							this.status = x.a.STARTED;
						};
						aa.prototype.O0 = function (ea, ba) {
							this.status = x.a.SUCCESS;
							if (ba) return ba(!1, ea);
						};
						aa.prototype.abort = function () {
							this.oM();
							this.aborted = !0;
							this.request.abort();
						};
						aa.prototype.oM = function () {
							var ea = Object(n.c)(this.url, this.range, b);
							-1 !== ea && b.splice(ea, 1);
							if (0 < h.length) {
								ea = h.shift();
								var ba = new aa(
									ea.url,
									ea.range,
									this.Sf,
									this.withCredentials,
								);
								ea.request = ba;
								b.push(ea);
								ba.start(Object(n.d)(ea));
							}
						};
						aa.prototype.extend = function (ea) {
							var ba = Object.assign({}, this, ea.prototype);
							ba.constructor = ea;
							return ba;
						};
						return aa;
					})(),
					z = (function (aa) {
						function ea(ba, fa, ha, pa, oa) {
							ha = aa.call(this, ba, ha, pa) || this;
							ha.Om = {};
							ha.pK = fa;
							ha.url = ba;
							ha.DISABLE_RANGE_HEADER = !1;
							ha.CG = w;
							ha.A2 = 3;
							ha.Sf = oa || {};
							return ha;
						}
						Object(la.c)(ea, aa);
						ea.prototype.DD = function (ba, fa, ha) {
							var pa = -1 === ba.indexOf("?") ? "?" : "&";
							switch (ha) {
								case !1:
								case y.a.NEVER_CACHE:
									ba = "".concat(ba + pa, "_=").concat(Object(na.uniqueId)());
									break;
								case !0:
								case y.a.CACHE:
									ba = ""
										.concat(ba + pa, "_=")
										.concat(fa.start, ",")
										.concat(Object(na.isUndefined)(fa.stop) ? "" : fa.stop);
							}
							return ba;
						};
						ea.prototype.d8 = function (ba, fa, ha, pa) {
							void 0 === ha && (ha = {});
							return new this.CG(ba, fa, ha, pa);
						};
						ea.prototype.Cza = function (ba, fa, ha, pa, oa) {
							for (var ka = 0; ka < h.length; ka++)
								if (
									Object(na.isEqual)(h[ka].range, fa) &&
									Object(na.isEqual)(h[ka].url, ba)
								)
									return h[ka].rj.push(pa), h[ka].QN++, null;
							for (ka = 0; ka < b.length; ka++)
								if (
									Object(na.isEqual)(b[ka].range, fa) &&
									Object(na.isEqual)(b[ka].url, ba)
								)
									return b[ka].rj.push(pa), b[ka].QN++, null;
							ha = { url: ba, range: fa, pK: ha, rj: [pa], QN: 1 };
							if (0 === h.length && b.length < this.A2)
								return (
									b.push(ha),
									(ha.request = this.d8(ba, fa, oa, this.withCredentials)),
									ha
								);
							h.push(ha);
							return null;
						};
						ea.prototype.Dt = function (ba, fa, ha) {
							var pa = this.DD(ba, fa, this.pK);
							(ba = this.Cza(pa, fa, this.pK, ha, this.Sf)) &&
								ba.request.start(Object(n.d)(ba));
							return function () {
								var oa = Object(n.c)(pa, fa, b);
								if (-1 !== oa) {
									var ka = --b[oa].QN;
									0 === ka && b[oa].request && b[oa].request.abort();
								} else
									(oa = Object(n.c)(pa, fa, h)),
										-1 !== oa &&
											((ka = --h[oa].QN), 0 === ka && h.splice(oa, 1));
							};
						};
						ea.prototype.E$ = function () {
							return { start: -ja.a };
						};
						ea.prototype.bFa = function () {
							var ba = -(ja.a + ja.e);
							return { start: ba - ja.d, end: ba };
						};
						ea.prototype.yA = function (ba) {
							var fa = this;
							this.AK = !0;
							var ha = ja.a;
							this.Dt(this.url, this.E$(), function (pa, oa, ka) {
								function ra() {
									var qa = fa.$e.A$();
									fa.Dt(fa.url, qa, function (sa, ta) {
										if (sa)
											return (
												Object(ma.i)(
													"Error loading central directory: ".concat(sa),
												),
												ba(sa)
											);
										ta = Object(ca.a)(ta);
										if (ta.length !== qa.stop - qa.start)
											return ba(
												"Invalid XOD file: Zip central directory data is wrong size! Should be "
													.concat(qa.stop - qa.start, " but is ")
													.concat(ta.length),
											);
										fa.$e.Cfa(ta);
										fa.vT = !0;
										fa.AK = !1;
										return ba(!1);
									});
								}
								if (pa)
									return (
										Object(ma.i)("Error loading end header: ".concat(pa)),
										ba(pa, oa, ka)
									);
								oa = Object(ca.a)(oa);
								if (oa.length !== ha)
									return ba(
										"Invalid XOD file: Zip end header data is wrong size!",
									);
								try {
									fa.$e = new ia.a(oa);
								} catch (qa) {
									return ba(qa);
								}
								fa.$e.BIa
									? fa.Dt(fa.url, fa.bFa(), function (qa, sa) {
											if (qa)
												return (
													Object(ma.i)(
														"Error loading zip64 header: ".concat(qa),
													),
													ba(qa)
												);
											sa = Object(ca.a)(sa);
											fa.$e.gJa(sa);
											ra();
										})
									: ra();
							});
						};
						ea.prototype.jaa = function (ba) {
							ba(Object.keys(this.$e.Rs));
						};
						ea.prototype.VZ = function (ba, fa) {
							var ha = this;
							if (this.$e.I7(ba)) {
								var pa = this.$e.vE(ba);
								if (pa in this.Om) {
									var oa = this.Uj[ba];
									oa.Py = this.Om[pa];
									oa.Py.$q++;
									oa.cancel = oa.Py.cancel;
								} else {
									var ka = this.$e.kCa(ba),
										ra = this.Dt(this.url, ka, function (sa, ta) {
											sa
												? (Object(ma.i)(
														'Error loading part "'.concat(ba, '": ').concat(sa),
													),
													ha.Dt(ha.url, ka, function (wa, za) {
														if (wa) return fa(wa, ba);
														ha.Gfa(za, ka, pa, ba, fa);
													}))
												: ha.Gfa(ta, ka, pa, ba, fa);
										}),
										qa = this.Uj[ba];
									qa &&
										((qa.aja = !0),
										(qa.cancel = function () {
											qa.Py.$q--;
											0 === qa.Py.$q && (ra(), delete ha.Om[pa]);
										}),
										(this.Om[pa] = new f(pa)),
										(qa.Py = this.Om[pa]),
										(qa.Py.cancel = qa.cancel));
								}
							} else
								delete this.Uj[ba],
									fa(Error('File not found: "'.concat(ba, '"')), ba);
						};
						ea.prototype.Gfa = function (ba, fa, ha, pa, oa) {
							if (ba.length !== fa.stop - fa.start)
								oa(Error("Part data is wrong size!"), pa);
							else {
								do {
									if (!this.Om[ha]) return;
									pa = this.Om[ha].$q;
									for (var ka = fa.Vw.length, ra = 0; ra < ka; ++ra) {
										var qa = fa.Vw[ra];
										oa(
											!1,
											qa.Qw,
											ba["string" === typeof ba ? "substring" : "subarray"](
												qa.start,
												qa.stop,
											),
											this.$e.Uba(qa.Qw),
										);
										qa.Qw in this.Uj && delete this.Uj[qa.Qw];
									}
								} while (pa !== this.Om[ha].$q);
								delete this.Om[ha];
							}
						};
						ea.DISABLE_RANGE_HEADER = !1;
						ea.A2 = 3;
						return ea;
					})(ya.a);
				(function (aa) {
					function ea(ba, fa, ha) {
						var pa = aa.call(this) || this,
							oa;
						for (oa in ba) pa[oa] = ba[oa];
						pa.G_a = ba;
						pa.startOffset = fa;
						pa.endOffset = ha;
						pa.d8 = function (ka, ra, qa, sa) {
							Object(na.isUndefined)(ra.stop)
								? ((ra.start += pa.endOffset), (ra.stop = pa.endOffset))
								: ((ra.start += pa.startOffset), (ra.stop += pa.startOffset));
							ka = pa.DD(pa.url, ra, pa.pK);
							return new ba.CG(ka, ra, qa, sa);
						};
						return pa;
					}
					Object(la.c)(ea, aa);
					return ea;
				})(z);
				Object(r.a)(z);
				Object(r.b)(z);
				va["default"] = z;
			},
		},
	]);
}).call(this || window);
