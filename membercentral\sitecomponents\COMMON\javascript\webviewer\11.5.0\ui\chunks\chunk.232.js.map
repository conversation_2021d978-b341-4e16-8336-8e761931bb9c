{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/tzm-latn.js"], "names": ["module", "exports", "a", "n", "default", "s", "i", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAmL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,WAAWC,SAAS,kDAAkDC,MAAM,KAAKC,OAAO,wFAAwFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,kDAAkDH,MAAM,KAAKI,YAAY,wFAAwFJ,MAAM,KAAKK,YAAY,kDAAkDL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,0BAA0BC,aAAa,CAACC,OAAO,iBAAiBC,KAAK,SAASpB,EAAE,OAAOqB,EAAE,QAAQC,GAAG,WAAWC,EAAE,OAAOC,GAAG,cAAcC,EAAE,MAAMC,GAAG,WAAWC,EAAE,QAAQC,GAAG,YAAYC,EAAE,QAAQC,GAAG,cAAc,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAA9lCD,CAAE,EAAQ", "file": "chunks/chunk.232.js", "sourcesContent": ["!function(a,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(a=\"undefined\"!=typeof globalThis?globalThis:a||self).dayjs_locale_tzm_latn=s(a.dayjs)}(this,(function(a){\"use strict\";function s(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var n=s(a),i={name:\"tzm-latn\",weekdays:\"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas\".split(\"_\"),months:\"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir\".split(\"_\"),weekStart:6,weekdaysShort:\"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas\".split(\"_\"),monthsShort:\"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir\".split(\"_\"),weekdaysMin:\"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd D MMMM YYYY HH:mm\"},relativeTime:{future:\"dadkh s yan %s\",past:\"yan %s\",s:\"imik\",m:\"minuḍ\",mm:\"%d minuḍ\",h:\"saɛa\",hh:\"%d tassaɛin\",d:\"ass\",dd:\"%d ossan\",M:\"ayowr\",MM:\"%d iyyirn\",y:\"asgas\",yy:\"%d isgasn\"}};return n.default.locale(i,null,!0),i}));"], "sourceRoot": ""}