(window.webpackJsonp = window.webpackJsonp || []).push([
	[214],
	{
		1472: function (e, t, _) {
			e.exports = (function (e) {
				"use strict";
				var t = (function (e) {
						return e && "object" == typeof e && "default" in e
							? e
							: { default: e };
					})(e),
					_ = {
						name: "sl",
						weekdays:
							"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),
						months:
							"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split(
								"_",
							),
						weekStart: 1,
						weekdaysShort: "ned._pon._tor._sre._čet._pet._sob.".split("_"),
						monthsShort:
							"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split(
								"_",
							),
						weekdaysMin: "ne_po_to_sr_če_pe_so".split("_"),
						ordinal: function (e) {
							return e + ".";
						},
						formats: {
							LT: "H:mm",
							LTS: "H:mm:ss",
							L: "DD.MM.YYYY",
							LL: "D. MMMM YYYY",
							LLL: "D. MMMM YYYY H:mm",
							LLLL: "dddd, D. MMMM YYYY H:mm",
						},
						relativeTime: {
							future: "čez %s",
							past: "pred %s",
							s: "nekaj sekund",
							m: "minuta",
							mm: "%d minut",
							h: "ura",
							hh: "%d ur",
							d: "dan",
							dd: "%d dni",
							M: "mesec",
							MM: "%d mesecev",
							y: "leto",
							yy: "%d let",
						},
					};
				return t.default.locale(_, null, !0), _;
			})(_(105));
		},
	},
]);
//# sourceMappingURL=chunk.214.js.map
