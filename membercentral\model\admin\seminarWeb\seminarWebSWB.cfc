<cfcomponent output="no">
	<cffunction name="createSWBProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="bundleName" type="string" required="true">
		<cfargument name="swFormat" type="string" required="true">

		<cfset var local = structnew()>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cfif local.tmpRights.addBundle>
			<cfset local.isSWOD = arguments.swFormat eq "SWOD">

			<cfstoredproc procedure="sw_addBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.isSWOD#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.bundleName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="I">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="$0.00">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.bundleID">
			</cfstoredproc>

			<cfset local.returnStruct.bundleID = local.bundleID>
			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSWBFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			getSWBFilter();
			local.SWBFilter = application.mcCacheManager.sessionGetValue(keyname='SWBFilter',defaultValue={});
			local.SWBFilter_hash = hash(serializeJSON(local.SWBFilter), "SHA", "UTF-8");
			
			local.SWBFilter.listFilter.fActivatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.SWBFilter.listFilter.fActivatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.SWBFilter.listFilter.fKeyword = arguments.event.getValue('fKeyword','');
			local.SWBFilter.listFilter.fProgramCode = arguments.event.getValue('fProgramCode','');
			local.SWBFilter.listFilter.fPubType = arguments.event.getValue('fPubType','PO');
			local.SWBFilter.listFilter.fStatus = arguments.event.getValue('fStatus',1);
			local.SWBFilter.listFilter.fFeaturedOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.SWBFilter.listFilter.fIsSWOD = arguments.event.getValue('fIsSWOD','');
			local.SWBFilter.listFilter.fbundleID = arguments.event.getValue('fbundleID','');
			local.SWBFilter.listFilter.fSyndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);

			if (local.SWBFilter_hash NEQ hash(serializeJSON(local.SWBFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWBFilter', value=local.SWBFilter);
			
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSWBFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			
			local.tmpStr = { fActivatedDateFrom='', fActivatedDateTo='', fKeyword='', fProgramCode='', fPubType='PO', fStatus=1, fFeaturedOnly=0, fIsSWOD='', fbundleID='', fSyndicatedOnly=0};

			local.SWBFilter = application.mcCacheManager.sessionGetValue(keyname='SWBFilter',defaultValue={});
			local.SWBFilter_hash = hash(serializeJSON(local.SWBFilter), "SHA", "UTF-8");

			if (NOT structKeyExists(local.SWBFilter,"listFilter"))
				local.SWBFilter.listFilter = duplicate(local.tmpStr);
			
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.SWBFilter.listFilter, local.thiskey))
					structInsert(local.SWBFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			}

			if (local.SWBFilter_hash NEQ hash(serializeJSON(local.SWBFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWBFilter', value=local.SWBFilter);
		</cfscript>

		<cfreturn local.SWBFilter>
	</cffunction>

	<cffunction name="getPrograms" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="keyword" type="string" required="true">
		<cfargument name="programCode" type="string" required="true">
		<cfargument name="publisherType" type="string" required="true">
		<cfargument name="hideInactive" type="boolean" required="true">
		<cfargument name="featuredOnly" type="boolean" required="false" default="0">
		<cfargument name="fIsSWOD" type="string" required="false" default="">
		<cfargument name="fbundleID" type="string" required="false" default="">
		<cfargument name="activatedDateFrom" type="string" required="false" default="">
		<cfargument name="activatedDateTo" type="string" required="false" default="">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="pid" type="numeric" required="false" default="0">
		<cfargument name="fPublisher" type="string" required="false" default="">
		<cfargument name="publisherIDList" type="string" required="false" default="">	
		<cfargument name="syndicatedOnly" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		
		<cfset local.arrCols = arrayNew(1)>		
		<cfset arrayAppend(local.arrCols,"tmp.bundleName")>
		<cfif arguments.mode eq 'copyRatesGrid'>
			<cfset arrayAppend(local.arrCols,"tmp.bundleName")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"tmp.dateActivated")>
		<cfif arguments.mode eq "swSearchGrid">
			<cfset arrayAppend(local.arrCols,"tmp.publisherOrgCode")>
		</cfif>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfquery name="local.qryBundles" datasource="#application.dsn.tlasites_seminarweb.dsn#" result="local.qryBundlesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- create temp table to store all bundles in catalog
				DECLARE @tmpTable TABLE (autoID int IDENTITY(1,1), bundleID int, isSWOD bit, bundleName varchar(200), 
					bundleSubTitle varchar(200), publisherOrgCode varchar(10), isFeatured bit, publisherParticipantID int, 
					isActive bit, dateActivated date, allowSyndication bit, lockSettings bit, row int);

				declare @participantID int, @pn varchar(200), @rc varchar(15), @maxrows int, @startRow int, 
					@siteCode varchar(10), @totalCount int, @posStart int, @posStartAndCount int, @asd date, @aed date;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sitecode#">;
				select @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				SET @pn = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.keyword#">;
				SET @rc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.programCode#">;
				<cfif len(arguments.activatedDateFrom)>
					SET @asd = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.activatedDateFrom#">;
				</cfif>
				<cfif len(arguments.activatedDateTo)>
					SET @aed = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.activatedDateTo#">;
				</cfif>

				INSERT INTO @tmpTable (bundleID, isSWOD, bundleName, bundleSubTitle, publisherOrgCode, publisherParticipantID, isActive, dateActivated, isFeatured, allowSyndication, lockSettings, row)
				SELECT tmp.bundleID, tmp.isSWOD, tmp.bundleName, tmp.bundleSubTitle, tmp.publisherOrgCode, tmp.publisherParticipantID, tmp.isActive, tmp.dateActivated,
					case when fp.featuredID is not null then 1 else 0 end as isFeatured, tmp.allowSyndication, tmp.lockSettings, 
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
				FROM (
					<cfif arguments.mode EQ "swSearchGrid">
						SELECT b.bundleID, b.programCode, b.isSWOD, b.bundleName, b.bundleSubTitle, p.participantID, p.orgcode AS publisherOrgCode, p.participantID AS publisherParticipantID, 
							CASE WHEN b.status = 'A' THEN 1 ELSE 0 END AS isActive, b.dateActivated, b.allowSyndication, b.lockSettings
						FROM dbo.tblBundles as b
						INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
						<cfif arguments.hideInactive is 1>
							WHERE b.status = 'A'
						</cfif>
					<cfelse>
						SELECT b.bundleID, b.programCode, b.isSWOD, b.bundleName, b.bundleSubTitle, p.participantID, p.orgcode AS publisherOrgCode, p.participantID AS publisherParticipantID, 
							CASE WHEN b.status = 'A' THEN 1 ELSE 0 END AS isActive, b.dateActivated, b.allowSyndication, b.lockSettings
						FROM dbo.tblBundles as b
						INNER JOIN dbo.tblParticipants as p on p.participantID = b.participantID
						WHERE p.participantID = @participantID
						<cfif arguments.hideInactive is 1>
							AND b.status = 'A'
						</cfif>
							UNION
						SELECT b.bundleID, b.programCode, b.isSWOD, b.bundleName, b.bundleSubTitle, p2.participantID, p2.orgcode as publisherOrgCode, p.participantID as publisherParticipantID, 
							CASE WHEN b.status = 'A' THEN 1 ELSE 0 END AS isActive, b.dateActivated, b.allowSyndication, b.lockSettings
						FROM dbo.tblParticipants AS p 
						INNER JOIN dbo.tblBundlesOptIn AS boi ON p.participantID = boi.participantID AND boi.isActive = 1
						INNER JOIN dbo.tblBundles AS b ON boi.bundleID = b.bundleID 
						INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = b.participantID
						WHERE p.participantID = @participantID
						<cfif arguments.hideInactive is 1>
							AND b.status = 'A'
						</cfif>
					</cfif>
				) as tmp
				<cfif listLen(arguments.publisherIDList)>
					inner join memberCentral.dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publisherIDList#">,',') as tmpP on tmpP.listitem = tmp.participantID
				</cfif>
				left outer join dbo.tblFeaturedPrograms as fp on fp.participantID = @participantID
					and fp.bundleID = tmp.bundleID
				where 1 = 1
				<cfif len(arguments.keyword)>
					and tmp.bundleName + isnull(' ' + tmp.bundleSubTitle,'') LIKE '%' + @pn + '%'
				</cfif>
				<cfif len(arguments.programCode)>
					and tmp.programCode = @rc
				</cfif>
				<cfif len(arguments.activatedDateFrom) and len(arguments.activatedDateTo)>
					and tmp.dateActivated between @asd and @aed 
				<cfelseif len(arguments.activatedDateFrom)>
					and tmp.dateActivated >= @asd
				<cfelseif len(arguments.activatedDateTo)>
					and tmp.dateActivated <= @aed
				</cfif>
				<cfif arguments.mode EQ "copyRatesGrid">
					and tmp.bundleID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pid#">
				</cfif>
				<cfif arguments.publisherType EQ 'P'>
					and tmp.publisherOrgCode = @siteCode
				<cfelseif arguments.publisherType EQ 'O'>
					and tmp.publisherOrgCode <> @siteCode
				</cfif>
				<cfif arguments.featuredOnly eq 1>
					and fp.featuredID is not null
				</cfif>
				<cfif arguments.mode EQ "swSearchGrid" and len(arguments.fPublisher)>
					and tmp.publisherParticipantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fPublisher#">
				</cfif>
				<cfif len(arguments.fbundleID)>
					and tmp.bundleID = <cfqueryparam cfsqltype="cf_sql_integer" value="#int(val(arguments.fbundleID))#">
				</cfif>
				<cfif len(arguments.fIsSWOD)>
					and tmp.isSWOD = <cfqueryparam cfsqltype="cf_sql_bit" value="#val(arguments.fIsSWOD)#">
				</cfif>
				<cfif arguments.syndicatedOnly eq 1>
					and tmp.allowSyndication = 1
					and tmp.publisherOrgCode = @siteCode
				</cfif>;

				SET @totalCount = @@ROWCOUNT;
				
				<cfif arguments.mode EQ "export">
					IF OBJECT_ID('tempdb..##tmpSWB') IS NOT NULL
		      			DROP TABLE ##tmpSWB;
					
					SELECT 'SWB-' + cast(tmp.bundleID as varchar(10)) as [Bundle ID], 
						tmp.bundleName as [Bundle], tmp.bundleSubTitle as [Bundle SubTitle],
						case when tmp.publisherOrgCode = @siteCode then 'Publisher' else 'Opted-In' end as [Opt-In], 						
						case when tmp.isActive = 1 then 'Active' else 'Inactive' end as [Status],
						case when tmp.isSWOD = 1 then 'SWOD' else 'SWL' end as [Bundle Type],
						tmp.dateActivated as [Date Activated]
					INTO ##tmpSWB
					FROM @tmpTable as tmp
					ORDER BY tmp.row;
					
					DECLARE @selectsql varchar(max) = 'SELECT [Bundle ID], [Bundle], [Bundle SubTitle], [Bundle Type], [Date Activated], [Opt-In], [Status], [Registrants], 
						ROW_NUMBER() OVER(order by Program) as mcCSVorder 
						*FROM* ##tmpSWB';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#arguments.folderPathUNC#/#arguments.reportFileName#",'\')#', @returnColumns=0;
					
					IF OBJECT_ID('tempdb..##tmpSWB') IS NOT NULL
						DROP TABLE ##tmpSWB;
				<cfelseif listFindNoCase("grid,copyRatesGrid,swSearchGrid", arguments.mode)>
					DECLARE @tmpBundleEnrollments TABLE (bundleID int PRIMARY KEY, enrolledCount int);

					INSERT INTO @tmpBundleEnrollments (bundleID, enrolledCount)
					SELECT b.bundleID, COUNT(bo.orderID)
					FROM dbo.tblBundleOrders AS bo
					INNER JOIN dbo.tblBundles AS b ON b.bundleID = bo.bundleID
					INNER JOIN @tmpTable AS tmp ON tmp.bundleID = b.bundleID
					WHERE bo.isActive = 1
					<cfif Not listFindNoCase("swSearchGrid", arguments.mode)>
						AND (b.participantID = @participantID OR bo.participantID = @participantID)
					</cfif>
					GROUP BY b.bundleID;
					
					SELECT tmp.bundleID, tmp.bundleName, tmp.bundleSubTitle, tmp.publisherOrgCode, isFeatured, tmp.isActive, tmp.dateActivated,
						case when tmp.isSWOD = 1 then 'SWOD' else 'SWL' end as bundleType, be.enrolledCount, tmp.allowSyndication, tmp.lockSettings,
						@totalCount as totalCount
					FROM @tmpTable as tmp
					LEFT OUTER JOIN @tmpBundleEnrollments AS be ON be.bundleID = tmp.bundleID
					WHERE tmp.row > @posStart AND tmp.row <= @posStartAndCount
					ORDER BY tmp.row;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif listFindNoCase("grid,copyRatesGrid,swSearchGrid", arguments.mode)>
			<cfreturn local.qryBundles>
		</cfif>
	</cffunction>

	<cffunction name="getEnrollmentCount" access="public" output="false" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">

		<cfset var qryEnrollmentCount = "">

		<cfquery name="qryEnrollmentCount" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @bundleID int, @participantID int;

			SET @bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
			SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			SELECT count(bo.orderID) as count
			FROM dbo.tblBundleOrders AS bo
			INNER JOIN dbo.tblBundles AS b ON b.bundleID = bo.bundleID 
			INNER JOIN dbo.tblUsers AS u ON bo.userID = u.userID 
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
			WHERE bo.isActive = 1
			AND (b.participantID = @participantID OR bo.participantID = @participantID)
			AND b.bundleID = @bundleID
			AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
		</cfquery>
		
		<cfreturn qryEnrollmentCount>
	</cffunction>

	<cffunction name="copySWBProgram" access="public" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="incDetails" type="boolean" required="true">
		<cfargument name="incRates" type="boolean" required="true">
		<cfargument name="incSeminars" type="boolean" required="true">
	
		<cfset var newBundleID = 0>

		<cfstoredproc procedure="sw_copyBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incDetails#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incRates#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSeminars#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="newBundleID">
		</cfstoredproc>

		<cfreturn newBundleID>
	</cffunction>

	<cffunction name="getRegistrants" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="true">
		<cfargument name="rDateFrom" type="string" required="true">
		<cfargument name="rDateTo" type="string" required="true">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		
		<cfset var local = StructNew()>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"m2.LastName + ', ' + m2.FirstName")>
		<cfset arrayAppend(local.arrCols,"tmp.dateOfOrder")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>
		
		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @maxrows int, @startRow int, @posStart int, @posStartAndCount int, @siteCode varchar(10), @bundleID int, @totalCount int, 
					@rDateFrom datetime, @rDateTo datetime, @siteID int, @orgID int, @participantID int;

				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL
					DROP TABLE ##tmpEnrollments2;
				CREATE TABLE ##tmpEnrollments (orderID int PRIMARY KEY, bundleID int, MCMemberID int, depoMemberDataID int INDEX IX_tmpEnrollments_depomemberdataID, 
					dateOfOrder datetime, amountBilled decimal(14,2), amountDue decimal(14,2), handlesOwnPayment bit, signUpOrgCode varchar(10));
				CREATE TABLE ##tmpEnrollments2 (autoID int IDENTITY(1,1), depoMemberDataID int, orderID int PRIMARY KEY, bundleID int, MCMemberID int, firstName varchar(100), 
					lastName varchar(100), memberNumber varchar(50), company varchar(200), seminarCount int, dateOfOrder datetime, amountBilled decimal(14,2), amountDue decimal(14,2),
					handlesOwnPayment bit, signUpOrgCode varchar(10), row int);

				SET @siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sitecode#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				SET @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				SELECT @siteID = siteID, @orgID = orgID from membercentral.dbo.sites where siteCode = @siteCode;
				<cfif arguments.rDateFrom NEQ ''>
					SET @rDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.rDateFrom#">;
				</cfif>
				<cfif arguments.rDateTo NEQ ''>
					SET @rDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rDateTo# 23:59:59.997">;
				</cfif>
				SET @bundleID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.bundleID#">;

				INSERT INTO ##tmpEnrollments (orderID, bundleID, MCMemberID, depoMemberDataID, dateOfOrder, amountBilled, amountDue, handlesOwnPayment, signUpOrgCode)
				select bo.orderID, b.bundleID, bo.MCMemberID, d.depomemberdataID, bo.dateOfOrder,
					case when bo.handlesOwnPayment = 0 
						then isnull((select sum(dt.amountBilled + dt.salesTaxAmount) 
								from trialsmith.dbo.depoTransactionsApplications as dta 
								inner join trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
								where dta.itemID = bo.orderID
								and dta.itemType = 'SWBO'),0)
						else regFee.totalRegFee end as amountBilled,
					CASE WHEN bo.handlesOwnPayment = 1 THEN regFee.totalRegFee-regFee.regFeePaid ELSE 0 END as amountDue,
					bo.handlesOwnPayment, p.orgcode
				from seminarweb.dbo.tblBundleOrders as bo
				inner join seminarweb.dbo.tblBundles as b on b.bundleID = bo.bundleID
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = bo.participantID
				inner join seminarweb.dbo.tblUsers as u on u.userID = bo.userID
				inner join trialsmith.dbo.depomemberdata as d on d.depomemberdataID = u.depoMemberDataID
				outer apply memberCentral.dbo.fn_sw_totalRegFeeAndPaid(bo.orderID,'SWB') as regFee
				WHERE bo.bundleID = @bundleID
				AND (b.participantID = @participantID OR bo.participantID = @participantID)
				AND bo.isActive = 1
				<cfif arguments.rDateFrom NEQ '' and arguments.rDateTo NEQ ''>
					AND bo.dateOfOrder BETWEEN @rDateFrom AND @rDateTo
				<cfelseif arguments.rDateFrom NEQ ''>
					AND bo.dateOfOrder >= @rDateFrom
				<cfelseif arguments.rDateTo NEQ ''>
					AND bo.dateOfOrder <= @rDateTo
				</cfif>;

				INSERT INTO ##tmpEnrollments2 (depomemberdataID, orderID, bundleID, MCMemberID, firstName, lastName, memberNumber, company, 
					seminarCount, dateOfOrder, amountBilled, amountDue, handlesOwnPayment, signUpOrgCode, row)
				SELECT tmp.depomemberdataid, tmp.orderID, tmp.bundleID, m2.memberID, m2.FirstName, m2.LastName, m2.membernumber, m2.Company, 
					count(distinct e.seminarID) as seminarCount, tmp.dateOfOrder, tmp.amountBilled, tmp.amountDue, tmp.handlesOwnPayment, tmp.signUpOrgCode,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
				FROM ##tmpEnrollments AS tmp
				INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = tmp.MCMemberID
				INNER JOIN memberCentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				LEFT OUTER JOIN seminarweb.dbo.tblEnrollments as e on e.bundleOrderID = tmp.orderID
				GROUP BY tmp.depomemberdataid, tmp.orderID, tmp.bundleID, m2.memberID, m2.FirstName, m2.LastName, m2.membernumber, 
					m2.Company, tmp.dateOfOrder, tmp.amountBilled, tmp.amountDue, tmp.handlesOwnPayment, tmp.signUpOrgCode

				SET @totalCount = @@ROWCOUNT;

				SELECT tmp.depomemberdataID, tmp.orderID, tmp.bundleID, tmp.firstName, tmp.lastName, tmp.memberNumber, tmp.company,
					tmp.seminarCount, tmp.dateOfOrder,tmp.amountBilled, tmp.amountDue, tmp.handlesOwnPayment, tmp.signUpOrgCode,
					m.memberID, @totalCount as totalCount
				FROM ##tmpEnrollments2 as tmp
				LEFT OUTER JOIN memberCentral.dbo.ams_members as m on m.orgID = @orgID AND m.memberID = tmp.MCMemberID
				WHERE tmp.row > @posStart AND tmp.row <= @posStartAndCount
				ORDER BY tmp.row;

				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL
					DROP TABLE ##tmpEnrollments2;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryEnrollments>
	</cffunction>

	<cffunction name="saveBundleRateOptions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, err='' }>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="Edit", checkLockSettings=true)>
				<cfthrow message="You do not have rights to this section.">
			</cfif>

			<cfstoredproc procedure="swb_updateBundlePricing" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
			
			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.err = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getBundleRatesByBundleID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="true">

		<cfset var qryRates = "">

		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getBundleRatesByBundleID">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
			<cfprocresult name="qryRates">
		</cfstoredproc>

		<cfreturn qryRates>
	</cffunction>

	<cffunction name="getBundleRateByRateID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		
		<cfset var qryRate = "">
		
		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getBundleRateByRateIDForAdmin">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			<cfprocresult name="qryRate">
		</cfstoredproc>

		<cfreturn qryRate>
	</cffunction>

	<cffunction name="saveBundleRate" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">
		<cfargument name="rate" type="string" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">

		<cfset var qrySaveSWRate = "">

		<cfquery name="qrySaveSWRate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @rateID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">,0),
					@participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">,
					@bundleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">,
					@rateGroupingID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">,0),
					@rateName varchar(100) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateName#">,
					@rate money = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(replace(arguments.rate,',','','ALL'),"0.00")#">,
					@isHidden bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isHidden#">,
					@revenueGLAccountID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">,0),
					@rateGrouping varchar(200) = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.newRateGrouping)#">,''),
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@siteResourceID int;

				IF @rateGroupingID IS NULL AND @rateGrouping IS NOT NULL 
					SELECT @rateGroupingID = rateGroupingID
					FROM dbo.tblBundlesAndRatesGrouping
					WHERE participantID = @participantID
					AND bundleID = @bundleID
					AND rateGrouping = @rateGrouping;

				BEGIN TRAN;
					IF @rateGroupingID IS NULL AND @rateGrouping IS NOT NULL 
						EXEC dbo.sw_createBundleRateGrouping @participantID=@participantID, @bundleID=@bundleID, @rateGrouping=@rateGrouping,
							@rateGroupingID=@rateGroupingID OUTPUT;

					IF @rateID IS NULL
						EXEC dbo.sw_createBundleRate @participantID=@participantID, @bundleID=@bundleID, @rateGroupingID=@rateGroupingID,
							@rateName=@rateName, @rate=@rate, @isHidden=@isHidden, @revenueGLAccountID=@revenueGLAccountID, 
							@recordedByMemberID=@recordedByMemberID, @rateID=@rateID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;
					ELSE
						EXEC dbo.swb_updateBundleRate @participantID=@participantID, @rateID=@rateID, @rateGroupingID=@rateGroupingID,
							@rateName=@rateName, @rate=@rate, @isHidden=@isHidden, @revenueGLAccountID=@revenueGLAccountID, 
							@recordedByMemberID=@recordedByMemberID;
				COMMIT TRAN;

				SELECT @rateID AS rateID;

			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true, "rateID":val(local.qrySaveSWRate.rateID) }>
	</cffunction>

	<cffunction name="deleteBundleRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swb_deleteBundleRate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doBundleRateMove" access="public" output="false" returntype="struct" hint="Re-Order Rates">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="true">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_moveBundlesAndRates">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteMemberGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="true">
		<cfargument name="rateid" type="numeric" required="true">
		<cfargument name="groupid" type="numeric" required="true">
		<cfargument name="include" type="numeric" required="false" default="1">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySiteResourceRights" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @siteResourceID int;

				select @siteResourceID = siteResourceID
				from seminarWeb.dbo.tblBundlesAndRates
				where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateid#">
				and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

				select srr.resourceID, srr.resourceRightsID, sr.siteID
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = srr.siteID
				where srr.resourceID = @siteResourceID
				and srr.groupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.groupID#">
				and srr.include = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.include#">
			</cfquery>

			<cfloop query="local.qrySiteResourceRights">
				<cfstoredproc procedure="cms_deleteSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySiteResourceRights.siteID#">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySiteResourceRights.resourceID#">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySiteResourceRights.resourceRightsID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBundleRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryRateGrouping">
				SELECT rateGroupingID, rateGrouping
				FROM dbo.tblBundlesAndRatesGrouping
				WHERE rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">
				AND participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			</cfquery>

			<cfset local.data.rateGroupingID = local.qryRateGrouping.rateGroupingID>
			<cfset local.data.rateGrouping = local.qryRateGrouping.rateGrouping>
			<cfset local.data.success = true>
		<cfcatch type="any">		
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBundleRateGroupsbyBundleID" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="bundleID" type="numeric" required="yes">
		
		<cfset var qryRateGroupings = structNew()>
		
		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="qryRateGroupings">
			SET NOCOUNT ON;

			DECLARE @participantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			SELECT rg.rateGroupingID, rg.rateGrouping, rg.bundleID, rg.rateGroupingOrder,
				(
					SELECT COUNT(r.rateID) 
					FROM dbo.tblBundlesAndRates as r
					inner join memberCentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
					WHERE r.rateGroupingID = rg.rateGroupingID
					AND r.participantID = @participantID
				) AS rateCount
			FROM dbo.tblBundlesAndRatesGrouping as rg
			WHERE rg.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
			AND rg.participantID = @participantID
			ORDER BY rg.rateGroupingOrder;
		</cfquery>

		<cfreturn qryRateGroupings>
	</cffunction>

	<cffunction name="saveBundleRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="rateGrouping" type="string" required="true">
		<cfargument name="bundleID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateRateGrouping">
				SET NOCOUNT ON;
				
				DECLARE @participantID int, @rateGroupingID INT, @rateGrouping VARCHAR(200), @bundleID INT;
				SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;
				SET @rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">;
				SET @rateGrouping = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateGrouping#">;
				
				SELECT @bundleID = bundleID 
				FROM dbo.tblBundlesAndRatesGrouping 
				WHERE rateGroupingID = @rateGroupingID
				AND participantID = @participantID;
				
				IF NOT EXISTS (
					SELECT rateGroupingID FROM dbo.tblBundlesAndRatesGrouping 
					WHERE bundleID = @bundleID 
					AND rateGrouping = @rateGrouping 
					AND participantID = @participantID
					AND rateGroupingID <> @rateGroupingID
				)
					UPDATE dbo.tblBundlesAndRatesGrouping
					SET rateGrouping = @rateGrouping
					WHERE rateGroupingID = @rateGroupingID
					AND participantID = @participantID;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="deleteBundleRateGrouping" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryDeleteRateGrouping">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @participantID int, @bundleID INT, @rateGroupingID INT;
					SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;
					SET @bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
					SET @rateGroupingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">;

					BEGIN TRAN;
						UPDATE dbo.tblBundlesAndRates
						SET rateGroupingID = NULL
						WHERE rateGroupingID = @rateGroupingID
						AND participantID = @participantID;

						DELETE FROM dbo.tblBundlesAndRatesGrouping
						WHERE rateGroupingID = @rateGroupingID
						AND participantID = @participantID;

						EXEC dbo.sw_reorderBundleRateGrouping @participantID=@participantID, @bundleID=@bundleID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doBundleRateGroupingMove" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="rateGroupingID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="ManageRates", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_moveBundleRateGrouping">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateGroupingID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="sendTestInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="bundleIDList" type="string" required="yes">
		<cfargument name="testEmail" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWB = CreateObject("component","model.seminarweb.SWBundles")>
		
		<cfif len(arguments.testEmail) is 0 or NOT IsValid("regex",arguments.testEmail,application.regEx.email) or NOT listFindNoCase("2017,2020,2021",arguments.template)>
			<cfreturn>
		</cfif>

		<cfif ListLen(arguments.bundleIDList) EQ 1>
			<cfset local.utm_campaign = local.objSWB.getBundleByBundleID(bundleID=val(arguments.bundleIDList), orgcode=arguments.orgcode).bundleName>
		<cfelse>
			<cfset local.utm_campaign = 'Multiple Programs'>
		</cfif>

		<cfset local.qrySend = generateInvitationTable(bundleIDList=arguments.bundleIDList,orgcode=arguments.orgcode)>
		<cfoutput query="local.qrySend" group="orgcode">
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select bundleID
				from [local].qrySend
				where orgcode = '#local.qrySend.orgcode#'
			</cfquery>

			<!--- construct email messages --->
			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(bundleIDList=valuelist(local.qrySendIndiv.bundleID), orgcode=local.qrySend.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(bundleID=local.qrySendIndiv.bundleID,	orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017(bundleID=local.qrySendIndiv.bundleID,	orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			</cfif>

			<cfif application.MCEnvironment neq "production">
				<cfset arguments.testEmail = "<EMAIL>">
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html,
				utm_campaign=local.utm_campaign, utm_source="SWB Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>

			<!--- send email --->
			<cfmail to="#arguments.testEmail#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="**TEST** #local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8">
				#local.strEmailContent.html#
			</cfmail>
		
			<cfset local.objSWB.logAction(local.qrySend.bundleID,arguments.outgoingType,arguments.performedBy,arguments.testEmail,0)>
		</cfoutput>
	</cffunction>

	<cffunction name="sendInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="bundleIDList" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="listchoice" type="string" required="yes">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWB = CreateObject("component","model.seminarweb.SWBundles")>

		<cfif NOT listFindNoCase("2017,2020,2021",arguments.template)>
			<cfreturn>
		</cfif>

		<cfif ListLen(arguments.bundleIDList) EQ 1>
			<cfset local.utm_campaign = local.objSWB.getBundleByBundleID(bundleID=val(arguments.bundleIDList), orgcode=arguments.orgcode).bundleName>
		<cfelse>
			<cfset local.utm_campaign = 'Multiple Programs'>
		</cfif>

		<cfset local.qrySend = generateInvitationTable(bundleIDList=arguments.bundleIDList, orgcode=arguments.orgcode)>

		<cfoutput query="local.qrySend" group="orgcode">
			<cfset local.objSWB.logAction(local.qrySend.bundleID,"logInvitationToLyris",arguments.performedBy,'',2)>

			<cfset local.thisOrgCode = UCASE(local.qrySend.orgcode)>

			<!--- get programs for orgcode --->
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select bundleID
				from [local].qrySend
				where UPPER(orgcode) = '#local.thisOrgCode#'
			</cfquery>

			<!--- construct email messages --->
			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(bundleIDList=valuelist(local.qrySendIndiv.bundleID), orgcode=local.qrySend.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(bundleID=local.qrySendIndiv.bundleID,	orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017(bundleID=local.qrySendIndiv.bundleID,	orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html,
				utm_campaign=local.utm_campaign, utm_source="SWB Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>	

			<!--- set list email address --->
			<cfswitch expression="#arguments.listChoice#">
				<cfcase value="SAE">
					<cfset local.emailList = "seminarweb_sae.#local.qrySend.orgcode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="NATLE">
					<cfset local.emailList = "seminarweblive.#local.qrySend.orgcode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="RX">
					<cfset local.emailList = "seminarweb_rx.#local.qrySend.orgcode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="PT">
					<cfset local.emailList = "seminarweb_pt.#local.qrySend.orgcode#@lists.trialsmith.com">
				</cfcase>
				<cfdefaultcase>
					<cfset local.emailList = "seminarweb_#arguments.listChoice#.#arguments.listChoice#@lists.trialsmith.com">
				</cfdefaultcase>
			</cfswitch>

			<cfif application.MCEnvironment neq "production">
				<cfset local.emailList = "<EMAIL>">
			</cfif>

			<cfmail to="#local.emailList#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="#local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8" server="#application.mailservers.lyris.server#" port="#application.mailservers.lyris.port#" username="#application.mailservers.lyris.username#" password="#application.mailservers.lyris.password#">
				#local.strEmailContent.html#
			</cfmail>

			<cfset local.objSWB.logAction(local.qrySend.bundleID,arguments.outgoingType,arguments.performedBy,local.emailList,0)>
			<cfset local.objSWB.logAction(local.qrySend.bundleID,"logInvitationToLyris",arguments.performedBy,'',1)>
		</cfoutput>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2021" access="public" returntype="struct" output="no">
		<cfargument name="bundleIDList" type="string" required="yes">
		<cfargument name="orgCode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.objSWB = CreateObject("component","model.seminarweb.SWBundles")>
		<cfset local.objSWCommon = CreateObject("component","seminarWebSWCommon")>
		<cfset local.objResourceTemplate = CreateObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.orgCode)>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.strAssociation.qryAssociation.orgIdentityID)>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryPlatformFeaturedImageSetup = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getPlatformFeaturedImagesSetup()>
		<cfset local.qryParticipantFeaturedImageSetup = local.strAssociation.qryParticipantFeaturedImageSetup>

		<cfset local.bundlesArr = ArrayNew(1)>
		<cfset local.programLevelFeaturedImageCount = 0>

		<cfloop list="#arguments.bundleIDList#" index="local.thisBundleID">
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.bundleID = local.thisBundleID>
			<cfset local.tmpStr.strBundle = local.objSWB.getBundleForCatalog(bundleID=local.thisBundleID, catalogOrgCode=arguments.orgCode, billingState='', billingZip='', MCMemberID=0)>
			<cfif local.tmpStr.strBundle.qryBundle.recordCount>
				<cfif local.tmpStr.strBundle.qryBundle.isSWOD>
					<cfset local.tmpStr.bundleType = "SWOD">
				<cfelse>
					<cfset local.tmpStr.bundleType = "SWL">
				</cfif>
				<cfset local.tmpStr.trimmedProgramDesc = local.objSWCommon.trimByWordsCount(inputString=local.tmpStr.strBundle.qryBundle.bundleDesc, count=100)>

				<!--- featured image paths --->
				<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
					<cfset local.programFeaturedImagesStr = local.objSWCommon.getProgramFeaturedImagePathsForInvitationEmail(publisherOrgCode=local.tmpStr.strBundle.qryBundle.publisherOrgCode, programType="SWB",
						programFeatureImageID=val(local.tmpStr.strBundle.qryBundle.featureImageID), programFeatureImageSizeID=val(local.tmpStr.strBundle.qryBundle.featureImageSizeID),
						programFeatureImageFileExtension=local.tmpStr.strBundle.qryBundle.featureImageFileExtension, qryPlatformFeaturedImageSetup=local.qryPlatformFeaturedImageSetup,
						qryParticipantFeaturedImageSetup=local.qryParticipantFeaturedImageSetup, qrySWHostName=local.qrySWHostName)>
					
					<cfset local.tmpStr.programLevelFeaturedImagePath = local.programFeaturedImagesStr.programLevelFeaturedImagePath>
					<cfif len(local.tmpStr.programLevelFeaturedImagePath)>
						<cfset local.programLevelFeaturedImageCount += 1>
					</cfif>
					<cfset local.tmpStr.featuredImagePath = local.programFeaturedImagesStr.featuredImagePath>
				<cfelse>
					<cfset local.tmpStr.programLevelFeaturedImagePath = "">
					<cfset local.tmpStr.featuredImagePath = "">
				</cfif>
				
				<cfset local.publisherSiteInfo = application.objSiteInfo.getSiteInfo(local.tmpStr.strBundle.qryBundle.publisherOrgCode)>
				<cfset local.publisherHostName = local.objWebsite.getMainHost(val(local.publisherSiteInfo.siteID)).mainHostname>
				<cfset local.tmpStr.browsePageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?panel=browse&_swft=swl&_swft=swod">
				<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWB-#local.tmpStr.bundleID#">

				<cfset ArrayAppend(local.bundlesArr,local.tmpStr)>
			</cfif>
		</cfloop>
			
		<!--- set from/subject --->
		<cfif len(local.strAssociation.qryAssociation.emailFrom)>
			<cfset local.strEmailContent.fromName = local.strAssociation.qryAssociation.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>
		
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<cfif arrayLen(local.bundlesArr) eq 1>
				<cfset local.thisBundle = local.bundlesArr[1]>
				<cfset local.strBundle = local.thisBundle.strBundle>
				<cfset local.includedProgramsArr = []>

				<cfset local.qryIncludedPrograms = getBundledItemsForInvitationEmail(bundleID=local.thisBundle.bundleID, bundleType=local.thisBundle.bundleType, qryAssociation=local.strAssociation.qryAssociation)>

				<cfloop query="local.qryIncludedPrograms">
					<cfset local.tmpStr = { "programID":local.qryIncludedPrograms.seminarID, "programTitle":local.qryIncludedPrograms.seminarName,
							"programSubTitle":local.qryIncludedPrograms.seminarSubTitle, "displayDate":local.qryIncludedPrograms.displayDate, "featuredImagePath":"", "programDetailPageLink":""
						}>

					<cfif local.thisBundle.bundleType eq "SWL">
						<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWLive-#local.qryIncludedPrograms.seminarID#">
					<cfelseif local.thisBundle.bundleType eq "SWOD">
						<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWOD-#local.qryIncludedPrograms.seminarID#">
					</cfif>

					<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
						<cfset local.programFeaturedImagesStr = local.objSWCommon.getProgramFeaturedImagePathsForInvitationEmail(publisherOrgCode=local.qryIncludedPrograms.siteCode, programType=local.thisBundle.bundleType,
							programFeatureImageID=val(local.qryIncludedPrograms.featureImageID), programFeatureImageSizeID=val(local.qryIncludedPrograms.featureImageSizeID),
							programFeatureImageFileExtension=local.qryIncludedPrograms.featureImageFileExtension, qryPlatformFeaturedImageSetup=local.qryPlatformFeaturedImageSetup,
							qryParticipantFeaturedImageSetup=local.qryParticipantFeaturedImageSetup, qrySWHostName=local.qrySWHostName)>
							
						<cfset local.tmpStr.featuredImagePath = local.programFeaturedImagesStr.featuredImagePath>
					</cfif>

					<cfset local.includedProgramsArr.append(local.tmpStr)>
				</cfloop>

				<cfinclude template="dsp_programInvitationTemplate_bundle_single.cfm">
			<cfelseif arrayLen(local.bundlesArr) gt 1>
				<cfinclude template="dsp_programInvitationTemplate_bundle_multiple.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.strRenderedTemplate = local.objResourceTemplate.doRenderResourceTemplate(template=local.strEmailContent.html, model={}, templateFormat="MJML")>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strRenderedTemplate.content,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2020" access="public" returntype="struct" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.qryAssociationDetails = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qryBundleStr = CreateObject("component","model.seminarweb.SWBundles").getBundleForCatalog(bundleID=arguments.bundleID, 
			catalogOrgCode=arguments.orgcode, billingState='', billingZip='', MCMemberID=0)>

		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>

		<!--- Get SWLive dates, if any --->
		<cfquery name="local.qrySWLiveDates" datasource="#application.dsn.tlasites_seminarweb.dsn#">		
			select distinct dateStart
			from dbo.swb_getBundledItemsForCatalog(<cfqueryparam value="#arguments.bundleID#" cfsqltype="cf_sql_integer">,0)
			where format = 'SWL'
			order by dateStart
		</cfquery>	

		<cfquery name="local.qryUpcomingWebinars" datasource="#application.dsn.tlasites_seminarweb.dsn#">		
			SET NOCOUNT ON;

			declare @tblSWLive table (
				seminarID int,
				seminarName varchar(250),
				seminarSubTitle varchar(250),
				publisherOrgcode varchar(10),
				dateStart datetime,
				isPast bit,
				totalCount int,
				enrolledCount int,
				seminarIDList varchar(max)
			);

			insert into @tblSWLive
			exec dbo.swl_getSeminarsForSWAdminBySearch 
				@orgcode = <cfqueryparam value="#arguments.orgcode#" cfsqltype="cf_sql_varchar">,
				@sd = <cfqueryparam value="#now()#" cfsqltype="cf_sql_date">,
				@ed = <cfqueryparam value="#dateAdd("yyyy",1,now())#" cfsqltype="cf_sql_date">,
				@pn = '',
				@startRow = 1,
				@maxrows = 5;

			select seminarID, seminarName, seminarSubTitle, dateStart 
			from @tblSWLive 
			where isPast = 0 
			order by dateStart asc;
		</cfquery>			

		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
		    <head>
		    </head>
		    <body>
		        <table style="background-color:##ffffff; font-family:Verdana,Arial,sans-serif; font-size:10pt; color:##666666; margin:0; padding:7px;" cellspacing="0" cellpadding="0" border="0">
		            <tbody>
		                <tr>
		                    <td width="600">
		                    <table style="background-color: rgb(59, 97, 142);" valign="top" width="624" height="107" border="0">
		                        <tbody>
		                            <tr>
		                                <td width="425" valign="top">
		                                	<cfif len(trim(local.qryAssociationDetails.description))>
				                                <div style="padding:3px; font-family:Calibri; color:##ffffff; font-size:12pt; font-style:italic; line-height:93%;">
				                                	#local.qryAssociationDetails.description# invites you to attend:
				                                </div>                                		
		                                	</cfif>
			                                <div style="font-size:16pt; line-height:119%; font-family:Calibri; color:##ffffff; font-weight:bold;">#local.qryBundleStr.qryBundle.bundleName# Bundle!</div>
		                                </td>
		                                <td>&##160;</td>
		                                <td width="175">
		                                <table cellspacing="0" cellpadding="0" border="0">
		                                <tbody>
		                                    <tr>
		                                        <td width="142" height="37" bgcolor="##983634" align="center">
		                                            <div style="padding:2px;text-align:center;">
		                                            	<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%" style="text-align:center; font-size:12pt; line-height:100%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;">Register Now!</a>
		                                            </div>
		                                        </td>
		                                    </tr>
		                                </tbody>
		                                </table>
		                                </td>
		                            </tr>
		                        </tbody>
		                    </table>		                    
		                    <cfif local.qrySWLiveDates.recordcount>
	                    	    <table cellspacing="0" cellpadding="0" border="0" width="624">
			                        <tbody>
			                            <tr>
			                                <td style="background:silver;" width="624" valign="middle" height="23" bgcolor="silver">
			                                	<div width="624" style="line-height:93%; font-size:12pt; font-family:Calibri; color:##3b618e;">
			                                		&##160;
			                                		<cfloop query="local.qrySWLiveDates">
			                                			#dateFormat(local.qrySWLiveDates.dateStart, "mmmm d")#<cfif local.qrySWLiveDates.currentRow lt local.qrySWLiveDates.recordCount>, </cfif>
			                                		</cfloop>
			                                	</div>
			                                </td>
			                            </tr>
			                        </tbody>
			                    </table>
		                    </cfif>
		                    <div style="height:20px;">&##160;</div>
		                    <table>
		                        <tbody>
		                            <tr valign="top">
		                                <td valign="top">
		                                <table cellspacing="0" cellpadding="0" border="0">
		                                    <tbody>
		                                        <tr valign="top">
		                                            <td width="400" valign="top">
			                                            <table cellspacing="0" cellpadding="0" border="0">
			                                                <tbody>
			                                                    <tr valign="top">
			                                                        <td width="400" valign="top">
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Summary</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;"><br />
			     															#local.qryBundleStr.qryBundle.bundleDesc#
			                                                    		</span>
			                                                    	</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td height="7">&nbsp;</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td>
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Credit</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;">
			                                                        		Detailed credit information is available on the <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%">registration page</a>.
			                                                        	</span>
			                                                        </td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td height="7">&nbsp;</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td>
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Questions?</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;">For immediate help please consult our <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWB} -name {FAQ} -group {#local.qryAssociationDetails.shortname#}%%"><b>FAQ page</b></a>.<br />
			                                                        		<br />
			                                                        		If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
			                                                    		</span>
			                                                    	</td>
			                                                    </tr>
			                                                </tbody>
			                                            </table>
			                                            </td>
			                                        </tr>
			                                    </tbody>
			                                </table>
		                                </td>
		                                <td style="vertical-align:top; background:silver;" width="1px;">&##160;</td>
		                                <td width="230">
			                                <table cellspacing="0" cellpadding="0">
			                                    <tbody>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="210">
			                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Fee</div>
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;" width="220">
															<cfset local.qryPrices = local.qryBundleStr.qryBundlePrices>
															<div style="padding:4px;padding-left:5px; line-height:110%; font-size:10pt; font-family:Calibri; color:##000000;">
															<cfloop query="local.qryPrices">																											
																<cfif local.qryPrices.price gte 0>
																	<cfif local.qryPrices.price is 0>
																		<strong>#replace(local.qryBundleStr.qryBundle.freeRateDisplay,".00","")#</strong>
																	<cfelseif local.qryPrices.price gt 0>
																		<strong>#replace(dollarformat(local.qryPrices.price),".00","")#<cfif local.qryBundleStr.qryBundle.showUSD> USD</cfif></strong>
																	</cfif>
																	<cfif len(local.qryPrices.description)> <cfif local.qryPrices.price gt 0 or len(local.qryBundleStr.qryBundle.freeRateDisplay)>for </cfif>#local.qryPrices.description#</cfif><br/>
																</cfif>
															</cfloop>
															</div>                                           	
			                                            	<div align="center">
			                                            		<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%" style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">
			                                            			<strong><span style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">Click here to register!</span></strong>
			                                            		</a>
			                                            	</div>
			                                            	<br />
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td>&##160;</td>
			                                        </tr>
			                                         <cfif local.qryUpcomingWebinars.recordCount>
				                                        <tr>
				                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="220">
				                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Upcoming Webinars</div>
				                                            </td>
				                                        </tr>
			                                        	<cfloop query="local.qryUpcomingWebinars">
				                                        <tr>
				                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;" width="220">
				                                            <table cellspacing="0" cellpadding="0">
				                                                <tbody>
				                                                    <tr>
				                                                        <td style="background:##1F497D; padding:4px; border-right:solid ##777671 1.0pt; border-bottom:solid ##fff 1.0pt;" width="45">
				                                                     		<div style="font-size:15pt; line-height:119%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">#day(local.qryUpcomingWebinars.dateStart)#</div>
				                                                        	<div style="font-size:11pt; line-height:75%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;"><cfif monthAsString(month(local.qryUpcomingWebinars.dateStart)) neq "September">
				                                                        		#left(monthAsString(month(local.qryUpcomingWebinars.dateStart)),3)#<cfelse>#left(monthAsString(month(local.qryUpcomingWebinars.dateStart)),4)#</cfif></div>
				                                                        </td>
				                                                        <td style="border-bottom:1px solid ##A6A6A6;">
				                                                        	<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.qryUpcomingWebinars.seminarID#&linksource=PI&linkterms=SWML" style="text-align:center; font-size:10pt; line-height:90%; font-family:Calibri; text-decoration:none;">#encodeForHTML(local.qryUpcomingWebinars.seminarName)#</a>
				                                                        </td>
				                                                    </tr>
				                                                </tbody>
				                                            </table>
				                                            </td>
				                                            <td>&##160;</td>
				                                        </tr>
				                                        </cfloop>			                                       
				                                        <tr>
				                                            <td>&##160;</td>
				                                        </tr>
													</cfif>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="220">
			                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Tell a Colleague!</div>
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top; padding-top:10px;" width="230" align="center">
			                                            	<span class="st_facebook_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Facebook" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/facebook_32.png" alt="Share this on Facebook" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_linkedin_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Linked In" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/linkedin_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_email_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="mailto:?Subject=#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#.' You can register at #local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#." title="Email this to a colleague" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/email_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_twitter_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Twitter" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/twitter_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>
			                                            </td>
			                                        </tr>
			                                    </tbody>
			                                </table>
		                                </td>
		                            </tr>
		                        </tbody>
		                    </table>
		                    </td>
		                </tr>
		                <tr>
		                    <td>
		                    	<br>
			                    <p style="font-family:Verdana,Arial,sans-serif;font-size:7pt;color:##666;text-align:center;">
			                    	This promotional email was sent by SeminarWeb on behalf of #local.qryAssociationDetails.description#.<br />
			                    	13359 N Hwy 183 ##406-1220, Austin, TX 78750<br />
								
									<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
									To cease further e-mails regarding #local.qryAssociationDetails.shortname#'s seminars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color:##000;">here</a>.
			                    </p>
		                	</td>
		                </tr>
		            </tbody>
		        </table>
		   	</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2017" access="public" returntype="struct" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.qryAssociationDetails = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qryBundleStr = CreateObject("component","model.seminarweb.SWBundles").getBundleForCatalog(bundleID=arguments.bundleID, 
			catalogOrgCode=arguments.orgcode, billingState='', billingZip='', MCMemberID=0)>

		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>

		<!--- Get SWLive dates, if any --->
		<cfquery name="local.qrySWLiveDates" datasource="#application.dsn.tlasites_seminarweb.dsn#">		
			select distinct dateStart
			from dbo.swb_getBundledItemsForCatalog(<cfqueryparam value="#arguments.bundleID#" cfsqltype="cf_sql_integer">,0)
			where format = 'SWL'
			order by dateStart
		</cfquery>	

		<cfquery name="local.qryUpcomingWebinars" datasource="#application.dsn.tlasites_seminarweb.dsn#">		
			SET NOCOUNT ON;

			declare @tblSWLive table (
				seminarID int,
				seminarName varchar(250),
				seminarSubTitle varchar(250),
				publisherOrgcode varchar(10),
				dateStart datetime,
				isPast bit,
				totalCount int,
				enrolledCount int,
				seminarIDList varchar(max)
			);

			insert into @tblSWLive
			exec dbo.swl_getSeminarsForSWAdminBySearch 
				@orgcode = <cfqueryparam value="#arguments.orgcode#" cfsqltype="cf_sql_varchar">,
				@sd = <cfqueryparam value="#now()#" cfsqltype="cf_sql_date">,
				@ed = <cfqueryparam value="#dateAdd("yyyy",1,now())#" cfsqltype="cf_sql_date">,
				@pn = '',
				@startRow = 1,
				@maxrows = 5;

			select seminarID, seminarName, seminarSubTitle, dateStart 
			from @tblSWLive 
			where isPast = 0 
			order by dateStart asc;
		</cfquery>			

		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
		    <head>
		    </head>
		    <body>
		        <table style="background-color:##ffffff; font-family:Verdana,Arial,sans-serif; font-size:10pt; color:##666666; margin:0; padding:7px;" cellspacing="0" cellpadding="0" border="0">
		            <tbody>
		                <tr>
		                    <td width="600">
		                    <table style="background-color: rgb(59, 97, 142);" valign="top" width="624" height="107" border="0">
		                        <tbody>
		                            <tr>
		                                <td width="425" valign="top">
		                                	<cfif len(trim(local.qryAssociationDetails.description))>
				                                <div style="padding:3px; font-family:Calibri; color:##ffffff; font-size:12pt; font-style:italic; line-height:93%;">
				                                	#local.qryAssociationDetails.description# invites you to attend:
				                                </div>                                		
		                                	</cfif>
			                                <div style="font-size:16pt; line-height:119%; font-family:Calibri; color:##ffffff; font-weight:bold;">#local.qryBundleStr.qryBundle.bundleName# Bundle!</div>
		                                </td>
		                                <td>&##160;</td>
		                                <td width="175">
		                                <table cellspacing="0" cellpadding="0" border="0">
		                                <tbody>
		                                    <tr>
		                                        <td width="142" height="37" bgcolor="##983634" align="center">
		                                            <div style="padding:2px;text-align:center;">
		                                            	<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%" style="text-align:center; font-size:12pt; line-height:100%; font-family:Calibri; color:##ffffff; font-weight:bold; text-decoration:none;">Register Now!</a>
		                                            </div>
		                                        </td>
		                                    </tr>
		                                </tbody>
		                                </table>
		                                </td>
		                            </tr>
		                        </tbody>
		                    </table>		                    
		                    <cfif local.qrySWLiveDates.recordcount>
	                    	    <table cellspacing="0" cellpadding="0" border="0" width="624">
			                        <tbody>
			                            <tr>
			                                <td style="background:silver;" width="624" valign="middle" height="23" bgcolor="silver">
			                                	<div width="624" style="line-height:93%; font-size:12pt; font-family:Calibri; color:##3b618e;">
			                                		&##160;
			                                		<cfloop query="local.qrySWLiveDates">
			                                			#dateFormat(local.qrySWLiveDates.dateStart, "mmmm d")#<cfif local.qrySWLiveDates.currentRow lt local.qrySWLiveDates.recordCount>, </cfif>
			                                		</cfloop>
			                                	</div>
			                                </td>
			                            </tr>
			                        </tbody>
			                    </table>
		                    </cfif>
		                    <div style="height:20px;">&##160;</div>
		                    <table>
		                        <tbody>
		                            <tr valign="top">
		                                <td valign="top">
		                                <table cellspacing="0" cellpadding="0" border="0">
		                                    <tbody>
		                                        <tr valign="top">
		                                            <td width="400" valign="top">
			                                            <table cellspacing="0" cellpadding="0" border="0">
			                                                <tbody>
			                                                    <tr valign="top">
			                                                        <td width="400" valign="top">
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Summary</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;"><br />
			     															#local.qryBundleStr.qryBundle.bundleDesc#
			                                                    		</span>
			                                                    	</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td height="7">&nbsp;</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td>
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Credit</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;">
			                                                        		Detailed credit information is available on the <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%">registration page</a>.
			                                                        	</span>
			                                                        </td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td height="7">&nbsp;</td>
			                                                    </tr>
			                                                    <tr>
			                                                        <td>
			                                                        	<span style="font-size:12pt; line-height:110%; font-family:Georgia; color:##983634; font-weight:bold;">Questions?</span><br />
			                                                        	<span style="font-size:10pt; line-hieght:110%; font-family:Calibri; color:##000000;">For immediate help please consult our <a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWB} -name {FAQ} -group {#local.qryAssociationDetails.shortname#}%%"><b>FAQ page</b></a>.<br />
			                                                        		<br />
			                                                        		If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
			                                                    		</span>
			                                                    	</td>
			                                                    </tr>
			                                                </tbody>
			                                            </table>
			                                            </td>
			                                        </tr>
			                                    </tbody>
			                                </table>
		                                </td>
		                                <td style="vertical-align:top; background:silver;" width="1px;">&##160;</td>
		                                <td width="230">
			                                <table cellspacing="0" cellpadding="0">
			                                    <tbody>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="210">
			                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Fee</div>
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;" width="220">
															<cfset local.qryPrices = local.qryBundleStr.qryBundlePrices>
															<div style="padding:4px;padding-left:5px; line-height:110%; font-size:10pt; font-family:Calibri; color:##000000;">
															<cfloop query="local.qryPrices">																											
																<cfif local.qryPrices.price gte 0>
																	<cfif local.qryPrices.price is 0>
																		<strong>#replace(local.qryBundleStr.qryBundle.freeRateDisplay,".00","")#</strong>
																	<cfelseif local.qryPrices.price gt 0>
																		<strong>#replace(dollarformat(local.qryPrices.price),".00","")#<cfif local.qryBundleStr.qryBundle.showUSD> USD</cfif></strong>
																	</cfif>
																	<cfif len(local.qryPrices.description)> <cfif local.qryPrices.price gt 0 or len(local.qryBundleStr.qryBundle.freeRateDisplay)>for </cfif>#local.qryPrices.description#</cfif><br/>
																</cfif>
															</cfloop>
															</div>                                           	
			                                            	<div align="center">
			                                            		<a href="%%track {#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#&linksource=PI&linkterms=SWML} -name {#local.qryAssociationDetails.shortname#} -group {#local.qryAssociationDetails.shortname#}%%" style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">
			                                            			<strong><span style="font-size:12pt; line-height:100%; font-family:Calibri; color:##983634; text-decoration:underline; font-weight:bold;">Click here to register!</span></strong>
			                                            		</a>
			                                            	</div>
			                                            	<br />
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td>&##160;</td>
			                                        </tr>
			                                         <cfif local.qryUpcomingWebinars.recordCount>
				                                        <tr>
				                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="220">
				                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Upcoming Webinars</div>
				                                            </td>
				                                        </tr>
			                                        	<cfloop query="local.qryUpcomingWebinars">
				                                        <tr>
				                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top;" width="220">
				                                            <table cellspacing="0" cellpadding="0">
				                                                <tbody>
				                                                    <tr>
				                                                        <td style="background:##1F497D; padding:4px; border-right:solid ##777671 1.0pt; border-bottom:solid ##fff 1.0pt;" width="45">
				                                                     		<div style="font-size:15pt; line-height:119%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;">#day(local.qryUpcomingWebinars.dateStart)#</div>
				                                                        	<div style="font-size:11pt; line-height:75%; font-family:Calibri; color:##EEECE1; font-weight:bold; text-align:center;"><cfif monthAsString(month(local.qryUpcomingWebinars.dateStart)) neq "September">
				                                                        		#left(monthAsString(month(local.qryUpcomingWebinars.dateStart)),3)#<cfelse>#left(monthAsString(month(local.qryUpcomingWebinars.dateStart)),4)#</cfif></div>
				                                                        </td>
				                                                        <td style="border-bottom:1px solid ##A6A6A6;">
				                                                        	<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWLive-#local.qryUpcomingWebinars.seminarID#&linksource=PI&linkterms=SWML" style="text-align:center; font-size:10pt; line-height:90%; font-family:Calibri; text-decoration:none;">#encodeForHTML(local.qryUpcomingWebinars.seminarName)#</a>
				                                                        </td>
				                                                    </tr>
				                                                </tbody>
				                                            </table>
				                                            </td>
				                                            <td>&##160;</td>
				                                        </tr>
				                                        </cfloop>			                                       
				                                        <tr>
				                                            <td>&##160;</td>
				                                        </tr>
													</cfif>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; vertical-align:top; background:silver;" width="220">
			                                            	<div style="padding:4px; line-height:100%; font-size:12pt; font-family:Georgia; color:##3b618e; font-weight:bold;">Tell a Colleague!</div>
			                                            </td>
			                                        </tr>
			                                        <tr>
			                                            <td style="border:1px solid ##A6A6A6; background:##ffffff; vertical-align:top; padding-top:10px;" width="230" align="center">
			                                            	<span class="st_facebook_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Facebook" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/facebook_32.png" alt="Share this on Facebook" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_linkedin_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Linked In" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/linkedin_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_email_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="mailto:?Subject=#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#.' You can register at #local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#." title="Email this to a colleague" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/email_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>&##160;
			                                            	<span class="st_twitter_large">
			                                            		<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border:0;">
			                                            			<a href="https://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.qryBundleStr.qryBundle.bundleName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWB-#local.qryBundleStr.qryBundle.bundleID#" title="Share this on Twitter" target="_blank">
			                                            				<img src="https://ws.sharethis.com/images/twitter_32.png" alt="" width="32" height="32" border="0" />
			                                            			</a>
			                                            		</span>
			                                            	</span>
			                                            </td>
			                                        </tr>
			                                    </tbody>
			                                </table>
		                                </td>
		                            </tr>
		                        </tbody>
		                    </table>
		                    </td>
		                </tr>
		                <tr>
		                    <td>
		                    	<br>
			                    <p style="font-family:Verdana,Arial,sans-serif;font-size:7pt;color:##666;text-align:center;">
			                    	This promotional email was sent by SeminarWeb on behalf of #local.qryAssociationDetails.description#.<br />
			                    	13359 N Hwy 183 ##406-1220, Austin, TX 78750<br />
								
									<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
									To cease further e-mails regarding #local.qryAssociationDetails.shortname#'s seminars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color:##000;">here</a>.
			                    </p>
		                	</td>
		                </tr>
		            </tbody>
		        </table>
		   	</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationTable" access="private" returntype="query" output="no">
		<cfargument name="bundleIDList" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.objSWPAdmin = CreateObject("component","model.admin.seminarweb.seminarWebParticipants")>

		<cfset local.tmpOptedIn = ArrayNew(1)>
		<cfset local.tmpArr = ListToArray(arguments.bundleIDList)>
		<cfset ArraySet(local.tmpOptedIn,1,ArrayMax(local.tmpArr),'')>
		<cfloop list="#arguments.bundleIDList#" index="local.thisBID">
			<cfset local.tmpOptedIn[local.thisBID] = getParticipantsOptedIntoBundle(local.thisBID)>
		</cfloop>
		<cfset local.qrySend = QueryNew("orgcode,bundleID","varchar,integer")>

		<cfif len(arguments.orgcode)>
			<cfloop list="#arguments.bundleIDList#" index="local.thisBID">
				<cfset local.tmpqry = local.tmpOptedIn[local.thisBID]>
				<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),arguments.orgcode)>
					<cfset QueryAddRow(local.qrySend)>
					<cfset QuerySetCell(local.qrySend,"orgcode",arguments.orgcode)>
					<cfset QuerySetCell(local.qrySend,"bundleID",local.thisBID)>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.qryAssociations = getParticipantsOptedIntoBundle(local.thisBID)>
			<cfloop query="local.qryAssociations">
				<cfset local.tmpOrgcode = local.qryAssociations.orgcode>
				<cfloop list="#arguments.bundleIDList#" index="local.thisBID">
					<cfset local.tmpqry = local.tmpOptedIn[local.thisBID]>
					<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),local.tmpOrgcode)>
						<cfset QueryAddRow(local.qrySend)>
						<cfset QuerySetCell(local.qrySend,"orgcode",local.tmpOrgcode)>
						<cfset QuerySetCell(local.qrySend,"bundleID",local.thisBID)>
					</cfif>
				</cfloop>
			</cfloop>
		</cfif>

		<cfreturn local.qrySend>
	</cffunction>

	<cffunction name="getParticipantsOptedIntoBundle" access="package" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getParticipantsOptedIntoBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
			<cfprocresult name="local.qryParticipants" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryParticipants>
	</cffunction>

	<cffunction name="updateSWBProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="mcproxy_orgID" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="bundleStatus" type="string" required="no">
		<cfargument name="bundleName" type="string" required="yes">
		<cfargument name="bundleSubTitle" type="string" required="no">
		<cfargument name="programCode" type="string" required="yes">
		<cfargument name="bundleDesc" type="string" required="no">
		<cfargument name="dateActivated" type="string" required="no">
		<cfargument name="lockSWBProgramSettings" type="boolean" required="no">
		<cfargument name="sendConfirmationEmail" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, errmsg='' }>
		<cfset local.objSWB = CreateObject("component","model.seminarweb.SWBundles")>

		<cfparam name="arguments.bundleName" default="">
		<cfparam name="arguments.bundleSubTitle" default="">
		<cfparam name="arguments.programCode" default="">
		<cfparam name="arguments.bundleDesc" default="">
		<cfparam name="arguments.bundleStatus" default="I">
		<cfparam name="arguments.lockSWBProgramSettings" default="0">
		<cfparam name="arguments.dateActivated" default="">
		<cfparam name="arguments.sendConfirmationEmail" default="0">

		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryBundle">
				select p.orgcode as publisherOrgCode, b.lockSettings
				from dbo.tblBundles as b
				inner join dbo.tblParticipants as p on p.participantID = b.participantID
				where b.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
			</cfquery>

			<!--- security --->
			<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfset local.hasLockSWProgramRights = local.tmpRights.lockSWProgram is 1>
			<cfset local.hasUpdateSWBProgramRights = (local.tmpRights.editBundleAll is 1 OR local.tmpRights.editBundlePublish is 1) AND local.qryBundle.publisherOrgCode eq arguments.mcproxy_siteCode>

			<cfif local.hasUpdateSWBProgramRights AND NOT local.qryBundle.lockSettings>
				
				<!--- update bundle --->
				<cfstoredproc procedure="swb_updateBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.bundleName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.bundleSubTitle#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.bundleDesc#">
					<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="#arguments.bundleStatus#">
					<cfif arguments.bundleStatus eq "A" and not len(arguments.dateActivated)>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#now()#">
					<cfelseif len(arguments.dateActivated)>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateActivated#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					</cfif>
					<cfif local.hasLockSWProgramRights>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.lockSWBProgramSettings#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
				
				<cfif arguments.sendConfirmationEmail and not application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfset local.qryBundle = local.objSWB.getBundleByBundleID(bundleID=arguments.bundleID, orgcode=arguments.mcproxy_siteCode)>
					<cfset local.sendingSiteInfo = application.objSiteInfo.getSiteInfo(local.qryBundle.publisherOrgCode)>
					<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(local.qryBundle.publisherOrgCode)>
					<cfset local.programLink = "#local.siteInfo.scheme#://#local.siteInfo.mainhostname##CreateObject('component','model.admin.admin').buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWBProgram')#&pid=#local.qryBundle.bundleID#">
					<cfset local.qryItems = createObject("component","seminarWebSWB").getBundledItems(bundleID=arguments.bundleID)>

					<!--- confirmation email to submitter --->
					<cfif len(local.qryBundle.submittedByEmail)>
						<cfsavecontent variable="local.emailHTMLContent">
							<cfoutput>
								Hi #local.qryBundle.submitterFirstName#,<br/><br/>
								<b>#local.qryBundle.bundleName#</b> has been successfully converted to a bundle. To review & activate your bundle, <a href="#local.programLink#">click here.</a><br/>
								<hr>
								Below is a summary of your bundle: <br/><br/>
								Bundle Title: #encodeForHTML(local.qryBundle.bundleName)#
								<cfif len(local.qryBundle.bundleSubTitle)>
									<br/><br/>Bundle SubTitle: #encodeForHTML(local.qryBundle.bundleSubTitle)#
								</cfif>
								<cfif LEN(local.qryBundle.dateCatalogStart)>
									<br/><br/>Catalog Sale Dates: #DateFormat(local.qryBundle.dateCatalogStart,'mm/dd/yyyy')# - #DateFormat(local.qryBundle.dateCatalogEnd,'mm/dd/yyyy')#
								</cfif>
								<cfif local.qryItems.recordcount>
									<br/><br/>Programs Included in Bundle:
									<ul>
										<cfloop query="local.qryItems">
											<li>#contentName#</li>
										</cfloop>
									</ul>
								</cfif>

								<hr><br/>
								<p style="margin-top:2px;margin-bottom:2px;">SeminarWeb</p>
								737-201-2059
							</cfoutput>
						</cfsavecontent>
						<cfset local.emailHTMLContent = trim(replace(replace(replace(local.emailHTMLContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
						<cfset local.bundleType = (local.qryBundle.isSWOD ? "OnDemand" : "Live")>
						<cfset local.emailsubject = "#local.bundleType# Bundle Created - #local.qryBundle.bundleName#">

						<cfset application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="SeminarWeb", email="<EMAIL>" },
							emailto=[{ name=local.qryBundle.submittedByMember, email=local.qryBundle.submittedByEmail }],
							emailreplyto="",
							emailsubject=local.emailsubject,
							emailtitle="#local.bundleType# Bundle Created",
							emailhtmlcontent=local.emailHTMLContent,
							siteID=local.qryBundle.participantSiteID,
							memberID=local.qryBundle.submittedByMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWBCREATE"),
							sendingSiteResourceID=local.sendingSiteInfo.siteSiteResourceID
						)>
					</cfif>
				</cfif>

				<cfset local.returnStruct.success = true>

			<cfelseif local.hasUpdateSWBProgramRights AND local.hasLockSWProgramRights>
				<cfset CreateObject("seminarWebSWCommon").updateProgramLockSettings(orgID=arguments.mcproxy_orgID,
					siteID=arguments.mcproxy_siteID, programType="SWB", programID=arguments.bundleID,
					lockSWProgramSettings=arguments.lockSWBProgramSettings)>

				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errmsg = "You do not have rights to this section.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSWBProgramCatalog" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="no">
		<cfargument name="allowCatalog" type="boolean" required="no">
		<cfargument name="dateCatalogStart" type="string" required="no">
		<cfargument name="dateCatalogEnd" type="string" required="no">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="isFeatured" type="boolean" required="no">
		<cfargument name="sponsorUsageIdsList" type="string" required="no">
		<cfargument name="includeSpeakers" type="boolean" required="yes">

		<cfset var local = structNew()>
		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="catalog", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swb_updateBundleCatalog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfif NOT isNull(arguments.allowCatalog)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowCatalog#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfif NOT isNull(arguments.dateCatalogStart) and arguments.allowCatalog is 1 and len(arguments.dateCatalogStart) and isDate(arguments.dateCatalogStart)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogStart#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif NOT isNull(arguments.dateCatalogEnd) and arguments.allowCatalog is 1 and len(arguments.dateCatalogEnd) and isDate(arguments.dateCatalogEnd)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogEnd#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif NOT isNull(arguments.revenueGLAccountID) and val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif NOT isNull(arguments.isFeatured)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isFeatured#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfif NOT isNull(arguments.sponsorUsageIdsList)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorUsageIdsList#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeSpeakers#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBundledItems" access="public" returntype="query" output="no">
		<cfargument name="bundleID" type="numeric" required="yes">
		
		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getBundledItemsByBundleID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
			<cfprocresult name="local.qryBundledItems" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryBundledItems>
	</cffunction>

	<cffunction name="getAvailableSWLSeminarsToAdd" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="bundleID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.returnStruct.arrSWLPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#" returntype="array">
			SET NOCOUNT ON;

			DECLARE @startdate date = getdate();
			DECLARE @enddate date = dateadd(year,1,getdate());

			SELECT s.seminarID, s.seminarName, s.seminarSubTitle, swl.dateStart, s.isPublished
			FROM dbo.tblSeminars AS s 
			INNER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID 
			INNER JOIN dbo.tblParticipants AS p ON s.participantID = p.participantID
			LEFT OUTER JOIN dbo.tblBundledItems AS bi ON bi.seminarID = s.seminarID
				AND bi.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
			WHERE p.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
			AND swl.dateStart between @startdate AND @enddate
			AND s.isDeleted = 0
			AND bi.seminarID IS NULL
			ORDER BY swl.dateStart, s.seminarID;
		</cfquery>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAvailableSWODSeminarsToAdd" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="bundleID" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.returnStruct.arrSWODPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#" returntype="array">
			SELECT s.seminarID, s.seminarName, s.seminarSubTitle, s.isPublished
			FROM dbo.tblSeminars AS s 
			INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarID = s.seminarID
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
			LEFT OUTER JOIN dbo.tblBundledItems AS bi ON bi.seminarID = s.seminarID
				AND bi.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
			WHERE p.orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
			AND s.isDeleted = 0
			AND bi.seminarID IS NULL
			ORDER BY s.isPublished DESC, s.seminarName, s.seminarSubTitle;
		</cfquery>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addSeminarToBundle" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_addSeminarAndBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeSeminarFromBundle" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_removeSeminarAndBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSWBItemOverrideDescription" access="public" output="false" returntype="struct">
		<cfargument name="itemID" type="numeric">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWBItemDescription">
				SELECT itemID, ovDescription
				FROM dbo.tblBundledItems
				WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
			</cfquery>

			<cfset local.data.itemID = local.qrySWBItemDescription.itemID>
			<cfset local.data.itemDescription = local.qrySWBItemDescription.ovDescription>
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWBItemOverrideDescription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="itemID" type="numeric" required="yes">
		<cfargument name="itemDescription" type="string" required="yes">

		<cfset var local = structNew()>	

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="Edit", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateSWBItemDescription">
				DECLARE @itemID INT, @ovDescription VARCHAR(MAX), @oldDescription VARCHAR(MAX), @orgID INT, @siteID INT, @recordedByMemberID INT, @msg VARCHAR(MAX) = '';
				SELECT @orgID = orgID, @siteID = siteID FROM membercentral.dbo.sites WHERE siteCode = <cfqueryparam value="#arguments.mcproxy_siteCode#" cfsqltype="CF_SQL_VARCHAR">;
				SET @recordedByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
				SET @itemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="CF_SQL_INTEGER">;
				SET @ovDescription = <cfqueryparam value="#arguments.itemDescription#" cfsqltype="CF_SQL_VARCHAR">;
				SELECT @oldDescription = ovDescription FROM dbo.tblBundledItems WHERE itemID = @itemID;				

				UPDATE dbo.tblBundledItems SET ovDescription = @ovDescription WHERE itemID = @itemID;

				SELECT @msg = 'Override Program Description of ' + CASE WHEN swl.seminarID IS NULL THEN 'SWOD' ELSE 'SWL' END + '-' + cast(b.seminarID AS VARCHAR(20)) + ' [' + s.seminarName 
					+ '] changed from [' + ISNULL(NULLIF(@oldDescription,''),'blank') + '] to [' + ISNULL(NULLIF(b.ovDescription,''),'blank') + '] for SWB-' + cast(b.bundleID AS VARCHAR(20)) + '.'
				FROM dbo.tblBundledItems AS b
				INNER JOIN dbo.tblSeminars AS s ON s.seminarID = b.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
				WHERE b.itemID = @itemID
				AND ISNULL(b.ovDescription,'') <> ISNULL(@oldDescription,'');

				IF LEN(ISNULL(@msg,'')) > 0
					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"'+ REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars(@msg),'"','\"') + '" } }');
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="any">		
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSWBProgramOptInAndOuts" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="programType" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cftry>
		
			<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
			<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.mcproxy_siteCode)>
			<cfset local.qryAssociation = local.strAssociation.qryAssociation>

			<cfquery name="local.data.arrOptinsOptOuts" datasource="#application.dsn.tlasites_seminarweb.dsn#" returntype="array">
				SELECT DISTINCT p.participantID, p.orgcode, tla.Description, 
					CASE WHEN boi.bundleOptInID IS NULL THEN 0 ELSE 1 END AS isAdded, 
					COUNT(e.enrollmentID) AS isEnrollmentExist
				FROM dbo.tblParticipants AS p 
				INNER JOIN trialsmith.dbo.depoTLA AS tla ON tla.State = p.orgcode 
				INNER JOIN dbo.tblNationalProgramParticipants AS npp ON npp.participantID = p.participantID
				AND npp.programID IN (
					SELECT DISTINCT npp.programID
					FROM dbo.tblNationalPrograms np
					INNER JOIN dbo.tblNationalProgramParticipants npp ON npp.programID = np.programid 
					AND npp.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryAssociation.participantID#">)
				LEFT JOIN dbo.tblBundlesOptIn boi ON p.participantID = boi.participantID AND boi.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#"> AND boi.isActive = 1
				LEFT JOIN dbo.tblBundleOrders bo ON bo.bundleID = boi.bundleID AND boi.participantID = bo.participantID AND bo.isActive = 1
				LEFT JOIN dbo.tblEnrollments e 
					INNER JOIN tblUsers u ON e.userID = u.userID
					INNER JOIN trialsmith.dbo.depoMemberData md ON u.depoMemberDataID = md.depoMemberDataID AND (md.adminflag2 IS NULL OR md.adminflag2 <> 'Y')
				ON e.bundleOrderID = bo.orderID AND e.participantID = p.participantID AND e.isActive = 1
				WHERE p.handlesOwnPayment = 0
				AND p.participantID != <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryAssociation.participantID#">
				GROUP BY p.participantID, p.orgcode, tla.Description, CASE WHEN boi.bundleOptInID IS NULL THEN 0 ELSE 1 END 
				ORDER BY p.orgcode;
			</cfquery>

		<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getParticipantsOptedIntoBundle" access="public" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getParticipantsOptedIntoBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
			<cfprocresult name="local.qryParticipants" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryParticipants>
	</cffunction>
	
	<cffunction name="getParticipantsOptedOutBundle" access="public" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cfstoredproc procedure="sw_getParticipantsOptedOutBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#" null="No">
			<cfprocresult name="local.qryParticipants" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryParticipants>
	</cffunction>

	<cffunction name="optInSWBNationalProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="bundleID" type="string" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sw_optInProgramBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="optInSWBundle" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="orgCodeList" type="string" required="yes">

		<cfset var local = StructNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfloop list="#arguments.orgCodeList#" index="local.thisOrgCode">
				<cfstoredproc procedure="sw_optInBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.thisOrgCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="optOutSWBundle" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="orgCodeList" type="string" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cftry>
			<cfif not hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, programID=arguments.bundleID, action="manageSWOptIns", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfloop list="#arguments.orgCodeList#" index="local.thisOrgCode">
				<cfstoredproc procedure="sw_optOutBundle" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.thisOrgCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
				</cfstoredproc>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBundledItemsForInvitationEmail" access="private" output="false" returntype="query">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="bundleType" type="string" required="yes">
		<cfargument name="qryAssociation" type="query" required="yes">
		
		<cfset var local = structnew()>
		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>

		<cfif arguments.bundleType eq "SWL">
			<cfset local.featureImageReferenceType = "swlProgram">
		<cfelse>
			<cfset local.featureImageReferenceType = "swodProgram">
		</cfif>

		<cfquery name="local.qryBundledItems" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT bi.itemOrder, CASE WHEN swl.seminarID is not null THEN 'SWL' WHEN swod.seminarID is not null THEN 'SWOD' END AS swlType,
				bi.seminarID, s.seminarName, s.seminarSubTitle, swl.dateStart, swl.dateEnd, swl.wddxTimeZones, sites.siteCode,
				fiu.featureImageID, ficus.featureImageSizeID, fics.fileExtension AS featureImageFileExtension
			FROM dbo.tblBundles AS b
			INNER JOIN dbo.tblBundledItems AS bi ON b.bundleID = bi.bundleID 
			INNER JOIN dbo.tblSeminars AS s ON bi.seminarID = s.seminarID and s.isPublished = 1 AND s.isDeleted = 0
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
			INNER JOIN memberCentral.dbo.sites AS sites ON sites.siteCode = p.orgCode
			LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
			LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
			LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = sites.siteID
				AND ficu.referenceType = 'swProgram'
			LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
				INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
				ON fiu.featureImageConfigID = ficu.featureImageConfigID 
				AND fiu.referenceID = s.seminarID
				AND fiu.referenceType = '#local.featureImageReferenceType#'
			LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
				AND ficus.referenceType = 'swProgramDetail'
			LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes as fics on fics.featureImageSizeID = ficus.featureImageSizeID
			WHERE b.bundleID = <cfqueryparam value="#arguments.bundleID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY bi.itemOrder
		</cfquery>

		<cfset QueryAddColumn(local.qryBundledItems,"displayDate","date",ArrayNew(1))>
		
		<cfif arguments.bundleType eq "SWL">
			<cfloop query="local.qryBundledItems">
				<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(seminarWDDXTimeZones=local.qryBundledItems.wddxTimeZones, orgWDDXTimeZones=arguments.qryAssociation.wddxTimeZones, ifErrStartTime=local.qryBundledItems.dateStart, ifErrEndTime=local.qryBundledItems.dateEnd)>

				<cfif dateDiff("d", local.parsedTime.StartDate, local.parsedTime.EndDate) gt 0>
					<cfset local.displayDate = "#DateFormat(local.parsedTime.StartDate,'ddd, mmm d')# - #DateFormat(local.parsedTime.EndDate,'ddd, mmm d')#">
				<cfelse>
					<cfset local.displayDate = DateFormat(local.parsedTime.StartDate,'ddd, mmmm d')>
				</cfif>

				<cfset QuerySetCell(local.qryBundledItems,"displayDate",local.displayDate,local.qryBundledItems.currentrow)>
			</cfloop>
		</cfif>

		<cfreturn local.qryBundledItems>
	</cffunction>

	<cffunction name="getAllBundlesForInvitation" access="public" returntype="query" output="no">

		<cfset var qryBundles = "">
		
		<cfquery name="qryBundles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT b.bundleID, p.orgcode, b.bundleName, b.bundleSubTitle
			FROM dbo.tblBundles AS b 
			INNER JOIN dbo.tblParticipants AS p ON b.participantID = p.participantID
			INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
			WHERE b.status = 'A'
			ORDER BY p.orgcode, b.bundleName, b.bundleSubTitle;
		</cfquery>
		
		<cfreturn qryBundles>
	</cffunction>

	<cffunction name="getBundleOrder" access="public" output="false" returntype="query">
		<cfargument name="orderID" type="numeric" required="true">

		<cfset var qryBundleOrder = "">

		<cfstoredproc procedure="swb_getBundleOrder" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">
			<cfprocresult name="qryBundleOrder" resultset="1">
		</cfstoredproc>

		<cfreturn qryBundleOrder>
	</cffunction>

	<cffunction name="getBundleOrderTotals" access="public" output="false" returntype="query">
		<cfargument name="orderID" type="numeric" required="true">

		<cfset var qryBundleOrderTotals = "">
		
		<cfquery name="qryBundleOrderTotals" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select sum(dt.amountBilled) as amountBilled, sum(dt.salesTaxAmount) as salesTaxAmount, max(dt.datePurchased) as datePurchased
			from dbo.depoTransactionsApplications as dta
			inner join dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
			where dta.itemType = 'SWBO'
			and dta.itemID = <cfqueryparam value="#arguments.orderID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryBundleOrderTotals>
	</cffunction>

	<cffunction name="removeEnrollment" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="orderID" type="numeric" required="yes">
		<cfargument name="AROption" type="string" required="yes">
		<cfargument name="emailRegistrant" type="boolean" required="yes">

		<cfset var local = structnew()>
		<cfset local.data.success = false>
		<cfset local.data.errmsg = "">

		<cfset local.qryBundleOrder = getBundleOrder(orderID=arguments.orderID)>

		<cfif NOT local.qryBundleOrder.recordCount OR NOT hasUpdateBundleRights(siteID=arguments.mcproxy_siteID, orderID=arguments.orderID, action="removeEnrollment", checkLockSettings=false)>
			<cfset local.data.errmsg = "You do not have rights to this section.">
			<cfreturn local.data>
		</cfif>

		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.qryBundleOrder.signUpOrgCode).qryAssociation>
		<cfif NOT local.qryBundleOrder.handlesOwnPayment>
			<cfset local.qryBundleOrderTotals = getBundleOrderTotals(orderID=arguments.orderID)>
			<cfset local.hasSWRegRefund = arguments.AROption EQ 'A' AND val(local.qryBundleOrderTotals.amountBilled) GT 0>
			<cfif local.hasSWRegRefund>
				<cfset local.data.SWRegRefundAmount = dollarformat(val(local.qryBundleOrderTotals.amountBilled)+val(local.qryBundleOrderTotals.salesTaxAmount))>
			</cfif>
		<cfelse>
			<cfset local.hasSWRegRefund = false>
		</cfif>

		<cfstoredproc procedure="swb_removeBundleOrder" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
		</cfstoredproc>
		
		<cfif arguments.emailRegistrant>
			<cfset local.arrEmailTo = []>
			<cfif len(local.qryBundleOrder.email)>
				<cfset local.arrEmailTo.append({ name="#local.qryBundleOrder.firstName# #local.qryBundleOrder.lastName#", email=local.qryBundleOrder.email })>
			</cfif>

			<cfquery name="local.qryOverrideEmails" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SELECT DISTINCT eao.email
				FROM dbo.tblBundleOrders AS bo
				INNER JOIN dbo.tblEnrollments AS e ON e.bundleOrderID = bo.orderID
				INNER JOIN membercentral.dbo.ams_emailAppOverrides AS eao ON eao.itemID = e.enrollmentID
					AND eao.itemType = 'semwebreg'
				WHERE bo.orderID= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">
			</cfquery>

			<cfloop query="local.qryOverrideEmails">
				<cfif len(local.qryOverrideEmails.email) AND local.qryOverrideEmails.email NEQ local.qryBundleOrder.email>
					<cfset local.arrEmailTo.append({ name="#local.qryBundleOrder.firstName# #local.qryBundleOrder.lastName#", email=local.qryOverrideEmails.email })>
				</cfif>
			</cfloop>

			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryBundleOrder.signUpOrgCode)>

			<cfif arrayLen(local.arrEmailTo)>
				<cfsavecontent variable="local.emailContent">
					<cfoutput>
						#local.qryBundleOrder.firstName# #local.qryBundleOrder.lastName#:<br/><br/>
						This email confirms that your registration for #local.qryBundleOrder.bundleName# has been cancelled and deactivated.<cfif local.hasSWRegRefund> A refund request for #local.data.SWRegRefundAmount# has been sent to our accounting department. Please allow 3 business days for your refund to be processed.</cfif><br/><br/>
						If you have any questions about this cancellation, please contact us at #local.mc_siteInfo.supportProviderEmail#.<br/><br/>
						#local.qryAssociation.emailFrom#<br/>
						#local.qryAssociation.supportPhone#
					</cfoutput>
				</cfsavecontent>
				<cfset local.emailContent = trim(replace(replace(replace(local.emailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

				<cfset local.SeminarWebAdminSRIDEnrollee = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=local.mc_siteInfo.siteID)>

				<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryAssociation.emailFrom, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto=local.mc_siteInfo.supportProviderEmail,
						emailsubject="Registration Cancelled: #local.qryBundleOrder.bundleName#",
						emailtitle="Registration Cancelled: #local.qryBundleOrder.bundleName#",
						emailhtmlcontent=local.emailContent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.qryBundleOrder.MCMemberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREGCANCEL"),
						sendingSiteResourceID=local.SeminarWebAdminSRIDEnrollee
				)>
			</cfif>
		</cfif>
		
		<!--- email SW Support for Refunds --->
		<cfif local.hasSWRegRefund>
			<cfsavecontent variable="local.refundEmailContent">
				<cfoutput>
					This registration has been cancelled and a refund may be applicable.<br/><br/>
					<b>Registration Details</b><br/><hr>
					Registrant Name: #local.qryBundleOrder.firstName# #local.qryBundleOrder.lastName#<br/><br/>
					Program: #local.qryBundleOrder.bundleName# (SWB-#local.qryBundleOrder.bundleID#)<br/><br/>
					DepoMemberDataID: <a href="https://admin.trialsmith.com/TransactionView.cfm?depoMemberDataID=#local.qryBundleOrder.depoMemberDataID#">#local.qryBundleOrder.depoMemberDataID#</a><br/><br/>
					Refund Amount: #local.data.SWRegRefundAmount#
				</cfoutput>
			</cfsavecontent>
			<cfset local.refundEmailContent = trim(replace(replace(replace(local.refundEmailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
			<cfset local.emailtitle = "Refund Request for #local.qryBundleOrder.firstName# #local.qryBundleOrder.lastName# on SWB-#local.qryBundleOrder.bundleID#">

			<cfset local.SWSiteInfo = application.objSiteInfo.getSiteInfo('SW')>

			<cfset application.objEmailWrapper.sendMailESQ(
				emailfrom={ name='SeminarWeb', email='<EMAIL>' },
				emailto=[{ name="", email="<EMAIL>" }],
				emailreplyto="",
				emailsubject=local.emailtitle,
				emailtitle=local.emailtitle,
				emailhtmlcontent=local.refundEmailContent,
				siteID=local.SWSiteInfo.siteID,
				memberID=local.SWSiteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREFUNDREQ"),
				sendingSiteResourceID=local.SWSiteInfo.siteSiteResourceID
			)>
		</cfif>

		<cfset local.data.success = true>
		<cfset local.data.hasSWRegRefund = local.hasSWRegRefund>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasUpdateBundleRights" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="programID" type="numeric" required="false" default="0">
		<cfargument name="orderID" type="numeric" required="false" default="0">
		<cfargument name="checkLockSettings" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cfif arguments.programID eq 0 and arguments.orderID eq 0>
			<cfreturn false>
		</cfif>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWInfo">
			SET NOCOUNT ON;

			DECLARE @siteID int, @siteResourceID int, @siteCode varchar(10), @publisherOrgCode varchar(10), @lockSettings bit,
				@handlesOwnPayment bit, @allowSyndication bit, @isSWOD bit;

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.SeminarWebAdminSRID#">;
			SELECT @siteCode = memberCentral.dbo.fn_getSiteCodeFromSiteID(@siteID);

			<cfif arguments.programID gt 0>
				SELECT @publisherOrgCode = p.orgcode, @isSWOD = b.isSWOD, @lockSettings = b.lockSettings,
					@handlesOwnPayment = p.handlesOwnPayment, @allowSyndication = b.allowSyndication
				FROM dbo.tblBundles AS b 
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = b.participantID
				WHERE b.bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
			<cfelseif arguments.orderID gt 0>
				SELECT @publisherOrgCode = p.orgcode, @isSWOD = b.isSWOD, @lockSettings = b.lockSettings,
					@handlesOwnPayment = p.handlesOwnPayment, @allowSyndication = b.allowSyndication
				FROM dbo.tblBundleOrders AS bo
				INNER JOIN dbo.tblBundles AS b ON b.bundleID = bo.bundleID
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = bo.participantID
				WHERE bo.orderID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">
				AND bo.isActive = 1;
			</cfif>

			SELECT @siteResourceID as semWebAdminSRID, isPublisher = CASE WHEN @publisherOrgCode = @siteCode THEN 1 ELSE 0 END,
				@isSWOD as isSWOD, @lockSettings as lockSettings, @handlesOwnPayment as handlesOwnPayment, @allowSyndication as allowSyndication;
		</cfquery>

		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qrySWInfo.semWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfswitch expression="#arguments.action#">
			<cfcase value="Edit">
				<cfset local.hasRights = (local.tmpRights['editBundleAll'] is 1 OR local.tmpRights['editBundlePublish'] is 1) AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="ManageRates">
				<cfset local.hasRights = local.tmpRights['editBundleAll'] is 1 OR local.tmpRights['editBundlePublish'] is 1>
			</cfcase>
			<cfcase value="manageSWOptIns">
				<cfset local.hasRights = local.tmpRights['manageSWOptIns'] is 1 AND val(local.qrySWInfo.handlesOwnPayment) EQ 0>
			</cfcase>
			<cfcase value="catalog">
				<cfset local.hasRights = (local.tmpRights['editBundleAll'] is 1 OR local.tmpRights['editBundlePublish'] is 1)>
			</cfcase>
			<cfcase value="removeEnrollment">
				<cfif local.qrySWInfo.isSWOD>
					<cfset local.hasRights = (local.tmpRights['deleteSWODRegistrantSignUp'] is 1 AND local.qrySWInfo.isPublisher) OR local.tmpRights['deleteSWODRegistrantAll'] is 1>
				<cfelse>
					<cfset local.hasRights = (local.tmpRights['deleteSWLRegistrantSignUp'] is 1 AND local.qrySWInfo.isPublisher) OR local.tmpRights['deleteSWLRegistrantAll'] is 1>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfreturn local.hasRights AND (arguments.checkLockSettings ? NOT local.qrySWInfo.lockSettings : true)>
	</cffunction>

</cfcomponent>