(window.webpackJsonp = window.webpackJsonp || []).push([
	[108],
	{
		1366: function (_, t, e) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					e = {
						name: "ar-ly",
						weekdays:
							"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),
						months:
							"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split(
								"_",
							),
						weekStart: 6,
						weekdaysShort: "أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),
						monthsShort:
							"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split(
								"_",
							),
						weekdaysMin: "ح_ن_ث_ر_خ_ج_س".split("_"),
						ordinal: function (_) {
							return _;
						},
						meridiem: function (_) {
							return _ > 12 ? "ص" : "م";
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "D/‏M/‏YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY HH:mm",
							LLLL: "dddd D MMMM YYYY HH:mm",
						},
					};
				return t.default.locale(e, null, !0), e;
			})(e(105));
		},
	},
]);
//# sourceMappingURL=chunk.108.js.map
