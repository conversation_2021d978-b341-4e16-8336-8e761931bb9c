var Module = typeof Module != "undefined" ? Module : {};
if (!Module.expectedDataFileDownloads) {
	Module.expectedDataFileDownloads = 0;
}
Module.expectedDataFileDownloads++;
(function () {
	if (Module["ENVIRONMENT_IS_PTHREAD"]) return;
	var loadPackage = function (metadata) {
		var PACKAGE_PATH = "";
		if (typeof window === "object") {
			PACKAGE_PATH = window["encodeURIComponent"](
				window.location.pathname
					.toString()
					.substring(0, window.location.pathname.toString().lastIndexOf("/")) +
					"/",
			);
		} else if (
			typeof process === "undefined" &&
			typeof location !== "undefined"
		) {
			PACKAGE_PATH = encodeURIComponent(
				location.pathname
					.toString()
					.substring(0, location.pathname.toString().lastIndexOf("/")) + "/",
			);
		}
		var PACKAGE_NAME = "../output/exe/InfixServerWasm.mem";
		var REMOTE_PACKAGE_BASE = "InfixServerWasm.mem";
		if (
			typeof Module["locateFilePackage"] === "function" &&
			!Module["locateFile"]
		) {
			Module["locateFile"] = Module["locateFilePackage"];
			err(
				"warning: you defined Module.locateFilePackage, that has been renamed to Module.locateFile (using your locateFilePackage for now)",
			);
		}
		var REMOTE_PACKAGE_NAME = Module["locateFile"]
			? Module["locateFile"](REMOTE_PACKAGE_BASE, "")
			: REMOTE_PACKAGE_BASE;
		var REMOTE_PACKAGE_SIZE = metadata["remote_package_size"];
		function fetchRemotePackage(packageName, packageSize, callback, errback) {
			var xhr = new XMLHttpRequest();
			xhr.open("GET", packageName, true);
			xhr.responseType = "arraybuffer";
			xhr.onprogress = function (event) {
				var url = packageName;
				var size = packageSize;
				if (event.total) size = event.total;
				if (event.loaded) {
					if (!xhr.addedTotal) {
						xhr.addedTotal = true;
						if (!Module.dataFileDownloads) Module.dataFileDownloads = {};
						Module.dataFileDownloads[url] = {
							loaded: event.loaded,
							total: size,
						};
					} else {
						Module.dataFileDownloads[url].loaded = event.loaded;
					}
					var total = 0;
					var loaded = 0;
					var num = 0;
					for (var download in Module.dataFileDownloads) {
						var data = Module.dataFileDownloads[download];
						total += data.total;
						loaded += data.loaded;
						num++;
					}
					total = Math.ceil((total * Module.expectedDataFileDownloads) / num);
					if (Module["setStatus"])
						Module["setStatus"](
							"Downloading data... (" + loaded + "/" + total + ")",
						);
				} else if (!Module.dataFileDownloads) {
					if (Module["setStatus"]) Module["setStatus"]("Downloading data...");
				}
			};
			xhr.onerror = function (event) {
				throw new Error("NetworkError for: " + packageName);
			};
			xhr.onload = function (event) {
				if (
					xhr.status == 200 ||
					xhr.status == 304 ||
					xhr.status == 206 ||
					(xhr.status == 0 && xhr.response)
				) {
					var packageData = xhr.response;
					callback(packageData);
				} else {
					throw new Error(xhr.statusText + " : " + xhr.responseURL);
				}
			};
			xhr.send(null);
		}
		function handleError(error) {
			console.error("package error:", error);
		}
		var fetchedCallback = null;
		var fetched = Module["getPreloadedPackage"]
			? Module["getPreloadedPackage"](REMOTE_PACKAGE_NAME, REMOTE_PACKAGE_SIZE)
			: null;
		if (!fetched)
			fetchRemotePackage(
				REMOTE_PACKAGE_NAME,
				REMOTE_PACKAGE_SIZE,
				function (data) {
					if (fetchedCallback) {
						fetchedCallback(data);
						fetchedCallback = null;
					} else {
						fetched = data;
					}
				},
				handleError,
			);
		function runWithFS() {
			function assert(check, msg) {
				if (!check) throw msg + new Error().stack;
			}
			Module["FS_createPath"]("/", "cmaps", true, true);
			Module["FS_createPath"]("/", "fonts", true, true);
			Module["FS_createPath"]("/", "hyphenation", true, true);
			Module["FS_createPath"]("/", "profiles", true, true);
			Module["FS_createPath"]("/", "sysfonts", true, true);
			Module["FS_createPath"]("/", "unimaps", true, true);
			function DataRequest(start, end, audio) {
				this.start = start;
				this.end = end;
				this.audio = audio;
			}
			DataRequest.prototype = {
				requests: {},
				open: function (mode, name) {
					this.name = name;
					this.requests[name] = this;
					Module["addRunDependency"]("fp " + this.name);
				},
				send: function () {},
				onload: function () {
					var byteArray = this.byteArray.subarray(this.start, this.end);
					this.finish(byteArray);
				},
				finish: function (byteArray) {
					var that = this;
					Module["FS_createDataFile"](
						this.name,
						null,
						byteArray,
						true,
						true,
						true,
					);
					Module["removeRunDependency"]("fp " + that.name);
					this.requests[this.name] = null;
				},
			};
			var files = metadata["files"];
			for (var i = 0; i < files.length; ++i) {
				new DataRequest(
					files[i]["start"],
					files[i]["end"],
					files[i]["audio"] || 0,
				).open("GET", files[i]["filename"]);
			}
			function processPackageData(arrayBuffer) {
				assert(arrayBuffer, "Loading data file failed.");
				assert(
					arrayBuffer instanceof ArrayBuffer,
					"bad input to processPackageData",
				);
				var byteArray = new Uint8Array(arrayBuffer);
				DataRequest.prototype.byteArray = byteArray;
				var files = metadata["files"];
				for (var i = 0; i < files.length; ++i) {
					DataRequest.prototype.requests[files[i].filename].onload();
				}
				Module["removeRunDependency"](
					"datafile_../output/exe/InfixServerWasm.mem",
				);
			}
			Module["addRunDependency"]("datafile_../output/exe/InfixServerWasm.mem");
			if (!Module.preloadResults) Module.preloadResults = {};
			Module.preloadResults[PACKAGE_NAME] = { fromCache: false };
			if (fetched) {
				processPackageData(fetched);
				fetched = null;
			} else {
				fetchedCallback = processPackageData;
			}
		}
		if (Module["calledRun"]) {
			runWithFS();
		} else {
			if (!Module["preRun"]) Module["preRun"] = [];
			Module["preRun"].push(runWithFS);
		}
	};
	loadPackage({
		files: [
			{ filename: "/autotransXML.cfg", start: 0, end: 2901 },
			{ filename: "/coreTransXML.cfg", start: 2901, end: 5204 },
			{ filename: "/fieldXML.cfg", start: 5204, end: 7316 },
			{ filename: "/infixcore.cfg", start: 7316, end: 8477 },
			{ filename: "/New Document.pdf", start: 8477, end: 14555 },
			{ filename: "/transXML.cfg", start: 14555, end: 17841 },
			{ filename: "/cmaps/83pv-RKSJ-H", start: 17841, end: 23758 },
			{ filename: "/cmaps/90ms-RKSJ-H", start: 23758, end: 28665 },
			{ filename: "/cmaps/90ms-RKSJ-UCS2", start: 28665, end: 133695 },
			{ filename: "/cmaps/90ms-RKSJ-V", start: 133695, end: 136761 },
			{ filename: "/cmaps/90msp-RKSJ-H", start: 136761, end: 141599 },
			{ filename: "/cmaps/90msp-RKSJ-V", start: 141599, end: 144648 },
			{ filename: "/cmaps/90pv-RKSJ-H", start: 144648, end: 151300 },
			{ filename: "/cmaps/90pv-RKSJ-UCS2", start: 151300, end: 153391 },
			{ filename: "/cmaps/90pv-RKSJ-UCS2C", start: 153391, end: 249519 },
			{ filename: "/cmaps/Add-RKSJ-H", start: 249519, end: 263394 },
			{ filename: "/cmaps/Add-RKSJ-V", start: 263394, end: 266054 },
			{ filename: "/cmaps/Adobe-CNS1-UCS2", start: 266054, end: 492640 },
			{ filename: "/cmaps/Adobe-GB1-UCS2", start: 492640, end: 709091 },
			{ filename: "/cmaps/Adobe-Japan1-UCS2", start: 709091, end: 891290 },
			{ filename: "/cmaps/Adobe-Korea1-UCS2", start: 891290, end: 1036938 },
			{ filename: "/cmaps/B5pc-H", start: 1036938, end: 1043322 },
			{ filename: "/cmaps/B5pc-UCS2", start: 1043322, end: 1044806 },
			{ filename: "/cmaps/B5pc-UCS2C", start: 1044806, end: 1316588 },
			{ filename: "/cmaps/B5pc-V", start: 1316588, end: 1318361 },
			{ filename: "/cmaps/CNS-EUC-H", start: 1318361, end: 1329444 },
			{ filename: "/cmaps/CNS-EUC-V", start: 1329444, end: 1341559 },
			{ filename: "/cmaps/ecnames.txt", start: 1341559, end: 1344798 },
			{ filename: "/cmaps/ETen-B5-H", start: 1344798, end: 1351338 },
			{ filename: "/cmaps/ETen-B5-UCS2", start: 1351338, end: 1626800 },
			{ filename: "/cmaps/ETen-B5-V", start: 1626800, end: 1628611 },
			{ filename: "/cmaps/ETenms-B5-H", start: 1628611, end: 1630070 },
			{ filename: "/cmaps/ETenms-B5-V", start: 1630070, end: 1631936 },
			{ filename: "/cmaps/EUC-H", start: 1631936, end: 1635848 },
			{ filename: "/cmaps/EUC-V", start: 1635848, end: 1637908 },
			{ filename: "/cmaps/Ext-RKSJ-H", start: 1637908, end: 1652352 },
			{ filename: "/cmaps/Ext-RKSJ-V", start: 1652352, end: 1654670 },
			{ filename: "/cmaps/GB-EUC-H", start: 1654670, end: 1657934 },
			{ filename: "/cmaps/GB-EUC-V", start: 1657934, end: 1659871 },
			{ filename: "/cmaps/GBK-EUC-H", start: 1659871, end: 1741810 },
			{ filename: "/cmaps/GBK-EUC-UCS2", start: 1741810, end: 1964894 },
			{ filename: "/cmaps/GBK-EUC-V", start: 1964894, end: 1966820 },
			{ filename: "/cmaps/GBpc-EUC-H", start: 1966820, end: 1970112 },
			{ filename: "/cmaps/GBpc-EUC-UCS2", start: 1970112, end: 1971678 },
			{ filename: "/cmaps/GBpc-EUC-UCS2C", start: 1971678, end: 2113271 },
			{ filename: "/cmaps/GBpc-EUC-V", start: 2113271, end: 2115220 },
			{ filename: "/cmaps/GBT-EUC-H", start: 2115220, end: 2160830 },
			{ filename: "/cmaps/GBT-EUC-V", start: 2160830, end: 2162775 },
			{ filename: "/cmaps/H", start: 2162775, end: 2166553 },
			{ filename: "/cmaps/iceni-macroman", start: 2166553, end: 2176340 },
			{ filename: "/cmaps/iceni-winansii", start: 2176340, end: 2182814 },
			{ filename: "/cmaps/Identity-H", start: 2182814, end: 2189214 },
			{ filename: "/cmaps/Identity-V", start: 2189214, end: 2190413 },
			{ filename: "/cmaps/KSC-EUC-H", start: 2190413, end: 2200978 },
			{ filename: "/cmaps/KSC-EUC-V", start: 2200978, end: 2202851 },
			{ filename: "/cmaps/KSCms-UHC-H", start: 2202851, end: 2217628 },
			{ filename: "/cmaps/KSCms-UHC-HW-H", start: 2217628, end: 2232401 },
			{ filename: "/cmaps/KSCms-UHC-HW-V", start: 2232401, end: 2234287 },
			{ filename: "/cmaps/KSCms-UHC-UCS2", start: 2234287, end: 2415684 },
			{ filename: "/cmaps/KSCms-UHC-V", start: 2415684, end: 2417571 },
			{ filename: "/cmaps/KSCpc-EUC-H", start: 2417571, end: 2428963 },
			{ filename: "/cmaps/KSCpc-EUC-UCS2C", start: 2428963, end: 2578861 },
			{ filename: "/cmaps/KSCpc-EUC-V", start: 2578861, end: 2580746 },
			{ filename: "/cmaps/ReadMe.html", start: 2580746, end: 2583451 },
			{ filename: "/cmaps/UniCNS-UCS2-H", start: 2583451, end: 2889027 },
			{ filename: "/cmaps/UniCNS-UCS2-V", start: 2889027, end: 2890835 },
			{ filename: "/cmaps/UniGB-UCS2-H", start: 2890835, end: 3156817 },
			{ filename: "/cmaps/UniGB-UCS2-V", start: 3156817, end: 3158821 },
			{ filename: "/cmaps/UniJIS-UCS2-H", start: 3158821, end: 3326255 },
			{ filename: "/cmaps/UniJIS-UCS2-HW-H", start: 3326255, end: 3327912 },
			{ filename: "/cmaps/UniJIS-UCS2-HW-V", start: 3327912, end: 3333340 },
			{ filename: "/cmaps/UniJIS-UCS2-V", start: 3333340, end: 3338686 },
			{ filename: "/cmaps/UniJIS-UTF16-H", start: 3338686, end: 3538708 },
			{ filename: "/cmaps/UniJIS-UTF16-V", start: 3538708, end: 3543519 },
			{ filename: "/cmaps/UniKS-UCS2-H", start: 3543519, end: 3708365 },
			{ filename: "/cmaps/UniKS-UCS2-V", start: 3708365, end: 3710278 },
			{ filename: "/cmaps/V", start: 3710278, end: 3712314 },
			{ filename: "/fonts/a010013l.pfb", start: 3712314, end: 3738308 },
			{ filename: "/fonts/a010015l.pfb", start: 3738308, end: 3765288 },
			{ filename: "/fonts/a010033l.pfb", start: 3765288, end: 3792170 },
			{ filename: "/fonts/a010035l.pfb", start: 3792170, end: 3819700 },
			{ filename: "/fonts/b018012l.pfb", start: 3819700, end: 3855222 },
			{ filename: "/fonts/b018015l.pfb", start: 3855222, end: 3890675 },
			{ filename: "/fonts/b018032l.pfb", start: 3890675, end: 3925292 },
			{ filename: "/fonts/b018035l.pfb", start: 3925292, end: 3960510 },
			{ filename: "/fonts/c059013l.pfb", start: 3960510, end: 3998071 },
			{ filename: "/fonts/c059016l.pfb", start: 3998071, end: 4036834 },
			{ filename: "/fonts/c059033l.pfb", start: 4036834, end: 4075247 },
			{ filename: "/fonts/c059036l.pfb", start: 4075247, end: 4113989 },
			{ filename: "/fonts/d050000l.pfb", start: 4113989, end: 4158526 },
			{ filename: "/fonts/fontFamilyNames.txt", start: 4158526, end: 4159054 },
			{ filename: "/fonts/fonts.dir", start: 4159054, end: 4161469 },
			{ filename: "/fonts/n019003l.pfb", start: 4161469, end: 4188974 },
			{ filename: "/fonts/n019004l.pfb", start: 4188974, end: 4215736 },
			{ filename: "/fonts/n019023l.pfb", start: 4215736, end: 4244133 },
			{ filename: "/fonts/n019024l.pfb", start: 4244133, end: 4273104 },
			{ filename: "/fonts/n019043l.pfb", start: 4273104, end: 4300510 },
			{ filename: "/fonts/n019044l.pfb", start: 4300510, end: 4328470 },
			{ filename: "/fonts/n019063l.pfb", start: 4328470, end: 4356768 },
			{ filename: "/fonts/n019064l.pfb", start: 4356768, end: 4386152 },
			{ filename: "/fonts/n021003l.pfb", start: 4386152, end: 4421313 },
			{ filename: "/fonts/n021004l.pfb", start: 4421313, end: 4456309 },
			{ filename: "/fonts/n021023l.pfb", start: 4456309, end: 4493448 },
			{ filename: "/fonts/n021024l.pfb", start: 4493448, end: 4529773 },
			{ filename: "/fonts/n022003l.pfb", start: 4529773, end: 4565492 },
			{ filename: "/fonts/n022004l.pfb", start: 4565492, end: 4603776 },
			{ filename: "/fonts/n022023l.pfb", start: 4603776, end: 4638453 },
			{ filename: "/fonts/n022024l.pfb", start: 4638453, end: 4679139 },
			{ filename: "/fonts/p052003l.pfb", start: 4679139, end: 4721467 },
			{ filename: "/fonts/p052004l.pfb", start: 4721467, end: 4763058 },
			{ filename: "/fonts/p052023l.pfb", start: 4763058, end: 4802975 },
			{ filename: "/fonts/p052024l.pfb", start: 4802975, end: 4843362 },
			{ filename: "/fonts/s050000l.pfb", start: 4843362, end: 4875575 },
			{ filename: "/fonts/z003034l.pfb", start: 4875575, end: 4913997 },
			{ filename: "/hyphenation/hyph-bg.tex", start: 4913997, end: 4929937 },
			{ filename: "/hyphenation/hyph-ca.tex", start: 4929937, end: 4942131 },
			{ filename: "/hyphenation/hyph-cop.tex", start: 4942131, end: 4951831 },
			{ filename: "/hyphenation/hyph-cs.tex", start: 4951831, end: 4981387 },
			{ filename: "/hyphenation/hyph-cy.tex", start: 4981387, end: 5034044 },
			{ filename: "/hyphenation/hyph-da.tex", start: 5034044, end: 5044683 },
			{
				filename: "/hyphenation/hyph-de-1901.tex",
				start: 5044683,
				end: 5154374,
			},
			{
				filename: "/hyphenation/hyph-de-1996.tex",
				start: 5154374,
				end: 5265369,
			},
			{
				filename: "/hyphenation/hyph-el-monoton.tex",
				start: 5265369,
				end: 5273940,
			},
			{
				filename: "/hyphenation/hyph-el-polyton.tex",
				start: 5273940,
				end: 5294575,
			},
			{ filename: "/hyphenation/hyph-en-gb.tex", start: 5294575, end: 5360630 },
			{ filename: "/hyphenation/hyph-en-us.tex", start: 5360630, end: 5399209 },
			{ filename: "/hyphenation/hyph-eo.tex", start: 5399209, end: 5417328 },
			{ filename: "/hyphenation/hyph-es.tex", start: 5417328, end: 5447494 },
			{ filename: "/hyphenation/hyph-et.tex", start: 5447494, end: 5477994 },
			{ filename: "/hyphenation/hyph-eu.tex", start: 5477994, end: 5481069 },
			{ filename: "/hyphenation/hyph-fi.tex", start: 5481069, end: 5488486 },
			{ filename: "/hyphenation/hyph-fr.tex", start: 5488486, end: 5522215 },
			{ filename: "/hyphenation/hyph-ga.tex", start: 5522215, end: 5576774 },
			{ filename: "/hyphenation/hyph-gl.tex", start: 5576774, end: 5598669 },
			{ filename: "/hyphenation/hyph-grc.tex", start: 5598669, end: 5682298 },
			{ filename: "/hyphenation/hyph-hr.tex", start: 5682298, end: 5694555 },
			{ filename: "/hyphenation/hyph-hsb.tex", start: 5694555, end: 5710083 },
			{ filename: "/hyphenation/hyph-hu.tex", start: 5710083, end: 6303226 },
			{ filename: "/hyphenation/hyph-ia.tex", start: 6303226, end: 6311184 },
			{ filename: "/hyphenation/hyph-id.tex", start: 6311184, end: 6317413 },
			{ filename: "/hyphenation/hyph-is.tex", start: 6317413, end: 6351382 },
			{ filename: "/hyphenation/hyph-it.tex", start: 6351382, end: 6360233 },
			{ filename: "/hyphenation/hyph-la.tex", start: 6360233, end: 6371054 },
			{ filename: "/hyphenation/hyph-lt.tex", start: 6371054, end: 6382268 },
			{
				filename: "/hyphenation/hyph-mn-cyrl-x-2a.tex",
				start: 6382268,
				end: 6399540,
			},
			{
				filename: "/hyphenation/hyph-mn-cyrl.tex",
				start: 6399540,
				end: 6408347,
			},
			{ filename: "/hyphenation/hyph-nb.tex", start: 6408347, end: 6409406 },
			{ filename: "/hyphenation/hyph-nl.tex", start: 6409406, end: 6511950 },
			{ filename: "/hyphenation/hyph-nn.tex", start: 6511950, end: 6513011 },
			{ filename: "/hyphenation/hyph-no.tex", start: 6513011, end: 6735838 },
			{ filename: "/hyphenation/hyph-pl.tex", start: 6735838, end: 6774563 },
			{ filename: "/hyphenation/hyph-pt.tex", start: 6774563, end: 6780387 },
			{ filename: "/hyphenation/hyph-ro.tex", start: 6780387, end: 6789522 },
			{ filename: "/hyphenation/hyph-ru.tex", start: 6789522, end: 6846902 },
			{ filename: "/hyphenation/hyph-sa.tex", start: 6846902, end: 6855652 },
			{
				filename: "/hyphenation/hyph-sh-cyrl.tex",
				start: 6855652,
				end: 6892089,
			},
			{
				filename: "/hyphenation/hyph-sh-latn.tex",
				start: 6892089,
				end: 6918789,
			},
			{ filename: "/hyphenation/hyph-sk.tex", start: 6918789, end: 6945072 },
			{ filename: "/hyphenation/hyph-sl.tex", start: 6945072, end: 6956212 },
			{
				filename: "/hyphenation/hyph-sr-cyrl.tex",
				start: 6956212,
				end: 6994566,
			},
			{ filename: "/hyphenation/hyph-sv.tex", start: 6994566, end: 7034079 },
			{ filename: "/hyphenation/hyph-tr.tex", start: 7034079, end: 7038969 },
			{ filename: "/hyphenation/hyph-uk.tex", start: 7038969, end: 7067526 },
			{
				filename: "/hyphenation/hyph-zh-latn.tex",
				start: 7067526,
				end: 7072367,
			},
			{
				filename: "/profiles/USWebCoatedSWOP.icc",
				start: 7072367,
				end: 7629535,
			},
			{ filename: "/sysfonts/NotoSans-Bold.ttf", start: 7629535, end: 7943327 },
			{
				filename: "/sysfonts/NotoSans-BoldItalic.ttf",
				start: 7943327,
				end: 8268003,
			},
			{
				filename: "/sysfonts/NotoSans-Italic.ttf",
				start: 8268003,
				end: 8594007,
			},
			{
				filename: "/sysfonts/NotoSans-Regular.ttf",
				start: 8594007,
				end: 8907151,
			},
			{ filename: "/unimaps/unifont", start: 8907151, end: 9243780 },
		],
		remote_package_size: 9243780,
	});
})();
var moduleOverrides = Object.assign({}, Module);
var arguments_ = [];
var thisProgram = "./this.program";
var quit_ = (status, toThrow) => {
	throw toThrow;
};
var ENVIRONMENT_IS_WEB = true;
var ENVIRONMENT_IS_WORKER = false;
var scriptDirectory = "";
function locateFile(path) {
	if (Module["locateFile"]) {
		return Module["locateFile"](path, scriptDirectory);
	}
	return scriptDirectory + path;
}
var read_, readAsync, readBinary, setWindowTitle;
if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
	if (ENVIRONMENT_IS_WORKER) {
		scriptDirectory = self.location.href;
	} else if (typeof document != "undefined" && document.currentScript) {
		scriptDirectory = document.currentScript.src;
	}
	if (scriptDirectory.indexOf("blob:") !== 0) {
		scriptDirectory = scriptDirectory.substr(
			0,
			scriptDirectory.replace(/[?#].*/, "").lastIndexOf("/") + 1,
		);
	} else {
		scriptDirectory = "";
	}
	{
		read_ = (url) => {
			var xhr = new XMLHttpRequest();
			xhr.open("GET", url, false);
			xhr.send(null);
			return xhr.responseText;
		};
		if (ENVIRONMENT_IS_WORKER) {
			readBinary = (url) => {
				var xhr = new XMLHttpRequest();
				xhr.open("GET", url, false);
				xhr.responseType = "arraybuffer";
				xhr.send(null);
				return new Uint8Array(xhr.response);
			};
		}
		readAsync = (url, onload, onerror) => {
			var xhr = new XMLHttpRequest();
			xhr.open("GET", url, true);
			xhr.responseType = "arraybuffer";
			xhr.onload = () => {
				if (xhr.status == 200 || (xhr.status == 0 && xhr.response)) {
					onload(xhr.response);
					return;
				}
				onerror();
			};
			xhr.onerror = onerror;
			xhr.send(null);
		};
	}
	setWindowTitle = (title) => (document.title = title);
} else {
}
var out = Module["print"] || console.log.bind(console);
var err = Module["printErr"] || console.warn.bind(console);
Object.assign(Module, moduleOverrides);
moduleOverrides = null;
if (Module["arguments"]) arguments_ = Module["arguments"];
if (Module["thisProgram"]) thisProgram = Module["thisProgram"];
if (Module["quit"]) quit_ = Module["quit"];
var tempRet0 = 0;
var setTempRet0 = (value) => {
	tempRet0 = value;
};
var getTempRet0 = () => tempRet0;
var wasmBinary;
if (Module["wasmBinary"]) wasmBinary = Module["wasmBinary"];
var noExitRuntime = Module["noExitRuntime"] || true;
if (typeof WebAssembly != "object") {
	abort("no native wasm support detected");
}
var wasmMemory;
var ABORT = false;
var EXITSTATUS;
function assert(condition, text) {
	if (!condition) {
		abort(text);
	}
}
var UTF8Decoder =
	typeof TextDecoder != "undefined" ? new TextDecoder("utf8") : undefined;
function UTF8ArrayToString(heapOrArray, idx, maxBytesToRead) {
	var endIdx = idx + maxBytesToRead;
	var endPtr = idx;
	while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr;
	if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
		return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));
	}
	var str = "";
	while (idx < endPtr) {
		var u0 = heapOrArray[idx++];
		if (!(u0 & 128)) {
			str += String.fromCharCode(u0);
			continue;
		}
		var u1 = heapOrArray[idx++] & 63;
		if ((u0 & 224) == 192) {
			str += String.fromCharCode(((u0 & 31) << 6) | u1);
			continue;
		}
		var u2 = heapOrArray[idx++] & 63;
		if ((u0 & 240) == 224) {
			u0 = ((u0 & 15) << 12) | (u1 << 6) | u2;
		} else {
			u0 =
				((u0 & 7) << 18) | (u1 << 12) | (u2 << 6) | (heapOrArray[idx++] & 63);
		}
		if (u0 < 65536) {
			str += String.fromCharCode(u0);
		} else {
			var ch = u0 - 65536;
			str += String.fromCharCode(55296 | (ch >> 10), 56320 | (ch & 1023));
		}
	}
	return str;
}
function UTF8ToString(ptr, maxBytesToRead) {
	return ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "";
}
function stringToUTF8Array(str, heap, outIdx, maxBytesToWrite) {
	if (!(maxBytesToWrite > 0)) return 0;
	var startIdx = outIdx;
	var endIdx = outIdx + maxBytesToWrite - 1;
	for (var i = 0; i < str.length; ++i) {
		var u = str.charCodeAt(i);
		if (u >= 55296 && u <= 57343) {
			var u1 = str.charCodeAt(++i);
			u = (65536 + ((u & 1023) << 10)) | (u1 & 1023);
		}
		if (u <= 127) {
			if (outIdx >= endIdx) break;
			heap[outIdx++] = u;
		} else if (u <= 2047) {
			if (outIdx + 1 >= endIdx) break;
			heap[outIdx++] = 192 | (u >> 6);
			heap[outIdx++] = 128 | (u & 63);
		} else if (u <= 65535) {
			if (outIdx + 2 >= endIdx) break;
			heap[outIdx++] = 224 | (u >> 12);
			heap[outIdx++] = 128 | ((u >> 6) & 63);
			heap[outIdx++] = 128 | (u & 63);
		} else {
			if (outIdx + 3 >= endIdx) break;
			heap[outIdx++] = 240 | (u >> 18);
			heap[outIdx++] = 128 | ((u >> 12) & 63);
			heap[outIdx++] = 128 | ((u >> 6) & 63);
			heap[outIdx++] = 128 | (u & 63);
		}
	}
	heap[outIdx] = 0;
	return outIdx - startIdx;
}
function stringToUTF8(str, outPtr, maxBytesToWrite) {
	return stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite);
}
function lengthBytesUTF8(str) {
	var len = 0;
	for (var i = 0; i < str.length; ++i) {
		var c = str.charCodeAt(i);
		if (c <= 127) {
			len++;
		} else if (c <= 2047) {
			len += 2;
		} else if (c >= 55296 && c <= 57343) {
			len += 4;
			++i;
		} else {
			len += 3;
		}
	}
	return len;
}
var buffer, HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64;
function updateGlobalBufferAndViews(buf) {
	buffer = buf;
	Module["HEAP8"] = HEAP8 = new Int8Array(buf);
	Module["HEAP16"] = HEAP16 = new Int16Array(buf);
	Module["HEAP32"] = HEAP32 = new Int32Array(buf);
	Module["HEAPU8"] = HEAPU8 = new Uint8Array(buf);
	Module["HEAPU16"] = HEAPU16 = new Uint16Array(buf);
	Module["HEAPU32"] = HEAPU32 = new Uint32Array(buf);
	Module["HEAPF32"] = HEAPF32 = new Float32Array(buf);
	Module["HEAPF64"] = HEAPF64 = new Float64Array(buf);
}
var INITIAL_MEMORY = Module["INITIAL_MEMORY"] || 62914560;
var wasmTable;
var __ATPRERUN__ = [];
var __ATINIT__ = [];
var __ATMAIN__ = [];
var __ATPOSTRUN__ = [];
var runtimeInitialized = false;
function keepRuntimeAlive() {
	return noExitRuntime;
}
function preRun() {
	if (Module["preRun"]) {
		if (typeof Module["preRun"] == "function")
			Module["preRun"] = [Module["preRun"]];
		while (Module["preRun"].length) {
			addOnPreRun(Module["preRun"].shift());
		}
	}
	callRuntimeCallbacks(__ATPRERUN__);
}
function initRuntime() {
	runtimeInitialized = true;
	if (!Module["noFSInit"] && !FS.init.initialized) FS.init();
	FS.ignorePermissions = false;
	TTY.init();
	callRuntimeCallbacks(__ATINIT__);
}
function preMain() {
	callRuntimeCallbacks(__ATMAIN__);
}
function postRun() {
	if (Module["postRun"]) {
		if (typeof Module["postRun"] == "function")
			Module["postRun"] = [Module["postRun"]];
		while (Module["postRun"].length) {
			addOnPostRun(Module["postRun"].shift());
		}
	}
	callRuntimeCallbacks(__ATPOSTRUN__);
}
function addOnPreRun(cb) {
	__ATPRERUN__.unshift(cb);
}
function addOnInit(cb) {
	__ATINIT__.unshift(cb);
}
function addOnPostRun(cb) {
	__ATPOSTRUN__.unshift(cb);
}
var runDependencies = 0;
var runDependencyWatcher = null;
var dependenciesFulfilled = null;
function getUniqueRunDependency(id) {
	return id;
}
function addRunDependency(id) {
	runDependencies++;
	if (Module["monitorRunDependencies"]) {
		Module["monitorRunDependencies"](runDependencies);
	}
}
function removeRunDependency(id) {
	runDependencies--;
	if (Module["monitorRunDependencies"]) {
		Module["monitorRunDependencies"](runDependencies);
	}
	if (runDependencies == 0) {
		if (runDependencyWatcher !== null) {
			clearInterval(runDependencyWatcher);
			runDependencyWatcher = null;
		}
		if (dependenciesFulfilled) {
			var callback = dependenciesFulfilled;
			dependenciesFulfilled = null;
			callback();
		}
	}
}
function abort(what) {
	{
		if (Module["onAbort"]) {
			Module["onAbort"](what);
		}
	}
	what = "Aborted(" + what + ")";
	err(what);
	ABORT = true;
	EXITSTATUS = 1;
	what += ". Build with -sASSERTIONS for more info.";
	var e = new WebAssembly.RuntimeError(what);
	throw e;
}
var dataURIPrefix = "data:application/octet-stream;base64,";
function isDataURI(filename) {
	return filename.startsWith(dataURIPrefix);
}
var wasmBinaryFile;
wasmBinaryFile = "InfixServerWasm.wasm";
if (!isDataURI(wasmBinaryFile)) {
	wasmBinaryFile = locateFile(wasmBinaryFile);
}
function getBinary(file) {
	try {
		if (file == wasmBinaryFile && wasmBinary) {
			return new Uint8Array(wasmBinary);
		}
		if (readBinary) {
			return readBinary(file);
		}
		throw "both async and sync fetching of the wasm failed";
	} catch (err) {
		abort(err);
	}
}
function getBinaryPromise() {
	if (!wasmBinary && (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER)) {
		if (typeof fetch == "function") {
			return fetch(wasmBinaryFile, { credentials: "same-origin" })
				.then(function (response) {
					if (!response["ok"]) {
						throw "failed to load wasm binary file at '" + wasmBinaryFile + "'";
					}
					return response["arrayBuffer"]();
				})
				.catch(function () {
					return getBinary(wasmBinaryFile);
				});
		}
	}
	return Promise.resolve().then(function () {
		return getBinary(wasmBinaryFile);
	});
}
function createWasm() {
	var info = { a: asmLibraryArg };
	function receiveInstance(instance, module) {
		var exports = instance.exports;
		Module["asm"] = exports;
		wasmMemory = Module["asm"]["Ba"];
		updateGlobalBufferAndViews(wasmMemory.buffer);
		wasmTable = Module["asm"]["Ra"];
		addOnInit(Module["asm"]["Ca"]);
		removeRunDependency("wasm-instantiate");
	}
	addRunDependency("wasm-instantiate");
	function receiveInstantiationResult(result) {
		receiveInstance(result["instance"]);
	}
	function instantiateArrayBuffer(receiver) {
		return getBinaryPromise()
			.then(function (binary) {
				return WebAssembly.instantiate(binary, info);
			})
			.then(function (instance) {
				return instance;
			})
			.then(receiver, function (reason) {
				err("failed to asynchronously prepare wasm: " + reason);
				abort(reason);
			});
	}
	function instantiateAsync() {
		if (
			!wasmBinary &&
			typeof WebAssembly.instantiateStreaming == "function" &&
			!isDataURI(wasmBinaryFile) &&
			typeof fetch == "function"
		) {
			return fetch(wasmBinaryFile, { credentials: "same-origin" }).then(
				function (response) {
					var result = WebAssembly.instantiateStreaming(response, info);
					return result.then(receiveInstantiationResult, function (reason) {
						err("wasm streaming compile failed: " + reason);
						err("falling back to ArrayBuffer instantiation");
						return instantiateArrayBuffer(receiveInstantiationResult);
					});
				},
			);
		} else {
			return instantiateArrayBuffer(receiveInstantiationResult);
		}
	}
	if (Module["instantiateWasm"]) {
		try {
			var exports = Module["instantiateWasm"](info, receiveInstance);
			return exports;
		} catch (e) {
			err("Module.instantiateWasm callback failed with error: " + e);
			return false;
		}
	}
	instantiateAsync();
	return {};
}
var tempDouble;
var tempI64;
var ASM_CONSTS = {
	3836092: ($0, $1, $2, $3) => {
		var xhr = new XMLHttpRequest();
		var url = Module.UTF8ToString($0);
		xhr.open("GET", url, false);
		xhr.responseType = "arraybuffer";
		if ($1 >= 0) {
			xhr.setRequestHeader("Range", ["bytes=", $1, "-", $2].join(""));
		}
		if ($3 >= 0) {
			var customHeaders = Module["customHeadersMap"][$3];
			if (customHeaders) {
				for (var header in customHeaders) {
					xhr.setRequestHeader(header, customHeaders[header]);
				}
			}
			var withCredentials = Module["withCredentials"][$3];
			if (withCredentials) {
				xhr.withCredentials = withCredentials;
			}
		}
		try {
			xhr.send();
		} catch (err) {
			return 0;
		}
		if (xhr.status == 200 || xhr.status == 206) {
			var responseArray = new Uint8Array(xhr.response);
			Module["__LAST_REQUESTED_DATA__"] = responseArray;
			return Module["__LAST_REQUESTED_DATA__"].length;
		}
		return 0;
	},
	3836860: ($0) => {
		if (Module["__LAST_REQUESTED_DATA__"]) {
			var data = Module["__LAST_REQUESTED_DATA__"];
			Module.HEAPU8.set(data, $0);
			delete Module["__LAST_REQUESTED_DATA__"];
			return 1;
		}
		return 0;
	},
};
function ExitStatus(status) {
	this.name = "ExitStatus";
	this.message = "Program terminated with exit(" + status + ")";
	this.status = status;
}
function callRuntimeCallbacks(callbacks) {
	while (callbacks.length > 0) {
		callbacks.shift()(Module);
	}
}
function handleException(e) {
	if (e instanceof ExitStatus || e == "unwind") {
		return EXITSTATUS;
	}
	quit_(1, e);
}
function writeArrayToMemory(array, buffer) {
	HEAP8.set(array, buffer);
}
function ___cxa_allocate_exception(size) {
	return _malloc(size + 24) + 24;
}
var exceptionCaught = [];
function exception_addRef(info) {
	info.add_ref();
}
var uncaughtExceptionCount = 0;
function ___cxa_begin_catch(ptr) {
	var info = new ExceptionInfo(ptr);
	if (!info.get_caught()) {
		info.set_caught(true);
		uncaughtExceptionCount--;
	}
	info.set_rethrown(false);
	exceptionCaught.push(info);
	exception_addRef(info);
	return info.get_exception_ptr();
}
var exceptionLast = 0;
function ExceptionInfo(excPtr) {
	this.excPtr = excPtr;
	this.ptr = excPtr - 24;
	this.set_type = function (type) {
		HEAPU32[(this.ptr + 4) >> 2] = type;
	};
	this.get_type = function () {
		return HEAPU32[(this.ptr + 4) >> 2];
	};
	this.set_destructor = function (destructor) {
		HEAPU32[(this.ptr + 8) >> 2] = destructor;
	};
	this.get_destructor = function () {
		return HEAPU32[(this.ptr + 8) >> 2];
	};
	this.set_refcount = function (refcount) {
		HEAP32[this.ptr >> 2] = refcount;
	};
	this.set_caught = function (caught) {
		caught = caught ? 1 : 0;
		HEAP8[(this.ptr + 12) >> 0] = caught;
	};
	this.get_caught = function () {
		return HEAP8[(this.ptr + 12) >> 0] != 0;
	};
	this.set_rethrown = function (rethrown) {
		rethrown = rethrown ? 1 : 0;
		HEAP8[(this.ptr + 13) >> 0] = rethrown;
	};
	this.get_rethrown = function () {
		return HEAP8[(this.ptr + 13) >> 0] != 0;
	};
	this.init = function (type, destructor) {
		this.set_adjusted_ptr(0);
		this.set_type(type);
		this.set_destructor(destructor);
		this.set_refcount(0);
		this.set_caught(false);
		this.set_rethrown(false);
	};
	this.add_ref = function () {
		var value = HEAP32[this.ptr >> 2];
		HEAP32[this.ptr >> 2] = value + 1;
	};
	this.release_ref = function () {
		var prev = HEAP32[this.ptr >> 2];
		HEAP32[this.ptr >> 2] = prev - 1;
		return prev === 1;
	};
	this.set_adjusted_ptr = function (adjustedPtr) {
		HEAPU32[(this.ptr + 16) >> 2] = adjustedPtr;
	};
	this.get_adjusted_ptr = function () {
		return HEAPU32[(this.ptr + 16) >> 2];
	};
	this.get_exception_ptr = function () {
		var isPointer = ___cxa_is_pointer_type(this.get_type());
		if (isPointer) {
			return HEAPU32[this.excPtr >> 2];
		}
		var adjusted = this.get_adjusted_ptr();
		if (adjusted !== 0) return adjusted;
		return this.excPtr;
	};
}
function ___cxa_free_exception(ptr) {
	return _free(new ExceptionInfo(ptr).ptr);
}
var wasmTableMirror = [];
function getWasmTableEntry(funcPtr) {
	var func = wasmTableMirror[funcPtr];
	if (!func) {
		if (funcPtr >= wasmTableMirror.length) wasmTableMirror.length = funcPtr + 1;
		wasmTableMirror[funcPtr] = func = wasmTable.get(funcPtr);
	}
	return func;
}
function exception_decRef(info) {
	if (info.release_ref() && !info.get_rethrown()) {
		var destructor = info.get_destructor();
		if (destructor) {
			getWasmTableEntry(destructor)(info.excPtr);
		}
		___cxa_free_exception(info.excPtr);
	}
}
function ___cxa_end_catch() {
	_setThrew(0);
	var info = exceptionCaught.pop();
	exception_decRef(info);
	exceptionLast = 0;
}
function ___resumeException(ptr) {
	if (!exceptionLast) {
		exceptionLast = ptr;
	}
	throw ptr;
}
function ___cxa_find_matching_catch_2() {
	var thrown = exceptionLast;
	if (!thrown) {
		setTempRet0(0);
		return 0;
	}
	var info = new ExceptionInfo(thrown);
	info.set_adjusted_ptr(thrown);
	var thrownType = info.get_type();
	if (!thrownType) {
		setTempRet0(0);
		return thrown;
	}
	var typeArray = Array.prototype.slice.call(arguments);
	for (var i = 0; i < typeArray.length; i++) {
		var caughtType = typeArray[i];
		if (caughtType === 0 || caughtType === thrownType) {
			break;
		}
		var adjusted_ptr_addr = info.ptr + 16;
		if (___cxa_can_catch(caughtType, thrownType, adjusted_ptr_addr)) {
			setTempRet0(caughtType);
			return thrown;
		}
	}
	setTempRet0(thrownType);
	return thrown;
}
function ___cxa_find_matching_catch_3() {
	var thrown = exceptionLast;
	if (!thrown) {
		setTempRet0(0);
		return 0;
	}
	var info = new ExceptionInfo(thrown);
	info.set_adjusted_ptr(thrown);
	var thrownType = info.get_type();
	if (!thrownType) {
		setTempRet0(0);
		return thrown;
	}
	var typeArray = Array.prototype.slice.call(arguments);
	for (var i = 0; i < typeArray.length; i++) {
		var caughtType = typeArray[i];
		if (caughtType === 0 || caughtType === thrownType) {
			break;
		}
		var adjusted_ptr_addr = info.ptr + 16;
		if (___cxa_can_catch(caughtType, thrownType, adjusted_ptr_addr)) {
			setTempRet0(caughtType);
			return thrown;
		}
	}
	setTempRet0(thrownType);
	return thrown;
}
function ___cxa_rethrow() {
	var info = exceptionCaught.pop();
	if (!info) {
		abort("no exception to throw");
	}
	var ptr = info.excPtr;
	if (!info.get_rethrown()) {
		exceptionCaught.push(info);
		info.set_rethrown(true);
		info.set_caught(false);
		uncaughtExceptionCount++;
	}
	exceptionLast = ptr;
	throw ptr;
}
function ___cxa_throw(ptr, type, destructor) {
	var info = new ExceptionInfo(ptr);
	info.init(type, destructor);
	exceptionLast = ptr;
	uncaughtExceptionCount++;
	throw ptr;
}
var PATH = {
	isAbs: (path) => path.charAt(0) === "/",
	splitPath: (filename) => {
		var splitPathRe =
			/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
		return splitPathRe.exec(filename).slice(1);
	},
	normalizeArray: (parts, allowAboveRoot) => {
		var up = 0;
		for (var i = parts.length - 1; i >= 0; i--) {
			var last = parts[i];
			if (last === ".") {
				parts.splice(i, 1);
			} else if (last === "..") {
				parts.splice(i, 1);
				up++;
			} else if (up) {
				parts.splice(i, 1);
				up--;
			}
		}
		if (allowAboveRoot) {
			for (; up; up--) {
				parts.unshift("..");
			}
		}
		return parts;
	},
	normalize: (path) => {
		var isAbsolute = PATH.isAbs(path),
			trailingSlash = path.substr(-1) === "/";
		path = PATH.normalizeArray(
			path.split("/").filter((p) => !!p),
			!isAbsolute,
		).join("/");
		if (!path && !isAbsolute) {
			path = ".";
		}
		if (path && trailingSlash) {
			path += "/";
		}
		return (isAbsolute ? "/" : "") + path;
	},
	dirname: (path) => {
		var result = PATH.splitPath(path),
			root = result[0],
			dir = result[1];
		if (!root && !dir) {
			return ".";
		}
		if (dir) {
			dir = dir.substr(0, dir.length - 1);
		}
		return root + dir;
	},
	basename: (path) => {
		if (path === "/") return "/";
		path = PATH.normalize(path);
		path = path.replace(/\/$/, "");
		var lastSlash = path.lastIndexOf("/");
		if (lastSlash === -1) return path;
		return path.substr(lastSlash + 1);
	},
	join: function () {
		var paths = Array.prototype.slice.call(arguments, 0);
		return PATH.normalize(paths.join("/"));
	},
	join2: (l, r) => {
		return PATH.normalize(l + "/" + r);
	},
};
function getRandomDevice() {
	if (
		typeof crypto == "object" &&
		typeof crypto["getRandomValues"] == "function"
	) {
		var randomBuffer = new Uint8Array(1);
		return () => {
			crypto.getRandomValues(randomBuffer);
			return randomBuffer[0];
		};
	} else return () => abort("randomDevice");
}
var PATH_FS = {
	resolve: function () {
		var resolvedPath = "",
			resolvedAbsolute = false;
		for (var i = arguments.length - 1; i >= -1 && !resolvedAbsolute; i--) {
			var path = i >= 0 ? arguments[i] : FS.cwd();
			if (typeof path != "string") {
				throw new TypeError("Arguments to path.resolve must be strings");
			} else if (!path) {
				return "";
			}
			resolvedPath = path + "/" + resolvedPath;
			resolvedAbsolute = PATH.isAbs(path);
		}
		resolvedPath = PATH.normalizeArray(
			resolvedPath.split("/").filter((p) => !!p),
			!resolvedAbsolute,
		).join("/");
		return (resolvedAbsolute ? "/" : "") + resolvedPath || ".";
	},
	relative: (from, to) => {
		from = PATH_FS.resolve(from).substr(1);
		to = PATH_FS.resolve(to).substr(1);
		function trim(arr) {
			var start = 0;
			for (; start < arr.length; start++) {
				if (arr[start] !== "") break;
			}
			var end = arr.length - 1;
			for (; end >= 0; end--) {
				if (arr[end] !== "") break;
			}
			if (start > end) return [];
			return arr.slice(start, end - start + 1);
		}
		var fromParts = trim(from.split("/"));
		var toParts = trim(to.split("/"));
		var length = Math.min(fromParts.length, toParts.length);
		var samePartsLength = length;
		for (var i = 0; i < length; i++) {
			if (fromParts[i] !== toParts[i]) {
				samePartsLength = i;
				break;
			}
		}
		var outputParts = [];
		for (var i = samePartsLength; i < fromParts.length; i++) {
			outputParts.push("..");
		}
		outputParts = outputParts.concat(toParts.slice(samePartsLength));
		return outputParts.join("/");
	},
};
function intArrayFromString(stringy, dontAddNull, length) {
	var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1;
	var u8array = new Array(len);
	var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
	if (dontAddNull) u8array.length = numBytesWritten;
	return u8array;
}
var TTY = {
	ttys: [],
	init: function () {},
	shutdown: function () {},
	register: function (dev, ops) {
		TTY.ttys[dev] = { input: [], output: [], ops: ops };
		FS.registerDevice(dev, TTY.stream_ops);
	},
	stream_ops: {
		open: function (stream) {
			var tty = TTY.ttys[stream.node.rdev];
			if (!tty) {
				throw new FS.ErrnoError(43);
			}
			stream.tty = tty;
			stream.seekable = false;
		},
		close: function (stream) {
			stream.tty.ops.flush(stream.tty);
		},
		flush: function (stream) {
			stream.tty.ops.flush(stream.tty);
		},
		read: function (stream, buffer, offset, length, pos) {
			if (!stream.tty || !stream.tty.ops.get_char) {
				throw new FS.ErrnoError(60);
			}
			var bytesRead = 0;
			for (var i = 0; i < length; i++) {
				var result;
				try {
					result = stream.tty.ops.get_char(stream.tty);
				} catch (e) {
					throw new FS.ErrnoError(29);
				}
				if (result === undefined && bytesRead === 0) {
					throw new FS.ErrnoError(6);
				}
				if (result === null || result === undefined) break;
				bytesRead++;
				buffer[offset + i] = result;
			}
			if (bytesRead) {
				stream.node.timestamp = Date.now();
			}
			return bytesRead;
		},
		write: function (stream, buffer, offset, length, pos) {
			if (!stream.tty || !stream.tty.ops.put_char) {
				throw new FS.ErrnoError(60);
			}
			try {
				for (var i = 0; i < length; i++) {
					stream.tty.ops.put_char(stream.tty, buffer[offset + i]);
				}
			} catch (e) {
				throw new FS.ErrnoError(29);
			}
			if (length) {
				stream.node.timestamp = Date.now();
			}
			return i;
		},
	},
	default_tty_ops: {
		get_char: function (tty) {
			if (!tty.input.length) {
				var result = null;
				if (
					typeof window != "undefined" &&
					typeof window.prompt == "function"
				) {
					result = window.prompt("Input: ");
					if (result !== null) {
						result += "\n";
					}
				} else if (typeof readline == "function") {
					result = readline();
					if (result !== null) {
						result += "\n";
					}
				}
				if (!result) {
					return null;
				}
				tty.input = intArrayFromString(result, true);
			}
			return tty.input.shift();
		},
		put_char: function (tty, val) {
			if (val === null || val === 10) {
				out(UTF8ArrayToString(tty.output, 0));
				tty.output = [];
			} else {
				if (val != 0) tty.output.push(val);
			}
		},
		flush: function (tty) {
			if (tty.output && tty.output.length > 0) {
				out(UTF8ArrayToString(tty.output, 0));
				tty.output = [];
			}
		},
	},
	default_tty1_ops: {
		put_char: function (tty, val) {
			if (val === null || val === 10) {
				err(UTF8ArrayToString(tty.output, 0));
				tty.output = [];
			} else {
				if (val != 0) tty.output.push(val);
			}
		},
		flush: function (tty) {
			if (tty.output && tty.output.length > 0) {
				err(UTF8ArrayToString(tty.output, 0));
				tty.output = [];
			}
		},
	},
};
function zeroMemory(address, size) {
	HEAPU8.fill(0, address, address + size);
}
function alignMemory(size, alignment) {
	return Math.ceil(size / alignment) * alignment;
}
function mmapAlloc(size) {
	size = alignMemory(size, 65536);
	var ptr = _emscripten_builtin_memalign(65536, size);
	if (!ptr) return 0;
	zeroMemory(ptr, size);
	return ptr;
}
var MEMFS = {
	ops_table: null,
	mount: function (mount) {
		return MEMFS.createNode(null, "/", 16384 | 511, 0);
	},
	createNode: function (parent, name, mode, dev) {
		if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
			throw new FS.ErrnoError(63);
		}
		if (!MEMFS.ops_table) {
			MEMFS.ops_table = {
				dir: {
					node: {
						getattr: MEMFS.node_ops.getattr,
						setattr: MEMFS.node_ops.setattr,
						lookup: MEMFS.node_ops.lookup,
						mknod: MEMFS.node_ops.mknod,
						rename: MEMFS.node_ops.rename,
						unlink: MEMFS.node_ops.unlink,
						rmdir: MEMFS.node_ops.rmdir,
						readdir: MEMFS.node_ops.readdir,
						symlink: MEMFS.node_ops.symlink,
					},
					stream: { llseek: MEMFS.stream_ops.llseek },
				},
				file: {
					node: {
						getattr: MEMFS.node_ops.getattr,
						setattr: MEMFS.node_ops.setattr,
					},
					stream: {
						llseek: MEMFS.stream_ops.llseek,
						read: MEMFS.stream_ops.read,
						write: MEMFS.stream_ops.write,
						allocate: MEMFS.stream_ops.allocate,
						mmap: MEMFS.stream_ops.mmap,
						msync: MEMFS.stream_ops.msync,
					},
				},
				link: {
					node: {
						getattr: MEMFS.node_ops.getattr,
						setattr: MEMFS.node_ops.setattr,
						readlink: MEMFS.node_ops.readlink,
					},
					stream: {},
				},
				chrdev: {
					node: {
						getattr: MEMFS.node_ops.getattr,
						setattr: MEMFS.node_ops.setattr,
					},
					stream: FS.chrdev_stream_ops,
				},
			};
		}
		var node = FS.createNode(parent, name, mode, dev);
		if (FS.isDir(node.mode)) {
			node.node_ops = MEMFS.ops_table.dir.node;
			node.stream_ops = MEMFS.ops_table.dir.stream;
			node.contents = {};
		} else if (FS.isFile(node.mode)) {
			node.node_ops = MEMFS.ops_table.file.node;
			node.stream_ops = MEMFS.ops_table.file.stream;
			node.usedBytes = 0;
			node.contents = null;
		} else if (FS.isLink(node.mode)) {
			node.node_ops = MEMFS.ops_table.link.node;
			node.stream_ops = MEMFS.ops_table.link.stream;
		} else if (FS.isChrdev(node.mode)) {
			node.node_ops = MEMFS.ops_table.chrdev.node;
			node.stream_ops = MEMFS.ops_table.chrdev.stream;
		}
		node.timestamp = Date.now();
		if (parent) {
			parent.contents[name] = node;
			parent.timestamp = node.timestamp;
		}
		return node;
	},
	getFileDataAsTypedArray: function (node) {
		if (!node.contents) return new Uint8Array(0);
		if (node.contents.subarray)
			return node.contents.subarray(0, node.usedBytes);
		return new Uint8Array(node.contents);
	},
	expandFileStorage: function (node, newCapacity) {
		var prevCapacity = node.contents ? node.contents.length : 0;
		if (prevCapacity >= newCapacity) return;
		var CAPACITY_DOUBLING_MAX = 1024 * 1024;
		newCapacity = Math.max(
			newCapacity,
			(prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125)) >>> 0,
		);
		if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256);
		var oldContents = node.contents;
		node.contents = new Uint8Array(newCapacity);
		if (node.usedBytes > 0)
			node.contents.set(oldContents.subarray(0, node.usedBytes), 0);
	},
	resizeFileStorage: function (node, newSize) {
		if (node.usedBytes == newSize) return;
		if (newSize == 0) {
			node.contents = null;
			node.usedBytes = 0;
		} else {
			var oldContents = node.contents;
			node.contents = new Uint8Array(newSize);
			if (oldContents) {
				node.contents.set(
					oldContents.subarray(0, Math.min(newSize, node.usedBytes)),
				);
			}
			node.usedBytes = newSize;
		}
	},
	node_ops: {
		getattr: function (node) {
			var attr = {};
			attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
			attr.ino = node.id;
			attr.mode = node.mode;
			attr.nlink = 1;
			attr.uid = 0;
			attr.gid = 0;
			attr.rdev = node.rdev;
			if (FS.isDir(node.mode)) {
				attr.size = 4096;
			} else if (FS.isFile(node.mode)) {
				attr.size = node.usedBytes;
			} else if (FS.isLink(node.mode)) {
				attr.size = node.link.length;
			} else {
				attr.size = 0;
			}
			attr.atime = new Date(node.timestamp);
			attr.mtime = new Date(node.timestamp);
			attr.ctime = new Date(node.timestamp);
			attr.blksize = 4096;
			attr.blocks = Math.ceil(attr.size / attr.blksize);
			return attr;
		},
		setattr: function (node, attr) {
			if (attr.mode !== undefined) {
				node.mode = attr.mode;
			}
			if (attr.timestamp !== undefined) {
				node.timestamp = attr.timestamp;
			}
			if (attr.size !== undefined) {
				MEMFS.resizeFileStorage(node, attr.size);
			}
		},
		lookup: function (parent, name) {
			throw FS.genericErrors[44];
		},
		mknod: function (parent, name, mode, dev) {
			return MEMFS.createNode(parent, name, mode, dev);
		},
		rename: function (old_node, new_dir, new_name) {
			if (FS.isDir(old_node.mode)) {
				var new_node;
				try {
					new_node = FS.lookupNode(new_dir, new_name);
				} catch (e) {}
				if (new_node) {
					for (var i in new_node.contents) {
						throw new FS.ErrnoError(55);
					}
				}
			}
			delete old_node.parent.contents[old_node.name];
			old_node.parent.timestamp = Date.now();
			old_node.name = new_name;
			new_dir.contents[new_name] = old_node;
			new_dir.timestamp = old_node.parent.timestamp;
			old_node.parent = new_dir;
		},
		unlink: function (parent, name) {
			delete parent.contents[name];
			parent.timestamp = Date.now();
		},
		rmdir: function (parent, name) {
			var node = FS.lookupNode(parent, name);
			for (var i in node.contents) {
				throw new FS.ErrnoError(55);
			}
			delete parent.contents[name];
			parent.timestamp = Date.now();
		},
		readdir: function (node) {
			var entries = [".", ".."];
			for (var key in node.contents) {
				if (!node.contents.hasOwnProperty(key)) {
					continue;
				}
				entries.push(key);
			}
			return entries;
		},
		symlink: function (parent, newname, oldpath) {
			var node = MEMFS.createNode(parent, newname, 511 | 40960, 0);
			node.link = oldpath;
			return node;
		},
		readlink: function (node) {
			if (!FS.isLink(node.mode)) {
				throw new FS.ErrnoError(28);
			}
			return node.link;
		},
	},
	stream_ops: {
		read: function (stream, buffer, offset, length, position) {
			var contents = stream.node.contents;
			if (position >= stream.node.usedBytes) return 0;
			var size = Math.min(stream.node.usedBytes - position, length);
			if (size > 8 && contents.subarray) {
				buffer.set(contents.subarray(position, position + size), offset);
			} else {
				for (var i = 0; i < size; i++)
					buffer[offset + i] = contents[position + i];
			}
			return size;
		},
		write: function (stream, buffer, offset, length, position, canOwn) {
			if (buffer.buffer === HEAP8.buffer) {
				canOwn = false;
			}
			if (!length) return 0;
			var node = stream.node;
			node.timestamp = Date.now();
			if (buffer.subarray && (!node.contents || node.contents.subarray)) {
				if (canOwn) {
					node.contents = buffer.subarray(offset, offset + length);
					node.usedBytes = length;
					return length;
				} else if (node.usedBytes === 0 && position === 0) {
					node.contents = buffer.slice(offset, offset + length);
					node.usedBytes = length;
					return length;
				} else if (position + length <= node.usedBytes) {
					node.contents.set(buffer.subarray(offset, offset + length), position);
					return length;
				}
			}
			MEMFS.expandFileStorage(node, position + length);
			if (node.contents.subarray && buffer.subarray) {
				node.contents.set(buffer.subarray(offset, offset + length), position);
			} else {
				for (var i = 0; i < length; i++) {
					node.contents[position + i] = buffer[offset + i];
				}
			}
			node.usedBytes = Math.max(node.usedBytes, position + length);
			return length;
		},
		llseek: function (stream, offset, whence) {
			var position = offset;
			if (whence === 1) {
				position += stream.position;
			} else if (whence === 2) {
				if (FS.isFile(stream.node.mode)) {
					position += stream.node.usedBytes;
				}
			}
			if (position < 0) {
				throw new FS.ErrnoError(28);
			}
			return position;
		},
		allocate: function (stream, offset, length) {
			MEMFS.expandFileStorage(stream.node, offset + length);
			stream.node.usedBytes = Math.max(stream.node.usedBytes, offset + length);
		},
		mmap: function (stream, length, position, prot, flags) {
			if (!FS.isFile(stream.node.mode)) {
				throw new FS.ErrnoError(43);
			}
			var ptr;
			var allocated;
			var contents = stream.node.contents;
			if (!(flags & 2) && contents.buffer === buffer) {
				allocated = false;
				ptr = contents.byteOffset;
			} else {
				if (position > 0 || position + length < contents.length) {
					if (contents.subarray) {
						contents = contents.subarray(position, position + length);
					} else {
						contents = Array.prototype.slice.call(
							contents,
							position,
							position + length,
						);
					}
				}
				allocated = true;
				ptr = mmapAlloc(length);
				if (!ptr) {
					throw new FS.ErrnoError(48);
				}
				HEAP8.set(contents, ptr);
			}
			return { ptr: ptr, allocated: allocated };
		},
		msync: function (stream, buffer, offset, length, mmapFlags) {
			if (!FS.isFile(stream.node.mode)) {
				throw new FS.ErrnoError(43);
			}
			if (mmapFlags & 2) {
				return 0;
			}
			var bytesWritten = MEMFS.stream_ops.write(
				stream,
				buffer,
				0,
				length,
				offset,
				false,
			);
			return 0;
		},
	},
};
function asyncLoad(url, onload, onerror, noRunDep) {
	var dep = !noRunDep ? getUniqueRunDependency("al " + url) : "";
	readAsync(
		url,
		(arrayBuffer) => {
			assert(
				arrayBuffer,
				'Loading data file "' + url + '" failed (no arrayBuffer).',
			);
			onload(new Uint8Array(arrayBuffer));
			if (dep) removeRunDependency(dep);
		},
		(event) => {
			if (onerror) {
				onerror();
			} else {
				throw 'Loading data file "' + url + '" failed.';
			}
		},
	);
	if (dep) addRunDependency(dep);
}
var FS = {
	root: null,
	mounts: [],
	devices: {},
	streams: [],
	nextInode: 1,
	nameTable: null,
	currentPath: "/",
	initialized: false,
	ignorePermissions: true,
	ErrnoError: null,
	genericErrors: {},
	filesystems: null,
	syncFSRequests: 0,
	lookupPath: (path, opts = {}) => {
		path = PATH_FS.resolve(FS.cwd(), path);
		if (!path) return { path: "", node: null };
		var defaults = { follow_mount: true, recurse_count: 0 };
		opts = Object.assign(defaults, opts);
		if (opts.recurse_count > 8) {
			throw new FS.ErrnoError(32);
		}
		var parts = PATH.normalizeArray(
			path.split("/").filter((p) => !!p),
			false,
		);
		var current = FS.root;
		var current_path = "/";
		for (var i = 0; i < parts.length; i++) {
			var islast = i === parts.length - 1;
			if (islast && opts.parent) {
				break;
			}
			current = FS.lookupNode(current, parts[i]);
			current_path = PATH.join2(current_path, parts[i]);
			if (FS.isMountpoint(current)) {
				if (!islast || (islast && opts.follow_mount)) {
					current = current.mounted.root;
				}
			}
			if (!islast || opts.follow) {
				var count = 0;
				while (FS.isLink(current.mode)) {
					var link = FS.readlink(current_path);
					current_path = PATH_FS.resolve(PATH.dirname(current_path), link);
					var lookup = FS.lookupPath(current_path, {
						recurse_count: opts.recurse_count + 1,
					});
					current = lookup.node;
					if (count++ > 40) {
						throw new FS.ErrnoError(32);
					}
				}
			}
		}
		return { path: current_path, node: current };
	},
	getPath: (node) => {
		var path;
		while (true) {
			if (FS.isRoot(node)) {
				var mount = node.mount.mountpoint;
				if (!path) return mount;
				return mount[mount.length - 1] !== "/"
					? mount + "/" + path
					: mount + path;
			}
			path = path ? node.name + "/" + path : node.name;
			node = node.parent;
		}
	},
	hashName: (parentid, name) => {
		var hash = 0;
		for (var i = 0; i < name.length; i++) {
			hash = ((hash << 5) - hash + name.charCodeAt(i)) | 0;
		}
		return ((parentid + hash) >>> 0) % FS.nameTable.length;
	},
	hashAddNode: (node) => {
		var hash = FS.hashName(node.parent.id, node.name);
		node.name_next = FS.nameTable[hash];
		FS.nameTable[hash] = node;
	},
	hashRemoveNode: (node) => {
		var hash = FS.hashName(node.parent.id, node.name);
		if (FS.nameTable[hash] === node) {
			FS.nameTable[hash] = node.name_next;
		} else {
			var current = FS.nameTable[hash];
			while (current) {
				if (current.name_next === node) {
					current.name_next = node.name_next;
					break;
				}
				current = current.name_next;
			}
		}
	},
	lookupNode: (parent, name) => {
		var errCode = FS.mayLookup(parent);
		if (errCode) {
			throw new FS.ErrnoError(errCode, parent);
		}
		var hash = FS.hashName(parent.id, name);
		for (var node = FS.nameTable[hash]; node; node = node.name_next) {
			var nodeName = node.name;
			if (node.parent.id === parent.id && nodeName === name) {
				return node;
			}
		}
		return FS.lookup(parent, name);
	},
	createNode: (parent, name, mode, rdev) => {
		var node = new FS.FSNode(parent, name, mode, rdev);
		FS.hashAddNode(node);
		return node;
	},
	destroyNode: (node) => {
		FS.hashRemoveNode(node);
	},
	isRoot: (node) => {
		return node === node.parent;
	},
	isMountpoint: (node) => {
		return !!node.mounted;
	},
	isFile: (mode) => {
		return (mode & 61440) === 32768;
	},
	isDir: (mode) => {
		return (mode & 61440) === 16384;
	},
	isLink: (mode) => {
		return (mode & 61440) === 40960;
	},
	isChrdev: (mode) => {
		return (mode & 61440) === 8192;
	},
	isBlkdev: (mode) => {
		return (mode & 61440) === 24576;
	},
	isFIFO: (mode) => {
		return (mode & 61440) === 4096;
	},
	isSocket: (mode) => {
		return (mode & 49152) === 49152;
	},
	flagModes: { r: 0, "r+": 2, w: 577, "w+": 578, a: 1089, "a+": 1090 },
	modeStringToFlags: (str) => {
		var flags = FS.flagModes[str];
		if (typeof flags == "undefined") {
			throw new Error("Unknown file open mode: " + str);
		}
		return flags;
	},
	flagsToPermissionString: (flag) => {
		var perms = ["r", "w", "rw"][flag & 3];
		if (flag & 512) {
			perms += "w";
		}
		return perms;
	},
	nodePermissions: (node, perms) => {
		if (FS.ignorePermissions) {
			return 0;
		}
		if (perms.includes("r") && !(node.mode & 292)) {
			return 2;
		} else if (perms.includes("w") && !(node.mode & 146)) {
			return 2;
		} else if (perms.includes("x") && !(node.mode & 73)) {
			return 2;
		}
		return 0;
	},
	mayLookup: (dir) => {
		var errCode = FS.nodePermissions(dir, "x");
		if (errCode) return errCode;
		if (!dir.node_ops.lookup) return 2;
		return 0;
	},
	mayCreate: (dir, name) => {
		try {
			var node = FS.lookupNode(dir, name);
			return 20;
		} catch (e) {}
		return FS.nodePermissions(dir, "wx");
	},
	mayDelete: (dir, name, isdir) => {
		var node;
		try {
			node = FS.lookupNode(dir, name);
		} catch (e) {
			return e.errno;
		}
		var errCode = FS.nodePermissions(dir, "wx");
		if (errCode) {
			return errCode;
		}
		if (isdir) {
			if (!FS.isDir(node.mode)) {
				return 54;
			}
			if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
				return 10;
			}
		} else {
			if (FS.isDir(node.mode)) {
				return 31;
			}
		}
		return 0;
	},
	mayOpen: (node, flags) => {
		if (!node) {
			return 44;
		}
		if (FS.isLink(node.mode)) {
			return 32;
		} else if (FS.isDir(node.mode)) {
			if (FS.flagsToPermissionString(flags) !== "r" || flags & 512) {
				return 31;
			}
		}
		return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
	},
	MAX_OPEN_FDS: 4096,
	nextfd: (fd_start = 0, fd_end = FS.MAX_OPEN_FDS) => {
		for (var fd = fd_start; fd <= fd_end; fd++) {
			if (!FS.streams[fd]) {
				return fd;
			}
		}
		throw new FS.ErrnoError(33);
	},
	getStream: (fd) => FS.streams[fd],
	createStream: (stream, fd_start, fd_end) => {
		if (!FS.FSStream) {
			FS.FSStream = function () {
				this.shared = {};
			};
			FS.FSStream.prototype = {};
			Object.defineProperties(FS.FSStream.prototype, {
				object: {
					get: function () {
						return this.node;
					},
					set: function (val) {
						this.node = val;
					},
				},
				isRead: {
					get: function () {
						return (this.flags & 2097155) !== 1;
					},
				},
				isWrite: {
					get: function () {
						return (this.flags & 2097155) !== 0;
					},
				},
				isAppend: {
					get: function () {
						return this.flags & 1024;
					},
				},
				flags: {
					get: function () {
						return this.shared.flags;
					},
					set: function (val) {
						this.shared.flags = val;
					},
				},
				position: {
					get: function () {
						return this.shared.position;
					},
					set: function (val) {
						this.shared.position = val;
					},
				},
			});
		}
		stream = Object.assign(new FS.FSStream(), stream);
		var fd = FS.nextfd(fd_start, fd_end);
		stream.fd = fd;
		FS.streams[fd] = stream;
		return stream;
	},
	closeStream: (fd) => {
		FS.streams[fd] = null;
	},
	chrdev_stream_ops: {
		open: (stream) => {
			var device = FS.getDevice(stream.node.rdev);
			stream.stream_ops = device.stream_ops;
			if (stream.stream_ops.open) {
				stream.stream_ops.open(stream);
			}
		},
		llseek: () => {
			throw new FS.ErrnoError(70);
		},
	},
	major: (dev) => dev >> 8,
	minor: (dev) => dev & 255,
	makedev: (ma, mi) => (ma << 8) | mi,
	registerDevice: (dev, ops) => {
		FS.devices[dev] = { stream_ops: ops };
	},
	getDevice: (dev) => FS.devices[dev],
	getMounts: (mount) => {
		var mounts = [];
		var check = [mount];
		while (check.length) {
			var m = check.pop();
			mounts.push(m);
			check.push.apply(check, m.mounts);
		}
		return mounts;
	},
	syncfs: (populate, callback) => {
		if (typeof populate == "function") {
			callback = populate;
			populate = false;
		}
		FS.syncFSRequests++;
		if (FS.syncFSRequests > 1) {
			err(
				"warning: " +
					FS.syncFSRequests +
					" FS.syncfs operations in flight at once, probably just doing extra work",
			);
		}
		var mounts = FS.getMounts(FS.root.mount);
		var completed = 0;
		function doCallback(errCode) {
			FS.syncFSRequests--;
			return callback(errCode);
		}
		function done(errCode) {
			if (errCode) {
				if (!done.errored) {
					done.errored = true;
					return doCallback(errCode);
				}
				return;
			}
			if (++completed >= mounts.length) {
				doCallback(null);
			}
		}
		mounts.forEach((mount) => {
			if (!mount.type.syncfs) {
				return done(null);
			}
			mount.type.syncfs(mount, populate, done);
		});
	},
	mount: (type, opts, mountpoint) => {
		var root = mountpoint === "/";
		var pseudo = !mountpoint;
		var node;
		if (root && FS.root) {
			throw new FS.ErrnoError(10);
		} else if (!root && !pseudo) {
			var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
			mountpoint = lookup.path;
			node = lookup.node;
			if (FS.isMountpoint(node)) {
				throw new FS.ErrnoError(10);
			}
			if (!FS.isDir(node.mode)) {
				throw new FS.ErrnoError(54);
			}
		}
		var mount = { type: type, opts: opts, mountpoint: mountpoint, mounts: [] };
		var mountRoot = type.mount(mount);
		mountRoot.mount = mount;
		mount.root = mountRoot;
		if (root) {
			FS.root = mountRoot;
		} else if (node) {
			node.mounted = mount;
			if (node.mount) {
				node.mount.mounts.push(mount);
			}
		}
		return mountRoot;
	},
	unmount: (mountpoint) => {
		var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
		if (!FS.isMountpoint(lookup.node)) {
			throw new FS.ErrnoError(28);
		}
		var node = lookup.node;
		var mount = node.mounted;
		var mounts = FS.getMounts(mount);
		Object.keys(FS.nameTable).forEach((hash) => {
			var current = FS.nameTable[hash];
			while (current) {
				var next = current.name_next;
				if (mounts.includes(current.mount)) {
					FS.destroyNode(current);
				}
				current = next;
			}
		});
		node.mounted = null;
		var idx = node.mount.mounts.indexOf(mount);
		node.mount.mounts.splice(idx, 1);
	},
	lookup: (parent, name) => {
		return parent.node_ops.lookup(parent, name);
	},
	mknod: (path, mode, dev) => {
		var lookup = FS.lookupPath(path, { parent: true });
		var parent = lookup.node;
		var name = PATH.basename(path);
		if (!name || name === "." || name === "..") {
			throw new FS.ErrnoError(28);
		}
		var errCode = FS.mayCreate(parent, name);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		if (!parent.node_ops.mknod) {
			throw new FS.ErrnoError(63);
		}
		return parent.node_ops.mknod(parent, name, mode, dev);
	},
	create: (path, mode) => {
		mode = mode !== undefined ? mode : 438;
		mode &= 4095;
		mode |= 32768;
		return FS.mknod(path, mode, 0);
	},
	mkdir: (path, mode) => {
		mode = mode !== undefined ? mode : 511;
		mode &= 511 | 512;
		mode |= 16384;
		return FS.mknod(path, mode, 0);
	},
	mkdirTree: (path, mode) => {
		var dirs = path.split("/");
		var d = "";
		for (var i = 0; i < dirs.length; ++i) {
			if (!dirs[i]) continue;
			d += "/" + dirs[i];
			try {
				FS.mkdir(d, mode);
			} catch (e) {
				if (e.errno != 20) throw e;
			}
		}
	},
	mkdev: (path, mode, dev) => {
		if (typeof dev == "undefined") {
			dev = mode;
			mode = 438;
		}
		mode |= 8192;
		return FS.mknod(path, mode, dev);
	},
	symlink: (oldpath, newpath) => {
		if (!PATH_FS.resolve(oldpath)) {
			throw new FS.ErrnoError(44);
		}
		var lookup = FS.lookupPath(newpath, { parent: true });
		var parent = lookup.node;
		if (!parent) {
			throw new FS.ErrnoError(44);
		}
		var newname = PATH.basename(newpath);
		var errCode = FS.mayCreate(parent, newname);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		if (!parent.node_ops.symlink) {
			throw new FS.ErrnoError(63);
		}
		return parent.node_ops.symlink(parent, newname, oldpath);
	},
	rename: (old_path, new_path) => {
		var old_dirname = PATH.dirname(old_path);
		var new_dirname = PATH.dirname(new_path);
		var old_name = PATH.basename(old_path);
		var new_name = PATH.basename(new_path);
		var lookup, old_dir, new_dir;
		lookup = FS.lookupPath(old_path, { parent: true });
		old_dir = lookup.node;
		lookup = FS.lookupPath(new_path, { parent: true });
		new_dir = lookup.node;
		if (!old_dir || !new_dir) throw new FS.ErrnoError(44);
		if (old_dir.mount !== new_dir.mount) {
			throw new FS.ErrnoError(75);
		}
		var old_node = FS.lookupNode(old_dir, old_name);
		var relative = PATH_FS.relative(old_path, new_dirname);
		if (relative.charAt(0) !== ".") {
			throw new FS.ErrnoError(28);
		}
		relative = PATH_FS.relative(new_path, old_dirname);
		if (relative.charAt(0) !== ".") {
			throw new FS.ErrnoError(55);
		}
		var new_node;
		try {
			new_node = FS.lookupNode(new_dir, new_name);
		} catch (e) {}
		if (old_node === new_node) {
			return;
		}
		var isdir = FS.isDir(old_node.mode);
		var errCode = FS.mayDelete(old_dir, old_name, isdir);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		errCode = new_node
			? FS.mayDelete(new_dir, new_name, isdir)
			: FS.mayCreate(new_dir, new_name);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		if (!old_dir.node_ops.rename) {
			throw new FS.ErrnoError(63);
		}
		if (FS.isMountpoint(old_node) || (new_node && FS.isMountpoint(new_node))) {
			throw new FS.ErrnoError(10);
		}
		if (new_dir !== old_dir) {
			errCode = FS.nodePermissions(old_dir, "w");
			if (errCode) {
				throw new FS.ErrnoError(errCode);
			}
		}
		FS.hashRemoveNode(old_node);
		try {
			old_dir.node_ops.rename(old_node, new_dir, new_name);
		} catch (e) {
			throw e;
		} finally {
			FS.hashAddNode(old_node);
		}
	},
	rmdir: (path) => {
		var lookup = FS.lookupPath(path, { parent: true });
		var parent = lookup.node;
		var name = PATH.basename(path);
		var node = FS.lookupNode(parent, name);
		var errCode = FS.mayDelete(parent, name, true);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		if (!parent.node_ops.rmdir) {
			throw new FS.ErrnoError(63);
		}
		if (FS.isMountpoint(node)) {
			throw new FS.ErrnoError(10);
		}
		parent.node_ops.rmdir(parent, name);
		FS.destroyNode(node);
	},
	readdir: (path) => {
		var lookup = FS.lookupPath(path, { follow: true });
		var node = lookup.node;
		if (!node.node_ops.readdir) {
			throw new FS.ErrnoError(54);
		}
		return node.node_ops.readdir(node);
	},
	unlink: (path) => {
		var lookup = FS.lookupPath(path, { parent: true });
		var parent = lookup.node;
		if (!parent) {
			throw new FS.ErrnoError(44);
		}
		var name = PATH.basename(path);
		var node = FS.lookupNode(parent, name);
		var errCode = FS.mayDelete(parent, name, false);
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		if (!parent.node_ops.unlink) {
			throw new FS.ErrnoError(63);
		}
		if (FS.isMountpoint(node)) {
			throw new FS.ErrnoError(10);
		}
		parent.node_ops.unlink(parent, name);
		FS.destroyNode(node);
	},
	readlink: (path) => {
		var lookup = FS.lookupPath(path);
		var link = lookup.node;
		if (!link) {
			throw new FS.ErrnoError(44);
		}
		if (!link.node_ops.readlink) {
			throw new FS.ErrnoError(28);
		}
		return PATH_FS.resolve(
			FS.getPath(link.parent),
			link.node_ops.readlink(link),
		);
	},
	stat: (path, dontFollow) => {
		var lookup = FS.lookupPath(path, { follow: !dontFollow });
		var node = lookup.node;
		if (!node) {
			throw new FS.ErrnoError(44);
		}
		if (!node.node_ops.getattr) {
			throw new FS.ErrnoError(63);
		}
		return node.node_ops.getattr(node);
	},
	lstat: (path) => {
		return FS.stat(path, true);
	},
	chmod: (path, mode, dontFollow) => {
		var node;
		if (typeof path == "string") {
			var lookup = FS.lookupPath(path, { follow: !dontFollow });
			node = lookup.node;
		} else {
			node = path;
		}
		if (!node.node_ops.setattr) {
			throw new FS.ErrnoError(63);
		}
		node.node_ops.setattr(node, {
			mode: (mode & 4095) | (node.mode & ~4095),
			timestamp: Date.now(),
		});
	},
	lchmod: (path, mode) => {
		FS.chmod(path, mode, true);
	},
	fchmod: (fd, mode) => {
		var stream = FS.getStream(fd);
		if (!stream) {
			throw new FS.ErrnoError(8);
		}
		FS.chmod(stream.node, mode);
	},
	chown: (path, uid, gid, dontFollow) => {
		var node;
		if (typeof path == "string") {
			var lookup = FS.lookupPath(path, { follow: !dontFollow });
			node = lookup.node;
		} else {
			node = path;
		}
		if (!node.node_ops.setattr) {
			throw new FS.ErrnoError(63);
		}
		node.node_ops.setattr(node, { timestamp: Date.now() });
	},
	lchown: (path, uid, gid) => {
		FS.chown(path, uid, gid, true);
	},
	fchown: (fd, uid, gid) => {
		var stream = FS.getStream(fd);
		if (!stream) {
			throw new FS.ErrnoError(8);
		}
		FS.chown(stream.node, uid, gid);
	},
	truncate: (path, len) => {
		if (len < 0) {
			throw new FS.ErrnoError(28);
		}
		var node;
		if (typeof path == "string") {
			var lookup = FS.lookupPath(path, { follow: true });
			node = lookup.node;
		} else {
			node = path;
		}
		if (!node.node_ops.setattr) {
			throw new FS.ErrnoError(63);
		}
		if (FS.isDir(node.mode)) {
			throw new FS.ErrnoError(31);
		}
		if (!FS.isFile(node.mode)) {
			throw new FS.ErrnoError(28);
		}
		var errCode = FS.nodePermissions(node, "w");
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		node.node_ops.setattr(node, { size: len, timestamp: Date.now() });
	},
	ftruncate: (fd, len) => {
		var stream = FS.getStream(fd);
		if (!stream) {
			throw new FS.ErrnoError(8);
		}
		if ((stream.flags & 2097155) === 0) {
			throw new FS.ErrnoError(28);
		}
		FS.truncate(stream.node, len);
	},
	utime: (path, atime, mtime) => {
		var lookup = FS.lookupPath(path, { follow: true });
		var node = lookup.node;
		node.node_ops.setattr(node, { timestamp: Math.max(atime, mtime) });
	},
	open: (path, flags, mode) => {
		if (path === "") {
			throw new FS.ErrnoError(44);
		}
		flags = typeof flags == "string" ? FS.modeStringToFlags(flags) : flags;
		mode = typeof mode == "undefined" ? 438 : mode;
		if (flags & 64) {
			mode = (mode & 4095) | 32768;
		} else {
			mode = 0;
		}
		var node;
		if (typeof path == "object") {
			node = path;
		} else {
			path = PATH.normalize(path);
			try {
				var lookup = FS.lookupPath(path, { follow: !(flags & 131072) });
				node = lookup.node;
			} catch (e) {}
		}
		var created = false;
		if (flags & 64) {
			if (node) {
				if (flags & 128) {
					throw new FS.ErrnoError(20);
				}
			} else {
				node = FS.mknod(path, mode, 0);
				created = true;
			}
		}
		if (!node) {
			throw new FS.ErrnoError(44);
		}
		if (FS.isChrdev(node.mode)) {
			flags &= ~512;
		}
		if (flags & 65536 && !FS.isDir(node.mode)) {
			throw new FS.ErrnoError(54);
		}
		if (!created) {
			var errCode = FS.mayOpen(node, flags);
			if (errCode) {
				throw new FS.ErrnoError(errCode);
			}
		}
		if (flags & 512 && !created) {
			FS.truncate(node, 0);
		}
		flags &= ~(128 | 512 | 131072);
		var stream = FS.createStream({
			node: node,
			path: FS.getPath(node),
			flags: flags,
			seekable: true,
			position: 0,
			stream_ops: node.stream_ops,
			ungotten: [],
			error: false,
		});
		if (stream.stream_ops.open) {
			stream.stream_ops.open(stream);
		}
		if (Module["logReadFiles"] && !(flags & 1)) {
			if (!FS.readFiles) FS.readFiles = {};
			if (!(path in FS.readFiles)) {
				FS.readFiles[path] = 1;
			}
		}
		return stream;
	},
	close: (stream) => {
		if (FS.isClosed(stream)) {
			throw new FS.ErrnoError(8);
		}
		if (stream.getdents) stream.getdents = null;
		try {
			if (stream.stream_ops.close) {
				stream.stream_ops.close(stream);
			}
		} catch (e) {
			throw e;
		} finally {
			FS.closeStream(stream.fd);
		}
		stream.fd = null;
	},
	isClosed: (stream) => {
		return stream.fd === null;
	},
	llseek: (stream, offset, whence) => {
		if (FS.isClosed(stream)) {
			throw new FS.ErrnoError(8);
		}
		if (!stream.seekable || !stream.stream_ops.llseek) {
			throw new FS.ErrnoError(70);
		}
		if (whence != 0 && whence != 1 && whence != 2) {
			throw new FS.ErrnoError(28);
		}
		stream.position = stream.stream_ops.llseek(stream, offset, whence);
		stream.ungotten = [];
		return stream.position;
	},
	read: (stream, buffer, offset, length, position) => {
		if (length < 0 || position < 0) {
			throw new FS.ErrnoError(28);
		}
		if (FS.isClosed(stream)) {
			throw new FS.ErrnoError(8);
		}
		if ((stream.flags & 2097155) === 1) {
			throw new FS.ErrnoError(8);
		}
		if (FS.isDir(stream.node.mode)) {
			throw new FS.ErrnoError(31);
		}
		if (!stream.stream_ops.read) {
			throw new FS.ErrnoError(28);
		}
		var seeking = typeof position != "undefined";
		if (!seeking) {
			position = stream.position;
		} else if (!stream.seekable) {
			throw new FS.ErrnoError(70);
		}
		var bytesRead = stream.stream_ops.read(
			stream,
			buffer,
			offset,
			length,
			position,
		);
		if (!seeking) stream.position += bytesRead;
		return bytesRead;
	},
	write: (stream, buffer, offset, length, position, canOwn) => {
		if (length < 0 || position < 0) {
			throw new FS.ErrnoError(28);
		}
		if (FS.isClosed(stream)) {
			throw new FS.ErrnoError(8);
		}
		if ((stream.flags & 2097155) === 0) {
			throw new FS.ErrnoError(8);
		}
		if (FS.isDir(stream.node.mode)) {
			throw new FS.ErrnoError(31);
		}
		if (!stream.stream_ops.write) {
			throw new FS.ErrnoError(28);
		}
		if (stream.seekable && stream.flags & 1024) {
			FS.llseek(stream, 0, 2);
		}
		var seeking = typeof position != "undefined";
		if (!seeking) {
			position = stream.position;
		} else if (!stream.seekable) {
			throw new FS.ErrnoError(70);
		}
		var bytesWritten = stream.stream_ops.write(
			stream,
			buffer,
			offset,
			length,
			position,
			canOwn,
		);
		if (!seeking) stream.position += bytesWritten;
		return bytesWritten;
	},
	allocate: (stream, offset, length) => {
		if (FS.isClosed(stream)) {
			throw new FS.ErrnoError(8);
		}
		if (offset < 0 || length <= 0) {
			throw new FS.ErrnoError(28);
		}
		if ((stream.flags & 2097155) === 0) {
			throw new FS.ErrnoError(8);
		}
		if (!FS.isFile(stream.node.mode) && !FS.isDir(stream.node.mode)) {
			throw new FS.ErrnoError(43);
		}
		if (!stream.stream_ops.allocate) {
			throw new FS.ErrnoError(138);
		}
		stream.stream_ops.allocate(stream, offset, length);
	},
	mmap: (stream, length, position, prot, flags) => {
		if (
			(prot & 2) !== 0 &&
			(flags & 2) === 0 &&
			(stream.flags & 2097155) !== 2
		) {
			throw new FS.ErrnoError(2);
		}
		if ((stream.flags & 2097155) === 1) {
			throw new FS.ErrnoError(2);
		}
		if (!stream.stream_ops.mmap) {
			throw new FS.ErrnoError(43);
		}
		return stream.stream_ops.mmap(stream, length, position, prot, flags);
	},
	msync: (stream, buffer, offset, length, mmapFlags) => {
		if (!stream || !stream.stream_ops.msync) {
			return 0;
		}
		return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);
	},
	munmap: (stream) => 0,
	ioctl: (stream, cmd, arg) => {
		if (!stream.stream_ops.ioctl) {
			throw new FS.ErrnoError(59);
		}
		return stream.stream_ops.ioctl(stream, cmd, arg);
	},
	readFile: (path, opts = {}) => {
		opts.flags = opts.flags || 0;
		opts.encoding = opts.encoding || "binary";
		if (opts.encoding !== "utf8" && opts.encoding !== "binary") {
			throw new Error('Invalid encoding type "' + opts.encoding + '"');
		}
		var ret;
		var stream = FS.open(path, opts.flags);
		var stat = FS.stat(path);
		var length = stat.size;
		var buf = new Uint8Array(length);
		FS.read(stream, buf, 0, length, 0);
		if (opts.encoding === "utf8") {
			ret = UTF8ArrayToString(buf, 0);
		} else if (opts.encoding === "binary") {
			ret = buf;
		}
		FS.close(stream);
		return ret;
	},
	writeFile: (path, data, opts = {}) => {
		opts.flags = opts.flags || 577;
		var stream = FS.open(path, opts.flags, opts.mode);
		if (typeof data == "string") {
			var buf = new Uint8Array(lengthBytesUTF8(data) + 1);
			var actualNumBytes = stringToUTF8Array(data, buf, 0, buf.length);
			FS.write(stream, buf, 0, actualNumBytes, undefined, opts.canOwn);
		} else if (ArrayBuffer.isView(data)) {
			FS.write(stream, data, 0, data.byteLength, undefined, opts.canOwn);
		} else {
			throw new Error("Unsupported data type");
		}
		FS.close(stream);
	},
	cwd: () => FS.currentPath,
	chdir: (path) => {
		var lookup = FS.lookupPath(path, { follow: true });
		if (lookup.node === null) {
			throw new FS.ErrnoError(44);
		}
		if (!FS.isDir(lookup.node.mode)) {
			throw new FS.ErrnoError(54);
		}
		var errCode = FS.nodePermissions(lookup.node, "x");
		if (errCode) {
			throw new FS.ErrnoError(errCode);
		}
		FS.currentPath = lookup.path;
	},
	createDefaultDirectories: () => {
		FS.mkdir("/tmp");
		FS.mkdir("/home");
		FS.mkdir("/home/<USER>");
	},
	createDefaultDevices: () => {
		FS.mkdir("/dev");
		FS.registerDevice(FS.makedev(1, 3), {
			read: () => 0,
			write: (stream, buffer, offset, length, pos) => length,
		});
		FS.mkdev("/dev/null", FS.makedev(1, 3));
		TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
		TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
		FS.mkdev("/dev/tty", FS.makedev(5, 0));
		FS.mkdev("/dev/tty1", FS.makedev(6, 0));
		var random_device = getRandomDevice();
		FS.createDevice("/dev", "random", random_device);
		FS.createDevice("/dev", "urandom", random_device);
		FS.mkdir("/dev/shm");
		FS.mkdir("/dev/shm/tmp");
	},
	createSpecialDirectories: () => {
		FS.mkdir("/proc");
		var proc_self = FS.mkdir("/proc/self");
		FS.mkdir("/proc/self/fd");
		FS.mount(
			{
				mount: () => {
					var node = FS.createNode(proc_self, "fd", 16384 | 511, 73);
					node.node_ops = {
						lookup: (parent, name) => {
							var fd = +name;
							var stream = FS.getStream(fd);
							if (!stream) throw new FS.ErrnoError(8);
							var ret = {
								parent: null,
								mount: { mountpoint: "fake" },
								node_ops: { readlink: () => stream.path },
							};
							ret.parent = ret;
							return ret;
						},
					};
					return node;
				},
			},
			{},
			"/proc/self/fd",
		);
	},
	createStandardStreams: () => {
		if (Module["stdin"]) {
			FS.createDevice("/dev", "stdin", Module["stdin"]);
		} else {
			FS.symlink("/dev/tty", "/dev/stdin");
		}
		if (Module["stdout"]) {
			FS.createDevice("/dev", "stdout", null, Module["stdout"]);
		} else {
			FS.symlink("/dev/tty", "/dev/stdout");
		}
		if (Module["stderr"]) {
			FS.createDevice("/dev", "stderr", null, Module["stderr"]);
		} else {
			FS.symlink("/dev/tty1", "/dev/stderr");
		}
		var stdin = FS.open("/dev/stdin", 0);
		var stdout = FS.open("/dev/stdout", 1);
		var stderr = FS.open("/dev/stderr", 1);
	},
	ensureErrnoError: () => {
		if (FS.ErrnoError) return;
		FS.ErrnoError = function ErrnoError(errno, node) {
			this.node = node;
			this.setErrno = function (errno) {
				this.errno = errno;
			};
			this.setErrno(errno);
			this.message = "FS error";
		};
		FS.ErrnoError.prototype = new Error();
		FS.ErrnoError.prototype.constructor = FS.ErrnoError;
		[44].forEach((code) => {
			FS.genericErrors[code] = new FS.ErrnoError(code);
			FS.genericErrors[code].stack = "<generic error, no stack>";
		});
	},
	staticInit: () => {
		FS.ensureErrnoError();
		FS.nameTable = new Array(4096);
		FS.mount(MEMFS, {}, "/");
		FS.createDefaultDirectories();
		FS.createDefaultDevices();
		FS.createSpecialDirectories();
		FS.filesystems = { MEMFS: MEMFS };
	},
	init: (input, output, error) => {
		FS.init.initialized = true;
		FS.ensureErrnoError();
		Module["stdin"] = input || Module["stdin"];
		Module["stdout"] = output || Module["stdout"];
		Module["stderr"] = error || Module["stderr"];
		FS.createStandardStreams();
	},
	quit: () => {
		FS.init.initialized = false;
		for (var i = 0; i < FS.streams.length; i++) {
			var stream = FS.streams[i];
			if (!stream) {
				continue;
			}
			FS.close(stream);
		}
	},
	getMode: (canRead, canWrite) => {
		var mode = 0;
		if (canRead) mode |= 292 | 73;
		if (canWrite) mode |= 146;
		return mode;
	},
	findObject: (path, dontResolveLastLink) => {
		var ret = FS.analyzePath(path, dontResolveLastLink);
		if (!ret.exists) {
			return null;
		}
		return ret.object;
	},
	analyzePath: (path, dontResolveLastLink) => {
		try {
			var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
			path = lookup.path;
		} catch (e) {}
		var ret = {
			isRoot: false,
			exists: false,
			error: 0,
			name: null,
			path: null,
			object: null,
			parentExists: false,
			parentPath: null,
			parentObject: null,
		};
		try {
			var lookup = FS.lookupPath(path, { parent: true });
			ret.parentExists = true;
			ret.parentPath = lookup.path;
			ret.parentObject = lookup.node;
			ret.name = PATH.basename(path);
			lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
			ret.exists = true;
			ret.path = lookup.path;
			ret.object = lookup.node;
			ret.name = lookup.node.name;
			ret.isRoot = lookup.path === "/";
		} catch (e) {
			ret.error = e.errno;
		}
		return ret;
	},
	createPath: (parent, path, canRead, canWrite) => {
		parent = typeof parent == "string" ? parent : FS.getPath(parent);
		var parts = path.split("/").reverse();
		while (parts.length) {
			var part = parts.pop();
			if (!part) continue;
			var current = PATH.join2(parent, part);
			try {
				FS.mkdir(current);
			} catch (e) {}
			parent = current;
		}
		return current;
	},
	createFile: (parent, name, properties, canRead, canWrite) => {
		var path = PATH.join2(
			typeof parent == "string" ? parent : FS.getPath(parent),
			name,
		);
		var mode = FS.getMode(canRead, canWrite);
		return FS.create(path, mode);
	},
	createDataFile: (parent, name, data, canRead, canWrite, canOwn) => {
		var path = name;
		if (parent) {
			parent = typeof parent == "string" ? parent : FS.getPath(parent);
			path = name ? PATH.join2(parent, name) : parent;
		}
		var mode = FS.getMode(canRead, canWrite);
		var node = FS.create(path, mode);
		if (data) {
			if (typeof data == "string") {
				var arr = new Array(data.length);
				for (var i = 0, len = data.length; i < len; ++i)
					arr[i] = data.charCodeAt(i);
				data = arr;
			}
			FS.chmod(node, mode | 146);
			var stream = FS.open(node, 577);
			FS.write(stream, data, 0, data.length, 0, canOwn);
			FS.close(stream);
			FS.chmod(node, mode);
		}
		return node;
	},
	createDevice: (parent, name, input, output) => {
		var path = PATH.join2(
			typeof parent == "string" ? parent : FS.getPath(parent),
			name,
		);
		var mode = FS.getMode(!!input, !!output);
		if (!FS.createDevice.major) FS.createDevice.major = 64;
		var dev = FS.makedev(FS.createDevice.major++, 0);
		FS.registerDevice(dev, {
			open: (stream) => {
				stream.seekable = false;
			},
			close: (stream) => {
				if (output && output.buffer && output.buffer.length) {
					output(10);
				}
			},
			read: (stream, buffer, offset, length, pos) => {
				var bytesRead = 0;
				for (var i = 0; i < length; i++) {
					var result;
					try {
						result = input();
					} catch (e) {
						throw new FS.ErrnoError(29);
					}
					if (result === undefined && bytesRead === 0) {
						throw new FS.ErrnoError(6);
					}
					if (result === null || result === undefined) break;
					bytesRead++;
					buffer[offset + i] = result;
				}
				if (bytesRead) {
					stream.node.timestamp = Date.now();
				}
				return bytesRead;
			},
			write: (stream, buffer, offset, length, pos) => {
				for (var i = 0; i < length; i++) {
					try {
						output(buffer[offset + i]);
					} catch (e) {
						throw new FS.ErrnoError(29);
					}
				}
				if (length) {
					stream.node.timestamp = Date.now();
				}
				return i;
			},
		});
		return FS.mkdev(path, mode, dev);
	},
	forceLoadFile: (obj) => {
		if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;
		if (typeof XMLHttpRequest != "undefined") {
			throw new Error(
				"Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.",
			);
		} else if (read_) {
			try {
				obj.contents = intArrayFromString(read_(obj.url), true);
				obj.usedBytes = obj.contents.length;
			} catch (e) {
				throw new FS.ErrnoError(29);
			}
		} else {
			throw new Error("Cannot load without read() or XMLHttpRequest.");
		}
	},
	createLazyFile: (parent, name, url, canRead, canWrite) => {
		function LazyUint8Array() {
			this.lengthKnown = false;
			this.chunks = [];
		}
		LazyUint8Array.prototype.get = function LazyUint8Array_get(idx) {
			if (idx > this.length - 1 || idx < 0) {
				return undefined;
			}
			var chunkOffset = idx % this.chunkSize;
			var chunkNum = (idx / this.chunkSize) | 0;
			return this.getter(chunkNum)[chunkOffset];
		};
		LazyUint8Array.prototype.setDataGetter =
			function LazyUint8Array_setDataGetter(getter) {
				this.getter = getter;
			};
		LazyUint8Array.prototype.cacheLength =
			function LazyUint8Array_cacheLength() {
				var xhr = new XMLHttpRequest();
				xhr.open("HEAD", url, false);
				xhr.send(null);
				if (!((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304))
					throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
				var datalength = Number(xhr.getResponseHeader("Content-length"));
				var header;
				var hasByteServing =
					(header = xhr.getResponseHeader("Accept-Ranges")) &&
					header === "bytes";
				var usesGzip =
					(header = xhr.getResponseHeader("Content-Encoding")) &&
					header === "gzip";
				var chunkSize = 1024 * 1024;
				if (!hasByteServing) chunkSize = datalength;
				var doXHR = (from, to) => {
					if (from > to)
						throw new Error(
							"invalid range (" + from + ", " + to + ") or no bytes requested!",
						);
					if (to > datalength - 1)
						throw new Error(
							"only " + datalength + " bytes available! programmer error!",
						);
					var xhr = new XMLHttpRequest();
					xhr.open("GET", url, false);
					if (datalength !== chunkSize)
						xhr.setRequestHeader("Range", "bytes=" + from + "-" + to);
					xhr.responseType = "arraybuffer";
					if (xhr.overrideMimeType) {
						xhr.overrideMimeType("text/plain; charset=x-user-defined");
					}
					xhr.send(null);
					if (!((xhr.status >= 200 && xhr.status < 300) || xhr.status === 304))
						throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
					if (xhr.response !== undefined) {
						return new Uint8Array(xhr.response || []);
					}
					return intArrayFromString(xhr.responseText || "", true);
				};
				var lazyArray = this;
				lazyArray.setDataGetter((chunkNum) => {
					var start = chunkNum * chunkSize;
					var end = (chunkNum + 1) * chunkSize - 1;
					end = Math.min(end, datalength - 1);
					if (typeof lazyArray.chunks[chunkNum] == "undefined") {
						lazyArray.chunks[chunkNum] = doXHR(start, end);
					}
					if (typeof lazyArray.chunks[chunkNum] == "undefined")
						throw new Error("doXHR failed!");
					return lazyArray.chunks[chunkNum];
				});
				if (usesGzip || !datalength) {
					chunkSize = datalength = 1;
					datalength = this.getter(0).length;
					chunkSize = datalength;
					out(
						"LazyFiles on gzip forces download of the whole file when length is accessed",
					);
				}
				this._length = datalength;
				this._chunkSize = chunkSize;
				this.lengthKnown = true;
			};
		if (typeof XMLHttpRequest != "undefined") {
			if (!ENVIRONMENT_IS_WORKER)
				throw "Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";
			var lazyArray = new LazyUint8Array();
			Object.defineProperties(lazyArray, {
				length: {
					get: function () {
						if (!this.lengthKnown) {
							this.cacheLength();
						}
						return this._length;
					},
				},
				chunkSize: {
					get: function () {
						if (!this.lengthKnown) {
							this.cacheLength();
						}
						return this._chunkSize;
					},
				},
			});
			var properties = { isDevice: false, contents: lazyArray };
		} else {
			var properties = { isDevice: false, url: url };
		}
		var node = FS.createFile(parent, name, properties, canRead, canWrite);
		if (properties.contents) {
			node.contents = properties.contents;
		} else if (properties.url) {
			node.contents = null;
			node.url = properties.url;
		}
		Object.defineProperties(node, {
			usedBytes: {
				get: function () {
					return this.contents.length;
				},
			},
		});
		var stream_ops = {};
		var keys = Object.keys(node.stream_ops);
		keys.forEach((key) => {
			var fn = node.stream_ops[key];
			stream_ops[key] = function forceLoadLazyFile() {
				FS.forceLoadFile(node);
				return fn.apply(null, arguments);
			};
		});
		function writeChunks(stream, buffer, offset, length, position) {
			var contents = stream.node.contents;
			if (position >= contents.length) return 0;
			var size = Math.min(contents.length - position, length);
			if (contents.slice) {
				for (var i = 0; i < size; i++) {
					buffer[offset + i] = contents[position + i];
				}
			} else {
				for (var i = 0; i < size; i++) {
					buffer[offset + i] = contents.get(position + i);
				}
			}
			return size;
		}
		stream_ops.read = (stream, buffer, offset, length, position) => {
			FS.forceLoadFile(node);
			return writeChunks(stream, buffer, offset, length, position);
		};
		stream_ops.mmap = (stream, length, position, prot, flags) => {
			FS.forceLoadFile(node);
			var ptr = mmapAlloc(length);
			if (!ptr) {
				throw new FS.ErrnoError(48);
			}
			writeChunks(stream, HEAP8, ptr, length, position);
			return { ptr: ptr, allocated: true };
		};
		node.stream_ops = stream_ops;
		return node;
	},
	createPreloadedFile: (
		parent,
		name,
		url,
		canRead,
		canWrite,
		onload,
		onerror,
		dontCreateFile,
		canOwn,
		preFinish,
	) => {
		var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
		var dep = getUniqueRunDependency("cp " + fullname);
		function processData(byteArray) {
			function finish(byteArray) {
				if (preFinish) preFinish();
				if (!dontCreateFile) {
					FS.createDataFile(parent, name, byteArray, canRead, canWrite, canOwn);
				}
				if (onload) onload();
				removeRunDependency(dep);
			}
			if (
				Browser.handledByPreloadPlugin(byteArray, fullname, finish, () => {
					if (onerror) onerror();
					removeRunDependency(dep);
				})
			) {
				return;
			}
			finish(byteArray);
		}
		addRunDependency(dep);
		if (typeof url == "string") {
			asyncLoad(url, (byteArray) => processData(byteArray), onerror);
		} else {
			processData(url);
		}
	},
	indexedDB: () => {
		return (
			window.indexedDB ||
			window.mozIndexedDB ||
			window.webkitIndexedDB ||
			window.msIndexedDB
		);
	},
	DB_NAME: () => {
		return "EM_FS_" + window.location.pathname;
	},
	DB_VERSION: 20,
	DB_STORE_NAME: "FILE_DATA",
	saveFilesToDB: (paths, onload, onerror) => {
		onload = onload || (() => {});
		onerror = onerror || (() => {});
		var indexedDB = FS.indexedDB();
		try {
			var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
		} catch (e) {
			return onerror(e);
		}
		openRequest.onupgradeneeded = () => {
			out("creating db");
			var db = openRequest.result;
			db.createObjectStore(FS.DB_STORE_NAME);
		};
		openRequest.onsuccess = () => {
			var db = openRequest.result;
			var transaction = db.transaction([FS.DB_STORE_NAME], "readwrite");
			var files = transaction.objectStore(FS.DB_STORE_NAME);
			var ok = 0,
				fail = 0,
				total = paths.length;
			function finish() {
				if (fail == 0) onload();
				else onerror();
			}
			paths.forEach((path) => {
				var putRequest = files.put(FS.analyzePath(path).object.contents, path);
				putRequest.onsuccess = () => {
					ok++;
					if (ok + fail == total) finish();
				};
				putRequest.onerror = () => {
					fail++;
					if (ok + fail == total) finish();
				};
			});
			transaction.onerror = onerror;
		};
		openRequest.onerror = onerror;
	},
	loadFilesFromDB: (paths, onload, onerror) => {
		onload = onload || (() => {});
		onerror = onerror || (() => {});
		var indexedDB = FS.indexedDB();
		try {
			var openRequest = indexedDB.open(FS.DB_NAME(), FS.DB_VERSION);
		} catch (e) {
			return onerror(e);
		}
		openRequest.onupgradeneeded = onerror;
		openRequest.onsuccess = () => {
			var db = openRequest.result;
			try {
				var transaction = db.transaction([FS.DB_STORE_NAME], "readonly");
			} catch (e) {
				onerror(e);
				return;
			}
			var files = transaction.objectStore(FS.DB_STORE_NAME);
			var ok = 0,
				fail = 0,
				total = paths.length;
			function finish() {
				if (fail == 0) onload();
				else onerror();
			}
			paths.forEach((path) => {
				var getRequest = files.get(path);
				getRequest.onsuccess = () => {
					if (FS.analyzePath(path).exists) {
						FS.unlink(path);
					}
					FS.createDataFile(
						PATH.dirname(path),
						PATH.basename(path),
						getRequest.result,
						true,
						true,
						true,
					);
					ok++;
					if (ok + fail == total) finish();
				};
				getRequest.onerror = () => {
					fail++;
					if (ok + fail == total) finish();
				};
			});
			transaction.onerror = onerror;
		};
		openRequest.onerror = onerror;
	},
};
var SYSCALLS = {
	DEFAULT_POLLMASK: 5,
	calculateAt: function (dirfd, path, allowEmpty) {
		if (PATH.isAbs(path)) {
			return path;
		}
		var dir;
		if (dirfd === -100) {
			dir = FS.cwd();
		} else {
			var dirstream = FS.getStream(dirfd);
			if (!dirstream) throw new FS.ErrnoError(8);
			dir = dirstream.path;
		}
		if (path.length == 0) {
			if (!allowEmpty) {
				throw new FS.ErrnoError(44);
			}
			return dir;
		}
		return PATH.join2(dir, path);
	},
	doStat: function (func, path, buf) {
		try {
			var stat = func(path);
		} catch (e) {
			if (
				e &&
				e.node &&
				PATH.normalize(path) !== PATH.normalize(FS.getPath(e.node))
			) {
				return -54;
			}
			throw e;
		}
		HEAP32[buf >> 2] = stat.dev;
		HEAP32[(buf + 8) >> 2] = stat.ino;
		HEAP32[(buf + 12) >> 2] = stat.mode;
		HEAP32[(buf + 16) >> 2] = stat.nlink;
		HEAP32[(buf + 20) >> 2] = stat.uid;
		HEAP32[(buf + 24) >> 2] = stat.gid;
		HEAP32[(buf + 28) >> 2] = stat.rdev;
		(tempI64 = [
			stat.size >>> 0,
			((tempDouble = stat.size),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[(buf + 40) >> 2] = tempI64[0]),
			(HEAP32[(buf + 44) >> 2] = tempI64[1]);
		HEAP32[(buf + 48) >> 2] = 4096;
		HEAP32[(buf + 52) >> 2] = stat.blocks;
		(tempI64 = [
			Math.floor(stat.atime.getTime() / 1e3) >>> 0,
			((tempDouble = Math.floor(stat.atime.getTime() / 1e3)),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[(buf + 56) >> 2] = tempI64[0]),
			(HEAP32[(buf + 60) >> 2] = tempI64[1]);
		HEAP32[(buf + 64) >> 2] = 0;
		(tempI64 = [
			Math.floor(stat.mtime.getTime() / 1e3) >>> 0,
			((tempDouble = Math.floor(stat.mtime.getTime() / 1e3)),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[(buf + 72) >> 2] = tempI64[0]),
			(HEAP32[(buf + 76) >> 2] = tempI64[1]);
		HEAP32[(buf + 80) >> 2] = 0;
		(tempI64 = [
			Math.floor(stat.ctime.getTime() / 1e3) >>> 0,
			((tempDouble = Math.floor(stat.ctime.getTime() / 1e3)),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[(buf + 88) >> 2] = tempI64[0]),
			(HEAP32[(buf + 92) >> 2] = tempI64[1]);
		HEAP32[(buf + 96) >> 2] = 0;
		(tempI64 = [
			stat.ino >>> 0,
			((tempDouble = stat.ino),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[(buf + 104) >> 2] = tempI64[0]),
			(HEAP32[(buf + 108) >> 2] = tempI64[1]);
		return 0;
	},
	doMsync: function (addr, stream, len, flags, offset) {
		var buffer = HEAPU8.slice(addr, addr + len);
		FS.msync(stream, buffer, offset, len, flags);
	},
	varargs: undefined,
	get: function () {
		SYSCALLS.varargs += 4;
		var ret = HEAP32[(SYSCALLS.varargs - 4) >> 2];
		return ret;
	},
	getStr: function (ptr) {
		var ret = UTF8ToString(ptr);
		return ret;
	},
	getStreamFromFD: function (fd) {
		var stream = FS.getStream(fd);
		if (!stream) throw new FS.ErrnoError(8);
		return stream;
	},
};
function ___syscall_chdir(path) {
	try {
		path = SYSCALLS.getStr(path);
		FS.chdir(path);
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function setErrNo(value) {
	HEAP32[___errno_location() >> 2] = value;
	return value;
}
function ___syscall_fcntl64(fd, cmd, varargs) {
	SYSCALLS.varargs = varargs;
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		switch (cmd) {
			case 0: {
				var arg = SYSCALLS.get();
				if (arg < 0) {
					return -28;
				}
				var newStream;
				newStream = FS.createStream(stream, arg);
				return newStream.fd;
			}
			case 1:
			case 2:
				return 0;
			case 3:
				return stream.flags;
			case 4: {
				var arg = SYSCALLS.get();
				stream.flags |= arg;
				return 0;
			}
			case 5: {
				var arg = SYSCALLS.get();
				var offset = 0;
				HEAP16[(arg + offset) >> 1] = 2;
				return 0;
			}
			case 6:
			case 7:
				return 0;
			case 16:
			case 8:
				return -28;
			case 9:
				setErrNo(28);
				return -1;
			default: {
				return -28;
			}
		}
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_fstat64(fd, buf) {
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		return SYSCALLS.doStat(FS.stat, stream.path, buf);
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function convertI32PairToI53Checked(lo, hi) {
	return (hi + 2097152) >>> 0 < 4194305 - !!lo
		? (lo >>> 0) + hi * 4294967296
		: NaN;
}
function ___syscall_ftruncate64(fd, length_low, length_high) {
	try {
		var length = convertI32PairToI53Checked(length_low, length_high);
		if (isNaN(length)) return -61;
		FS.ftruncate(fd, length);
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_getcwd(buf, size) {
	try {
		if (size === 0) return -28;
		var cwd = FS.cwd();
		var cwdLengthInBytes = lengthBytesUTF8(cwd) + 1;
		if (size < cwdLengthInBytes) return -68;
		stringToUTF8(cwd, buf, size);
		return cwdLengthInBytes;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_getdents64(fd, dirp, count) {
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		if (!stream.getdents) {
			stream.getdents = FS.readdir(stream.path);
		}
		var struct_size = 280;
		var pos = 0;
		var off = FS.llseek(stream, 0, 1);
		var idx = Math.floor(off / struct_size);
		while (idx < stream.getdents.length && pos + struct_size <= count) {
			var id;
			var type;
			var name = stream.getdents[idx];
			if (name === ".") {
				id = stream.node.id;
				type = 4;
			} else if (name === "..") {
				var lookup = FS.lookupPath(stream.path, { parent: true });
				id = lookup.node.id;
				type = 4;
			} else {
				var child = FS.lookupNode(stream.node, name);
				id = child.id;
				type = FS.isChrdev(child.mode)
					? 2
					: FS.isDir(child.mode)
						? 4
						: FS.isLink(child.mode)
							? 10
							: 8;
			}
			(tempI64 = [
				id >>> 0,
				((tempDouble = id),
				+Math.abs(tempDouble) >= 1
					? tempDouble > 0
						? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) |
								0) >>>
							0
						: ~~+Math.ceil(
								(tempDouble - +(~~tempDouble >>> 0)) / 4294967296,
							) >>> 0
					: 0),
			]),
				(HEAP32[(dirp + pos) >> 2] = tempI64[0]),
				(HEAP32[(dirp + pos + 4) >> 2] = tempI64[1]);
			(tempI64 = [
				((idx + 1) * struct_size) >>> 0,
				((tempDouble = (idx + 1) * struct_size),
				+Math.abs(tempDouble) >= 1
					? tempDouble > 0
						? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) |
								0) >>>
							0
						: ~~+Math.ceil(
								(tempDouble - +(~~tempDouble >>> 0)) / 4294967296,
							) >>> 0
					: 0),
			]),
				(HEAP32[(dirp + pos + 8) >> 2] = tempI64[0]),
				(HEAP32[(dirp + pos + 12) >> 2] = tempI64[1]);
			HEAP16[(dirp + pos + 16) >> 1] = 280;
			HEAP8[(dirp + pos + 18) >> 0] = type;
			stringToUTF8(name, dirp + pos + 19, 256);
			pos += struct_size;
			idx += 1;
		}
		FS.llseek(stream, idx * struct_size, 0);
		return pos;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_ioctl(fd, op, varargs) {
	SYSCALLS.varargs = varargs;
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		switch (op) {
			case 21509:
			case 21505: {
				if (!stream.tty) return -59;
				return 0;
			}
			case 21510:
			case 21511:
			case 21512:
			case 21506:
			case 21507:
			case 21508: {
				if (!stream.tty) return -59;
				return 0;
			}
			case 21519: {
				if (!stream.tty) return -59;
				var argp = SYSCALLS.get();
				HEAP32[argp >> 2] = 0;
				return 0;
			}
			case 21520: {
				if (!stream.tty) return -59;
				return -28;
			}
			case 21531: {
				var argp = SYSCALLS.get();
				return FS.ioctl(stream, op, argp);
			}
			case 21523: {
				if (!stream.tty) return -59;
				return 0;
			}
			case 21524: {
				if (!stream.tty) return -59;
				return 0;
			}
			default:
				return -28;
		}
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_lstat64(path, buf) {
	try {
		path = SYSCALLS.getStr(path);
		return SYSCALLS.doStat(FS.lstat, path, buf);
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_mkdirat(dirfd, path, mode) {
	try {
		path = SYSCALLS.getStr(path);
		path = SYSCALLS.calculateAt(dirfd, path);
		path = PATH.normalize(path);
		if (path[path.length - 1] === "/") path = path.substr(0, path.length - 1);
		FS.mkdir(path, mode, 0);
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_newfstatat(dirfd, path, buf, flags) {
	try {
		path = SYSCALLS.getStr(path);
		var nofollow = flags & 256;
		var allowEmpty = flags & 4096;
		flags = flags & ~4352;
		path = SYSCALLS.calculateAt(dirfd, path, allowEmpty);
		return SYSCALLS.doStat(nofollow ? FS.lstat : FS.stat, path, buf);
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_openat(dirfd, path, flags, varargs) {
	SYSCALLS.varargs = varargs;
	try {
		path = SYSCALLS.getStr(path);
		path = SYSCALLS.calculateAt(dirfd, path);
		var mode = varargs ? SYSCALLS.get() : 0;
		return FS.open(path, flags, mode).fd;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_rmdir(path) {
	try {
		path = SYSCALLS.getStr(path);
		FS.rmdir(path);
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_stat64(path, buf) {
	try {
		path = SYSCALLS.getStr(path);
		return SYSCALLS.doStat(FS.stat, path, buf);
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function ___syscall_unlinkat(dirfd, path, flags) {
	try {
		path = SYSCALLS.getStr(path);
		path = SYSCALLS.calculateAt(dirfd, path);
		if (flags === 0) {
			FS.unlink(path);
		} else if (flags === 512) {
			FS.rmdir(path);
		} else {
			abort("Invalid flags passed to unlinkat");
		}
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function __emscripten_date_now() {
	return Date.now();
}
var nowIsMonotonic = true;
function __emscripten_get_now_is_monotonic() {
	return nowIsMonotonic;
}
function __emscripten_throw_longjmp() {
	throw Infinity;
}
function readI53FromI64(ptr) {
	return HEAPU32[ptr >> 2] + HEAP32[(ptr + 4) >> 2] * 4294967296;
}
function __gmtime_js(time, tmPtr) {
	var date = new Date(readI53FromI64(time) * 1e3);
	HEAP32[tmPtr >> 2] = date.getUTCSeconds();
	HEAP32[(tmPtr + 4) >> 2] = date.getUTCMinutes();
	HEAP32[(tmPtr + 8) >> 2] = date.getUTCHours();
	HEAP32[(tmPtr + 12) >> 2] = date.getUTCDate();
	HEAP32[(tmPtr + 16) >> 2] = date.getUTCMonth();
	HEAP32[(tmPtr + 20) >> 2] = date.getUTCFullYear() - 1900;
	HEAP32[(tmPtr + 24) >> 2] = date.getUTCDay();
	var start = Date.UTC(date.getUTCFullYear(), 0, 1, 0, 0, 0, 0);
	var yday = ((date.getTime() - start) / (1e3 * 60 * 60 * 24)) | 0;
	HEAP32[(tmPtr + 28) >> 2] = yday;
}
function __localtime_js(time, tmPtr) {
	var date = new Date(readI53FromI64(time) * 1e3);
	HEAP32[tmPtr >> 2] = date.getSeconds();
	HEAP32[(tmPtr + 4) >> 2] = date.getMinutes();
	HEAP32[(tmPtr + 8) >> 2] = date.getHours();
	HEAP32[(tmPtr + 12) >> 2] = date.getDate();
	HEAP32[(tmPtr + 16) >> 2] = date.getMonth();
	HEAP32[(tmPtr + 20) >> 2] = date.getFullYear() - 1900;
	HEAP32[(tmPtr + 24) >> 2] = date.getDay();
	var start = new Date(date.getFullYear(), 0, 1);
	var yday = ((date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24)) | 0;
	HEAP32[(tmPtr + 28) >> 2] = yday;
	HEAP32[(tmPtr + 36) >> 2] = -(date.getTimezoneOffset() * 60);
	var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
	var winterOffset = start.getTimezoneOffset();
	var dst =
		(summerOffset != winterOffset &&
			date.getTimezoneOffset() == Math.min(winterOffset, summerOffset)) | 0;
	HEAP32[(tmPtr + 32) >> 2] = dst;
}
function __mktime_js(tmPtr) {
	var date = new Date(
		HEAP32[(tmPtr + 20) >> 2] + 1900,
		HEAP32[(tmPtr + 16) >> 2],
		HEAP32[(tmPtr + 12) >> 2],
		HEAP32[(tmPtr + 8) >> 2],
		HEAP32[(tmPtr + 4) >> 2],
		HEAP32[tmPtr >> 2],
		0,
	);
	var dst = HEAP32[(tmPtr + 32) >> 2];
	var guessedOffset = date.getTimezoneOffset();
	var start = new Date(date.getFullYear(), 0, 1);
	var summerOffset = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();
	var winterOffset = start.getTimezoneOffset();
	var dstOffset = Math.min(winterOffset, summerOffset);
	if (dst < 0) {
		HEAP32[(tmPtr + 32) >> 2] = Number(
			summerOffset != winterOffset && dstOffset == guessedOffset,
		);
	} else if (dst > 0 != (dstOffset == guessedOffset)) {
		var nonDstOffset = Math.max(winterOffset, summerOffset);
		var trueOffset = dst > 0 ? dstOffset : nonDstOffset;
		date.setTime(date.getTime() + (trueOffset - guessedOffset) * 6e4);
	}
	HEAP32[(tmPtr + 24) >> 2] = date.getDay();
	var yday = ((date.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24)) | 0;
	HEAP32[(tmPtr + 28) >> 2] = yday;
	HEAP32[tmPtr >> 2] = date.getSeconds();
	HEAP32[(tmPtr + 4) >> 2] = date.getMinutes();
	HEAP32[(tmPtr + 8) >> 2] = date.getHours();
	HEAP32[(tmPtr + 12) >> 2] = date.getDate();
	HEAP32[(tmPtr + 16) >> 2] = date.getMonth();
	return (date.getTime() / 1e3) | 0;
}
function __mmap_js(len, prot, flags, fd, off, allocated) {
	try {
		var stream = FS.getStream(fd);
		if (!stream) return -8;
		var res = FS.mmap(stream, len, off, prot, flags);
		var ptr = res.ptr;
		HEAP32[allocated >> 2] = res.allocated;
		return ptr;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function __munmap_js(addr, len, prot, flags, fd, offset) {
	try {
		var stream = FS.getStream(fd);
		if (stream) {
			if (prot & 2) {
				SYSCALLS.doMsync(addr, stream, len, flags, offset);
			}
			FS.munmap(stream);
		}
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return -e.errno;
	}
}
function allocateUTF8(str) {
	var size = lengthBytesUTF8(str) + 1;
	var ret = _malloc(size);
	if (ret) stringToUTF8Array(str, HEAP8, ret, size);
	return ret;
}
function _tzset_impl(timezone, daylight, tzname) {
	var currentYear = new Date().getFullYear();
	var winter = new Date(currentYear, 0, 1);
	var summer = new Date(currentYear, 6, 1);
	var winterOffset = winter.getTimezoneOffset();
	var summerOffset = summer.getTimezoneOffset();
	var stdTimezoneOffset = Math.max(winterOffset, summerOffset);
	HEAP32[timezone >> 2] = stdTimezoneOffset * 60;
	HEAP32[daylight >> 2] = Number(winterOffset != summerOffset);
	function extractZone(date) {
		var match = date.toTimeString().match(/\(([A-Za-z ]+)\)$/);
		return match ? match[1] : "GMT";
	}
	var winterName = extractZone(winter);
	var summerName = extractZone(summer);
	var winterNamePtr = allocateUTF8(winterName);
	var summerNamePtr = allocateUTF8(summerName);
	if (summerOffset < winterOffset) {
		HEAPU32[tzname >> 2] = winterNamePtr;
		HEAPU32[(tzname + 4) >> 2] = summerNamePtr;
	} else {
		HEAPU32[tzname >> 2] = summerNamePtr;
		HEAPU32[(tzname + 4) >> 2] = winterNamePtr;
	}
}
function __tzset_js(timezone, daylight, tzname) {
	if (__tzset_js.called) return;
	__tzset_js.called = true;
	_tzset_impl(timezone, daylight, tzname);
}
function _abort() {
	abort("");
}
var readAsmConstArgsArray = [];
function readAsmConstArgs(sigPtr, buf) {
	readAsmConstArgsArray.length = 0;
	var ch;
	buf >>= 2;
	while ((ch = HEAPU8[sigPtr++])) {
		buf += (ch != 105) & buf;
		readAsmConstArgsArray.push(ch == 105 ? HEAP32[buf] : HEAPF64[buf++ >> 1]);
		++buf;
	}
	return readAsmConstArgsArray;
}
function _emscripten_asm_const_int(code, sigPtr, argbuf) {
	var args = readAsmConstArgs(sigPtr, argbuf);
	return ASM_CONSTS[code].apply(null, args);
}
var _emscripten_get_now;
_emscripten_get_now = () => performance.now();
function _emscripten_memcpy_big(dest, src, num) {
	HEAPU8.copyWithin(dest, src, src + num);
}
function getHeapMax() {
	return 2147483648;
}
function emscripten_realloc_buffer(size) {
	try {
		wasmMemory.grow((size - buffer.byteLength + 65535) >>> 16);
		updateGlobalBufferAndViews(wasmMemory.buffer);
		return 1;
	} catch (e) {}
}
function _emscripten_resize_heap(requestedSize) {
	var oldSize = HEAPU8.length;
	requestedSize = requestedSize >>> 0;
	var maxHeapSize = getHeapMax();
	if (requestedSize > maxHeapSize) {
		return false;
	}
	let alignUp = (x, multiple) => x + ((multiple - (x % multiple)) % multiple);
	for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
		var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);
		overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);
		var newSize = Math.min(
			maxHeapSize,
			alignUp(Math.max(requestedSize, overGrownHeapSize), 65536),
		);
		var replacement = emscripten_realloc_buffer(newSize);
		if (replacement) {
			return true;
		}
	}
	return false;
}
var ENV = {};
function getExecutableName() {
	return thisProgram || "./this.program";
}
function getEnvStrings() {
	if (!getEnvStrings.strings) {
		var lang =
			(
				(typeof navigator == "object" &&
					navigator.languages &&
					navigator.languages[0]) ||
				"C"
			).replace("-", "_") + ".UTF-8";
		var env = {
			USER: "web_user",
			LOGNAME: "web_user",
			PATH: "/",
			PWD: "/",
			HOME: "/home/<USER>",
			LANG: lang,
			_: getExecutableName(),
		};
		for (var x in ENV) {
			if (ENV[x] === undefined) delete env[x];
			else env[x] = ENV[x];
		}
		var strings = [];
		for (var x in env) {
			strings.push(x + "=" + env[x]);
		}
		getEnvStrings.strings = strings;
	}
	return getEnvStrings.strings;
}
function writeAsciiToMemory(str, buffer, dontAddNull) {
	for (var i = 0; i < str.length; ++i) {
		HEAP8[buffer++ >> 0] = str.charCodeAt(i);
	}
	if (!dontAddNull) HEAP8[buffer >> 0] = 0;
}
function _environ_get(__environ, environ_buf) {
	var bufSize = 0;
	getEnvStrings().forEach(function (string, i) {
		var ptr = environ_buf + bufSize;
		HEAPU32[(__environ + i * 4) >> 2] = ptr;
		writeAsciiToMemory(string, ptr);
		bufSize += string.length + 1;
	});
	return 0;
}
function _environ_sizes_get(penviron_count, penviron_buf_size) {
	var strings = getEnvStrings();
	HEAPU32[penviron_count >> 2] = strings.length;
	var bufSize = 0;
	strings.forEach(function (string) {
		bufSize += string.length + 1;
	});
	HEAPU32[penviron_buf_size >> 2] = bufSize;
	return 0;
}
function _proc_exit(code) {
	EXITSTATUS = code;
	if (!keepRuntimeAlive()) {
		if (Module["onExit"]) Module["onExit"](code);
		ABORT = true;
	}
	quit_(code, new ExitStatus(code));
}
function exitJS(status, implicit) {
	EXITSTATUS = status;
	_proc_exit(status);
}
var _exit = exitJS;
function _fd_close(fd) {
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		FS.close(stream);
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return e.errno;
	}
}
function doReadv(stream, iov, iovcnt, offset) {
	var ret = 0;
	for (var i = 0; i < iovcnt; i++) {
		var ptr = HEAPU32[iov >> 2];
		var len = HEAPU32[(iov + 4) >> 2];
		iov += 8;
		var curr = FS.read(stream, HEAP8, ptr, len, offset);
		if (curr < 0) return -1;
		ret += curr;
		if (curr < len) break;
	}
	return ret;
}
function _fd_read(fd, iov, iovcnt, pnum) {
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		var num = doReadv(stream, iov, iovcnt);
		HEAP32[pnum >> 2] = num;
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return e.errno;
	}
}
function _fd_seek(fd, offset_low, offset_high, whence, newOffset) {
	try {
		var offset = convertI32PairToI53Checked(offset_low, offset_high);
		if (isNaN(offset)) return 61;
		var stream = SYSCALLS.getStreamFromFD(fd);
		FS.llseek(stream, offset, whence);
		(tempI64 = [
			stream.position >>> 0,
			((tempDouble = stream.position),
			+Math.abs(tempDouble) >= 1
				? tempDouble > 0
					? (Math.min(+Math.floor(tempDouble / 4294967296), 4294967295) | 0) >>>
						0
					: ~~+Math.ceil((tempDouble - +(~~tempDouble >>> 0)) / 4294967296) >>>
						0
				: 0),
		]),
			(HEAP32[newOffset >> 2] = tempI64[0]),
			(HEAP32[(newOffset + 4) >> 2] = tempI64[1]);
		if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null;
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return e.errno;
	}
}
function doWritev(stream, iov, iovcnt, offset) {
	var ret = 0;
	for (var i = 0; i < iovcnt; i++) {
		var ptr = HEAPU32[iov >> 2];
		var len = HEAPU32[(iov + 4) >> 2];
		iov += 8;
		var curr = FS.write(stream, HEAP8, ptr, len, offset);
		if (curr < 0) return -1;
		ret += curr;
	}
	return ret;
}
function _fd_write(fd, iov, iovcnt, pnum) {
	try {
		var stream = SYSCALLS.getStreamFromFD(fd);
		var num = doWritev(stream, iov, iovcnt);
		HEAPU32[pnum >> 2] = num;
		return 0;
	} catch (e) {
		if (typeof FS == "undefined" || !(e instanceof FS.ErrnoError)) throw e;
		return e.errno;
	}
}
function _getTempRet0() {
	return getTempRet0();
}
function _llvm_eh_typeid_for(type) {
	return type;
}
function _setTempRet0(val) {
	setTempRet0(val);
}
function __isLeapYear(year) {
	return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
}
function __arraySum(array, index) {
	var sum = 0;
	for (var i = 0; i <= index; sum += array[i++]) {}
	return sum;
}
var __MONTH_DAYS_LEAP = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var __MONTH_DAYS_REGULAR = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
function __addDays(date, days) {
	var newDate = new Date(date.getTime());
	while (days > 0) {
		var leap = __isLeapYear(newDate.getFullYear());
		var currentMonth = newDate.getMonth();
		var daysInCurrentMonth = (leap ? __MONTH_DAYS_LEAP : __MONTH_DAYS_REGULAR)[
			currentMonth
		];
		if (days > daysInCurrentMonth - newDate.getDate()) {
			days -= daysInCurrentMonth - newDate.getDate() + 1;
			newDate.setDate(1);
			if (currentMonth < 11) {
				newDate.setMonth(currentMonth + 1);
			} else {
				newDate.setMonth(0);
				newDate.setFullYear(newDate.getFullYear() + 1);
			}
		} else {
			newDate.setDate(newDate.getDate() + days);
			return newDate;
		}
	}
	return newDate;
}
function _strftime(s, maxsize, format, tm) {
	var tm_zone = HEAP32[(tm + 40) >> 2];
	var date = {
		tm_sec: HEAP32[tm >> 2],
		tm_min: HEAP32[(tm + 4) >> 2],
		tm_hour: HEAP32[(tm + 8) >> 2],
		tm_mday: HEAP32[(tm + 12) >> 2],
		tm_mon: HEAP32[(tm + 16) >> 2],
		tm_year: HEAP32[(tm + 20) >> 2],
		tm_wday: HEAP32[(tm + 24) >> 2],
		tm_yday: HEAP32[(tm + 28) >> 2],
		tm_isdst: HEAP32[(tm + 32) >> 2],
		tm_gmtoff: HEAP32[(tm + 36) >> 2],
		tm_zone: tm_zone ? UTF8ToString(tm_zone) : "",
	};
	var pattern = UTF8ToString(format);
	var EXPANSION_RULES_1 = {
		"%c": "%a %b %d %H:%M:%S %Y",
		"%D": "%m/%d/%y",
		"%F": "%Y-%m-%d",
		"%h": "%b",
		"%r": "%I:%M:%S %p",
		"%R": "%H:%M",
		"%T": "%H:%M:%S",
		"%x": "%m/%d/%y",
		"%X": "%H:%M:%S",
		"%Ec": "%c",
		"%EC": "%C",
		"%Ex": "%m/%d/%y",
		"%EX": "%H:%M:%S",
		"%Ey": "%y",
		"%EY": "%Y",
		"%Od": "%d",
		"%Oe": "%e",
		"%OH": "%H",
		"%OI": "%I",
		"%Om": "%m",
		"%OM": "%M",
		"%OS": "%S",
		"%Ou": "%u",
		"%OU": "%U",
		"%OV": "%V",
		"%Ow": "%w",
		"%OW": "%W",
		"%Oy": "%y",
	};
	for (var rule in EXPANSION_RULES_1) {
		pattern = pattern.replace(new RegExp(rule, "g"), EXPANSION_RULES_1[rule]);
	}
	var WEEKDAYS = [
		"Sunday",
		"Monday",
		"Tuesday",
		"Wednesday",
		"Thursday",
		"Friday",
		"Saturday",
	];
	var MONTHS = [
		"January",
		"February",
		"March",
		"April",
		"May",
		"June",
		"July",
		"August",
		"September",
		"October",
		"November",
		"December",
	];
	function leadingSomething(value, digits, character) {
		var str = typeof value == "number" ? value.toString() : value || "";
		while (str.length < digits) {
			str = character[0] + str;
		}
		return str;
	}
	function leadingNulls(value, digits) {
		return leadingSomething(value, digits, "0");
	}
	function compareByDay(date1, date2) {
		function sgn(value) {
			return value < 0 ? -1 : value > 0 ? 1 : 0;
		}
		var compare;
		if ((compare = sgn(date1.getFullYear() - date2.getFullYear())) === 0) {
			if ((compare = sgn(date1.getMonth() - date2.getMonth())) === 0) {
				compare = sgn(date1.getDate() - date2.getDate());
			}
		}
		return compare;
	}
	function getFirstWeekStartDate(janFourth) {
		switch (janFourth.getDay()) {
			case 0:
				return new Date(janFourth.getFullYear() - 1, 11, 29);
			case 1:
				return janFourth;
			case 2:
				return new Date(janFourth.getFullYear(), 0, 3);
			case 3:
				return new Date(janFourth.getFullYear(), 0, 2);
			case 4:
				return new Date(janFourth.getFullYear(), 0, 1);
			case 5:
				return new Date(janFourth.getFullYear() - 1, 11, 31);
			case 6:
				return new Date(janFourth.getFullYear() - 1, 11, 30);
		}
	}
	function getWeekBasedYear(date) {
		var thisDate = __addDays(new Date(date.tm_year + 1900, 0, 1), date.tm_yday);
		var janFourthThisYear = new Date(thisDate.getFullYear(), 0, 4);
		var janFourthNextYear = new Date(thisDate.getFullYear() + 1, 0, 4);
		var firstWeekStartThisYear = getFirstWeekStartDate(janFourthThisYear);
		var firstWeekStartNextYear = getFirstWeekStartDate(janFourthNextYear);
		if (compareByDay(firstWeekStartThisYear, thisDate) <= 0) {
			if (compareByDay(firstWeekStartNextYear, thisDate) <= 0) {
				return thisDate.getFullYear() + 1;
			}
			return thisDate.getFullYear();
		}
		return thisDate.getFullYear() - 1;
	}
	var EXPANSION_RULES_2 = {
		"%a": function (date) {
			return WEEKDAYS[date.tm_wday].substring(0, 3);
		},
		"%A": function (date) {
			return WEEKDAYS[date.tm_wday];
		},
		"%b": function (date) {
			return MONTHS[date.tm_mon].substring(0, 3);
		},
		"%B": function (date) {
			return MONTHS[date.tm_mon];
		},
		"%C": function (date) {
			var year = date.tm_year + 1900;
			return leadingNulls((year / 100) | 0, 2);
		},
		"%d": function (date) {
			return leadingNulls(date.tm_mday, 2);
		},
		"%e": function (date) {
			return leadingSomething(date.tm_mday, 2, " ");
		},
		"%g": function (date) {
			return getWeekBasedYear(date).toString().substring(2);
		},
		"%G": function (date) {
			return getWeekBasedYear(date);
		},
		"%H": function (date) {
			return leadingNulls(date.tm_hour, 2);
		},
		"%I": function (date) {
			var twelveHour = date.tm_hour;
			if (twelveHour == 0) twelveHour = 12;
			else if (twelveHour > 12) twelveHour -= 12;
			return leadingNulls(twelveHour, 2);
		},
		"%j": function (date) {
			return leadingNulls(
				date.tm_mday +
					__arraySum(
						__isLeapYear(date.tm_year + 1900)
							? __MONTH_DAYS_LEAP
							: __MONTH_DAYS_REGULAR,
						date.tm_mon - 1,
					),
				3,
			);
		},
		"%m": function (date) {
			return leadingNulls(date.tm_mon + 1, 2);
		},
		"%M": function (date) {
			return leadingNulls(date.tm_min, 2);
		},
		"%n": function () {
			return "\n";
		},
		"%p": function (date) {
			if (date.tm_hour >= 0 && date.tm_hour < 12) {
				return "AM";
			}
			return "PM";
		},
		"%S": function (date) {
			return leadingNulls(date.tm_sec, 2);
		},
		"%t": function () {
			return "\t";
		},
		"%u": function (date) {
			return date.tm_wday || 7;
		},
		"%U": function (date) {
			var days = date.tm_yday + 7 - date.tm_wday;
			return leadingNulls(Math.floor(days / 7), 2);
		},
		"%V": function (date) {
			var val = Math.floor((date.tm_yday + 7 - ((date.tm_wday + 6) % 7)) / 7);
			if ((date.tm_wday + 371 - date.tm_yday - 2) % 7 <= 2) {
				val++;
			}
			if (!val) {
				val = 52;
				var dec31 = (date.tm_wday + 7 - date.tm_yday - 1) % 7;
				if (
					dec31 == 4 ||
					(dec31 == 5 && __isLeapYear((date.tm_year % 400) - 1))
				) {
					val++;
				}
			} else if (val == 53) {
				var jan1 = (date.tm_wday + 371 - date.tm_yday) % 7;
				if (jan1 != 4 && (jan1 != 3 || !__isLeapYear(date.tm_year))) val = 1;
			}
			return leadingNulls(val, 2);
		},
		"%w": function (date) {
			return date.tm_wday;
		},
		"%W": function (date) {
			var days = date.tm_yday + 7 - ((date.tm_wday + 6) % 7);
			return leadingNulls(Math.floor(days / 7), 2);
		},
		"%y": function (date) {
			return (date.tm_year + 1900).toString().substring(2);
		},
		"%Y": function (date) {
			return date.tm_year + 1900;
		},
		"%z": function (date) {
			var off = date.tm_gmtoff;
			var ahead = off >= 0;
			off = Math.abs(off) / 60;
			off = (off / 60) * 100 + (off % 60);
			return (ahead ? "+" : "-") + String("0000" + off).slice(-4);
		},
		"%Z": function (date) {
			return date.tm_zone;
		},
		"%%": function () {
			return "%";
		},
	};
	pattern = pattern.replace(/%%/g, "\0\0");
	for (var rule in EXPANSION_RULES_2) {
		if (pattern.includes(rule)) {
			pattern = pattern.replace(
				new RegExp(rule, "g"),
				EXPANSION_RULES_2[rule](date),
			);
		}
	}
	pattern = pattern.replace(/\0\0/g, "%");
	var bytes = intArrayFromString(pattern, false);
	if (bytes.length > maxsize) {
		return 0;
	}
	writeArrayToMemory(bytes, s);
	return bytes.length - 1;
}
function _system(command) {
	if (!command) return 0;
	setErrNo(52);
	return -1;
}
function allocateUTF8OnStack(str) {
	var size = lengthBytesUTF8(str) + 1;
	var ret = stackAlloc(size);
	stringToUTF8Array(str, HEAP8, ret, size);
	return ret;
}
function getCFunc(ident) {
	var func = Module["_" + ident];
	return func;
}
function ccall(ident, returnType, argTypes, args, opts) {
	var toC = {
		string: (str) => {
			var ret = 0;
			if (str !== null && str !== undefined && str !== 0) {
				var len = (str.length << 2) + 1;
				ret = stackAlloc(len);
				stringToUTF8(str, ret, len);
			}
			return ret;
		},
		array: (arr) => {
			var ret = stackAlloc(arr.length);
			writeArrayToMemory(arr, ret);
			return ret;
		},
	};
	function convertReturnValue(ret) {
		if (returnType === "string") {
			return UTF8ToString(ret);
		}
		if (returnType === "boolean") return Boolean(ret);
		return ret;
	}
	var func = getCFunc(ident);
	var cArgs = [];
	var stack = 0;
	if (args) {
		for (var i = 0; i < args.length; i++) {
			var converter = toC[argTypes[i]];
			if (converter) {
				if (stack === 0) stack = stackSave();
				cArgs[i] = converter(args[i]);
			} else {
				cArgs[i] = args[i];
			}
		}
	}
	var ret = func.apply(null, cArgs);
	function onDone(ret) {
		if (stack !== 0) stackRestore(stack);
		return convertReturnValue(ret);
	}
	ret = onDone(ret);
	return ret;
}
function cwrap(ident, returnType, argTypes, opts) {
	argTypes = argTypes || [];
	var numericArgs = argTypes.every(
		(type) => type === "number" || type === "boolean",
	);
	var numericRet = returnType !== "string";
	if (numericRet && numericArgs && !opts) {
		return getCFunc(ident);
	}
	return function () {
		return ccall(ident, returnType, argTypes, arguments, opts);
	};
}
var FSNode = function (parent, name, mode, rdev) {
	if (!parent) {
		parent = this;
	}
	this.parent = parent;
	this.mount = parent.mount;
	this.mounted = null;
	this.id = FS.nextInode++;
	this.name = name;
	this.mode = mode;
	this.node_ops = {};
	this.stream_ops = {};
	this.rdev = rdev;
};
var readMode = 292 | 73;
var writeMode = 146;
Object.defineProperties(FSNode.prototype, {
	read: {
		get: function () {
			return (this.mode & readMode) === readMode;
		},
		set: function (val) {
			val ? (this.mode |= readMode) : (this.mode &= ~readMode);
		},
	},
	write: {
		get: function () {
			return (this.mode & writeMode) === writeMode;
		},
		set: function (val) {
			val ? (this.mode |= writeMode) : (this.mode &= ~writeMode);
		},
	},
	isFolder: {
		get: function () {
			return FS.isDir(this.mode);
		},
	},
	isDevice: {
		get: function () {
			return FS.isChrdev(this.mode);
		},
	},
});
FS.FSNode = FSNode;
FS.staticInit();
Module["FS_createPath"] = FS.createPath;
Module["FS_createDataFile"] = FS.createDataFile;
Module["FS_createPreloadedFile"] = FS.createPreloadedFile;
Module["FS_unlink"] = FS.unlink;
Module["FS_createLazyFile"] = FS.createLazyFile;
Module["FS_createDevice"] = FS.createDevice;
var asmLibraryArg = {
	m: ___cxa_allocate_exception,
	z: ___cxa_begin_catch,
	C: ___cxa_end_catch,
	b: ___cxa_find_matching_catch_2,
	h: ___cxa_find_matching_catch_3,
	n: ___cxa_free_exception,
	va: ___cxa_rethrow,
	t: ___cxa_throw,
	e: ___resumeException,
	ka: ___syscall_chdir,
	T: ___syscall_fcntl64,
	ta: ___syscall_fstat64,
	ca: ___syscall_ftruncate64,
	qa: ___syscall_getcwd,
	ua: ___syscall_getdents64,
	za: ___syscall_ioctl,
	ra: ___syscall_lstat64,
	la: ___syscall_mkdirat,
	pa: ___syscall_newfstatat,
	U: ___syscall_openat,
	ma: ___syscall_rmdir,
	sa: ___syscall_stat64,
	R: ___syscall_unlinkat,
	D: __emscripten_date_now,
	Aa: __emscripten_get_now_is_monotonic,
	ja: __emscripten_throw_longjmp,
	Z: __gmtime_js,
	fa: __localtime_js,
	ha: __mktime_js,
	na: __mmap_js,
	oa: __munmap_js,
	ia: __tzset_js,
	x: _abort,
	Q: _emscripten_asm_const_int,
	W: _emscripten_get_now,
	ya: _emscripten_memcpy_big,
	N: _emscripten_resize_heap,
	wa: _environ_get,
	xa: _environ_sizes_get,
	X: _exit,
	F: _fd_close,
	S: _fd_read,
	da: _fd_seek,
	V: _fd_write,
	a: _getTempRet0,
	J: invoke_fii,
	y: invoke_i,
	ea: invoke_ifffi,
	c: invoke_ii,
	f: invoke_iii,
	k: invoke_iiii,
	j: invoke_iiiii,
	ga: invoke_iiiiifii,
	I: invoke_iiiiifiii,
	u: invoke_iiiiii,
	v: invoke_iiiiiii,
	s: invoke_iiiiiiii,
	L: invoke_iiiiiiiii,
	A: invoke_iiiiiiiiii,
	P: invoke_iiiiiiiiiii,
	$: invoke_iiji,
	aa: invoke_ji,
	_: invoke_jij,
	p: invoke_v,
	l: invoke_vi,
	H: invoke_vif,
	i: invoke_vii,
	g: invoke_viii,
	r: invoke_viiii,
	o: invoke_viiiii,
	B: invoke_viiiiii,
	q: invoke_viiiiiii,
	M: invoke_viiiiiiii,
	K: invoke_viiiiiiiii,
	O: invoke_viiiiiiiiii,
	ba: invoke_vij,
	Y: invoke_viji,
	G: _llvm_eh_typeid_for,
	d: _setTempRet0,
	w: _strftime,
	E: _system,
};
var asm = createWasm();
var ___wasm_call_ctors = (Module["___wasm_call_ctors"] = function () {
	return (___wasm_call_ctors = Module["___wasm_call_ctors"] =
		Module["asm"]["Ca"]).apply(null, arguments);
});
var _main = (Module["_main"] = function () {
	return (_main = Module["_main"] = Module["asm"]["Da"]).apply(null, arguments);
});
var _wasmInitInfixServer = (Module["_wasmInitInfixServer"] = function () {
	return (_wasmInitInfixServer = Module["_wasmInitInfixServer"] =
		Module["asm"]["Ea"]).apply(null, arguments);
});
var _wasmDestroyInfixServer = (Module["_wasmDestroyInfixServer"] = function () {
	return (_wasmDestroyInfixServer = Module["_wasmDestroyInfixServer"] =
		Module["asm"]["Fa"]).apply(null, arguments);
});
var _wasmRunXML = (Module["_wasmRunXML"] = function () {
	return (_wasmRunXML = Module["_wasmRunXML"] = Module["asm"]["Ga"]).apply(
		null,
		arguments,
	);
});
var ___errno_location = (Module["___errno_location"] = function () {
	return (___errno_location = Module["___errno_location"] =
		Module["asm"]["Ha"]).apply(null, arguments);
});
var _malloc = (Module["_malloc"] = function () {
	return (_malloc = Module["_malloc"] = Module["asm"]["Ia"]).apply(
		null,
		arguments,
	);
});
var _free = (Module["_free"] = function () {
	return (_free = Module["_free"] = Module["asm"]["Ja"]).apply(null, arguments);
});
var _emscripten_builtin_memalign = (Module["_emscripten_builtin_memalign"] =
	function () {
		return (_emscripten_builtin_memalign = Module[
			"_emscripten_builtin_memalign"
		] =
			Module["asm"]["Ka"]).apply(null, arguments);
	});
var _setThrew = (Module["_setThrew"] = function () {
	return (_setThrew = Module["_setThrew"] = Module["asm"]["La"]).apply(
		null,
		arguments,
	);
});
var stackSave = (Module["stackSave"] = function () {
	return (stackSave = Module["stackSave"] = Module["asm"]["Ma"]).apply(
		null,
		arguments,
	);
});
var stackRestore = (Module["stackRestore"] = function () {
	return (stackRestore = Module["stackRestore"] = Module["asm"]["Na"]).apply(
		null,
		arguments,
	);
});
var stackAlloc = (Module["stackAlloc"] = function () {
	return (stackAlloc = Module["stackAlloc"] = Module["asm"]["Oa"]).apply(
		null,
		arguments,
	);
});
var ___cxa_can_catch = (Module["___cxa_can_catch"] = function () {
	return (___cxa_can_catch = Module["___cxa_can_catch"] =
		Module["asm"]["Pa"]).apply(null, arguments);
});
var ___cxa_is_pointer_type = (Module["___cxa_is_pointer_type"] = function () {
	return (___cxa_is_pointer_type = Module["___cxa_is_pointer_type"] =
		Module["asm"]["Qa"]).apply(null, arguments);
});
var dynCall_vij = (Module["dynCall_vij"] = function () {
	return (dynCall_vij = Module["dynCall_vij"] = Module["asm"]["Sa"]).apply(
		null,
		arguments,
	);
});
var dynCall_ji = (Module["dynCall_ji"] = function () {
	return (dynCall_ji = Module["dynCall_ji"] = Module["asm"]["Ta"]).apply(
		null,
		arguments,
	);
});
var dynCall_viji = (Module["dynCall_viji"] = function () {
	return (dynCall_viji = Module["dynCall_viji"] = Module["asm"]["Ua"]).apply(
		null,
		arguments,
	);
});
var dynCall_iiji = (Module["dynCall_iiji"] = function () {
	return (dynCall_iiji = Module["dynCall_iiji"] = Module["asm"]["Va"]).apply(
		null,
		arguments,
	);
});
var dynCall_jij = (Module["dynCall_jij"] = function () {
	return (dynCall_jij = Module["dynCall_jij"] = Module["asm"]["Wa"]).apply(
		null,
		arguments,
	);
});
function invoke_v(index) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)();
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_ii(index, a1) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iii(index, a1, a2) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viii(index, a1, a2, a3) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiii(index, a1, a2, a3) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_vi(index, a1) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_vii(index, a1, a2) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiii(index, a1, a2, a3, a4) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_i(index) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)();
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiiii(index, a1, a2, a3, a4, a5, a6) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiii(index, a1, a2, a3, a4) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiii(index, a1, a2, a3, a4, a5) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiiiii(index, a1, a2, a3, a4, a5, a6, a7) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiiii(index, a1, a2, a3, a4, a5, a6) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_fii(index, a1, a2) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiifiii(index, a1, a2, a3, a4, a5, a6, a7, a8) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiiiifii(index, a1, a2, a3, a4, a5, a6, a7) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_vif(index, a1, a2) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_ifffi(index, a1, a2, a3, a4) {
	var sp = stackSave();
	try {
		return getWasmTableEntry(index)(a1, a2, a3, a4);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viiiiiiiiii(index, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) {
	var sp = stackSave();
	try {
		getWasmTableEntry(index)(a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_vij(index, a1, a2, a3) {
	var sp = stackSave();
	try {
		dynCall_vij(index, a1, a2, a3);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_ji(index, a1) {
	var sp = stackSave();
	try {
		return dynCall_ji(index, a1);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_iiji(index, a1, a2, a3, a4) {
	var sp = stackSave();
	try {
		return dynCall_iiji(index, a1, a2, a3, a4);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_jij(index, a1, a2, a3) {
	var sp = stackSave();
	try {
		return dynCall_jij(index, a1, a2, a3);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
function invoke_viji(index, a1, a2, a3, a4) {
	var sp = stackSave();
	try {
		dynCall_viji(index, a1, a2, a3, a4);
	} catch (e) {
		stackRestore(sp);
		if (e !== e + 0) throw e;
		_setThrew(1, 0);
	}
}
Module["UTF8ToString"] = UTF8ToString;
Module["addRunDependency"] = addRunDependency;
Module["removeRunDependency"] = removeRunDependency;
Module["FS_createPath"] = FS.createPath;
Module["FS_createDataFile"] = FS.createDataFile;
Module["FS_createPreloadedFile"] = FS.createPreloadedFile;
Module["FS_createLazyFile"] = FS.createLazyFile;
Module["FS_createDevice"] = FS.createDevice;
Module["FS_unlink"] = FS.unlink;
Module["callMain"] = callMain;
Module["ccall"] = ccall;
Module["cwrap"] = cwrap;
var calledRun;
dependenciesFulfilled = function runCaller() {
	if (!calledRun) run();
	if (!calledRun) dependenciesFulfilled = runCaller;
};
function callMain(args) {
	var entryFunction = Module["_main"];
	args = args || [];
	args.unshift(thisProgram);
	var argc = args.length;
	var argv = stackAlloc((argc + 1) * 4);
	var argv_ptr = argv >> 2;
	args.forEach((arg) => {
		HEAP32[argv_ptr++] = allocateUTF8OnStack(arg);
	});
	HEAP32[argv_ptr] = 0;
	try {
		var ret = entryFunction(argc, argv);
		exitJS(ret, true);
		return ret;
	} catch (e) {
		return handleException(e);
	}
}
function run(args) {
	args = args || arguments_;
	if (runDependencies > 0) {
		return;
	}
	preRun();
	if (runDependencies > 0) {
		return;
	}
	function doRun() {
		if (calledRun) return;
		calledRun = true;
		Module["calledRun"] = true;
		if (ABORT) return;
		initRuntime();
		preMain();
		if (Module["onRuntimeInitialized"]) Module["onRuntimeInitialized"]();
		if (shouldRunNow) callMain(args);
		postRun();
	}
	if (Module["setStatus"]) {
		Module["setStatus"]("Running...");
		setTimeout(function () {
			setTimeout(function () {
				Module["setStatus"]("");
			}, 1);
			doRun();
		}, 1);
	} else {
		doRun();
	}
}
if (Module["preInit"]) {
	if (typeof Module["preInit"] == "function")
		Module["preInit"] = [Module["preInit"]];
	while (Module["preInit"].length > 0) {
		Module["preInit"].pop()();
	}
}
var shouldRunNow = true;
if (Module["noInitialRun"]) shouldRunNow = false;
run();
