{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/setCellFontStyle.js", "webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellDecoratorButton.js"], "names": ["setCellFontStyle", "font", "core", "getDocumentViewer", "getSpreadsheetEditorManager", "setSelectedCellRangeStyle", "propTypes", "styleType", "PropTypes", "oneOf", "Object", "values", "STYLE_TOGGLE_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "CellDecoratorButton", "forwardRef", "props", "ref", "isActive", "useSelector", "state", "selectors", "getActiveCellRangeFontStyle", "menuItems", "dataElement", "icon", "title", "handleClick", "FlyoutItemContainer", "onClick", "additionalClass", "ActionButton", "aria<PERSON>urrent", "img", "displayName"], "mappings": "6QAaeA,MAXf,SAA0BC,GACnBA,GAI4BC,IAAKC,oBAAoBC,8BACjCC,0BAA0B,CACjDJ,U,u4BCCJ,IAAMK,EAAY,CAChBC,UAAWC,IAAUC,MAAMC,OAAOC,OAAOC,MAAuBC,WAChEC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAsBC,sBAAW,SAACC,EAAOC,GAC7C,IAAQT,EAA8CQ,EAA9CR,aAAcP,EAAgCe,EAAhCf,UAAWS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAElCM,EADmBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,4BAA4BF,EAAOnB,MAE7F,EAAqCsB,IAAU,GAAD,OAAItB,EAAS,WAAnDuB,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAClBjC,EAAiB,EAAD,GAAIO,GAAaiB,KAGnC,OACEV,EACE,kBAACoB,EAAA,EAAmB,KACdZ,EAAK,CACTC,IAAKA,EACLY,QAASF,EACTG,gBAAiBZ,EAAW,SAAW,MAGvC,kBAACa,EAAA,EAAY,CACXC,YAAad,EACbA,SAAUA,EACVM,YAAaA,EACbE,MAAOA,EACPO,IAAKR,EACLI,QAASF,EACTjB,MAAOA,EACPE,UAAWA,OAMrBE,EAAoBd,UAAYA,EAChCc,EAAoBoB,YAAc,sBAEnBpB", "file": "chunks/chunk.92.js", "sourcesContent": ["import core from 'core';\n\nfunction setCellFontStyle(font) {\n  if (!font) {\n    return;\n  }\n\n  const spreadsheetEditorManager = core.getDocumentViewer().getSpreadsheetEditorManager();\n  spreadsheetEditorManager.setSelectedCellRangeStyle({\n    font,\n  });\n}\n\nexport default setCellFontStyle;", "import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport { STYLE_TOGGLE_OPTIONS } from 'constants/customizationVariables';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport setCellFontStyle from 'src/helpers/setCellFontStyle';\n\nconst propTypes = {\n  styleType: PropTypes.oneOf(Object.values(STYLE_TOGGLE_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CellDecoratorButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, styleType, style, className } = props;\n  const currentFontStyle = useSelector((state) => selectors.getActiveCellRangeFontStyle(state, styleType));\n  const isActive = currentFontStyle;\n  const { dataElement, icon, title } = menuItems[`${styleType}Button`];\n\n  const handleClick = () => {\n    setCellFontStyle({ [styleType]: !isActive });\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          ariaCurrent={isActive}\n          isActive={isActive}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          onClick={handleClick}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellDecoratorButton.propTypes = propTypes;\nCellDecoratorButton.displayName = 'CellDecoratorButton';\n\nexport default CellDecoratorButton;"], "sourceRoot": ""}