(window.webpackJsonp = window.webpackJsonp || []).push([
	[96],
	{
		2009: function (e, t, n) {
			"use strict";
			n.r(t);
			n(58), n(44);
			var a = n(0),
				r = n.n(a),
				i = n(571),
				l = n(4),
				o = n(3),
				s = n.n(o);
			function c() {
				return (c = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var a in n)
									Object.prototype.hasOwnProperty.call(n, a) && (e[a] = n[a]);
							}
							return e;
						}).apply(this, arguments);
			}
			var p = {
					isFlyoutItem: s.a.bool,
					alignment: s.a.string,
					style: s.a.object,
					className: s.a.string,
					alignmentValue: s.a.string,
				},
				u = function (e, t) {
					return r.a.createElement(
						i.a,
						c({}, e, {
							ref: t,
							selector: l.a.getActiveCellRangeVerticalAlignment,
						}),
					);
				};
			(t.default = r.a.forwardRef(u)),
				(u.displayName = "VerticalAlignmentButton"),
				(u.propTypes = p);
		},
	},
]);
//# sourceMappingURL=chunk.96.js.map
