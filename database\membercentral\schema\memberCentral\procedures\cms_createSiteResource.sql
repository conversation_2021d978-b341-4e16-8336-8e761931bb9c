ALTER PROC dbo.cms_createSiteResource
@resourceTypeID int,
@siteResourceStatusID int,
@siteID int,
@isVisible bit,
@parentSiteResourceID int,
@siteResourceID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @siteResourceID = null;

	DECLARE @tblUniversalRoles TABLE (roleID INT, functionID INT, PRIMARY KEY(roleID,functionID));
	DECLARE @srrCount int, @runBulk bit = 0;

	-- get the universal roles including this resourceType
	INSERT INTO @tblUniversalRoles (roleID, functionID)
	SELECT srroles.roleID, srtf.functionID
	FROM dbo.cms_siteResourceTypeFunctions as srtf
	INNER JOIN dbo.cms_siteResourceRoleFunctions as srrf on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
	INNER JOIN dbo.cms_siteResourceRoles as srroles on srroles.roleTypeID = 2 AND srroles.roleID = srrf.roleID
	WHERE srtf.resourceTypeID = @resourceTypeID
	GROUP BY srroles.roleID, srtf.functionID;

	SELECT @srrCount = count(srr.roleID)
	FROM @tblUniversalRoles as srroles
	INNER JOIN dbo.cms_siteResourceRights as srr on srr.siteID = @siteID 
		AND srr.roleID = srroles.roleID;
	
	BEGIN TRAN;
		INSERT INTO dbo.cms_siteResources (resourceTypeID, siteResourceStatusID, siteID, isVisible, parentSiteResourceID)
		VALUES (@resourceTypeID, @siteResourceStatusID, @siteID, @isVisible, @parentSiteResourceID);
			SET @siteResourceID = SCOPE_IDENTITY();

		IF @srrCount > 0 BEGIN
			INSERT INTO dbo.cms_siteResourceRightsCache (siteID, resourceID, functionID, groupID, [include], 
				universalRoleResourceRightsID, universalRoleResourceTypeID)
			SELECT @siteID, srr.resourceID, srroles.functionID, srr.groupID, srr.[include], srr.resourceRightsID, @resourceTypeID
			FROM @tblUniversalRoles as srroles
			INNER JOIN dbo.cms_siteResourceRights as srr on srr.siteID = @siteID 
				AND srr.roleID = srroles.roleID
			LEFT OUTER JOIN dbo.cms_siteResourceRightsCache as srrc on srrc.siteID = @siteID
				AND srrc.universalRoleResourceRightsID = srr.resourceRightsID
				AND srrc.universalRoleResourceTypeID = @resourceTypeID
				AND srrc.functionID = srroles.functionID
			WHERE srrc.cachedRightsID is null;

			SET @runBulk = 1;
		END
	COMMIT TRAN;

	IF @runBulk = 1 BEGIN
		IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
			DROP TABLE #siteResourcesToProcess;
		CREATE TABLE #siteResourcesToProcess (siteResourceID int PRIMARY KEY, resourceTypeID int);

		insert into #siteResourcesToProcess (siteResourceID,resourceTypeID) values (@siteResourceID,@resourceTypeID);

		exec dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk @siteID=@siteID;

		IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
			DROP TABLE #siteResourcesToProcess;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
