{"version": 3, "sources": ["webpack:///./src/ui/src/components/StylePicker/ColorPicker/index.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss?fe54", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/index.js", "webpack:///./src/ui/src/components/StylePicker/StylePicker.scss?c539", "webpack:///./src/ui/src/components/StylePicker/StylePicker.scss", "webpack:///./src/ui/src/components/StylePanel/StylePanel.scss?64d9", "webpack:///./src/ui/src/components/StylePanel/StylePanel.scss", "webpack:///./src/ui/src/components/StylePicker/SnapModeToggle/index.js", "webpack:///./src/ui/src/components/StylePicker/SnapModeToggle/SnapModeToggle.js", "webpack:///./src/ui/src/helpers/stylePanelHelper.js", "webpack:///./src/ui/src/components/StylePicker/StrokePanelSection/StrokePanelSection.js", "webpack:///./src/ui/src/components/StylePicker/OpacityPanelSection/OpacityPanelSection.js", "webpack:///./src/ui/src/components/StylePicker/OpacityPanelSection/index.js", "webpack:///./src/ui/src/hooks/useStylePanel.js", "webpack:///./src/ui/src/components/StylePanel/panels/NoSharedStylePanel.js", "webpack:///./src/ui/src/components/StylePicker/StylePicker.js", "webpack:///./src/ui/src/components/StylePicker/index.js", "webpack:///./src/ui/src/components/StylePanel/panels/TextStylePanel.js", "webpack:///./src/ui/src/components/StylePanel/panels/DefaultStylePanel.js", "webpack:///./src/ui/src/components/StylePanel/StylePanelFactory.js", "webpack:///./src/ui/src/components/StylePanel/panels/NoToolStylePanel.js", "webpack:///./src/ui/src/components/StylePanel/StylePanelContainer.js", "webpack:///./src/ui/src/components/StylePanel/index.js"], "names": ["ColorPicker", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "annotation", "PropTypes", "object", "editor", "style", "shape", "TextColor", "oneOfType", "string", "RichTextStyle", "any", "isFreeTextAutoSize", "bool", "onFreeTextSizeToggle", "func", "onPropertyChange", "onRichTextStyleChange", "isRedaction", "isRichTextEditMode", "setIsRichTextEditMode", "isWidget", "RichTextStyleEditor", "activeTool", "fonts", "useSelector", "state", "selectors", "getFonts", "shallowEqual", "useState", "format", "setFormat", "editor<PERSON><PERSON>", "useRef", "annotationRef", "propertiesRef", "dispatch", "useDispatch", "oldSelectionRef", "richTextEditModeRef", "current", "t", "useTranslation", "useEffect", "handleSelectionChange", "range", "oldRange", "setSelection", "index", "getFormat", "handleTextChange", "getSelection", "core", "addEventListener", "actions", "disableElements", "DataElements", "ANNOTATION_STYLE_POPUP", "removeEventListener", "enableElements", "StrokeStyle", "err", "console", "error", "stylesTemp", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "includes", "strikeout", "size", "font", "calculatedFontSize", "getCalculatedFontSize", "handleEditorBlur", "handleEditorFocus", "properties", "color", "Core", "Annotations", "Color", "Array", "isArray", "lastSelectedColor", "prop", "undefined", "applyFormat", "formatKey", "value", "handlePropertyChange", "property", "blur", "adjustFreeTextBoundingBox", "setTimeout", "getAnnotationManager", "getEditBoxManager", "focusBox", "defaults", "strike", "quillFont", "quillFontSize", "originalSize", "commonProps", "stateless", "isFreeText", "nonWidgetProps", "propertyTranslation", "freeText", "isAutoSized", "resizeAnnotation", "newSelection", "currentFormat", "handleTextFormatChange", "widgetProps", "className", "onMouseDown", "e", "type", "preventDefault", "TextStylePicker", "onColorChange", "name", "toHexString", "handleColorChange", "ariaTypeLabel", "React", "memo", "SnapModeToggle", "Scale", "Precision", "isSnapModeEnabled", "wasDocumentSwappedToClientSide", "getDocument", "getType", "workerTypes", "WEBVIEWER_SERVER", "isWebViewerServerDocument", "isEligibleDocumentForSnapping", "toLowerCase", "PDF", "showMeasurementSnappingOption", "isFullPDFEnabled", "Choice", "dataElement", "id", "label", "i18next", "checked", "onChange", "event", "enableSnapping", "target", "mode", "getDocumentViewer", "SnapMode", "e_DefaultSnapMode", "POINT_ON_LINE", "getMeasurementTools", "tool", "setSnapMode", "setEnableSnapMode", "toolName", "isEnabled", "Tools", "ToolNames", "shouldHideStrokeDropdowns", "CountMeasurementCreateTool", "FreeHandCreateTool", "FreeHandHighlightCreateTool", "ArcCreateTool", "ArcMeasurementCreateTool", "TextAnnotationCreateTool", "some", "getTool", "hasFillColorAndCollapsablePanelSections", "RectangleCreateTool", "EllipseCreateTool", "PolygonCreateTool", "PolygonCloudCreateTool", "EllipseMeasurementCreateTool", "AreaMeasurementCreateTool", "FreeTextCreateTool", "CalloutCreateTool", "RedactionCreateTool", "TextFormFieldCreateTool", "RadioButtonFormFieldCreateTool", "CheckBoxFormFieldCreateTool", "ListBoxFormFieldCreateTool", "ComboBoxFormFieldCreateTool", "shouldHideFillColorAndCollapsablePanelSections", "RubberStampCreateTool", "StampCreateTool", "EraserTool", "shouldHideStrokeSlider", "TextUnderlineCreateTool", "TextHighlightCreateTool", "TextSquigglyCreateTool", "TextStrikeoutCreateTool", "FileAttachmentCreateTool", "StickyCreateTool", "MarkInsertTextCreateTool", "MarkReplaceTextCreateTool", "shouldHideStrokeStyle", "shouldHideCloudyLineStyle", "LineCreateTool", "shouldHideOpacitySlider", "SignatureFormFieldCreateTool", "shouldHideTransparentFillColor", "stylePanelSectionTitles", "section", "toolTitles", "getAnnotationTypes", "selectedAnnotations", "from", "Set", "map", "ToolName", "parseToolType", "currentTool", "annotationTypes", "REDACTION", "isStamp", "STAMP", "WidgetAnnotation", "TEXT_FORM_FIELD", "LIST_BOX_FIELD", "COMBO_BOX_FIELD", "shouldRenderWidgetLayout", "isInFormFieldCreationMode", "getFormFieldCreationManager", "FREE_TEXT", "useStylePanelSections", "isStyleOptionDisabled", "isElementDisabled", "STYLE_OPTION", "isStrokeStyleContainerActive", "isElementOpen", "STROKE_STYLE_CONTAINER", "isFillColorContainerActive", "FILL_COLOR_CONTAINER", "isOpacityContainerActive", "OPACITY_CONTAINER", "isTextStyleContainerActive", "RICH_TEXT_STYLE_CONTAINER", "panelItems", "togglePanelItem", "closeElement", "openElement", "openTextStyleContainer", "openElements", "RICH_TEXT_EDITOR", "openStrokeStyleContainer", "openFillColorContainer", "openOpacityContainer", "withCloudyStyle", "defaultStrokeStyles", "concat", "cloudyStrokeStyle", "StrokePanelSection", "showFillColorAndCollapsablePanelSections", "onStrokeColorChange", "onStyleChange", "strokeColor", "hideStrokeDropdowns", "hideStrokeSlider", "strokethicknessComponent", "showLineStyleOptions", "renderSlider", "strokeStyle", "onStartLineStyleChange", "startingLineStyle", "onStrokeStyleChange", "strokeLineStyle", "onEndLineStyleChange", "endingLineStyle", "hideCloudyLineStyle", "middleLineSegmentLabel", "sectionContent", "Dropdown", "translationPrefix", "images", "defaultStartLineStyles", "onClickItem", "currentSelectionKey", "showLabelInList", "defaultEndLineStyles", "CollapsibleSection", "header", "headingLevel", "isInitiallyExpanded", "onToggle", "shouldShowHeading", "isExpanded", "node", "OpacityPanelSection", "useStylePanel", "StrokeColor", "StrokeThickness", "Opacity", "FillColor", "setStyle", "panelTitle", "setPanelTitle", "setStrokeStyle", "startLineStyle", "setStartLineStyle", "endLineStyle", "setEndLineStyle", "isAutoSizeFont", "setIsAutoSizeFont", "editorInstance", "setEditorInstance", "currentToolName", "defaultTool", "getDataWithKey", "mapToolNameToKey", "hasLineEndings", "setShowLineStyleOptions", "colorProperties", "getToolButtonObjects", "isAnnotationToolStyleSyncingEnabled", "getActiveDocumentViewerKey", "toolButtonObject", "activeDocumentViewerKey", "selectedAnnotation", "updateSnapModeFromTool", "getSnapMode", "applyFreeTextStyles", "extraStyles", "jsonData", "inputText", "uniqueFontFamilies", "uniqueFontSizes", "key", "hasOwnProperty", "parseInt", "isNaN", "add", "trim", "sizes", "extractUniqueFontFamilies", "getContents", "updateFromTool", "styles", "getToolStyles", "StartLineStyle", "EndLineStyle", "title", "setPanelTitleForSelectedTool", "annotations", "isContentEditPlaceholder", "FreeTextAnnotation", "RedactionAnnotation", "OverlayText", "getStartStyle", "getEndStyle", "dashes", "getStrokeStyle", "updateStylePanelProps", "every", "newStyle", "hasFocus", "background", "colorRGB", "hexToRGBA", "r", "g", "b", "a", "setAnnotationStyles", "setToolStyles", "redrawAnnotation", "refresh", "getToolMode", "showPreview", "onLineStyleChange", "sectionPropertyMap", "start", "middle", "end", "setStartStyle", "split", "Style", "shift", "Dash<PERSON>", "setEndStyle", "trigger", "handleAutoSize", "handleFreeTextAutoSizeToggle", "handleRichTextStyleChange", "richStyle", "newValue", "getTextDecoration", "richTextStyle", "0", "updateAnnotationRichTextStyle", "saveEditorInstance", "NoSharedStylePanel", "Icon", "glyph", "arrayOf", "activeType", "hasParentPicker", "isTextStylePickerHidden", "isRequired", "redactionLabelProperties", "sliderProperties", "StylePicker", "checkFunction", "setStrokeColor", "setStartingLineStyle", "setEndingLineStyle", "setStrokeLineStyle", "fillColor", "setFillColor", "checkAnyAnnotationTypes", "hideOpacitySlider", "hideStrokeStyle", "hideFillColorAndCollapsablePanelSections", "showSnapModeCheckbox", "DistanceMeasurementCreateTool", "PerimeterMeasurementCreateTool", "RectangularAreaMeasurementCreateTool", "CloudyRectangularAreaMeasurementCreateTool", "onSliderChange", "shouldHideSliderTitle", "sliderProps", "displayProperty", "getDisplayValue", "Math", "round", "OPACITY_SLIDER", "withInputField", "inputFieldType", "min", "max", "step", "getLocalValue", "opacity", "getStrokeDisplayValue", "STROKE_THICKNESS_SLIDER", "steps", "getStrokeSliderSteps", "FONT_SIZE_SLIDER", "parseFloat", "toFixed", "getSliderProps", "Slide<PERSON>", "customCircleRadius", "customLineStrokeWidth", "renderDivider", "parentProps", "toUpperCase", "hasTransparentColor", "ParentComponent", "children", "TextStylePanel", "ANNOTATION_POPUP", "toolTypeProps", "onOpenProps", "useOnFreeTextEdit", "LabelTextEditor", "placeholderText", "colorMapKey", "DefaultStylePanel", "AddParagraphTool", "AddImageContentTool", "CropCreateTool", "SnippingCreateTool", "shouldHideStylePanelOptions", "getPanelFromNames", "toolNames", "shouldHideSharedStyleOptions", "shouldShowTextStyle", "NoToolStylePanel", "StylePanelContainer", "isPanelOpen", "annotationCreateToolNames", "getAnnotationCreateToolNames", "getSelectedAnnotations", "setSelectedAnnotations", "setCurrentTool", "filteredTypes", "PushButtonWidgetAnnotation", "handleChange", "debounce", "annotationManager", "allSelectedAnnotations", "isGrouped", "getGroupAnnotations", "grouped", "getGroupedChildren", "child", "leading", "trailing", "StylePanelComponent", "types", "isInstanceOfAny", "shouldShowNoStyles", "getStylePanelComponent", "DataElementWrapper"], "mappings": "+FAAA,aAEeA,MAAW,G,qBCF1B,IAAIC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,sxEAAuxE,KAGhzE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,s0FCGvB,IAAMC,EAAY,CAChBC,WAAYC,IAAUC,OACtBC,OAAQF,IAAUC,OAClBE,MAAOH,IAAUI,MAAM,CACrBC,UAAWL,IAAUM,UAAU,CAC7BN,IAAUO,OACVP,IAAUC,SAEZO,cAAeR,IAAUS,MAE3BC,mBAAoBV,IAAUW,KAC9BC,qBAAsBZ,IAAUa,KAChCC,iBAAkBd,IAAUa,KAC5BE,sBAAuBf,IAAUa,KACjCG,YAAahB,IAAUW,KACvBM,mBAAoBjB,IAAUW,KAC9BO,sBAAuBlB,IAAUa,KACjCM,SAAUnB,IAAUW,MAGhBS,EAAsB,SAAH,GAYnB,8BAXJrB,EAAU,EAAVA,WAAYG,EAAM,EAANA,OACZC,EAAK,EAALA,MACAO,EAAkB,EAAlBA,mBACAE,EAAoB,EAApBA,qBACAE,EAAgB,EAAhBA,iBACAC,EAAqB,EAArBA,sBACAE,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAF,EAAW,EAAXA,YACAG,EAAQ,EAARA,SACAE,EAAU,EAAVA,WAGEC,EAMD,EALGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,SAASF,MAErBG,KACD,GANM,GAQiC,IAAZC,mBAAS,IAAG,GAAjCC,EAAM,KAAEC,EAAS,KAClBC,EAAYC,iBAAO,MACnBC,EAAgBD,iBAAO,MACvBE,EAAgBF,iBAAO,IACvBG,EAAWC,cACXC,EAAkBL,mBAClBM,EAAsBN,mBAC5BM,EAAoBC,QAAUtB,EAC9B,IAAOuB,EAAqB,EAAhBC,cAAgB,GAApB,GAERC,qBAAU,WACR,IAAMC,EAAwB,SAACC,EAAOC,IACAD,GAASC,GAAYd,EAAUQ,SAEjER,EAAUQ,QAAQO,aAAaD,EAASE,MAAOF,EAASjE,QAEtDgE,GAASb,EAAUQ,SACrBT,EAAUkB,EAAUJ,KAGlBK,EAAmB,WAAM,MAC7BnB,EAAUkB,EAA2B,QAAlB,EAACjB,EAAUQ,eAAO,aAAjB,EAAmBW,kBAMzC,OAJAC,IAAKC,iBAAiB,yBAA0BT,GAChDQ,IAAKC,iBAAiB,oBAAqBH,GAE3Cd,EAASkB,IAAQC,gBAAgB,CAACC,IAAaC,0BACxC,WACLL,IAAKM,oBAAoB,yBAA0Bd,GACnDQ,IAAKM,oBAAoB,oBAAqBR,GAC9Cd,EAASkB,IAAQK,eAAe,CAACH,IAAaC,6BAE/C,IAEHd,qBAAU,WAAM,MAGd,GAFAX,EAAUQ,QAAUrC,EACpB+B,EAAcM,QAAUxC,EACpBkB,GAAsBlB,EAAY,iBAChC4D,EAAc,QAClB,IACEA,EAAuC,SAAxB5D,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAO6D,GACPC,QAAQC,MAAMF,GAEhB,IACMG,EADiBhE,EAAWiE,mBACA,GAElC9B,EAAcK,QAAU,CACtB0B,KAAMlE,EAAWkE,KACjBC,SAAUnE,EAAWmE,SACrBC,UAAWpE,EAAWoE,UACtBC,kBAAmBrE,EAAWqE,kBAC9BC,KAA4C,QAAxC,EAAkC,UAAhCN,aAAU,EAAVA,EAAa,uBAAyB,SAC5CO,OAA+C,QAAzC,EAAiC,YAA/BP,aAAU,EAAVA,EAAa,sBAA0B,SAC/CQ,WAAWR,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,gBAChDT,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,SAC/CC,UAAoE,QAA3D,EAAEV,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,uBAAe,SACpEE,KAAMX,aAAU,EAAVA,EAAa,aACnBY,KAAMZ,aAAU,EAAVA,EAAa,eACnBJ,cACAiB,mBAAoB7E,EAAW8E,yBAInC/C,EAAUkB,EAA2B,QAAlB,EAACjB,EAAUQ,eAAO,aAAjB,EAAmBW,iBAEnCb,EAAgBE,UAClBR,EAAUQ,QAAQO,aAAaT,EAAgBE,SAC/CF,EAAgBE,QAAU,QAE3B,CAACxC,EAAYG,EAAQe,IAExByB,qBAAU,WACR,IAAMoC,EAAmB,WACvB/C,EAAUQ,QAAU,KACpBN,EAAcM,QAAU,KACxBrB,GAAsB,IAElB6D,EAAoB,WACxB7D,GAAsB,IAKxB,OAFAiC,IAAKC,iBAAiB,aAAc0B,GACpC3B,IAAKC,iBAAiB,cAAe2B,GAC9B,WACL5B,IAAKM,oBAAoB,aAAcqB,GACvC3B,IAAKM,oBAAoB,cAAesB,MAEzC,CAAC5C,IAGJ,IAqHI6C,EArHEhC,EAAY,SAACJ,GACjB,IAAKA,EACH,MAAO,GAGT,IAAMf,EAASE,EAAUQ,QAAQS,UAAUJ,EAAMG,MAAOH,EAAMhE,QAE9D,GAA4B,iBAAjBiD,EAAOoD,MAChBpD,EAAOoD,MAAQ,IAAI5G,OAAO6G,KAAKC,YAAYC,MAAMvD,EAAOoD,YACnD,GAAII,MAAMC,QAAQzD,EAAOoD,OAAQ,CAEtC,IAAMM,EAAoB,IAAIlH,OAAO6G,KAAKC,YAAYC,MAAMvD,EAAOoD,MAAMpD,EAAOoD,MAAMrG,OAAS,IAC/FiD,EAAOoD,MAAQM,OACL1D,EAAOoD,QACjBpD,EAAOoD,MAAQhD,EAAcM,QAAQlC,WAKvC,IAFA,IAEA,MAF0B,CAAC,OAAQ,OAAQ,gBAEP,eAAE,CAAjC,IAAMmF,EAAI,KACT3D,EAAO2D,IAASH,MAAMC,QAAQzD,EAAO2D,MACvC3D,EAAO2D,QAAQC,GAInB,OAAO5D,GAwBH6D,GAAc,SAACC,EAAWC,GACJ,MAEnB,EAFW,SAAdD,EACe,QAAjB,EAAA5D,EAAUQ,eAAO,OAAjB,EAAmBV,OAAO,sBAAuB+D,GAEhC,QAAjB,EAAA7D,EAAUQ,eAAO,OAAjB,EAAmBV,OAAO8D,EAAWC,GAGrB,UAAdD,IACFC,EAAQ,IAAIvH,OAAO6G,KAAKC,YAAYC,MAAMQ,IAI5C9D,EAAU,EAAD,KACJD,GAAM,QACR8D,EAAYC,MAKXC,GAAuB,SAACC,EAAUF,GACtC,GAAKtD,EAAoBC,QAAzB,CAKA,MAA0BR,EAAUQ,QAAQW,eAApCH,EAAK,EAALA,MAAOnE,EAAM,EAANA,OACTmB,EAAakC,EAAcM,QACjCxC,EAAW+F,GAAYF,EACvB7D,EAAUQ,QAAQwD,OACD,aAAbD,GAAwC,SAAbA,GAC7BE,YAA0BjG,GAG5BkG,YAAW,WACT5D,EAAgBE,QAAU,CAAEQ,QAAOnE,UACZuE,IAAK+C,uBAAuBC,oBACpCC,SAASrG,KACvB,QAhBDe,EAAiBgF,EAAUF,IAiDvBpF,GAAkBL,EAAlBK,cACF6F,GAAW,CACfhC,KAAoD,QAAhD,EAA0C,UAAxC7D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,uBAAyB,SACpD8D,OAAuD,QAAjD,EAAyC,YAAvC9D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,sBAA0B,SACvD+D,WAAW/D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,gBAAgBhE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,SAC/HC,UAA4E,QAAnE,EAAEjE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,uBAAe,SAC5EG,KAAMnE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,eAC3BkE,KAAMlE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,aAC3BmD,YAAa,SAGfqB,EAAa,EAAH,KACL7E,GACAkG,IAGDpF,GAAsBlB,IACxBmC,EAAcK,QAAQ8B,KAAOxC,EAAOwC,KACpCnC,EAAcK,QAAQ+B,OAASzC,EAAOyC,OACtCpC,EAAcK,QAAQgC,UAAY1C,EAAO0C,UACzCrC,EAAcK,QAAQkC,UAAY5C,EAAOyE,OACzCpE,EAAcK,QAAQgE,UAAY1E,EAAO8C,MAAQzC,EAAcK,QAAQ0B,KACvE/B,EAAcK,QAAQiE,cAAgB3E,EAAO4E,cAAgBvE,EAAcK,QAAQ2B,UAGrF,IAAMwC,GAAc,CAClBpF,MAAOA,EACPR,iBAAkB+E,GAClBb,WAAYA,EACZ2B,WAAW,EACXC,YAAa5F,GAGT6F,GAAiB,CACrB9F,sBA9DgC,SAAC+E,EAAUF,GAC3C,GAAKtD,EAAoBC,QAAzB,CAKA,IAAMuE,EAAsB,CAC1B,cAAe,OACf,aAAc,SACd,UAAa,YACb,eAAgB,SAChB,cAAe,OACf,YAAa,QAEf,GAAiB,gBAAbhB,GAA2C,cAAbA,EAA0B,CAC1DJ,GAAYoB,EAAoBhB,GAAWF,GAC3C,IAAMmB,EAAW9E,EAAcM,QAC/B,GAAIwE,EAASC,cACY7D,IAAK+C,uBAAuBC,oBACpCc,iBAAiBF,QAlFP,SAAClF,GAAM,OAAK,WACzC,MAAwBE,EAAUQ,QAAQW,eAApCH,EAAK,EAALA,MAAOnE,EAAM,EAANA,OACb,GAAe,IAAXA,EAAc,CAChByD,EAAgBE,QAAU,CAAEQ,QAAOnE,UACnC,IAAMsI,EAAenF,EAAUQ,QAAQW,eACvCH,EAAQmE,EAAanE,MACrBnE,EAASsI,EAAatI,OAExB,IAAMuI,EAAgBpF,EAAUQ,QAAQS,UAAUD,EAAOnE,GAEzD8G,GAAY7D,GAASsF,EAActF,KA2EjCuF,CAAuBN,EAAoBhB,GAA3CsB,QApBArG,EAAsB+E,EAAUF,IA6DlCZ,WAAY/D,EAAqBiB,EAAcK,QAAUyC,EACzDtE,mBAAoBA,EACpBO,mBAAoBA,EACpBD,YAAaA,EACbJ,qBAAsBA,GAGlByG,GAAc,CAClBtG,sBAAuB8E,GACvBnF,oBAAoB,EACpBO,oBAAoB,EACpBD,aAAa,EACbG,SAAUA,GAGZ,OACE,yBAAKmG,UAAU,sBACbC,YAAa,SAACC,GACG,eAAXA,EAAEC,MAAyBxG,GAC7BuG,EAAEE,mBAIN,yBAAKJ,UAAU,cACb,kBAACK,EAAA,EAAe,KACVjB,GACCvF,EAAWkG,GAAcR,MAGlC,kBAACjJ,EAAA,EAAW,CACVgK,cAAe,SAAC3C,IA/II,SAAC4C,EAAM5C,GAC1B3C,EAAoBC,QAIzBmD,GAAY,QAAST,EAAM6C,eAHzBhH,EAAiB+G,EAAM5C,GA8InB8C,CAAkB,YAAa,IAAI1J,OAAO6G,KAAKC,YAAYC,MAAMH,KAEnEA,MAAOhE,EAAqBY,EAAOoD,MAAQ9E,EAAiB,UAC5DkB,WAAYA,EACZoG,KAAM,OACNO,cAAexF,EAAE,mCAKzBpB,EAAoBtB,UAAYA,EAEjBmI,UAAMC,KAAK9G,GCvVXA,a,qBCFf,IAAIvD,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,upKAAwpK,KAGjrK0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,4zEAA6zE,KAGt1E0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,yVCRRsI,ECOQ,SAAH,GAId,QAHJC,EAAK,EAALA,MACAC,EAAS,EAATA,UACAC,EAAiB,EAAjBA,kBAEMnG,EAAWC,cAEXmG,GACc,QAAlB,EAAApF,IAAKqF,qBAAa,aAAlB,EAAoBC,aAAcC,IAAYC,kBAAoBxF,IAAKqF,cAAcI,4BACjFC,GAAkD,QAAlB,EAAA1F,IAAKqF,qBAAa,aAAlB,EAAoBC,UAAUK,iBAAkBJ,IAAYK,KAAOR,EACnGS,EAAgCZ,GAASC,GAAaQ,GAAiC1F,IAAK8F,mBAoBlG,OACE,oCACGD,GACC,yBAAK1B,UAAU,mBACb,kBAAC4B,EAAA,EAAM,CACLC,YAAY,4BACZC,GAAG,uBACH3B,KAAK,WACL4B,MAAOC,IAAQ9G,EAAE,gCACjB+G,QAASjB,EACTkB,SA5Be,SAACC,GACxB,GAAKtG,IAAK8F,mBAAV,CAIA,IAAMS,EAAiBD,EAAME,OAAOJ,QAC9BK,EAAOF,EACTvG,IAAK0G,oBAAoBC,SAASC,kBAAoB5G,IAAK0G,oBAAoBC,SAASE,cACxF,KACqBC,cAER/K,SAAQ,SAACgL,GAAS,MACjB,QAAhB,EAAAA,EAAKC,mBAAW,OAAhB,OAAAD,EAAmBN,GACnBzH,EAASkB,IAAQ+G,kBAAkB,CAAEC,SAAUH,EAAKrC,KAAMyC,UAAWZ,e,ysBC5B3E,MAA+BrL,OAAO6G,KAA9BqF,EAAK,EAALA,MAAOpF,EAAW,EAAXA,YACPqF,EAAcD,EAAdC,UAUKC,EAA4B,SAACJ,GAUxC,MATmC,CACjCE,EAAMG,2BACNH,EAAMI,mBACNJ,EAAMK,4BACNL,EAAMM,cACNN,EAAMO,yBACNP,EAAMQ,0BAG0BC,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAcxEgB,EAA0C,SAACb,GAmBtD,MAlBsC,CACpCE,EAAMY,oBACNZ,EAAMa,kBACNb,EAAMc,kBACNd,EAAMe,uBACNf,EAAMgB,6BACNhB,EAAMiB,0BACNjB,EAAMkB,mBACNlB,EAAMmB,kBACNnB,EAAMoB,oBAENpB,EAAMqB,wBACNrB,EAAMsB,+BACNtB,EAAMuB,4BACNvB,EAAMwB,2BACNxB,EAAMyB,6BAG6BhB,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAG3E+B,EAAiD,SAAC5B,GAM7D,MALyC,CACvCE,EAAM2B,sBACN3B,EAAM4B,gBACN5B,EAAM6B,YAEgCpB,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAG9EmC,EAAyB,SAAChC,GAcrC,MAboC,CAClCE,EAAM+B,wBACN/B,EAAMgC,wBACNhC,EAAMiC,uBACNjC,EAAMkC,wBACNlC,EAAMG,2BACNH,EAAM2B,sBACN3B,EAAMmC,yBACNnC,EAAM4B,gBACN5B,EAAMoC,iBACNpC,EAAMqC,yBACNrC,EAAMsC,2BAE2B7B,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAGzE4C,EAAwB,SAACzC,GAMpC,MALmC,CACjCE,EAAM2B,sBACN3B,EAAM4B,gBACN5B,EAAM6B,YAE0BpB,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAGxE6C,EAA4B,SAAC1C,GAKxC,MAJuC,CACrCE,EAAMa,kBACNb,EAAMyC,gBAE8BhC,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAe5E+C,EAA0B,SAAC5C,GAWtC,MAVqC,CACnCE,EAAMoB,oBACNpB,EAAM6B,WACN7B,EAAMqB,wBACNrB,EAAMwB,2BACNxB,EAAMyB,4BACNzB,EAAM2C,6BACN3C,EAAMuB,4BACNvB,EAAMsB,gCAE4Bb,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAqC1EiD,EAAiC,SAAC9C,GAE7C,MADwC,CAACE,EAAMoB,qBACRX,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,MAG7EkD,EAA0B,SAAC/C,EAAUgD,GAChD,IAAMC,EAAa,CACjB,0BAA6B,CAC3B,MAAS,sBACT,YAAe,2CACf,UAAa,sCAGjB,OAAOA,EAAWjD,IAAaiD,EAAWjD,GAAUgD,IAoBzCE,EAAqB,SAACC,GACjC,OAAOA,EAAoB5O,QAAU,EACjCyG,MAAMoI,KAAK,IAAIC,IAAIF,EAAoBG,KAAI,SAAC5N,GAAU,OAAKA,EAAW6N,cACtE,MAGOC,EAAgB,SAACL,EAAqBM,GACjD,IAAMC,EAAkBR,EAAmBC,GACrCnD,GAAW0D,aAAe,EAAfA,EAAiBnP,QAAS,EAAImP,EAAgB,GAAKD,EAAYjG,KAUhF,MAAO,CACLwC,WACArJ,YAT4B,KAA5B+M,aAAe,EAAfA,EAAiBnP,SAAgBmP,EAAgB,KAAOvD,EAAUwD,WAClE3D,IAAaG,EAAUwD,UASvBC,SARcF,aAAe,EAAfA,EAAiBvJ,SAASgG,EAAU0D,SAAU7D,IAAaG,EAAU0D,MASnF/M,SAReqM,EAAoBxC,MAAK,SAACjL,GAAU,OAAKA,aAAsBoF,EAAYgJ,qBA/BtD,SAAC9D,GAMvC,MALuC,CACrCG,EAAU4D,gBACV5D,EAAU6D,eACV7D,EAAU8D,iBAE0B9J,SAAS6F,GAyBkEkE,CAAyBT,EAAYjG,MASpJ2G,0BARgCrL,IAAKsL,8BAA8BD,4BASnE5H,WARiByD,IAAaG,EAAUkE,UASxCrN,WAAYgJ,EACZ0D,oBAISY,EAAwB,WAAM,MACnCxM,EAAWC,cACXkG,EAAoB/G,YAAYE,IAAU6G,mBAC1CsG,EAAwBrN,aAAY,SAACC,GAAK,OAAKC,IAAUoN,kBAAkBrN,EAAO+B,IAAauL,iBAC/FC,EAA+BxN,aAAY,SAACC,GAAK,OAAKC,IAAUuN,cAAcxN,EAAO+B,IAAa0L,2BAClGC,EAA6B3N,aAAY,SAACC,GAAK,OAAKC,IAAUuN,cAAcxN,EAAO+B,IAAa4L,yBAChGC,EAA2B7N,aAAY,SAACC,GAAK,OAAKC,IAAUuN,cAAcxN,EAAO+B,IAAa8L,sBAC9FC,EAA6B/N,aAAY,SAACC,GAAK,OAAKC,IAAUuN,cAAcxN,EAAO+B,IAAagM,8BAEhGC,GAAU,OACbjM,IAAa0L,uBAAyBF,GAA4B,IAClExL,IAAa4L,qBAAuBD,GAA0B,IAC9D3L,IAAa8L,kBAAoBD,GAAwB,IACzD7L,IAAagM,0BAA4BD,GAA0B,GAGhEG,EAAkB,SAACtG,GAIrBhH,EAHGqN,EAAWrG,GAGL9F,IAAQqM,aAAavG,GAFrB9F,IAAQsM,YAAYxG,KAajC,MAAO,CACLb,oBACAsG,wBACAG,+BACAG,6BACAE,2BACAE,6BACAM,uBAf6B,WAC7BzN,EAASkB,IAAQwM,aAAatM,IAAauM,mBAC3CL,EAAgBlM,IAAagM,4BAc7BQ,yBAZ+B,WAAH,OAASN,EAAgBlM,IAAa0L,yBAalEe,uBAZ6B,WAAH,OAASP,EAAgBlM,IAAa4L,uBAahEc,qBAZ2B,WAAH,OAASR,EAAgBlM,IAAa8L,sB,ikCCpPlE,IAAMa,EAAkBC,IAAoBC,OAAOC,KAE7CC,EAAqB,SAAH,GAyBlB,IAxBJC,EAAwC,EAAxCA,yCACAtC,EAAO,EAAPA,QACAuC,EAAmB,EAAnBA,oBACAC,EAAa,EAAbA,cACAC,EAAW,EAAXA,YACArP,EAAU,EAAVA,WACAsP,EAAmB,EAAnBA,oBACAC,EAAgB,EAAhBA,iBACAC,EAAwB,EAAxBA,yBACAC,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACAC,EAAW,EAAXA,YACAxC,EAAyB,EAAzBA,0BACA5H,EAAU,EAAVA,WACAqK,EAAsB,EAAtBA,uBACAC,EAAiB,EAAjBA,kBACAtC,EAAqB,EAArBA,sBACAuC,EAAmB,EAAnBA,oBACAC,EAAe,EAAfA,gBACAC,EAAoB,EAApBA,qBACAC,EAAe,EAAfA,gBACAvB,EAAwB,EAAxBA,yBACAhB,EAA4B,EAA5BA,6BACAwC,EAAmB,EAAnBA,oBAEO/O,EAAqB,EAAhBC,cAAgB,GAApB,GAEF+O,EAAyBV,EAAuB,+BAAiC,yBAEjFW,EACJ,yBAAKnK,UAAU,0BACX2G,GACA,oCACE,yBAAK3G,UAAU,cACb,kBAAC1J,EAAA,EAAW,CAACgK,cAAe4I,EAAqBC,cAAeA,EAAexL,MAAOyL,EACpFrP,WAAYA,EAAYoG,KAAM,SAAUO,cAAexF,EAAE,0CAE3DoO,GAAoBC,GAA6BA,EAIlDC,GAAwB,yBAAKxJ,UAAU,eAAeyJ,EAAa,cACjEC,KAAiBxC,IAA8B5H,KAAgB+J,GAChE,yBAAKrJ,UAAU,eACb,yBAAKA,UAAU,uCACb,yBAAKA,UAAU,gBAAgB9E,EAAE,6BACjC,yBAAK8E,UAAU,yBACZwJ,GACC,kBAACY,EAAA,EAAQ,CACPtI,GAAG,yBACHuI,kBAAkB,8BAClBrK,UAAU,qCACV6B,YAAY,yBACZyI,OAAQC,IACRC,YAAab,EACbc,oBAAqBb,EACrBc,iBAAe,KAGjBpD,GACA,kBAAC8C,EAAA,EAAQ,CACPtI,GAAG,0BACHuI,kBAAmBH,EACnBlK,UAAS,6CAA0C0J,IAAgBF,EAAuB,gBAAkB,IAC5G3H,YAAY,0BACZyI,OAAQd,GAAwBS,EAAsBpB,IAAsBD,EAC5E4B,YAAaX,EACbY,oBAAqBX,EACrBY,iBAAe,IAGlBlB,GACC,kBAACY,EAAA,EAAQ,CACPtI,GAAG,uBACHuI,kBAAkB,4BAClBrK,UAAU,mCACV6B,YAAY,uBACZyI,OAAQK,IACRH,YAAaT,EACbU,oBAAqBT,EACrBU,iBAAe,SAYnC,OAAKzB,EAKH,kBAAC2B,EAAA,EAAkB,CACjBC,OAAQ3P,EAAE4K,EAAwB/L,EAAY,gBAAkB,sCAChE+Q,aAAc,EACdC,qBAAqB,EACrBC,SAAUvC,EACVwC,kBAAmBhC,EACnBiC,WAAazD,IAAiCwB,GAC5CkB,GAXGA,GAgBInB,I,+hCAEfA,EAAmBxQ,UAAY,CAC7ByQ,yCAA0CvQ,IAAUW,KACpDsN,QAASjO,IAAUW,KACnB6P,oBAAqBxQ,IAAUa,KAC/B4P,cAAezQ,IAAUa,KACzB6P,YAAa1Q,IAAUM,UAAU,CAACN,IAAUO,OAAQP,IAAUC,SAC9DoB,WAAYrB,IAAUO,OACtBoQ,oBAAqB3Q,IAAUW,KAC/BiQ,iBAAkB5Q,IAAUW,KAC5BkQ,yBAA0B7Q,IAAUyS,KACpC3B,qBAAsB9Q,IAAUW,KAChCoQ,aAAc/Q,IAAUa,KACxBmQ,YAAahR,IAAUO,OACvBiO,0BAA2BxO,IAAUW,KACrCiG,WAAY5G,IAAUW,KACtBsQ,uBAAwBjR,IAAUa,KAClCqQ,kBAAmBlR,IAAUO,OAC7BqO,sBAAuB5O,IAAUW,KACjCwQ,oBAAqBnR,IAAUa,KAC/BuQ,gBAAiBpR,IAAUO,OAC3B8Q,qBAAsBrR,IAAUa,KAChCyQ,gBAAiBtR,IAAUO,OAC3BwP,yBAA0B/P,IAAUa,KACpCkO,6BAA8B/O,IAAUW,KACxC4Q,oBAAqBvR,IAAUW,MCnJjC,ICHe+R,EDGa,SAAtBA,EAAmB,GAOnB,IANJnC,EAAwC,EAAxCA,yCACAtD,EAAuB,EAAvBA,wBACA6D,EAAoB,EAApBA,qBACAC,EAAY,EAAZA,aACA3B,EAAwB,EAAxBA,yBACAa,EAAoB,EAApBA,qBAEOzN,EAAqB,EAAhBC,cAAgB,GAApB,GAERiQ,EAAoB5S,UAAY,CAC9ByQ,yCAA0CvQ,IAAUW,KACpDsM,wBAAyBjN,IAAUW,KACnCmQ,qBAAsB9Q,IAAUW,KAChCoQ,aAAc/Q,IAAUa,KACxBuO,yBAA0BpP,IAAUW,KACpCsP,qBAAsBjQ,IAAUa,MAGlC,IAAM4Q,EACJ,yBAAKnK,UAAU,kCAKXwJ,IAAyB7D,GACzB,yBAAK3F,UAAU,eAAeyJ,EAAa,UAAWR,KAK5D,OAAMA,GAA6CtD,EAC1CwE,EAIP,kBAACS,EAAA,EAAkB,CACjBC,OAAQ3P,EAAE,yBACV4P,aAAc,EACdC,qBAAqB,EACrBG,WAAapD,IAA6BmB,EAC1C+B,SAAUrC,GACRwB,I,63EE/BR,IAAQtM,GAAgB9G,OAAO6G,KAAvBC,YA8TOwN,GA5TO,SAAH,GAA6C,IAAvCnF,EAAmB,EAAnBA,oBAAqBM,EAAW,EAAXA,YACrCtL,EAAqB,GAAhBC,cAAgB,GAApB,GAMN,KALwBb,mBAAS,CACjCgR,YAAa,KACbC,gBAAiB,KACjBC,QAAS,KACTC,UAAW,OACX,GALK5S,EAAK,KAAE6S,EAAQ,KAM0B,KAAZpR,mBAAS,IAAG,GAAzCqR,EAAU,KAAEC,EAAa,KACkB,KAAZtR,mBAAS,IAAG,GAA3CoP,EAAW,KAAEmC,EAAc,KACsB,KAAZvR,mBAAS,IAAG,GAAjDwR,EAAc,KAAEC,EAAiB,KACY,KAAZzR,mBAAS,IAAG,GAA7C0R,EAAY,KAAEC,EAAe,KACuB,KAAf3R,oBAAS,GAAM,GAApD4R,EAAc,KAAEC,EAAiB,KACkB,KAAd7R,mBAAS,MAAK,GAAnD8R,EAAc,KAAEC,EAAiB,KAClCC,EAAkB9F,aAAW,EAAXA,EAAajG,KACuI,KAApHjG,mBAASgS,IAAoBC,MAAsBC,YAAeC,YAAiBH,IAAkBI,gBAAe,GAArKlD,EAAoB,KAAEmD,EAAuB,KAC9CC,EAAkB,CAAC,cAAe,aAClC/R,EAAWC,cAWf,KAJEb,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAU0S,qBAAqB3S,GAC/BC,IAAU2S,oCAAoC5S,GAC9CC,IAAU4S,2BAA2B7S,OACrC,GAPA8S,EAAgB,KAChBF,EAAmC,KACnCG,EAAuB,KAOnBC,EAAqBhH,aAAmB,EAAnBA,EAAsB,GAEjD9K,qBAAU,WACR+R,EAAuB3G,KACtB,CAACA,IAEJ,IAAM2G,EAAyB,SAAC3G,GAC9B,GAAK3K,IAAK8F,oBAGN6E,GAAeA,EAAY4G,YAAa,CAC1C,IAAMpM,IAAsBwF,EAAY4G,cACxCvS,EAASkB,IAAQ+G,kBAAkB,CAAEC,SAAUyD,EAAYjG,KAAMyC,UAAWhC,OA4B1EqM,EAAsB,SAAC5U,GAC3B,IAAM6U,EAAc,GAEhBjR,EAAc,QAClB,IACEA,EAAuC,SAAxB5D,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAO6D,GACPC,QAAQC,MAAMF,GAGhBgR,EAAYvU,UAAYN,EAAWM,UACnCuU,EAAYpU,cAAgBT,EAAWiE,mBACvC4Q,EAAY3Q,KAAOlE,EAAWkE,KAC9B2Q,EAAY1Q,SAAWnE,EAAWmE,SAClC0Q,EAAYzQ,UAAYpE,EAAWoE,UACnCyQ,EAAYxQ,kBAAoBrE,EAAWqE,kBAC3CwQ,EAAYhQ,mBAAqB7E,EAAW8E,wBAC5C+P,EAAYjR,YAAcA,EAC1BiR,EAAYpB,eAAiBzT,EAAWyT,iBACxCC,EAAkB1T,EAAWyT,kBAE7B,MJiCqC,SAACqB,EAAUC,GAClD,IAAMC,EAAqB,IAAIrH,IACzBsH,EAAkB,IAAItH,IAE5B,IAAK,IAAMuH,KAAOJ,EAChB,GAAIA,EAASK,eAAeD,GAAM,CAChC,IAAMlS,EAAQoS,SAASF,EAAK,KACvBG,MAAMrS,IAA+B,MAArB+R,EAAU/R,IAAkB8R,EAASI,GAAK,gBAC7DF,EAAmBM,IAAIR,EAASI,GAAK,eAAeK,SAEjDF,MAAMrS,IAA+B,MAArB+R,EAAU/R,IAAkB8R,EAASI,GAAK,cAC7DD,EAAgBK,IAAIR,EAASI,GAAK,aAAaK,QAKrD,MAAO,CACLhU,MAAO+D,MAAMoI,KAAKsH,GAClBQ,MAAOlQ,MAAMoI,KAAKuH,IInDOQ,CAA0BZ,EAAYpU,cAAeT,EAAW0V,eAAjFnU,EAAK,EAALA,MAAOiU,EAAK,EAALA,MAQf,OAPIjU,EAAM1C,QAAU,GAAuB,IAAjB0C,EAAM1C,QAAgB0C,EAAM,KAAOsT,EAAY3Q,QACvE2Q,EAAY3Q,UAAOwB,IAEjB8P,EAAM3W,QAAU,GAAuB,IAAjB2W,EAAM3W,QAAgB2W,EAAM,KAAOX,EAAY1Q,YACvE0Q,EAAY1Q,cAAWuB,GAGlBmP,GAkCHc,EAAiB,SAACxL,GACtB,IAGmE,EAH7DG,EAAWH,EAAKrC,KAChB8N,EAASC,YAAcvL,IAEzBA,EAAS7F,SAAS,aAAe6F,EAAS7F,SAAS,cACrDmR,EAAOnC,eAA8B,QAAhB,EAAGtJ,EAAK7D,gBAAQ,aAAb,EAAemN,eACvCC,EAAkBkC,EAAOnC,iBAG3BR,EAAS2C,GAAU,IACnBtC,GAAkBsC,aAAM,EAANA,EAAQE,iBAAkB,IAC5CtC,GAAgBoC,aAAM,EAANA,EAAQG,eAAgB,IACxC3C,GAAewC,aAAM,EAANA,EAAQhS,cAAe,IArGH,SAACuG,GAAS,MACvCG,EAAWH,EAAKrC,KAChBkO,EAAkC,QAA7B,EAAGzB,EAAiBjK,UAAS,aAA1B,EAA4B0L,MAC1C7C,EAAc,GAAD,OAAI1Q,EAAE4K,EAAwB/C,EAAU,UAAY0L,GAAM,YAAIvT,EAAE,8BAmG7EwT,CAA6B9L,IAG/BxH,qBAAU,WACR,GAAI8R,EAAoB,CAClBhH,EAAoB5O,OAAS,GA7FQqX,EA8FHzI,EA7FxC0F,EAAc,GAAD,OAAI1Q,EAAE,mCAAkC,aAAKyT,EAAYrX,OAAM,QAT1CmB,EAwGHyU,GAvGhB0B,2BACbhD,EAAc,GAAD,OAAI1Q,EAAE,mCAAkC,YAAIA,EAAE,oCAG7D0Q,EAAc,GAAD,OAAI1Q,EAAE4K,EAAwBrN,EAAW6N,SAAU,WAAiD,QAAzC,EAAI0G,EAAiBvU,EAAW6N,iBAAS,aAArC,EAAuCmI,QAAM,YAAIvT,EAAE,oCA+CnG,SAACzC,GAAe,YACxC6U,EAAc,GACd7U,aAAsBoF,GAAYgR,qBACpCvB,EAAcD,EAAoB5U,IAGhCA,aAAsBoF,GAAYiR,sBACpCxB,EAAyB,YAAI7U,EAAWsW,YACxCzB,EAAkB,KAAI7U,EAAWkE,KACjC2Q,EAAsB,SAAI7U,EAAWmE,SACrC0Q,EAAuB,UAAI7U,EAAWoE,WAGpCpE,aAAsBoF,GAAYgJ,uBAA4C1I,IAAxB1F,EAAWmE,WACnE0Q,EAAY1Q,SAAWnE,EAAWmE,UAGpC8O,EAAS,GAAD,MACH7S,GAAK,IACRyS,YAAmC,QAAxB,EAAE7S,EAAW6S,mBAAW,QAAI,KACvCC,gBAA2C,QAA5B,EAAE9S,EAAW8S,uBAAe,QAAI,KAC/CC,QAA2B,QAApB,EAAE/S,EAAW+S,eAAO,QAAI,KAC/BC,UAA+B,QAAtB,EAAEhT,EAAWgT,iBAAS,QAAI,MAChC6B,IAGLvB,EAAkBtT,EAAWuW,cAAgBvW,EAAWuW,gBAAkB,QAC1E/C,EAAgBxT,EAAWwW,YAAcxW,EAAWwW,cAAgB,QACpEpD,EApEqB,SAACpT,GACtB,IAAMI,EAAQJ,EAAkB,MAC1ByW,EAASzW,EAAmB,OAClC,MAAiB,SAAVI,EAAmBA,EAAQ,GAAH,OAAMA,EAAK,YAAIqW,GAiE/BC,CAAe1W,IA0B5B2W,CAAsBlC,GACtB,IAAMR,EAAiBxG,EAAoBmJ,OAAM,SAAC5W,GAAU,OAAK+T,YAAeC,YAAiBhU,EAAW6N,WAAWoG,kBACvHC,EAAwBD,QACflG,IACT4H,EAAe5H,GACfmG,EAAwBH,YAAeC,YAAiBH,IAAkBI,iBA/G3C,IAACjU,EAAe,EAQNkW,IAyG1C,CAACzB,EAAoB1G,EAAaN,IAsIrC,MAAO,CACLyF,aACA9S,QACA6Q,cACAoC,iBACAE,eACA7C,cA1IoB,SAAC3K,EAAUF,GAC/B,IAAMgR,EAAW,GAAH,MAAQzW,GAAK,SAAG2F,EAAWF,IAGzC,GAFAoN,EAAS4D,GAE0B,IAA/BpJ,EAAoB5O,QAAgB8U,GAA+B,cAAb5N,EAA0B,CAClF,IAAM5F,EAASwT,EAAe,GAC9B,GAAIxT,WAAQ2W,WAAY,CACtB,IAAM9W,EAAa2T,EAAe,GAGlC,OAFAxT,EAAO8S,SAAS,CAAE8D,WAAYlR,SAC9B7F,EAAsB,UAAI,IAAIoF,GAAYC,MAAMQ,KAKpD,GAAI4H,EAAoB5O,OAAS,EAC/B4O,EAAoBtO,SAAQ,SAACa,GAC3B,GAAImU,EAAgB1P,SAASsB,GAAW,CACtC,IAAMiR,EAAWC,aAAUpR,GACrBX,EAAQ,IAAIE,GAAYC,MAAM2R,EAASE,EAAGF,EAASG,EAAGH,EAASI,EAAGJ,EAASK,GACjFjU,IAAKkU,oBAAoBtX,EAAY,MAAG+F,EAAWb,GAASsP,GACxDH,GACFkD,YAAcvX,EAAW6N,SAAU9H,EAAUb,QAG/C9B,IAAKkU,oBAAoBtX,EAAY,MAAG+F,EAAWF,GAAS2O,GACxDxU,aAAsBoF,GAAYgR,oBAAsB,CAAC,WAAY,OAAQ,mBAAmB3R,SAASsB,IAC3GE,YAA0BjG,GAExBqU,GACFkD,YAAcvX,EAAW6N,SAAU9H,EAAUF,GAGjDzC,IAAK+C,uBAAuBqR,iBAAiBxX,GACzCA,aAAsBoF,GAAYgJ,kBACpCpO,EAAWyX,iBAGV,CACL,IAAM1J,EAAc3K,IAAKsU,cACzB,GAAI3J,EAAa,CACf,GAAIoG,EAAgB1P,SAASsB,GAAW,CACtC,IAAMiR,EAAWC,aAAUpR,GACrBX,EAAQ,IAAIE,GAAYC,MAAM2R,EAASE,EAAGF,EAASG,EAAGH,EAASI,EAAGJ,EAASK,GACjFE,YAAcxJ,EAAYjG,KAAM/B,EAAUb,QAE1CqS,YAAcxJ,EAAYjG,KAAM/B,EAAUF,GAExCkI,aAAuBzP,OAAO6G,KAAKqF,MAAM2B,uBAC3C4B,EAAY4J,iBA2FlBC,kBArFwB,SAACtK,EAASzH,GAClC,IAAMgS,EAAqB,CACzBC,MAAO,iBACPC,OAAQ,cACRC,IAAK,gBAYP,GAVgB,UAAZ1K,GACFgG,EAAkBzN,GAEJ,WAAZyH,GACF8F,EAAevN,GAED,QAAZyH,GACFkG,EAAgB3N,GAGd4H,EAAoB5O,OAAS,EAC/B4O,EAAoBtO,SAAQ,SAACa,GAI3B,GAHgB,UAAZsN,GACFtN,EAAWiY,cAAcpS,GAEX,WAAZyH,EAAsB,CACxB,IAAMmJ,EAAS5Q,EAAMqS,MAAM,KAC3BlY,EAAWmY,MAAQ1B,EAAO2B,QAC1BpY,EAAWqY,OAAS5B,EAEN,QAAZnJ,GACFtN,EAAWsY,YAAYzS,GAEzBzC,IAAK+C,qBAAqBqO,GAAyBgD,iBAAiBxX,GAChEqU,GACFkD,YAAcvX,EAAW6N,SAAUgK,EAAmBvK,GAAUzH,MAGpEzC,IAAK+C,qBAAqBqO,GAAyB+D,QAAQ,oBAAqB,CAAC9K,EAAqB,SAAU,SAC3G,CACL,IAAMM,EAAc3K,IAAKsU,cACrB3J,GACFwJ,YAAcxJ,EAAYjG,KAAM+P,EAAmBvK,GAAUzH,KAgDjE4N,iBACA+E,eA5CqB,WACjB/K,EAAoB5O,OAAS,EAC/B4O,EAAoBtO,SAAQ,SAACa,GAC3ByY,aAA6BzY,EAAY0T,EAAmBD,MAErD1F,IACTwJ,YAAcxJ,EAAYjG,KAAM,kBAAmB2L,GACnDC,GAAmBD,KAsCrBiF,0BAlCgC,SAAC3S,EAAUF,GAAU,MAC/C8S,GAA+B,QAAnB,EAAAvY,EAAMK,qBAAa,aAAnB,EAAsB,KAAM,GACxCmY,EAAW,CAAC,YAAa,gBAAgBnU,SAASsB,GACpD8S,aAAkB,MAAG9S,EAAWF,GAAS8S,GACzC9S,EAEEiT,EAAgB,CACpBC,EAAG,GAAF,MACIJ,GAAS,SACE,cAAb5S,GAAyC,iBAAbA,EAA8B,kBAAoBA,EAAW6S,KAI1FnL,EAAoB5O,OAAS,EAC/B4O,EAAoBtO,SAAQ,SAACa,GAC3BoD,IAAK4V,8BAA8BhZ,EAAY,MAAG+F,EAAWF,GAAS2O,MAE/DzG,GACTwJ,YAAcxJ,EAAYjG,KAAM,gBAAiBgR,GAGnD7F,EAAS,GAAD,MAAM7S,GAAK,IAAEK,cAAeqY,MAcpCG,mBAAoBrF,EACpBtS,WAAYyM,aAAW,EAAXA,EAAajG,KACzBiJ,yB,4iCCpUJ,IAAMmI,GAAqB,SAAH,GAA6C,IAAvCnL,EAAW,EAAXA,YAAaN,EAAmB,EAAnBA,oBAClChL,EAAqB,GAAhBC,cAAgB,GAApB,GAGNwQ,EACEN,GAAc,CAAE7E,cAAaN,wBAD/ByF,WAGF,OACE,oCACGA,GACC,wBAAI3L,UAAU,sBAAsB2L,GAEtC,yBAAK3L,UAAU,oBACb,6BACE,kBAAC4R,GAAA,EAAI,CAAC5R,UAAU,aAAa6R,MAAM,kCAErC,yBAAK7R,UAAU,OAAO9E,EAAE,oCAMhCyW,GAAmBnZ,UAAY,CAC7B0N,oBAAqBxN,IAAUoZ,QAAQpZ,IAAUC,QACjD6N,YAAa9N,IAAUC,QAGVgZ,U,wwCCJf,IAAMnZ,GAAY,CAChBuZ,WAAYrZ,IAAUO,OACtB+S,aAActT,IAAUO,OACxBqG,WAAY5G,IAAUW,KACtB6N,0BAA2BxO,IAAUW,KACrC2Y,gBAAiBtZ,IAAUW,KAC3BK,YAAahB,IAAUW,KACvBsN,QAASjO,IAAUW,KACnB4Y,wBAAyBvZ,IAAUW,KACnCgX,kBAAmB3X,IAAUa,KAC7B4P,cAAezQ,IAAUa,KAAK2Y,WAC9BC,yBAA0BzZ,IAAUC,OACpC6Q,qBAAsB9Q,IAAUW,KAChC+Y,iBAAkB1Z,IAAUoZ,QAAQpZ,IAAUO,QAC9C6S,eAAgBpT,IAAUO,OAC1ByQ,YAAahR,IAAUO,OACvBJ,MAAOH,IAAUC,OAAOuZ,WACxBnP,SAAUrK,IAAUO,OACpBwN,gBAAiB/N,IAAUoZ,QAAQpZ,IAAUO,SAKzCoZ,GAAc,SAAH,GAeX,IAgB6BC,ENiDCvP,EM/ElCoG,EAAa,EAAbA,cACAtQ,EAAK,EAALA,MACAyG,EAAU,EAAVA,WACA5F,EAAW,EAAXA,YACA8P,EAAoB,EAApBA,qBACA7C,EAAO,EAAPA,QACAO,EAAyB,EAAzBA,0BACA4E,EAAc,EAAdA,eACAE,EAAY,EAAZA,aACAtC,EAAW,EAAXA,YACA2G,EAAiB,EAAjBA,kBACAtW,EAAU,EAAVA,WACAiY,EAAe,EAAfA,gBACAvL,EAAe,EAAfA,gBAEOvL,EAAqB,GAAhBC,cAAgB,GAApB,GACFN,EAAWC,cACgD,KAA3BR,mBAASzB,EAAMyS,aAAY,GAA1DlC,EAAW,KAAEmJ,EAAc,KACwC,KAAxBjY,mBAASwR,GAAe,GAAnElC,EAAiB,KAAE4I,EAAoB,KACsB,KAAtBlY,mBAAS0R,GAAa,GAA7DhC,EAAe,KAAEyI,EAAkB,KACyB,KAArBnY,mBAASoP,GAAY,GAA5DI,GAAe,KAAE4I,GAAkB,KACiB,MAAzBpY,mBAASzB,EAAM4S,WAAU,GAApDkH,GAAS,MAAEC,GAAY,MAExBC,GAA0B,SAACP,GAC/B,OAAI7L,GAAmBA,EAAgBnP,OAAS,EACvCmP,EAAgB/C,KAAK4O,GAEvBA,EAAcvY,IAUjB+Y,GAAoBD,GAAwBlN,GAC5CoN,GAAkBF,GAAwBrN,GAC1CyD,IAT2BqJ,EASwC1O,EARnE6C,GAAmBA,EAAgBnP,OAAS,EACvCmP,EAAgB4I,MAAMiD,GAExBA,EAAcvY,IAMjBiZ,GAA2CH,GAAwBlO,GACnE0E,GAAsBwJ,GAAwB1P,GAC9CmG,GAAmBuJ,GAAwB9N,GAC3CkO,INoC4BlQ,EMpCehJ,ENqCf,CAChCkJ,EAAMiQ,8BACNjQ,EAAMO,yBACNP,EAAMkQ,+BACNlQ,EAAMiB,0BACNjB,EAAMmQ,qCACNnQ,EAAMoQ,4CAEyB3P,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,OM7CjB6D,GAC3DwD,GAAsB4I,GAAwBpN,GAEpDrK,qBAAU,WACJ6N,IACFpO,EAASkB,IAAQsM,YAAYpM,IAAa0L,2BAE3C,CAAC5N,IAEJqB,qBAAU,WACRmX,EAAe1Z,EAAMyS,aACrBsH,GAAa/Z,EAAM4S,aAClB,CAACrC,EAAauJ,GAAW9Z,IAE5BuC,qBAAU,WACRmX,EAAe1Z,EAAMyS,aACrBkH,EAAqB1G,GACrB4G,GAAmBhJ,GACnB+I,EAAmBzG,KAClB,CAACF,EAAgBE,EAActC,IAElC,IAyBM4J,GAAiB,SAAC9U,EAAUF,GAChC6K,WAAgB3K,EAAUF,IAIxB5E,IACFb,EAAM2S,QAAU,KAChB3S,EAAM0S,gBAAkB,MAG1B,OASIlE,IARFrG,GAAiB,GAAjBA,kBACAsG,GAAqB,GAArBA,sBACAG,GAA4B,GAA5BA,6BACAG,GAA0B,GAA1BA,2BACAE,GAAwB,GAAxBA,yBACAW,GAAwB,GAAxBA,yBACAC,GAAsB,GAAtBA,uBACAC,GAAoB,GAApBA,qBA6DIc,GAAe,SAACjL,EAAU+U,GAC9B,IAAMC,EA3De,SAACrT,GACtB,IAAQqL,EAAuC3S,EAAvC2S,QAASD,EAA8B1S,EAA9B0S,gBAAiB3O,EAAa/D,EAAb+D,SAElC,OAAQuD,EAAKqB,eACX,IAAK,UACH,OAAgB,OAAZgK,EACK,KAEF,CACLhN,SAAU,UACViV,gBAAiB,UACjBnV,MAAiB,IAAVkN,EACPkI,gBAAiB,SAAClI,GAAO,gBAAQmI,KAAKC,MAAMpI,GAAQ,MACpD3J,YAAa5F,IAAa4X,eAC1BC,gBAAgB,EAChBC,eAAgB,SAChBC,IAAK,EACLC,IAAK,IACLC,KAAM,EACNC,cAAe,SAACC,GAAO,OAAKvG,SAASuG,GAAW,MAEpD,IAAK,kBACH,OAAwB,OAApB7I,EACK,KAEF,CACL/M,SAAU,kBACViV,gBAAiB,YACjBnV,MAAOiN,EACPmI,gBAAiBW,IACjBxS,YAAa5F,IAAaqY,wBAC1BR,gBAAgB,EAChBC,eAAgB,SAChBC,IAAK,EACLC,IAnJmB,GAoJnBC,KAAM,EACNK,MAAOC,YAAqBlV,IAEhC,IAAK,WACH,OAAiB,OAAb1C,EACK,KAEF,CACL4B,SAAU,WACViV,gBAAiB,OACjBnV,MAAO1B,EACP8W,gBAAiB,SAAC9W,GAAQ,gBAAQ+W,KAAKC,MAAM/F,SAASjR,EAAU,KAAI,OACpEiF,YAAa5F,IAAawY,iBAC1BT,IAAK,EACLC,IAAK,GACLC,KAAM,EACNJ,gBAAgB,EAChBC,eAAgB,SAChBI,cAAe,SAACvX,GAAQ,gBAAQ8X,WAAW9X,GAAU+X,QAAQ,GAAE,SAMjDC,CAAepW,GACnC,OAAKgV,EAIH,kBAACqB,EAAA,EAAM,IACLlH,IAAKnP,GACDgV,EAAW,CACfrK,cAAemK,GACfA,eAAgBA,GAChBC,sBAAuBA,EACvBuB,mBAAoB,EACpBC,sBAAuB,KAVlB,MAeLC,GAAgB,WACpB,GAAI/L,GACF,OAAO,yBAAKjJ,UAAU,aAIpBuJ,GAA2BE,GAAa,mBAExCwL,GAAcjD,EAAkB,GAAK,CACzChS,UAAW,cACXC,YAAa,SAACC,GACG,eAAXA,EAAEC,MAA4D,UAAnCD,EAAEmC,OAAO7K,QAAQ0d,eAC9ChV,EAAEE,mBAOR,OAFyB2S,IAAmBC,IAA4CF,KAEhErM,aAAe,EAAfA,EAAiBnP,QAAS,EACzC,kBAAC,GAAkB,MAI1B,kBAAC,GAAe,CAAC2d,YAAaA,GAAajD,gBAAiBA,IACxDe,IACA,yBAAK/S,UAAU,gBACb,kBAAC,EAAkB,CACjBiJ,yCAA0CA,GAC1CtC,QAASA,EACTuC,oBAxJkB,SAACvL,GAC3BwL,WAAgB,cAAexL,GAC/B4U,EAAe5U,IAuJPwL,cAAeA,EACfC,YAAaA,EACbrP,WAAYA,EACZsP,oBAAqBA,GACrBC,iBAAkBA,GAClBC,yBAA0BA,GAC1BC,qBAAsBA,EACtBC,aAAcA,GACdC,YAAaI,GACb5C,0BAA2BA,EAC3B5H,WAAYA,EACZqK,uBA/JqB,SAAC9Q,GAC9BwX,WAAoB,QAASxX,GAC7B2Z,EAAqB3Z,IA8Jb+Q,kBAAmBA,EACnBtC,sBAAuBA,GACvBuC,oBA7JkB,SAAChR,GAC3BwX,WAAoB,SAAUxX,GAC9B6Z,GAAmB7Z,IA4JXiR,gBAAiBA,GACjBC,qBA1JmB,SAAClR,GAC5BwX,WAAoB,MAAOxX,GAC3B4Z,EAAmB5Z,IAyJXmR,gBAAiBA,EACjBvB,yBAA0BA,GAC1BhB,6BAA8BA,GAC9BwC,oBAAqBA,KAEtB+K,MAIJjC,KAAoBzJ,IAAoBC,IAA6BA,GACrEN,KAA6C+J,IAC5C,yBAAKhT,UAAU,gBACb,kBAAC4K,EAAA,EAAkB,CACjBC,OAAQ3P,EAAE4K,EAAwB/L,EAAY,cAAgB,oCAC9D+Q,aAAc,EACdC,qBAAqB,EACrBG,WAAYtD,GACZoD,SAAUtC,IACV,yBAAK1I,UAAU,yBACb,yBAAKA,UAAU,cACb,kBAAC1J,EAAA,EAAW,CACVgK,cA3KU,SAAC3C,GACzBwL,WAAgB,YAAaxL,GAC7BiV,GAAajV,IA0KCwL,cAAeA,EACfxL,MAAOgV,GACPwC,qBAAsBtP,EAA+B9L,GACrDA,WAAYA,EACZoG,KAAM,OACNO,cAAexF,EAAE,0CAKvB4X,IAAqBkC,MAI3B,yBAAKhV,UAAU,gBACb,kBAAC,EAAmB,CAClBiJ,yCAA0CA,GAC1CtD,wBAAyBmN,GACzB/Y,WAAYA,EACZyP,qBAAsBA,EACtBC,aAAcA,GACd3B,yBAA0BA,GAC1Ba,qBAAsBA,KAEvBsK,IAAwB+B,MAG1B/B,IACC,qCAEIhK,IAA4C,yBAAKjJ,UAAU,WAC7D,yBAAKA,UAAU,gBACb,kBAAC,EAAc,CAACc,MAAOjI,EAAMiI,MAAOC,UAAWlI,EAAMkI,UAAWC,kBAAmBA,SAQzFoU,GAAkB,SAAH,GAAmD,IAA7CpD,EAAe,EAAfA,gBAAiBqD,EAAQ,EAARA,SAAUJ,EAAW,EAAXA,YACpD,OAAIjD,EACK,oCAAGqD,GAEL,wBAASJ,EAAcI,IAGhCD,GAAgB5c,UAAY,CAC1BwZ,gBAAiBtZ,IAAUW,KAC3Bgc,SAAU3c,IAAUS,IACpB8b,YAAavc,IAAUC,QAGzB0Z,GAAY7Z,UAAYA,GAET6Z,IC1WAA,GD0WAA,G,uyCEzVf,IAAMiD,GAAiB,SAAH,GAA6C,IAAvCpP,EAAmB,EAAnBA,oBAAqBM,EAAW,EAAXA,YACvC3L,EAAWC,cACTI,EAAMC,cAAND,EAC2D,KAAfZ,oBAAS,GAAM,GAA5DX,EAAkB,KAAEC,EAAqB,KAEhDwB,qBAAU,WACJzB,GACFkB,EAASkB,IAAQqM,aAAanM,IAAasZ,qBAE5C,CAAC5b,IAEJ,MAaI0R,GAAc,CAAEnF,sBAAqBM,gBAZvCmF,EAAU,EAAVA,WACA9S,EAAK,EAALA,MACA6Q,EAAW,EAAXA,YACAP,EAAa,EAAbA,cACAkH,EAAiB,EAAjBA,kBACAnE,EAAc,EAAdA,eACA+E,EAAc,EAAdA,eACAE,EAAyB,EAAzBA,0BACAO,EAAkB,EAAlBA,mBACAlI,EAAoB,EAApBA,qBACAsC,EAAc,EAAdA,eACAE,EAAY,EAAZA,aAGIwJ,EAAgBjP,EAAcL,EAAqBM,GAEvD9M,EAGE8b,EAHF9b,YACAG,EAEE2b,EAFF3b,SACAE,EACEyb,EADFzb,WAGF,EAGIsN,IAFFiB,EAAsB,EAAtBA,uBACAN,EAA0B,EAA1BA,2BAGIiB,EAA2CrF,EAAwC7J,GACzFqB,qBAAU,WACJ6N,GACFpO,EAASkB,IAAQsM,YAAYpM,IAAagM,8BAE3C,CAAClO,IAEJ,IAAM0b,EAAcC,aAAkBhE,GAEtC,OACE,oCACE,wBAAI1R,UAAU,sBAAsB2L,GACpC,yBAAK3L,UAAU,cACbC,YAAa,SAACC,GACG,eAAXA,EAAEC,MAA4D,UAAnCD,EAAEmC,OAAO7K,QAAQ0d,eAC9ChV,EAAEE,mBAIN,yBAAKJ,UAAU,0BACb,kBAAC4K,EAAA,EAAkB,CACjBC,OAAQ3P,EAAE4K,EAAwB/L,EAAY,gBAAkB,+BAChE+Q,aAAc,EACdC,qBAAqB,EACrBG,WAAYlD,EACZgD,SAAU1C,GACV,yBAAKtI,UAAU,yBACZtG,GACC,yBAAKsG,UAAU,sCACb,yBAAKA,UAAU,iBAAiB9E,EAAE,2CAClC,kBAACya,GAAA,EAAe,CAACjY,WAAY7E,EAAOW,iBAAkB2P,EAAeyM,gBAAiB,OAG1F,kBAAC9b,GAAA,QAAmB,IAClBjB,MAAOA,GACH4c,EAAW,CACfjX,SAAU,YACVqX,YAAa,WACbzc,mBAAoB8S,EACpB5S,qBAAsB2X,EACtBzX,iBAAkB2P,EAClB1P,sBAAuB0X,EACvBxX,mBAAoBA,EACpBC,sBAAuBA,EACvBF,YAAaA,EACbK,WAAYA,EACZF,SAAUA,OAIhB,yBAAKmG,UAAU,aAEjB,kBAAC,GAAW,MACNwV,EAAa,CACjBxD,iBAAe,EACfI,iBAAkB,CAAC,UAAW,mBAC9BvZ,MAAOA,EACPsQ,cAAeA,EACfO,YAAaA,EACb2G,kBAAmBA,EACnBtW,WAAYA,EACZyP,qBAAsBA,EACtBsC,eAAgBA,EAChBE,aAAcA,QAOxBsJ,GAAe9c,UAAY,CACzB0N,oBAAqBxN,IAAUoZ,QAAQpZ,IAAUC,QACjD6N,YAAa9N,IAAUC,QAGV2c,U,wwCC5Hf,IAAMQ,GAAoB,SAAH,GAA6C,IAAvCtP,EAAW,EAAXA,YAAaN,EAAmB,EAAnBA,oBACjChL,EAAqB,GAAhBC,cAAgB,GAApB,GAER,EASIkQ,GAAc,CAAE7E,cAAaN,wBAR/BrN,EAAK,EAALA,MACA8S,EAAU,EAAVA,WACAjC,EAAW,EAAXA,YACAoC,EAAc,EAAdA,eACAE,EAAY,EAAZA,aACA7C,EAAa,EAAbA,cACAkH,EAAiB,EAAjBA,kBACA7G,EAAoB,EAApBA,qBAGIgM,EAAgBjP,EAAcL,EAAqBM,GACjDzD,EAAayS,EAAbzS,SAER,OACE,oCACE,wBAAI/C,UAAU,sBAAsB2L,GTGC,SAAC5I,GAQ1C,MAPiC,CAC/BE,EAAM8S,iBACN9S,EAAM+S,oBACN/S,EAAMgT,eACNhT,EAAMiT,oBAGwBxS,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,KSV5EuT,CAA4BpT,GAC3B,yBAAK/C,UAAU,oBACb,6BACE,kBAAC4R,GAAA,EAAI,CAAC5R,UAAU,aAAa6R,MAAM,kCAErC,yBAAK7R,UAAU,OAAO9E,EAAE,4BAG1B,kBAAC,GAAW,MACNsa,EAAa,CACjBpD,iBAAkB,CAAC,UAAW,mBAC9BvZ,MAAOA,EACPsQ,cAAeA,EACfK,qBAAsBA,EACtBsC,eAAgBA,EAChBE,aAAcA,EACdtC,YAAaA,EACb2G,kBAAmBA,OAO7ByF,GAAkBtd,UAAY,CAC5B0N,oBAAqBxN,IAAUoZ,QAAQpZ,IAAUC,QACjD6N,YAAa9N,IAAUC,QAGVmd,UCpDTM,GAAoB,SAACC,GACzB,OAAIA,EAAU/e,OAAS,GAAK+e,EAAU3S,MAAK,SAACX,GAAQ,OVGV,SAACA,GAK3C,MAJgC,CAC9BE,EAAMoB,qBAGuBX,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,KURvB0T,CAA6BvT,MAC7E4O,GAEL0E,EAAUhH,OAAM,SAACtM,GAAQ,OViGI,SAACA,GASlC,MARoC,CAClCE,EAAMkB,mBACNlB,EAAMmB,kBACNnB,EAAMoB,oBACNpB,EAAMqB,wBACNrB,EAAMwB,2BACNxB,EAAMyB,6BAE2BhB,MAAK,SAACd,GAAI,OAAK/G,IAAK8H,QAAQZ,aAAqBH,KU1GlD2T,CAAoBxT,MAC7CuS,GAEFQ,I,4iCCRT,IAeeU,GAfU,WACvB,IAAOtb,EAAqB,GAAhBC,cAAgB,GAApB,GACR,OACE,oCACE,wBAAI6E,UAAU,sBAAsB9E,EAAE,+BACtC,yBAAK8E,UAAU,oBACb,6BACE,kBAAC4R,GAAA,EAAI,CAAC5R,UAAU,aAAa6R,MAAM,kCAErC,yBAAK7R,UAAU,OAAO9E,EAAE,iC,0kCCAhC,IAAQ2C,GAAgB9G,OAAO6G,KAAvBC,YA2EO4Y,GAzEa,WAC1B,IAAMC,EAAczc,aAAY,SAACC,GAAK,OAAKC,IAAUuN,cAAcxN,EAAO,iBACpEyc,EAA4BC,eAE2D,KAAvCtc,mBAASuB,IAAKgb,0BAAyB,GAAtF3Q,EAAmB,KAAE4Q,EAAsB,KACgB,KAA5Bxc,mBAASuB,IAAKsU,eAAc,GAA3D3J,EAAW,KAAEuQ,EAAc,KAE5BC,EAAgB,CAACnZ,GAAYoZ,4BAE7BC,EAAeC,MAAS,WAC5B,IAAMC,EAAoBvb,IAAK+C,uBACzBgE,EAAO/G,IAAKsU,cACZxB,EAAc9S,IAAKgb,yBAEnBQ,EAAyB,IAAIjR,IAAIuI,GACvCA,EAAY/W,SAAQ,SAACa,GACfA,EAAW6e,YACcF,EAAkBG,oBAAoB9e,GAC9Cb,SAAQ,SAAC4f,GAAO,OAAKH,EAAuBtJ,IAAIyJ,MAC1D/e,EAAWgf,qBAAqBngB,OAAS,GAClDmB,EAAWgf,qBAAqB7f,SAAQ,SAAC8f,GAAK,OAAKL,EAAuBtJ,IAAI2J,SAGlF,IAAMxR,EAAsBnI,MAAMoI,KAAKkR,GAEvCP,EAAuB5Q,GACvB6Q,EAAenU,KACd,IAAK,CAAE+U,SAAS,EAAOC,UAAU,IAEpCxc,qBAAU,WACJsb,GACFQ,MAED,CAACR,IAEJtb,qBAAU,WAIR,OAHAS,IAAKC,iBAAiB,qBAAsBob,GAC5Crb,IAAKC,iBAAiB,kBAAmBob,GAElC,WACLrb,IAAKM,oBAAoB,qBAAsB+a,GAC/Crb,IAAKM,oBAAoB,kBAAmB+a,MAE7C,IAcH,IAAKR,EACH,OAAO,KAGT,IAAMmB,GAfe3R,EAAoB5O,OAAS,EZsIlB,SAACqX,EAAaqI,GAC9C,OAA8B,IAAvBrI,EAAYrX,QALU,SAACmB,EAAYqf,GAC1C,OAAOA,EAAMpU,MAAK,SAACvD,GAAI,OAAK1H,aAAsB0H,KAIf4X,CAAgBpJ,EAAY,GAAIqI,GYtI/DgB,CAAmB9R,EAAqB8Q,IACvCL,EAA0BzZ,SAASsJ,aAAW,EAAXA,EAAajG,OAG1CiW,GFnDyB,SAAChQ,EAAaN,GAClD,IAAMO,EAAkBR,EAAmBC,GACrCmQ,GAAY5P,aAAe,EAAfA,EAAiBnP,SAAU,EAAImP,EAAkB,CAACD,EAAYjG,MAEhF,OAAO6V,GAAkBC,GEkDhB4B,CAAuBzR,EAAaN,GAS7C,OACE,kBAACgS,GAAA,EAAkB,CAACrW,YAAY,aAAa7B,UAAU,oBACrD,kBAAC6X,EAAmB,CAClBrR,YAAaA,EACbN,oBAAqBA,MChFduQ", "file": "chunks/chunk.44.js", "sourcesContent": ["import ColorPicker from './ColorPicker';\n\nexport default ColorPicker;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RichTextStyleEditor.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport ColorPicker from 'components/StylePicker/ColorPicker';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport './RichTextStyleEditor.scss';\nimport DataElements from 'constants/dataElement';\nimport TextStylePicker from 'components/TextStylePicker';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport { useTranslation } from 'react-i18next';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  editor: PropTypes.object,\n  style: PropTypes.shape({\n    TextColor: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.object\n    ]),\n    RichTextStyle: PropTypes.any,\n  }),\n  isFreeTextAutoSize: PropTypes.bool,\n  onFreeTextSizeToggle: PropTypes.func,\n  onPropertyChange: PropTypes.func,\n  onRichTextStyleChange: PropTypes.func,\n  isRedaction: PropTypes.bool,\n  isRichTextEditMode: PropTypes.bool,\n  setIsRichTextEditMode: PropTypes.func,\n  isWidget: PropTypes.bool,\n};\n\nconst RichTextStyleEditor = ({\n  annotation, editor,\n  style,\n  isFreeTextAutoSize,\n  onFreeTextSizeToggle,\n  onPropertyChange,\n  onRichTextStyleChange,\n  isRichTextEditMode,\n  setIsRichTextEditMode,\n  isRedaction,\n  isWidget,\n  activeTool,\n}) => {\n  const [\n    fonts,\n  ] = useSelector(\n    (state) => [\n      selectors.getFonts(state),\n    ],\n    shallowEqual,\n  );\n\n  const [format, setFormat] = useState({});\n  const editorRef = useRef(null);\n  const annotationRef = useRef(null);\n  const propertiesRef = useRef({});\n  const dispatch = useDispatch();\n  const oldSelectionRef = useRef();\n  const richTextEditModeRef = useRef();\n  richTextEditModeRef.current = isRichTextEditMode;\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const handleSelectionChange = (range, oldRange) => {\n      const shouldRestoreLostSelection = !range && oldRange && editorRef.current;\n      if (shouldRestoreLostSelection) {\n        editorRef.current.setSelection(oldRange.index, oldRange.length);\n      }\n      if (range && editorRef.current) {\n        setFormat(getFormat(range));\n      }\n    };\n    const handleTextChange = () => {\n      setFormat(getFormat(editorRef.current?.getSelection()));\n    };\n    core.addEventListener('editorSelectionChanged', handleSelectionChange);\n    core.addEventListener('editorTextChanged', handleTextChange);\n    // Have to disable instead of closing because annotation popup will reopen itself\n    dispatch(actions.disableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    return () => {\n      core.removeEventListener('editorSelectionChanged', handleSelectionChange);\n      core.removeEventListener('editorTextChanged', handleTextChange);\n      dispatch(actions.enableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    };\n  }, []);\n\n  useEffect(() => {\n    editorRef.current = editor;\n    annotationRef.current = annotation;\n    if (isRichTextEditMode && annotation) {\n      let StrokeStyle = 'solid';\n      try {\n        StrokeStyle = (annotation['Style'] === 'dash')\n          ? `${annotation['Style']},${annotation['Dashes']}`\n          : annotation['Style'];\n      } catch (err) {\n        console.error(err);\n      }\n      const richTextStyles = annotation.getRichTextStyle();\n      const stylesTemp = richTextStyles[0];\n\n      propertiesRef.current = {\n        Font: annotation.Font,\n        FontSize: annotation.FontSize,\n        TextAlign: annotation.TextAlign,\n        TextVerticalAlign: annotation.TextVerticalAlign,\n        bold: stylesTemp?.['font-weight'] === 'bold' ?? false,\n        italic: stylesTemp?.['font-style'] === 'italic' ?? false,\n        underline: stylesTemp?.['text-decoration']?.includes('underline')\n          || stylesTemp?.['text-decoration']?.includes('word'),\n        strikeout: stylesTemp?.['text-decoration']?.includes('line-through') ?? false,\n        size: stylesTemp?.['font-size'],\n        font: stylesTemp?.['font-family'],\n        StrokeStyle,\n        calculatedFontSize: annotation.getCalculatedFontSize()\n      };\n    }\n\n    setFormat(getFormat(editorRef.current?.getSelection()));\n\n    if (oldSelectionRef.current) {\n      editorRef.current.setSelection(oldSelectionRef.current);\n      oldSelectionRef.current = null;\n    }\n  }, [annotation, editor, isRichTextEditMode]);\n\n  useEffect(() => {\n    const handleEditorBlur = () => {\n      editorRef.current = null;\n      annotationRef.current = null;\n      setIsRichTextEditMode(false);\n    };\n    const handleEditorFocus = () => {\n      setIsRichTextEditMode(true);\n    };\n\n    core.addEventListener('editorBlur', handleEditorBlur);\n    core.addEventListener('editorFocus', handleEditorFocus);\n    return () => {\n      core.removeEventListener('editorBlur', handleEditorBlur);\n      core.removeEventListener('editorFocus', handleEditorFocus);\n    };\n  }, [dispatch]);\n\n\n  const getFormat = (range) => {\n    if (!range) {\n      return {};\n    }\n\n    const format = editorRef.current.getFormat(range.index, range.length);\n\n    if (typeof format.color === 'string') {\n      format.color = new window.Core.Annotations.Color(format.color);\n    } else if (Array.isArray(format.color)) {\n      // the selection contains multiple color, so we set the current color to the last selected color\n      const lastSelectedColor = new window.Core.Annotations.Color(format.color[format.color.length - 1]);\n      format.color = lastSelectedColor;\n    } else if (!format.color) {\n      format.color = annotationRef.current.TextColor;\n    }\n\n    const propertiesToCheck = ['font', 'size', 'originalSize'];\n\n    for (const prop of propertiesToCheck) {\n      if (format[prop] && Array.isArray(format[prop])) {\n        format[prop] = undefined;\n      }\n    }\n\n    return format;\n  };\n\n  const handleTextFormatChange = (format) => () => {\n    let { index, length } = editorRef.current.getSelection();\n    if (length === 0) {\n      oldSelectionRef.current = { index, length };\n      const newSelection = editorRef.current.getSelection();\n      index = newSelection.index;\n      length = newSelection.length;\n    }\n    const currentFormat = editorRef.current.getFormat(index, length);\n\n    applyFormat(format, !currentFormat[format]);\n  };\n\n  const handleColorChange = (name, color) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(name, color);\n      return;\n    }\n    applyFormat('color', color.toHexString());\n  };\n\n  const applyFormat = (formatKey, value) => {\n    if (formatKey === 'size') {\n      editorRef.current?.format('applyCustomFontSize', value);\n    } else {\n      editorRef.current?.format(formatKey, value);\n    }\n\n    if (formatKey === 'color') {\n      value = new window.Core.Annotations.Color(value);\n    }\n\n    // format the entire editor doesn't trigger the editorTextChanged event, so we set the format state here\n    setFormat({\n      ...format,\n      [formatKey]: value\n    });\n  };\n\n  // onPropertyChange\n  const handlePropertyChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(property, value);\n      return;\n    }\n\n    const { index, length } = editorRef.current.getSelection();\n    const annotation = annotationRef.current;\n    annotation[property] = value;\n    editorRef.current.blur();\n    if (property === 'FontSize' || property === 'Font') {\n      adjustFreeTextBoundingBox(annotation);\n    }\n    // Needs this setTimeout since blur has a slight delay\n    setTimeout(() => {\n      oldSelectionRef.current = { index, length };\n      const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n      editBoxManager.focusBox(annotation);\n    }, 0);\n  };\n\n\n  // onRichTextStyleChange\n  const handleRichTextStyleChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onRichTextStyleChange(property, value);\n      return;\n    }\n\n    const propertyTranslation = {\n      'font-weight': 'bold',\n      'font-style': 'italic',\n      'underline': 'underline',\n      'line-through': 'strike',\n      'font-family': 'font',\n      'font-size': 'size',\n    };\n    if (property === 'font-family' || property === 'font-size') {\n      applyFormat(propertyTranslation[property], value);\n      const freeText = annotationRef.current;\n      if (freeText.isAutoSized()) {\n        const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n        editBoxManager.resizeAnnotation(freeText);\n      }\n    } else {\n      handleTextFormatChange(propertyTranslation[property])();\n    }\n  };\n\n  let properties = {};\n\n  const { RichTextStyle } = style;\n  const defaults = {\n    bold: RichTextStyle?.[0]?.['font-weight'] === 'bold' ?? false,\n    italic: RichTextStyle?.[0]?.['font-style'] === 'italic' ?? false,\n    underline: RichTextStyle?.[0]?.['text-decoration']?.includes('underline') || RichTextStyle?.[0]?.['text-decoration']?.includes('word'),\n    strikeout: RichTextStyle?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n    font: RichTextStyle?.[0]?.['font-family'],\n    size: RichTextStyle?.[0]?.['font-size'],\n    StrokeStyle: 'solid',\n  };\n\n  properties = {\n    ...style,\n    ...defaults,\n  };\n\n  if (isRichTextEditMode && annotation) {\n    propertiesRef.current.bold = format.bold;\n    propertiesRef.current.italic = format.italic;\n    propertiesRef.current.underline = format.underline;\n    propertiesRef.current.strikeout = format.strike;\n    propertiesRef.current.quillFont = format.font || propertiesRef.current.Font;\n    propertiesRef.current.quillFontSize = format.originalSize || propertiesRef.current.FontSize;\n  }\n\n  const commonProps = {\n    fonts: fonts,\n    onPropertyChange: handlePropertyChange,\n    properties: properties,\n    stateless: true,\n    isFreeText: !isRedaction,\n  };\n\n  const nonWidgetProps = {\n    onRichTextStyleChange: handleRichTextStyleChange,\n    properties: isRichTextEditMode ? propertiesRef.current : properties,\n    isFreeTextAutoSize: isFreeTextAutoSize,\n    isRichTextEditMode: isRichTextEditMode,\n    isRedaction: isRedaction,\n    onFreeTextSizeToggle: onFreeTextSizeToggle,\n  };\n\n  const widgetProps = {\n    onRichTextStyleChange: handlePropertyChange,\n    isFreeTextAutoSize: false,\n    isRichTextEditMode: false,\n    isRedaction: false,\n    isWidget: isWidget,\n  };\n\n  return (\n    <div className=\"RichTextStyleEditor\"\n      onMouseDown={(e) => {\n        if (e.type !== 'touchstart' && isRichTextEditMode) {\n          e.preventDefault();\n        }\n      }}\n    >\n      <div className=\"menu-items\">\n        <TextStylePicker\n          {...commonProps}\n          {...(isWidget ? widgetProps : nonWidgetProps)}\n        />\n      </div>\n      <ColorPicker\n        onColorChange={(color) => {\n          handleColorChange('TextColor', new window.Core.Annotations.Color(color));\n        }}\n        color={isRichTextEditMode ? format.color : style['TextColor']}\n        activeTool={activeTool}\n        type={'Text'}\n        ariaTypeLabel={t('option.stylePopup.textStyle')}\n      />\n    </div>\n  );\n};\nRichTextStyleEditor.propTypes = propTypes;\n\nexport default React.memo(RichTextStyleEditor);\n", "import RichTextStyleEditor from './RichTextStyleEditor';\n\nexport default RichTextStyleEditor;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./StylePicker.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker{display:flex;flex-direction:column}.StylePicker .slider-property{font-size:14px;font-weight:700;margin-bottom:8px!important}.StylePicker .StyleOption{margin-bottom:16px}.StylePicker .StyleOption .styles-container .styles-title{margin:0 0 8px;font-size:14px;font-weight:700}.StylePicker .StyleOption .styles-container [data-element=borderStylePicker]{margin-top:8px}.StylePicker .StyleOption .slider:only-child{margin-bottom:0}.StylePicker .StyleOption .slider .slider-element-container{margin-left:-3px}.StylePicker .StyleOption:last-child{margin-bottom:0}.StylePicker .PanelSection~.PanelSection .CollapsibleSection>.collapsible-page-group-header{margin-top:16px}.StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px;padding:0;font-weight:700;height:31.5px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}.StylePicker .PanelSection .CollapsibleSection:first-of-type{margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}.StylePicker .PanelSection .panel-section-wrapper.Opacity{margin-top:16px}.StylePicker .PanelSection:first-child .panel-section-wrapper.Opacity{margin-top:0}.StylePicker .PanelSection .collapsible-page-group-header+.collapsible-content{margin-top:16px}.StylePicker .PanelSection .PanelSubsection{margin-bottom:12px}.StylePicker .PanelSection .PanelSubsection .menu-subtitle{font-size:14px;font-weight:700;margin-bottom:12px}.StylePicker .PanelSection .divider{background-color:var(--divider);width:100%;height:1px}.StylePicker .PanelSection .menu-items{margin-bottom:16px}.StylePicker .PanelSection .menu-items:only-child{margin-bottom:0}.StylePicker .PanelSection .menu-items .ColorPalette{margin-bottom:8px}.StylePicker .PanelSection .slider{margin-bottom:16px}.StylePicker .PanelSection .slider:last-child,.StylePicker .PanelSection:empty,.StylePicker .PanelSection:last-child{margin-bottom:0}.StylePicker .PanelSection .snapping-option{margin-top:16px}.StylePicker .spacer{width:100%}.StylePicker .Dropdown,.StylePicker .FontSizeDropdown,.StylePicker .overlay-text-input{height:32px}.StylePicker .overlay-text-input:focus{border-color:var(--blue-5)}.StylePicker .lineStyleContainer{margin-bottom:0!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle{display:flex;flex-direction:row;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.StylePicker .lineStyleContainer .StylePicker-LineStyle div.Dropdown{width:100%!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items,.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__wrapper{width:100%}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image svg{width:35px;margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image.shift-alignment svg{margin-top:8px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items .linestyle-image svg{width:45px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment{padding-top:0}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment svg{margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .Dropdown__items{top:-197px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .StyleOptions{max-width:80px}.StylePicker .lineStyleContainer .StylePicker-LineStyle>*{flex-grow:1;flex-basis:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker{padding:0 16px 16px;overflow:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker{padding:0 16px 16px;overflow:auto}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./StylePanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePanel{display:flex;flex-direction:column;background-color:var(--panel-background);padding-bottom:75px}.StylePanel .style-panel-header{font-size:16px;font-weight:700;margin-top:0;margin-bottom:16px}.StylePanel .label{padding-top:16px;font-size:14px;font-weight:700}.StylePanel .no-tool-selected{padding-top:36px;display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.StylePanel .no-tool-selected .msg{padding-top:24px;font-size:13px;text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}.StylePanel .no-tool-selected .empty-icon,.StylePanel .no-tool-selected .empty-icon svg{width:55px;height:56px}.StylePanel .no-tool-selected .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .style-panel-header{margin:0 16px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .style-panel-header{margin:0 16px 16px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import SnapModeToggle from './SnapModeToggle';\n\nexport default SnapModeToggle;", "import React from 'react';\nimport { workerTypes } from 'constants/types';\nimport core from 'core';\nimport i18next from 'i18next';\nimport Choice from 'components/Choice/Choice';\nimport getMeasurementTools from 'helpers/getMeasurementTools';\nimport actions from 'actions';\nimport { useDispatch } from 'react-redux';\n\nconst SnapModeToggle = ({\n  Scale,\n  Precision,\n  isSnapModeEnabled,\n}) => {\n  const dispatch = useDispatch();\n\n  const wasDocumentSwappedToClientSide =\n    core.getDocument()?.getType() === workerTypes.WEBVIEWER_SERVER && core.getDocument().isWebViewerServerDocument();\n  const isEligibleDocumentForSnapping = core.getDocument()?.getType().toLowerCase() === workerTypes.PDF || wasDocumentSwappedToClientSide;\n  const showMeasurementSnappingOption = Scale && Precision && isEligibleDocumentForSnapping && core.isFullPDFEnabled();\n\n  const onSnappingChange = (event) => {\n    if (!core.isFullPDFEnabled()) {\n      return;\n    }\n\n    const enableSnapping = event.target.checked;\n    const mode = enableSnapping\n      ? core.getDocumentViewer().SnapMode.e_DefaultSnapMode | core.getDocumentViewer().SnapMode.POINT_ON_LINE\n      : null;\n    const measurementTools = getMeasurementTools();\n\n    measurementTools.forEach((tool) => {\n      tool.setSnapMode?.(mode);\n      dispatch(actions.setEnableSnapMode({ toolName: tool.name, isEnabled: enableSnapping }));\n    });\n\n  };\n\n  return (\n    <>\n      {showMeasurementSnappingOption && (\n        <div className=\"snapping-option\">\n          <Choice\n            dataElement=\"measurementSnappingOption\"\n            id=\"measurement-snapping\"\n            type=\"checkbox\"\n            label={i18next.t('option.shared.enableSnapping')}\n            checked={isSnapModeEnabled}\n            onChange={onSnappingChange}\n          />\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default SnapModeToggle;", "import core from 'core';\nimport { useSelector, useDispatch } from 'react-redux';\nimport DataElements from 'constants/dataElement';\nimport selectors from 'selectors';\nimport actions from 'actions';\n\nconst { Tools, Annotations } = window.Core;\nconst { ToolNames } = Tools;\n\nexport const shouldHideSharedStyleOptions = (toolName) => {\n  const toolsWithNoSharedStyles = [\n    Tools.RedactionCreateTool,\n  ];\n\n  return toolsWithNoSharedStyles.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStrokeDropdowns = (toolName) => {\n  const toolsWithNoStrokeDropdowns = [\n    Tools.CountMeasurementCreateTool,\n    Tools.FreeHandCreateTool,\n    Tools.FreeHandHighlightCreateTool,\n    Tools.ArcCreateTool,\n    Tools.ArcMeasurementCreateTool,\n    Tools.TextAnnotationCreateTool,\n  ];\n\n  return toolsWithNoStrokeDropdowns.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStylePanelOptions = (toolName) => {\n  const toolsNoStylePanelOptions = [\n    Tools.AddParagraphTool,\n    Tools.AddImageContentTool,\n    Tools.CropCreateTool,\n    Tools.SnippingCreateTool,\n  ];\n\n  return toolsNoStylePanelOptions.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const hasFillColorAndCollapsablePanelSections = (toolName) => {\n  const toolsWithCollapsedStylePanels = [\n    Tools.RectangleCreateTool,\n    Tools.EllipseCreateTool,\n    Tools.PolygonCreateTool,\n    Tools.PolygonCloudCreateTool,\n    Tools.EllipseMeasurementCreateTool,\n    Tools.AreaMeasurementCreateTool,\n    Tools.FreeTextCreateTool,\n    Tools.CalloutCreateTool,\n    Tools.RedactionCreateTool,\n    // ... form builder\n    Tools.TextFormFieldCreateTool,\n    Tools.RadioButtonFormFieldCreateTool,\n    Tools.CheckBoxFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool\n  ];\n\n  return toolsWithCollapsedStylePanels.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideFillColorAndCollapsablePanelSections = (toolName) => {\n  const toolsWithHiddenFillColorSections = [\n    Tools.RubberStampCreateTool,\n    Tools.StampCreateTool,\n    Tools.EraserTool,\n  ];\n  return toolsWithHiddenFillColorSections.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStrokeSlider = (toolName) => {\n  const toolsWithHiddenStrokeSlider = [\n    Tools.TextUnderlineCreateTool,\n    Tools.TextHighlightCreateTool,\n    Tools.TextSquigglyCreateTool,\n    Tools.TextStrikeoutCreateTool,\n    Tools.CountMeasurementCreateTool,\n    Tools.RubberStampCreateTool,\n    Tools.FileAttachmentCreateTool,\n    Tools.StampCreateTool,\n    Tools.StickyCreateTool,\n    Tools.MarkInsertTextCreateTool,\n    Tools.MarkReplaceTextCreateTool,\n  ];\n  return toolsWithHiddenStrokeSlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideStrokeStyle = (toolName) => {\n  const toolsWithHiddenStrokeStyle = [\n    Tools.RubberStampCreateTool,\n    Tools.StampCreateTool,\n    Tools.EraserTool,\n  ];\n  return toolsWithHiddenStrokeStyle.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideCloudyLineStyle = (toolName) => {\n  const toolsWithHiddenCloudyLineStyle = [\n    Tools.EllipseCreateTool,\n    Tools.LineCreateTool,\n  ];\n  return toolsWithHiddenCloudyLineStyle.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldShowTextStyle = (toolName) => {\n  const toolsWithHiddenStrokeSlider = [\n    Tools.FreeTextCreateTool,\n    Tools.CalloutCreateTool,\n    Tools.RedactionCreateTool,\n    Tools.TextFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool,\n  ];\n  return toolsWithHiddenStrokeSlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const shouldHideOpacitySlider = (toolName) => {\n  const toolsWithHiddenOpacitySlider = [\n    Tools.RedactionCreateTool,\n    Tools.EraserTool,\n    Tools.TextFormFieldCreateTool,\n    Tools.ListBoxFormFieldCreateTool,\n    Tools.ComboBoxFormFieldCreateTool,\n    Tools.SignatureFormFieldCreateTool,\n    Tools.CheckBoxFormFieldCreateTool,\n    Tools.RadioButtonFormFieldCreateTool\n  ];\n  return toolsWithHiddenOpacitySlider.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const hasSnapModeCheckbox = (toolName) => {\n  const toolsWithSnapModeCheckbox = [\n    Tools.DistanceMeasurementCreateTool,\n    Tools.ArcMeasurementCreateTool,\n    Tools.PerimeterMeasurementCreateTool,\n    Tools.AreaMeasurementCreateTool,\n    Tools.RectangularAreaMeasurementCreateTool,\n    Tools.CloudyRectangularAreaMeasurementCreateTool,\n  ];\n  return toolsWithSnapModeCheckbox.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const extractUniqueFontFamilies = (jsonData, inputText) => {\n  const uniqueFontFamilies = new Set();\n  const uniqueFontSizes = new Set();\n\n  for (const key in jsonData) {\n    if (jsonData.hasOwnProperty(key)) {\n      const index = parseInt(key, 10);\n      if (!isNaN(index) && inputText[index] !== ' ' && jsonData[key]['font-family']) {\n        uniqueFontFamilies.add(jsonData[key]['font-family'].trim());\n      }\n      if (!isNaN(index) && inputText[index] !== ' ' && jsonData[key]['font-size']) {\n        uniqueFontSizes.add(jsonData[key]['font-size'].trim());\n      }\n    }\n  }\n\n  return {\n    fonts: Array.from(uniqueFontFamilies),\n    sizes: Array.from(uniqueFontSizes),\n  };\n};\n\nexport const shouldHideTransparentFillColor = (toolName) => {\n  const toolsWithHiddenTransparentColor = [Tools.RedactionCreateTool];\n  return toolsWithHiddenTransparentColor.some((tool) => core.getTool(toolName) instanceof tool);\n};\n\nexport const stylePanelSectionTitles = (toolName, section) => {\n  const toolTitles = {\n    'AnnotationCreateRedaction': {\n      'Title': 'component.redaction',\n      'StrokeColor': 'stylePanel.headings.redactionMarkOutline',\n      'FillColor': 'stylePanel.headings.redactionFill',\n    },\n  };\n  return toolTitles[toolName] && toolTitles[toolName][section];\n};\n\nexport const shouldRenderWidgetLayout = (toolName) => {\n  const toolsWithHiddenTextStylePicker = [\n    ToolNames.TEXT_FORM_FIELD,\n    ToolNames.LIST_BOX_FIELD,\n    ToolNames.COMBO_BOX_FIELD,\n  ];\n  return toolsWithHiddenTextStylePicker.includes(toolName);\n};\n\nexport const isInstanceOfAny = (annotation, types) => {\n  return types.some((type) => annotation instanceof type);\n};\n\nexport const shouldShowNoStyles = (annotations, filteredTypes) => {\n  return annotations.length === 1 && isInstanceOfAny(annotations[0], filteredTypes);\n};\n\nexport const getAnnotationTypes = (selectedAnnotations) => {\n  return selectedAnnotations.length >= 1\n    ? Array.from(new Set(selectedAnnotations.map((annotation) => annotation.ToolName)))\n    : null;\n};\n\nexport const parseToolType = (selectedAnnotations, currentTool) => {\n  const annotationTypes = getAnnotationTypes(selectedAnnotations);\n  const toolName = annotationTypes?.length > 0 ? annotationTypes[0] : currentTool.name;\n\n  const isRedaction =\n    annotationTypes?.length === 1 && annotationTypes[0] === ToolNames.REDACTION ||\n    toolName === ToolNames.REDACTION;\n  const isStamp = annotationTypes?.includes(ToolNames.STAMP) || toolName === ToolNames.STAMP;\n  const isWidget = selectedAnnotations.some((annotation) => annotation instanceof Annotations.WidgetAnnotation) || shouldRenderWidgetLayout(currentTool.name);\n  const isInFormFieldCreationMode = core.getFormFieldCreationManager().isInFormFieldCreationMode();\n  const isFreeText = toolName === ToolNames.FREE_TEXT;\n\n  return {\n    toolName,\n    isRedaction,\n    isStamp,\n    isWidget,\n    isInFormFieldCreationMode,\n    isFreeText,\n    activeTool: toolName,\n    annotationTypes,\n  };\n};\n\nexport const useStylePanelSections = () => {\n  const dispatch = useDispatch();\n  const isSnapModeEnabled = useSelector(selectors.isSnapModeEnabled);\n  const isStyleOptionDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.STYLE_OPTION));\n  const isStrokeStyleContainerActive = useSelector((state) => selectors.isElementOpen(state, DataElements.STROKE_STYLE_CONTAINER));\n  const isFillColorContainerActive = useSelector((state) => selectors.isElementOpen(state, DataElements.FILL_COLOR_CONTAINER));\n  const isOpacityContainerActive = useSelector((state) => selectors.isElementOpen(state, DataElements.OPACITY_CONTAINER));\n  const isTextStyleContainerActive = useSelector((state) => selectors.isElementOpen(state, DataElements.RICH_TEXT_STYLE_CONTAINER));\n\n  const panelItems = {\n    [DataElements.STROKE_STYLE_CONTAINER]: isStrokeStyleContainerActive,\n    [DataElements.FILL_COLOR_CONTAINER]: isFillColorContainerActive,\n    [DataElements.OPACITY_CONTAINER]: isOpacityContainerActive,\n    [DataElements.RICH_TEXT_STYLE_CONTAINER]: isTextStyleContainerActive,\n  };\n\n  const togglePanelItem = (dataElement) => {\n    if (!panelItems[dataElement]) {\n      dispatch(actions.openElement(dataElement));\n    } else {\n      dispatch(actions.closeElement(dataElement));\n    }\n  };\n  const openTextStyleContainer = () => {\n    dispatch(actions.openElements(DataElements.RICH_TEXT_EDITOR));\n    togglePanelItem(DataElements.RICH_TEXT_STYLE_CONTAINER);\n  };\n  const openStrokeStyleContainer = () => togglePanelItem(DataElements.STROKE_STYLE_CONTAINER);\n  const openFillColorContainer = () => togglePanelItem(DataElements.FILL_COLOR_CONTAINER);\n  const openOpacityContainer = () => togglePanelItem(DataElements.OPACITY_CONTAINER);\n\n  return {\n    isSnapModeEnabled,\n    isStyleOptionDisabled,\n    isStrokeStyleContainerActive,\n    isFillColorContainerActive,\n    isOpacityContainerActive,\n    isTextStyleContainerActive,\n    openTextStyleContainer,\n    openStrokeStyleContainer,\n    openFillColorContainer,\n    openOpacityContainer,\n  };\n};\n", "import { useTranslation } from 'react-i18next';\nimport React from 'react';\nimport ColorPicker from '../ColorPicker';\nimport Dropdown from '../../Dropdown';\nimport CollapsibleSection from 'src/components/CollapsibleSection';\nimport PropTypes from 'prop-types';\nimport {\n  defaultStartLineStyles,\n  defaultStrokeStyles,\n  defaultEndLineStyles,\n  cloudyStrokeStyle,\n} from 'constants/strokeStyleIcons';\nimport { stylePanelSectionTitles } from 'helpers/stylePanelHelper';\n\nconst withCloudyStyle = defaultStrokeStyles.concat(cloudyStrokeStyle);\n\nconst StrokePanelSection = ({\n  showFillColorAndCollapsablePanelSections,\n  isStamp,\n  onStrokeColorChange,\n  onStyleChange,\n  strokeColor,\n  activeTool,\n  hideStrokeDropdowns,\n  hideStrokeSlider,\n  strokethicknessComponent,\n  showLineStyleOptions,\n  renderSlider,\n  strokeStyle,\n  isInFormFieldCreationMode,\n  isFreeText,\n  onStartLineStyleChange,\n  startingLineStyle,\n  isStyleOptionDisabled,\n  onStrokeStyleChange,\n  strokeLineStyle,\n  onEndLineStyleChange,\n  endingLineStyle,\n  openStrokeStyleContainer,\n  isStrokeStyleContainerActive,\n  hideCloudyLineStyle,\n}) => {\n  const [t] = useTranslation();\n\n  const middleLineSegmentLabel = showLineStyleOptions ? 'stylePanel.lineEnding.middle' : 'stylePanel.borderStyle';\n\n  const sectionContent = (\n    <div className=\"panel-section-wrapper\">\n      {!isStamp && (\n        <>\n          <div className=\"menu-items\">\n            <ColorPicker onColorChange={onStrokeColorChange} onStyleChange={onStyleChange} color={strokeColor}\n              activeTool={activeTool} type={'Stroke'} ariaTypeLabel={t('option.annotationColor.StrokeColor')}/>\n          </div>\n          {!hideStrokeSlider && strokethicknessComponent && (strokethicknessComponent)}\n          {/*\n            When showLineStyleOptions is true, we want to show the opacity slider together with the stroke slider\n          */}\n          {showLineStyleOptions && <div className=\"StyleOption\">{renderSlider('opacity')}</div>}\n          {!!strokeStyle && !(isInFormFieldCreationMode && !isFreeText) && !hideStrokeDropdowns && (\n            <div className=\"StyleOption\">\n              <div className=\"styles-container lineStyleContainer\">\n                <div className=\"styles-title\">{t('option.styleOption.style')}</div>\n                <div className=\"StylePicker-LineStyle\">\n                  {showLineStyleOptions && (\n                    <Dropdown\n                      id=\"startLineStyleDropdown\"\n                      translationPrefix=\"stylePanel.lineEnding.start\"\n                      className=\"StylePicker-StartLineStyleDropdown\"\n                      dataElement=\"startLineStyleDropdown\"\n                      images={defaultStartLineStyles}\n                      onClickItem={onStartLineStyleChange}\n                      currentSelectionKey={startingLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                  {!isStyleOptionDisabled && (\n                    <Dropdown\n                      id=\"middleLineStyleDropdown\"\n                      translationPrefix={middleLineSegmentLabel}\n                      className={`StylePicker-StrokeLineStyleDropdown${!!strokeStyle && !showLineStyleOptions ? ' StyleOptions' : ''}`}\n                      dataElement=\"middleLineStyleDropdown\"\n                      images={showLineStyleOptions || hideCloudyLineStyle ? defaultStrokeStyles : withCloudyStyle}\n                      onClickItem={onStrokeStyleChange}\n                      currentSelectionKey={strokeLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                  {showLineStyleOptions && (\n                    <Dropdown\n                      id=\"endLineStyleDropdown\"\n                      translationPrefix=\"stylePanel.lineEnding.end\"\n                      className=\"StylePicker-EndLineStyleDropdown\"\n                      dataElement=\"endLineStyleDropdown\"\n                      images={defaultEndLineStyles}\n                      onClickItem={onEndLineStyleChange}\n                      currentSelectionKey={endingLineStyle}\n                      showLabelInList\n                    />\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n\n  if (!showFillColorAndCollapsablePanelSections) {\n    return sectionContent;\n  }\n\n  return (\n    <CollapsibleSection\n      header={t(stylePanelSectionTitles(activeTool, 'StrokeColor') || 'option.annotationColor.StrokeColor')}\n      headingLevel={2}\n      isInitiallyExpanded={false}\n      onToggle={openStrokeStyleContainer}\n      shouldShowHeading={showFillColorAndCollapsablePanelSections}\n      isExpanded={(isStrokeStyleContainerActive || !showFillColorAndCollapsablePanelSections)}>\n      { sectionContent }\n    </CollapsibleSection>\n  );\n};\n\nexport default StrokePanelSection;\n\nStrokePanelSection.propTypes = {\n  showFillColorAndCollapsablePanelSections: PropTypes.bool,\n  isStamp: PropTypes.bool,\n  onStrokeColorChange: PropTypes.func,\n  onStyleChange: PropTypes.func,\n  strokeColor: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),\n  activeTool: PropTypes.string,\n  hideStrokeDropdowns: PropTypes.bool,\n  hideStrokeSlider: PropTypes.bool,\n  strokethicknessComponent: PropTypes.node,\n  showLineStyleOptions: PropTypes.bool,\n  renderSlider: PropTypes.func,\n  strokeStyle: PropTypes.string,\n  isInFormFieldCreationMode: PropTypes.bool,\n  isFreeText: PropTypes.bool,\n  onStartLineStyleChange: PropTypes.func,\n  startingLineStyle: PropTypes.string,\n  isStyleOptionDisabled: PropTypes.bool,\n  onStrokeStyleChange: PropTypes.func,\n  strokeLineStyle: PropTypes.string,\n  onEndLineStyleChange: PropTypes.func,\n  endingLineStyle: PropTypes.string,\n  openStrokeStyleContainer: PropTypes.func,\n  isStrokeStyleContainerActive: PropTypes.bool,\n  hideCloudyLineStyle: PropTypes.bool,\n};", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport CollapsibleSection from 'src/components/CollapsibleSection';\nimport PropTypes from 'prop-types';\n\nconst OpacityPanelSection = ({\n  showFillColorAndCollapsablePanelSections,\n  shouldHideOpacitySlider,\n  showLineStyleOptions,\n  renderSlider,\n  isOpacityContainerActive,\n  openOpacityContainer,\n}) => {\n  const [t] = useTranslation();\n\n  OpacityPanelSection.propTypes = {\n    showFillColorAndCollapsablePanelSections: PropTypes.bool,\n    shouldHideOpacitySlider: PropTypes.bool,\n    showLineStyleOptions: PropTypes.bool,\n    renderSlider: PropTypes.func,\n    isOpacityContainerActive: PropTypes.bool,\n    openOpacityContainer: PropTypes.func,\n  };\n\n  const sectionContent = (\n    <div className=\"panel-section-wrapper Opacity\">\n      {/*\n        If showLineStyleOptions is true, then we don't want to show the opacity slider\n        in the bottom because it is already shown before together with the stroke slider\n      */}\n      {!showLineStyleOptions && !shouldHideOpacitySlider && (\n        <div className=\"StyleOption\">{renderSlider('opacity', showFillColorAndCollapsablePanelSections)}</div>\n      )}\n    </div>\n  );\n\n  if (!(showFillColorAndCollapsablePanelSections && !shouldHideOpacitySlider)) {\n    return sectionContent;\n  }\n\n  return (\n    <CollapsibleSection\n      header={t('option.slider.opacity')}\n      headingLevel={2}\n      isInitiallyExpanded={false}\n      isExpanded={(isOpacityContainerActive || !showFillColorAndCollapsablePanelSections)}\n      onToggle={openOpacityContainer}>\n      { sectionContent }\n    </CollapsibleSection>\n  );\n};\n\nexport default OpacityPanelSection;", "import OpacityPanelSection from './OpacityPanelSection';\n\nexport default OpacityPanelSection;", "import { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { getDataWithKey, mapToolNameToKey } from 'constants/map';\nimport getToolStyles from 'helpers/getToolStyles';\nimport setToolStyles from 'helpers/setToolStyles';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport handleFreeTextAutoSizeToggle from 'helpers/handleFreeTextAutoSizeToggle';\nimport getTextDecoration from 'helpers/getTextDecoration';\nimport { hexToRGBA } from 'helpers/color';\nimport { extractUniqueFontFamilies, stylePanelSectionTitles } from 'helpers/stylePanelHelper';\nimport { useTranslation } from 'react-i18next';\nimport defaultTool from 'constants/defaultTool';\nimport actions from 'actions';\n\nconst { Annotations } = window.Core;\n\nconst useStylePanel = ({ selectedAnnotations, currentTool }) => {\n  const [t] = useTranslation();\n  const [style, setStyle] = useState({\n    StrokeColor: null,\n    StrokeThickness: null,\n    Opacity: null,\n    FillColor: null,\n  });\n  const [panelTitle, setPanelTitle] = useState('');\n  const [strokeStyle, setStrokeStyle] = useState('');\n  const [startLineStyle, setStartLineStyle] = useState('');\n  const [endLineStyle, setEndLineStyle] = useState('');\n  const [isAutoSizeFont, setIsAutoSizeFont] = useState(false);\n  const [editorInstance, setEditorInstance] = useState(null);\n  const currentToolName = currentTool?.name;\n  const [showLineStyleOptions, setShowLineStyleOptions] = useState(currentToolName === defaultTool ? false : getDataWithKey(mapToolNameToKey(currentToolName)).hasLineEndings);\n  const colorProperties = ['StrokeColor', 'FillColor'];\n  const dispatch = useDispatch();\n\n\n  const [\n    toolButtonObject,\n    isAnnotationToolStyleSyncingEnabled,\n    activeDocumentViewerKey,\n  ] = useSelector((state) => [\n    selectors.getToolButtonObjects(state),\n    selectors.isAnnotationToolStyleSyncingEnabled(state),\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n\n  const selectedAnnotation = selectedAnnotations?.[0];\n\n  useEffect(() => {\n    updateSnapModeFromTool(currentTool);\n  }, [currentTool]);\n\n  const updateSnapModeFromTool = (currentTool) => {\n    if (!core.isFullPDFEnabled()) {\n      return;\n    }\n    if (currentTool && currentTool.getSnapMode) {\n      const isSnapModeEnabled = !!currentTool.getSnapMode();\n      dispatch(actions.setEnableSnapMode({ toolName: currentTool.name, isEnabled: isSnapModeEnabled }));\n    }\n  };\n\n  const setPanelTitleForSelectedTool = (tool) => {\n    const toolName = tool.name;\n    const title = toolButtonObject[toolName]?.title;\n    setPanelTitle(`${t(stylePanelSectionTitles(toolName, 'Title') || title)} ${t('stylePanel.headings.tool')}`);\n  };\n\n  const setPanelTitleForAnnotation = (annotation) => {\n    if (annotation.isContentEditPlaceholder()) {\n      setPanelTitle(`${t('stylePanel.headings.contentEdit')} ${t('stylePanel.headings.annotation')}`);\n      return;\n    }\n    setPanelTitle(`${t(stylePanelSectionTitles(annotation.ToolName, 'Title') || toolButtonObject[annotation.ToolName]?.title)} ${t('stylePanel.headings.annotation')}`);\n  };\n\n  const setPanelTitleForMultipleAnnotations = (annotations) => {\n    setPanelTitle(`${t('stylePanel.headings.annotations')} (${annotations.length})`);\n  };\n\n  const getStrokeStyle = (annotation) => {\n    const style = annotation['Style'];\n    const dashes = annotation['Dashes'];\n    return style !== 'dash' ? style : `${style},${dashes}`;\n  };\n\n  const applyFreeTextStyles = (annotation) => {\n    const extraStyles = {};\n\n    let StrokeStyle = 'solid';\n    try {\n      StrokeStyle = (annotation['Style'] === 'dash')\n        ? `${annotation['Style']},${annotation['Dashes']}`\n        : annotation['Style'];\n    } catch (err) {\n      console.error(err);\n    }\n\n    extraStyles.TextColor = annotation.TextColor;\n    extraStyles.RichTextStyle = annotation.getRichTextStyle();\n    extraStyles.Font = annotation.Font;\n    extraStyles.FontSize = annotation.FontSize;\n    extraStyles.TextAlign = annotation.TextAlign;\n    extraStyles.TextVerticalAlign = annotation.TextVerticalAlign;\n    extraStyles.calculatedFontSize = annotation.getCalculatedFontSize();\n    extraStyles.StrokeStyle = StrokeStyle;\n    extraStyles.isAutoSizeFont = annotation.isAutoSizeFont();\n    setIsAutoSizeFont(annotation.isAutoSizeFont());\n\n    const { fonts, sizes } = extractUniqueFontFamilies(extraStyles.RichTextStyle, annotation.getContents());\n    if (fonts.length >= 2 || (fonts.length === 1 && fonts[0] !== extraStyles.Font)) {\n      extraStyles.Font = undefined;\n    }\n    if (sizes.length >= 2 || (sizes.length === 1 && sizes[0] !== extraStyles.FontSize)) {\n      extraStyles.FontSize = undefined;\n    }\n\n    return extraStyles;\n  };\n\n  const updateStylePanelProps = (annotation) => {\n    let extraStyles = {};\n    if (annotation instanceof Annotations.FreeTextAnnotation) {\n      extraStyles = applyFreeTextStyles(annotation);\n    }\n\n    if (annotation instanceof Annotations.RedactionAnnotation) {\n      extraStyles['OverlayText'] = annotation.OverlayText;\n      extraStyles['Font'] = annotation.Font;\n      extraStyles['FontSize'] = annotation.FontSize;\n      extraStyles['TextAlign'] = annotation.TextAlign;\n    }\n\n    if (annotation instanceof Annotations.WidgetAnnotation && annotation.FontSize !== undefined) {\n      extraStyles.FontSize = annotation.FontSize;\n    }\n\n    setStyle({\n      ...style,\n      StrokeColor: annotation.StrokeColor ?? null,\n      StrokeThickness: annotation.StrokeThickness ?? null,\n      Opacity: annotation.Opacity ?? null,\n      FillColor: annotation.FillColor ?? null,\n      ...extraStyles,\n    });\n\n    setStartLineStyle(annotation.getStartStyle ? annotation.getStartStyle() : 'None');\n    setEndLineStyle(annotation.getEndStyle ? annotation.getEndStyle() : 'None');\n    setStrokeStyle(getStrokeStyle(annotation));\n  };\n\n  const updateFromTool = (tool) => {\n    const toolName = tool.name;\n    const styles = getToolStyles(toolName);\n\n    if (toolName.includes('FreeText') || toolName.includes('Callout')) {\n      styles.isAutoSizeFont = tool.defaults?.isAutoSizeFont;\n      setIsAutoSizeFont(styles.isAutoSizeFont);\n    }\n\n    setStyle(styles || {});\n    setStartLineStyle(styles?.StartLineStyle || '');\n    setEndLineStyle(styles?.EndLineStyle || '');\n    setStrokeStyle(styles?.StrokeStyle || '');\n    setPanelTitleForSelectedTool(tool);\n  };\n\n  useEffect(() => {\n    if (selectedAnnotation) {\n      if (selectedAnnotations.length > 1) {\n        setPanelTitleForMultipleAnnotations(selectedAnnotations);\n      } else {\n        setPanelTitleForAnnotation(selectedAnnotation);\n      }\n      updateStylePanelProps(selectedAnnotation);\n      const hasLineEndings = selectedAnnotations.every((annotation) => getDataWithKey(mapToolNameToKey(annotation.ToolName)).hasLineEndings);\n      setShowLineStyleOptions(hasLineEndings);\n    } else if (currentTool) {\n      updateFromTool(currentTool);\n      setShowLineStyleOptions(getDataWithKey(mapToolNameToKey(currentToolName)).hasLineEndings);\n    }\n  }, [selectedAnnotation, currentTool, selectedAnnotations]);\n\n  const onStyleChange = (property, value) => {\n    const newStyle = { ...style, [property]: value };\n    setStyle(newStyle);\n\n    if (selectedAnnotations.length === 0 && editorInstance && property === 'FillColor') {\n      const editor = editorInstance[0];\n      if (editor?.hasFocus()) {\n        const annotation = editorInstance[1];\n        editor.setStyle({ background: value });\n        annotation['FillColor'] = new Annotations.Color(value);\n        return;\n      }\n    }\n\n    if (selectedAnnotations.length > 0) {\n      selectedAnnotations.forEach((annotation) => {\n        if (colorProperties.includes(property)) {\n          const colorRGB = hexToRGBA(value);\n          const color = new Annotations.Color(colorRGB.r, colorRGB.g, colorRGB.b, colorRGB.a);\n          core.setAnnotationStyles(annotation, { [property]: color }, activeDocumentViewerKey);\n          if (isAnnotationToolStyleSyncingEnabled) {\n            setToolStyles(annotation.ToolName, property, color);\n          }\n        } else {\n          core.setAnnotationStyles(annotation, { [property]: value }, activeDocumentViewerKey);\n          if (annotation instanceof Annotations.FreeTextAnnotation && ['FontSize', 'Font', 'StrokeThickness'].includes(property)) {\n            adjustFreeTextBoundingBox(annotation);\n          }\n          if (isAnnotationToolStyleSyncingEnabled) {\n            setToolStyles(annotation.ToolName, property, value);\n          }\n        }\n        core.getAnnotationManager().redrawAnnotation(annotation);\n        if (annotation instanceof Annotations.WidgetAnnotation) {\n          annotation.refresh();\n        }\n      });\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        if (colorProperties.includes(property)) {\n          const colorRGB = hexToRGBA(value);\n          const color = new Annotations.Color(colorRGB.r, colorRGB.g, colorRGB.b, colorRGB.a);\n          setToolStyles(currentTool.name, property, color);\n        } else {\n          setToolStyles(currentTool.name, property, value);\n        }\n        if (currentTool instanceof window.Core.Tools.RubberStampCreateTool) {\n          currentTool.showPreview();\n        }\n      }\n    }\n  };\n\n  const onLineStyleChange = (section, value) => {\n    const sectionPropertyMap = {\n      start: 'StartLineStyle',\n      middle: 'StrokeStyle',\n      end: 'EndLineStyle',\n    };\n    if (section === 'start') {\n      setStartLineStyle(value);\n    }\n    if (section === 'middle') {\n      setStrokeStyle(value);\n    }\n    if (section === 'end') {\n      setEndLineStyle(value);\n    }\n\n    if (selectedAnnotations.length > 0) {\n      selectedAnnotations.forEach((annotation) => {\n        if (section === 'start') {\n          annotation.setStartStyle(value);\n        }\n        if (section === 'middle') {\n          const dashes = value.split(',');\n          annotation.Style = dashes.shift();\n          annotation.Dashes = dashes;\n        }\n        if (section === 'end') {\n          annotation.setEndStyle(value);\n        }\n        core.getAnnotationManager(activeDocumentViewerKey).redrawAnnotation(annotation);\n        if (isAnnotationToolStyleSyncingEnabled) {\n          setToolStyles(annotation.ToolName, sectionPropertyMap[section], value);\n        }\n      });\n      core.getAnnotationManager(activeDocumentViewerKey).trigger('annotationChanged', [selectedAnnotations, 'modify', {}]);\n    } else {\n      const currentTool = core.getToolMode();\n      if (currentTool) {\n        setToolStyles(currentTool.name, sectionPropertyMap[section], value);\n      }\n    }\n  };\n\n  const handleAutoSize = () => {\n    if (selectedAnnotations.length > 0) {\n      selectedAnnotations.forEach((annotation) => {\n        handleFreeTextAutoSizeToggle(annotation, setIsAutoSizeFont, isAutoSizeFont);\n      });\n    } else if (currentTool) {\n      setToolStyles(currentTool.name, 'isAutoSizeFont', !isAutoSizeFont);\n      setIsAutoSizeFont(!isAutoSizeFont);\n    }\n  };\n\n  const handleRichTextStyleChange = (property, value) => {\n    const richStyle = style.RichTextStyle?.[0] || {};\n    const newValue = ['underline', 'line-through'].includes(property)\n      ? getTextDecoration({ [property]: value }, richStyle)\n      : value;\n\n    const richTextStyle = {\n      0: {\n        ...richStyle,\n        [property === 'underline' || property === 'line-through' ? 'text-decoration' : property]: newValue,\n      },\n    };\n\n    if (selectedAnnotations.length > 0) {\n      selectedAnnotations.forEach((annotation) => {\n        core.updateAnnotationRichTextStyle(annotation, { [property]: value }, activeDocumentViewerKey);\n      });\n    } else if (currentTool) {\n      setToolStyles(currentTool.name, 'RichTextStyle', richTextStyle);\n    }\n\n    setStyle({ ...style, RichTextStyle: richTextStyle });\n  };\n\n  return {\n    panelTitle,\n    style,\n    strokeStyle,\n    startLineStyle,\n    endLineStyle,\n    onStyleChange,\n    onLineStyleChange,\n    isAutoSizeFont,\n    handleAutoSize,\n    handleRichTextStyleChange,\n    saveEditorInstance: setEditorInstance,\n    activeTool: currentTool?.name,\n    showLineStyleOptions,\n  };\n};\n\nexport default useStylePanel;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport useStylePanel from 'hooks/useStylePanel';\nimport Icon from 'src/components/Icon';\nimport { useTranslation } from 'react-i18next';\n\nconst NoSharedStylePanel = ({ currentTool, selectedAnnotations }) => {\n  const [t] = useTranslation();\n\n  const {\n    panelTitle,\n  } = useStylePanel({ currentTool, selectedAnnotations });\n\n  return (\n    <>\n      {panelTitle && (\n        <h2 className=\"style-panel-header\">{panelTitle}</h2>\n      )}\n      <div className=\"no-tool-selected\">\n        <div>\n          <Icon className=\"empty-icon\" glyph=\"style-panel-no-tool-selected\" />\n        </div>\n        <div className=\"msg\">{t('stylePanel.noSharedToolStyle')}</div>\n      </div>\n    </>\n  );\n};\n\nNoSharedStylePanel.propTypes = {\n  selectedAnnotations: PropTypes.arrayOf(PropTypes.object),\n  currentTool: PropTypes.object,\n};\n\nexport default NoSharedStylePanel;", "import React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useDispatch } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport './StylePicker.scss';\nimport ColorPicker from './ColorPicker';\nimport Slider from 'components/Slider';\nimport DataElements from 'constants/dataElement';\nimport { getStrokeSliderSteps, getStrokeDisplayValue } from 'constants/slider';\nimport SnapModeToggle from './SnapModeToggle';\nimport actions from 'actions';\nimport {\n  hasFillColorAndCollapsablePanelSections,\n  stylePanelSectionTitles,\n  shouldHideStrokeDropdowns,\n  shouldHideStrokeSlider,\n  shouldHideOpacitySlider,\n  hasSnapModeCheckbox,\n  shouldHideTransparentFillColor,\n  shouldHideStrokeStyle,\n  shouldHideFillColorAndCollapsablePanelSections,\n  useStylePanelSections,\n  shouldHideCloudyLineStyle,\n} from 'helpers/stylePanelHelper';\nimport CollapsibleSection from '../CollapsibleSection';\nimport StrokePanelSection from './StrokePanelSection/StrokePanelSection';\nimport OpacityPanelSection from './OpacityPanelSection';\nimport NoSharedStylePanel from '../StylePanel/panels/NoSharedStylePanel';\n\nconst propTypes = {\n  activeType: PropTypes.string,\n  endLineStyle: PropTypes.string,\n  isFreeText: PropTypes.bool,\n  isInFormFieldCreationMode: PropTypes.bool,\n  hasParentPicker: PropTypes.bool,\n  isRedaction: PropTypes.bool,\n  isStamp: PropTypes.bool,\n  isTextStylePickerHidden: PropTypes.bool,\n  onLineStyleChange: PropTypes.func,\n  onStyleChange: PropTypes.func.isRequired,\n  redactionLabelProperties: PropTypes.object,\n  showLineStyleOptions: PropTypes.bool,\n  sliderProperties: PropTypes.arrayOf(PropTypes.string),\n  startLineStyle: PropTypes.string,\n  strokeStyle: PropTypes.string,\n  style: PropTypes.object.isRequired,\n  toolName: PropTypes.string,\n  annotationTypes: PropTypes.arrayOf(PropTypes.string),\n};\n\nconst MAX_STROKE_THICKNESS = 23;\n\nconst StylePicker = ({\n  onStyleChange,\n  style,\n  isFreeText,\n  isRedaction,\n  showLineStyleOptions,\n  isStamp,\n  isInFormFieldCreationMode,\n  startLineStyle,\n  endLineStyle,\n  strokeStyle,\n  onLineStyleChange,\n  activeTool,\n  hasParentPicker,\n  annotationTypes,\n}) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const [strokeColor, setStrokeColor] = useState(style.StrokeColor);\n  const [startingLineStyle, setStartingLineStyle] = useState(startLineStyle);\n  const [endingLineStyle, setEndingLineStyle] = useState(endLineStyle);\n  const [strokeLineStyle, setStrokeLineStyle] = useState(strokeStyle);\n  const [fillColor, setFillColor] = useState(style.FillColor);\n\n  const checkAnyAnnotationTypes = (checkFunction) => {\n    if (annotationTypes && annotationTypes.length > 0) {\n      return annotationTypes.some(checkFunction);\n    }\n    return checkFunction(activeTool);\n  };\n\n  const checkAllAnnotationTypes = (checkFunction) => {\n    if (annotationTypes && annotationTypes.length > 0) {\n      return annotationTypes.every(checkFunction);\n    }\n    return checkFunction(activeTool);\n  };\n\n  const hideOpacitySlider = checkAnyAnnotationTypes(shouldHideOpacitySlider);\n  const hideStrokeStyle = checkAnyAnnotationTypes(shouldHideStrokeStyle);\n  const showFillColorAndCollapsablePanelSections = checkAllAnnotationTypes(hasFillColorAndCollapsablePanelSections);\n  const hideFillColorAndCollapsablePanelSections = checkAnyAnnotationTypes(shouldHideFillColorAndCollapsablePanelSections);\n  const hideStrokeDropdowns = checkAnyAnnotationTypes(shouldHideStrokeDropdowns);\n  const hideStrokeSlider = checkAnyAnnotationTypes(shouldHideStrokeSlider);\n  const showSnapModeCheckbox = hasSnapModeCheckbox(activeTool) && !annotationTypes;\n  const hideCloudyLineStyle = checkAnyAnnotationTypes(shouldHideCloudyLineStyle);\n\n  useEffect(() => {\n    if (showFillColorAndCollapsablePanelSections) {\n      dispatch(actions.openElement(DataElements.STROKE_STYLE_CONTAINER));\n    }\n  }, [activeTool]);\n\n  useEffect(() => {\n    setStrokeColor(style.StrokeColor);\n    setFillColor(style.FillColor);\n  }, [strokeColor, fillColor, style]);\n\n  useEffect(() => {\n    setStrokeColor(style.StrokeColor);\n    setStartingLineStyle(startLineStyle);\n    setStrokeLineStyle(strokeStyle);\n    setEndingLineStyle(endLineStyle);\n  }, [startLineStyle, endLineStyle, strokeStyle]);\n\n  const onStrokeColorChange = (color) => {\n    onStyleChange?.('StrokeColor', color);\n    setStrokeColor(color);\n  };\n\n  const onStartLineStyleChange = (style) => {\n    onLineStyleChange?.('start', style);\n    setStartingLineStyle(style);\n  };\n\n  const onStrokeStyleChange = (style) => {\n    onLineStyleChange?.('middle', style);\n    setStrokeLineStyle(style);\n  };\n\n  const onEndLineStyleChange = (style) => {\n    onLineStyleChange?.('end', style);\n    setEndingLineStyle(style);\n  };\n\n  const onFillColorChange = (color) => {\n    onStyleChange?.('FillColor', color);\n    setFillColor(color);\n  };\n\n  const onSliderChange = (property, value) => {\n    onStyleChange?.(property, value);\n  };\n\n  // We do not have sliders to show up for redaction annots\n  if (isRedaction) {\n    style.Opacity = null;\n    style.StrokeThickness = null;\n  }\n\n  const {\n    isSnapModeEnabled,\n    isStyleOptionDisabled,\n    isStrokeStyleContainerActive,\n    isFillColorContainerActive,\n    isOpacityContainerActive,\n    openStrokeStyleContainer,\n    openFillColorContainer,\n    openOpacityContainer,\n  } = useStylePanelSections();\n\n  const getSliderProps = (type) => {\n    const { Opacity, StrokeThickness, FontSize } = style;\n\n    switch (type.toLowerCase()) {\n      case 'opacity':\n        if (Opacity === null) {\n          return null;\n        }\n        return {\n          property: 'Opacity',\n          displayProperty: 'opacity',\n          value: Opacity * 100,\n          getDisplayValue: (Opacity) => `${Math.round(Opacity)}%`,\n          dataElement: DataElements.OPACITY_SLIDER,\n          withInputField: true,\n          inputFieldType: 'number',\n          min: 0,\n          max: 100,\n          step: 1,\n          getLocalValue: (opacity) => parseInt(opacity) / 100,\n        };\n      case 'strokethickness':\n        if (StrokeThickness === null) {\n          return null;\n        }\n        return {\n          property: 'StrokeThickness',\n          displayProperty: 'thickness',\n          value: StrokeThickness,\n          getDisplayValue: getStrokeDisplayValue,\n          dataElement: DataElements.STROKE_THICKNESS_SLIDER,\n          withInputField: true,\n          inputFieldType: 'number',\n          min: 0,\n          max: MAX_STROKE_THICKNESS,\n          step: 1,\n          steps: getStrokeSliderSteps(isFreeText),\n        };\n      case 'fontsize':\n        if (FontSize === null) {\n          return null;\n        }\n        return {\n          property: 'FontSize',\n          displayProperty: 'text',\n          value: FontSize,\n          getDisplayValue: (FontSize) => `${Math.round(parseInt(FontSize, 10))}pt`,\n          dataElement: DataElements.FONT_SIZE_SLIDER,\n          min: 5,\n          max: 45,\n          step: 1,\n          withInputField: true,\n          inputFieldType: 'number',\n          getLocalValue: (FontSize) => `${parseFloat(FontSize).toFixed(2)}pt`,\n        };\n    }\n  };\n\n  const renderSlider = (property, shouldHideSliderTitle) => {\n    const sliderProps = getSliderProps(property);\n    if (!sliderProps) {\n      return null;\n    }\n    return (\n      <Slider\n        key={property}\n        {...sliderProps}\n        onStyleChange={onSliderChange}\n        onSliderChange={onSliderChange}\n        shouldHideSliderTitle={shouldHideSliderTitle}\n        customCircleRadius={8}\n        customLineStrokeWidth={5}\n      />\n    );\n  };\n\n  const renderDivider = () => {\n    if (showFillColorAndCollapsablePanelSections) {\n      return <div className=\"divider\" />;\n    }\n  };\n\n  const strokethicknessComponent = renderSlider('strokethickness');\n\n  const parentProps = hasParentPicker ? {} : {\n    className: 'StylePicker',\n    onMouseDown: (e) => {\n      if (e.type !== 'touchstart' && e.target.tagName.toUpperCase() !== 'INPUT') {\n        e.preventDefault();\n      }\n    }\n  };\n\n  const allOptionsHidden = hideStrokeStyle && hideFillColorAndCollapsablePanelSections && hideOpacitySlider;\n\n  if (allOptionsHidden && annotationTypes?.length > 1) {\n    return <NoSharedStylePanel />;\n  }\n\n  return (\n    <ParentComponent parentProps={parentProps} hasParentPicker={hasParentPicker}>\n      {!hideStrokeStyle && (\n        <div className=\"PanelSection\">\n          <StrokePanelSection\n            showFillColorAndCollapsablePanelSections={showFillColorAndCollapsablePanelSections}\n            isStamp={isStamp}\n            onStrokeColorChange={onStrokeColorChange}\n            onStyleChange={onStyleChange}\n            strokeColor={strokeColor}\n            activeTool={activeTool}\n            hideStrokeDropdowns={hideStrokeDropdowns}\n            hideStrokeSlider={hideStrokeSlider}\n            strokethicknessComponent={strokethicknessComponent}\n            showLineStyleOptions={showLineStyleOptions}\n            renderSlider={renderSlider}\n            strokeStyle={strokeLineStyle}\n            isInFormFieldCreationMode={isInFormFieldCreationMode}\n            isFreeText={isFreeText}\n            onStartLineStyleChange={onStartLineStyleChange}\n            startingLineStyle={startingLineStyle}\n            isStyleOptionDisabled={isStyleOptionDisabled}\n            onStrokeStyleChange={onStrokeStyleChange}\n            strokeLineStyle={strokeLineStyle}\n            onEndLineStyleChange={onEndLineStyleChange}\n            endingLineStyle={endingLineStyle}\n            openStrokeStyleContainer={openStrokeStyleContainer}\n            isStrokeStyleContainerActive={isStrokeStyleContainerActive}\n            hideCloudyLineStyle={hideCloudyLineStyle}\n          />\n          {renderDivider()}\n        </div>\n      )}\n\n      {hideStrokeStyle && !hideStrokeSlider && strokethicknessComponent && (strokethicknessComponent)}\n      {showFillColorAndCollapsablePanelSections && !hideFillColorAndCollapsablePanelSections && (\n        <div className=\"PanelSection\">\n          <CollapsibleSection\n            header={t(stylePanelSectionTitles(activeTool, 'FillColor') || 'option.annotationColor.FillColor')}\n            headingLevel={2}\n            isInitiallyExpanded={false}\n            isExpanded={isFillColorContainerActive}\n            onToggle={openFillColorContainer}>\n            <div className=\"panel-section-wrapper\">\n              <div className=\"menu-items\">\n                <ColorPicker\n                  onColorChange={onFillColorChange}\n                  onStyleChange={onStyleChange}\n                  color={fillColor}\n                  hasTransparentColor={!shouldHideTransparentFillColor(activeTool)}\n                  activeTool={activeTool}\n                  type={'Fill'}\n                  ariaTypeLabel={t('option.annotationColor.FillColor')}\n                />\n              </div>\n            </div>\n          </CollapsibleSection>\n          {!hideOpacitySlider && renderDivider()}\n        </div>\n      )}\n\n      <div className=\"PanelSection\">\n        <OpacityPanelSection\n          showFillColorAndCollapsablePanelSections={showFillColorAndCollapsablePanelSections}\n          shouldHideOpacitySlider={hideOpacitySlider}\n          activeTool={activeTool}\n          showLineStyleOptions={showLineStyleOptions}\n          renderSlider={renderSlider}\n          isOpacityContainerActive={isOpacityContainerActive}\n          openOpacityContainer={openOpacityContainer}\n        />\n        {showSnapModeCheckbox && renderDivider()}\n      </div>\n\n      {showSnapModeCheckbox && (\n        <>\n          {/* to avoid inline styling when there's no divider */}\n          {!showFillColorAndCollapsablePanelSections && <div className=\"spacer\" />}\n          <div className=\"PanelSection\">\n            <SnapModeToggle Scale={style.Scale} Precision={style.Precision} isSnapModeEnabled={isSnapModeEnabled} />\n          </div>\n        </>\n      )}\n    </ParentComponent>\n  );\n};\n\nconst ParentComponent = ({ hasParentPicker, children, parentProps }) => {\n  if (hasParentPicker) {\n    return <>{children}</>;\n  }\n  return <div {...parentProps}>{children}</div>;\n};\n\nParentComponent.propTypes = {\n  hasParentPicker: PropTypes.bool,\n  children: PropTypes.any,\n  parentProps: PropTypes.object,\n};\n\nStylePicker.propTypes = propTypes;\n\nexport default StylePicker;", "import StylePicker from './StylePicker';\n\nexport default StylePicker;", "import React, { useEffect, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport StylePicker from 'components/StylePicker';\nimport useStylePanel from 'hooks/useStylePanel';\nimport {\n  parseToolType,\n  stylePanelSectionTitles,\n  useStylePanelSections,\n  hasFillColorAndCollapsablePanelSections\n} from 'helpers/stylePanelHelper';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport LabelTextEditor from 'components/LabelTextEditor';\nimport RichTextStyleEditor from 'components/RichTextStyleEditor';\nimport { useTranslation } from 'react-i18next';\nimport DataElements from 'constants/dataElement';\nimport { useDispatch } from 'react-redux';\nimport useOnFreeTextEdit from 'hooks/useOnFreeTextEdit';\nimport actions from 'actions';\n\nconst TextStylePanel = ({ selectedAnnotations, currentTool }) => {\n  const dispatch = useDispatch();\n  const { t } = useTranslation();\n  const [isRichTextEditMode, setIsRichTextEditMode] = useState(false);\n\n  useEffect(() => {\n    if (isRichTextEditMode) {\n      dispatch(actions.closeElement(DataElements.ANNOTATION_POPUP));\n    }\n  }, [isRichTextEditMode]);\n\n  const {\n    panelTitle,\n    style,\n    strokeStyle,\n    onStyleChange,\n    onLineStyleChange,\n    isAutoSizeFont,\n    handleAutoSize,\n    handleRichTextStyleChange,\n    saveEditorInstance,\n    showLineStyleOptions,\n    startLineStyle,\n    endLineStyle,\n  } = useStylePanel({ selectedAnnotations, currentTool });\n\n  const toolTypeProps = parseToolType(selectedAnnotations, currentTool);\n  const {\n    isRedaction,\n    isWidget,\n    activeTool,\n  } = toolTypeProps;\n\n  const {\n    openTextStyleContainer,\n    isTextStyleContainerActive,\n  } = useStylePanelSections();\n\n  const showFillColorAndCollapsablePanelSections = hasFillColorAndCollapsablePanelSections(activeTool);\n  useEffect(() => {\n    if (showFillColorAndCollapsablePanelSections) {\n      dispatch(actions.openElement(DataElements.RICH_TEXT_STYLE_CONTAINER));\n    }\n  }, [activeTool]);\n\n  const onOpenProps = useOnFreeTextEdit(saveEditorInstance);\n\n  return (\n    <>\n      <h2 className=\"style-panel-header\">{panelTitle}</h2>\n      <div className=\"StylePicker\"\n        onMouseDown={(e) => {\n          if (e.type !== 'touchstart' && e.target.tagName.toUpperCase() !== 'INPUT') {\n            e.preventDefault();\n          }\n        }}\n      >\n        <div className=\"PanelSection TextStyle\">\n          <CollapsibleSection\n            header={t(stylePanelSectionTitles(activeTool, 'OverlayText') || 'option.stylePopup.textStyle')}\n            headingLevel={2}\n            isInitiallyExpanded={false}\n            isExpanded={isTextStyleContainerActive}\n            onToggle={openTextStyleContainer}>\n            <div className=\"panel-section-wrapper\">\n              {isRedaction && (\n                <div className=\"PanelSubsection RedactionTextLabel\">\n                  <div className=\"menu-subtitle\">{t('stylePanel.headings.redactionTextLabel')}</div>\n                  <LabelTextEditor properties={style} onPropertyChange={onStyleChange} placeholderText={' '}/>\n                </div>\n              )}\n              <RichTextStyleEditor\n                style={style}\n                {...onOpenProps}\n                property={'TextColor'}\n                colorMapKey={'freeText'}\n                isFreeTextAutoSize={isAutoSizeFont}\n                onFreeTextSizeToggle={handleAutoSize}\n                onPropertyChange={onStyleChange}\n                onRichTextStyleChange={handleRichTextStyleChange}\n                isRichTextEditMode={isRichTextEditMode}\n                setIsRichTextEditMode={setIsRichTextEditMode}\n                isRedaction={isRedaction}\n                activeTool={activeTool}\n                isWidget={isWidget}\n              />\n            </div>\n          </CollapsibleSection>\n          <div className=\"divider\"/>\n        </div>\n        <StylePicker\n          {...toolTypeProps}\n          hasParentPicker\n          sliderProperties={['Opacity', 'StrokeThickness']}\n          style={style}\n          onStyleChange={onStyleChange}\n          strokeStyle={strokeStyle}\n          onLineStyleChange={onLineStyleChange}\n          activeTool={activeTool}\n          showLineStyleOptions={showLineStyleOptions}\n          startLineStyle={startLineStyle}\n          endLineStyle={endLineStyle}\n        />\n      </div>\n    </>\n  );\n};\n\nTextStylePanel.propTypes = {\n  selectedAnnotations: PropTypes.arrayOf(PropTypes.object),\n  currentTool: PropTypes.object,\n};\n\nexport default TextStylePanel;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport StylePicker from 'components/StylePicker';\nimport useStylePanel from 'hooks/useStylePanel';\nimport { shouldHideStylePanelOptions, parseToolType, } from 'helpers/stylePanelHelper';\nimport Icon from 'src/components/Icon';\nimport { useTranslation } from 'react-i18next';\n\nconst DefaultStylePanel = ({ currentTool, selectedAnnotations }) => {\n  const [t] = useTranslation();\n\n  const {\n    style,\n    panelTitle,\n    strokeStyle,\n    startLineStyle,\n    endLineStyle,\n    onStyleChange,\n    onLineStyleChange,\n    showLineStyleOptions,\n  } = useStylePanel({ currentTool, selectedAnnotations });\n\n  const toolTypeProps = parseToolType(selectedAnnotations, currentTool);\n  const { toolName } = toolTypeProps;\n\n  return (\n    <>\n      <h2 className=\"style-panel-header\">{panelTitle}</h2>\n      {shouldHideStylePanelOptions(toolName) ? (\n        <div className=\"no-tool-selected\">\n          <div>\n            <Icon className=\"empty-icon\" glyph=\"style-panel-no-tool-selected\" />\n          </div>\n          <div className=\"msg\">{t('stylePanel.noToolStyle')}</div>\n        </div>\n      ) : (\n        <StylePicker\n          {...toolTypeProps}\n          sliderProperties={['Opacity', 'StrokeThickness']}\n          style={style}\n          onStyleChange={onStyleChange}\n          showLineStyleOptions={showLineStyleOptions}\n          startLineStyle={startLineStyle}\n          endLineStyle={endLineStyle}\n          strokeStyle={strokeStyle}\n          onLineStyleChange={onLineStyleChange}\n        />\n      )}\n    </>\n  );\n};\n\nDefaultStylePanel.propTypes = {\n  selectedAnnotations: PropTypes.arrayOf(PropTypes.object),\n  currentTool: PropTypes.object,\n};\n\nexport default DefaultStylePanel;", "import TextStylePanel from './panels/TextStylePanel';\nimport DefaultStylePanel from './panels/DefaultStylePanel';\nimport NoSharedStylePanel from './panels/NoSharedStylePanel';\nimport { getAnnotationTypes, shouldShowTextStyle, shouldHideSharedStyleOptions } from 'helpers/stylePanelHelper';\n\nconst getPanelFromNames = (toolNames) => {\n  if (toolNames.length > 1 && toolNames.some((toolName) => shouldHideSharedStyleOptions(toolName))) {\n    return NoSharedStylePanel;\n  }\n  if (toolNames.every((toolName) => shouldShowTextStyle(toolName))) {\n    return TextStylePanel;\n  }\n  return DefaultStylePanel;\n};\n\nexport const getStylePanelComponent = (currentTool, selectedAnnotations) => {\n  const annotationTypes = getAnnotationTypes(selectedAnnotations);\n  const toolNames = annotationTypes?.length >= 1 ? annotationTypes : [currentTool.name];\n\n  return getPanelFromNames(toolNames);\n};\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\n\nconst NoToolStylePanel = () => {\n  const [t] = useTranslation();\n  return (\n    <>\n      <h2 className=\"style-panel-header\">{t('stylePanel.headings.styles')}</h2>\n      <div className=\"no-tool-selected\">\n        <div>\n          <Icon className=\"empty-icon\" glyph=\"style-panel-no-tool-selected\" />\n        </div>\n        <div className=\"msg\">{t('stylePanel.noToolSelected')}</div>\n      </div>\n    </>\n  );\n};\n\nexport default NoToolStylePanel;", "import React, { useEffect, useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { getStylePanelComponent } from './StylePanelFactory';\nimport DataElementWrapper from '../DataElementWrapper';\nimport NoToolStylePanel from './panels/NoToolStylePanel';\nimport getAnnotationCreateToolNames from 'helpers/getAnnotationCreateToolNames';\nimport { shouldShowNoStyles } from 'helpers/stylePanelHelper';\nimport debounce from 'lodash/debounce';\n\nimport './StylePanel.scss';\n\nconst { Annotations } = window.Core;\n\nconst StylePanelContainer = () => {\n  const isPanelOpen = useSelector((state) => selectors.isElementOpen(state, 'stylePanel'));\n  const annotationCreateToolNames = getAnnotationCreateToolNames();\n\n  const [selectedAnnotations, setSelectedAnnotations] = useState(core.getSelectedAnnotations());\n  const [currentTool, setCurrentTool] = useState(core.getToolMode());\n\n  const filteredTypes = [Annotations.PushButtonWidgetAnnotation];\n\n  const handleChange = debounce(() => {\n    const annotationManager = core.getAnnotationManager();\n    const tool = core.getToolMode();\n    const annotations = core.getSelectedAnnotations();\n\n    const allSelectedAnnotations = new Set(annotations);\n    annotations.forEach((annotation) => {\n      if (annotation.isGrouped()) {\n        const groupedAnnotations = annotationManager.getGroupAnnotations(annotation);\n        groupedAnnotations.forEach((grouped) => allSelectedAnnotations.add(grouped));\n      } else if (annotation.getGroupedChildren().length > 1) {\n        annotation.getGroupedChildren().forEach((child) => allSelectedAnnotations.add(child));\n      }\n    });\n    const selectedAnnotations = Array.from(allSelectedAnnotations);\n\n    setSelectedAnnotations(selectedAnnotations);\n    setCurrentTool(tool);\n  }, 150, { leading: false, trailing: true });\n\n  useEffect(() => {\n    if (isPanelOpen) {\n      handleChange();\n    }\n  }, [isPanelOpen]);\n\n  useEffect(() => {\n    core.addEventListener('annotationSelected', handleChange);\n    core.addEventListener('toolModeUpdated', handleChange);\n\n    return () => {\n      core.removeEventListener('annotationSelected', handleChange);\n      core.removeEventListener('toolModeUpdated', handleChange);\n    };\n  }, []);\n\n  const getComponent = () => {\n    const hideStyles = selectedAnnotations.length > 0 ?\n      shouldShowNoStyles(selectedAnnotations, filteredTypes) :\n      !annotationCreateToolNames.includes(currentTool?.name);\n\n    if (hideStyles) {\n      return NoToolStylePanel;\n    }\n\n    return getStylePanelComponent(currentTool, selectedAnnotations);\n  };\n\n  if (!isPanelOpen) {\n    return null;\n  }\n\n  const StylePanelComponent = getComponent();\n\n  return (\n    <DataElementWrapper dataElement=\"stylePanel\" className=\"Panel StylePanel\">\n      <StylePanelComponent\n        currentTool={currentTool}\n        selectedAnnotations={selectedAnnotations}\n      />\n    </DataElementWrapper>\n  );\n};\n\nexport default StylePanelContainer;", "import StylePanelContainer from './StylePanelContainer';\n\nexport default StylePanelContainer;"], "sourceRoot": ""}