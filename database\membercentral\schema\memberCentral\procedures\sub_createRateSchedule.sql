ALTER PROCEDURE dbo.sub_createRateSchedule
@siteID int,
@scheduleName varchar(200),
@recordedByMemberID int,
@scheduleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @scheduleID = null;
	DECLARE @orgID int, @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);
	select @orgID = orgID from dbo.sites where siteID = @siteID;

	IF len(@scheduleName) = 0
		RAISERROR('Schedule name invalid.',16,1);
	
	-- check to see if valid name
	SELECT TOP 1 @scheduleID = scheduleID
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleName = @scheduleName
	AND [status] <> 'D';

	IF @scheduleID is not null
		RAISERROR('Schedule name already in use.',16,1);

	INSERT INTO dbo.sub_rateSchedules (scheduleName, status, siteID)
	VALUES (@scheduleName, 'A', @siteID);

	select @scheduleID = SCOPE_IDENTITY();

	-- audit log
	DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID": '+ CAST(@scheduleID AS varchar(10)) +' }';
	SET @msgjson = 'New Subscription Rate Schedule [' + STRING_ESCAPE(@scheduleName,'json') + '] has been created.'

	EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msgjson,
			@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
