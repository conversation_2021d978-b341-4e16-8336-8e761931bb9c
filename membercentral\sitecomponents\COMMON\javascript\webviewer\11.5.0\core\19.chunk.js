/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[19],
		{
			642: function (ya) {
				(function () {
					ya.exports = {
						Na: function () {
							function va(e, f) {
								this.scrollLeft = e;
								this.scrollTop = f;
							}
							function r(e) {
								if (
									null === e ||
									"object" !== typeof e ||
									void 0 === e.behavior ||
									"auto" === e.behavior ||
									"instant" === e.behavior
								)
									return !0;
								if ("object" === typeof e && "smooth" === e.behavior) return !1;
								throw new TypeError(
									"behavior member of ScrollOptions " +
										e.behavior +
										" is not a valid value for enumeration ScrollBehavior.",
								);
							}
							function la(e, f) {
								if ("Y" === f) return e.clientHeight + b < e.scrollHeight;
								if ("X" === f) return e.clientWidth + b < e.scrollWidth;
							}
							function na(e, f) {
								e = ca.getComputedStyle(e, null)["overflow" + f];
								return "auto" === e || "scroll" === e;
							}
							function ma(e) {
								var f = la(e, "Y") && na(e, "Y");
								e = la(e, "X") && na(e, "X");
								return f || e;
							}
							function ja(e) {
								var f = (h() - e.startTime) / 468;
								var a = 0.5 * (1 - Math.cos(Math.PI * (1 < f ? 1 : f)));
								f = e.ur + (e.x - e.ur) * a;
								a = e.uH + (e.y - e.uH) * a;
								e.method.call(e.XO, f, a);
								(f === e.x && a === e.y) ||
									ca.requestAnimationFrame(ja.bind(ca, e));
							}
							function ia(e, f, a) {
								var w = h();
								if (e === y.body) {
									var z = ca;
									var aa = ca.scrollX || ca.pageXOffset;
									e = ca.scrollY || ca.pageYOffset;
									var ea = n.scroll;
								} else
									(z = e), (aa = e.scrollLeft), (e = e.scrollTop), (ea = va);
								ja({
									XO: z,
									method: ea,
									startTime: w,
									ur: aa,
									uH: e,
									x: f,
									y: a,
								});
							}
							var ca = window,
								y = document;
							if (
								!("scrollBehavior" in y.documentElement.style && !0 !== ca.eYa)
							) {
								var x = ca.HTMLElement || ca.Element,
									n = {
										scroll: ca.scroll || ca.scrollTo,
										scrollBy: ca.scrollBy,
										d9: x.prototype.scroll || va,
										scrollIntoView: x.prototype.scrollIntoView,
									},
									h =
										ca.performance && ca.performance.now
											? ca.performance.now.bind(ca.performance)
											: Date.now,
									b = RegExp("MSIE |Trident/|Edge/").test(
										ca.navigator.userAgent,
									)
										? 1
										: 0;
								ca.scroll = ca.scrollTo = function (e, f) {
									void 0 !== e &&
										(!0 === r(e)
											? n.scroll.call(
													ca,
													void 0 !== e.left
														? e.left
														: "object" !== typeof e
															? e
															: ca.scrollX || ca.pageXOffset,
													void 0 !== e.top
														? e.top
														: void 0 !== f
															? f
															: ca.scrollY || ca.pageYOffset,
												)
											: ia.call(
													ca,
													y.body,
													void 0 !== e.left
														? ~~e.left
														: ca.scrollX || ca.pageXOffset,
													void 0 !== e.top
														? ~~e.top
														: ca.scrollY || ca.pageYOffset,
												));
								};
								ca.scrollBy = function (e, f) {
									void 0 !== e &&
										(r(e)
											? n.scrollBy.call(
													ca,
													void 0 !== e.left
														? e.left
														: "object" !== typeof e
															? e
															: 0,
													void 0 !== e.top ? e.top : void 0 !== f ? f : 0,
												)
											: ia.call(
													ca,
													y.body,
													~~e.left + (ca.scrollX || ca.pageXOffset),
													~~e.top + (ca.scrollY || ca.pageYOffset),
												));
								};
								x.prototype.scroll = x.prototype.scrollTo = function (e, f) {
									if (void 0 !== e)
										if (!0 === r(e)) {
											if ("number" === typeof e && void 0 === f)
												throw new SyntaxError("Value could not be converted");
											n.d9.call(
												this,
												void 0 !== e.left
													? ~~e.left
													: "object" !== typeof e
														? ~~e
														: this.scrollLeft,
												void 0 !== e.top
													? ~~e.top
													: void 0 !== f
														? ~~f
														: this.scrollTop,
											);
										} else
											(f = e.left),
												(e = e.top),
												ia.call(
													this,
													this,
													"undefined" === typeof f ? this.scrollLeft : ~~f,
													"undefined" === typeof e ? this.scrollTop : ~~e,
												);
								};
								x.prototype.scrollBy = function (e, f) {
									void 0 !== e &&
										(!0 === r(e)
											? n.d9.call(
													this,
													void 0 !== e.left
														? ~~e.left + this.scrollLeft
														: ~~e + this.scrollLeft,
													void 0 !== e.top
														? ~~e.top + this.scrollTop
														: ~~f + this.scrollTop,
												)
											: this.scroll({
													left: ~~e.left + this.scrollLeft,
													top: ~~e.top + this.scrollTop,
													behavior: e.behavior,
												}));
								};
								x.prototype.scrollIntoView = function (e) {
									if (!0 === r(e))
										n.scrollIntoView.call(this, void 0 === e ? !0 : e);
									else {
										for (e = this; e !== y.body && !1 === ma(e); )
											e = e.parentNode || e.host;
										var f = e.getBoundingClientRect(),
											a = this.getBoundingClientRect();
										e !== y.body
											? (ia.call(
													this,
													e,
													e.scrollLeft + a.left - f.left,
													e.scrollTop + a.top - f.top,
												),
												"fixed" !== ca.getComputedStyle(e).position &&
													ca.scrollBy({
														left: f.left,
														top: f.top,
														behavior: "smooth",
													}))
											: ca.scrollBy({
													left: a.left,
													top: a.top,
													behavior: "smooth",
												});
									}
								};
							}
						},
					};
				})();
			},
		},
	]);
}).call(this || window);
