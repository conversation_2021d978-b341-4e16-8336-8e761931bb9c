{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/setCellFormatString.js", "webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellFormatButton.js"], "names": ["setCellFormatString", "formatType", "formatString", "formatsMap", "core", "getDocumentViewer", "getSpreadsheetEditorManager", "setSelectedCellRangeStyle", "propTypes", "PropTypes", "string", "isFlyoutItem", "bool", "secondaryLabel", "style", "object", "className", "selector", "func", "CellFormatButton", "forwardRef", "props", "ref", "currentFormatType", "useSelector", "state", "selectors", "getActiveCellFormatType", "isActive", "menuItems", "dataElement", "icon", "title", "handleClick", "FlyoutItemContainer", "onClick", "additionalClass", "ActionButton", "key", "img", "ariaPressed", "displayName"], "mappings": "yMAeeA,MAZf,SAA6BC,GAC3B,IAAMC,EAAeC,IAAWF,GAC3BC,GAI4BE,IAAKC,oBAAoBC,8BACjCC,0BAA0B,CACjDL,kB,mOCFJ,IAAMM,EAAY,CAChBP,WAAYQ,IAAUC,OACtBC,aAAcF,IAAUG,KACxBC,eAAgBJ,IAAUC,OAC1BI,MAAOL,IAAUM,OACjBC,UAAWP,IAAUC,OACrBO,SAAUR,IAAUS,MAGhBC,EAAmBC,sBAAW,SAACC,EAAOC,GAC1C,IAAQX,EAA+DU,EAA/DV,aAAcV,EAAiDoB,EAAjDpB,WAAYY,EAAqCQ,EAArCR,eAAgBC,EAAqBO,EAArBP,MAAOE,EAAcK,EAAdL,UACnDO,EAAoBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,wBAAwBF,MAC7EG,EAAW3B,IAAesB,EAEhC,EAAqCM,IAAU5B,GAAvC6B,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAClBjC,EAAoBC,IAGtB,OACEU,EACE,kBAACuB,EAAA,EAAmB,KACdb,EAAK,CACTC,IAAKA,EACLa,QAASF,EACTpB,eAAgBA,EAChBuB,gBAAiBR,EAAW,SAAW,MAGvC,kBAACS,EAAA,EAAY,CACXC,IAAKrC,EACL2B,SAAUA,EACVO,QAASF,EACTH,YAAaA,EACbE,MAAOA,EACPO,IAAKR,EACLS,YAAaZ,EACbd,MAAOA,EACPE,UAAWA,OAMrBG,EAAiBX,UAAYA,EAC7BW,EAAiBsB,YAAc,mBAEhBtB", "file": "chunks/chunk.93.js", "sourcesContent": ["import core from 'core';\nimport { formatsMap } from 'src/constants/spreadsheetEditor';\n\nfunction setCellFormatString(formatType) {\n  const formatString = formatsMap[formatType];\n  if (!formatString) {\n    return;\n  }\n\n  const spreadsheetEditorManager = core.getDocumentViewer().getSpreadsheetEditorManager();\n  spreadsheetEditorManager.setSelectedCellRangeStyle({\n    formatString,\n  });\n}\n\nexport default setCellFormatString;", "import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport setCellFormatString from '../../../../../helpers/setCellFormatString';\n\nconst propTypes = {\n  formatType: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  secondaryLabel: PropTypes.string,\n  style: PropTypes.object,\n  className: PropTypes.string,\n  selector: PropTypes.func,\n};\n\nconst CellFormatButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, formatType, secondaryLabel, style, className } = props;\n  const currentFormatType = useSelector((state) => selectors.getActiveCellFormatType(state));\n  const isActive = formatType === currentFormatType;\n\n  const { dataElement, icon, title } = menuItems[formatType];\n\n  const handleClick = () => {\n    setCellFormatString(formatType);\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        secondaryLabel={secondaryLabel}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={formatType}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellFormatButton.propTypes = propTypes;\nCellFormatButton.displayName = 'CellFormatButton';\n\nexport default CellFormatButton;"], "sourceRoot": ""}