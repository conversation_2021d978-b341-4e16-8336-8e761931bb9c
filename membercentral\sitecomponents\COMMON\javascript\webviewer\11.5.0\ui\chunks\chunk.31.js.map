{"version": 3, "sources": ["webpack:///./src/ui/src/components/Spinner/Spinner.js", "webpack:///./src/ui/src/components/Spinner/index.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.js", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss?2f3f", "webpack:///./src/ui/src/components/NoteTextPreview/NoteTextPreview.scss", "webpack:///./src/ui/src/components/Spinner/Spinner.scss?ed1d", "webpack:///./src/ui/src/components/Spinner/Spinner.scss", "webpack:///./src/ui/src/components/ReactSelectWebComponentProvider/ReactSelectWebComponentProvider.js", "webpack:///./src/ui/src/components/ReactSelectWebComponentProvider/index.js", "webpack:///./src/ui/src/helpers/applyRedactions.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.scss?ba2d", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.scss", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss?b17d", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.scss", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.scss?4f71", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.scss", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.scss?ed7b", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.scss", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss?fa75", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.scss", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss?d5bb", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.scss", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss?16f3", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.scss", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss?84f9", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.scss", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.scss?4fbd", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.scss", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanelContext.js", "webpack:///./src/ui/src/components/RedactionTextPreview/RedactionTextPreviewContainer.js", "webpack:///./src/ui/src/components/RedactionTextPreview/index.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItem.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/RedactionItemContainer.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionItem/index.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroup.js", "webpack:///./src/ui/src/components/RedactionPageGroup/RedactionPageGroupContainer.js", "webpack:///./src/ui/src/components/RedactionPageGroup/index.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanel.js", "webpack:///./src/ui/src/components/CreatableMultiSelect/CreatableMultiSelect.js", "webpack:///./src/ui/src/components/CreatableMultiSelect/index.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/RedactionSearchMultiSelect.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchMultiSelect/index.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlay.js", "webpack:///./src/ui/src/helpers/multiSearch.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/RedactionSearchOverlayContainer.js", "webpack:///./src/ui/src/components/RedactionSearchOverlay/index.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResult.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/index.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResult/RedactionSearchResultContainer.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/RedactionSearchResultGroup.js", "webpack:///./src/ui/src/components/RedactionSearchResultGroup/index.js", "webpack:///./src/ui/src/constants/searchStatus.js", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResults.js", "webpack:///./src/ui/src/components/RedactionSearchResults/RedactionSearchResultsContainer.js", "webpack:///./src/ui/src/components/RedactionSearchResults/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/RedactionSearchPanel.js", "webpack:///./src/ui/src/hooks/useOnRedactionSearchCompleted/useOnRedactionSearchCompleted.js", "webpack:///./src/ui/src/hooks/useOnRedactionSearchCompleted/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/index.js", "webpack:///./src/ui/src/components/RedactionSearchPanel/RedactionSearchPanelContainer.js", "webpack:///./src/ui/src/components/RedactionPanel/RedactionPanelContainer.js", "webpack:///./src/ui/src/components/RedactionPanel/index.js"], "names": ["Spinner", "height", "width", "spinnerStyle", "className", "style", "NoteTextPreview", "props", "text", "children", "replace", "panelWidth", "linesToBreak", "renderRichText", "richTextStyle", "resize", "comment", "beforeContent", "useState", "expanded", "setExpand", "previewElementWidth", "setPreviewWidth", "charsPerLine", "setCharsperLine", "showPrompt", "setShowPrompt", "ref", "React", "useRef", "t", "useTranslation", "textToDisplay", "substring", "prompt", "noteTextPreviewClass", "classNames", "useEffect", "textNodeWidth", "current", "clientWidth", "useLayoutEffect", "textWidth", "context", "document", "createElement", "getContext", "font", "measureText", "getTextWidth", "averageCharWidth", "length", "Math", "floor", "aria-live", "onClick", "event", "stopPropagation", "propTypes", "PropTypes", "number", "func", "any", "bool", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ReactSelectWebComponentProvider", "emotionCache", "useMemo", "container", "getRootNode", "createCache", "key", "value", "noop", "annotations", "onRedactionCompleted", "activeDocumentViewerKey", "dispatch", "core", "isWebViewerServerDocument", "webViewerServerApply", "webViewerApply", "applyRedactions", "then", "results", "url", "downloadPdf", "filename", "includeAnnotations", "externalURL", "console", "warn", "warning", "message", "i18next", "title", "confirmBtnText", "onConfirm", "err", "fireError", "Promise", "resolve", "actions", "showWarningMessage", "RedactionPanelContext", "createContext", "RedactionPanelProvider", "selectedRedactionItemId", "setSelectedRedactionItemId", "isRedactionSearchActive", "setIsRedactionSearchActive", "activeSearchResultIndex", "setActiveSearchResultIndex", "onAnnotationSelected", "action", "redactionAnnotations", "filter", "annotation", "Subject", "selectedAnnotationId", "Id", "activeSearchResultChanged", "newActiveSearchResult", "newActiveSearchResultIndex", "getPageSearchResults", "findIndex", "searchResult", "isSearchResultEqual", "addEventListener", "removeEventListener", "Provider", "RedactionTextPreviewContainer", "redactionPanelWidth", "useSelector", "state", "selectors", "getRedactionPanelWidth", "shallowEqual", "RedactionItem", "isCustomUI", "getFeatureFlags", "customizableUI", "iconColor", "author", "dateFormat", "language", "onRedactionItemDelete", "onRedactionItemSelection", "textPreview", "isSelected", "timezone", "date", "getLatestActivityDate", "datetimeStr", "toLocaleString", "timeZone", "Date", "redactionPreview", "formattedDate", "dayjs", "locale", "format", "dateAndAuthor", "label", "icon", "redactionType", "redactionTypeMap", "RedactionTextPreview", "getContents", "<PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "aria<PERSON>urrent", "Icon", "glyph", "color", "OverlayText", "marginLeft", "img", "memo", "RedactionItemContainer", "useContext", "getNoteDateFormat", "getCurrentLanguage", "getCustomNoteSelectionFunction", "getTimezone", "customNoteSelectionFunction", "getCustomData", "StrokeColor", "toString", "getDisplayAuthor", "useCallback", "deselectAllAnnotations", "selectAnnotation", "jumpToAnnotation", "deleteAnnotations", "RedactionPageGroup", "pageNumber", "redactionItems", "CollapsibleSection", "header", "expansionDescription", "headingLevel", "map", "redactionItem", "oneOfType", "string", "array", "RedactionPageGroupContainer", "sortedRedactionItems", "setSortedRedactionItems", "getSortStrategies", "getSortedNotes", "RedactionPanel", "applyAllRedactions", "deleteAllRedactionAnnotations", "redactionTypesDictionary", "redactionPageMap", "setRedactionPageMap", "redactionPageNumbers", "setRedactionPageNumbers", "isTestMode", "mapAnnotationToRedactionType", "PageNumber", "undefined", "Object", "keys", "testModeProps", "noRedactionAnnotations", "redactAllButtonClassName", "disabled", "clearAllButtonClassName", "applyAllRedactionsWithFocusHandler", "useFocusHandler", "initialItemCount", "data", "itemContent", "index", "dataElement", "DataElements", "REDACT_ALL_MARKED_BUTTON", "CreatableMultiSelect", "id", "rest", "htmlFor", "onTouchEndCapture", "e", "is<PERSON><PERSON><PERSON>", "inputId", "defaultProps", "getColorForMode", "isDarkMode", "darkModeColor", "lightModeColor", "isFocused", "RedactionOption", "Option", "object", "isRequired", "MultiValueLabel", "tabIndex", "display", "CustomControl", "Control", "MultiValueRemove", "updatedProps", "innerProps", "onKeyDown", "node", "RedactionSearchMultiSelect", "activeTheme", "redactionSearchOptions", "redactionGroup", "styles", "groupHeading", "base", "textTransform", "fontSize", "fontWeight", "COMMON_COLORS", "CUSTOM_UI_VARS", "paddingBottom", "paddingLeft", "paddingTop", "group", "padding", "menu", "borderRadius", "overflowY", "margin", "menuList", "backgroundColor", "multiValue", "whiteSpace", "multiValueRemove", "boxShadow", "option", "outline", "noOptionsMessage", "valueContainer", "maxHeight", "isMobileSize", "control", "minHeight", "borderColor", "placeholder", "input", "getStyles", "components", "IndicatorsContainer", "formatCreateLabel", "buildSearchOptions", "searchTerms", "textSearch", "caseSensitive", "searchTerm", "type", "regex", "ignoreCase", "RedactionSearchOverlay", "setSearchTerms", "executeRedactionSearch", "translatedOptions", "DataElementWrapper", "onFocus", "onCreateOption", "newValue", "textTerm", "updatedSearchTerms", "onChange", "multiSearch", "store", "getState", "redactionSearchPatterns", "getRedactionSearchPatterns", "searchOptionsMap", "reduce", "searchArray", "searchType", "searchRegex", "source", "searchString", "join", "searchTextFullFactory", "searchTextFull", "clearSearchResults", "multiSearchFactory", "RedactionSearchOverlayContainer", "useStore", "getActiveTheme", "values", "pattern", "RedactionSearchResult", "isChecked", "onClickResult", "isActive", "ambientStr", "displayResult", "resultStrStart", "resultStrEnd", "resultStr", "searchValue", "slice", "textBeforeSearchValue", "textAfterSearchValue", "displayRedactionSearchResult", "searchResultClassname", "active", "aria-label", "aria-current", "paddingRight", "Choice", "checked", "RedactionSearchResultContainer", "checkResult", "setActiveSearchResult", "RedactionSearchResultGroup", "searchResults", "selectedSearchResultIndexes", "setSelectedSearchResultIndexes", "groupResultIndexes", "result", "allItemsChecked", "setAllItemsChecked", "allResultsSelected", "allSelected", "currentIndex", "checkAllResults", "target", "resultIndex", "RedactionSearchResults", "redactionSearchResults", "searchStatus", "onCancelSearch", "isProcessingRedactionResults", "markSelectedResultsForRedaction", "redactSelectedResults", "redactionSearchResultPageMap", "setRedactionSearchResultPageMap", "selectedSearchResultIndexesMap", "setSelectedSearchResultIndexesMap", "selectedIndexes", "setSelectedIndexes", "pageNum", "selectedIndexesMap", "redactionSearchResult", "noResults", "role", "isEmptyList", "resultsContainerClass", "emptyList", "redactAllButtonClass", "markAllForRedactionButtonClass", "shouldShowResultsCounterOptions", "SearchStatus", "flexGrow", "searchResultIndexMap", "resultGroupPageNumbers", "renderSearchResults", "ToolNames", "Core", "Tools", "defaultRedactionStyles", "Annotations", "Color", "TextColor", "Font", "createRedactionAnnotations", "activeToolStyles", "FillColor", "FontSize", "TextAlign", "redaction", "RedactionAnnotation", "page_num", "Quads", "quads", "quad", "getPoints", "Utilities", "calculateAutoFontSize", "setContents", "result_str", "Author", "getCurrentUser", "setCustomData", "RedactionSearchResultsContainer", "useDispatch", "getActiveToolStyles", "getActiveToolName", "activeToolName", "tool", "getTool", "REDACTION", "alternativeDefaultStyles", "defaults", "includes", "getAnnotationManager", "addAnnotations", "RedactionSearchPanel", "clearRedactionSearchResults", "isMobile", "closeElement", "useOnRedactionSearchCompleted", "setSearchStatus", "setRedactionSearchResults", "setIsProcessingRedactionResults", "searchPatterns", "mapResultToType", "resultType", "find", "test", "onSearchResultsChanged", "mappedResults", "searchInProgressEventHandler", "isSearching", "setTimeout", "ReactionSearchPanelContainer", "RedactionPanelContainer", "isElementOpen", "isElementDisabled", "isInDesktopOnlyMode", "getCustomApplyRedactionsHandler", "isOpen", "isDisabled", "customApplyRedactionsHandler", "redactionAnnotationsList", "isCustomPanel", "defaultRedactionTypes", "closeRedactionPanel", "tempDataElement", "min<PERSON><PERSON><PERSON>", "renderNull", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeout", "clearTimeout", "dataElementToUse", "originalApplyRedactions", "callOnRedactionCompleted", "RedactionPanelContainerWithProvider"], "mappings": "gHAceA,G,QAXC,SAAH,GAA4C,QAAtCC,cAAM,IAAG,SAAM,MAAEC,MAC5BC,EAAe,CACnBF,SACAC,WAHqC,IAAG,SAAM,GAMhD,OACE,yBAAKE,UAAU,UAAUC,MAAOF,MCRrBH,O,otCCKf,SAASM,EAAgBC,GAEvB,IAAMC,EAAOD,EAAME,SAASC,QAAQ,MAAO,IAEzCC,EASEJ,EATFI,WACAC,EAQEL,EARFK,aACAC,EAOEN,EAPFM,eACAC,EAMEP,EANFO,cACAC,EAKER,EALFQ,OACAV,EAIEE,EAJFF,MAAK,EAIHE,EAFFS,eAAO,IAAG,GAAK,IAEbT,EADFU,qBAAa,IAAG,eAAQ,EAEmB,IAAfC,oBAAS,GAAM,GAAtCC,EAAQ,KAAEC,EAAS,KACmC,IAAdF,mBAAS,MAAK,GAAtDG,EAAmB,KAAEC,EAAe,KACW,IAAdJ,mBAAS,MAAK,GAA/CK,EAAY,KAAEC,EAAe,KACe,IAAfN,oBAAS,GAAM,GAA5CO,EAAU,KAAEC,EAAa,KAC1BC,EAAMC,IAAMC,OAAO,MACjBC,EAAMC,cAAND,EAQFE,EAAgBb,EAAWX,EAAOA,EAAKyB,UAAU,EAAGV,EAAeX,GACnEsB,EAAoBJ,EAAXX,EAAa,kBAAuB,mBAC7CgB,EAAuBC,IAAW,oBAAqB,CAAE,kBAAmBpB,IA0BlF,OAxBAqB,qBAAU,WACR,IAAMC,EAAgBX,EAAIY,QAAQC,YAClClB,EAAgBgB,KACf,CAAC3B,IAGJ8B,2BAAgB,WASd,IAAMC,EARN,SAAsBlC,GACpB,IACMmC,EADSC,SAASC,cAAc,UACfC,WAAW,MAGlC,OAFAH,EAAQI,KAAO,kBAERJ,EAAQK,YAAYxC,GAAMN,MAGjB+C,CAAazC,GACzB0C,EAAmBR,EAAYlC,EAAK2C,OACpC5B,EAAe6B,KAAKC,MAAMhC,EAAsB6B,GACtD1B,EAAgBD,GAGhBG,EADmBgB,EAAYrB,EACJT,KAC1B,CAACJ,EAAMa,IAGR,yBAAKjB,UAAW+B,EAAsBR,IAAKA,EAAKtB,MAAOA,EAAOiD,YAAU,UACrErC,IACAJ,GAAkBC,EACfD,EAAemB,EAAelB,EAAe,GAC7CkB,EAAc,IAAEP,GAAc,4BAAQrB,UAAU,2BAA2BmD,QAvC5D,SAACC,GACtBA,EAAMC,kBACNrC,GAAWD,GACXJ,GAAUA,MAoCiGmB,IAK/G5B,EAAgBoD,UAAY,CAC1B/C,WAAYgD,IAAUC,OACtBhD,aAAc+C,IAAUC,OACxB/C,eAAgB8C,IAAUE,KAC1B/C,cAAe6C,IAAUG,IACzB/C,OAAQ4C,IAAUE,KAClBxD,MAAOsD,IAAUG,IACjB9C,QAAS2C,IAAUI,KACnB9C,cAAe0C,IAAUE,MAGZvD,O,qBCnFf,IAAI0D,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,uxBAAwxB,M,qBCLjzB,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,goCAAioC,KAG1pCwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,kFCeRC,EApByB,SAAH,GAAqB,IAAftF,EAAQ,EAARA,SAEnCuF,EAAeC,mBAAQ,WAC3B,IAAMC,EAAYC,cAClB,OAAOC,YAAY,CACjBC,IAAK,0BACLH,gBAED,IAGH,OAAO1B,OAAOC,8BACZ,kBAAC,IAAa,CAAC6B,MAAON,GACnBvF,GAGHA,GCnBWsF,O,wFCKf,SAASQ,KAEM,aAACC,GAAW,IAAEC,EAAuB,UAAH,6CAAGF,EAAMG,EAA0B,UAAH,6CAAG,EAAC,OAAK,SAACC,GACzF,OAAIC,IAAKC,4BAEAC,EAAqBN,EAAaG,EAAUD,GAE9CK,EAAeP,EAAaC,EAAsBE,EAAUD,KAGrE,IAAMI,EAAuB,SAACN,EAAaG,EAAUD,GAAuB,OAAKE,IAAKI,gBAAgBR,EAAaE,GAAyBO,MAAK,SAACC,GAChJ,GAAIA,GAAWA,EAAQC,IACrB,OAAOC,YAAYT,EAAU,CAC3BU,SAAU,eACVC,oBAAoB,EACpBC,YAAaL,EAAQC,MAGzBK,QAAQC,KAAK,sDAGTV,EAAiB,SAACP,EAAaC,EAAsBE,EAAUD,GACnE,IAIMgB,EAAU,CACdC,QALcC,IAAQ9F,EAAE,kCAMxB+F,MALYD,IAAQ9F,EAAE,+BAMtBgG,eALqBF,IAAQ9F,EAAE,gBAM/BiG,UAAW,WAMT,OALAnB,IAAKI,gBAAgBR,EAAaE,GAC/BO,MAAK,WACJR,OACA,OACK,SAACuB,GAAG,OAAKC,YAAUD,MACrBE,QAAQC,YAInB,OAAOxB,EAASyB,IAAQC,mBAAmBX,M,qBC/C7C,IAAI1D,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,s5QAAu5Q,KAGh7QwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI9B,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACjB,EAAOC,EAAI,6+BAA8+B,M,qBCLvgC,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,mkEAAokE,KAG7lEwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI9B,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,yGAA0G,M,qBCLnI,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACjB,EAAOC,EAAI,o7CAAq7C,KAG98CwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI9B,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,uhDAAwhD,KAGjjDwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAI9B,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACjB,EAAOC,EAAI,2vBAA4vB,M,qBCLrxB,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEzB1B,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,8cAA+c,M,qBCLxe,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADA7B,SAAS8B,KAAKC,YAAYJ,GAI5B,IAAIK,EAEJA,EAAgBhC,SAASiC,qBAAqB,oBAEzCD,EAAczB,SACjByB,EAzBF,SAASE,EAAwBC,EAASC,EAAOpC,UAC/C,MAAMqC,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIlB,EAAI,EAAGA,EAAIO,EAAczB,OAAQkB,IAAK,CAC7C,MAAMmB,EAAeZ,EAAcP,GACnC,GAAU,IAANA,EACFmB,EAAaF,WAAWX,YAAYJ,GACpCA,EAASkB,OAAS,WACZF,EAAgBpC,OAAS,GAC3BoC,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYpB,EAASoB,iBAIhC,CACL,MAAMD,EAAYnB,EAASqB,WAAU,GACrCJ,EAAaF,WAAWX,YAAYe,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP1B,EAAIC,EAASK,GAI1BF,EAAOyB,QAAU5B,EAAQ6B,QAAU,I,sBClEnCD,EAAUzB,EAAOyB,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACjB,EAAOC,EAAI,m+KAAs+K,KAG//KwB,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,i1CCNvB,IAAMwC,EAAwB1G,IAAM2G,gBAE9BC,EAAyB,SAAH,GAAqB,IAAf/H,EAAQ,EAARA,SAC4C,IAAdS,mBAAS,MAAK,GAArEuH,EAAuB,KAAEC,EAA0B,KACmB,IAAfxH,oBAAS,GAAM,GAAtEyH,EAAuB,KAAEC,EAA0B,KACgB,IAAZ1H,oBAAU,GAAE,GAAnE2H,EAAuB,KAAEC,EAA0B,KAE1DzG,qBAAU,WACR,IAAM0G,EAAuB,SAACvC,EAAawC,GACzC,GAAe,aAAXA,EAAuB,CACzB,IAAMC,EAAuBzC,EAAY0C,QAAO,SAACC,GAAU,MAA4B,WAAvBA,EAAWC,WAErEC,EAAuBJ,EAAqB9F,OAAS,EAAI8F,EAAqB,GAAGK,GAAK,KAC5FZ,EAA2BW,QAE3BX,EAA2B,OAIzBa,EAA4B,SAACC,GACjC,GAAKA,EAAL,CAGA,IACMC,GADoB7C,IAAK8C,wBAA0B,IACJC,WAAU,SAACC,GAC9D,OAAOhD,IAAKiD,oBAAoBD,EAAcJ,MAEhDV,EAA2BW,KAM7B,OAHA7C,IAAKkD,iBAAiB,qBAAsBf,GAC5CnC,IAAKkD,iBAAiB,4BAA6BP,GAE5C,WACL3C,IAAKmD,oBAAoB,qBAAsBhB,GAC/CnC,IAAKmD,oBAAoB,4BAA6BR,MAEvD,IAEH,IAAMjD,EAAQ,CACZmC,0BACAC,6BACAC,0BACAC,6BACAC,2BAGF,OAAO,kBAACP,EAAsB0B,SAAQ,CAAC1D,MAAOA,GAAQ7F,I,82CC9CxD,ICHewJ,EDGuB,SAAC1J,GACrC,IAAO2J,EAKN,EAL6BC,aAC5B,SAACC,GAAK,MAAK,CACTC,IAAUC,uBAAuBF,MAEnCG,KACD,GALyB,GAO1B,OACE,kBAACjK,EAAA,EAAe,KAAKC,EAAK,CAAEI,WAAYuJ,EAAqBlJ,SAAO,M,+hCEDxE,IAAMwJ,EAAgB,SAACjK,GAErB,IAAOkK,EAIN,EAJoBN,aACnB,SAACC,GAAK,YAAK,CACuB,QADvB,EACTC,IAAUK,gBAAgBN,UAAM,aAAhC,EAAkCO,mBAErC,GAJgB,GAMfC,EAUErK,EAVFqK,UACAzB,EASE5I,EATF4I,WACA0B,EAQEtK,EARFsK,OACAC,EAOEvK,EAPFuK,WACAC,EAMExK,EANFwK,SACAC,EAKEzK,EALFyK,sBACAC,EAIE1K,EAJF0K,yBACAC,EAGE3K,EAHF2K,YACAC,EAEE5K,EAFF4K,WACAC,EACE7K,EADF6K,SAEMtJ,EAAMC,cAAND,EAEJuJ,EAAOC,YAAsBnC,GAEjC,GAAIiC,EAAU,CACZ,IAAMG,EAAcF,EAAKG,eAAe,QAAS,CAAEC,SAAUL,IAC7DC,EAAO,IAAIK,KAAKH,GAGlB,IASII,EATEC,EAAgBP,EAAOQ,IAAMR,GAAMS,OAAOf,GAAUgB,OAAOjB,GAAchJ,EAAE,wCAC3EkK,EAAgB,GAAH,OAAMnB,EAAM,cAAMe,GAC/BxL,EAAYgC,IAAW,iBAAkB,CAAE,0BAA2B+I,GAAc,CAAE,aAAcV,IAExGwB,EAGE9C,EAHF8C,MAAK,EAGH9C,EAFF+C,YAAI,IAAG,wBAAqB,EAC5BC,EACEhD,EADFgD,cAsBF,OAhBER,EADEQ,IAAkBC,IAAuB,KAEzC,kBAACC,EAAoB,CAACzL,aAAc,GACjCsK,GAGLiB,IAAkBC,IAA4B,WAC3CD,IAAkBC,IAAmC,kBACrDD,IAAkBC,IAAyB,QAC3CD,IAAkBC,IAAkC,iBACpDD,IAAkBC,IAA6C,2BAE/CtK,EAAEmK,GAEF9C,EAAWmD,cAI9B,wBAAIlM,UAAWA,GACb,kBAACmM,EAAA,EAAM,CACLnM,UAAU,wBACVmD,QAAS0H,EACTuB,UAAS,UAAKb,EAAgB,YAAIK,EAAa,YAAIlK,EAAE,kBACrD2K,YAAatB,IAEf,yBAAK/K,UAAU,4BACb,kBAACsM,EAAA,EAAI,CAACC,MAAOT,EAAMU,MAAOhC,KAE5B,yBAAKxK,UAAU,uBACb,yBAAKA,UAAU,0BACZuL,GAGDxC,EAAW0D,YACT,yBAAKzM,UAAU,6BACZ0B,EAAE,+BAA+B,KAAGqH,EAAW0D,aACzC,KAEb,yBAAKzM,UAAU,8BACZ4L,IAGL,kBAACO,EAAA,EAAM,CACLnM,UAAU,wBACVC,MAAO,CAAEyM,WAAY,QACrBC,IAAK,aACLxJ,QAASyH,EACTwB,UAAS,UAAKb,EAAgB,YAAIK,EAAa,YAAIlK,EAAE,sBAM9CF,MAAMoL,KAAKxC,G,+hCClG1B,ICLeyC,EDKgB,SAAC1M,GAC9B,IAAQ4I,EAAe5I,EAAf4I,WAER,EAAgE+D,qBAAW5E,GAAnEG,EAAuB,EAAvBA,wBAAyBC,EAA0B,EAA1BA,2BAehC,IARGyB,aACF,SAACC,GAAK,MAAK,CACTC,IAAU8C,kBAAkB/C,GAC5BC,IAAU+C,mBAAmBhD,GAC7BC,IAAUgD,+BAA+BjD,GACzCC,IAAUiD,YAAYlD,MAExBG,KACD,GAZCO,EAAU,KACVC,EAAQ,KACRwC,EAA2B,KAC3BnC,EAAQ,KAWJF,EAAc/B,EAAWqE,cAAc,qBACvC5C,EAAYzB,EAAWsE,YAAYC,WACnC7C,EAASjE,IAAK+G,iBAAiBxE,EAAmB,QAElD8B,EAA2B2C,uBAAY,WAC3CL,GAA+BA,EAA4BpE,GAC3DvC,IAAKiH,yBACLjH,IAAKkH,iBAAiB3E,GACtBvC,IAAKmH,iBAAiB5E,GACtBT,EAA2BS,EAAWG,MACrC,CAACH,IAEE6B,EAAwB4C,uBAAY,WACxChH,IAAKoH,kBAAkB,CAAC7E,MACvB,CAACA,IAEJ,OACE,kBAAC,EAAa,CACZ2B,WAAYA,EACZC,SAAUA,EACVK,SAAUA,EACVP,OAAQA,EACR1B,WAAYA,EACZyB,UAAWA,EACXM,YAAaA,EACbF,sBAAuBA,EACvBC,yBAA0BA,EAC1BE,WAAY1C,IAA4BU,EAAWG,M,SE9CnD2E,G,QAAqB,SAAC1N,GAC1B,IACE2N,EAEE3N,EAFF2N,WACAC,EACE5N,EADF4N,eAGMrM,EAAMC,cAAND,EAQR,OACE,kBAACsM,EAAA,EAAkB,CACjBhO,UAAU,uBACViO,OATW,WACb,MAAO,GAAP,OACKvM,EAAE,sBAAqB,YAAIoM,IAQ9BI,qBAAoB,UAAKxM,EAAE,sBAAqB,YAAIoM,EAAU,YAAIpM,EAAE,kCACpEyM,aAAc,GAEd,wBAAInO,UAAU,mBACX+N,EAAeK,KAAI,SAACC,GAAa,OAChC,kBAAC,EAAa,CACZtF,WAAYsF,EACZpI,IAAG,UAAKoI,EAAcnF,GAAE,YAAI4E,YAQxCD,EAAmBvK,UAAY,CAC7BwK,WAAYvK,IAAU+K,UAAU,CAAC/K,IAAUC,OAAQD,IAAUgL,SAC7DR,eAAgBxK,IAAUiL,OAGbX,Q,2wCC1Cf,ICFeY,EDEqB,SAACtO,GAGnC,IAAQ4N,EAAmB5N,EAAnB4N,eAE4D,IAAZjN,mBAAS,IAAG,GAA7D4N,EAAoB,KAAEC,EAAuB,KAIpD,OAHA1M,qBAAU,WACR0M,EAAwBC,cAA8B,SAAEC,eAAed,MACtE,CAACA,IAEF,kBAAC,EAAkB,GACjBA,eAAgBW,GACZvO,K,knDEFV,IAsGe2O,EAtGQ,SAAC3O,GACtB,IACE0I,EAIE1I,EAJF0I,qBACAkG,EAGE5O,EAHF4O,mBACAC,EAEE7O,EAFF6O,8BACAC,EACE9O,EADF8O,yBAGMvN,EAAMC,cAAND,EACoD,IAAZZ,mBAAS,IAAG,GAArDoO,EAAgB,KAAEC,EAAmB,KACwB,IAAZrO,mBAAS,IAAG,GAA7DsO,EAAoB,KAAEC,EAAuB,KAG5CC,EAAexC,qBAAW5E,GAA1BoH,WAERrN,qBAAU,WACR,IAAMiN,EAAmB,GACzBrG,EAAqB9D,SAAQ,SAACgE,GAC5B,IAAMgD,EAAgBwD,YAA6BxG,GACnD,EAAwBkG,EAAyBlD,IAAkB,CACjED,KAAM,2BACND,MAAO,gDAFDA,EAAK,EAALA,MAAOC,EAAI,EAAJA,KAIf/C,EAAW8C,MAAQA,EACnB9C,EAAW+C,KAAOA,EAClB/C,EAAWgD,cAAgBA,EAE3B,IAAM+B,EAAa/E,EAAWyG,gBACOC,IAAjCP,EAAiBpB,GACnBoB,EAAiBpB,GAAc,CAAC/E,GAEhCmG,EAAiBpB,GAAc,CAAC/E,GAAU,SAAKmG,EAAiBpB,QAIpEqB,EAAoBD,GACpBG,EAAwBK,OAAOC,KAAKT,MACnC,CAACrG,IAEJ,IAGQ+G,EAmBFC,EACJ,yBAAK7P,UAAU,wBACb,6BACE,kBAACsM,EAAA,EAAI,CAACtM,UAAU,aAAauM,MAAM,+BAErC,yBAAKvM,UAAU,OAAO0B,EAAE,uCAItBoO,EAA2B9N,IAAW,oBAAqB,CAAE+N,SAA0C,IAAhClH,EAAqB9F,SAC5FiN,EAA0BhO,IAAW,mBAAoB,CAAE+N,SAA0C,IAAhClH,EAAqB9F,SAE1FkN,EAAqCC,YAAgBnB,GAC3D,OACE,oCACE,wBAAI/O,UAAU,4BACX0B,EAAE,mCACH,0CACQmH,EAAqB9F,OAAM,OAGpCqM,EAAqBrM,OAAS,GAxC3B6M,EAAgBN,EAAa,CAAEa,iBAAkBf,EAAqBrM,QAAW,GAErF,yBAAK/C,UAAU,6BACb,kBAAC,IAAQ,GACPoQ,KAAMhB,EACNiB,YAAa,SAACC,EAAOxC,GACnB,OACE,kBAAC,EAAkB,CACjB7H,IAAKqK,EACLxC,WAAYA,EACZC,eAAgBmB,EAAiBpB,OAGnC8B,MA2ByDC,EACjE,yBAAK7P,UAAU,4BACb,kBAACmM,EAAA,EAAM,CACL4D,SAA0C,IAAhClH,EAAqB9F,OAC/B/C,UAAWgQ,EACX7M,QAAS6L,EACTnD,MAAOnK,EAAE,gCAEX,kBAACyK,EAAA,EAAM,CACL4D,SAA0C,IAAhClH,EAAqB9F,OAC/B/C,UAAW8P,EACX3M,QAAS8M,EACTM,YAAaC,IAAaC,yBAC1B5E,MAAOnK,EAAE,uC,wsBCvGnB,IAAMgP,GAAuB,SAAH,GAA+B,IAAzBC,EAAE,EAAFA,GAAI9E,EAAK,EAALA,MAAU+E,EAAI,SAChD,OACE,kBAACjL,GAAA,EAA+B,KAC9B,2BAAOkL,QAASF,EAAI3Q,UAAU,gCAAgC6L,GAC9D,yBAAKiF,kBAAmB,SAACC,GAAC,OAAKA,EAAE1N,oBAC/B,kBAAC,KAAe,IACd2N,SAAO,GACHJ,EAAI,CACRK,QAASN,QAOnBD,GAAqBpN,UAAY,CAC/BqN,GAAIpN,IAAUgL,OACd1C,MAAOtI,IAAUgL,QAGnBmC,GAAqBQ,aAAe,CAClCP,GAAI,GACJ9E,MAAO,IAGM6E,IC7BAA,GD6BAA,G,42DErBf,IAAMS,GAAkB,SAACC,EAAYC,EAAeC,GAAqC,IAArBC,IAAY,UAAH,+CAC3E,OAAIA,EACKH,EAAaC,EAAgBC,EAE/B,eA0HHE,GAAkB,SAACrR,GACvB,IAAQiQ,EAASjQ,EAATiQ,KACR,OACE,kBAAC,KAAWqB,OAAWtR,EACpBiQ,EAAKtE,MAAQ,kBAACQ,EAAA,EAAI,CAACC,MAAO6D,EAAKtE,OAC/BsE,EAAKvE,QAKZ2F,GAAgBlO,UAAY,CAC1B8M,KAAM7M,IAAUmO,OAAOC,YAGzB,IAAMC,GAAkB,SAAH,GAAiB,IAAXxB,EAAI,EAAJA,KACzB,OACE,yBAAKyB,SAAU,EAAG5R,MAAO,CAAE6R,QAAS,OAAQjS,OAAQ,SACjDuQ,EAAKtE,MAAQ,kBAACQ,EAAA,EAAI,CAACC,MAAO6D,EAAKtE,OAC/BsE,EAAKvE,QAKZ+F,GAAgBtO,UAAY,CAC1B8M,KAAM7M,IAAUmO,OAAOC,YAGzB,IAAMI,GAAgB,SAAH,OAAM1R,EAAQ,EAARA,SAAaF,EAAK,gBACzC,kBAAC,KAAW6R,QAAY7R,EACtB,yBAAKH,UAAU,uDACb,kBAACsM,EAAA,EAAI,CAACtM,UAAU,4CAA4CuM,MAAM,wBAEnElM,IAIC4R,GAAmB,SAAC9R,GACxB,IAAQuB,EAAMC,cAAND,EACFmK,EAAQ1L,EAAMiQ,KAAKvE,MAUnBqG,EAAe,GAAH,MACb/R,GAAK,IACRgS,WAAY,GAAF,MACLhS,EAAMgS,YAAU,IACnB,aAAc,GAAF,OAAKzQ,EAAE,iBAAgB,YAAImK,GACvCgG,SAAU,EACVO,UAdkB,SAAChP,GACH,UAAdA,EAAM6C,KAAiC,MAAd7C,EAAM6C,MAEjC7C,EAAMC,kBACNlD,EAAMgS,WAAWhP,gBAcrB,OACE,kBAAC,KAAW8O,iBAAqBC,IAIrCH,GAAczO,UAAY,CACxBjD,SAAUkD,IAAU8O,MAGtB,IAAMC,GAA6B,SAACnS,GAClC,IAAQuB,EAAMC,cAAND,EACA6Q,EAAwCpS,EAAxCoS,YAAaC,EAA2BrS,EAA3BqS,uBAEfC,EAAiB,CACrB,CACE5G,MAAOnK,EAAE,iCACTwC,QAASsO,IAKPE,EA/LU,SAACtB,GAAU,MAAM,CACjCuB,aAAc,SAACC,GAAI,gBACdA,GAAI,IACPC,cAAe,OACfC,SAAU,OACVC,WAAY,OACZvG,MAAO2E,GAAgBC,EAAY4B,KAAqB,MAAGC,KAAe,eAC1EC,cAAe,MACfC,YAAa,MACbC,WAAY,UAEdC,MAAO,SAACT,GAAI,gBACPA,GAAI,IACPU,QAAS,SAEXC,KAAM,SAACX,GAAI,gBACNA,GAAI,IACPU,QAAS,kBACTE,aAAc,MACdC,UAAW,UACXC,OAAQ,OAEVC,SAAU,SAACf,GAAI,gBACVA,GAAI,IACPU,QAAS,MACTM,gBAAiBzC,GAAgBC,EAAY4B,KAAqB,MAAGA,KAAqB,OAC1FS,UAAW,UACXD,aAAc,SAEhBK,WAAY,SAACjB,GAAI,gBACZA,GAAI,IACPgB,gBAAiBzC,GAAgBC,EAAY4B,KAA6B,cAAGA,KAAqB,OAClGM,QAAS,UACTR,SAAU,OACVU,aAAc,MACdC,UAAW,SACXK,WAAY,SACZtH,MAAO2E,GAAgBC,EAAY4B,KAAqB,MAAGC,KAAe,kBAE5Ec,iBAAkB,SAACnB,GAAI,gBAClBA,GAAI,IACPpG,MAAOwG,KAAqB,MAC5BQ,aAAc,MACd9G,WAAY,MACZ4G,QAAS,MACT,UAAW,CACTM,gBAAiBZ,KAAqB,MACtCgB,UAAW,mBAAF,OAAqBhB,KAAqB,OACnDxG,MAAOwG,KAAqB,OAE9B,IAAO,CACLxG,MAAOwG,KAAqB,MAC5BnT,OAAQ,OACRC,MAAO,WAGXmU,OAAQ,SAACrB,EAAM,GAAF,IAAIrB,EAAS,EAATA,UAAS,gBACrBqB,GAAI,IACPd,QAAS,OACTgB,SAAU,OACVQ,QAAS,YACTY,QAAS3C,EAAY,oCAAiC9B,EACtD,UAAW,CACTmE,gBAAiBzC,GAAgBC,EAAY4B,KAA6B,cAAGC,KAAe,yBAC5FzG,MAAOwG,KAAqB,OAE9BY,gBAAiBzC,GAAgBC,EAAY4B,KAA6B,cAAGA,KAAqB,OAClGS,UAAW,UACXK,WAAY,SACZ,eAAgB,CACdN,aAAc,cACdN,cAAe,UAGnBiB,iBAAkB,SAACvB,GAAI,gBAClBA,GAAI,IACPpG,MAAOyG,KAAe,iBAExBmB,eAAgB,SAACxB,GAAI,gBAChBA,GAAI,IACPU,QAAS,MACTe,UAvFEC,eACK,OAEF,OAqFLb,UAAW,YAEbc,QAAS,SAAC3B,GAAI,gBACTA,GAAI,IACPgB,gBAAiBzC,GAAgBC,EAAY4B,KAAsB,OAAGA,KAAqB,OAC3FwB,UAAW,OACXC,YAAatD,GAAgBC,EAAY4B,KAAqB,MAAGA,KAAqB,OACtF,iBAAkB,CAChByB,YAAatD,GAAgBC,EAAY4B,KAAqB,MAAGA,KAAqB,QAGxF,UAAW,CACTyB,YAAatD,GAAgBC,EAAY4B,KAAqB,MAAGA,KAAqB,QAExFgB,UAAW,qBAEbU,YAAa,SAAC9B,GAAI,gBACbA,GAAI,IACPE,SAAU,OACVtG,MAAO2E,GAAgBC,EAAY4B,KAAqB,MAAGA,KAAqB,OAChFG,YAAa,SAEfwB,MAAO,SAAC/B,GAAI,gBACPA,GAAI,IACPE,SAAU,OACVtG,MAAO2E,GAAgBC,EAAY4B,KAAqB,MAAGC,KAAe,eAC1EE,YAAa,UAmFAyB,CADoB,SAAhBrC,GAGnB,OACE,kBAAC,GAAoB,IACnBrO,QAASuO,EACTC,OAAQA,EACRmC,WAAY,CAAEpD,OAAQD,GAAiBI,mBAAiBkD,oBAAqB,kBAAM,MAAM9C,QAASD,GAAeE,qBACjHyC,YAAa,GACbK,kBAAmB,SAAC7O,GAAK,gBAAQxE,EAAE,yBAAwB,YAAIwE,IAC/DyK,GAAG,gCACH9E,MAAOnK,EAAE,8CACLvB,KAKVmS,GAA2BhP,UAAY,CACrCiP,YAAahP,IAAUgL,OAAOoD,WAC9Ba,uBAAwBjP,IAAUiL,MAAMmD,YAG3BW,IC1OAA,GD0OAA,G,+nFErOf,IAAM0C,GAAqB,SAACC,GAC1B,IAAM/Q,EAAU,CACdgR,WAAY,GACZC,eAAe,GAGjB,OAAKF,GAILA,EAAYlQ,SAAQ,SAACqQ,GACnB,IAAQC,EAASD,EAATC,KACJA,IAASrJ,IAAuB,KAClC9H,EAAQgR,WAAWjQ,KAAKmQ,EAAWvJ,OAEnC3H,EAAQmR,IAAQ,EAEdD,EAAWE,QACbpR,EAAQiR,cAAgBjR,EAAQiR,gBAAkBC,EAAWE,MAAMC,eAIhErR,GAfEA,GA0EIsR,GAxDgB,SAACrV,GAC9B,IACEqI,EAMErI,EANFqI,2BACAyM,EAKE9U,EALF8U,YACAQ,EAIEtV,EAJFsV,eACAC,EAGEvV,EAHFuV,uBACAnD,EAEEpS,EAFFoS,YACAC,EACErS,EADFqS,uBAEK9Q,EAAqB,GAAhBC,cAAgB,GAApB,GAEFgU,EAAoBnD,EAAuBpE,KAAI,SAAC6F,GAAM,gBACvDA,GAAM,IACTpI,MAAOnK,EAAEuS,EAAOpI,YAwBlB,OACE,kBAAC+J,GAAA,EAAkB,CACjB5V,UAAU,yBACVuQ,YAAY,0BAEZ,kBAAC,GAA0B,CACzBsF,QAAS,kBAAMrN,GAA2B,IAC1CtC,MAAO+O,EACPa,eAvBe,SAACC,GACpB,IAAMC,EAAW,CACfnK,MAAOkK,EACP7P,MAAO6P,EACPV,KAAMrJ,IAAuB,MAIzBiK,EAAqB,GAAH,UADGhB,GAAe,IACO,CAAEe,IACnDP,EAAeQ,GACf,IAAM/R,EAAU8Q,GAAmBiB,GACnC/R,EAAQiR,eAAgB,EACxBO,EAAuBxR,IAYnBgS,SA9Be,SAACD,GACpBR,EAAeQ,GACf,IAAM/R,EAAU8Q,GAAmBiB,GACnCP,EAAuBxR,IA4BnBqO,YAAaA,EACbC,uBAAwBmD,M,oxBChCjBQ,OA3Cf,SAAqBC,GACnB,OAAO,SAAqBnB,GAC1B,IACMjL,GAAQqM,EADOD,EAAbC,YAEFC,EAA0BrM,IAAUsM,2BAA2BvM,GAE/DwM,EAAmB9G,OAAOC,KAAK2G,GAAyBG,QAAO,SAACrI,EAAKnI,GACzE,MAAwBqQ,EAAwBrQ,GAAxCqP,EAAK,EAALA,MAER,OADAlH,EADmB,EAAJiH,MACHC,EACLlH,IACN,IAGGlK,EAAU,CACdoR,OAAO,EACPH,cAAeF,EAAYE,eAIvBuB,EAAc,GADGzB,EAAfC,YAIRxF,OAAOC,KAAKsF,GAAalQ,SAAQ,SAAC4R,GAChC,IAAMC,EAAcJ,EAAiBG,GACjCC,GACFF,EAAYzR,KAAK2R,EAAYC,WAIjC,IAAMC,EAAeJ,EAAYK,KAAK,KAIjB,KAAjBD,EAKmBE,cACvBC,CAAeH,EAAc5S,GAL3BsC,IAAK0Q,uB,m9CC9BX,SAASxB,GAAuBxR,EAASkS,GACnBe,GAAmBf,EACvCD,CAAYjS,GAGd,ICZekT,GDYyB,SAACjX,GACvC,IAAQqI,EAA+BsE,qBAAW5E,GAA1CM,2BACF4N,EAAQiB,cACR9E,EAAcxI,aAAY,SAACC,GAAK,OAAKC,IAAUqN,eAAetN,MAC9DsM,EAA0BvM,aAAY,SAACC,GAAK,OAAKC,IAAUsM,2BAA2BvM,KAAQG,KAC9FqI,EAAyB9C,OAAO6H,OAAOjB,GAAyBlI,KAAI,SAACoJ,GAAO,gBAC7EA,GAAO,IACVtR,MAAOsR,EAAQnC,UAGjB,OACE,kBAAC,GAAsB,IACrB7M,2BAA4BA,EAC5BkN,uBAAwB,eAACxR,EAAU,UAAH,6CAAG,GAAE,OAAKwR,GAAuBxR,EAASkS,IAC1E7D,YAAaA,EACbC,uBAAwBA,GACpBrS,K,yBEJJsX,I,QAAwB,SAACtX,GAC7B,IACEuX,EAMEvX,EANFuX,UACAxB,EAKE/V,EALF+V,SACAyB,EAIExX,EAJFwX,cACAC,EAGEzX,EAHFyX,SACA9L,EAEE3L,EAFF2L,KACA+L,EACE1X,EADF0X,WAGIC,EA3B6B,SAAC3X,GACpC,IAAQ0X,EAA8D1X,EAA9D0X,WAAYE,EAAkD5X,EAAlD4X,eAAgBC,EAAkC7X,EAAlC6X,aAAcC,EAAoB9X,EAApB8X,UAClD,GADsE9X,EAATkV,OAChDrJ,IAAuB,KAAG,CACrC,IAAMkM,EAA6B,KAAfL,EAAoBI,EAAYJ,EAAWM,MAAMJ,EAAgBC,GAC/EI,EAAwBP,EAAWM,MAAM,EAAGJ,GAC5CM,EAAuBR,EAAWM,MAAMH,GAC9C,OACE,oCACGI,EACD,0BAAMpY,UAAU,gBAAgBkY,GAC/BG,GAIP,OAAOJ,EAaeK,CAA6BnY,GAC7CoY,EAAwBvW,IAAW,0BAA2B,CAAEwW,OAAQZ,IAE9E,OACE,wBAAI5X,UAAWuY,GACb,4BACEvY,UAAU,iCACVmD,QAASwU,EACTc,aAAYZ,EACZa,eAAcd,IAEhB,yBAAK3X,MAAO,CAAE0Y,aAAc,SAC1B,kBAACC,GAAA,EAAM,CACLH,aAAA,UAAeZ,GACfgB,QAASnB,EACTxB,SAAUA,KAGd,yBAAKjW,MAAO,CAAE0Y,aAAc,SAC1B,kBAACrM,EAAA,EAAI,CAACC,MAAOT,KAEf,yBAAK9L,UAAU,gCACZ8X,MAMTL,GAAsBnU,UAAY,CAChCoU,UAAWnU,IAAUI,KACrBuS,SAAU3S,IAAUE,KACpBkU,cAAepU,IAAUE,KACzBmU,SAAUrU,IAAUI,KACpBmI,KAAMvI,IAAUgL,OAChBsJ,WAAYtU,IAAUgL,QAGT/M,WAAMoL,KAAK6K,ICvEXqB,GCGwB,SAAC3Y,GACtC,IACEqJ,EAGErJ,EAHFqJ,aACAqP,EAEE1Y,EAFF0Y,QACAE,EACE5Y,EADF4Y,YAGMtQ,EAA4BqE,qBAAW5E,GAAvCO,wBAEAoP,EAA2ErO,EAA3EqO,WAAYE,EAA+DvO,EAA/DuO,eAAgBC,EAA+CxO,EAA/CwO,aAAcC,EAAiCzO,EAAjCyO,UAAWnM,EAAsBtC,EAAtBsC,KAAMwE,EAAgB9G,EAAhB8G,MAAO+E,EAAS7L,EAAT6L,KAEpEa,EAAW1I,uBAAY,SAACpK,GAC5B2V,EAAY3V,EAAOkN,KAClB,CAACA,EAAOyI,IAELpB,EAAgBnK,uBAAY,WAChChH,IAAKwS,sBAAsBxP,KAC1B,CAACA,IAEJ,OACE,kBAAC,GAAqB,CACpBqO,WAAYA,EACZE,eAAgBA,EAChBC,aAAcA,EACdC,UAAWA,EACXnM,KAAMA,EACNuJ,KAAMA,EACNqC,UAAWmB,EACX3C,SAAUA,EACVyB,cAAeA,EACfC,SAAUnP,IAA4B6H,K,yxEC5B5C,ICLe2I,GDKoB,SAAC9Y,GAClC,IACE2N,EAIE3N,EAJF2N,WACAoL,EAGE/Y,EAHF+Y,cACAC,EAEEhZ,EAFFgZ,4BACAC,EACEjZ,EADFiZ,+BAGM1X,EAAMC,cAAND,EACF2X,EAAqBH,EAAc9K,KAAI,SAACkL,GAAM,OAAKA,EAAOhJ,SACH,KAAfxP,oBAAS,GAAM,GAAtDyY,EAAe,KAAEC,EAAkB,KAE1CvX,qBAAU,WACR,IAAMwX,EAAqBJ,EAAmB5C,QAAO,SAACiD,EAAaC,GACjE,OAAOR,EAA4BQ,IAAiBD,KACnD,GAEHF,EAAmBC,KAClB,CAACN,EAA6BE,IAEjC,IAAMO,EAAkBpM,uBAAY,SAACpK,GACnC,IAAMyV,EAAUzV,EAAMyW,OAAOhB,QAC7BQ,EAAmBtU,SAAQ,SAAC+U,GAC1BX,EAA4BW,GAAejB,KAE7CW,EAAmBX,GACnBO,EAA+B,MAAKD,MACnC,CAACA,EAA6BE,IAE3BN,EAAcvL,uBAAY,SAACpK,EAAOkN,GACtC,IAAMuI,EAAUzV,EAAMyW,OAAOhB,QAC7BM,EAA4B7I,GAASuI,EACrCO,EAA+B,MAAKD,MACnC,CAACA,IAYJ,OACE,yBAAKnZ,UAAU,wCACb,kBAAC4Y,GAAA,EAAM,CACL5Y,UAAU,gDACVyY,aAAA,UAAe/W,EAAE,sBAAqB,YAAIoM,GAC1C+K,QAASU,EACTrD,SAAU,SAAC9S,GACTA,EAAMC,kBACNuW,EAAgBxW,MAGpB,kBAAC4K,EAAA,EAAkB,CAACC,OArBT,WACb,MAAO,GAAP,OACKvM,EAAE,sBAAqB,YAAIoM,IAmBM7N,MAf1B,CACZH,MAAO,QAc6CoO,qBAAoB,UAAKxM,EAAE,sBAAqB,YAAIoM,IACpG,wBAAI9N,UAAU,4BACXkZ,EAAc9K,KAAI,SAAC5E,EAAc8G,GAAK,OACrC,kBAAC,GAAqB,CACpBuI,QAASM,EAA4B3P,EAAa8G,OAClDyI,YAAaA,EACbvP,aAAcA,EACdvD,IAAG,UAAKqK,EAAK,YAAIxC,Y,WEtEhB,I,QACS,wBADT,GAEO,qBAFP,GAGA,c,8mDCuMAiM,OA/Lf,SAAgC5Z,GAC9B,IACE6Z,EAME7Z,EANF6Z,uBACAC,EAKE9Z,EALF8Z,aACAC,EAIE/Z,EAJF+Z,eACAC,EAGEha,EAHFga,6BACAC,EAEEja,EAFFia,gCACAC,EACEla,EADFka,sBAGM3Y,EAAMC,cAAND,EAC4E,KAAZZ,mBAAS,IAAG,GAA7EwZ,EAA4B,KAAEC,EAA+B,KACoB,KAAZzZ,mBAAS,IAAG,GAAjF0Z,EAA8B,KAAEC,EAAiC,KACd,KAAZ3Z,mBAAS,IAAG,GAAnD4Z,EAAe,KAAEC,EAAkB,KAGlCrL,EAAexC,qBAAW5E,GAA1BoH,WAGRrN,qBAAU,WACR,IAAMqY,EAA+B,GACrCN,EAAuBjV,SAAQ,SAACuU,EAAQhJ,GACtC,IAAMxC,EAAawL,EAAOsB,QAC1BtB,EAAOhJ,MAAQA,OACkCb,IAA7C6K,EAA6BxM,GAC/BwM,EAA6BxM,GAAc,CAACwL,GAE5CgB,EAA6BxM,GAAc,GAAH,UAAOwM,EAA6BxM,IAAW,CAAEwL,OAI7FiB,EAAgCD,GAEhC,IAAMO,EAAqB,GAC3Bb,EAAuBjV,SAAQ,SAACmB,EAAOoK,GACrCuK,EAAmBvK,IAAS,KAE9BmK,EAAkCI,KACjC,CAACb,IAGJ/X,qBAAU,WACR,IAAMyY,EAAkBV,EAAuBlR,QAAO,SAACgS,EAAuBxK,GAC5E,OAAOkK,EAA+BlK,MAGxCqK,EAAmBD,KAClB,CAACF,IAGJ,IA6BMO,EACJ,yBAAKtC,aAAY/W,EAAE,sBACjB,uBAAGwB,YAAU,YAAY8X,KAAK,QAAQhb,UAAU,aAAa0B,EAAE,uBAwC7DuZ,EAAgD,IAAlCjB,EAAuBjX,OAErCmY,EAAwBlZ,IAAW,qCAAsC,CAAEmZ,UAAWF,IACtFG,EAAuBpZ,IAAW,sBAAuB,CAAE+N,SAAqC,IAA3B2K,EAAgB3X,SACrFsY,EAAiCrZ,IAAW,oBAAqB,CAAE+N,SAAqC,IAA3B2K,EAAgB3X,SAC7FuY,EAAmCrB,IAAiBsB,KAAgCpB,GAAiCF,IAAiBsB,GAE5I,OACE,oCACE,yBAAKvb,UAAU,qCACZia,IAAiBsB,IAChB,yBAAKtb,MAAO,CAAEub,SAAU,IACtB,kBAAC5b,GAAA,EAAO,CAACC,OAAO,OAAOC,MAAM,UAEhCwb,GACC,oCACE,yBAAKtb,UAAU,oCACb,wBAAIkD,YAAU,YAAY8X,KAAK,QAAQhb,UAAU,aAC9C0B,EAAE,gCACH,0CAAYsY,EAAuBjX,OAAM,QAG7C,kBAACoJ,EAAA,EAAM,CACLnM,UAAWgC,IAAW,CACpB,SAAY0Y,EAAgB3X,OAAS,IAEvCI,QAnDa,WACvB,IAAMsY,EAAuB,GAC7BzB,EAAuBjV,SAAQ,SAACmB,EAAOoK,GACrCmL,EAAqBnL,IAAS,KAEhCmK,EAAkCgB,IA+CxB1L,SAAUkL,EACVpP,MAAOnK,EAAE,sBAEX,kBAACyK,EAAA,EAAM,CACLnM,UAAWgC,IAAW,CACpB,SAAY0Y,EAAgB3X,OAAS,IAEvCgN,SAAUkL,EACV9X,QApDe,WACzB,IAAMsY,EAAuB,GAC7BzB,EAAuBjV,SAAQ,SAACmB,EAAOoK,GACrCmL,EAAqBnL,IAAS,KAEhCmK,EAAkCgB,IAgDxB5P,MAAOnK,EAAE,uBAIjB,yBAAK1B,UAAWkb,EAAuBF,KAAK,QACzCf,IAAiBsB,IAxFtB,yBAAK9C,aAAY/W,EAAE,gCAChBA,EAAE,gCAwFCuY,IAAiBsB,IAAsCN,GAAed,GA7E5E,6BACE,kBAACva,GAAA,EAAO,CAACC,OAAO,OAAOC,MAAM,UA6E1Bma,IAAiBsB,IAA+BN,IAAgBd,GAAgCY,GAC/Fd,IAAiBsB,IAAsCtB,IAAiBsB,KAnHpD,WAC1B,IAAMG,EAAyBhM,OAAOC,KAAK2K,GAC3C,GAAIoB,EAAuB3Y,OAAS,EAAG,CAErC,IAAM6M,EAAgBN,EAAa,CAAEa,iBAAkBuL,EAAuB3Y,QAAW,GACzF,OACE,kBAAC,IAAQ,IACPqN,KAAMsL,EACNrL,YAAa,SAACC,EAAOxC,GACnB,OACE,kBAAC,GAA0B,CACzB7H,IAAKqK,EACLxC,WAAYA,EACZoL,cAAeoB,EAA6BxM,GAC5CqL,4BAA6BqB,EAC7BpB,+BAAgCqB,MAGlC7K,KAiGoG+L,IAE5G,yBAAK3b,UAAU,mCACb,kBAACmM,EAAA,EAAM,CACLhJ,QA9EgB,WACtBoX,EAAgC,IAChCL,KA6EMrO,MAAOnK,EAAE,iBACT1B,UAAU,WAEZ,kBAACmM,EAAA,EAAM,CACL4D,SAAqC,IAA3B2K,EAAgB3X,OAC1B8I,MAAOnK,EAAE,qBACT1B,UAAWob,EACXjY,QA5DwB,WAC9BkX,EAAsBK,MA6DlB,kBAACvO,EAAA,EAAM,CACL4D,SAAqC,IAA3B2K,EAAgB3X,OAC1B8I,MAAOnK,EAAE,kBACT0K,UAAW1K,EAAE,kBACb1B,UAAWqb,EACXlY,QAxEsB,WAC5BiX,EAAgCM,GAChCR,U,wwCCtHJ,IAAQ0B,GAAcxX,OAAOyX,KAAKC,MAA1BF,UAEKG,GAAyB,CACpCtP,YAAa,GACbY,YAAa,IAAIjJ,OAAOyX,KAAKG,YAAYC,MAAM,IAAK,EAAG,GACvDC,UAAW,IAAI9X,OAAOyX,KAAKG,YAAYC,MAAM,IAAK,EAAG,EAAG,GACxDE,KAAM,aAGD,SAASC,GAA2BlD,GAA0D,IAA3CmD,EAAmB,UAAH,6CAAGN,GAEzE1O,EAOEgP,EAPFhP,YACAZ,EAME4P,EANF5P,YACA6P,EAKED,EALFC,UAAS,EAKPD,EAJFF,YAAI,IAAG,cAAW,EAClBD,EAGEG,EAHFH,UACAK,EAEEF,EAFFE,SACAC,EACEH,EADFG,UAEI3T,EAAuBqQ,EAAc9K,KAAI,SAACkL,GAC9C,IAAMmD,EAAY,IAAIrY,OAAOyX,KAAKG,YAAYU,oBAuB9C,OAtBAD,EAAUjN,WAAa8J,EAAOqD,SAC9BF,EAAUG,MAAQtD,EAAOuD,MAAMzO,KAAI,SAAC0O,GAAI,OAAKA,EAAKC,eAClDN,EAAUpP,YAAcA,EACxBoP,EAAUhQ,YAAcA,EACxBgQ,EAAUH,UAAYA,EACtBG,EAAUN,KAAOA,EACjBM,EAAUF,SAAWA,EACjBnY,OAAOyX,KAAKG,YAAYgB,UAAUC,wBACpCR,EAAUF,SAAWnY,OAAOyX,KAAKG,YAAYgB,UAAUC,sBAAsBR,IAE/EA,EAAUP,UAAYA,EACtBO,EAAUD,UAAYA,EACtBC,EAAUS,YAAY5D,EAAO6D,YAC7BV,EAAUpH,KAAOiE,EAAOjE,KACxBoH,EAAUW,OAAS5W,IAAK6W,iBAEJ,SAAhB/D,EAAOjE,MACToH,EAAUa,cAAc,oBAAqBhE,EAAO6D,YAGtDV,EAAUa,cAAc,qBAAsBhE,EAAOjE,MAE9CoH,KAGT,OAAO5T,EAmCM0U,ICtFAA,GDsDf,SAAyCpd,GACvC,IAAQ+Z,EAAmB/Z,EAAnB+Z,eACF3T,EAAWiX,cAMC,KAJyBzT,aACzC,SAACC,GAAK,MAAK,CACTC,IAAUwT,oBAAoBzT,GAC9BC,IAAUyT,kBAAkB1T,MAC3BG,KAAa,GAJXkS,EAAgB,KAAEsB,EAAc,KAWjCvD,EAAkC5M,uBAAY,SAAC0L,GACnD,IAAM0E,EAAOpX,IAAKqX,QAAQjC,GAAUkC,WAC9BC,EAA4BH,GAAQA,EAAKI,SAAYJ,EAAKI,SAAWjC,GAErElT,EAAuBuT,GAA2BlD,EADhCyE,EAAeM,SAAS,aAAe5B,EAAmB0B,GAExDvX,IAAK0X,uBACbC,eAAetV,KAChC,CAACwT,EAAkBsB,IAEtB,OACE,kBAAC,GAAsB,IACrBvD,gCAAiCA,EACjCC,sBAjB0B,SAACnB,GAC7B,IAAMrQ,EAAuBuT,GAA2BlD,EAAe6C,IACvExV,EAASK,YAAgBiC,EAAsBqR,MAgBzC/Z,K,miCE3EV,IAuDeie,GAvDc,SAACje,GAC5B,IAAMoG,EAAWiX,cACiC,KAAZ1c,mBAAS,IAAG,GAA3CmU,EAAW,KAAEQ,EAAc,KAClC,EAAgE3I,qBAAW5E,GAAnEK,EAAuB,EAAvBA,wBAAyBC,EAA0B,EAA1BA,2BAQ/BwR,EAIE7Z,EAJF6Z,uBACAG,EAGEha,EAHFga,6BACAkE,EAEEle,EAFFke,4BACApE,EACE9Z,EADF8Z,aAGIqE,EAAWhK,eAMjB,OACE,oCACGgK,GACC,yBACEte,UAAU,mBAEV,4BACEA,UAAU,uBACVmD,QAZiB,WACzBoD,EAASyB,IAAQuW,aAAa,qBAatB,kBAACjS,EAAA,EAAI,CACHC,MAAM,sBACNvM,UAAU,iBAIlB,kBAAC,GAAsB,CACrBiV,YAAaA,EACbQ,eAAgBA,IAEjBlN,GACC,kBAAC,GAAsB,CACrByR,uBAAwBA,EACxBE,eA1Ce,WACrBzE,EAAe,IACf4I,IACA7V,GAA2B,IAwCrByR,aAAcA,EACdE,6BAA8BA,M,yiCCqCzBqE,IC5FAA,GDKf,WACE,IAAsF,KAA9C1d,mBAASya,IAAqC,GAA/EtB,EAAY,KAAEwE,EAAe,KACoC,KAAZ3d,mBAAS,IAAG,GAAjEkZ,EAAsB,KAAE0E,EAAyB,KAC+B,KAAf5d,oBAAS,GAAM,GAAhFqZ,EAA4B,KAAEwE,EAA+B,KAC9DrI,EAA0BvM,aAAY,SAACC,GAAK,OAAKC,IAAUsM,2BAA2BvM,KAAQG,KAE9FyU,EAAiB/Y,mBAAQ,WAC7B,OAAO6J,OAAOC,KAAK2G,GAAyBG,QAAO,SAACrI,EAAKnI,GACvD,MAA8BqQ,EAAwBrQ,GAA9CqP,EAAK,EAALA,MAAOD,EAAI,EAAJA,KAAMvJ,EAAI,EAAJA,KAKrB,OAJAsC,EAAIiH,GAAQ,CACVC,QACAxJ,QAEKsC,IACN,MACF,CAACkI,IAEEuI,EAAkBrR,uBAAY,SAAC8L,GAEnC,IAAQrB,EAAcqB,EAAdrB,UAGF6G,EAFoBpP,OAAOC,KAAKiP,GAEDG,MAAK,SAAC9Y,GAEzC,OADkB2Y,EAAe3Y,GAAzBqP,MACK0J,KAAK/G,MAIpBqB,EAAOjE,UAAsB5F,IAAfqP,EAA2B9S,IAAuB,KAAI8S,EAEpE,IAA0E,GAAjCF,EAAetF,EAAOjE,OAAS,IAAhEvJ,YAAI,IAAG,wBAAqB,EAEpC,OADAwN,EAAOxN,KAAOA,EACPwN,IACN,CAACsF,IAEEP,EAA8B7Q,uBAAY,WAC9CkR,EAA0B,IAC1BlY,IAAK0Q,qBACLyH,GAAgC,MAyClC,OAtCA1c,qBAAU,WACR,SAASgd,EAAuBnY,GAC9B,IAAMoY,EAAgBpY,EAAQsH,IAAIyQ,GAClCF,GAAgC,GAChCD,EAA0BQ,GAI5B,OADA1Y,IAAKkD,iBAAiB,uBAAwBuV,GACvC,WACLzY,IAAKmD,oBAAoB,uBAAwBsV,MAElD,CAAChF,IAEJhY,qBAAU,WACR,SAASkd,EAA6BC,GAChCA,QAEFX,EAAgBlD,IACP6D,EACTX,EAAgBlD,KAEhBkD,EAAgBlD,IAIhB8D,YAAW,WACTV,GAAgC,KAC/B,MAMP,OAFAnY,IAAKkD,iBAAiB,mBAAoByV,GAEnC,WACL3Y,IAAKmD,oBAAoB,mBAAoBwV,MAE9C,IAEI,CACLnF,yBACAG,+BACAkE,8BACApE,iBExFWqF,GCEsB,WACnC,MAKId,KAJFxE,EAAsB,EAAtBA,uBACAG,EAA4B,EAA5BA,6BACAkE,EAA2B,EAA3BA,4BACApE,EAAY,EAAZA,aAGF,OACE,kBAAC,GAAoB,CACnBD,uBAAwBA,EACxBG,6BAA8BA,EAC9BkE,4BAA6BA,EAC7BpE,aAAcA,K,ixECFb,IAAMsF,GAA0B,SAACpf,GACtC,IAiBC,KAVG4J,aACF,SAACC,GAAK,MAAK,CACTC,IAAUuV,cAAcxV,EAAO,kBAC/BC,IAAUwV,kBAAkBzV,EAAO,kBACnCC,IAAUC,uBAAuBF,GACjCC,IAAUyV,oBAAoB1V,GAC9BC,IAAU0V,gCAAgC3V,GAC1CC,IAAUsM,2BAA2BvM,MAEvCG,KACD,GAhBCyV,EAAM,KACNC,EAAU,KACV/V,EAAmB,KACnB4V,EAAmB,KACnBI,EAA4B,KAC5BxJ,EAAuB,KAanBgI,EAAWhK,eAETyL,EAAyD5f,EAAzD4f,yBAA0BC,EAA+B7f,EAA/B6f,cAAezP,EAAgBpQ,EAAhBoQ,YAE3CtB,EAA2BpJ,mBAAQ,WAUvC,OAAO,SATsB6J,OAAOC,KAAK2G,GAAyBG,QAAO,SAACrI,EAAKnI,GAC7E,MAA8BqQ,EAAwBrQ,GAA9C4F,EAAK,EAALA,MAAOwJ,EAAI,EAAJA,KAAMvJ,EAAI,EAAJA,KAKrB,OAJAsC,EAAIiH,GAAQ,CACVxJ,QACAC,QAEKsC,IACN,KAEkC6R,OACpC,CAAC3J,IAME/P,EAAWiX,cAaX0C,EAAsB,WAC1B,IAAMC,EAAkBH,EAAgBzP,EAAc,iBACtDhK,EAASyB,IAAQuW,aAAa4B,KAe1BlgB,EAAQ+f,IAAmBN,GAAuBpB,EACpD,GACA,CAAExe,MAAO,GAAF,OAAKgK,EAAmB,MAAMsW,SAAU,GAAF,OAAKtW,EAAmB,OAEjEvB,EAA4BuE,qBAAW5E,GAAvCK,wBAE2C,KAAfzH,oBAAS,GAAM,GAA5Cuf,EAAU,KAAEC,EAAa,KAWhC,GATAre,qBAAU,WACR,IAAMse,EAAUlB,YAAW,WACzBiB,GAAeV,KACd,KACH,OAAO,WACLY,aAAaD,MAEd,CAACX,IAEAC,IAAgBD,GAAUS,IAAeL,EAC3C,OAAO,KAGT,IAAMS,EAAmBT,EAAgBzP,EAAc,iBAEvD,OACE,kBAACqF,GAAA,EAAkB,CAACrF,YAAakQ,EAAkBzgB,UAAU,uBAAuBC,MAAOA,IACvFyf,GAAuBpB,IAnCxB0B,GACC,yBAAKhgB,UAAU,mBACb,yBAAKA,UAAU,uBAAuBmD,QAAS+c,GAC7C,kBAAC5T,EAAA,EAAI,CAACC,MAAM,sBAAsBvM,UAAU,iBAiClD,kBAAC,GAAoB,OACnBuI,GACA,kBAAC,EAAc,CACbM,qBAAsBkX,EACtB9Q,yBAA0BA,EAC1BF,mBA5DmB,WACzB,IAAM2R,EAA0B,WAC9B,IAAMC,EAA2BX,EAAgBE,EAAsB,aACvE3Z,EAASK,YAAgBmZ,EAA0BY,KAEjDb,EACFA,EAA6BC,EAA0BW,GAEvDA,KAqDI1R,8BAlE8B,WACpCxI,IAAKoH,kBAAkBmS,QAwE3BR,GAAwBjc,UAAY,CAClCyc,yBAA0Bxc,IAAUiL,MACpCwR,cAAezc,IAAUI,KACzB4M,YAAahN,IAAUgL,QAGzBgR,GAAwBrO,aAAe,CACrC8O,eAAe,EACfzP,YAAa,IAGf,IAQeqQ,GAR6B,SAACzgB,GAC3C,OACE,kBAAC,EAAsB,KACrB,kBAAC,GAA4BA,KCzIpBygB", "file": "chunks/chunk.31.js", "sourcesContent": ["import React from 'react';\nimport './Spinner.scss';\n\nconst Spinner = ({ height = '50px', width = '54px' }) => {\n  const spinnerStyle = {\n    height,\n    width,\n  };\n\n  return (\n    <div className=\"spinner\" style={spinnerStyle}></div>\n  );\n};\n\nexport default Spinner;", "import Spinner from './Spinner';\n\nexport default Spinner;\n", "import React, { useState, useEffect, useLayoutEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\n\nimport './NoteTextPreview.scss';\n\nfunction NoteTextPreview(props) {\n  /* This replace is to remove the break line that the React Quill component add into the text */\n  const text = props.children.replace(/\\n$/, '');\n  const {\n    panelWidth,\n    linesToBreak,\n    renderRichText,\n    richTextStyle,\n    resize,\n    style,\n    /* If text being previewed is a comment it gets a darker font color */\n    comment = false,\n    beforeContent = () => {},\n  } = props;\n  const [expanded, setExpand] = useState(false);\n  const [previewElementWidth, setPreviewWidth] = useState(null);\n  const [charsPerLine, setCharsperLine] = useState(null);\n  const [showPrompt, setShowPrompt] = useState(false);\n  const ref = React.useRef(null);// Must import this way in order to mock it properly\n  const { t } = useTranslation();\n\n  const onClickHandler = (event) => {\n    event.stopPropagation();\n    setExpand(!expanded);\n    resize && resize();\n  };\n\n  const textToDisplay = expanded ? text : text.substring(0, charsPerLine * linesToBreak);\n  const prompt = expanded ? t('action.showLess') : t('action.showMore');\n  const noteTextPreviewClass = classNames('note-text-preview', { 'preview-comment': comment });\n\n  useEffect(() => {\n    const textNodeWidth = ref.current.clientWidth;\n    setPreviewWidth(textNodeWidth);\n  }, [panelWidth]);\n\n  // useLayoutEffect to avoid a flicker before we get the final text prop\n  useLayoutEffect(() => {\n    function getTextWidth(text) {\n      const canvas = document.createElement('canvas');\n      const context = canvas.getContext('2d');\n      context.font = '13px sans-serif';\n\n      return context.measureText(text).width;\n    }\n\n    const textWidth = getTextWidth(text);\n    const averageCharWidth = textWidth / text.length;\n    const charsPerLine = Math.floor(previewElementWidth / averageCharWidth);\n    setCharsperLine(charsPerLine);\n\n    const totalLines = textWidth / previewElementWidth;\n    setShowPrompt(totalLines > linesToBreak);\n  }, [text, previewElementWidth]);\n\n  return (\n    <div className={noteTextPreviewClass} ref={ref} style={style} aria-live=\"polite\">\n      {beforeContent()}\n      {renderRichText && richTextStyle\n        ? renderRichText(textToDisplay, richTextStyle, 0)\n        : textToDisplay} {showPrompt && <button className=\"note-text-preview-prompt\" onClick={onClickHandler}>{prompt}</button>}\n    </div>\n  );\n}\n\nNoteTextPreview.propTypes = {\n  panelWidth: PropTypes.number,\n  linesToBreak: PropTypes.number,\n  renderRichText: PropTypes.func,\n  richTextStyle: PropTypes.any,\n  resize: PropTypes.func,\n  style: PropTypes.any,\n  comment: PropTypes.bool,\n  beforeContent: PropTypes.func,\n};\n\nexport default NoteTextPreview;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./NoteTextPreview.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.note-text-preview>*{pointer-events:all}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./Spinner.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useMemo } from 'react';\nimport createCache from '@emotion/cache';\nimport { CacheProvider } from '@emotion/react'; // Emotion's CacheProvider\nimport getRootNode from 'helpers/getRootNode';\n\nconst ReactSelectWebComponentProvider = ({ children }) => {\n  // Memoize the cache so it's created only once per container (shadow DOM root)\n  const emotionCache = useMemo(() => {\n    const container = getRootNode(); // Ensure we get the root node of the shadow DOM\n    return createCache({\n      key: 'wv-react-select-emotion', // Unique key for the cache\n      container, // Set the container to the shadow DOM root for styles insertion\n    });\n  }, []);\n\n  // Render with CacheProvider for emotion when inside a web component\n  return window.isApryseWebViewerWebComponent ? (\n    <CacheProvider value={emotionCache}>\n      {children}\n    </CacheProvider>\n  ) : (\n    children\n  );\n};\n\nexport default ReactSelectWebComponentProvider;", "import ReactSelectWebComponentProvider from './ReactSelectWebComponentProvider';\n\nexport default ReactSelectWebComponentProvider;", "import core from 'core';\nimport i18next from 'i18next';\n\nimport actions from 'actions';\nimport { fireError } from 'helpers/fireEvent';\nimport downloadPdf from 'helpers/downloadPdf';\n\nfunction noop() { }\n\nexport default (annotations, onRedactionCompleted = noop, activeDocumentViewerKey = 1) => (dispatch) => {\n  if (core.isWebViewerServerDocument()) {\n    // when are using Webviewer Server, it'll download the redacted document\n    return webViewerServerApply(annotations, dispatch, activeDocumentViewerKey);\n  }\n  return webViewerApply(annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey);\n};\n\nconst webViewerServerApply = (annotations, dispatch, activeDocumentViewerKey) => core.applyRedactions(annotations, activeDocumentViewerKey).then((results) => {\n  if (results && results.url) {\n    return downloadPdf(dispatch, {\n      filename: 'redacted.pdf',\n      includeAnnotations: true,\n      externalURL: results.url,\n    });\n  }\n  console.warn('WebViewer Server did not return a valid result');\n});\n\nconst webViewerApply = (annotations, onRedactionCompleted, dispatch, activeDocumentViewerKey) => {\n  const message = i18next.t('warning.redaction.applyMessage');\n  const title = i18next.t('warning.redaction.applyTile');\n  const confirmBtnText = i18next.t('action.apply');\n\n  const warning = {\n    message,\n    title,\n    confirmBtnText,\n    onConfirm: () => {\n      core.applyRedactions(annotations, activeDocumentViewerKey)\n        .then(() => {\n          onRedactionCompleted();\n        })\n        .catch((err) => fireError(err));\n      return Promise.resolve();\n    },\n  };\n\n  return dispatch(actions.showWarningMessage(warning));\n};\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RedactionPanel{padding:16px 16px 0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.RedactionPanel .marked-redaction-counter{flex:0 1 19px;margin-top:24px;margin-bottom:12px;font-size:16px}.RedactionPanel .marked-redaction-counter span{font-weight:400}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}.RedactionPanel .no-marked-redactions{display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.RedactionPanel .no-marked-redactions .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}.RedactionPanel .no-marked-redactions .empty-icon,.RedactionPanel .no-marked-redactions .empty-icon svg{width:65px;height:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon,.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}.RedactionPanel .no-marked-redactions .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.RedactionPanel .redaction-panel-controls{flex:0 0 57px;margin-left:-16px;padding-top:13px;border-top:1px solid var(--divider);display:flex;background-color:var(--component-background);width:inherit;justify-content:flex-end;padding-right:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked{padding:0;border:none;background-color:transparent;background-color:var(--primary-button);color:var(--primary-button-text);border-radius:4px;height:32px;width:90px}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked:hover:not(.disabled){background-color:var(--primary-button-hover)}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled{opacity:.5}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled span{color:var(--primary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}.RedactionPanel .redaction-panel-controls .clear-all-marked{padding:0;color:var(--secondary-button-text);background-color:transparent;border:none;height:32px;width:70px;margin-right:8px;cursor:pointer}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .clear-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .clear-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .clear-all-marked:hover:not(.disabled){color:var(--secondary-button-hover)}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled{opacity:.5;cursor:not-allowed}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled span{color:var(--secondary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}.RedactionPanel .redaction-group-container{flex:1 1 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-group-container{margin-right:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-group-container{margin-right:4px}}.RedactionPanel button.focus-visible,.RedactionPanel button:focus-visible{outline:var(--focus-visible-outline)}.ModularPanel-container .RedactionPanel{height:100%;padding:unset}.ModularPanel-container .RedactionPanel .redaction-panel-controls{margin-right:-16px;padding-bottom:16px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionItem.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-item{display:flex;align-items:center;padding:12px 16px;position:relative}.redaction-item:hover{background-color:var(--view-header-button-hover);cursor:pointer}.redaction-item.focus-visible,.redaction-item:focus-visible{outline:var(--focus-visible-outline)}.redaction-item.modular-ui:hover:not(:disabled):not(.disabled){background-color:transparent;box-shadow:inset 0 0 0 1px var(--hover-border)}.redaction-item .redaction-item-button{position:absolute;width:100%;height:100%;top:0;left:0}.redaction-item-selected{background-color:var(--view-header-button-active)!important}.redaction-item-selected.modular-ui{box-shadow:inset 0 0 0 1px var(--focus-border)}.redaction-item-info{flex:1;padding-left:18px;padding-right:20px}.redaction-item-preview{font-size:13px;color:var(--text-color)}.redaction-item-date-author{font-size:10px;color:var(--faded-text)}.redaction-item-label-text{font-size:10px;margin:2px 0}.redaction-item-delete.customUI:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionPageGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-items{margin:8px 2px 1px;background-color:var(--component-background);box-shadow:0 0 3px 0 var(--box-shadow);border-radius:4px;padding:0}.redaction-items>:first-child{padding-top:16px;border-radius:4px 4px 0 0}.redaction-items>:last-child{padding-bottom:16px;border-radius:0 0 4px 4px}.redaction-items>:only-child{padding-top:16px;padding-bottom:16px;border-radius:4px}.redaction-page-group{padding-top:12px;padding-bottom:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}.redaction-page-group h2{margin:0}.redaction-page-group h2 button{margin:0;font-size:13px;font-weight:400;color:var(--faded-text)}.redaction-page-group-header{display:flex;justify-content:space-between;align-items:baseline}.expand-arrow{height:16px;width:16px;display:flex;align-items:center;cursor:pointer}.expand-arrow .Icon{width:12px;height:12px}.expand-arrow.Button.custom-ui.icon-only:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CreatableMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".creatable-multi-select-label{display:inline-block;font-weight:700;margin-bottom:var(--padding-small)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchMultiSelect.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-multi-select{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-multi-select{font-size:13px}}.redaction-search-multi-select-search-icon-container{height:28px;align-self:flex-start;display:flex;align-items:center;margin:0 var(--padding-tiny)}.redaction-search-multi-select-search-icon-container .Icon{width:16px;height:16px}.custom-remove-button{display:flex;align-items:center}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}.RedactionSearchOverlay input{width:100%;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResult.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-search-result{display:flex;align-items:center;padding:12px;background-color:var(--component-background);border:1px solid transparent;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);margin:8px 0;position:relative}.redaction-search-result .redaction-search-result-button{position:absolute;width:100%;height:100%;top:0;left:0;background:transparent;border:none;cursor:pointer}.redaction-search-result .Icon svg{transform:scale(1.2);padding-top:2px}.redaction-search-result .search-value{word-break:break-all;color:var(--secondary-button-text);font-weight:700}.redaction-search-result.active{background-color:transparent!important;border:1px solid var(--focus-border)}.redaction-search-result-info{font-size:13px;color:var(--text-color)}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResultGroup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".redaction-search-results-page-number{display:flex;align-items:start;padding:8px 12px 4px}.redaction-search-results-page-number .redaction-search-results-page-number-checkbox{position:absolute;left:24px}.redaction-search-results-page-number .collapsible-page-group-header button{font-size:13px;font-weight:400;color:var(--faded-text);margin:0 0 0 32px;width:calc(100% - 32px)}.redaction-search-results-page-number .redaction-search-results{padding:0;margin:0}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RedactionSearchResults.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-search-counter-controls{display:flex;flex-direction:row;margin-top:36px;font-size:var(--font-size-default);padding:16px;border:1px solid var(--lighter-border);background-color:var(--gray-0);border-radius:4px 4px 0 0;max-height:50px;min-height:50px;grid-column-gap:var(--padding-medium);-moz-column-gap:var(--padding-medium);column-gap:var(--padding-medium)}.redaction-search-counter-controls .redaction-search-results-counter{flex:2 1 auto}.redaction-search-counter-controls .redaction-search-results-counter span{font-weight:400}.redaction-search-counter-controls .spinner{margin:auto;flex:3 1 \\\"25px\\\"}.redaction-search-counter-controls button{padding:0;background-color:transparent;flex:1 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;height:100%;white-space:nowrap}:host(:not([data-tabbing=true])) .redaction-search-counter-controls button,html:not([data-tabbing=true]) .redaction-search-counter-controls button{outline:none}.redaction-search-counter-controls button:hover:not(:disabled){color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}.redaction-search-counter-controls button.disabled{opacity:.5}.redaction-search-counter-controls button.disabled span{color:var(--secondary-button-text)}.redaction-search-results-container{flex:1 1 auto;background-color:var(--gray-2);color:var(--faded-text);font-size:13px;border-left:1px solid var(--lighter-border);border-right:1px solid var(--lighter-border);display:flex;flex-direction:column}.redaction-search-no-results,.redaction-search-results-container.emptyList{justify-content:center;align-items:center}.redaction-search-panel-controls{display:flex;flex-direction:row;flex:0 1 52px;padding:12px;background-color:var(--component-background);border:1px solid var(--lighter-border);margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}.redaction-search-panel-controls button{border:none;background-color:transparent;height:28px;padding:0 16px;cursor:pointer}:host(:not([data-tabbing=true])) .redaction-search-panel-controls button,html:not([data-tabbing=true]) .redaction-search-panel-controls button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls button{height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls button{height:32px}}.redaction-search-panel-controls .Button{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}.redaction-search-panel-controls .Button.cancel{flex:2 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;margin-right:20px}.redaction-search-panel-controls .Button.cancel:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected{flex:1 1 auto;border:1px solid var(--secondary-button-text);border-radius:4px;margin-right:8px}.redaction-search-panel-controls .Button.redact-all-selected span{color:var(--secondary-button-text)}.redaction-search-panel-controls .Button.redact-all-selected.disabled{opacity:.5}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled){border-color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.mark-all-selected{flex:2 1 auto;background-color:var(--primary-button)!important;border:1px solid var(--primary-button);border-radius:4px}.redaction-search-panel-controls .Button.mark-all-selected span{color:var(--primary-button-text)}.redaction-search-panel-controls .Button.mark-all-selected:hover:not(.disabled){border-color:var(--primary-button-hover);background-color:var(--primary-button-hover)!important}.redaction-search-panel-controls .Button.mark-all-selected.disabled{opacity:.5}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport core from 'core';\n\n\nconst RedactionPanelContext = React.createContext();\n\nconst RedactionPanelProvider = ({ children }) => {\n  const [selectedRedactionItemId, setSelectedRedactionItemId] = useState(null);\n  const [isRedactionSearchActive, setIsRedactionSearchActive] = useState(false);\n  const [activeSearchResultIndex, setActiveSearchResultIndex] = useState(-1);\n\n  useEffect(() => {\n    const onAnnotationSelected = (annotations, action) => {\n      if (action === 'selected') {\n        const redactionAnnotations = annotations.filter((annotation) => annotation.Subject === 'Redact');\n        // If multiple ones selected, we only use the first one\n        const selectedAnnotationId = redactionAnnotations.length > 0 ? redactionAnnotations[0].Id : null;\n        setSelectedRedactionItemId(selectedAnnotationId);\n      } else {\n        setSelectedRedactionItemId(null);\n      }\n    };\n\n    const activeSearchResultChanged = (newActiveSearchResult) => {\n      if (!newActiveSearchResult) {\n        return;\n      }\n      const coreSearchResults = core.getPageSearchResults() || [];\n      const newActiveSearchResultIndex = coreSearchResults.findIndex((searchResult) => {\n        return core.isSearchResultEqual(searchResult, newActiveSearchResult);\n      });\n      setActiveSearchResultIndex(newActiveSearchResultIndex);\n    };\n\n    core.addEventListener('annotationSelected', onAnnotationSelected);\n    core.addEventListener('activeSearchResultChanged', activeSearchResultChanged);\n\n    return () => {\n      core.removeEventListener('annotationSelected', onAnnotationSelected);\n      core.removeEventListener('activeSearchResultChanged', activeSearchResultChanged);\n    };\n  }, []);\n\n  const value = {\n    selectedRedactionItemId,\n    setSelectedRedactionItemId,\n    isRedactionSearchActive,\n    setIsRedactionSearchActive,\n    activeSearchResultIndex\n  };\n\n  return <RedactionPanelContext.Provider value={value}>{children}</RedactionPanelContext.Provider>;\n};\n\nexport { RedactionPanelProvider, RedactionPanelContext };\n", "import React from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport NoteTextPreview from '../NoteTextPreview/NoteTextPreview';\n\nconst RedactionTextPreviewContainer = (props) => {\n  const [redactionPanelWidth] = useSelector(\n    (state) => [\n      selectors.getRedactionPanelWidth(state),\n    ],\n    shallowEqual,\n  );\n\n  return (\n    <NoteTextPreview {...props} panelWidth={redactionPanelWidth} comment />\n  );\n};\n\nexport default RedactionTextPreviewContainer;", "import RedactionTextPreviewContainer from './RedactionTextPreviewContainer';\n\nexport default RedactionTextPreviewContainer;", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'src/components/Icon';\nimport getLatestActivityDate from 'helpers/getLatestActivityDate';\nimport dayjs from 'dayjs';\nimport Button from 'components/Button';\nimport './RedactionItem.scss';\nimport RedactionTextPreview from 'components/RedactionTextPreview';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\n\nconst RedactionItem = (props) => {\n  // Remove if we get rid of legacy UI along with stylesheet changes\n  const [isCustomUI] = useSelector(\n    (state) => [\n      selectors.getFeatureFlags(state)?.customizableUI,\n    ]\n  );\n  const {\n    iconColor,\n    annotation,\n    author,\n    dateFormat,\n    language,\n    onRedactionItemDelete,\n    onRedactionItemSelection,\n    textPreview,\n    isSelected,\n    timezone,\n  } = props;\n  const { t } = useTranslation();\n\n  let date = getLatestActivityDate(annotation);\n\n  if (timezone) {\n    const datetimeStr = date.toLocaleString('en-US', { timeZone: timezone });\n    date = new Date(datetimeStr);\n  }\n\n  const formattedDate = date ? dayjs(date).locale(language).format(dateFormat) : t('option.notesPanel.noteContent.noDate');\n  const dateAndAuthor = `${author} - ${formattedDate}`;\n  const className = classNames('redaction-item', { 'redaction-item-selected': isSelected }, { 'modular-ui': isCustomUI });\n  const {\n    label,\n    icon = 'icon-text-redaction', // Default icon if none provided\n    redactionType\n  } = annotation;\n\n  let redactionPreview;\n\n  if (redactionType === redactionTypeMap['TEXT']) {\n    redactionPreview = (\n      <RedactionTextPreview linesToBreak={2}>\n        {textPreview}\n      </RedactionTextPreview>);\n  } else if (\n    redactionType === redactionTypeMap['FULL_PAGE']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME']\n    || redactionType === redactionTypeMap['REGION']\n    || redactionType === redactionTypeMap['AUDIO_REDACTION']\n    || redactionType === redactionTypeMap['FULL_VIDEO_FRAME_AND_AUDIO']\n  ) {\n    redactionPreview = t(label);\n  } else {\n    redactionPreview = annotation.getContents();\n  }\n\n  return (\n    <li className={className}>\n      <Button\n        className='redaction-item-button'\n        onClick={onRedactionItemSelection}\n        ariaLabel={`${redactionPreview} ${dateAndAuthor} ${t('action.select')}`}\n        ariaCurrent={isSelected}\n      />\n      <div className=\"redaction-icon-container\">\n        <Icon glyph={icon} color={iconColor} />\n      </div>\n      <div className=\"redaction-item-info\">\n        <div className=\"redaction-item-preview\">\n          {redactionPreview}\n        </div>\n        {\n          annotation.OverlayText ?\n            <div className=\"redaction-item-label-text\">\n              {t('option.stylePopup.labelText')}: {annotation.OverlayText}\n            </div> : null\n        }\n        <div className=\"redaction-item-date-author\">\n          {dateAndAuthor}\n        </div>\n      </div>\n      <Button\n        className='redaction-item-delete'\n        style={{ marginLeft: 'auto' }}\n        img={'icon-close'}\n        onClick={onRedactionItemDelete}\n        ariaLabel={`${redactionPreview} ${dateAndAuthor} ${t('action.delete')}`}\n      />\n    </li>\n  );\n};\n\nexport default React.memo(RedactionItem);", "import React, { useCallback, useContext } from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport RedactionItem from './RedactionItem';\nimport core from 'core';\nimport { RedactionPanelContext } from '../../RedactionPanel/RedactionPanelContext';\n\nconst RedactionItemContainer = (props) => {\n  const { annotation } = props;\n\n  const { selectedRedactionItemId, setSelectedRedactionItemId } = useContext(RedactionPanelContext);\n\n  const [\n    dateFormat,\n    language,\n    customNoteSelectionFunction,\n    timezone,\n  ] = useSelector(\n    (state) => [\n      selectors.getNoteDateFormat(state),\n      selectors.getCurrentLanguage(state),\n      selectors.getCustomNoteSelectionFunction(state),\n      selectors.getTimezone(state)\n    ],\n    shallowEqual,\n  );\n\n  const textPreview = annotation.getCustomData('trn-annot-preview');\n  const iconColor = annotation.StrokeColor.toString();\n  const author = core.getDisplayAuthor(annotation['Author']);\n\n  const onRedactionItemSelection = useCallback(() => {\n    customNoteSelectionFunction && customNoteSelectionFunction(annotation);\n    core.deselectAllAnnotations();\n    core.selectAnnotation(annotation);\n    core.jumpToAnnotation(annotation);\n    setSelectedRedactionItemId(annotation.Id);\n  }, [annotation]);\n\n  const onRedactionItemDelete = useCallback(() => {\n    core.deleteAnnotations([annotation]);\n  }, [annotation]);\n\n  return (\n    <RedactionItem\n      dateFormat={dateFormat}\n      language={language}\n      timezone={timezone}\n      author={author}\n      annotation={annotation}\n      iconColor={iconColor}\n      textPreview={textPreview}\n      onRedactionItemDelete={onRedactionItemDelete}\n      onRedactionItemSelection={onRedactionItemSelection}\n      isSelected={selectedRedactionItemId === annotation.Id}\n    />\n  );\n};\n\nexport default RedactionItemContainer;", "import RedactionItemContainer from './RedactionItemContainer';\n\nexport default RedactionItemContainer;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionItem from './RedactionItem';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport { useTranslation } from 'react-i18next';\n\nimport './RedactionPageGroup.scss';\n\nconst RedactionPageGroup = (props) => {\n  const {\n    pageNumber,\n    redactionItems,\n  } = props;\n\n  const { t } = useTranslation();\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  return (\n    <CollapsibleSection\n      className=\"redaction-page-group\"\n      header={header}\n      expansionDescription={`${t('option.shared.page')} ${pageNumber} ${t('redactionPanel.redactionItems')}`}\n      headingLevel={2}\n    >\n      <ul className=\"redaction-items\">\n        {redactionItems.map((redactionItem) => (\n          <RedactionItem\n            annotation={redactionItem}\n            key={`${redactionItem.Id}-${pageNumber}`}\n          />\n        ))}\n      </ul>\n    </CollapsibleSection>\n  );\n};\n\nRedactionPageGroup.propTypes = {\n  pageNumber: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  redactionItems: PropTypes.array,\n};\n\nexport default RedactionPageGroup;", "import React, { useEffect, useState } from 'react';\nimport RedactionPageGroup from './RedactionPageGroup';\nimport { getSortStrategies } from 'constants/sortStrategies';\n\nconst RedactionPageGroupContainer = (props) => {\n  // Putting this in the container in case we want to allow users to change sort strategies\n  // which are stored in the application state\n  const { redactionItems } = props;\n  // Sorting strategies can be applied to any list of annotations\n  const [sortedRedactionItems, setSortedRedactionItems] = useState([]);\n  useEffect(() => {\n    setSortedRedactionItems(getSortStrategies()['position'].getSortedNotes(redactionItems));\n  }, [redactionItems]);\n  return (\n    <RedactionPageGroup\n      redactionItems={sortedRedactionItems}\n      {...props}\n    />\n  );\n};\n\nexport default RedactionPageGroupContainer;", "import RedactionPageGroupContainer from './RedactionPageGroupContainer';\n\nexport default RedactionPageGroupContainer;", "import React, { useEffect, useState, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport classNames from 'classnames';\nimport Icon from 'components/Icon';\nimport { Virtuoso } from 'react-virtuoso';\nimport { RedactionPanelContext } from './RedactionPanelContext';\nimport { mapAnnotationToRedactionType } from 'constants/redactionTypes';\nimport Button from 'components/Button';\n\nimport './RedactionPanel.scss';\nimport RedactionPageGroup from '../RedactionPageGroup';\nimport DataElements from 'src/constants/dataElement';\nimport useFocusHandler from 'hooks/useFocusHandler';\n\nconst RedactionPanel = (props) => {\n  const {\n    redactionAnnotations,\n    applyAllRedactions,\n    deleteAllRedactionAnnotations,\n    redactionTypesDictionary,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionPageMap, setRedactionPageMap] = useState({});\n  const [redactionPageNumbers, setRedactionPageNumbers] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n  useEffect(() => {\n    const redactionPageMap = {};\n    redactionAnnotations.forEach((annotation) => {\n      const redactionType = mapAnnotationToRedactionType(annotation);\n      const { label, icon } = redactionTypesDictionary[redactionType] || {\n        icon: 'icon-tool-redaction-area',\n        label: 'redactionPanel.redactionItem.regionRedaction'\n      };\n      annotation.label = label;\n      annotation.icon = icon;\n      annotation.redactionType = redactionType;\n\n      const pageNumber = annotation.PageNumber;\n      if (redactionPageMap[pageNumber] === undefined) {\n        redactionPageMap[pageNumber] = [annotation];\n      } else {\n        redactionPageMap[pageNumber] = [annotation, ...redactionPageMap[pageNumber]];\n      }\n    });\n\n    setRedactionPageMap(redactionPageMap);\n    setRedactionPageNumbers(Object.keys(redactionPageMap));\n  }, [redactionAnnotations]);\n\n  const renderRedactionPageGroups = () => {\n    // Needed for the tests to actually render a list of results\n    // Not needed for the actual app; if we set it it kills performance when there are a lot of annotations\n    const testModeProps = isTestMode ? { initialItemCount: redactionPageNumbers.length } : {};\n    return (\n      <div className=\"redaction-group-container\">\n        <Virtuoso\n          data={redactionPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionPageGroup\n                key={index}\n                pageNumber={pageNumber}\n                redactionItems={redactionPageMap[pageNumber]}\n              />);\n          }}\n          {...testModeProps}\n        />\n      </div>\n    );\n  };\n\n  const noRedactionAnnotations = (\n    <div className=\"no-marked-redactions\">\n      <div>\n        <Icon className=\"empty-icon\" glyph=\"icon-no-marked-redactions\" />\n      </div>\n      <div className=\"msg\">{t('redactionPanel.noMarkedRedactions')}</div>\n    </div>\n  );\n\n  const redactAllButtonClassName = classNames('redact-all-marked', { disabled: redactionAnnotations.length === 0 });\n  const clearAllButtonClassName = classNames('clear-all-marked', { disabled: redactionAnnotations.length === 0 });\n\n  const applyAllRedactionsWithFocusHandler = useFocusHandler(applyAllRedactions);\n  return (\n    <>\n      <h2 className=\"marked-redaction-counter\">\n        {t('redactionPanel.redactionCounter')}\n        <span>\n          {` (${redactionAnnotations.length})`}\n        </span>\n      </h2>\n      {redactionPageNumbers.length > 0 ? renderRedactionPageGroups() : noRedactionAnnotations}\n      <div className=\"redaction-panel-controls\">\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={clearAllButtonClassName}\n          onClick={deleteAllRedactionAnnotations}\n          label={t('redactionPanel.clearMarked')}\n        />\n        <Button\n          disabled={redactionAnnotations.length === 0}\n          className={redactAllButtonClassName}\n          onClick={applyAllRedactionsWithFocusHandler}\n          dataElement={DataElements.REDACT_ALL_MARKED_BUTTON}\n          label={t('redactionPanel.redactAllMarked')}\n        />\n      </div>\n    </>\n  );\n};\n\nexport default RedactionPanel;", "import React from 'react';\nimport CreatableSelect from 'react-select/creatable';\nimport ReactSelectWebComponentProvider from '../ReactSelectWebComponentProvider';\nimport './CreatableMultiSelect.scss';\nimport PropTypes from 'prop-types';\n\nconst CreatableMultiSelect = ({ id, label, ...rest }) => {\n  return (\n    <ReactSelectWebComponentProvider>\n      <label htmlFor={id} className=\"creatable-multi-select-label\">{label}</label>\n      <div onTouchEndCapture={(e) => e.stopPropagation()}>\n        <CreatableSelect\n          isMulti\n          {...rest}\n          inputId={id}\n        />\n      </div>\n    </ReactSelectWebComponentProvider>\n  );\n};\n\nCreatableMultiSelect.propTypes = {\n  id: PropTypes.string,\n  label: PropTypes.string,\n};\n\nCreatableMultiSelect.defaultProps = {\n  id: '',\n  label: '',\n};\n\nexport default CreatableMultiSelect;", "import CreatableMultiSelect from './CreatableMultiSelect';\n\nexport default CreatableMultiSelect;", "import React from 'react';\nimport { components } from 'react-select';\nimport Icon from 'components/Icon';\nimport CreatableMultiSelect from 'components/CreatableMultiSelect';\nimport { useTranslation } from 'react-i18next';\nimport { COMMON_COLORS, CUSTOM_UI_VARS } from 'constants/commonColors';\nimport PropTypes from 'prop-types';\nimport './RedactionSearchMultiSelect.scss';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst getColorForMode = (isDarkMode, darkModeColor, lightModeColor, isFocused = true) => {\n  if (isFocused) {\n    return isDarkMode ? darkModeColor : lightModeColor;\n  }\n  return 'transparent';\n};\n\nconst getContainerMaxHeight = () => {\n  if (isMobileSize()) {\n    return '55px';\n  }\n  return '70px';\n};\n\nconst getStyles = (isDarkMode) => ({\n  groupHeading: (base) => ({\n    ...base,\n    textTransform: 'none',\n    fontSize: '13px',\n    fontWeight: 'bold',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingBottom: '8px',\n    paddingLeft: '8px',\n    paddingTop: '10px',\n  }),\n  group: (base) => ({\n    ...base,\n    padding: '0px',\n  }),\n  menu: (base) => ({\n    ...base,\n    padding: '0px 0px 0px 0px',\n    borderRadius: '4px',\n    overflowY: 'visible',\n    margin: '0'\n  }),\n  menuList: (base) => ({\n    ...base,\n    padding: '0px',\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['black'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    borderRadius: '4px',\n  }),\n  multiValue: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray2']),\n    padding: '2px 8px',\n    fontSize: '13px',\n    borderRadius: '4px',\n    overflowY: 'hidden',\n    whiteSpace: 'nowrap',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color'])\n  }),\n  multiValueRemove: (base) => ({\n    ...base,\n    color: COMMON_COLORS['gray6'],\n    borderRadius: '4px',\n    marginLeft: '4px',\n    padding: '0px',\n    '&:hover': {\n      backgroundColor: COMMON_COLORS['gray2'],\n      boxShadow: `inset 0 0 0 1px ${COMMON_COLORS['blue6']}`,\n      color: COMMON_COLORS['gray6'],\n    },\n    'svg': {\n      color: COMMON_COLORS['gray6'],\n      height: '16px',\n      width: '16px',\n    },\n  }),\n  option: (base, { isFocused }) => ({\n    ...base,\n    display: 'flex',\n    fontSize: '13px',\n    padding: '6px 8px 0',\n    outline: isFocused ? 'var(--focus-visible-outline)' : undefined,\n    '&:hover': {\n      backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], CUSTOM_UI_VARS['primary-button-hover']),\n      color: COMMON_COLORS['gray0'],\n    },\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['blue1DarkMode'], COMMON_COLORS['gray0']),\n    overflowY: 'visible',\n    whiteSpace: 'normal',\n    '&:last-child': {\n      borderRadius: '0 0 4px 4px',\n      paddingBottom: '6px',\n    },\n  }),\n  noOptionsMessage: (base) => ({\n    ...base,\n    color: CUSTOM_UI_VARS['text-color'],\n  }),\n  valueContainer: (base) => ({\n    ...base,\n    padding: '1px',\n    maxHeight: getContainerMaxHeight(),\n    overflowY: 'scroll',\n  }),\n  control: (base) => ({\n    ...base,\n    backgroundColor: getColorForMode(isDarkMode, COMMON_COLORS['gray10'], COMMON_COLORS['white']),\n    minHeight: '28px',\n    borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    '&:focus-within': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['blue5']),\n    },\n    // override the default border color on focus\n    '&:hover': {\n      borderColor: getColorForMode(isDarkMode, COMMON_COLORS['gray8'], COMMON_COLORS['gray6']),\n    },\n    boxShadow: 'none !important',\n  }),\n  placeholder: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['gray7'], COMMON_COLORS['gray5']),\n    paddingLeft: '4px',\n  }),\n  input: (base) => ({\n    ...base,\n    fontSize: '13px',\n    color: getColorForMode(isDarkMode, COMMON_COLORS['white'], CUSTOM_UI_VARS['text-color']),\n    paddingLeft: '3px',\n  }),\n});\n\nconst RedactionOption = (props) => {\n  const { data } = props;\n  return (\n    <components.Option {...props}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {data.label}\n    </components.Option>\n  );\n};\n\nRedactionOption.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst MultiValueLabel = ({ data }) => {\n  return (\n    <div tabIndex={0} style={{ display: 'flex', height: '18px' }}>\n      {data.icon && <Icon glyph={data.icon} />}\n      {data.label}\n    </div>\n  );\n};\n\nMultiValueLabel.propTypes = {\n  data: PropTypes.object.isRequired,\n};\n\nconst CustomControl = ({ children, ...props }) => (\n  <components.Control {...props}>\n    <div className=\"redaction-search-multi-select-search-icon-container\">\n      <Icon className=\"redaction-search-multi-select-search-icon\" glyph=\"icon-header-search\" />\n    </div>\n    {children}\n  </components.Control>\n);\n\nconst MultiValueRemove = (props) => {\n  const { t } = useTranslation();\n  const label = props.data.label;\n\n  const handleKeyDown = (event) => {\n    if (event.key === 'Enter' || event.key === ' ') {\n      // Trigger the removal action when Enter or Space is pressed\n      event.stopPropagation();\n      props.innerProps.onClick();\n    }\n  };\n\n  const updatedProps = {\n    ...props,\n    innerProps: {\n      ...props.innerProps,\n      'aria-label': `${t('action.remove')} ${label}`,\n      tabIndex: 0,\n      onKeyDown: handleKeyDown, // Add the keydown handler for accessibility\n    }\n  };\n\n  return (\n    <components.MultiValueRemove {...updatedProps} />\n  );\n};\n\nCustomControl.propTypes = {\n  children: PropTypes.node,\n};\n\nconst RedactionSearchMultiSelect = (props) => {\n  const { t } = useTranslation();\n  const { activeTheme, redactionSearchOptions } = props;\n\n  const redactionGroup = [\n    {\n      label: t('redactionPanel.search.pattern'),\n      options: redactionSearchOptions,\n    },\n  ];\n\n  const isDarkMode = activeTheme === 'dark';\n  const styles = getStyles(isDarkMode);\n\n  return (\n    <CreatableMultiSelect\n      options={redactionGroup}\n      styles={styles}\n      components={{ Option: RedactionOption, MultiValueLabel, IndicatorsContainer: () => null, Control: CustomControl, MultiValueRemove }}\n      placeholder={''}\n      formatCreateLabel={(value) => `${t('component.searchPanel')} ${value}`}\n      id=\"redaction-search-multi-select\"\n      label={t('redactionPanel.redactionSearchPlaceholder')}\n      {...props}\n    />\n  );\n};\n\nRedactionSearchMultiSelect.propTypes = {\n  activeTheme: PropTypes.string.isRequired,\n  redactionSearchOptions: PropTypes.array.isRequired,\n};\n\nexport default RedactionSearchMultiSelect;\n", "import RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\n\nexport default RedactionSearchMultiSelect;", "import React from 'react';\nimport DataElementWrapper from '../DataElementWrapper';\nimport RedactionSearchMultiSelect from './RedactionSearchMultiSelect';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport './RedactionSearchOverlay.scss';\nimport { useTranslation } from 'react-i18next';\n\nconst buildSearchOptions = (searchTerms) => {\n  const options = {\n    textSearch: [],\n    caseSensitive: true,\n  };\n\n  if (!searchTerms) {\n    return options;\n  }\n\n  searchTerms.forEach((searchTerm) => {\n    const { type } = searchTerm;\n    if (type === redactionTypeMap['TEXT']) {\n      options.textSearch.push(searchTerm.label);\n    } else {\n      options[type] = true;\n    }\n    if (searchTerm.regex) {\n      options.caseSensitive = options.caseSensitive && !searchTerm.regex.ignoreCase;\n    }\n  });\n\n  return options;\n};\n\nconst RedactionSearchOverlay = (props) => {\n  const {\n    setIsRedactionSearchActive,\n    searchTerms,\n    setSearchTerms,\n    executeRedactionSearch,\n    activeTheme,\n    redactionSearchOptions,\n  } = props;\n  const [t] = useTranslation();\n\n  const translatedOptions = redactionSearchOptions.map((option) => ({\n    ...option,\n    label: t(option.label),\n  }));\n\n  const handleChange = (updatedSearchTerms) => {\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    executeRedactionSearch(options);\n  };\n\n  const handleCreate = (newValue) => {\n    const textTerm = {\n      label: newValue,\n      value: newValue,\n      type: redactionTypeMap['TEXT']\n    };\n    // Initially search terms are null so we safeguard against this\n    const nonNullSearchTerms = searchTerms || [];\n    const updatedSearchTerms = [...nonNullSearchTerms, textTerm];\n    setSearchTerms(updatedSearchTerms);\n    const options = buildSearchOptions(updatedSearchTerms);\n    options.caseSensitive = false;\n    executeRedactionSearch(options);\n  };\n\n  return (\n    <DataElementWrapper\n      className=\"RedactionSearchOverlay\"\n      dataElement=\"redactionSearchOverlay\"\n    >\n      <RedactionSearchMultiSelect\n        onFocus={() => setIsRedactionSearchActive(true)}\n        value={searchTerms}\n        onCreateOption={handleCreate}\n        onChange={handleChange}\n        activeTheme={activeTheme}\n        redactionSearchOptions={translatedOptions}\n      />\n\n    </DataElementWrapper>\n\n  );\n};\n\nexport default RedactionSearchOverlay;", "import searchTextFullFactory from '../apis/searchTextFull';\nimport selectors from 'selectors';\nimport core from 'core';\n\n\nfunction multiSearch(store) {\n  return function multiSearch(searchTerms) {\n    const { getState } = store;\n    const state = getState();\n    const redactionSearchPatterns = selectors.getRedactionSearchPatterns(state);\n    // collect all regexes into an array\n    const searchOptionsMap = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type } = redactionSearchPatterns[key];\n      map[type] = regex;\n      return map;\n    }, {});\n\n\n    const options = {\n      regex: true,\n      caseSensitive: searchTerms.caseSensitive,\n    };\n\n    const { textSearch } = searchTerms;\n    const searchArray = [...textSearch];\n\n    // Now we can map type to regex\n    Object.keys(searchTerms).forEach((searchType) => {\n      const searchRegex = searchOptionsMap[searchType];\n      if (searchRegex) {\n        searchArray.push(searchRegex.source);\n      }\n    });\n\n    const searchString = searchArray.join('|');\n\n    // If search string is empty we return and clear searches or we send the search logic\n    // into an infinte loop\n    if (searchString === '') {\n      core.clearSearchResults();\n      return;\n    }\n\n    const searchTextFull = searchTextFullFactory();\n    searchTextFull(searchString, options);\n  };\n}\n\nexport default multiSearch;", "import React, { useContext } from 'react';\nimport { useStore, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\n\nimport RedactionSearchOverlay from './RedactionSearchOverlay';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport multiSearchFactory from '../../helpers/multiSearch';\n\n\nfunction executeRedactionSearch(options, store) {\n  const multiSearch = multiSearchFactory(store);\n  multiSearch(options);\n}\n\nconst RedactionSearchOverlayContainer = (props) => {\n  const { setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const store = useStore();\n  const activeTheme = useSelector((state) => selectors.getActiveTheme(state));\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n  const redactionSearchOptions = Object.values(redactionSearchPatterns).map((pattern) => ({\n    ...pattern,\n    value: pattern.type,\n  }));\n\n  return (\n    <RedactionSearchOverlay\n      setIsRedactionSearchActive={setIsRedactionSearchActive}\n      executeRedactionSearch={(options = {}) => executeRedactionSearch(options, store)}\n      activeTheme={activeTheme}\n      redactionSearchOptions={redactionSearchOptions}\n      {...props}\n    />);\n};\n\nexport default RedactionSearchOverlayContainer;", "import RedactionSearchOverlayContainer from './RedactionSearchOverlayContainer';\n\nexport default RedactionSearchOverlayContainer;", "import React from 'react';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport Icon from 'components/Icon';\nimport './RedactionSearchResult.scss';\nimport classNames from 'classnames';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport PropTypes from 'prop-types';\n\n// Alternatively wrap this in useCallback and declare inside component\nconst displayRedactionSearchResult = (props) => {\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, type } = props;\n  if (type === redactionTypeMap['TEXT']) {\n    const searchValue = ambientStr === '' ? resultStr : ambientStr.slice(resultStrStart, resultStrEnd);\n    const textBeforeSearchValue = ambientStr.slice(0, resultStrStart);\n    const textAfterSearchValue = ambientStr.slice(resultStrEnd);\n    return (\n      <>\n        {textBeforeSearchValue}\n        <span className=\"search-value\">{searchValue}</span>\n        {textAfterSearchValue}\n      </>\n    );\n  }\n  return resultStr;\n};\n\nconst RedactionSearchResult = (props) => {\n  const {\n    isChecked,\n    onChange,\n    onClickResult,\n    isActive,\n    icon,\n    ambientStr\n  } = props;\n\n  const displayResult = displayRedactionSearchResult(props);\n  const searchResultClassname = classNames('redaction-search-result', { active: isActive });\n\n  return (\n    <li className={searchResultClassname}>\n      <button\n        className='redaction-search-result-button'\n        onClick={onClickResult}\n        aria-label={ambientStr}\n        aria-current={isActive}\n      ></button>\n      <div style={{ paddingRight: '14px' }}>\n        <Choice\n          aria-label={`${ambientStr}`}\n          checked={isChecked}\n          onChange={onChange}\n        />\n      </div>\n      <div style={{ paddingRight: '14px' }}>\n        <Icon glyph={icon} />\n      </div>\n      <div className=\"redaction-search-result-info\">\n        {displayResult}\n      </div>\n    </li>\n  );\n};\n\nRedactionSearchResult.propTypes = {\n  isChecked: PropTypes.bool,\n  onChange: PropTypes.func,\n  onClickResult: PropTypes.func,\n  isActive: PropTypes.bool,\n  icon: PropTypes.string,\n  ambientStr: PropTypes.string,\n};\n\nexport default React.memo(RedactionSearchResult);", "import RedactionSearchResultContainer from './RedactionSearchResultContainer';\n\nexport default RedactionSearchResultContainer;", "import React, { useCallback, useContext } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport core from 'core';\n\nconst RedactionSearchResultContainer = (props) => {\n  const {\n    searchResult,\n    checked,\n    checkResult,\n  } = props;\n\n  const { activeSearchResultIndex } = useContext(RedactionPanelContext);\n\n  const { ambientStr, resultStrStart, resultStrEnd, resultStr, icon, index, type } = searchResult;\n\n  const onChange = useCallback((event) => {\n    checkResult(event, index);\n  }, [index, checkResult]);\n\n  const onClickResult = useCallback(() => {\n    core.setActiveSearchResult(searchResult);\n  }, [searchResult]);\n\n  return (\n    <RedactionSearchResult\n      ambientStr={ambientStr}\n      resultStrStart={resultStrStart}\n      resultStrEnd={resultStrEnd}\n      resultStr={resultStr}\n      icon={icon}\n      type={type}\n      isChecked={checked}\n      onChange={onChange}\n      onClickResult={onClickResult}\n      isActive={activeSearchResultIndex === index}\n    />\n  );\n};\n\nexport default RedactionSearchResultContainer;\n", "import React, { useCallback, useEffect, useState } from 'react';\nimport RedactionSearchResult from './RedactionSearchResult';\nimport { Choice } from '@pdftron/webviewer-react-toolkit';\nimport { useTranslation } from 'react-i18next';\nimport CollapsibleSection from 'components/CollapsibleSection';\nimport './RedactionSearchResultGroup.scss';\n\nconst RedactionSearchResultGroup = (props) => {\n  const {\n    pageNumber,\n    searchResults,\n    selectedSearchResultIndexes,\n    setSelectedSearchResultIndexes,\n  } = props;\n\n  const { t } = useTranslation();\n  const groupResultIndexes = searchResults.map((result) => result.index);\n  const [allItemsChecked, setAllItemsChecked] = useState(false);\n\n  useEffect(() => {\n    const allResultsSelected = groupResultIndexes.reduce((allSelected, currentIndex) => {\n      return selectedSearchResultIndexes[currentIndex] && allSelected;\n    }, true);\n\n    setAllItemsChecked(allResultsSelected);\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkAllResults = useCallback((event) => {\n    const checked = event.target.checked;\n    groupResultIndexes.forEach((resultIndex) => {\n      selectedSearchResultIndexes[resultIndex] = checked;\n    });\n    setAllItemsChecked(checked);\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes, groupResultIndexes]);\n\n  const checkResult = useCallback((event, index) => {\n    const checked = event.target.checked;\n    selectedSearchResultIndexes[index] = checked;\n    setSelectedSearchResultIndexes({ ...selectedSearchResultIndexes });\n  }, [selectedSearchResultIndexes]);\n\n  const header = () => {\n    return (\n      `${t('option.shared.page')} ${pageNumber}`\n    );\n  };\n\n  const style = {\n    width: '100%',\n  };\n\n  return (\n    <div className=\"redaction-search-results-page-number\">\n      <Choice\n        className=\"redaction-search-results-page-number-checkbox\"\n        aria-label={`${t('option.shared.page')} ${pageNumber}`}\n        checked={allItemsChecked}\n        onChange={(event) => {\n          event.stopPropagation();\n          checkAllResults(event);\n        }}\n      />\n      <CollapsibleSection header={header} style={style} expansionDescription={`${t('option.shared.page')} ${pageNumber}`}>\n        <ul className=\"redaction-search-results\">\n          {searchResults.map((searchResult, index) => (\n            <RedactionSearchResult\n              checked={selectedSearchResultIndexes[searchResult.index]}\n              checkResult={checkResult}\n              searchResult={searchResult}\n              key={`${index}-${pageNumber}`}\n            />)\n          )}\n        </ul>\n      </CollapsibleSection>\n    </div>\n  );\n};\n\nexport default RedactionSearchResultGroup;", "import RedactionSearchResultGroup from './RedactionSearchResultGroup';\n\nexport default RedactionSearchResultGroup;", "export default {\n  SEARCH_NOT_INITIATED: 'SEARCH_NOT_INITIATED',\n  SEARCH_IN_PROGRESS: 'SEARCH_IN_PROGRESS',\n  SEARCH_DONE: 'SEARCH_DONE',\n};", "import React, { useState, useEffect, useContext } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport RedactionSearchResultGroup from 'components/RedactionSearchResultGroup';\nimport Spinner from 'components/Spinner';\nimport './RedactionSearchResults.scss';\nimport classNames from 'classnames';\nimport { Virtuoso } from 'react-virtuoso';\nimport SearchStatus from 'constants/searchStatus';\nimport { RedactionPanelContext } from '../RedactionPanel/RedactionPanelContext';\nimport Button from 'components/Button';\n\nfunction RedactionSearchResults(props) {\n  const {\n    redactionSearchResults,\n    searchStatus,\n    onCancelSearch,\n    isProcessingRedactionResults,\n    markSelectedResultsForRedaction,\n    redactSelectedResults,\n  } = props;\n\n  const { t } = useTranslation();\n  const [redactionSearchResultPageMap, setRedactionSearchResultPageMap] = useState({});\n  const [selectedSearchResultIndexesMap, setSelectedSearchResultIndexesMap] = useState({});\n  const [selectedIndexes, setSelectedIndexes] = useState([]);\n  // The following prop is needed only for the tests to actually render a list of results\n  // it only is ever injected in the tests\n  const { isTestMode } = useContext(RedactionPanelContext);\n\n\n  useEffect(() => {\n    const redactionSearchResultPageMap = {};\n    redactionSearchResults.forEach((result, index) => {\n      const pageNumber = result.pageNum;\n      result.index = index;\n      if (redactionSearchResultPageMap[pageNumber] === undefined) {\n        redactionSearchResultPageMap[pageNumber] = [result];\n      } else {\n        redactionSearchResultPageMap[pageNumber] = [...redactionSearchResultPageMap[pageNumber], result];\n      }\n    });\n\n    setRedactionSearchResultPageMap(redactionSearchResultPageMap);\n\n    const selectedIndexesMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      selectedIndexesMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(selectedIndexesMap);\n  }, [redactionSearchResults]);\n\n\n  useEffect(() => {\n    const selectedIndexes = redactionSearchResults.filter((redactionSearchResult, index) => {\n      return selectedSearchResultIndexesMap[index];\n    });\n\n    setSelectedIndexes(selectedIndexes);\n  }, [selectedSearchResultIndexesMap]);\n\n\n  const renderSearchResults = () => {\n    const resultGroupPageNumbers = Object.keys(redactionSearchResultPageMap);\n    if (resultGroupPageNumbers.length > 0) {\n      // Needed for the tests to actually render a list of results\n      const testModeProps = isTestMode ? { initialItemCount: resultGroupPageNumbers.length } : {};\n      return (\n        <Virtuoso\n          data={resultGroupPageNumbers}\n          itemContent={(index, pageNumber) => {\n            return (\n              <RedactionSearchResultGroup\n                key={index}\n                pageNumber={pageNumber}\n                searchResults={redactionSearchResultPageMap[pageNumber]}\n                selectedSearchResultIndexes={selectedSearchResultIndexesMap}\n                setSelectedSearchResultIndexes={setSelectedSearchResultIndexesMap}\n              />);\n          }}\n          {...testModeProps}\n        />);\n    }\n  };\n\n  const renderStartSearch = () => (\n    <div aria-label={t('redactionPanel.search.start')}>\n      {t('redactionPanel.search.start')}\n    </div>\n  );\n\n  const noResults = (\n    <div aria-label={t('message.noResults')}>\n      <p aria-live=\"assertive\" role=\"alert\" className=\"no-margin\">{t('message.noResults')}</p>\n    </div>\n  );\n\n  const renderSearchInProgress = () => (\n    <div >\n      <Spinner height=\"25px\" width=\"25px\" />\n    </div>\n  );\n\n  const onCancelHandler = () => {\n    setRedactionSearchResultPageMap({});\n    onCancelSearch();\n  };\n\n  const selectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = true;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const unselectAllResults = () => {\n    const searchResultIndexMap = {};\n    redactionSearchResults.forEach((value, index) => {\n      searchResultIndexMap[index] = false;\n    });\n    setSelectedSearchResultIndexesMap(searchResultIndexMap);\n  };\n\n  const onMarkAllForRedaction = () => {\n    markSelectedResultsForRedaction(selectedIndexes);\n    onCancelSearch();\n  };\n\n  const onRedactSelectedResults = () => {\n    redactSelectedResults(selectedIndexes);\n  };\n\n  const isEmptyList = redactionSearchResults.length === 0;\n\n  const resultsContainerClass = classNames('redaction-search-results-container', { emptyList: isEmptyList });\n  const redactAllButtonClass = classNames('redact-all-selected', { disabled: selectedIndexes.length === 0 });\n  const markAllForRedactionButtonClass = classNames('mark-all-selected', { disabled: selectedIndexes.length === 0 });\n  const shouldShowResultsCounterOptions = (searchStatus === SearchStatus['SEARCH_DONE'] && !isProcessingRedactionResults) || searchStatus === SearchStatus['SEARCH_NOT_INITIATED'];\n\n  return (\n    <>\n      <div className=\"redaction-search-counter-controls\">\n        {searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && (\n          <div style={{ flexGrow: 1 }}>\n            <Spinner height=\"18px\" width=\"18px\" />\n          </div>)}\n        {shouldShowResultsCounterOptions && (\n          <>\n            <div className=\"redaction-search-results-counter\">\n              <h4 aria-live=\"assertive\" role=\"alert\" className=\"no-margin\">\n                {t('redactionPanel.searchResults')}\n                <span>{` (${redactionSearchResults.length})`}</span>\n              </h4>\n            </div>\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              onClick={selectAllResults}\n              disabled={isEmptyList}\n              label={t('action.selectAll')}\n            />\n            <Button\n              className={classNames({\n                'inactive': selectedIndexes.length < 1\n              })}\n              disabled={isEmptyList}\n              onClick={unselectAllResults}\n              label={t('action.unselect')}\n            />\n          </>)}\n      </div>\n      <div className={resultsContainerClass} role=\"list\">\n        {searchStatus === SearchStatus['SEARCH_NOT_INITIATED'] && renderStartSearch()}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] && isEmptyList && isProcessingRedactionResults) && renderSearchInProgress()}\n        {searchStatus === SearchStatus['SEARCH_DONE'] && isEmptyList && !isProcessingRedactionResults && noResults}\n        {(searchStatus === SearchStatus['SEARCH_IN_PROGRESS'] || searchStatus === SearchStatus['SEARCH_DONE']) && renderSearchResults()}\n      </div>\n      <div className=\"redaction-search-panel-controls\" >\n        <Button\n          onClick={onCancelHandler}\n          label={t('action.cancel')}\n          className=\"cancel\"\n        />\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('annotation.redact')}\n          className={redactAllButtonClass}\n          onClick={onRedactSelectedResults}\n        />\n        <Button\n          disabled={selectedIndexes.length === 0}\n          label={t('action.addMark')}\n          ariaLabel={t('action.addMark')}\n          className={markAllForRedactionButtonClass}\n          onClick={onMarkAllForRedaction}\n        />\n      </div >\n    </>\n  );\n}\n\nexport default RedactionSearchResults;\n", "import React, { useCallback } from 'react';\nimport RedactionSearchResults from './RedactionSearchResults';\nimport { useDispatch, useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport applyRedactions from 'helpers/applyRedactions';\nimport core from 'core';\n\nconst { ToolNames } = window.Core.Tools;\n\nexport const defaultRedactionStyles = {\n  OverlayText: '',\n  StrokeColor: new window.Core.Annotations.Color(255, 0, 0),\n  TextColor: new window.Core.Annotations.Color(255, 0, 0, 1),\n  Font: 'Helvetica',\n};\n\nexport function createRedactionAnnotations(searchResults, activeToolStyles = defaultRedactionStyles) {\n  const {\n    StrokeColor,\n    OverlayText,\n    FillColor,\n    Font = 'Helvetica',\n    TextColor,\n    FontSize,\n    TextAlign,\n  } = activeToolStyles;\n  const redactionAnnotations = searchResults.map((result) => {\n    const redaction = new window.Core.Annotations.RedactionAnnotation();\n    redaction.PageNumber = result.page_num;\n    redaction.Quads = result.quads.map((quad) => quad.getPoints());\n    redaction.StrokeColor = StrokeColor;\n    redaction.OverlayText = OverlayText;\n    redaction.FillColor = FillColor;\n    redaction.Font = Font;\n    redaction.FontSize = FontSize;\n    if (window.Core.Annotations.Utilities.calculateAutoFontSize) {\n      redaction.FontSize = window.Core.Annotations.Utilities.calculateAutoFontSize(redaction);\n    }\n    redaction.TextColor = TextColor;\n    redaction.TextAlign = TextAlign;\n    redaction.setContents(result.result_str);\n    redaction.type = result.type;\n    redaction.Author = core.getCurrentUser();\n\n    if (result.type === 'text') {\n      redaction.setCustomData('trn-annot-preview', result.result_str);\n    }\n\n    redaction.setCustomData('trn-redaction-type', result.type);\n\n    return redaction;\n  });\n\n  return redactionAnnotations;\n}\n\nfunction RedactionSearchResultsContainer(props) {\n  const { onCancelSearch } = props;\n  const dispatch = useDispatch();\n  // activeToolStyles is an object so we do a shallowEqual to check equality\n  const [activeToolStyles, activeToolName] = useSelector(\n    (state) => [\n      selectors.getActiveToolStyles(state),\n      selectors.getActiveToolName(state)\n    ], shallowEqual);\n\n  const redactSelectedResults = (searchResults) => {\n    const redactionAnnotations = createRedactionAnnotations(searchResults, defaultRedactionStyles);\n    dispatch(applyRedactions(redactionAnnotations, onCancelSearch));\n  };\n\n  const markSelectedResultsForRedaction = useCallback((searchResults) => {\n    const tool = core.getTool(ToolNames.REDACTION);\n    const alternativeDefaultStyles = (tool && tool.defaults) ? tool.defaults : defaultRedactionStyles;\n    const redactionStyles = activeToolName.includes('Redaction') ? activeToolStyles : alternativeDefaultStyles;\n    const redactionAnnotations = createRedactionAnnotations(searchResults, redactionStyles);\n    const annotationManager = core.getAnnotationManager();\n    annotationManager.addAnnotations(redactionAnnotations);\n  }, [activeToolStyles, activeToolName]);\n\n  return (\n    <RedactionSearchResults\n      markSelectedResultsForRedaction={markSelectedResultsForRedaction}\n      redactSelectedResults={redactSelectedResults}\n      {...props}\n    />);\n}\n\nexport default RedactionSearchResultsContainer;", "import RedactionSearchResultsContainer from './RedactionSearchResultsContainer';\n\nexport default RedactionSearchResultsContainer;", "import React, { useContext, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport RedactionSearchOverlay from 'src/components/RedactionSearchOverlay';\nimport { RedactionPanelContext } from 'components/RedactionPanel/RedactionPanelContext';\nimport RedactionSearchResults from 'components/RedactionSearchResults';\nimport Icon from 'components/Icon';\nimport { isMobileSize } from 'helpers/getDeviceSize';\n\nconst RedactionSearchPanel = (props) => {\n  const dispatch = useDispatch();\n  const [searchTerms, setSearchTerms] = useState([]);\n  const { isRedactionSearchActive, setIsRedactionSearchActive } = useContext(RedactionPanelContext);\n  const onCancelSearch = () => {\n    setSearchTerms([]);\n    clearRedactionSearchResults();\n    setIsRedactionSearchActive(false);\n  };\n\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = props;\n\n  const isMobile = isMobileSize();\n\n  const onCloseButtonClick = () => {\n    dispatch(actions.closeElement('redactionPanel'));\n  };\n\n  return (\n    <>\n      {isMobile &&\n        <div\n          className=\"close-container\"\n        >\n          <button\n            className=\"close-icon-container\"\n            onClick={onCloseButtonClick}\n          >\n            <Icon\n              glyph=\"ic_close_black_24px\"\n              className=\"close-icon\"\n            />\n          </button>\n        </div>}\n      <RedactionSearchOverlay\n        searchTerms={searchTerms}\n        setSearchTerms={setSearchTerms}\n      />\n      {isRedactionSearchActive &&\n        <RedactionSearchResults\n          redactionSearchResults={redactionSearchResults}\n          onCancelSearch={onCancelSearch}\n          searchStatus={searchStatus}\n          isProcessingRedactionResults={isProcessingRedactionResults}\n        />\n      }\n    </>\n  );\n};\n\nexport default RedactionSearchPanel;", "import { useEffect, useState, useCallback, useMemo } from 'react';\nimport { useSelector, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { redactionTypeMap } from 'constants/redactionTypes';\nimport SearchStatus from 'constants/searchStatus';\n\nfunction useOnRedactionSearchCompleted() {\n  const [searchStatus, setSearchStatus] = useState(SearchStatus['SEARCH_NOT_INITIATED']);\n  const [redactionSearchResults, setRedactionSearchResults] = useState([]);\n  const [isProcessingRedactionResults, setIsProcessingRedactionResults] = useState(false);\n  const redactionSearchPatterns = useSelector((state) => selectors.getRedactionSearchPatterns(state), shallowEqual);\n\n  const searchPatterns = useMemo(() => {\n    return Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { regex, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        regex,\n        icon\n      };\n      return map;\n    }, {});\n  }, [redactionSearchPatterns]);\n\n  const mapResultToType = useCallback((result) => {\n    // Iterate through the patterns and return the first match\n    const { resultStr } = result;\n    const searchPatternKeys = Object.keys(searchPatterns);\n\n    const resultType = searchPatternKeys.find((key) => {\n      const { regex } = searchPatterns[key];\n      return regex.test(resultStr);\n    });\n\n    // If it didn't match any of the patterns, return the default type which is text\n    result.type = resultType === undefined ? redactionTypeMap['TEXT'] : resultType;\n    // And also set the icon to display in the panel. If no icon provided use the text icon\n    const { icon = 'icon-text-redaction' } = searchPatterns[result.type] || {};\n    result.icon = icon;\n    return result;\n  }, [searchPatterns]);// Dependency is an object but it is memoized so it will not re-create unless the patterns change\n\n  const clearRedactionSearchResults = useCallback(() => {\n    setRedactionSearchResults([]);\n    core.clearSearchResults();\n    setIsProcessingRedactionResults(false);\n  });\n\n  useEffect(() => {\n    function onSearchResultsChanged(results) {\n      const mappedResults = results.map(mapResultToType);\n      setIsProcessingRedactionResults(true);\n      setRedactionSearchResults(mappedResults);\n    }\n\n    core.addEventListener('searchResultsChanged', onSearchResultsChanged);\n    return () => {\n      core.removeEventListener('searchResultsChanged', onSearchResultsChanged);\n    };\n  }, [searchStatus]);\n\n  useEffect(() => {\n    function searchInProgressEventHandler(isSearching) {\n      if (isSearching === undefined || isSearching === null) {\n        // if isSearching is not passed at all, we consider that to mean that search was reset to original state\n        setSearchStatus(SearchStatus['SEARCH_NOT_INITIATED']);\n      } else if (isSearching) {\n        setSearchStatus(SearchStatus['SEARCH_IN_PROGRESS']);\n      } else {\n        setSearchStatus(SearchStatus['SEARCH_DONE']);\n        // Need a timeout due to timing issue as SEARCH_DONE is fired\n        // before final call to onSearchResultsChanged, otherwise we briefly show\n        // the NO RESULTS message before showing actual results.\n        setTimeout(() => {\n          setIsProcessingRedactionResults(false);\n        }, 100);\n      }\n    }\n\n    core.addEventListener('searchInProgress', searchInProgressEventHandler);\n\n    return () => {\n      core.removeEventListener('searchInProgress', searchInProgressEventHandler);\n    };\n  }, []);\n\n  return {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  };\n}\n\nexport default useOnRedactionSearchCompleted;", "import useOnRedactionSearchCompleted from './useOnRedactionSearchCompleted';\n\nexport default useOnRedactionSearchCompleted;", "import ReactionSearchPanelContainer from './RedactionSearchPanelContainer';\n\nexport default ReactionSearchPanelContainer;", "import React from 'react';\nimport RedactionSearchPanel from './RedactionSearchPanel';\nimport useOnRedactionSearchCompleted from 'hooks/useOnRedactionSearchCompleted';\n\nconst ReactionSearchPanelContainer = () => {\n  const {\n    redactionSearchResults,\n    isProcessingRedactionResults,\n    clearRedactionSearchResults,\n    searchStatus,\n  } = useOnRedactionSearchCompleted();\n\n  return (\n    <RedactionSearchPanel\n      redactionSearchResults={redactionSearchResults}\n      isProcessingRedactionResults={isProcessingRedactionResults}\n      clearRedactionSearchResults={clearRedactionSearchResults}\n      searchStatus={searchStatus}\n    />\n  );\n};\n\nexport default ReactionSearchPanelContainer;", "import React, { useContext, useEffect, useMemo, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport RedactionPanel from './RedactionPanel';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport applyRedactions from 'helpers/applyRedactions';\nimport { RedactionPanelContext, RedactionPanelProvider } from './RedactionPanelContext';\nimport { isMobileSize } from 'helpers/getDeviceSize';\nimport DataElementWrapper from '../DataElementWrapper';\nimport Icon from 'components/Icon';\nimport RedactionSearchPanel from 'components/RedactionSearchPanel';\nimport { defaultRedactionTypes } from 'constants/redactionTypes';\n\nexport const RedactionPanelContainer = (props) => {\n  const [\n    isOpen,\n    isDisabled,\n    redactionPanelWidth,\n    isInDesktopOnlyMode,\n    customApplyRedactionsHandler,\n    redactionSearchPatterns,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, 'redactionPanel'),\n      selectors.isElementDisabled(state, 'redactionPanel'),\n      selectors.getRedactionPanelWidth(state),\n      selectors.isInDesktopOnlyMode(state),\n      selectors.getCustomApplyRedactionsHandler(state),\n      selectors.getRedactionSearchPatterns(state),\n    ],\n    shallowEqual,\n  );\n\n  const isMobile = isMobileSize();\n\n  const { redactionAnnotationsList, isCustomPanel, dataElement } = props;\n\n  const redactionTypesDictionary = useMemo(() => {\n    const storedRedactionTypes = Object.keys(redactionSearchPatterns).reduce((map, key) => {\n      const { label, type, icon } = redactionSearchPatterns[key];\n      map[type] = {\n        label,\n        icon,\n      };\n      return map;\n    }, {});\n\n    return { ...storedRedactionTypes, ...defaultRedactionTypes };\n  }, [redactionSearchPatterns]);\n\n  const deleteAllRedactionAnnotations = () => {\n    core.deleteAnnotations(redactionAnnotationsList);\n  };\n\n  const dispatch = useDispatch();\n  const applyAllRedactions = () => {\n    const originalApplyRedactions = () => {\n      const callOnRedactionCompleted = isCustomPanel ? closeRedactionPanel : () => {};\n      dispatch(applyRedactions(redactionAnnotationsList, callOnRedactionCompleted));\n    };\n    if (customApplyRedactionsHandler) {\n      customApplyRedactionsHandler(redactionAnnotationsList, originalApplyRedactions);\n    } else {\n      originalApplyRedactions();\n    }\n  };\n\n  const closeRedactionPanel = () => {\n    const tempDataElement = isCustomPanel ? dataElement : 'redactionPanel';\n    dispatch(actions.closeElement(tempDataElement));\n  };\n\n  const renderMobileCloseButton = () => {\n    return (\n      !isCustomPanel && (\n        <div className=\"close-container\">\n          <div className=\"close-icon-container\" onClick={closeRedactionPanel}>\n            <Icon glyph=\"ic_close_black_24px\" className=\"close-icon\" />\n          </div>\n        </div>\n      )\n    );\n  };\n\n  const style = isCustomPanel || (!isInDesktopOnlyMode && isMobile)\n    ? {}\n    : { width: `${redactionPanelWidth}px`, minWidth: `${redactionPanelWidth}px` };\n\n  const { isRedactionSearchActive } = useContext(RedactionPanelContext);\n\n  const [renderNull, setRenderNull] = useState(false);\n\n  useEffect(() => {\n    const timeout = setTimeout(() => {\n      setRenderNull(!isOpen);\n    }, 500);\n    return () => {\n      clearTimeout(timeout);\n    };\n  }, [isOpen]);\n\n  if (isDisabled || (!isOpen && renderNull && !isCustomPanel)) {\n    return null;\n  }\n\n  const dataElementToUse = isCustomPanel ? dataElement : 'redactionPanel';\n\n  return (\n    <DataElementWrapper dataElement={dataElementToUse} className=\"Panel RedactionPanel\" style={style}>\n      {!isInDesktopOnlyMode && isMobile && renderMobileCloseButton()}\n      <RedactionSearchPanel />\n      {!isRedactionSearchActive && (\n        <RedactionPanel\n          redactionAnnotations={redactionAnnotationsList}\n          redactionTypesDictionary={redactionTypesDictionary}\n          applyAllRedactions={applyAllRedactions}\n          deleteAllRedactionAnnotations={deleteAllRedactionAnnotations}\n        />\n      )}\n    </DataElementWrapper>\n  );\n};\n\nRedactionPanelContainer.propTypes = {\n  redactionAnnotationsList: PropTypes.array,\n  isCustomPanel: PropTypes.bool,\n  dataElement: PropTypes.string,\n};\n\nRedactionPanelContainer.defaultProps = {\n  isCustomPanel: false,\n  dataElement: '',\n};\n\nconst RedactionPanelContainerWithProvider = (props) => {\n  return (\n    <RedactionPanelProvider>\n      <RedactionPanelContainer {...props} />\n    </RedactionPanelProvider>\n  );\n};\n\nexport default RedactionPanelContainerWithProvider;\n", "import RedactionPanelContainerWithProvider from './RedactionPanelContainer';\n\nexport default RedactionPanelContainerWithProvider;"], "sourceRoot": ""}