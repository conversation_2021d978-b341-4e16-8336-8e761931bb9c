{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/OfficeEditor/FontStyleToggleButton.js"], "names": ["propTypes", "styleType", "PropTypes", "oneOf", "Object", "values", "STYLE_TOGGLE_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "FontStyleToggleButton", "forwardRef", "props", "ref", "isActive", "useSelector", "state", "selectors", "isStyleButtonActive", "menuItems", "dataElement", "icon", "title", "handleClick", "core", "getOfficeEditor", "updateSelectionAndCursorStyle", "onClick", "additionalClass", "key", "img", "ariaPressed", "displayName"], "mappings": "opCAUA,IAAMA,EAAY,CAChBC,UAAWC,IAAUC,MAAMC,OAAOC,OAAOC,MAAuBC,WAChEC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAwBC,sBAAW,SAACC,EAAOC,GAC/C,IAAQT,EAA8CQ,EAA9CR,aAAcP,EAAgCe,EAAhCf,UAAWS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAClCM,EAAWC,aAAY,SAACC,GAAK,OAAKC,IAAUC,oBAAoBF,EAAOnB,MAE7E,EAAqCsB,IAAU,GAAD,OAAItB,EAAS,WAAnDuB,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAClBC,IAAKC,kBAAkBC,8BAA8B,EAAD,GAAI7B,GAAY,KAGtE,OACEO,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACLc,QAASJ,EACTK,gBAAiBd,EAAW,SAAW,MAGvC,kBAAC,IAAY,CACXe,IAAKhC,EACLiB,SAAUA,EACVa,QAASJ,EACTH,YAAaA,EACbE,MAAOA,EACPQ,IAAKT,EACLU,YAAajB,EACbR,MAAOA,EACPE,UAAWA,OAMrBE,EAAsBd,UAAYA,EAClCc,EAAsBsB,YAAc,wBAErBtB", "file": "chunks/chunk.89.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport core from 'core';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { STYLE_TOGGLE_OPTIONS } from 'src/constants/customizationVariables';\n\nconst propTypes = {\n  styleType: PropTypes.oneOf(Object.values(STYLE_TOGGLE_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst FontStyleToggleButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, styleType, style, className } = props;\n  const isActive = useSelector((state) => selectors.isStyleButtonActive(state, styleType));\n\n  const { dataElement, icon, title } = menuItems[`${styleType}Button`];\n\n  const handleClick = () => {\n    core.getOfficeEditor().updateSelectionAndCursorStyle({ [styleType]: true });\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={styleType}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nFontStyleToggleButton.propTypes = propTypes;\nFontStyleToggleButton.displayName = 'FontStyleToggleButton';\n\nexport default FontStyleToggleButton;"], "sourceRoot": ""}