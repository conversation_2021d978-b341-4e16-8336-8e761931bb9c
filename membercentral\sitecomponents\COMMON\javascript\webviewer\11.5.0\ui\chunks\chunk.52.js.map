{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SheetTab/SheetTab.scss?03b6", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SheetTab/SheetTab.scss", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SpreadsheetSwitcher.scss?60c7", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SpreadsheetSwitcher.scss", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/AdditionalTabsFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/TabOptionsFlyout.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/TabOptionsButton.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SheetTab/SheetTab.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SpreadsheetSwitcher.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/SpreadsheetSwitcherContainer.js", "webpack:///./src/ui/src/components/ModularComponents/SpreadsheetEditor/SpreadsheetSwitcher/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "AdditionalTabsFlyout", "props", "id", "additionalTabs", "tabsForReference", "onClick", "activeItem", "dispatch", "useDispatch", "currentFlyout", "useSelector", "state", "selectors", "getFlyout", "useLayoutEffect", "noteStateFlyout", "dataElement", "className", "items", "map", "item", "tabLabel", "name", "label", "title", "option", "disabled", "isActive", "Symbol", "toString", "sheetIndex", "actions", "updateFlyout", "addFlyout", "propTypes", "PropTypes", "string", "arrayOf", "shape", "number", "bool", "array", "func", "createFlyoutItem", "toLowerCase", "sheetTabOptionsFlyoutItems", "TabOptionsFlyout", "sheetId", "handleClick", "sheetCount", "flyoutSelector", "DataElements", "SHEET_TAB_OPTIONS_FLYOUT", "isDisabled", "TabOptions", "onToggle", "t", "useTranslation", "ToggleElementButton", "img", "toggleElement", "sheet", "any", "isRequired", "activeSheetLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gEdited", "setActiveSheet", "renameSheet", "deleteSheet", "isEditMode", "validateName", "noRightBorder", "isReadOnlyMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SheetTab", "useState", "inputValue", "setInputValue", "isInputError", "setIsInputError", "inputRef", "useRef", "useEffect", "current", "focus", "onInputBlur", "warning", "message", "<PERSON><PERSON><PERSON>", "confirmBtnText", "onConfirm", "onCancel", "showWarningMessage", "component", "type", "classNames", "ref", "value", "onChange", "e", "new<PERSON>abel", "target", "isDuplicate", "isEmpty", "trim", "onBlur", "onKeyDown", "key", "aria-invalid", "<PERSON><PERSON>", "role", "ariaControls", "ariaSelected", "aria<PERSON><PERSON><PERSON>", "useI18String", "replace", "newActiveLabel", "tabIndex", "sheetName", "text", "secondaryBtnText", "React", "memo", "SpreadsheetSwitcher", "tabs", "activeSheetIndex", "createNewSheet", "width", "useWindowDimensions", "breakpoint", "useMemo", "Math", "floor", "labelBeingEdited", "getSpreadsheetEditorEditMode", "SpreadsheetEditorEditMode", "VIEW_ONLY", "updateActiveTab", "handleTabNameClick", "index", "preventDefault", "stopPropagation", "slice", "slicedTabs", "flyoutTabs", "tabElements", "isActiveSheetInFlyout", "some", "ADDITIONAL_SPREADSHEET_TABS_MENU", "Icon", "glyph", "ERROR", "NEW_SPREADSHEET_NUMBER", "SpreadsheetSwitcherContainer", "sheets", "setSheets", "setActiveSheetIndex", "getSheetsFromWorkbook", "workbookInstance", "sheetArray", "getSheetAt", "onSpreadsheetEditorSheetChanged", "event", "core", "getDocumentViewer", "getDocument", "getSpreadsheetEditorDocument", "getWorkbook", "getSheetIndex", "addEventListener", "removeEventListener", "handleDocumentUnloaded", "useCallback", "useOnDocumentUnloaded", "ownProps", "workbook", "console", "error", "newName", "isNameUnique", "getSheet", "createSheet", "removeSheet", "old<PERSON>ame", "currentName"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAA2E,IAK9FR,KAAK,CAACnB,EAAOC,EAAI,+yCAAgzC,M,qBCLz0C,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAwE,IAK3FR,KAAK,CAACnB,EAAOC,EAAI,gdAAid,M,8UCCpe4B,EAAuB,SAACC,GAC5B,MAMIA,EALFC,UAAE,IAAG,KAAE,EACPC,EAIEF,EAJFE,eACAC,EAGEH,EAHFG,iBACAC,EAEEJ,EAFFI,QACAC,EACEL,EADFK,WAEIC,EAAWC,cAEXC,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOT,MA2BxE,OAzBAY,2BAAgB,WACd,IAAMC,EAAkB,CACtBC,YAAad,EACbe,UAAW,uBACXC,MAAOf,EAAegB,KAAI,SAACC,GACzB,IAAMC,EAAWD,EAAKE,KAEtB,MAAO,CACLC,MAAOF,EACPG,MAAOH,EACPI,OAAQJ,EACRK,SAAUN,EAAKM,SACfC,SAAUN,IAAaf,EACvBU,YAAaY,OAAOP,GAAUQ,WAC9BxB,QAAS,kBAAMA,EAAQgB,EAAUD,EAAKU,kBAO1CvB,EAHGE,EAGMsB,IAAQC,aAAajB,EAAgBC,YAAaD,GAFlDgB,IAAQE,UAAUlB,MAI5B,CAACX,EAAkBE,IAEf,MAGTN,EAAqBkC,UAAY,CAC/BhC,GAAIiC,IAAUC,OACdjC,eAAgBgC,IAAUE,QAAQF,IAAUG,MAAM,CAChDhB,KAAMa,IAAUC,OAChBN,WAAYK,IAAUI,OACtBb,SAAUS,IAAUK,QAEtBpC,iBAAkB+B,IAAUM,MAC5BpC,QAAS8B,IAAUO,KACnBpC,WAAY6B,IAAUC,QAGTpC,Q,owCCnDf,IAAM2C,EAAmB,SAAClB,EAAQT,GAAW,MAAM,CACjDO,MAAO,UAAF,OAAYE,EAAOmB,eACxBpB,MAAO,UAAF,OAAYC,EAAOmB,eACxBnB,SACAT,gBAGW6B,EAA6B,CACxCF,EAAiB,SAAU,wBAC3BA,EAAiB,SAAU,yBAGvBG,EAAmB,SAAC7C,GACxB,IACE8C,EAIE9C,EAJF8C,QACAC,EAGE/C,EAHF+C,YACAC,EAEEhD,EAFFgD,WACAvB,EACEzB,EADFyB,SAGInB,EAAWC,cACX0C,EAAiB,GAAH,OAAMC,IAAaC,yBAAwB,YAAIL,GAC7DtC,EAAgBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,UAAUF,EAAOuC,MAuBxE,OArBApC,2BAAgB,WACd,IAAMC,EAAkB,CACtBC,YAAakC,EACbjC,UAAW,mBACXC,MAAO2B,EAA2B1B,KAAI,SAACC,GACrC,IAAIiC,EAA8B,WAAhBjC,EAAKK,QAAsC,IAAfwB,GAAqBvB,EACnE,OAAO,EAAP,KACKN,GAAI,IACPf,QAAS,kBAAM2C,EAAY5B,EAAKK,SAChCC,SAAU2B,QAQd9C,EAHGE,EAGMsB,IAAQC,aAAajB,EAAgBC,YAAaD,GAFlDgB,IAAQE,UAAUlB,MAI5B,CAACkC,EAAYvB,EAAUsB,IAEnB,MAGTF,EAAiBZ,UAAa,CAC5Ba,QAASZ,IAAUC,OACnBY,YAAab,IAAUO,KACvBO,WAAYd,IAAUI,OACtBb,SAAUS,IAAUK,MAGPM,Q,+hCCtDf,IAAMQ,EAAa,SAACrD,GAClB,MAOIA,EANF+C,mBAAW,IAAG,eAAS,EACvB9C,EAKED,EALFC,GACAqD,EAIEtD,EAJFsD,SACAhC,EAGEtB,EAHFsB,MACAG,EAEEzB,EAFFyB,SACAuB,EACEhD,EADFgD,WAGKO,EAAqB,EAAhBC,cAAgB,GAApB,GAGR,OACE,oCACE,kBAACC,EAAA,EAAmB,CAClB1C,YAAW,kBAAad,GACxBsB,MAAK,UAAKgC,EAAE,kCAAiC,YAAIjC,GACjDoC,IAPO,2BAQPJ,SAAU,SAAC5B,GACLA,GACF4B,EAAShC,IAGbG,SAAUA,EACVkC,cAAa,UAAKT,IAAaC,yBAAwB,YAAIlD,KAE7D,kBAAC,EAAkB,CACjB6C,QAAS7C,EACT+C,WAAYA,EACZD,YAAa,SAACvB,GAAM,OAAKuB,EAAY9C,EAAIqB,EAAOE,IAChDC,SAAUA,MAMlB4B,EAAWpB,UAAY,CACrBhC,GAAIiC,IAAUC,OACdb,MAAOY,IAAUC,OACjBmB,SAAUpB,IAAUO,KACpBM,YAAab,IAAUO,KACvBhB,SAAUS,IAAUK,KACpBS,WAAYd,IAAUI,QAGTe,Q,uiCC5Cf,IAAMpB,EAAY,CAChB2B,MAAO1B,IAAU2B,IAAIC,WACrBd,WAAYd,IAAUI,OACtByB,iBAAkB7B,IAAUC,OAAO2B,WACnCE,oBAAqB9B,IAAUO,KAAKqB,WACpCG,eAAgB/B,IAAUO,KAAKqB,WAC/BI,YAAahC,IAAUO,KAAKqB,WAC5BK,YAAajC,IAAUO,KAAKqB,WAC5B1D,QAAS8B,IAAUO,KAAKqB,WACxBM,WAAYlC,IAAUK,KAAKuB,WAC3BO,aAAcnC,IAAUO,KACxB6B,cAAepC,IAAUK,KACzBgC,eAAgBrC,IAAUK,KAC1BiC,kBAAmBtC,IAAUK,MAGzBkC,EAAW,SAAH,GAcR,IAbJb,EAAK,EAALA,MACAG,EAAgB,EAAhBA,iBACA3D,EAAO,EAAPA,QACA4C,EAAU,EAAVA,WACAgB,EAAmB,EAAnBA,oBACAC,EAAc,EAAdA,eACAG,EAAU,EAAVA,WACAE,EAAa,EAAbA,cACAD,EAAY,EAAZA,aACAE,EAAc,EAAdA,eACAJ,EAAW,EAAXA,YACAD,EAAW,EAAXA,YACAM,EAAiB,EAAjBA,kBAEQ3C,EAAsC+B,EAAtC/B,WAAkBP,EAAoBsC,EAA1BvC,KAAaI,EAAamC,EAAbnC,SAE3BC,EAAWJ,IAAUyC,EAEqB,IAAZW,mBAAS,IAAG,GAAzCC,EAAU,KAAEC,EAAa,KACuB,IAAfF,oBAAS,GAAM,GAAhDG,EAAY,KAAEC,EAAe,KAC5BvB,EAAMC,cAAND,EACFwB,EAAWC,mBACX1E,EAAWC,cAEjB0E,qBAAU,WACJN,GAAcI,EAASG,SACzBH,EAASG,QAAQC,UAElB,CAACR,IAEJ,IAsCMS,EAAc,WAClB,GAAIP,EAEF,OAjBIQ,EAAU,CACdC,QAAS/B,EAAE,GAAD,OAFNgC,EADyB,KAAfZ,EACa,iCAAmC,iCAEtC,aACxBpD,MAAOgC,EAAE,GAAD,OAAIgC,EAAU,WACtBC,eAAgBjC,EAAE,aAClBkC,UAAW,kBAAMV,EAASG,QAAQC,SAClCO,SAAU,WACR1B,GAAoB,GACpBc,GAAgB,GAChBF,EAActD,UAGlBhB,EAASwB,IAAQ6D,mBAAmBN,IAdZ,IAElBE,EACAF,EAmBNnB,EAAY5C,EAAOqD,GACnBX,EAAoB,MACpBc,GAAgB,GAChBF,EAAc,KAiCZgB,EAAY,KAyChB,OAvCEA,EADExB,EAEA,2BAAOyB,KAAK,OACV7E,UAAW8E,IAAW,cAAe,CAAE,cAAejB,IACtDkB,IAAKhB,EACLiB,MAAOrB,EACPsB,SAtFgB,SAACC,GACrB,IAAMC,EAAWD,EAAEE,OAAOJ,MACpBK,EAAchC,EAAa/C,EAAO6E,GAClCG,EAA8B,KAApBH,EAASI,OAEzBzB,EAAgBuB,GAAeC,GAC/B1B,EAAcuB,IAiFVK,OAAQpB,EACRqB,UAvCY,SAACP,GACH,UAAVA,EAAEQ,KAAoB7B,GACxBO,KAsCEuB,eAAc9B,IAKhB,oCACE,kBAAC+B,EAAA,EAAM,CACLC,KAAK,MACLC,aAAc,qBACdC,aAAcrF,EACdsF,UAAW1F,EACXN,UAAW,cACXZ,QAAS,SAAC8F,GAAC,OAAK9F,EAAQ8F,EAAG5E,EAAOO,IAClCN,MAAOD,EACPA,MAAOA,EACP2F,cAAc,EACdxF,SAAUA,IAEZ,yBAAKT,UAAU,iBACb,kBAAC,EAAU,CAACf,GAAIqB,aAAK,EAALA,EAAO4F,QAAQ,OAAQ,KAAKvE,cAC1CrB,MAAOA,EACP0B,WAAYA,EACZM,SAAU,SAACjC,GAAI,OAtGA8F,EAsGqB9F,EAtGL+F,EAsGWvF,EArGlDmC,EAAoB,MACpBc,GAAgB,QAChBb,EAAekD,EAAgBC,GAHT,IAACD,EAAgBC,GAuG/BrE,YAzDuB,SAAC9C,EAAIoH,EAAW7F,GAC/C,GAAe,WAAXA,EAxCJoD,EAD6B0C,EA0CLD,GAxCxBvC,GAAgB,GAChBd,EAAoBsD,QAwCb,GAAe,WAAX9F,EAAqB,CAC9B,GAAIgD,EACF,OAAOL,EAAY7C,GAErB,IAIM+D,EAAU,CACdC,QALc/B,EAAE,yCAMhBhC,MALYgC,EAAE,uCAMdiC,eALqBjC,EAAE,aAMvBgE,iBALuBhE,EAAE,iBAMzBkC,UAAW,WACTtB,EAAY7C,KAGhBhB,EAASwB,IAAQ6D,mBAAmBN,IA5DV,IAACiC,GAkGrB7F,SAAU8C,MAOZ,yBAAKvD,UAAW8E,IAAW,CACjC,OAAUpE,EACV,iBAAkB4C,EAClB,SAAY7C,GACX,cAED,yBAAKT,UAAW8E,IAAW,CAAE,aAAc1B,GAAa,aACrDwB,KAMPnB,EAASxC,UAAYA,EAENuF,UAAMC,KAAKhD,G,uiCCjL1B,IAAMiD,EAAsB,SAAC1H,GAAU,MACrC,EASIA,EARF2H,YAAI,IAAG,KAAE,IAQP3H,EAPF4H,wBAAgB,IAAG,IAAC,EACpB3D,EAMEjE,EANFiE,eACA4D,EAKE7H,EALF6H,eACA1D,EAIEnE,EAJFmE,YACAD,EAGElE,EAHFkE,YACAG,EAEErE,EAFFqE,aAAY,EAEVrE,EADFwE,yBAAiB,IAAG,GAAK,EAErBT,GAAyC,QAAtB,EAAA4D,EAAKC,UAAiB,aAAtB,EAAwBvG,OAAQ,GACjDyG,EAAUC,cAAVD,MACFE,EAAaC,mBAAQ,kBAAMC,KAAKC,OAAOL,EAAQ,IAAM,OAAM,CAACA,IAC1DvE,EAAMC,cAAND,EACoD,IAAZmB,mBAAS,IAAG,GAArD0D,EAAgB,KAAEpE,EAAmB,KAGtCO,EAD4B9D,YAAYE,IAAU0H,gCACHC,IAA0BC,UAEzEC,EAAkB,SAACrB,EAAgBC,GACvCpD,EAAoB,MACpBC,EAAekD,EAAgBC,IAG3BqB,EAAqB,SAACvC,EAAG5E,EAAOoH,GACpCxC,EAAEyC,iBACFzC,EAAE0C,kBACFJ,EAAgBlH,EAAOoH,IAMH,IAFWT,mBAAQ,WACvC,MAAO,CAACN,EAAKkB,MAAM,EAAGb,GAAaL,EAAKkB,MAAMb,MAC7C,CAACL,EAAMK,IAAY,GAFfc,EAAU,KAAEC,EAAU,KAIvBC,EAAcF,EAAW5H,KAAI,SAACC,GAAI,OACtC,kBAAC,EAAQ,CACPuF,IAAKvF,EAAKU,WACV+B,MAAOzC,EACP6B,WAAY2E,EAAK9I,OACjBkF,iBAAkBA,EAClB3D,QAASqI,EACTrE,WAAYgE,IAAqBjH,EAAKE,KACtC2C,oBAAqBA,EACrBC,eAAgBA,EAChBE,YAAaA,EACbD,YAAaA,EACbI,cAAeqD,EAAKxG,EAAKU,WAAa,IAAM8F,EAAKxG,EAAKU,WAAa,GAAGR,OAAS0C,EAC/EM,aAAcA,EACdE,eAAgBA,EAChBC,kBAAmBA,OAIjByE,EAAwBhB,mBAAQ,WACpC,OAAOc,EAAWG,MAAK,SAAC/H,GAAI,OAAKA,EAAKE,OAAS0C,OAC9C,CAACgF,EAAYhF,IAEhB,OACE,yBAAK/C,UAAU,+DACb,yBAAKA,UAAW,iBAAkB6F,KAAK,WACpCmC,GAEED,aAAU,EAAVA,EAAYlK,QAAS,EAElB,kBAAC4E,EAAA,EAAmB,CAClBzC,UAAU,oCACVD,YAAY,aACZQ,MAAOgC,EAAE,oBACTI,cAAeT,IAAaiG,iCAC5B7H,MAAOyH,EAAWlK,OAAO+C,YAEvBqH,GAA2B,kBAACG,EAAA,EAAI,CAACC,MAAM,2BAEzC,KAER,kBAACzC,EAAA,EAAM,CACL5F,UAAU,gBACVO,MAAM,kBACNmC,IAAI,gBACJtD,QAASyH,EACT9G,YAAa,eACbO,MAAO,GACP0F,UAAWzD,EAAE,mBACb9B,SAAU8C,KAEXwE,aAAU,EAAVA,EAAYlK,QAAS,GAElB,kBAAC,EAAoB,CACnBoB,GAAIiD,IAAaiG,iCACjBjJ,eAAgB6I,EAChB5I,iBAAkBwH,EAClBvH,QAASoI,EACTnI,WAAY0D,OAS1B2D,EAAoBzF,UAAY,CAC9B0F,KAAMzF,IAAUE,QAAQF,IAAUG,MAAM,CACtChB,KAAMa,IAAUC,OAChBN,WAAYK,IAAUI,OACtBb,SAAUS,IAAUK,QAEtBqF,iBAAkB1F,IAAUI,OAC5B2B,eAAgB/B,IAAUO,KAC1BoF,eAAgB3F,IAAUO,KAC1B0B,YAAajC,IAAUO,KACvByB,YAAahC,IAAUO,KACvB+B,kBAAmBtC,IAAUK,KAC7B8B,aAAcnC,IAAUO,MAGXiF,Q,2wEC/Hf,IAAM4B,EAAQ,0CACVC,EAAyB,EAE7B,SAASC,EAA6BxJ,GACpC,IAAQuD,EAAMC,cAAND,EACgC,IAAZmB,mBAAS,IAAG,GAAjC+E,EAAM,KAAEC,EAAS,KACmC,IAAXhF,mBAAS,GAAE,GAApDkD,EAAgB,KAAE+B,EAAmB,KAEtCC,EAAwB,SAACC,GAG7B,IAFA,IAAI7G,EAAa6G,EAAiB7G,WAC5B8G,EAAa,GACV3L,EAAI,EAAGA,EAAI6E,EAAY7E,IAAK,CACnC,IAAMyF,EAAQiG,EAAiBE,WAAW5L,GAC1C2L,EAAWzK,KAAK,CAAEgC,KAAMuC,EAAMvC,KAAMQ,WAAY1D,IAElD,OAAO2L,GAGT7E,qBAAU,WACR,IAAM+E,EAAkC,SAACC,GACvC,IAEMJ,EAFiBK,IAAKC,oBACDC,cAAcC,+BACZC,cAC7BZ,EAAUE,EAAsBC,IAChCF,EAAoBM,EAAMM,kBAI5B,OADAL,IAAKM,iBAAiB,qBAAsBR,GACrC,WACLE,IAAKO,oBAAoB,qBAAsBT,MAEhD,IAEH,IAAMU,EAAyBC,uBAAY,WACzCjB,EAAU,IACVC,EAAoB,KACnB,IACHiB,YAAsBF,GAEtB,IAiEMG,EAAW,OACZ7K,GAAK,IACR2H,KAAM8B,EACN7B,mBACA3D,eArEqB,SAAC5C,EAAMqH,GAAU,QAChCoC,EAA6B,QAArB,EAAGZ,IAAKE,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cACrE,IAAKQ,EACH,OAAOC,QAAQC,MAAM1B,GAGnBwB,EAASf,WAAWrB,KACtBoC,EAAS7G,eAAeyE,GACxBiB,EAAoBjB,KA8DtBb,eA1DqB,WAAM,QACrBiD,EAA6B,QAArB,EAAGZ,IAAKE,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cACrE,IAAKQ,EACH,OAAOC,QAAQC,MAAM1B,GAKvB,IAFA,IAAI2B,EAAU1H,EAAE,gCACZ2H,GAAgBJ,EAASK,SAASF,IAC9BC,GACND,EAAU,GAAH,OAAM1H,EAAE,gCAA+B,YAAIgG,KAClD2B,GAAgBJ,EAASK,SAASF,GAEpCH,EAASM,YAAYH,IA+CrB9G,YA5CkB,SAAC9C,GAAS,QACtByJ,EAA6B,QAArB,EAAGZ,IAAKE,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cACrE,IAAKQ,EACH,OAAOC,QAAQC,MAAM1B,GAGnBwB,EAASK,SAAS9J,IACpByJ,EAASO,YAAYhK,GAEvBqI,EAAUE,EAAsBkB,KAoChC5G,YAjCkB,SAACoH,EAASL,GAAY,QAClCH,EAA6B,QAArB,EAAGZ,IAAKE,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cACrE,IAAKQ,EACH,OAAOC,QAAQC,MAAM1B,GAGvB,IAAM1F,EAAQkH,EAASK,SAASG,GAC5B1H,IACFA,EAAMvC,KAAO4J,GAEfvB,EAAUE,EAAsBkB,KAwBhCzG,aArBmB,SAACkH,EAAaN,GAAY,QACvCH,EAA6B,QAArB,EAAGZ,IAAKE,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cACrE,OAAKQ,EAKDS,IAAgBN,KAGXH,EAASK,SAASF,IAPzBF,QAAQC,MAAM1B,IACP,MAoBX,OAAQ,kBAAC,EAAwBuB,GAGnCrB,EAA6BvH,UAAY,GAE1BuH,QC5HAA", "file": "chunks/chunk.52.js", "sourcesContent": ["var api = require(\"!../../../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../../../node_modules/css-loader/index.js!../../../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../../../node_modules/sass-loader/dist/cjs.js!./SheetTab.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".sheet-tab{cursor:pointer;border-bottom:none;border-top-right-radius:4px;border-top-left-radius:4px;min-width:170px;height:40px;position:relative;border-right:1px solid transparent}.sheet-tab:hover{background:var(--faded-component-background)}.sheet-tab.no-left-border .tab-body{border-right:1px solid transparent}.sheet-tab .tab-body{height:24px;display:flex;padding:0 8px;width:100%;position:absolute;top:50%;transform:translateY(-50%);border-right:1px solid var(--border)}.sheet-tab .tab-body.input-mode{padding-left:5px;padding-right:6px}.sheet-tab .tab-body .sheet-label{width:calc(100% - 24px);text-align:left;justify-content:left;height:24px}.sheet-tab .tab-body .sheet-label span{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.sheet-tab .tab-body .sheet-input{max-width:100%}.sheet-tab .tab-body input.input-error{border-color:red}.sheet-tab.active{border:1px solid var(--border);border-bottom:none;background:var(--gray-0)}.sheet-tab.active .tab-body{border-right:1px solid transparent}.sheet-tab .sheet-options .ToggleElementButton button{width:24px;height:24px;min-width:24px}.sheet-tab .sheet-options .ToggleElementButton button .Icon{width:16px;height:14px}.sheet-tab .sheet-options .more-options-icon{height:24px;width:24px}.sheet-tab .sheet-options .more-options-icon .Icon{width:16px;height:16px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../../node_modules/css-loader/index.js!../../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../../node_modules/sass-loader/dist/cjs.js!./SpreadsheetSwitcher.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".Flyout[\\\\:has\\\\(.SheetOptionsFlyout\\\\)]{z-index:130}.Flyout:has(.SheetOptionsFlyout){z-index:130}.SpreadsheetSwitcher{padding:0}.GenericFileTab{display:flex;padding-left:4px;padding-top:4px}.GenericFileTab .dropdown-menu{border:1px solid var(--border);margin-top:3px;margin-left:5px}.GenericFileTab .dropdown-menu .Icon{position:absolute}.GenericFileTab .add-sheet-tab{margin-top:3px;margin-left:5px}.GenericFileTab .add-sheet-tab .Icon{width:14px;height:14px}\", \"\"]);\n\n// exports\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\n\nconst AdditionalTabsFlyout = (props) => {\n  const {\n    id = '',\n    additionalTabs,\n    tabsForReference,\n    onClick,\n    activeItem,\n  } = props;\n  const dispatch = useDispatch();\n\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, id));\n\n  useLayoutEffect(() => {\n    const noteStateFlyout = {\n      dataElement: id,\n      className: 'AdditionalTabsFlyout',\n      items: additionalTabs.map((item) => {\n        const tabLabel = item.name;\n\n        return {\n          label: tabLabel,\n          title: tabLabel,\n          option: tabLabel,\n          disabled: item.disabled,\n          isActive: tabLabel === activeItem,\n          dataElement: Symbol(tabLabel).toString(),\n          onClick: () => onClick(tabLabel, item.sheetIndex),\n        };\n      })\n    };\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  }, [tabsForReference, activeItem]);\n\n  return null;\n};\n\nAdditionalTabsFlyout.propTypes = {\n  id: PropTypes.string,\n  additionalTabs: PropTypes.arrayOf(PropTypes.shape({\n    name: PropTypes.string,\n    sheetIndex: PropTypes.number,\n    disabled: PropTypes.bool,\n  })),\n  tabsForReference: PropTypes.array,\n  onClick: PropTypes.func,\n  activeItem: PropTypes.string,\n};\n\nexport default AdditionalTabsFlyout;\n\n\n", "import { useLayoutEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport DataElements from 'src/constants/dataElement';\nimport actions from 'actions';\nimport selectors from 'selectors';\n\nconst createFlyoutItem = (option, dataElement) => ({\n  label: `action.${option.toLowerCase()}`,\n  title: `action.${option.toLowerCase()}`,\n  option,\n  dataElement,\n});\n\nexport const sheetTabOptionsFlyoutItems = [\n  createFlyoutItem('Rename', 'sheetTabRenameOption'),\n  createFlyoutItem('Delete', 'sheetTabDeleteOption'),\n];\n\nconst TabOptionsFlyout = (props) => {\n  const {\n    sheetId,\n    handleClick,\n    sheetCount,\n    disabled,\n  } = props;\n\n  const dispatch = useDispatch();\n  const flyoutSelector = `${DataElements.SHEET_TAB_OPTIONS_FLYOUT}-${sheetId}`;\n  const currentFlyout = useSelector((state) => selectors.getFlyout(state, flyoutSelector));\n\n  useLayoutEffect(() => {\n    const noteStateFlyout = {\n      dataElement: flyoutSelector,\n      className: 'TabOptionsFlyout',\n      items: sheetTabOptionsFlyoutItems.map((item) => {\n        let isDisabled = (item.option === 'Delete' && sheetCount === 1) || disabled;\n        return {\n          ...item,\n          onClick: () => handleClick(item.option),\n          disabled: isDisabled,\n        };\n      }),\n    };\n\n    if (!currentFlyout) {\n      dispatch(actions.addFlyout(noteStateFlyout));\n    } else {\n      dispatch(actions.updateFlyout(noteStateFlyout.dataElement, noteStateFlyout));\n    }\n  }, [sheetCount, disabled, handleClick]);\n\n  return null;\n};\n\nTabOptionsFlyout.propTypes  = {\n  sheetId: PropTypes.string,\n  handleClick: PropTypes.func,\n  sheetCount: PropTypes.number,\n  disabled: PropTypes.bool,\n};\n\nexport default TabOptionsFlyout;", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport { useTranslation } from 'react-i18next';\nimport SheetOptionsFlyout from './TabOptionsFlyout';\nimport DataElements from 'src/constants/dataElement';\n\n\nconst TabOptions = (props) => {\n  const {\n    handleClick = () => { },\n    id,\n    onToggle,\n    label,\n    disabled,\n    sheetCount,\n  } = props;\n\n  const [t] = useTranslation();\n  const icon = 'icon-tools-more-vertical';\n\n  return (\n    <>\n      <ToggleElementButton\n        dataElement={`options-${id}`}\n        title={`${t('option.searchPanel.moreOptions')} ${label}`}\n        img={icon}\n        onToggle={(isActive) => {\n          if (isActive) {\n            onToggle(label);\n          }\n        }}\n        disabled={disabled}\n        toggleElement={`${DataElements.SHEET_TAB_OPTIONS_FLYOUT}-${id}`}\n      />\n      <SheetOptionsFlyout\n        sheetId={id}\n        sheetCount={sheetCount}\n        handleClick={(option) => handleClick(id, label, option)}\n        disabled={disabled}\n      />\n    </>\n  );\n};\n\nTabOptions.propTypes = {\n  id: PropTypes.string,\n  label: PropTypes.string,\n  onToggle: PropTypes.func,\n  handleClick: PropTypes.func,\n  disabled: PropTypes.bool,\n  sheetCount: PropTypes.number,\n};\n\nexport default TabOptions;\n", "import React, { useEffect, useState, useRef } from 'react';\nimport { useDispatch } from 'react-redux';\nimport actions from 'actions';\nimport PropTypes from 'prop-types';\nimport Button from 'components/Button';\nimport classNames from 'classnames';\nimport TabOptions from '../TabOptionsButton';\nimport { useTranslation } from 'react-i18next';\nimport './SheetTab.scss';\n\nconst propTypes = {\n  sheet: PropTypes.any.isRequired,\n  sheetCount: PropTypes.number,\n  activeSheetLabel: PropTypes.string.isRequired,\n  setLabelBeingEdited: PropTypes.func.isRequired,\n  setActiveSheet: PropTypes.func.isRequired,\n  renameSheet: PropTypes.func.isRequired,\n  deleteSheet: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  isEditMode: PropTypes.bool.isRequired,\n  validateName: PropTypes.func,\n  noRightBorder: PropTypes.bool,\n  isReadOnlyMode: PropTypes.bool,\n  skipDeleteWarning: PropTypes.bool,\n};\n\nconst SheetTab = ({\n  sheet,\n  activeSheetLabel,\n  onClick,\n  sheetCount,\n  setLabelBeingEdited,\n  setActiveSheet,\n  isEditMode,\n  noRightBorder,\n  validateName,\n  isReadOnlyMode,\n  deleteSheet,\n  renameSheet,\n  skipDeleteWarning,\n}) => {\n  const { sheetIndex, name: label, disabled } = sheet;\n\n  const isActive = label === activeSheetLabel;\n\n  const [inputValue, setInputValue] = useState('');\n  const [isInputError, setIsInputError] = useState(false);\n  const { t } = useTranslation();\n  const inputRef = useRef();\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (inputValue && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [inputValue]);\n\n  const onInputChange = (e) => {\n    const newLabel = e.target.value;\n    const isDuplicate = validateName(label, newLabel);\n    const isEmpty = newLabel.trim() === '';\n\n    setIsInputError(isDuplicate || isEmpty);\n    setInputValue(newLabel);\n  };\n\n  const updateActiveTab = (newActiveLabel, tabIndex) => {\n    setLabelBeingEdited(null);\n    setIsInputError(false);\n    setActiveSheet(newActiveLabel, tabIndex);\n  };\n\n  const startLabelEditingMode = (text) => {\n    setInputValue(text);\n    setIsInputError(false);\n    setLabelBeingEdited(text);\n  };\n\n  const inputErrorWarning = () => {\n    const isEmpty = inputValue === '';\n    const warningKey = isEmpty ? 'warning.sheetTabRenameIssueTwo' : 'warning.sheetTabRenameIssueOne';\n    const warning = {\n      message: t(`${warningKey}.message`),\n      title: t(`${warningKey}.title`),\n      confirmBtnText: t('action.ok'),\n      onConfirm: () => inputRef.current.focus(),\n      onCancel: () => {\n        setLabelBeingEdited(false);\n        setIsInputError(false);\n        setInputValue(label);\n      }\n    };\n    dispatch(actions.showWarningMessage(warning));\n  };\n\n  const onInputBlur = () => {\n    if (isInputError) {\n      inputErrorWarning();\n      return;\n    }\n    renameSheet(label, inputValue);\n    setLabelBeingEdited(null);\n    setIsInputError(false);\n    setInputValue('');\n  };\n\n  const onKeyDown = (e) => {\n    if (e.key === 'Enter' && !isInputError) {\n      onInputBlur();\n    }\n  };\n\n  const handleTabOptionSelection = (id, sheetName, option) => {\n    if (option === 'Rename') {\n      startLabelEditingMode(sheetName);\n    } else if (option === 'Delete') {\n      if (skipDeleteWarning) {\n        return deleteSheet(label);\n      }\n      const message = t('warning.sheetTabDeleteMessage.message');\n      const title = t('warning.sheetTabDeleteMessage.title');\n      const confirmBtnText = t('action.ok');\n      const secondaryBtnText = t('action.cancel');\n      const warning = {\n        message,\n        title,\n        confirmBtnText,\n        secondaryBtnText,\n        onConfirm: () => {\n          deleteSheet(label);\n        },\n      };\n      dispatch(actions.showWarningMessage(warning));\n    }\n  };\n\n  let component = null;\n  if (isEditMode) {\n    component = (\n      <input type='text'\n        className={classNames('sheet-input', { 'input-error': isInputError })}\n        ref={inputRef}\n        value={inputValue}\n        onChange={onInputChange}\n        onBlur={onInputBlur}\n        onKeyDown={onKeyDown}\n        aria-invalid={isInputError}\n      />\n    );\n  } else {\n    component = (\n      <>\n        <Button\n          role='tab'\n          ariaControls={'document-container'}\n          ariaSelected={isActive}\n          ariaLabel={label}\n          className={'sheet-label'}\n          onClick={(e) => onClick(e, label, sheetIndex)}\n          title={label}\n          label={label}\n          useI18String={false}\n          disabled={disabled}\n        />\n        <div className='sheet-options'>\n          <TabOptions id={label?.replace(/\\s+/g, '-').toLowerCase()}\n            label={label}\n            sheetCount={sheetCount}\n            onToggle={(name) => updateActiveTab(name, sheetIndex)}\n            handleClick={handleTabOptionSelection}\n            disabled={isReadOnlyMode}\n          />\n        </div>\n      </>\n    );\n  }\n\n  return (<div className={classNames({\n    'active': isActive,\n    'no-left-border': noRightBorder,\n    'disabled': disabled,\n  }, 'sheet-tab')}\n  >\n    <div className={classNames({ 'input-mode': isEditMode },'tab-body')}>\n      {component}\n    </div>\n  </div>);\n\n};\n\nSheetTab.propTypes = propTypes;\n\nexport default React.memo(SheetTab);", "import React, { useState, useMemo } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport useWindowDimensions from 'helpers/useWindowsDimensions';\nimport Button from 'components/Button';\nimport ToggleElementButton from 'components/ModularComponents/ToggleElementButton';\nimport Icon from 'components/Icon';\nimport DataElements from 'constants/dataElement';\nimport { SpreadsheetEditorEditMode } from 'constants/spreadsheetEditor';\nimport AdditionalTabsFlyout from './AdditionalTabsFlyout';\nimport SheetTab from './SheetTab/SheetTab';\nimport './SpreadsheetSwitcher.scss';\nimport PropTypes from 'prop-types';\nimport selectors from 'selectors';\n\nconst SpreadsheetSwitcher = (props) => {\n  const {\n    tabs = [],\n    activeSheetIndex = 0,\n    setActiveSheet,\n    createNewSheet,\n    deleteSheet,\n    renameSheet,\n    validateName,\n    skipDeleteWarning = false,\n  } = props;\n  const activeSheetLabel = tabs[activeSheetIndex]?.name || '';\n  const { width } = useWindowDimensions();\n  const breakpoint = useMemo(() => Math.floor((width - 80) / 170), [width]);\n  const { t } = useTranslation();\n  const [labelBeingEdited, setLabelBeingEdited] = useState('');\n\n  const spreadsheetEditorEditMode = useSelector(selectors.getSpreadsheetEditorEditMode);\n  const isReadOnlyMode = spreadsheetEditorEditMode === SpreadsheetEditorEditMode.VIEW_ONLY;\n\n  const updateActiveTab = (newActiveLabel, tabIndex) => {\n    setLabelBeingEdited(null);\n    setActiveSheet(newActiveLabel, tabIndex);\n  };\n\n  const handleTabNameClick = (e, label, index) => {\n    e.preventDefault();\n    e.stopPropagation();\n    updateActiveTab(label, index);\n  };\n\n  // Break the sheet tabs into two, one regular view, and one into flyout\n  const [slicedTabs, flyoutTabs] = useMemo(() => {\n    return [tabs.slice(0, breakpoint), tabs.slice(breakpoint)];\n  }, [tabs, breakpoint]);\n\n  const tabElements = slicedTabs.map((item) => (\n    <SheetTab\n      key={item.sheetIndex}\n      sheet={item}\n      sheetCount={tabs.length}\n      activeSheetLabel={activeSheetLabel}\n      onClick={handleTabNameClick}\n      isEditMode={labelBeingEdited === item.name}\n      setLabelBeingEdited={setLabelBeingEdited}\n      setActiveSheet={setActiveSheet}\n      deleteSheet={deleteSheet}\n      renameSheet={renameSheet}\n      noRightBorder={tabs[item.sheetIndex + 1] && tabs[item.sheetIndex + 1].name === activeSheetLabel}\n      validateName={validateName}\n      isReadOnlyMode={isReadOnlyMode}\n      skipDeleteWarning={skipDeleteWarning}\n    />\n  ));\n\n  const isActiveSheetInFlyout = useMemo(() => {\n    return flyoutTabs.some((item) => item.name === activeSheetLabel);\n  }, [flyoutTabs, activeSheetLabel]);\n\n  return (\n    <div className=\"SpreadsheetSwitcher ModularHeader BottomHeader stroke start\">\n      <div className={'GenericFileTab'} role='tablist'>\n        {tabElements}\n        {\n          (flyoutTabs?.length > 0) ?\n            (\n              <ToggleElementButton\n                className=\"dropdown-menu tab-dropdown-button\"\n                dataElement=\"tabTrigger\"\n                title={t('message.showMore')}\n                toggleElement={DataElements.ADDITIONAL_SPREADSHEET_TABS_MENU}\n                label={flyoutTabs.length.toString()}\n              >\n                {(isActiveSheetInFlyout) && (<Icon glyph=\"icon-active-indicator\"></Icon>)}\n              </ToggleElementButton>\n            ) : null\n        }\n        <Button\n          className=\"add-sheet-tab\"\n          title=\"action.addSheet\"\n          img=\"icon-menu-add\"\n          onClick={createNewSheet}\n          dataElement={'addTabButton'}\n          label={''}\n          ariaLabel={t('action.addSheet')}\n          disabled={isReadOnlyMode}\n        />\n        {flyoutTabs?.length > 0 &&\n          (\n            <AdditionalTabsFlyout\n              id={DataElements.ADDITIONAL_SPREADSHEET_TABS_MENU}\n              additionalTabs={flyoutTabs}\n              tabsForReference={tabs}\n              onClick={updateActiveTab}\n              activeItem={activeSheetLabel}\n            />\n          )\n        }\n      </div>\n    </div>\n  );\n};\n\nSpreadsheetSwitcher.propTypes = {\n  tabs: PropTypes.arrayOf(PropTypes.shape({\n    name: PropTypes.string,\n    sheetIndex: PropTypes.number,\n    disabled: PropTypes.bool,\n  })),\n  activeSheetIndex: PropTypes.number,\n  setActiveSheet: PropTypes.func,\n  createNewSheet: PropTypes.func,\n  deleteSheet: PropTypes.func,\n  renameSheet: PropTypes.func,\n  skipDeleteWarning: PropTypes.bool,\n  validateName: PropTypes.func,\n};\n\nexport default SpreadsheetSwitcher;", "import React, { useState, useEffect, useCallback } from 'react';\nimport core from 'core';\nimport SpreadsheetSwitcher from './SpreadsheetSwitcher';\nimport useOnDocumentUnloaded from 'hooks/useOnDocumentUnloaded';\nimport { useTranslation } from 'react-i18next';\n\nconst ERROR = 'SpreadsheetEditorDocument is not loaded';\nlet NEW_SPREADSHEET_NUMBER = 2;\n\nfunction SpreadsheetSwitcherContainer(props) {\n  const { t } = useTranslation();\n  const [sheets, setSheets] = useState([]);\n  const [activeSheetIndex, setActiveSheetIndex] = useState(0);\n\n  const getSheetsFromWorkbook = (workbookInstance) => {\n    let sheetCount = workbookInstance.sheetCount;\n    const sheetArray = [];\n    for (let i = 0; i < sheetCount; i++) {\n      const sheet = workbookInstance.getSheetAt(i);\n      sheetArray.push({ name: sheet.name, sheetIndex: i });\n    }\n    return sheetArray;\n  };\n\n  useEffect(() => {\n    const onSpreadsheetEditorSheetChanged = (event) => {\n      const documentViewer = core.getDocumentViewer();\n      const doc = documentViewer.getDocument().getSpreadsheetEditorDocument();\n      const workbookInstance = doc.getWorkbook();\n      setSheets(getSheetsFromWorkbook(workbookInstance));\n      setActiveSheetIndex(event.getSheetIndex());\n    };\n\n    core.addEventListener('activeSheetChanged', onSpreadsheetEditorSheetChanged);\n    return () => {\n      core.removeEventListener('activeSheetChanged', onSpreadsheetEditorSheetChanged);\n    };\n  }, []);\n\n  const handleDocumentUnloaded = useCallback(() => {\n    setSheets([]);\n    setActiveSheetIndex(0);\n  }, []);\n  useOnDocumentUnloaded(handleDocumentUnloaded);\n\n  const setActiveSheet = (name, index) => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    if (!workbook) {\n      return console.error(ERROR);\n    }\n\n    if (workbook.getSheetAt(index)) {\n      workbook.setActiveSheet(index);\n      setActiveSheetIndex(index);\n    }\n  };\n\n  const createNewSheet = () => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    if (!workbook) {\n      return console.error(ERROR);\n    }\n\n    let newName = t('spreadsheetEditor.blankSheet');\n    let isNameUnique = !workbook.getSheet(newName);\n    while (!isNameUnique) {\n      newName = `${t('spreadsheetEditor.blankSheet')} ${NEW_SPREADSHEET_NUMBER++}`;\n      isNameUnique = !workbook.getSheet(newName);\n    }\n    workbook.createSheet(newName);\n  };\n\n  const deleteSheet = (name) => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    if (!workbook) {\n      return console.error(ERROR);\n    }\n\n    if (workbook.getSheet(name)) {\n      workbook.removeSheet(name);\n    }\n    setSheets(getSheetsFromWorkbook(workbook));\n  };\n\n  const renameSheet = (oldName, newName) => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    if (!workbook) {\n      return console.error(ERROR);\n    }\n\n    const sheet = workbook.getSheet(oldName);\n    if (sheet) {\n      sheet.name = newName;\n    }\n    setSheets(getSheetsFromWorkbook(workbook));\n  };\n\n  const validateName = (currentName, newName) => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    if (!workbook) {\n      console.error(ERROR);\n      return false;\n    }\n\n    if (currentName === newName) {\n      return false;\n    }\n    return !!workbook.getSheet(newName);\n  };\n\n  const ownProps = {\n    ...props,\n    tabs: sheets,\n    activeSheetIndex,\n    setActiveSheet,\n    createNewSheet,\n    deleteSheet,\n    renameSheet,\n    validateName,\n  };\n\n  return (<SpreadsheetSwitcher {...ownProps} />);\n}\n\nSpreadsheetSwitcherContainer.propTypes = { };\n\nexport default SpreadsheetSwitcherContainer;", "import SpreadsheetSwitcherContainer from './SpreadsheetSwitcherContainer';\n\nexport default SpreadsheetSwitcherContainer;"], "sourceRoot": ""}