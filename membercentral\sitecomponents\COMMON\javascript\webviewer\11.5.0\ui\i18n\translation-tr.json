{"action": {"addParagraph": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "addSheet": "<PERSON><PERSON>", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyAll": "Tümünü uygula", "calendar": "Takvim", "calibrate": "kalibre et", "cancel": "İptal", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "currentPageIs": "Geçerli sayfa:", "clear": "Temizlemek", "clearAll": "<PERSON><PERSON><PERSON> temizle", "close": "Ka<PERSON><PERSON>", "undo": "<PERSON><PERSON>", "redo": "Hazır", "comment": "<PERSON><PERSON>", "reply": "<PERSON><PERSON><PERSON> e<PERSON>", "copy": "kopyala", "cut": "<PERSON><PERSON><PERSON>", "paste": "Yapıştırmak", "pasteWithoutFormatting": "biçimlendir<PERSON>en ya<PERSON>ıştır", "delete": "<PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON>i", "group": "Grup", "ungroup": "G<PERSON><PERSON>", "download": "İndirmek", "edit": "D<PERSON><PERSON>lemek", "collapse": "Yıkılmak", "expand": "Genişletmek", "extract": "Çıkarmak", "extractPage": "Sayfayı Çıkart", "enterFullscreen": "<PERSON>", "exitFullscreen": "Tam ekrandan çık", "fit": "<PERSON><PERSON><PERSON><PERSON>rm<PERSON>", "fitToPage": "<PERSON><PERSON>ya sığdır", "fitToWidth": "Genişliğe sığdır", "more": "Da<PERSON>", "openFile": "Açık <PERSON>", "showMoreFiles": "<PERSON><PERSON>", "page": "Say<PERSON>", "of": "ile ilgili", "pagePrev": "<PERSON><PERSON><PERSON> say<PERSON>", "pageNext": "<PERSON><PERSON><PERSON>", "pageSet": "<PERSON><PERSON>", "print": "Yazdır", "proceed": "İlerlemek", "name": "İsim", "rename": "<PERSON><PERSON>den isimlendirmek", "remove": "Kaldırmak", "ok": "<PERSON><PERSON>", "rotate": "<PERSON><PERSON>", "rotate3D": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rotateClockwise": "Saat Yönünde Döndür", "rotateCounterClockwise": "Saat yönü<PERSON>ün tersine çevirin", "rotatedClockwise": "saat yönünde döndürüldü", "rotatedCounterClockwise": "saat yö<PERSON><PERSON><PERSON><PERSON>n tersine dö<PERSON>ü<PERSON>", "rotationIs": "geçerli say<PERSON> rotas<PERSON>u", "movedToBottomOfDocument": "belgenin altına taşındı", "movedToTopofDocument": "belgenin en üstüne taşındı", "extracted": "çıkarılan", "save": "<PERSON>ıt etmek", "post": "Postalamak", "create": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showMoreResults": "<PERSON><PERSON> fazla son<PERSON>", "sign": "<PERSON><PERSON><PERSON>", "style": "stil", "submit": "Göndermek", "zoom": "yak<PERSON><PERSON>laştır", "zoomIn": "Yakınlaştır", "zoomOut": "Uzaklaştırmak", "zoomSet": "Yakınlaştırmayı ayarla", "zoomChanged": "Geçerli yakınlaştırma", "zoomControls": "Yakınlaş<PERSON><PERSON><PERSON>ntrolleri", "draw": "Çizmek", "type": "Tip", "upload": "Yüklemek", "link": "Bağlantı", "unlink": "Bağlantıyı Sil", "fileAttachmentDownload": "<PERSON><PERSON><PERSON>ı indirin", "prevResult": "Önceki son<PERSON>", "nextResult": "<PERSON><PERSON><PERSON>", "prev": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "startFormEditing": "Form Düzenlemeye Başlayın", "exitFormEditing": "Form Düzenleme Modundan Çık", "exit": "çıkış", "addOption": "Seçenek Ekle", "formFieldEdit": "Form Alanını Düzenle", "formFieldEditMode": "Form Alanlarını Düzenle", "contentEditMode": "İçeriği Düzenle", "viewShortCutKeysFor3D": "Kısayol Tuşlarını Görüntüle", "markAllRead": "Tümünü okundu olarak işaretle", "pageInsertion": "<PERSON><PERSON>", "insertPage": "<PERSON><PERSON>", "insert": "Sokmak", "pageManipulation": "<PERSON><PERSON>", "replace": "<PERSON><PERSON>", "replacePage": "Sayfayı Değiştir", "modal": "modal", "isOpen": "açık", "setDestination": "<PERSON><PERSON><PERSON>", "showLess": "daha az g<PERSON>", "showMore": "...daha fazla", "chooseFile": "<PERSON><PERSON> <PERSON><PERSON> se<PERSON>", "changeDate": "<PERSON><PERSON><PERSON>", "browse": "Dosyalara <PERSON>", "selectYourOption": "Seçeneğ<PERSON><PERSON> seç<PERSON>", "open": "Açık", "deselectAll": "Hiçbirini seçme", "select": "Seçme", "moveToTop": "En üste taşı", "moveToBottom": "Aşağıya taşı", "movePageToTop": "Sayfayı En Üste Taşı", "movePageToBottom": "Sayfayı Alta Taşı", "moveUp": "Yukarı taşı", "moveDown": "Aşağı taşı", "moveLeft": "Sola Hareket Et", "moveRight": "Sağa Hareket Et", "backToMenu": "<PERSON><PERSON><PERSON> dön", "redactPages": "Sayfaları yeniden düzenleyin", "playAudio": "ses çal", "pauseAudio": "ses durak<PERSON>ma", "selectAll": "<PERSON><PERSON><PERSON>", "unselect": "<PERSON><PERSON><PERSON><PERSON> kaldı<PERSON>", "addMark": "İşaret Ekle", "viewFile": "Dosyayı görü<PERSON>üle", "multiReplyAnnotations": "Seçili ek açıklamaları yanıtla ({{count}})", "comparePages": "Sayfaları karşılaştır", "startComparison": "Karşılaştırmayı Başlat", "showComparison": "Karşılaştırmayı Göster", "highlightChanges": "Değişiklikleri Vurgula", "back": "<PERSON><PERSON>", "clearSignature": "İmzayı temizle", "clearInitial": "<PERSON><PERSON><PERSON><PERSON>", "readOnlySignature": "Salt okunur imza silinemez", "newDocument": "<PERSON><PERSON> be<PERSON>", "sideBySideView": "<PERSON>", "pageNumberInput": "Sayfa numarası girişi", "addNewColor": "<PERSON><PERSON>", "deleteColor": "Seçilen <PERSON>", "copySelectedColor": "Seçilen <PERSON>", "showMoreColors": "<PERSON>ha Fazla Renk Göster", "showLessColors": "Daha Az Renk Göster", "fromCustomColorPicker": "Özel Renk Seçici'den", "newSpreadsheetDocument": "<PERSON><PERSON>", "resetDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "annotation": {"areaMeasurement": "<PERSON>", "arc": "yay", "arcMeasurement": "<PERSON>", "arrow": "Ok", "callout": "Çağırmak", "crop": "Sayfayı Kırp", "caret": "yoksun", "dateFreeText": "Takvim", "formFillCheckmark": "kene", "formFillCross": "Geçmek", "distanceMeasurement": "Mesafe", "rectangularAreaMeasurement": "<PERSON>k<PERSON><PERSON><PERSON><PERSON> Alan", "ellipseMeasurement": "Elips Alanı", "countMeasurement": "<PERSON><PERSON><PERSON> ölçümü", "ellipse": "Elips", "eraser": "<PERSON><PERSON><PERSON>", "fileattachment": "<PERSON><PERSON><PERSON>", "freehand": "Serbest El", "freeHandHighlight": "Serbest El Vurgusu", "freetext": "Ücretsiz Metin", "markInsertText": "<PERSON><PERSON>", "markReplaceText": "<PERSON><PERSON>", "highlight": "Vurgulamak", "image": "resim", "line": "Astar", "perimeterMeasurement": "Ç<PERSON><PERSON>", "polygon": "Çokgen", "polygonCloud": "Bulut", "polyline": "çoklu çizgi", "rectangle": "Dikdörtgen", "redact": "Yazıya dökmek", "formFillDot": "Nokta", "signature": "<PERSON><PERSON><PERSON>", "snipping": "<PERSON><PERSON><PERSON>", "squiggly": "dalgalı", "stamp": "Damga vurmak", "stickyNote": "Not", "strikeout": "üstü çizili", "underline": "Altını çizmek", "custom": "Gelenek", "rubberStamp": "<PERSON><PERSON> damga", "note": "Not", "textField": "<PERSON><PERSON>", "signatureFormField": "<PERSON><PERSON><PERSON>", "checkBoxFormField": "<PERSON><PERSON>", "radioButtonFormField": "<PERSON><PERSON><PERSON>", "listBoxFormField": "Liste Kutusu Alanı", "comboBoxFormField": "Birleşik Kutu Alanı", "link": "Bağlantı", "other": "<PERSON><PERSON><PERSON>", "3D": "3 boyutlu", "sound": "Ses", "changeView": "Görünü<PERSON><PERSON>ştir", "newImage": "<PERSON><PERSON>", "defaultCustomStampTitle": "<PERSON><PERSON>"}, "rubberStamp": {"Approved": "Onaylı", "AsIs": "Olduğu Gibi", "Completed": "Tamamlanmış", "Confidential": "<PERSON><PERSON><PERSON>", "Departmental": "departman", "Draft": "Taslak", "Experimental": "Den<PERSON>sel", "Expired": "Günü geçmiş", "Final": "son", "ForComment": "<PERSON><PERSON>", "ForPublicRelease": "<PERSON><PERSON>", "InformationOnly": "<PERSON><PERSON><PERSON> bilgi", "NotApproved": "Onaylanmamış", "NotForPublicRelease": "<PERSON><PERSON>y<PERSON>nı İçin <PERSON>", "PreliminaryResults": "<PERSON><PERSON>", "Sold": "Satıldı", "TopSecret": "Çok gizli", "Void": "Geçersiz", "SHSignHere": "Burayı imzalayın", "SHWitness": "Tanık", "SHInitialHere": "İlk Burada", "SHAccepted": "Kabul edilmiş", "SBRejected": "Reddedildi"}, "component": {"attachmentPanel": "ekler", "leftPanel": "Sol Panel", "toolsHeader": "Araçlar", "searchOverlay": "<PERSON><PERSON>", "searchPanel": "<PERSON><PERSON>", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "<PERSON><PERSON><PERSON>", "indexPanel": "<PERSON><PERSON> Paneli", "outlinePanel": "anahat", "outlinesPanel": "<PERSON><PERSON><PERSON>", "newOutlineTitle": "Yeni Taslak Başlığı", "outlineTitle": "<PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>", "bookmarkPanel": "Yer imi", "bookmarksPanel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "bookmarkTitle": "Yer İşareti Başlığı", "bookmarkPage": "Say<PERSON>", "signaturePanel": "<PERSON><PERSON><PERSON>", "layersPanel": "<PERSON><PERSON><PERSON>", "thumbnailsPanel": "küçük resimler", "toolsButton": "Araçlar", "redaction": "yazı", "viewControls": "Kontrolleri Görüntüle", "pageControls": "<PERSON><PERSON>", "calibration": "kali<PERSON><PERSON><PERSON>", "zoomOverlay": "Yak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "textPopup": "<PERSON><PERSON>", "createStampButton": "Yeni Damga Oluştur", "filter": "filtre", "multiSelectButton": "Çoklu seçim", "pageReplaceModalTitle": "Sayfayı Değiştir", "files": "<PERSON><PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON><PERSON>", "editText": "<PERSON><PERSON>", "redactionPanel": "Redaksiyon Paneli", "tabLabel": "Sekme", "noteGroupSection": {"open": "Tüm Ek Açıklamaları Görüntüle", "close": "Tüm Ek Açıklamaları Kapat"}, "comparePanel": "Karşılaştır Paneli", "watermarkPanel": "<PERSON>ligran <PERSON>i", "mainMenu": "<PERSON>"}, "message": {"showMore": "<PERSON><PERSON> faz<PERSON>", "showLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "toolsOverlayNoPresets": "<PERSON><PERSON>", "badDocument": "Belge yüklenemedi. Belge bozuk veya geçerli değil.", "customPrintPlaceholder": "Örneğin. 3, 4-10", "encryptedAttemptsExceeded": "Şifreli belge yüklenemedi. Çok fazla deneme.", "encryptedUserCancelled": "Şifreli belge yüklenemedi. Şifre girişi iptal edildi.", "enterPassword": "Bu belge parola korumalıdır. lütfen bir şifre girin", "incorrectPassword": "<PERSON><PERSON><PERSON><PERSON>, kalan deneme sayısı: {{ remainingAttempts }}", "noAnnotations": "<PERSON>rum bırakmak için ek açıklamalar yapmaya başlayın.", "noAnnotationsReadOnly": "Bu belgede ek açıklama yok.", "noAnnotationsFilter": "Ek açıklamalar yapmaya başlayın ve filtreler burada görünecektir.", "noBookmarks": "Kullanılabilir yer işareti yok", "noOutlines": "<PERSON>u belgenin taslağı yok.", "noAttachments": "Bu belgenin eki yok.", "noResults": "<PERSON><PERSON><PERSON> bulunamadı.", "numResultsFound": "<PERSON><PERSON><PERSON><PERSON> bulundu", "loadError": "Belge Yükleme Hatası", "notSupported": "<PERSON>u dosya tipi desteklenmiyor.", "passwordRequired": "<PERSON><PERSON><PERSON>", "enterPasswordPlaceholder": "Parolanı Gir", "preparingToPrint": "Yazdırmaya hazırlanıyor...", "annotationReplyCount": "{{count}} <PERSON><PERSON><PERSON><PERSON>", "annotationReplyCount_plural": "{{count}} <PERSON><PERSON><PERSON>", "printTotalPageCount": "Toplam: {{count}} sayfa", "printTotalPageCount_plural": "Toplam: {{count}} sayfa", "processing": "İşleme...", "searching": "Aranıyor...", "searchCommentsPlaceholder": "Yorum ara", "searchDocumentPlaceholder": "Belge ara", "clearSearchResults": "<PERSON><PERSON> sonuçlarını temizle", "searchResultsCleared": "Arama sonuçları temizlendi", "searchSettingsPlaceholder": "<PERSON><PERSON>", "searchSuggestionsPlaceholder": "Önerileri ara", "signHere": "Burayı imzalayın", "insertTextHere": "<PERSON><PERSON><PERSON> metin e<PERSON>in", "imageSignatureAcceptedFileTypes": "Yalnızca {{acceptedFileTypes}} kabul edilir", "signatureRequired": "<PERSON><PERSON> et<PERSON>k i<PERSON>in imza ve paraf gere<PERSON>li", "enterMeasurement": "İki nokta arasındaki ölçümü girin", "errorEnterMeasurement": "Girdiğiniz sayı geçersiz, 7,5 veya 7 1/2 gibi değerler girebilirsiniz.", "linkURLorPage": "Bağlantı URL'si veya Sayfa", "warning": "Uyarı", "svgMalicious": "SVG betiği güvenlik nedeniyle yok sayıldı", "doNotShowAgain": "<PERSON>unu bir daha bana g<PERSON>me", "doNotAskAgain": "<PERSON><PERSON><PERSON>", "enterReplacementText": "Değiştirmek istediğiniz metni girin", "sort": "D<PERSON><PERSON>lemek", "sortBy": "Çeşit", "emptyCustomStampInput": "<PERSON><PERSON> metni boş o<PERSON>az", "unpostedComment": "Yayınlanmamış Yorum", "lockedLayer": "<PERSON><PERSON>", "layerVisibililtyNoChange": "Katman görünürlüğü değiştirilemez", "noLayers": "<PERSON>u belgenin katmanı yok.", "noSignatureFields": "<PERSON>u belgede imza alanı yok.", "untitled": "başlıks<PERSON>z", "selectHowToLoadFile": "Belgenizi nasıl yükleyeceğinizi seçin", "openFileByUrl": "Dosyayı URL ile aç:", "enterUrlHere": "URL'yi buraya girin", "openLocalFile": "<PERSON><PERSON> açın:", "selectFile": "<PERSON><PERSON><PERSON>", "selectPageToReplace": "Belgede değiştirmek istediğiniz sayfaları seçin.", "embeddedFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageNum": "Say<PERSON>", "viewBookmark": "Sayfadaki Yer İşaretini Görüntüle", "error": "<PERSON><PERSON>", "errorPageNumber": "Geçersiz sayfa numarası. <PERSON><PERSON><PERSON><PERSON>r (şimdiki değeri)", "errorBlankPageNumber": "Lütfen bir sayfa numarası belirtin", "errorLoadingDocument": "Bu belge okunurken bir sorun oluştu ve bazı sayfalar görüntülenemeyebilir. Bu, belgenin bozuk olabileceğini gösterir. Toplam sayfa sayısı {{totalPageCount}} ve görüntülenen sayfa sayısı {{displayedPageCount}}.", "noRevisions": "<PERSON>u belgenin revizyonu yoktur."}, "option": {"type": {"caret": "yoksun", "custom": "Gelenek", "ellipse": "Elips", "fileattachment": "<PERSON><PERSON><PERSON>", "freehand": "Serbest El", "callout": "Çağırmak", "freetext": "Ücretsiz Metin", "line": "Astar", "polygon": "Çokgen", "polyline": "çoklu çizgi", "rectangle": "Dikdörtgen", "redact": "Yazıya dökmek", "signature": "<PERSON><PERSON><PERSON>", "stamp": "Damga vurmak", "stickyNote": "Yapışkan not", "highlight": "Vurgulamak", "strikeout": "üstü çizili", "underline": "Altını çizmek", "squiggly": "dalgalı", "3D": "3 boyutlu", "other": "<PERSON><PERSON><PERSON>", "initials": "ba<PERSON> harfler", "saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notesOrder": {"dropdownLabel": "Sıralama Listesi", "position": "<PERSON><PERSON>", "time": "Zaman", "status": "Durum", "author": "<PERSON><PERSON>", "type": "Tip", "color": "Renk", "createdDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modifiedDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "toolbarGroup": {"dropdownLabel": "<PERSON><PERSON> Grupları", "flyoutLabel": "<PERSON><PERSON><PERSON>", "toolbarGroup-View": "G<PERSON>rü<PERSON><PERSON><PERSON>", "toolbarGroup-Annotate": "açıklama ekle", "toolbarGroup-Shapes": "şekiller", "toolbarGroup-Insert": "Sokmak", "toolbarGroup-Measure": "Ölçüm", "toolbarGroup-Edit": "D<PERSON><PERSON>lemek", "toolbarGroup-EditText": "<PERSON><PERSON>", "toolbarGroup-FillAndSign": "<PERSON><PERSON><PERSON> ve <PERSON>zala", "toolbarGroup-Forms": "Formlar", "toolbarGroup-Redact": "Yazıya dökmek", "toolbarGroup-oe-Home": "Ev", "toolbarGroup-oe-Insert": "Sokmak", "toolbarGroup-oe-Layout": "D<PERSON>zen", "toolbarGroup-oe-Review": "Gözden geçirmek"}, "annotationColor": {"StrokeColor": "<PERSON><PERSON><PERSON>", "FillColor": "Doldurmak", "TextColor": "<PERSON><PERSON>"}, "colorPalette": {"colorLabel": "Renk"}, "colorPalettePicker": {"addColor": "<PERSON><PERSON> renk ekle", "selectColor": "Renk seç"}, "displayMode": {"layout": "<PERSON><PERSON>", "pageTransition": "Sayfa Geçişi"}, "documentControls": {"selectTooltip": "Birden çok sayfa seçin", "closeTooltip": "Çoklu seçimi kapat"}, "bookmarkOutlineControls": {"edit": "D<PERSON><PERSON>lemek", "done": "Tamamlandı", "reorder": "ye<PERSON><PERSON> s<PERSON>a"}, "layout": {"cover": "Kapak <PERSON>ı", "double": "Çift Sayfa", "single": "Tek sayfa"}, "mathSymbols": "Matematik sembolleri", "notesPanel": {"separator": {"today": "<PERSON><PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "unknown": "Bilinmeyen"}, "noteContent": {"noName": "(is<PERSON>iz)", "noDate": "(tarih yok)"}, "toggleMultiSelect": "Ek Açıklama için Çoklu Seçimi Değiştir"}, "pageTransition": {"continuous": "Sürekli <PERSON>", "default": "<PERSON><PERSON>", "reader": "<PERSON><PERSON><PERSON><PERSON>"}, "print": {"all": "<PERSON><PERSON><PERSON>", "current": "Geçerli sayfa", "pages": "Yazdırılacak sayfalar", "specifyPages": "Sayfaları Belirtin", "view": "Mev<PERSON> g<PERSON>", "pageQuality": "Baskı kalitesi", "qualityNormal": "Normal", "qualityHigh": "<PERSON><PERSON><PERSON><PERSON>", "includeAnnotations": "Ek açıklamaları dahil et", "includeComments": "Yo<PERSON>ları dahil et", "printSettings": "Yaz<PERSON><PERSON>rma <PERSON>", "printGrayscale": "Gri Tonlamalı Yazdır", "printCurrentDisabled": "Geçerli görünüm yalnızca tek bir sayfa görüntülenirken kullanılabilir."}, "printInfo": {"author": "<PERSON><PERSON>", "subject": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>"}, "redaction": {"markForRedaction": "Redaksiyon için <PERSON>"}, "searchPanel": {"caseSensitive": "<PERSON><PERSON><PERSON>", "wholeWordOnly": "<PERSON><PERSON><PERSON>", "wildcard": "joker karakter", "replace": "<PERSON><PERSON>", "replaceAll": "<PERSON><PERSON><PERSON>", "replaceText": "<PERSON><PERSON>", "confirmMessageReplaceAll": "Tüm metni değiştirmek istediğinizden emin misiniz?", "confirmMessageReplaceOne": "<PERSON>u metni değiştirmek istediğinizden emin misiniz?", "moreOptions": "<PERSON><PERSON> faz<PERSON>", "lessOptions": "<PERSON><PERSON> a<PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}, "toolsOverlay": {"currentStamp": "Geçerli <PERSON>", "currentSignature": "Geçerli <PERSON>", "signatureAltText": "<PERSON><PERSON><PERSON>"}, "stampOverlay": {"addStamp": "<PERSON><PERSON>"}, "signatureOverlay": {"addSignatureOrInitials": "<PERSON><PERSON>/<PERSON><PERSON>", "addSignature": "<PERSON><PERSON>"}, "signatureModal": {"modalName": "<PERSON>ni <PERSON>", "dragAndDrop": "Resminizi buraya sü<PERSON>ü<PERSON><PERSON> bı<PERSON>n", "or": "<PERSON><PERSON><PERSON>", "pickImage": "<PERSON><PERSON> <PERSON><PERSON>", "selectImage": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "typeSignature": "Tip <PERSON>*", "typeInitial": "Baş Harfleri yazın*", "drawSignature": "<PERSON><PERSON><PERSON>*", "drawInitial": "Baş Harflerini Çiz*", "imageSignature": "Resim <PERSON>", "imageInitial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i", "pickInitialsFile": "Baş Harfleri Seçin", "noSignatures": "<PERSON><PERSON> anda kayıtlı imza yok.", "fontStyle": "yazı stili", "textSignature": {"dropdownLabel": "Yazı Tipi Ailesi"}}, "pageReplacementModal": {"yourFiles": "Dosyalarınız", "chooseFile": "<PERSON><PERSON> <PERSON><PERSON> se<PERSON>", "localFile": "<PERSON><PERSON>", "pageReplaceInputLabel": "Sayfaları Değiştir", "pageReplaceInputFromSource": "sayfa(lar) ile", "warning": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>r sayfasından çıkılsın mı?", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, o ana kadar yaptığınız tüm seçimleri iptal edecektir. Hala çıkmak istediğinizden emin misiniz?"}}, "filterAnnotModal": {"color": "Renk", "includeReplies": "Yanıtları Dahil Et", "filters": "filtreler", "user": "kullanıcı", "type": "Tip", "status": "Durum", "filterSettings": "Filtre Ayarları", "filterDocument": "Belge ve yorum panelini filtrele"}, "state": {"accepted": "Kabul edilmiş", "rejected": "Reddedildi", "completed": "Tamamlanmış", "cancelled": "İptal edildi", "set": "<PERSON><PERSON><PERSON>:", "setBy": "o<PERSON><PERSON> a<PERSON>", "none": "Hiç<PERSON>i", "marked": "İşaretlenmiş", "unmarked": "işaretlenmemiş"}, "measurementOverlay": {"scale": "Ölçek Oranı", "angle": "Açı", "distance": "Mesafe", "perimeter": "Ç<PERSON><PERSON>", "area": "<PERSON>", "distanceMeasurement": "<PERSON>fe <PERSON>", "perimeterMeasurement": "<PERSON><PERSON><PERSON>", "arcMeasurement": "<PERSON>", "areaMeasurement": "<PERSON>", "countMeasurement": "<PERSON><PERSON><PERSON> ölçümü", "radius": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "count": "Saymak", "length": "Uzunluk", "xAxis": "X Ekseni", "yAxis": "Y Ekseni"}, "freeTextOption": {"autoSizeFont": "Yazı tipi boyutunu dinamik olarak ölçeklendirin"}, "measurementOption": {"scale": "Ölçek", "selectScale": "Ölçeği Seçin", "selectScaleDropdown": "Ölçek Açılır Menüsünü Seçin"}, "measurement": {"scaleModal": {"calibrate": "kalibre et", "custom": "Gelenek", "fractionalUnits": "<PERSON><PERSON><PERSON>", "precision": "Ke<PERSON>lik", "preset": "ön ayar", "paperUnits": "<PERSON><PERSON><PERSON><PERSON>", "displayUnits": "G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fractionUnitsTooltip": "Kesir birimleri yalnızca içeri ve ft-in için geçerlidir", "incorrectSyntax": "Yanlış sözdizimi", "units": "<PERSON><PERSON><PERSON>"}, "scaleOverlay": {"addNewScale": "<PERSON><PERSON>", "selectTwoPoints": "Kalibre etmek için bilinen bir boyutun iki noktasını seçin", "inputKnowDimension": "Bilinen boyutu ve kalibre edilecek birimleri girin", "multipleScales": "Çoklu Ölçekler"}, "deleteScaleModal": {"deleteScale": "Ölçeği Sil", "scaleIsOn-delete-info": "<PERSON>u ölçek şu anda kull<PERSON>", "page-delete-info": "sayfa", "appliedTo-delete-info": "ve uygulanan", "measurement": "ölçüm", "measurements": "ölçümler", "deletionIs": "<PERSON><PERSON><PERSON>", "irreversible": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "willDeleteMeasurement": "ve ilişkili ölçümleri siler.", "confirmDelete": "<PERSON>u ölçeği silmek istediğinizden emin misiniz?", "thisCantBeUndone": " <PERSON><PERSON> geri alınam<PERSON>.", "ifChangeScale": "Seçilen ölçüm veya araç için ölçeği değiştirirseniz, ölçek", "notUsedWillDelete": " artık herhangi bir ölçüm veya araç tarafından kullanılmayacak ve silinecektir. <PERSON>lme işlemi geri döndür<PERSON><PERSON>ez.", "ifToContinue": "<PERSON><PERSON> etmek istediğinizden emin misiniz?"}}, "contentEdit": {"deletionModal": {"title": "İçeriği Sil", "message": "Seçilen içeriği silmek istediğinizden emin misiniz? Bu geri alınamaz."}, "digitalSign": {"title": "Uyarı Mühürlü Belge", "message": "Bu belge imzalanmıştır ve değiştirilemez veya değiştirilemez."}}, "stylePopup": {"textStyle": "<PERSON><PERSON><PERSON> stili", "colors": "Ren<PERSON>r", "invalidFontSize": "Yazı tipi boyutu aşağıdaki aralıkta olmalıdır", "labelText": "Etiket Metni", "labelTextPlaceholder": "Etiket metni ekle"}, "styleOption": {"style": "stil", "solid": "Sağlam", "cloudy": "Bulutlu"}, "slider": {"opacity": "opaklık", "thickness": "<PERSON><PERSON><PERSON>", "text": "Yazı Boyutu"}, "shared": {"page": "Say<PERSON>", "precision": "<PERSON><PERSON><PERSON>", "enableSnapping": "Ölçüm araçları için yakalamayı etkinleştir"}, "watermark": {"title": "<PERSON>li<PERSON><PERSON>", "addWatermark": "<PERSON><PERSON><PERSON><PERSON> ekle", "size": "<PERSON><PERSON>", "location": "Filigranları düzenlemek için bir konum seçin", "text": "<PERSON><PERSON>", "style": "stil", "resetAllSettings": "Bütün a<PERSON>ları sıfırla", "font": "<PERSON><PERSON><PERSON>", "addNew": "<PERSON><PERSON>", "locations": {"center": "merkez", "topLeft": "<PERSON> ü<PERSON>", "topRight": "Sağ üst", "topCenter": "<PERSON><PERSON> merkez", "bottomLeft": "Sol alt", "bottomRight": "Sağ alt", "bottomCenter": "<PERSON>rkez"}}, "thumbnailPanel": {"delete": "<PERSON><PERSON><PERSON>", "rotateClockwise": "Saat yönünde", "rotateCounterClockwise": "saat yön<PERSON><PERSON>n tersine", "rotatePageClockwise": "Sayfayı Saat Yönünde Döndür", "rotatePageCounterClockwise": "Sayfayı Saat Yönünün Tersine Döndür", "moreOptions": "<PERSON><PERSON> faz<PERSON>", "moreOptionsMenu": "Diğer Küçük Resim Seçenekleri Menüsü", "enterPageNumbers": "Seçilecek sayfa numaralarını girin", "multiSelectPages": "Çoklu Seçim <PERSON>", "multiSelectPagesExample": "Örneğin. 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "Sayfaları taşı"}, "richText": {"bold": "Gözü pek", "italic": "İtalik", "underline": "Altını çizmek", "strikeout": "üstü çizili", "alignLeft": "<PERSON><PERSON> sola hizala", "alignRight": "<PERSON><PERSON>", "alignCenter": "<PERSON><PERSON>", "justifyCenter": "<PERSON><PERSON>", "alignTop": "<PERSON><PERSON> hizala", "alignMiddle": "<PERSON><PERSON><PERSON>", "alignBottom": "Aşa<PERSON><PERSON>ya hizala"}, "customStampModal": {"modalName": "Yeni Damga Oluştur", "stampText": "<PERSON><PERSON> metni", "timestampText": "zaman damgası metni", "Username": "Kullanıcı adı", "Date": "<PERSON><PERSON><PERSON>", "Time": "Zaman", "fontStyle": "yazı stili", "dateFormat": "<PERSON><PERSON><PERSON> formatı", "month": "Ay", "day": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON><PERSON>", "hour": "Saat", "minute": "Dakika", "second": "<PERSON><PERSON><PERSON>", "textColor": "<PERSON><PERSON>", "backgroundColor": "Arka plan rengi", "dateToolTipLabel": "<PERSON><PERSON><PERSON> biçimi hakkında daha fazla bilgi", "previewCustomStamp": "<PERSON><PERSON><PERSON><PERSON>"}, "pageRedactModal": {"addMark": "İşaret Ekle", "pageSelection": "<PERSON><PERSON>", "current": "Geçerli sayfa", "specify": "Sayfaları Belirtin", "odd": "<PERSON><PERSON><PERSON> tek <PERSON>", "even": "Yalnızca Çift Sayfalar"}, "lineStyleOptions": {"title": "stil"}, "settings": {"settings": "<PERSON><PERSON><PERSON>", "searchSettings": "<PERSON><PERSON>", "general": "<PERSON><PERSON>", "language": "Dil", "theme": "<PERSON><PERSON>", "darkMode": "Karanlık mod", "lightMode": "Işık modu", "advancedSetting": "Gelişmiş <PERSON>", "viewing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disableFadePageNavigationComponent": "Soldurma Sayfa Gezinme Bileşenini Devre Dışı Bırak", "disableFadePageNavigationComponentDesc": "<PERSON><PERSON> her zaman ekranda tutun. Varsayı<PERSON>, beli<PERSON>i bir hareketsizlik süresinden sonra onu soldurmaktır.", "enabledFormFieldHighlighting": "Form Alanı Vurgularını Etkinleştir", "enabledFormFieldHighlightingDesc": "PDF belgelerinde form alanlarının vurgulanmasını etkinleştirin. Varsayılan olarak etkindir.", "disableNativeScrolling": "<PERSON><PERSON>ı Devre Dışı Bırak", "disableNativeScrollingDesc": "Daha önce etkinleştirildiyse yerel mobil cihaz kaydırma davranışını devre dışı bırakın. Yerel mobil cihaz kaydırma davranışının varsayılan olarak kapalı olduğunu unutmayın.", "annotations": "Ek açıklamalar", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Açıklama Açılır Penceresinden Araç Varsayılan Stil Güncellemesini Devre Dışı Bırak", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Detaylandırma stili <PERSON>, detaylandırmayı oluşturan ilişkili araca eşitlenmesini devre dışı bırakır. <PERSON><PERSON>, bir detaylandırmanın stili de<PERSON>ril<PERSON>e, a<PERSON>ç var<PERSON>ılan stilleri güncellenmeyecektir.", "notesPanel": "Notlar Paneli", "disableNoteSubmissionWithEnter": "Enter ile Not Göndermeyi Devre Dışı Bırak", "disableNoteSubmissionWithEnterDesc": "Daha önce etkinleştirilmişse, yalnızca Enter tuşuna basarak not gönderme özelliğini devre dışı bırakın. Bu, not gönderimini Ctrl/Cmd + <PERSON><PERSON> olan var<PERSON> d<PERSON>ü<PERSON>ü<PERSON>.", "disableAutoExpandCommentThread": "<PERSON><PERSON> Konusunu Otomatik Genişletmeyi Devre Dışı Bırak", "disableAutoExpandCommentThreadDesc": "Notlar Panelindeki tüm yorum dizilerinin otomatik olarak genişletilmesini devre dışı bırakır.", "disableReplyCollapse": "Yanıtı Daraltmayı Devre Dışı Bırak", "disableReplyCollapseDesc": "Notlar Panelinde yanıtların daraltılmasını devre dışı bırakır.", "disableTextCollapse": "<PERSON>in Daraltmasını Devre Dışı Bırak", "disableTextCollapseDesc": "Notlar Panelinde ek açıklama metninin da<PERSON>tılmasını devre dışı bırakır.", "search": "<PERSON><PERSON>", "disableClearSearchOnPanelClose": "Panel Kapatmada Aramayı Temizle'yi devre dışı bırakın", "disableClearSearchOnPanelCloseDesc": "Kullanıcı arama panelini kapattığında arama sonuçlarını temizlemeyi devre dışı bırakın. Devre dışı bırakıldığında, kullanıcı arama panelini kapatıp yeniden açsa bile arama sonuçları korunur. Bu ayar etkinleştirilse bile mobil cihazların arama sonuçlarını asla temizlemediğini unutmayın. <PERSON><PERSON><PERSON> nedeni, belgedeki arama sonuçlarını görüntülemek için panelin kapatılması gerekmesidir.", "pageManipulation": "<PERSON><PERSON>", "disablePageDeletionConfirmationModal": "Sayfa Silme Onayı Kalıcılığını Devre Dışı Bırak", "disablePageDeletionConfirmationModalDesc": "Küçük resim görünümünden bir sayfayı silerken onay modunu devre dışı bırakın", "disableMultiselect": "Çoklu Seçimi Devre Dışı Bırak", "disableMultiselectDesc": "Sol küçük resim panelinde çoklu seçimi devre dışı bırakın", "miscellaneous": "Çeşitli", "keyboardShortcut": "Klavye kısayolu", "command": "Emretmek", "description": "Tanım", "action": "<PERSON><PERSON><PERSON>", "rotateDocumentClockwise": "Belgeyi saat yönünde döndür", "rotateDocumentCounterclockwise": "Belgeyi saat yönünün tersine döndür", "copyText": "Seçilen metni veya ek açıklamaları kopyala", "pasteText": "Metin veya ek açıklamaları yapıştırın", "undoChange": "Ek açıklama değişikliğini geri alma", "redoChange": "Ek açıklama değişikliğini yeniden yapma", "openFile": "<PERSON><PERSON><PERSON> a<PERSON>", "openSearch": "Arama yer paylaşımını aç", "zoomOptions": "Yakın<PERSON><PERSON><PERSON><PERSON><PERSON>", "zoomIn": "Yakınlaştır", "zoomOut": "Uzaklaştır", "setHeaderFocus": "Odaklanmayı başlığa ayarlar", "fitScreenWidth": "Belgeyi küçük bir ekranda (< 640 piksel) ekran genişliğine sığdırın, aksi halde orijinal boyutuna sığdırın", "print": "Yazdır", "bookmarkOpenPanel": "Bir sayfaya hızlıca yer işareti koyun ve yer işareti panelini açın", "goToPreviousPage": "önceki sayfaya git", "goToNextPage": "<PERSON><PERSON><PERSON> say<PERSON> git", "goToPreviousPageArrowUp": "Tek düzen modunda (ArrowUp) önceki sayfaya git", "goToNextPageArrowDown": "Tek düzen modunda sonraki sayfaya git (Aşağı Ok)", "holdSwitchPan": "Pan moduna geçmek için basılı tutun ve önceki araca dönmek için bırakın", "selectAnnotationEdit": "AnnotationEdit aracını seçin", "selectPan": "Kaydırma aracını seçin", "selectCreateArrowTool": "AnnotationCreateArrow aracını seçin", "selectCreateCalloutTool": "AnnotationCreateCallout aracını seçin", "selectEraserTool": "AnnotationEraser aracını seçin", "selectCreateFreeHandTool": "AnnotationCreateFreeHand aracını seçin", "selectCreateStampTool": "AnnotationCreateStamp aracını seçin", "selectCreateLineTool": "AnnotationCreateLine aracını seçin", "selectCreateStickyTool": "AnnotationCreateSticky aracını seçin", "selectCreateEllipseTool": "AnnotationCreateEllipse aracını seçin", "selectCreateRectangleTool": "AnnotationCreateRectangle aracını seçin", "selectCreateRubberStampTool": "AnnotationCreateRubberStamp aracını seçin", "selectCreateFreeTextTool": "AnnotationCreateFreeText aracını seçin", "openSignatureModal": "İmza kipini veya yer paylaşımını açın", "selectCreateTextSquigglyTool": "AnnotationCreateTextSquiggly aracını seçin", "selectCreateTextHighlightTool": "AnnotationCreateTextHighlight aracını seçin", "selectCreateTextStrikeoutTool": "AnnotationCreateTextStrikeout aracını seçin", "selectCreateTextUnderlineTool": "AnnotationCreateTextUnderline aracını seçin", "editKeyboardShorcut": "Klavye Kısayolunu Düzenle", "setShortcut": "Kısayol Ayarla", "editShortcut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcutAlreadyExists": "Yukarıdaki klavye kısayolu zaten var.", "close": "İpucunu kapat"}, "cellBorderStyle": {"none": "Hiç<PERSON>i", "solid": "Sağlam", "dashed": "Kesik kesik", "dotted": "Noktalı"}}, "warning": {"deletePage": {"deleteTitle": "Sayfayı Sil", "deleteMessage": "Seçili sayfaları silmek istediğinizden emin misiniz? Bu geri alınamaz.", "deleteLastPageMessage": "Belgedeki tüm sayfaları silemezsiniz."}, "extractPage": {"title": "Sayfayı <PERSON>", "message": "<PERSON><PERSON><PERSON> sayfayı/sayfaları çıkarmak istediğinizden emin misiniz?", "confirmBtn": "Çıkarmak", "secondaryBtn": "Çıkart ve Sil"}, "redaction": {"applyTile": "Redaksiyon <PERSON>", "applyMessage": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, redaksiyon için seçilen tüm öğeleri kalıcı olarak kaldıracaktır. Geri alınamaz."}, "deleteBookmark": {"title": "Yer <PERSON>ş<PERSON>?", "message": "Bu yer imlerini silmek istediğinizden emin misiniz? Bu işlemi geri alamazsınız."}, "deleteOutline": {"title": "<PERSON><PERSON>?", "message": "Bu anahatları silmek istediğinizden emin misiniz?\n\nİç içe anahatları olan bir anahattı silmek, tüm iç yapının silinmesine ve gerekirse yeniden oluşturulmasına neden olacaktır."}, "selectPage": {"selectTitle": "<PERSON><PERSON> Seçilmedi", "selectMessage": "Lütfen sayfaları seçin ve tekrar deneyin."}, "colorPicker": {"deleteTitle": "<PERSON><PERSON> rengi sil", "deleteMessage": "Seçilen özel renk silinsin mi? Renk paletinizden kaldırılacaktır."}, "colorPalettePicker": {"deleteTitle": "<PERSON><PERSON> rengi sil"}, "multiDeleteAnnotation": {"title": "Ek Açıklamalar Silinsin mi?", "message": "<PERSON>lme işlemi tüm yo<PERSON>ları, yanıtları ve gruplandırmayı kaldırır ve geri alınamaz.\n\n Bu ek açıklamaları silmek istediğinizden emin misiniz?"}, "closeFile": {"title": "İndirmeden kapatılsın mı?", "message": "Bu belgede yapılan değişiklikler var, çalışmanızı indirmeden kapatmak istediğinizden emin misiniz? Bu işlemi geri alamazsınız.", "rejectDownloadButton": "İndirmeden kapat"}, "connectToURL": {"title": "Güvenlik uyarısı", "message": "Bu belge şuraya bağlanmaya çalışıyor:\n\n{{- uri}}\n\n Bu belgeye güveniyorsanız açmak için Onayla'yı tıklayın."}, "sheetTabRenameIssueOne": {"title": "Düzenleme Uyarısı", "message": "Bu sayfa adı zaten var. Lütfen başka bir ad girin."}, "sheetTabRenameIssueTwo": {"title": "Düzenleme Uyarısı", "message": "<PERSON>u <PERSON>fa adı boş olamaz."}, "sheetTabDeleteMessage": {"title": "Düzenleme Uyarısı", "message": "Bu sayfayı silmek istediğinizden emin misiniz?"}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(O)", "ellipse": "(Ö)", "eraser": "(VE)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(İ)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Üst Karakter +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(ESC)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(İLE)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Üst Karakter + Sürükle", "zoom3D": "Üst Karakter + Kaydır"}, "tool": {"pan": "<PERSON><PERSON>", "select": "Seçme", "selectAnOption": "Bir seçenek seçin", "Marquee": "Seçim çerçevesi yakınlaştırması", "Link": "Bağlantı URL'si veya Sayfa", "Standard": "<PERSON><PERSON>", "Custom": "Gelenek"}, "link": {"url": "URL", "page": "Say<PERSON>", "enterurl": "Bağlanmak istediğiniz URL'yi girin", "enterUrlAlt": "URL girin", "insertLink": "Bağlantı Ekle", "insertLinkOrPage": "Bağlantı veya Sayfa Ekle", "enterpage": "Bağlanmak istediğiniz sayfa numarasını girin", "urlLink": "URL bağlantısı"}, "Model3D": {"add3D": "3B nesne ekle", "enterurl": "3B nesnenin URL'sini glTF formatında girin", "enterurlOrLocalFile": "URL'yi girin veya glTF formatında bir 3D nesne yükleyin", "formatError": "Yalnızca glTF (.glb) biçimi desteklenir"}, "OpenFile": {"enterUrlOrChooseFile": "Bir URL girin veya WebViewer'a yüklemek için bir dosya seçin", "enterUrl": "Dosya URL'sini girin", "extension": "<PERSON><PERSON>a u<PERSON>ı<PERSON>ı", "existingFile": "<PERSON><PERSON>a zaten açık", "addTab": "<PERSON><PERSON><PERSON>", "newTab": "<PERSON><PERSON> sekme"}, "datePicker": {"previousMonth": "Geçtiğimiz ay", "nextMonth": "Gelecek ay", "months": {"0": "Ocak ayı", "1": "Ş<PERSON><PERSON>", "2": "Mart", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "Haziran", "6": "Temmuz", "7": "<PERSON><PERSON><PERSON><PERSON>", "8": "<PERSON><PERSON><PERSON><PERSON>", "9": "<PERSON><PERSON>", "10": "Kasım", "11": "Aralık"}, "monthsShort": {"0": "Ocak", "1": "Ş<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "Haziran", "6": "Temmuz", "7": "<PERSON><PERSON><PERSON><PERSON>", "8": "<PERSON><PERSON>", "9": "<PERSON><PERSON>", "10": "kasım", "11": "Aralık"}, "weekdays": {"0": "Pazar", "1": "Pazartesi gü<PERSON>ü", "2": "Salı", "3": "Çarşamba günü", "4": "Perşembe günü", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON><PERSON><PERSON>"}, "weekdaysShort": {"0": "<PERSON><PERSON><PERSON>ş", "1": "<PERSON><PERSON>", "2": "senin", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "Doygunluk"}, "today": "<PERSON><PERSON><PERSON><PERSON>", "invalidDateTime": "Geçersiz <PERSON>h/Saat: <PERSON><PERSON>ş, formatla eşleşmelidir"}, "formField": {"indexPanel": {"formFieldList": "Form Alanı Listesi", "notFields": "Bu belgede form alanı yok"}, "formFieldPopup": {"fieldName": "<PERSON>", "fieldValue": "Varsay<PERSON><PERSON>", "readOnly": "<PERSON><PERSON><PERSON> oku", "multiSelect": "Çoklu seçim", "required": "<PERSON><PERSON><PERSON><PERSON>", "multiLine": "çok satırlı", "apply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "İptal", "flags": "<PERSON>", "fieldSize": "<PERSON>", "options": "Seçenekler", "properties": "<PERSON><PERSON><PERSON><PERSON>", "radioGroups": "<PERSON><PERSON><PERSON> Alan Adına sa<PERSON> radyo düğmeleri aynı gruplamaya ait olacaktır.", "nameRequired": "<PERSON> ad<PERSON> gere<PERSON>", "fieldIndicator": "<PERSON>", "documentFieldIndicator": "Belge Alanı Göstergeleri", "includeFieldIndicator": "<PERSON>", "indicatorPlaceHolders": {"TextFormField": "<PERSON><PERSON><PERSON>", "CheckBoxFormField": "Kontrol", "RadioButtonFormField": "Doldurmak", "ListBoxFormField": "Seçme", "ComboBoxFormField": "Seçme", "SignatureFormField": {"fullSignature": "Burayı imzalayın", "initialsSignature": "İlk Burada"}}, "size": "<PERSON><PERSON>", "width": "Genişlik", "height": "Yükseklik", "invalidField": {"duplicate": "<PERSON> zaten var", "empty": "<PERSON> boş olamaz"}}, "formFieldPanel": {"SignatureFormField": "İmza Alanı Açıklaması", "CheckBoxFormField": "<PERSON><PERSON>çıklaması", "RadioButtonFormField": "Radyo Düğmesi Alanı Açıklaması", "ListBoxFormField": "Liste Kutusu Alanı Açıklaması", "ComboBoxFormField": "Açılan Kutu Alanı Açıklaması", "TextFormField": "<PERSON><PERSON>"}, "apply": "Alanları Uygula", "type": "<PERSON>", "types": {"text": "<PERSON><PERSON>", "signature": "<PERSON><PERSON><PERSON>", "checkbox": "onay kutusu", "radio": "<PERSON><PERSON><PERSON>", "listbox": "Liste kutusu", "combobox": "Açılan kutu", "button": "<PERSON><PERSON><PERSON><PERSON>"}}, "alignmentPopup": {"alignment": "<PERSON><PERSON><PERSON>", "alignLeft": "<PERSON><PERSON>", "alignHorizontalCenter": "<PERSON><PERSON><PERSON>", "alignVerticalCenter": "<PERSON><PERSON>", "alignRight": "<PERSON><PERSON><PERSON>", "alignTop": "<PERSON><PERSON> Hizala", "alignBottom": "Alta Hizala", "distribute": "<PERSON><PERSON><PERSON><PERSON>", "distributeHorizontal": "<PERSON><PERSON><PERSON>", "distributeVertical": "<PERSON><PERSON>"}, "digitalSignatureModal": {"certification": "sertif<PERSON>", "Certification": "sertif<PERSON>", "signature": "imza", "Signature": "<PERSON><PERSON><PERSON>", "valid": "ge<PERSON><PERSON><PERSON>", "invalid": "geçersiz", "unknown": "Bilinmeyen", "title": "{{type}} <PERSON><PERSON><PERSON>", "header": {"documentIntegrity": "Belge Bütünlüğü", "identitiesTrust": "Kimlikler ve Güven", "generalErrors": "<PERSON><PERSON>", "digestStatus": "<PERSON><PERSON><PERSON> Du<PERSON>"}, "documentPermission": {"noChangesAllowed": "{{editor}}, bu be<PERSON> i<PERSON>ikliğe izin verilmediğini belirtti", "formfillingSigningAllowed": "{{editör}}, bu belge için Form Doldurma ve İmzalamaya izin verildiğini belirtti. Başka hiçbir değişikliğe izin verilmez.", "annotatingFormfillingSigningAllowed": "{{editör}}, bu belge için <PERSON> Doldurma, İmzalama ve Yorum Yapmaya izin verildiğini belirtti. Başka hiçbir değişikliğe izin verilmez.", "unrestricted": "{{editör}}, bu belge için herhangi bir kısıtl<PERSON> olmadığını belirtti."}, "digestAlgorithm": {"preamble": "İmzayı imzalamak için kullanılan özet algoritması:", "unknown": "İmzayı imzalamak için kullanılan özet algoritması bilinmiyor."}, "trustVerification": {"none": "Ayrıntılı güven doğrulama sonucu yok.", "current": "Geçerli zamana göre güven doğrulaması denendi", "signing": "<PERSON><PERSON>za zamanına göre güven doğrulaması denendi: {{trustVerificationTime}}", "timestamp": "Güvenli gömülü zaman damgasıyla ilgili olarak güven doğrulaması denendi: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "İmzalayanın k<PERSON>i", "valid": "geçerli.", "unknown": "Bilinmeyen."}, "summaryBox": {"summary": "Dijital {{type}}, {{status}}", "signedBy": ", {{name}} tarafından imzalandı"}}, "digitalSignatureVerification": {"certifier": "<PERSON><PERSON><PERSON><PERSON>", "certified": "sertifikalı", "Certified": "Sertifikalı", "Certification": "Sertifikasyon", "signer": "imza", "signed": "<PERSON><PERSON><PERSON>", "Signed": "imzalandı", "Signature": "<PERSON><PERSON><PERSON>", "by": "ile", "on": "üzerinde", "disallowedChange": "İzin verilmeyen Değişiklik: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "İmzasız imza alanı: {{fieldName}}", "signatureProperties": "<PERSON><PERSON><PERSON>", "trustVerification": {"current": "Kullanılan doğrulama zamanı şimdiki zamandı", "signing": "<PERSON><PERSON><PERSON><PERSON><PERSON>, imzalayanın bilgisayarındaki saatten", "timestamp": "Do<PERSON><PERSON><PERSON>a zamanı, be<PERSON>ye gömülü güvenli zaman damgasından gelir", "verifiedTrust": "<PERSON><PERSON>ven doğrulama sonucu: Doğ<PERSON>landı", "noTrustVerification": "Ayrıntılı güven doğrulama sonucu yok."}, "permissionStatus": {"noPermissionsStatus": "Bildirilecek izin durumu yok.", "permissionsVerificationDisabled": "İzin doğrulama devre dışı bırakıldı.", "hasAllowedChanges": "<PERSON><PERSON><PERSON>, imza izinleri ayarları tarafından izin verilen değişiklikler var.", "invalidatedByDisallowedChanges": "<PERSON><PERSON><PERSON>, imza izinleri ayarları tarafından izin verilmeyen değişiklikler var.", "unmodified": "Belge, değiştirildiğinden beri değiştirilmedi.", "unsupportedPermissionsFeatures": "Desteklenmeyen izin özellikleriyle karşılaşıldı."}, "trustStatus": {"trustVerified": "{{verificationType}} g<PERSON><PERSON><PERSON> başarıyla oluşturdu.", "untrusted": "<PERSON><PERSON><PERSON> k<PERSON>.", "trustVerificationDisabled": "G<PERSON>ven doğrulaması devre dışı bırakıldı.", "noTrustStatus": "Bildirilecek güven durumu yok."}, "digestStatus": {"digestInvalid": "Özet yanlış.", "digestVerified": "Özet doğru.", "digestVerificationDisabled": "Özet doğrulama devre dışı bırakıldı.", "weakDigestAlgorithmButDigestVerifiable": "Özet doğru, ancak özet algoritması zayıf ve güvenli değil.", "noDigestStatus": "Bildirilecek özet durumu yok.", "unsupportedEncoding": "Yüklü SignatureHandler imzanın kodlamasını tanıyamadı", "documentHasBeenAltered": "İmzalandığı tarihten bu yana belgede de<PERSON>şiklik veya bozulma meydana gel<PERSON>."}, "documentStatus": {"noError": "Bildirilecek genel bir hata yok.", "corruptFile": "SignatureHandler dosya bozulması bildirdi.", "unsigned": "İmza henüz kriptografik olarak imzalanmadı.", "badByteRanges": "Signature<PERSON><PERSON><PERSON>, dijital imzadaki ByteRanges'taki bozul<PERSON><PERSON><PERSON> bildiriyor.", "corruptCryptographicContents": "Signature<PERSON><PERSON><PERSON>, dijital imzanın İçeriğindeki bozulmayı bildirir."}, "signatureDetails": {"signatureDetails": "İmza Ayrıntıları", "contactInformation": "İletişim bilgileri", "location": "<PERSON><PERSON>", "reason": "Sebep", "signingTime": "<PERSON><PERSON><PERSON>", "noContactInformation": "İletişim bilgisi verilmedi", "noLocation": "Konum sağlanmadı", "noReason": "<PERSON>ç<PERSON> sebep be<PERSON>medi", "noSigningTime": "İmza zamanı bulunamadı"}, "panelMessages": {"noSignatureFields": "<PERSON>u belgede imza alanı yok", "certificateDownloadError": "Güvenilir bir sertifika indirilmeye çalışılırken hatayla karşılaşıldı", "localCertificateError": "<PERSON><PERSON> sert<PERSON>ika okumayla ilgili bazı sorunlar var"}, "verificationStatus": {"valid": "{{verificationType}} geçerlidir.", "failed": "{{verificationType}} başarısız oldu."}}, "cropPopUp": {"title": "Kırpılacak Sayfalar", "allPages": "<PERSON><PERSON><PERSON>", "singlePage": "Geçerli sayfa", "multiPage": "Sayfayı Belirtin", "cropDimensions": "Kırpma Boyutları", "dimensionInput": {"unitOfMeasurement": "<PERSON><PERSON><PERSON>", "autoTrim": "Otomatik kırp", "autoTrimCustom": "Gelenek"}, "cropModal": {"applyTitle": "Kırpma uygulansın mı?", "applyMessage": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, seçilen tüm seçili sayfaları kalıcı olarak kırpacaktır. Geri alınamaz.", "cancelTitle": "<PERSON><PERSON><PERSON>ma iptal edilsin mi?", "cancelMessage": "Seçilen tüm sayfaları kırpmayı iptal etmek istediğinizden emin misiniz?"}}, "snippingPopUp": {"title": "<PERSON><PERSON><PERSON>", "clipboard": "<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "cropAndRemove": "Kırp ve Kaldır", "snippingModal": {"applyTitle": "Kesmeyi Uygula?", "applyMessage": "Bu eylem belirtilen alanı kalıcı olarak keser ve diğer sayfaları kaldırır. Geri alınamaz.", "cancelTitle": "Kesmeyi İptal Et?", "cancelMessage": "Ke<PERSON>eyi durdurmak istediğinizden emin misiniz?"}}, "textEditingPanel": {"paragraph": "Paragra<PERSON>", "text": "<PERSON><PERSON>"}, "redactionPanel": {"noMarkedRedactions": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> işaretleyerek veya arama yaparak redaksiyona başlayın.", "redactionSearchPlaceholder": "Klavyeye veya desenlere göre düzenleme", "redactionCounter": "Redaksiyon için i<PERSON>lendi", "clearMarked": "Temizlemek", "redactAllMarked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "redactionItems": "Düzenleme <PERSON>", "redactionItem": {"regionRedaction": "<PERSON><PERSON><PERSON>", "fullPageRedaction": "Tam <PERSON> red<PERSON>", "fullVideoFrameRedaction": "Video Redaksiyonu", "audioRedaction": "<PERSON><PERSON>", "fullVideoFrameAndAudioRedaction": "Video ve <PERSON>", "image": "resim"}, "expand": "Genişletmek", "collapse": "Çöküş", "searchResults": "<PERSON>ma <PERSON>", "search": {"creditCards": "Kredi kartı", "phoneNumbers": "Telefon numaraları", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emails": "e-postalar", "pattern": "<PERSON><PERSON><PERSON>", "start": "Aramanızı yapmaya başlayın"}}, "wv3dPropertiesPanel": {"propertiesHeader": "<PERSON><PERSON><PERSON><PERSON>", "miscValuesHeader": "Tümü", "emptyPanelMessage": "Özelliklerini görüntülemek için bir öğe seçin."}, "watermarkPanel": {"textWatermark": "<PERSON><PERSON>", "uploadImage": "Fotoğ<PERSON><PERSON>", "browse": "Araştır", "watermarkOptions": "<PERSON><PERSON><PERSON><PERSON>", "watermarks": "filigranlar"}, "portfolio": {"createPDFPortfolio": "PDF Portföyü Oluştur", "uploadFiles": "Dosyaları yükle", "uploadFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addFiles": "<PERSON><PERSON><PERSON>", "addFile": "<PERSON><PERSON><PERSON>", "addFolder": "Klasörü eklemek", "createFolder": "<PERSON><PERSON><PERSON><PERSON>", "portfolioPanelTitle": "PDF Portföyü", "portfolioNewFolder": "<PERSON><PERSON>", "portfolioDocumentTitle": "Belge başlığı", "portfolioFolderPlaceholder": "Klasör adı", "portfolioFilePlaceholder": "<PERSON><PERSON><PERSON> adı", "folderNameAlreadyExists": "Klasör adı zaten var", "fileNameAlreadyExists": "<PERSON><PERSON>a adı zaten var", "openFile": "<PERSON><PERSON> sek<PERSON> aç", "fileAlreadyExists": "<PERSON><PERSON><PERSON> zaten mevcut", "fileAlreadyExistsMessage": "\"{{fileName}}\" dosyası portföyde zaten var.", "deletePortfolio": "\"{{fileName}}\" dosyasını silmek istediğinizden emin misiniz?", "reselect": "yeniden se<PERSON>"}, "languageModal": {"selectLanguage": "<PERSON><PERSON>"}, "officeEditor": {"bold": "Gözü pek", "italic": "İtalik", "underline": "Altını çizmek", "textColor": "metin rengi", "leftAlign": "<PERSON>a hizala", "centerAlign": "<PERSON><PERSON><PERSON>", "rightAlign": "<PERSON><PERSON><PERSON>", "justify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineSpacing": "Satır ve paragraf aralığı", "lineSpacingMenu": "<PERSON><PERSON><PERSON>r <PERSON>", "bulletList": "Madde işaretli liste", "numberList": "Numaralandırılmış liste", "decreaseIndent": "<PERSON><PERSON><PERSON><PERSON>", "increaseIndent": "<PERSON><PERSON><PERSON><PERSON> artır", "nonPrintingCharacters": "Yaz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "insertLink": "Bağlantı ekle", "insertImage": "<PERSON><PERSON><PERSON>", "image": "Resim", "table": "<PERSON><PERSON>", "insertRowAbove": "Yukarıya Satır <PERSON>", "insertRowBelow": "Aşağıya Satır Ekle", "insertColumnRight": "<PERSON><PERSON><PERSON>", "insertColumnLeft": "<PERSON><PERSON>", "deleteRow": "Sırayı sil", "deleteColumn": "<PERSON><PERSON><PERSON><PERSON>", "deleteTable": "<PERSON><PERSON><PERSON><PERSON>", "deleted": "<PERSON><PERSON><PERSON>:", "added": "Katma:", "editing": "Düzenleme", "editingDescription": "Dokümanı düzenle", "reviewing": "İnceleniyor", "reviewingDescription": "<PERSON>ner<PERSON> bulunun", "viewOnly": "<PERSON><PERSON><PERSON>", "viewOnlyDescription": "<PERSON><PERSON><PERSON> o<PERSON><PERSON>", "notSupportedOnMobile": "Office düzenlemesi mobil cihazlarda desteklenmez.", "previewAllChanges": "Tüm değişiklikleri önizleyin", "accept": "Kabul etmek", "reject": "Reddetmek", "pastingTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pastingMessage": "Tarayıcınızda yapıştırma desteklenmiyor. <PERSON><PERSON>un yerine bir klavye kısayolu kullanabilirsiniz", "pastingWithoutFormatTitle": "Biçimlendirme olmadan ya<PERSON>ış<PERSON>ırma mümkün de<PERSON>", "pastingWithoutFormatMessage": "Tarayıcınızda biçimlendirme olmadan yapıştırma desteklenmiyor. Bunun yerine bir klavye kısayolu kullanabilirsiniz", "breaks": "<PERSON><PERSON><PERSON>", "pageBreak": "<PERSON><PERSON> sonu", "pageBreakDescription": "Sayfayı bitir ve yeni sayfada başla", "sectionBreakNextPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "sectionBreakNextPageDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> sonu ekle ve bir sonraki say<PERSON>dan ba<PERSON>la", "sectionBreakContinuous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sectionBreakContinuousDescription": "Bölüm sonu ekle ve aynı sayfada devam et", "section": "Bölüm", "margins": "<PERSON><PERSON><PERSON>", "normal": "Normal", "narrow": "Dar", "moderate": "<PERSON><PERSON><PERSON>", "wide": "Geniş", "top": "Te<PERSON>", "bottom": "Alt", "left": "Sol", "right": "Sağ", "customMargins": "Özel Kenar <PERSON>şlukları", "customMarginsDescription": "Özel kenar boşluklarını tanımlayın", "header": {"0": "Başlık", "1": "İlk Sayfa Başlığı", "2": "Çift Sayfa Başlığı", "3": "Tek Sayfa Başlığı", "-1": "Geçersiz Başlık"}, "footer": {"0": "Altbilgi", "1": "İlk Sayfa Alt Bilgisi", "2": "Çift Sayfa Alt Bilgisi", "3": "Tek Sayfa Alt Bilgisi", "-1": "Geçersiz Altbilgi"}, "options": "Seçenekler", "pageOptions": "<PERSON><PERSON>", "removeHeader": "Başlığı Kaldır", "removeFooter": "Altbilgiyi Kaldır", "headerFooterOptionsModal": {"title": "Üstbilgi ve altbilgi biçimi", "headerFromTop": "Üstten Başlık", "footerFromBottom": "Alt Bilgi Alttan", "layouts": {"layout": "D<PERSON>zen", "noSelection": "<PERSON><PERSON><PERSON>", "differentFirstPage": "Farklı İlk Sayfa", "differentEvenOddPages": "Farklı Çift ve Tek Sayfalar", "differentFirstEvenOddPages": "Farklı İlk, Çift ve Tek Sayfalar"}}, "marginsModal": {"title": "<PERSON><PERSON>", "leftMargin": "Sol Kenar Boşluğu", "rightMargin": "Sağ Kenar <PERSON>luğ<PERSON>", "topMargin": "Üst Marj", "bottomMargin": "Alt Marj"}, "lineSpacingOptions": {"15": "1.5", "115": "1.15", "single": "<PERSON><PERSON>", "double": "Çift", "custom": "Gelenek"}, "numberDropdown": {"6": "Sayı Latin Roma 1", "7": "Sayı Ondalık", "8": "Sayı Latin Roma 2", "10": "<PERSON>ce Roma", "11": "Roma Latin Rakamı", "dropdownLabel": "Numaralandırma Ö<PERSON>"}, "bulletDropdown": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "Kontrol etmek", "5": "Ok", "dropdownLabel": "<PERSON><PERSON><PERSON>"}, "fontSize": {"dropdownLabel": "Yazı Tipi Boyutu"}, "fontStyles": {"dropdownLabel": "Yazı Stilleri"}, "fontFamily": {"dropdownLabel": "Yazı Tipi Ailesi"}}, "spreadsheetEditor": {"editing": "Düzenleme", "viewOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editingDescription": "Belgeyi düzenle", "viewOnlyDescription": "<PERSON><PERSON><PERSON>", "bold": "Gözü pek", "italic": "İtalik", "underline": "Altı çizili", "strikethrough": "Üstü çizili", "strikeout": "Vuruş", "cellBorderStyle": "Sınır Stili", "merge": "Birleştir", "unmerge": "Birleştirmeyi kaldır", "cellFormat": "<PERSON><PERSON><PERSON>", "automaticFormat": "Otomatik", "plainTextFormat": "<PERSON><PERSON><PERSON> metin", "increaseDecimalFormat": "Ondalık sayıyı artır", "decreaseDecimalFormat": "Ondalık sayıyı azalt", "numberFormat": "<PERSON><PERSON>", "percentFormat": "<PERSON><PERSON>z<PERSON>", "accountingFormat": "<PERSON><PERSON><PERSON><PERSON>", "financialFormat": "Finansal", "currencyFormat": "Para birimi", "currencyRoundedFormat": "Yuvarlatılmış para birimi", "calendarFormat": "<PERSON><PERSON><PERSON>", "clockHourFormat": "Zaman", "calendarTimeFormat": "<PERSON><PERSON><PERSON>", "formatAsCurrency": "Para birimi olarak biçimlendir", "formatAsPercent": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ak <PERSON>", "formatAsDecDecimal": "Ondalık noktayı azalt", "formatAsIncDecimal": "Ondalık noktayı artır", "textColor": "<PERSON><PERSON>", "cellBackgroundColor": "<PERSON><PERSON><PERSON>", "cellBorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textLabel": "<PERSON><PERSON>", "backgroundLabel": "Arka plan", "borderLabel": "Sınır", "textAlignment": "<PERSON><PERSON>", "topAlign": "<PERSON><PERSON>", "middleAlign": "<PERSON><PERSON>", "bottomAlign": "Alt hizalama", "cellAdjustment": "<PERSON><PERSON><PERSON>", "cellFormatMoreOptions": "Daha fazla hücre biçimi <PERSON>ği", "insertColumnLeft": "<PERSON> s<PERSON> ekle", "insertColumnRight": "<PERSON><PERSON><PERSON>", "insertRowTop": "<PERSON><PERSON><PERSON><PERSON> satır e<PERSON>", "insertRowBottom": "Aşağ<PERSON>ya satır e<PERSON>", "insertColumnShiftDown": "Hücreleri ekle ve aşağı kaydır", "insertColumnShiftRight": "Hücreleri ekle ve sağa kaydır", "deleteColumn": "<PERSON><PERSON><PERSON><PERSON> sil", "deleteRow": "Satırı sil", "deleteColumnShiftUp": "Hücreleri sil ve yukarı kaydır", "deleteColumnShiftLeft": "Hücreleri sil ve sola kaydır", "clearBorder": "Sınırı Temizle", "cellBorderAll": "<PERSON><PERSON><PERSON>", "cellBorderInside": "Sınırların İçinde", "cellBorderOutside": "Dış Sınırlar", "cellBorderTop": "Üst Sınır", "cellBorderBottom": "Alt Sınır", "cellBorderLeft": "Sol Sınır", "cellBorderRight": "Sağ Sınır", "cellBorderVertical": "<PERSON><PERSON>", "cellBorderHorizontal": "<PERSON><PERSON><PERSON>", "cellBorderNone": "Sınır Yok", "borders": "Sınırlar", "fontSize": {"dropdownLabel": "Yazı Tipi Boyutu"}, "fontFamily": {"dropdownLabel": "Yazı Tipi Ailesi"}, "blankSheet": "Boş Sayfa"}, "insertPageModal": {"title": "<PERSON><PERSON>", "tabs": {"blank": "Boşluk", "upload": "Yüklemek"}, "pagePlacements": {"header": "<PERSON><PERSON>", "above": "Sayfanın Ü<PERSON>ü", "below": "<PERSON><PERSON><PERSON><PERSON>"}, "pageLocations": {"header": "<PERSON><PERSON>", "specify": "Sayfayı Belirtin", "specifyLocation": "<PERSON><PERSON>", "amount": "Sayfa Sayısı", "total": "Toplam", "pages": "<PERSON><PERSON><PERSON>"}, "pageDimensions": {"header": "Sayfa Boyutları", "subHeader": "<PERSON><PERSON>", "presets": {"letter": "Mektup", "halfLetter": "yarım harf", "juniorLegal": "küçük yasal", "custom": "Gelenek"}, "units": "<PERSON><PERSON><PERSON>"}, "browse": "Dosyalara <PERSON>", "fileSelected": {"title": "Eklenecek Sayfaları Seçin"}, "button": "<PERSON><PERSON>", "selectPages": "Eklenecek Sayfaları Seçin", "page": "Say<PERSON>", "warning": {"title": "Çık yeni sayfa eklensin mi?", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, o ana kadar yaptığınız tüm seçimleri iptal edecektir. Hala çıkmak istediğinizden emin misiniz?"}}, "multiViewer": {"dragAndDrop": "Karşılaştırmak için dosyanızı buraya sürükleyip bırakın", "or": "<PERSON><PERSON><PERSON>", "browse": "Dosyalara <PERSON>", "startSync": "Senkroniza<PERSON><PERSON><PERSON>", "stopSync": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "closeDocument": "Belgeyi Kapat", "save": "<PERSON><PERSON><PERSON>", "comparePanel": {"Find": "Belgede bul", "changesList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "change": "Değiştirmek", "old": "Eskimiş", "new": "<PERSON><PERSON>", "page": "Say<PERSON>", "textContent": "<PERSON><PERSON>", "delete": "silmek", "insert": "sokmak", "edit": "<PERSON><PERSON><PERSON><PERSON>"}}, "saveModal": {"close": "Ka<PERSON><PERSON>", "saveAs": "Farklı kaydet", "general": "<PERSON><PERSON>", "fileName": "<PERSON><PERSON><PERSON> adı", "fileType": "<PERSON><PERSON><PERSON> tipi", "fileLocation": "<PERSON><PERSON><PERSON> k<PERSON>", "browse": "Araştır", "pageRange": "Sayfa aralığı", "all": "<PERSON><PERSON><PERSON>", "currentView": "Mev<PERSON> g<PERSON>", "currentPage": "Geçerli sayfa", "specifyPage": "Sayfayı Belirtin", "properties": "Özellikleri", "includeAnnotation": "Ek Açıklamaları Dahil Et", "includeComments": "Yorumları Dahil Et", "watermark": "<PERSON>li<PERSON><PERSON>", "addWatermark": "<PERSON><PERSON><PERSON><PERSON> ekle", "save": "Dosyayı kaydet", "pageError": "Lütfen 1 - arasında bir aralık girin ", "fileNameCannotBeEmpty": "<PERSON><PERSON><PERSON> boş olamaz"}, "filePicker": {"dragAndDrop": "Dosyanızı buraya sürükleyip bırakın", "selectFile": "dosyanızı buradan seçin", "or": "<PERSON><PERSON><PERSON>"}, "stylePanel": {"headings": {"styles": "Stiller", "annotation": "<PERSON><PERSON><PERSON>", "annotations": "Ek açıklamalar", "tool": "<PERSON><PERSON>", "textStyles": "<PERSON><PERSON>", "currentColor": "Mevcut Renk", "customColors": "<PERSON><PERSON>", "redactionTextLabel": "<PERSON><PERSON>", "redactionMarkOutline": "Anahattı İşaretle", "redactionFill": "<PERSON><PERSON><PERSON><PERSON>", "redactionTextPlaceholder": "<PERSON><PERSON> et<PERSON> e<PERSON>", "contentEdit": "İçerik Düzenleme"}, "lineStyles": {"startLineStyleLabel": "Başlangıç Çizgisi Stili", "middleLineStyleLabel": "Orta Çizgi Stili", "endLineStyleLabel": "<PERSON>"}, "addColorToCustom": "<PERSON><PERSON> renklere ekle", "noToolSelected": "Araç <PERSON>ini görüntülemek için bir araç seçin", "noToolStyle": "<PERSON><PERSON> herhangi bir stil özelliği içermiyor.", "noSharedToolStyle": "Seçili açıklamalar hiçbir stil özelliğini paylaşmıyor.", "lineEnding": {"start": {"dropdownLabel": "Satır <PERSON>langıcı"}, "end": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "middle": {"dropdownLabel": "<PERSON><PERSON><PERSON>"}}, "borderStyle": {"dropdownLabel": "Sınır"}}, "signatureListPanel": {"header": "<PERSON><PERSON><PERSON>", "newSignature": "<PERSON><PERSON>", "newSignatureAndInitial": "<PERSON><PERSON>", "signatureList": {"signature": "<PERSON><PERSON><PERSON>", "initials": "<PERSON><PERSON> ha<PERSON>"}}, "rubberStampPanel": {"header": "<PERSON><PERSON><PERSON>", "standard": "<PERSON><PERSON>"}, "colorPickerModal": {"modalTitle": "Renk Seçici"}, "accessibility": {"landmarks": {"topHeader": "Üst Başlık", "leftHeader": "Sol Başlık", "rightHeader": "Sağ Başlık", "bottomHeader": "Alt Başlık", "documentContent": "Belge İçeriği"}, "label": "Erişilebilirlik", "accessibilityMode": "Erişilebilirlik Modu", "skipTo": "Atla", "document": "Belge", "notes": "Notlar"}, "formulaBar": {"label": "<PERSON><PERSON><PERSON>", "range": "Menzil", "formulas": "<PERSON><PERSON><PERSON>", "sumif": "Bir aralıktaki koşullu toplam", "sumsq": "Bir aralığın karelerinin toplamını döndürür", "sum": "Bir aralıktaki tüm sayıları toplar", "asinh": "B<PERSON> sayının ters hiperbolik sinüsünü döndürür", "acos": "Bir sayının ark kosinüsünü radyan cinsinden döndürür", "cosh": "Bir sayının hiperbolik kosinüsünü döndürür", "iseven": "Bir sayının çift olup olmadığını kontrol eder", "isodd": "Bir sayının tek olup olmadığını kontrol eder"}}