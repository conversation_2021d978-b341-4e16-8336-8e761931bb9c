(window.webpackJsonp = window.webpackJsonp || []).push([
	[173],
	{
		1431: function (_, Y, t) {
			_.exports = (function (_) {
				"use strict";
				var Y = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					t = {
						name: "ja",
						weekdays: "日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split(
							"_",
						),
						weekdaysShort: "日_月_火_水_木_金_土".split("_"),
						weekdaysMin: "日_月_火_水_木_金_土".split("_"),
						months: "1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split(
							"_",
						),
						monthsShort:
							"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),
						ordinal: function (_) {
							return _ + "日";
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "YYYY/MM/DD",
							LL: "YYYY年M月D日",
							LLL: "YYYY年M月D日 HH:mm",
							LLLL: "YYYY年M月D日 dddd HH:mm",
							l: "YYYY/MM/DD",
							ll: "YYYY年M月D日",
							lll: "YYYY年M月D日 HH:mm",
							llll: "YYYY年M月D日(ddd) HH:mm",
						},
						meridiem: function (_) {
							return _ < 12 ? "午前" : "午後";
						},
						relativeTime: {
							future: "%s後",
							past: "%s前",
							s: "数秒",
							m: "1分",
							mm: "%d分",
							h: "1時間",
							hh: "%d時間",
							d: "1日",
							dd: "%d日",
							M: "1ヶ月",
							MM: "%dヶ月",
							y: "1年",
							yy: "%d年",
						},
					};
				return Y.default.locale(t, null, !0), t;
			})(t(105));
		},
	},
]);
//# sourceMappingURL=chunk.173.js.map
