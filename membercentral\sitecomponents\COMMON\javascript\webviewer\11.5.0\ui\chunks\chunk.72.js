(window.webpackJsonp = window.webpackJsonp || []).push([
	[72],
	{
		1977: function (t, e, o) {
			var n = o(30),
				r = o(1978);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[t.i, r, ""]]);
			var i = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const r = e[n];
						if (0 === n)
							r.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							r.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(r, i);
			t.exports = r.locals || {};
		},
		1978: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				'.open.OfficeEditorMarginsModal{visibility:visible}.closed.OfficeEditorMarginsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.OfficeEditorMarginsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.OfficeEditorMarginsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.cancel:hover,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.OfficeEditorMarginsModal .footer .modal-button.cancel,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.OfficeEditorMarginsModal .footer .modal-button.cancel.disabled,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.OfficeEditorMarginsModal .footer .modal-button.cancel.disabled span,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.OfficeEditorMarginsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.OfficeEditorMarginsModal .modal-container .wrapper .modal-content{padding:10px}.OfficeEditorMarginsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.OfficeEditorMarginsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.OfficeEditorMarginsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.OfficeEditorMarginsModal .footer .modal-button.confirm{margin-left:4px}.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .footer .modal-button{padding:23px 8px}}.OfficeEditorMarginsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .swipe-indicator{width:32px}}.OfficeEditorMarginsModal{flex-direction:column}.OfficeEditorMarginsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.OfficeEditorMarginsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.OfficeEditorMarginsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input:after{content:"cm";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.OfficeEditorMarginsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.OfficeEditorMarginsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.OfficeEditorMarginsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.OfficeEditorMarginsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}.OfficeEditorMarginsModal .modal-container .modal-body{display:grid;grid-template-columns:216px 216px;grid-row-gap:24px}.OfficeEditorMarginsModal .ui__input__wrapper{width:216px}',
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2059: function (t, e, o) {
			"use strict";
			o.r(e);
			o(289),
				o(90),
				o(41),
				o(19),
				o(12),
				o(13),
				o(8),
				o(14),
				o(10),
				o(9),
				o(11),
				o(16),
				o(15),
				o(20),
				o(18),
				o(54),
				o(22),
				o(61),
				o(62),
				o(63),
				o(64),
				o(37),
				o(39),
				o(23),
				o(24),
				o(40),
				o(60);
			var n = o(0),
				r = o.n(n),
				i = o(6),
				a = o(347),
				c = o(2),
				d = o(5),
				l = o(42),
				u = o(351),
				f = o(2007),
				s = o(1),
				p = o(26),
				h = o(72);
			o(1977);
			function m(t) {
				return (m =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function g() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ g =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					o = e.hasOwnProperty,
					n =
						Object.defineProperty ||
						function (t, e, o) {
							t[e] = o.value;
						},
					r = "function" == typeof Symbol ? Symbol : {},
					i = r.iterator || "@@iterator",
					a = r.asyncIterator || "@@asyncIterator",
					c = r.toStringTag || "@@toStringTag";
				function d(t, e, o) {
					return (
						Object.defineProperty(t, e, {
							value: o,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					d({}, "");
				} catch (t) {
					d = function (t, e, o) {
						return (t[e] = o);
					};
				}
				function l(t, e, o, r) {
					var i = e && e.prototype instanceof s ? e : s,
						a = Object.create(i.prototype),
						c = new k(r || []);
					return n(a, "_invoke", { value: E(t, o, c) }), a;
				}
				function u(t, e, o) {
					try {
						return { type: "normal", arg: t.call(e, o) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = l;
				var f = {};
				function s() {}
				function p() {}
				function h() {}
				var b = {};
				d(b, i, function () {
					return this;
				});
				var y = Object.getPrototypeOf,
					v = y && y(y(L([])));
				v && v !== e && o.call(v, i) && (b = v);
				var x = (h.prototype = s.prototype = Object.create(b));
				function w(t) {
					["next", "throw", "return"].forEach(function (e) {
						d(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function M(t, e) {
					var r;
					n(this, "_invoke", {
						value: function (n, i) {
							function a() {
								return new e(function (r, a) {
									!(function n(r, i, a, c) {
										var d = u(t[r], t, i);
										if ("throw" !== d.type) {
											var l = d.arg,
												f = l.value;
											return f && "object" == m(f) && o.call(f, "__await")
												? e.resolve(f.__await).then(
														function (t) {
															n("next", t, a, c);
														},
														function (t) {
															n("throw", t, a, c);
														},
													)
												: e.resolve(f).then(
														function (t) {
															(l.value = t), a(l);
														},
														function (t) {
															return n("throw", t, a, c);
														},
													);
										}
										c(d.arg);
									})(n, i, r, a);
								});
							}
							return (r = r ? r.then(a, a) : a());
						},
					});
				}
				function E(t, e, o) {
					var n = "suspendedStart";
					return function (r, i) {
						if ("executing" === n)
							throw new Error("Generator is already running");
						if ("completed" === n) {
							if ("throw" === r) throw i;
							return S();
						}
						for (o.method = r, o.arg = i; ; ) {
							var a = o.delegate;
							if (a) {
								var c = O(a, o);
								if (c) {
									if (c === f) continue;
									return c;
								}
							}
							if ("next" === o.method) o.sent = o._sent = o.arg;
							else if ("throw" === o.method) {
								if ("suspendedStart" === n) throw ((n = "completed"), o.arg);
								o.dispatchException(o.arg);
							} else "return" === o.method && o.abrupt("return", o.arg);
							n = "executing";
							var d = u(t, e, o);
							if ("normal" === d.type) {
								if (
									((n = o.done ? "completed" : "suspendedYield"), d.arg === f)
								)
									continue;
								return { value: d.arg, done: o.done };
							}
							"throw" === d.type &&
								((n = "completed"), (o.method = "throw"), (o.arg = d.arg));
						}
					};
				}
				function O(t, e) {
					var o = e.method,
						n = t.iterator[o];
					if (void 0 === n)
						return (
							(e.delegate = null),
							("throw" === o &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								O(t, e),
								"throw" === e.method)) ||
								("return" !== o &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + o + "' method",
									)))),
							f
						);
					var r = u(n, t.iterator, e.arg);
					if ("throw" === r.type)
						return (
							(e.method = "throw"), (e.arg = r.arg), (e.delegate = null), f
						);
					var i = r.arg;
					return i
						? i.done
							? ((e[t.resultName] = i.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								f)
							: i
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							f);
				}
				function _(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function j(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function k(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(_, this),
						this.reset(!0);
				}
				function L(t) {
					if (t) {
						var e = t[i];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var n = -1,
								r = function e() {
									for (; ++n < t.length; )
										if (o.call(t, n)) return (e.value = t[n]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (r.next = r);
						}
					}
					return { next: S };
				}
				function S() {
					return { value: void 0, done: !0 };
				}
				return (
					(p.prototype = h),
					n(x, "constructor", { value: h, configurable: !0 }),
					n(h, "constructor", { value: p, configurable: !0 }),
					(p.displayName = d(h, c, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === p || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, h)
								: ((t.__proto__ = h), d(t, c, "GeneratorFunction")),
							(t.prototype = Object.create(x)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					w(M.prototype),
					d(M.prototype, a, function () {
						return this;
					}),
					(t.AsyncIterator = M),
					(t.async = function (e, o, n, r, i) {
						void 0 === i && (i = Promise);
						var a = new M(l(e, o, n, r), i);
						return t.isGeneratorFunction(o)
							? a
							: a.next().then(function (t) {
									return t.done ? t.value : a.next();
								});
					}),
					w(x),
					d(x, c, "Generator"),
					d(x, i, function () {
						return this;
					}),
					d(x, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							o = [];
						for (var n in e) o.push(n);
						return (
							o.reverse(),
							function t() {
								for (; o.length; ) {
									var n = o.pop();
									if (n in e) return (t.value = n), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = L),
					(k.prototype = {
						constructor: k,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(j),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										o.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function n(o, n) {
								return (
									(a.type = "throw"),
									(a.arg = t),
									(e.next = o),
									n && ((e.method = "next"), (e.arg = void 0)),
									!!n
								);
							}
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var i = this.tryEntries[r],
									a = i.completion;
								if ("root" === i.tryLoc) return n("end");
								if (i.tryLoc <= this.prev) {
									var c = o.call(i, "catchLoc"),
										d = o.call(i, "finallyLoc");
									if (c && d) {
										if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
										if (this.prev < i.finallyLoc) return n(i.finallyLoc);
									} else if (c) {
										if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
									} else {
										if (!d)
											throw new Error("try statement without catch or finally");
										if (this.prev < i.finallyLoc) return n(i.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var n = this.tryEntries.length - 1; n >= 0; --n) {
								var r = this.tryEntries[n];
								if (
									r.tryLoc <= this.prev &&
									o.call(r, "finallyLoc") &&
									this.prev < r.finallyLoc
								) {
									var i = r;
									break;
								}
							}
							i &&
								("break" === t || "continue" === t) &&
								i.tryLoc <= e &&
								e <= i.finallyLoc &&
								(i = null);
							var a = i ? i.completion : {};
							return (
								(a.type = t),
								(a.arg = e),
								i
									? ((this.method = "next"), (this.next = i.finallyLoc), f)
									: this.complete(a)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								f
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.finallyLoc === t)
									return this.complete(o.completion, o.afterLoc), j(o), f;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.tryLoc === t) {
									var n = o.completion;
									if ("throw" === n.type) {
										var r = n.arg;
										j(o);
									}
									return r;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, o) {
							return (
								(this.delegate = { iterator: L(t), resultName: e, nextLoc: o }),
								"next" === this.method && (this.arg = void 0),
								f
							);
						},
					}),
					t
				);
			}
			function b(t, e, o, n, r, i, a) {
				try {
					var c = t[i](a),
						d = c.value;
				} catch (t) {
					return void o(t);
				}
				c.done ? e(d) : Promise.resolve(d).then(n, r);
			}
			function y(t) {
				return function () {
					var e = this,
						o = arguments;
					return new Promise(function (n, r) {
						var i = t.apply(e, o);
						function a(t) {
							b(i, n, r, a, c, "next", t);
						}
						function c(t) {
							b(i, n, r, a, c, "throw", t);
						}
						a(void 0);
					});
				};
			}
			function v(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								r,
								i,
								a,
								c = [],
								d = !0,
								l = !1;
							try {
								if (((i = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									d = !1;
								} else
									for (
										;
										!(d = (n = i.call(o)).done) &&
										(c.push(n.value), c.length !== e);
										d = !0
									);
							} catch (t) {
								(l = !0), (r = t);
							} finally {
								try {
									if (
										!d &&
										null != o.return &&
										((a = o.return()), Object(a) !== a)
									)
										return;
								} finally {
									if (l) throw r;
								}
							}
							return c;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return x(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return x(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function x(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var w = function () {
				var t = v(Object(a.a)(), 1)[0],
					e = Object(i.d)(),
					o = v(Object(n.useState)(""), 2),
					m = o[0],
					b = o[1],
					x = v(Object(n.useState)(""), 2),
					w = x[0],
					M = x[1],
					E = v(Object(n.useState)(""), 2),
					O = E[0],
					_ = E[1],
					j = v(Object(n.useState)(""), 2),
					k = j[0],
					L = j[1],
					S = v(
						Object(n.useState)({ left: "", right: "", top: "", bottom: "" }),
						2,
					),
					A = S[0],
					C = S[1],
					I = v(Object(n.useState)(0), 2),
					N = I[0],
					T = I[1],
					F = v(Object(n.useState)(0), 2),
					R = F[0],
					D = F[1],
					G = v(Object(n.useState)(0), 2),
					P = G[0],
					z = G[1],
					H = v(Object(n.useState)(0), 2),
					B = H[0],
					W = H[1],
					V = v(Object(n.useState)(p.p.CM), 2),
					q = V[0],
					J = V[1];
				Object(n.useEffect)(
					y(
						g().mark(function t() {
							var e, o, n, r;
							return g().wrap(function (t) {
								for (;;)
									switch ((t.prev = t.next)) {
										case 0:
											return (
												J(p.p.CM),
												(t.next = 3),
												s.a.getOfficeEditor().getEditingPageNumber()
											);
										case 3:
											return (
												(e = t.sent),
												(o = s.a.getDocumentViewer().getPageHeight(e) / p.z),
												W(s.a.getDocumentViewer().getPageWidth(e) / p.z),
												z(o * p.o),
												(t.next = 9),
												s.a.getOfficeEditor().getSectionMargins(q)
											);
										case 9:
											(n = t.sent),
												(r = {
													left: n.left.toFixed(2),
													right: n.right.toFixed(2),
													top: n.top.toFixed(2),
													bottom: n.bottom.toFixed(2),
												}),
												b(r.left),
												M(r.right),
												_(r.top),
												L(r.bottom),
												C(r);
										case 16:
										case "end":
											return t.stop();
									}
							}, t);
						}),
					),
					[],
				),
					Object(n.useEffect)(
						function () {
							U();
						},
						[B, m, w],
					);
				var Y = function (t, e) {
						return t - e - p.s;
					},
					U = function () {
						var t = {
								left: q === p.p.CM ? m / p.c : m,
								right: q === p.p.CM ? w / p.c : w,
							},
							e = Y(B, t.right),
							o = Y(B, t.left);
						T(e), D(o);
					},
					$ = function (t, e) {
						"" === t.target.value && e("0");
					},
					K = function (t, e, o) {
						e(Object(h.k)(t.target.value, o, q));
					},
					Q = function () {
						e(c.a.closeElement(d.a.OFFICE_EDITOR_MARGINS_MODAL)),
							setTimeout(function () {
								s.a.getOfficeEditor().focusContent();
							}, 0);
					},
					X = [
						{
							id: "leftMarginInput",
							label: t("officeEditor.marginsModal.leftMargin"),
							value: m,
							onChange: function (t) {
								return K(t, b, N);
							},
							onBlur: function (t) {
								return $(t, b);
							},
						},
						{
							id: "rightMarginInput",
							label: t("officeEditor.marginsModal.rightMargin"),
							value: w,
							onChange: function (t) {
								return K(t, M, R);
							},
							onBlur: function (t) {
								return $(t, M);
							},
						},
						{
							id: "topMarginInput",
							label: t("officeEditor.marginsModal.topMargin"),
							value: O,
							onChange: function (t) {
								return K(t, _, P);
							},
							onBlur: function (t) {
								return $(t, _);
							},
						},
						{
							id: "bottomMarginInput",
							label: t("officeEditor.marginsModal.bottomMargin"),
							value: k,
							onChange: function (t) {
								return K(t, L, P);
							},
							onBlur: function (t) {
								return $(t, L);
							},
						},
					];
				return r.a.createElement(
					"div",
					{
						className: "OfficeEditorMarginsModal",
						"data-element": d.a.OFFICE_EDITOR_MARGINS_MODAL,
					},
					r.a.createElement(
						u.a,
						{
							title: t("officeEditor.marginsModal.title"),
							closehandler: Q,
							onCloseClick: Q,
							swipeToClose: !0,
							isOpen: !0,
						},
						r.a.createElement(
							"div",
							{ className: "modal-body" },
							X.map(function (t) {
								return r.a.createElement(
									"div",
									{ key: t.id, className: "input-container" },
									r.a.createElement(
										"label",
										{ htmlFor: t.id, className: "label" },
										t.label,
									),
									r.a.createElement(f.a, {
										type: "number",
										id: t.id,
										onChange: t.onChange,
										onBlur: t.onBlur,
										value: t.value,
										min: "0",
										step: "any",
									}),
								);
							}),
						),
						r.a.createElement(
							"div",
							{ className: "footer" },
							r.a.createElement(l.a, {
								onClick: function () {
									if (
										(e(c.a.closeElement(d.a.OFFICE_EDITOR_MARGINS_MODAL)),
										m !== A.left ||
											w !== A.right ||
											O !== A.top ||
											k !== A.bottom)
									) {
										var t = { left: m, right: w, top: O, bottom: k };
										return s.a.getOfficeEditor().setSectionMargins(t, q);
									}
								},
								label: t("action.apply"),
							}),
						),
					),
				);
			};
			e.default = w;
		},
	},
]);
//# sourceMappingURL=chunk.72.js.map
