(window.webpackJsonp = window.webpackJsonp || []).push([
	[104],
	{
		1362: function (_, t, e) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					e = {
						name: "am",
						weekdays: "እሑድ_ሰኞ_ማክሰኞ_ረቡዕ_ሐሙስ_አርብ_ቅዳሜ".split("_"),
						weekdaysShort: "እሑድ_ሰኞ_ማክሰ_ረቡዕ_ሐሙስ_አርብ_ቅዳሜ".split("_"),
						weekdaysMin: "እሑ_ሰኞ_ማክ_ረቡ_ሐሙ_አር_ቅዳ".split("_"),
						months:
							"ጃንዋሪ_ፌብሯሪ_ማርች_ኤፕሪል_ሜይ_ጁን_ጁላይ_ኦገስት_ሴፕቴምበር_ኦክቶበር_ኖቬምበር_ዲሴምበር".split(
								"_",
							),
						monthsShort: "ጃንዋ_ፌብሯ_ማርች_ኤፕሪ_ሜይ_ጁን_ጁላይ_ኦገስ_ሴፕቴ_ኦክቶ_ኖቬም_ዲሴም".split(
							"_",
						),
						weekStart: 1,
						yearStart: 4,
						relativeTime: {
							future: "በ%s",
							past: "%s በፊት",
							s: "ጥቂት ሰከንዶች",
							m: "አንድ ደቂቃ",
							mm: "%d ደቂቃዎች",
							h: "አንድ ሰዓት",
							hh: "%d ሰዓታት",
							d: "አንድ ቀን",
							dd: "%d ቀናት",
							M: "አንድ ወር",
							MM: "%d ወራት",
							y: "አንድ ዓመት",
							yy: "%d ዓመታት",
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "DD/MM/YYYY",
							LL: "MMMM D ፣ YYYY",
							LLL: "MMMM D ፣ YYYY HH:mm",
							LLLL: "dddd ፣ MMMM D ፣ YYYY HH:mm",
						},
						ordinal: function (_) {
							return _ + "ኛ";
						},
					};
				return t.default.locale(e, null, !0), e;
			})(e(105));
		},
	},
]);
//# sourceMappingURL=chunk.104.js.map
