function doSelectGateway() {
	var chosenGatewayID = $('#gatewayID').val();
	var arrayValue = chosenGatewayID.split('_');
	var mp_gatewayID = parseInt(arrayValue[0] || '0'); 

	$('#trAffinipayConnect, #trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trGatewayAccountID, #trProfileInfo, #mpOrgIdentityRow, #acctSettings, .processFeeDonationSettings, #activateProcFeeSettingController, #enabledProcFeeSettingControllers, #autoSettings, #paymentDetails, #trCashGL, #trBankAcctName, #trTransLabel, .mpBDGatewayFields, #trPayInvoices, #trPayments, #trRefunds, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup,#secCodes, #cardTypes, #trPaymentInstructions, #trPaymentFields, #trSaveButton, #gatewayCredsInfo, #MCPaySettings').hide();
	showGateway(mp_gatewayID);
}

function showGateway(g) {
	var mp_profileID = $('#profileID').val();
	switch(g){
		case 2: case 10: case 11: case 12: case 13: case 14: case 16: case 19:
			$('#trProfileInfo, #mpOrgIdentityRow').show();
			showFields();
			showConnInfo();
			showRefundAnyProfile();
			showDaysBetweenAutoAttempts();
			showMaxFailedAutoAttempts();
			$('#trSaveButton').show();
			break;
		case 17:
			if (mp_profileID == 0) {
				if(mc_env == 'production') {
					$('#trAffinipayConnect').show();
				} else {
					$('#trProfileInfo, #mpOrgIdentityRow, #acctSettings, #trCashGL, #trSaveButton').show();
					$('input.cardType').prop('checked',true);
				}
			} else {
				$('#trProfileInfo, #mpOrgIdentityRow').show();
				showFields();
				showConnInfo();
				showRefundAnyProfile();
				showDaysBetweenAutoAttempts();
				showMaxFailedAutoAttempts();
				$('#trSaveButton').show();
			}
			break;
	}
}

function connectToAffinipay() {
	var connectToAffinipayResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			self.location.href = r.redirecturi;
		} else {
			alert('Unable to connect to AffiniPay.');
		}
	}
	var objParams = { h:mca_hostname };
	TS_AJX('ADMINMP','connectToAffinipay',objParams,connectToAffinipayResult,connectToAffinipayResult,15000,connectToAffinipayResult);
}

function checkProfileCode() {
	var thisForm = document.forms["frmMP"];
	var pCode = thisForm['new.profileCode'].value;

	if (pCode.length && pCode.toUpperCase().split(' ').join('') != pCode.toUpperCase()) {
		thisForm['profileCode.check'].value = false;
		$('#pCodeMSG').html('Profile Code must not contain space characters.').show();
		return false;
	}

	if( thisForm['new.profileCode'].value.toUpperCase().split(' ').join('') != thisForm['old.profileCode'].value.toUpperCase().split(' ').join('')){
		var checkCode = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				$('#pCodeMSG').html('').hide();
				thisForm['profileCode.check'].value = true;
				return true;
			} 
			else {
				$('#pCodeMSG').html('That profile code is already in use. Select a new profile code.').show();
				thisForm['profileCode.check'].value = false;
				return false;
			}
		};
		var objParams = { profileCode:pCode };
		TS_AJX('ADMINMP','checkProfileCodeExists',objParams,checkCode,checkCode,10000,checkCode);
	}
	else{
		$('#pCodeMSG').html('').hide();
		thisForm['profileCode.check'].value = true;
		return true;
	}
}

function showFields() {
	// show all questionfields
	$('.fieldscbox').show();
	$('.fieldsgateway').hide();

	// get selected gateway
	var chosenGatewayID = $('#gatewayID').val();
	var arrayValue = chosenGatewayID.split('_');
	var mp_gatewayID = parseInt(arrayValue[0] || '0'); 
	var mp_classType = arrayValue[1] || ''; 
	var mp_arrGF = eval('gatewayFields_' + mp_gatewayID);

	// handle gateway fields (hide and clear cboxes)
	for (var x=0; x<mp_arrGF.length; x++){
		$('#fields_' + mp_arrGF[x]).hide();
		$('#fields_' + mp_arrGF[x] + '_gateway').show();
		uncheckFieldsByID(mp_arrGF[x]);
	}

	var mp_profileID = $('#profileID').val();
	
	// hide fields that dont apply
	switch(mp_classType.toUpperCase()){
		case 'CREDITCARD':
			hideFieldsByType('check');
			hideFieldsByType('bankdraft');
			uncheckFieldsByType('check');
			uncheckFieldsByType('bankdraft');
			uncheckSecCodes();
			$('.mpBDGatewayFields, #secCodes').hide();
			$('#cardTypes').show();
			switch (mp_gatewayID) {
				case 1:
					$('#fields_5, #fields_5_gateway').hide();
					uncheckFieldsByID(5);
					break;
				case 10:
					$('#MCPaySettings, #autoSettings, #trMinDaysFailedCleanup').show();
					if ($('#activateProcFeeSettingController').length) {
						$('.processFeeDonationSettings, #activateProcFeeSettingController').show();
					}
					activateProcessFeeDonationSettings();
					if ($('#enableMCPay').length) {
						$('#enableMCPay').bootstrapToggle('destroy');
						$('#enableMCPay').bootstrapToggle();
						$('#enableMCPay').trigger('change');
					} else {
						if (mc_enableMCPay)	{
							if (!$('#MCPaySettingsTabs').find('li:visible').length) {
								$('#MCPaySettings').hide();
							}
						} else {
							$('#MCPaySettings').hide();
							$('#enableProcessingFeeDonation').prop('disabled',false);
							if ($('#enableApplePay').length) $('#enableApplePay').prop('disabled',false);
							if ($('#enableGooglePay').length) $('#enableGooglePay').prop('disabled',false);
						}
					}
					manageProcFeeAndSurchargeSettings();
					break;
				case 12:
					$('#autoSettings, #trMinDaysFailedCleanup').show();
					break;
				case 17:
					$('#gatewayPassword').attr('size',70);
					$('#trGatewayUsername label[for="gatewayUsername"]').html('Public Key');
					$('#trGatewayPassword label[for="gatewayPassword"]').html('Secret Key');
					$('#trGatewayMerchantID label[for="gatewayMerchantId"]').html('Account ID');
					$('#autoSettings, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').show();
					break;
			}
			break;
		case 'CHECK':
		case 'OFFLINE':
			hideFieldsByType('creditcard');
			hideFieldsByType('bankdraft');
			uncheckFieldsByType('creditcard');
			uncheckFieldsByType('bankdraft');
			uncheckCards();
			uncheckSecCodes();
			$('#cardTypes, .mpBDGatewayFields, #autoSettings, #secCodes, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').hide();
			break;
		case 'BANKDRAFT':
			hideFieldsByType('creditcard');
			hideFieldsByType('check');
			uncheckFieldsByType('creditcard');
			uncheckFieldsByType('check');
			uncheckCards();
			switch (mp_gatewayID) {
				case 19:
					$('#trGatewayMerchantID label[for="gatewayMerchantId"]').html('Merchant ID');
					$('#secCodes').show();
					$('#cardTypes').hide();
					break;
				default:
					$('#cardTypes, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts').hide();
					$('#autoSettings, #secCodes, .mpBDGatewayFields, #trMinDaysFailedCleanup').show();
			}
			break;
		default:
			hideFieldsByType('check');
			hideFieldsByType('creditcard');
			hideFieldsByType('bankdraft');
			uncheckFieldsByType('check');
			uncheckFieldsByType('creditcard');
			uncheckFieldsByType('bankdraft');
			uncheckCards();
			uncheckSecCodes();
			$('#cardTypes, .mpBDGatewayFields, #autoSettings, #secCodes, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').hide();
			break;
	}
}

function showConnInfo() {
	var chosenGatewayID = $('#gatewayID').val();
	var arrayValue = chosenGatewayID.split('_');
	var mp_gatewayID = parseInt(arrayValue[0] || '0');

	$('#acctSettings, #paymentDetails, #trCashGL, #trBankAcctName, #trPaymentFields, #trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trPayInvoices, #trRefunds, #trPayments').show();
	$('#autoSettings, #trPaymentInstructions, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup, tbody#trTransLabel').hide();

	switch(mp_gatewayID) {
		case 0:
			$('#gatewayUsername, #gatewayPassword, #gatewayMerchantId, #transactionLabel').val('');
			$('#paymentDetails, #trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trPaymentFields, #trCashGL, #trBankAcctName, #trPayInvoices, #trPayments, #trRefunds').hide();
			break;			
		case 2:
			$('#gatewayUsername, #gatewayPassword, #gatewayMerchantId').val('');
			$('#trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID').hide();
			$('tbody#trTransLabel').show();
			if ($('#profileID').val() == 0) {
				$('#transactionLabel').val('cash/check');
			}
			break;
		case 10:
			$('#gatewayMerchantId, #transactionLabel').val('');
			$('#trGatewayMerchantID').hide();
			uncheckFieldsByID(3); $('tbody#fields_3').hide();
			uncheckFieldsByID(5); $('tbody#fields_5').hide();
			uncheckFieldsByID(8); $('tbody#fields_8').hide();
			uncheckFieldsByID(16); $('tbody#fields_16').hide();
			uncheckFieldsByID(17); $('tbody#fields_17').hide();
			uncheckFieldsByID(19); $('tbody#fields_19').hide();
			uncheckFieldsByID(26); $('tbody#fields_26').hide();
			$('#trGatewayAccountID, #autoSettings, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').show();
			if (mc_env != 'production') {
				$('#trGatewayUsername, #trGatewayPassword, #trGatewayAccountID').hide();
				$('#gatewayCredsInfo').show();
			}
			break;
		case 11:
			$('#gatewayUsername, #gatewayPassword, #gatewayMerchantId, #transactionLabel').val('');
			$('#trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trPaymentFields, #trCashGL, #trBankAcctName, #trPayInvoices, #trRefunds').hide();
			$('#trPaymentInstructions').show();
			break;
		case 12:
			$('#gatewayMerchantId, #transactionLabel').val('');
			$('#paymentDetails, #trPaymentFields, #trGatewayMerchantID').hide();
			$('#autoSettings, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').show();
			break;
		case 13:
		case 14:
			$('#gatewayUsername, #gatewayPassword, #gatewayMerchantId, #transactionLabel').val('');
			$('#paymentDetails, #trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trPayInvoices, #trRefunds, #trPaymentFields').hide();
			break;
		case 16:
			$('#gatewayUsername, #gatewayPassword, #gatewayMerchantId, #transactionLabel').val('');
			$('#paymentDetails, #trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID, #trPaymentFields, #trRefunds').hide();
			$('#autoSettings, #trMinDaysFailedCleanup').show();
			break;
		case 17:
			$('#transactionLabel').val('');
			uncheckFieldsByID(3); $('tbody#fields_3').hide();
			uncheckFieldsByID(5); $('tbody#fields_5').hide();
			uncheckFieldsByID(8); $('tbody#fields_8').hide();
			uncheckFieldsByID(16); $('tbody#fields_16').hide();
			uncheckFieldsByID(17); $('tbody#fields_17').hide();
			uncheckFieldsByID(19); $('tbody#fields_19').hide();
			uncheckFieldsByID(26); $('tbody#fields_26').hide();
			$('#autoSettings, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').show();
			if (mc_env != 'production') {
				$('#trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID').hide();
				$('#gatewayCredsInfo').show();
			}
			break;
		case 19:
			$('#paymentDetails').hide();
			$('#autoSettings, #trDaysBetweenAutoAttempts, #trMaxFailedAutoAttempts, #trMinDaysFailedCleanup').show();
			if (mc_env != 'production') {
				$('#trGatewayUsername, #trGatewayPassword, #trGatewayMerchantID').hide();
				$('#gatewayCredsInfo').show();
			}
			break;
	}
}

function showRefundAnyProfile() {
	var rdoSel = $('#allowRefundsFromAnyProfile').val();
	if (rdoSel > 0) {
		$('#divallowRefundsFromAnyProfile').show();
		$('#divRefundsImageUpload').show();		
	} else {
		$('#divallowRefundsFromAnyProfile').hide();
		$('#divRefundsImageUpload').hide();
		
	}
}

function showDaysBetweenAutoAttempts() {
	var rdoSel = $('input:radio[name=daysBetweenAutoAttempts]:checked').val();
	if (rdoSel!=1) {
		$('input[name=numberOfDaysBetweenAutoAttempts]').val(0);
	}
}

function showMaxFailedAutoAttempts() {
	var rdoSel = $('input:radio[name=maxFailedAutoAttempts]:checked').val();
	if (rdoSel!=1) {
		$('input[name=numberOfMaxFailedAutoAttempts]').val(0);
	}
}

function hideFieldsByType(type){
	$('tbody.' + type).hide();
}

function uncheckFieldsByType(type){
	$('tbody.' + type + ' input[type=checkbox]').attr('checked',false);
	$('tbody.' + type + ' input[type=checkbox]').attr('checked',false);
}

function uncheckFieldsByID(id){
	$('#fieldID_' + id).attr('checked',false);
	$('#isRequired_' + id).attr('checked',false);
}

function uncheckCards(){
	$('input.cardType').attr('checked',false);
}
function uncheckSecCodes(){
	$('input.secCode').attr('checked',false);
}

function checkFieldID(element){
	var thisForm = document.forms["frmMP"];
	var arrayName = element.split('_');
	var myItem = 'isRequired_' + arrayName[1] + '_' + arrayName[2];
	if( thisForm[element].checked == false && thisForm[myItem].checked == true ){
		thisForm[myItem].checked = false; 
	}
}

function checkIsRequired(element){
	var thisForm = document.forms["frmMP"];
	var arrayName = element.split('_');
	var myItem = 'fieldID_' + arrayName[1] + '_' + arrayName[2];
	if( thisForm[element].checked == true && thisForm[myItem].checked == false ){
		thisForm[myItem].checked = true; 
	}
}

function valRoutingNum(x) {
	if (x.length > 0) {
		var i, n, t, c;
		t = "";
		for (i = 0; i < x.length; i++) {
			c = parseInt(x.charAt(i), 10);
			if (c >= 0 && c <= 9) t = t + c;
		}
		if (t.length != 9) return false;
		n = 0;
		for (i = 0; i < t.length; i += 3) {
			n += parseInt(t.charAt(i), 10) * 3 + parseInt(t.charAt(i + 1), 10) * 7 + parseInt(t.charAt(i + 2), 10);
		}
		if (n != 0 && n % 10 == 0) return true;
	}
	return false;
}

function validateYesLabel(obj,showErr) {
	let mc = obj.data('mc');
	let err = '';
	if (mc == 'p' && !obj.val().toUpperCase().includes('{{PERCENT}}')) {
		err = $('#lbl'+obj.attr('name')).text() + ' requires the mergecode {{PERCENT}}.';
	} else if (mc == 'a' && !obj.val().toUpperCase().includes('{{AMOUNT}}')) {
		err = $('#lbl'+obj.attr('name')).text() + ' requires the mergecode {{AMOUNT}}.';
	}
	if (showErr && err.length) mca_showAlert('mpfrm_err', err, true);
	
	return err;
}

function _validateMPForm() {
	var arrReq = new Array();
	var thisForm = document.forms["frmMP"];
	var gatewayID = $('#gatewayID').val();
	if (gatewayID == '') arrReq[arrReq.length] = 'Select a payment gateway.';
	if (!_CF_hasValue(thisForm['profileName'], "TEXT", false)) arrReq[arrReq.length] = 'Enter a profile name.';
	if (!_CF_hasValue(thisForm['tabTitle'], "TEXT", false)) arrReq[arrReq.length] = 'Enter a tab title (can be the same as the Profile Name).';
	if (!_CF_hasValue(thisForm['new.profileCode'], "TEXT", false)) arrReq[arrReq.length] = 'Enter a profile code.';
	if (!_validateCreditCards()) arrReq[arrReq.length] = 'Select credit card types.';
	if (!_validateSecCodes()) arrReq[arrReq.length] = 'Select SEC Codes.';
	if (thisForm['new.profileCode'].value.length && thisForm['new.profileCode'].value.toUpperCase().split(' ').join('') != thisForm['new.profileCode'].value.toUpperCase()) 
		arrReq[arrReq.length] = 'Profile Code must not contain space characters.';
	else if (thisForm['profileCode.check'].value == 'false') arrReq[arrReq.length] = 'The profile code you entered is already in use.';

	if (mc_env == 'production') {
		if ($('#gatewayUsername').is(':visible') && ! $('#gatewayUsername').val().trim().length) arrReq.push('Enter the Gateway Username.');
		if ($('#gatewayPassword').is(':visible') && ! $('#gatewayPassword').val().trim().length) arrReq.push('Enter the Gateway Password.');
	}

	if (!['','11_offline'].includes(gatewayID)) {
		if ($('#GLAccountID').val() == 0 || $('#GLAccountID').val().length == 0) arrReq[arrReq.length] = 'Choose a GL Account for this payment profile.';
	}
	if (gatewayID == '2_offline') {
		if (!_CF_hasValue(thisForm['transactionLabel'], "TEXT", false)) arrReq[arrReq.length] = 'Enter a Transactions Appear As label.';
	}
	if (gatewayID != '' && gatewayID == '16_bankdraft') {
		if (!_CF_hasValue(thisForm['bankAccountName'], "TEXT", false)) arrReq[arrReq.length] = 'Enter a bank account name.';

		var trn = '';
		for (var i = 1; i <= 9; i++) {
			trn += ($('#bankTRN_'+i).val() + ' ').substring(0,1);
		}
		if (!valRoutingNum(trn)) arrReq[arrReq.length] = 'Enter a valid Routing Number.';

		var bcn = '';
		for (var i = 1; i <= 10; i++) {
			bcn += ($('#bankCompanyNumber_'+i).val() + ' ').substring(0,1);
		}
		if (bcn.length == 0) arrReq[arrReq.length] = 'Enter a valid Company Number.';

		var bio = '';
		for (var i = 1; i <= 10; i++) {
			bio += ($('#bankImmediateOrigin'+i).val() + ' ').substring(0,1);
		}
		if (bio.length == 0) arrReq[arrReq.length] = 'Enter a valid Immediate Origin.';

		if (!$('#bankCompanyName').val().trim().length) arrReq.push('Enter a Company Name.');
		if (!$('#bankImmediateOriginName').val().trim().length) arrReq.push('Enter a Immediate Origin Name.');
		if (!$('#bankCompanyEnterDesc').val().trim().length) arrReq.push('Enter a Company Entry Description.');
	}

	if ($('#refundImage').length && $('#refundImage').val().length > 0 && !/(\.gif|\.png)$/i.test($('#refundImage').val())) arrReq.push('Only GIF or PNG files are accepted for the Refund Image.');

	let procFeeSettingsEnabled = false;
	if (gatewayID == '10_creditcard' && $('#activateProcessingFeeSettings').val() == 1 && $('#enableProcessingFeeDonation').val() == 1) procFeeSettingsEnabled = true;

	if (procFeeSettingsEnabled) {
		let processFeeDonationFeePercent = $('#processFeeDonationFeePercent').val();
		let processFeeDonationRenevueGLAccountID = $('#processFeeDonationRevGLAcctID').val();
		if (processFeeDonationFeePercent == '' || isNaN(processFeeDonationFeePercent)) arrReq.push('Enter a valid Voluntary Processing Fee Donation Fee Percentage.');
		if ($('#processingFeeLabel').val().trim() == '') arrReq.push('Enter the Voluntary Processing Fee Label.');
		if ($('#solicitationMessageID').val() == 0) arrReq.push('Select the Solicitation Title & Message.');
		if ($('#processFeeContributionsFELabel').length && $('#processFeeContributionsFELabel').val().trim() == '') arrReq.push('Enter the YES Option Label for Contributions Payments.');
		if ($('#processFeeContributionsFEDenyLabel').length && $('#processFeeContributionsFEDenyLabel').val().trim() == '') arrReq.push('Enter the NO Option Label for Contributions Payments.');
		if ($('#processFeeSubscriptionsFELabel').length && $('#processFeeSubscriptionsFELabel').val().trim() == '') arrReq.push('Enter the YES Option Label for Subscriptions Payments.');
		if ($('#processFeeSubscriptionsFEDenyLabel').length && $('#processFeeSubscriptionsFEDenyLabel').val().trim() == '') arrReq.push('Enter the NO Option Label for Subscriptions Payments.');
		if ($('#processFeeOtherPaymentsFELabel').length && $('#processFeeOtherPaymentsFELabel').val().trim() == '') arrReq.push('Enter the YES Option Label for All Other Payments (Events, etc).');
		if ($('#processFeeOtherPaymentsFEDenyLabel').length && $('#processFeeOtherPaymentsFEDenyLabel').val().trim() == '') arrReq.push('Enter the NO Option Label for All Other Payments (Events, etc).');
		if (processFeeDonationRenevueGLAccountID == '' || processFeeDonationRenevueGLAccountID == 0) arrReq.push('Choose a Voluntary Processing Fee Revenue GL Account.');
		if ($('#processFeeDonationRevTransDesc').val().trim() == '') arrReq.push('Enter the Voluntary Processing Fee Donation Revenue Transaction Description.');

		$('#frmMP').find('input.processFeeYesLabel').each(function() { 
			let thisErr = validateYesLabel($(this),0);
			if (thisErr.length) arrReq.push(thisErr);
		});
	}

	let surchargeSettingsEnabled = gatewayID == '10_creditcard' && $('#enableSurcharge').length && $('#enableSurcharge').prop('checked');
	if (surchargeSettingsEnabled) {
		let surchargePercent = $('#surchargePercent').val();
		let surchargeRevGLAcctID = $('#surchargeRevGLAcctID').val();
		if (surchargePercent == '' || isNaN(surchargePercent)) arrReq.push('Enter a valid Surcharge Fee Percentage.');
		if (surchargeRevGLAcctID == '' || surchargeRevGLAcctID == 0) arrReq.push('Choose a Revenue GL Account for Surcharge.');
	}

	if (arrReq.length > 0) {
		var msg = 'The following require your attention:<br />' + arrReq.join('<br />');
		mca_showAlert('mpfrm_err', msg, true);
		return false;
	}

	$('#frmMP button[type="submit"]').html('Please wait...').prop('disabled',true);

	return true;
}

function _validateCreditCards(){
	var thisForm = document.forms["frmMP"]; var ccCount = 0; var pass = false;

	if ($('#profileID').val() > 0) {
		myVal = $('#gatewayID').val();
		arrayValue = myVal.split('_');
		myType = arrayValue[1] || '';
		var mp_gatewayID = parseInt(arrayValue[0] || '0');
		if (myType.toUpperCase() == 'CREDITCARD') {
			for (var x=0; x < thisForm.length; x++){
				frmObj = thisForm[x]; objArray = frmObj.name.split('_');
				if( objArray[0].toUpperCase() == 'CARDTYPEID' && frmObj.checked == true ){ ccCount++; }
			}
		}
		else{ uncheckCards(thisForm); pass = true; }
	} else {
		var mp_gatewayID = 0;
		for (var i=0; i < thisForm.gatewayID.length; i++){
			if (thisForm.gatewayID.options[i].selected){
				myVal = thisForm.gatewayID.options[i].value; 
				arrayValue = myVal.split('_'); 
				myType = arrayValue[1] || '';
				mp_gatewayID = parseInt(arrayValue[0] || '0'); 
				if (myType.toUpperCase() == 'CREDITCARD') {
					for (var x=0; x < thisForm.length; x++){
						frmObj = thisForm[x]; objArray = frmObj.name.split('_');
						if( objArray[0].toUpperCase() == 'CARDTYPEID' && frmObj.checked == true ){ ccCount++; }
					}
				}
				else{ uncheckCards(thisForm); pass = true; }
			}
		}
	}
	if( pass == false ){
		if( ccCount > 0 ){ return true; }
		else { return false; }
	}
	else{ return true; }
}

function _validateSecCodes(){
	var thisForm = document.forms["frmMP"]; var ccCount = 0; var pass = false;
	if ($('#profileID').val() > 0) {
		myVal = $('#gatewayID').val();
		arrayValue = myVal.split('_');
		myType = arrayValue[1] || '';
		if( myType.toUpperCase() == 'BANKDRAFT' ){
			for (var x=0; x < thisForm.length; x++){
				frmObj = thisForm[x]; objArray = frmObj.name.split('_');
				if( objArray[0].toUpperCase() == 'SECCODEID' && frmObj.checked == true ){ ccCount++; }
			}
		}
		else{ uncheckSecCodes(thisForm); pass = true; }
	} else {
		for (var i=0; i < thisForm.gatewayID.length; i++){
			if (thisForm.gatewayID.options[i].selected){
				myVal = thisForm.gatewayID.options[i].value; 
				arrayValue = myVal.split('_'); 
				myType = arrayValue[1] || '';
				if( myType.toUpperCase() == 'BANKDRAFT' ){
					for (var x=0; x < thisForm.length; x++){
						frmObj = thisForm[x]; objArray = frmObj.name.split('_');
						if( objArray[0].toUpperCase() == 'SECCODEID' && frmObj.checked == true ){ ccCount++; }
					}
				}
				else{ uncheckSecCodes(thisForm); pass = true; }
			}
		}
	}
	if( pass == false ){
		if( ccCount > 0 ){ return true; }
		else{ return false;}
	}
	else{ return true; }
}
function closeBox() { MCModalUtils.hideModal(); }
function toggleCardTypeGL(ctid) {
	if ($('#cardTypeID_' + ctid + ':checked').val() != null) {
		$('#div_ct_' + ctid + '_gllink').show();
		$('#div_ct_' + ctid + '_glna').hide();
	} else {
		$('#div_ct_' + ctid + '_glna').show();
		$('#div_ct_' + ctid + '_gllink').hide();
	}
}
function fillTabTitle() {
	var tt = $('#tabTitle');
	if (tt.val() == '') tt.val($('#profileName').val());
}
function manageProcFeeAndSurchargeSettings() {
	if (! $('#enableSurcharge').length) {
		let enableSurcharge = $('#old_enableSurcharge').val();
		if (enableSurcharge == 1 && ! $('#enableProcessingFeeDonation').prop('disabled')) {
			$('#enableProcessingFeeDonation').prop('disabled',true);
		}
		return false;
	}

	if ($('#enableProcessingFeeDonation').val() == 1) {
		if ($('#enableSurcharge').prop('checked')) {
			$('#enableSurcharge').prop('checked',false).change();
		}
		$('#enableSurcharge').prop('disabled',true);
	} else if ($('#enableSurcharge').prop('checked')) {
		if ($('#enableProcessingFeeDonation').val() == 1) {
			$('#enableProcessingFeeDonation').val(0).change();
		}
		$('#enableProcessingFeeDonation').prop('disabled',true);
	}

	if ($('#enableProcessingFeeDonation').val() == 0 && $('#enableSurcharge').prop('disabled')) {
		$('#enableSurcharge').prop('disabled',false);
	} else if (! $('#enableSurcharge').prop('checked') && $('#enableProcessingFeeDonation').prop('disabled')) {
		$('#enableProcessingFeeDonation').prop('disabled',false);
	}
}

/* Process Fee Donation Settings */
function activateProcessFeeDonationSettings() {
	if ($('#activateProcessingFeeSettings').val() == 1) {
		$('#enabledProcFeeSettingControllers').show();
		if ($('.processFeeDonationSettings').is(':hidden')) {
			$('.processFeeDonationSettings').show();
		}
	} else {
		$('#enabledProcFeeSettingControllers').hide();
	}
}
function onChangeProcessFeeDonationSettings() {
	if ($('#enableProcessingFeeDonation').val() == 1) $('#procFeeDonationSettings').removeClass('d-none');
	else $('#procFeeDonationSettings').addClass('d-none');

	manageProcFeeAndSurchargeSettings();
}

// Enable MC Pay
function toggleMCPaySettings(x) {
	let hasSurcharge = $('#enableSurcharge').length;
	let hasApplePay = $('#enableApplePay').length;
	let hasGooglePay = $('#enableGooglePay').length;

	if ($(x).prop('checked')) {
		$('#MCPaySettingsContainer').removeClass('d-none');
		$('#enableProcessingFeeDonation').prop('disabled',false);
		if (hasSurcharge) {
			$('#enableSurcharge').prop('disabled',false).change();
			initEnableSurchargeToggleBtn();
		}
		if (hasApplePay) {
			$('#enableApplePay').prop('disabled',false).change();
			initEnableApplePayToggleBtn();
		}
		if (hasGooglePay) {
			$('#enableGooglePay').prop('disabled',false).change();
			initEnableGooglePayToggleBtn();
		}
	} else {
		$('#MCPaySettingsContainer').addClass('d-none');
		$('#enableProcessingFeeDonation').val(0).change();
		$('#enableProcessingFeeDonation').prop('disabled',true);
		if (hasSurcharge) {
			$('#enableSurcharge').prop('checked',false).change();
			$('#enableSurcharge').prop('disabled',true);
		}
		if (hasApplePay) {
			$('#enableApplePay').prop('checked',false).change();
			$('#enableApplePay').prop('disabled',true);
		}
		if (hasGooglePay) {
			$('#enableGooglePay').prop('checked',false).change();
			$('#enableGooglePay').prop('disabled',true);
		}
	}
}

// surcharge
function initEnableSurchargeToggleBtn() {
	$('#enableSurcharge').bootstrapToggle('destroy');
	$('#enableSurcharge').bootstrapToggle();
}
function toggleSurchargeSettings(x) {
	if ($(x).prop('checked')) {
		$('#surchargeSettingFields').removeClass('d-none');
	} else {
		$('#surchargeSettingFields').addClass('d-none');
	}

	manageProcFeeAndSurchargeSettings();
}

// apple pay
function initEnableApplePayToggleBtn() {
	$('#enableApplePay').bootstrapToggle('destroy');
	$('#enableApplePay').bootstrapToggle();
}
function toggleApplePaySettings(x) {
	if ($(x).prop('checked')) {
		$('#applePaySettingFields').removeClass('d-none');
	} else {
		$('#applePaySettingFields').addClass('d-none');
	}
}

// google pay
function initEnableGooglePayToggleBtn() {
	$('#enableGooglePay').bootstrapToggle('destroy');
	$('#enableGooglePay').bootstrapToggle();
}
function toggleGooglePaySettings(x) {
	if ($(x).prop('checked')) {
		$('#googlePaySettingFields').removeClass('d-none');
	} else {
		$('#googlePaySettingFields').addClass('d-none');
	}
}