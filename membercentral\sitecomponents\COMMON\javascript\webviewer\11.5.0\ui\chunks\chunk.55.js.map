{"version": 3, "sources": ["webpack:///./src/ui/src/components/StylePicker/ColorPicker/index.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss?fe54", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.scss", "webpack:///./src/ui/src/components/RichTextStyleEditor/RichTextStyleEditor.js", "webpack:///./src/ui/src/components/RichTextStyleEditor/index.js"], "names": ["ColorPicker", "api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "annotation", "PropTypes", "object", "editor", "style", "shape", "TextColor", "oneOfType", "string", "RichTextStyle", "any", "isFreeTextAutoSize", "bool", "onFreeTextSizeToggle", "func", "onPropertyChange", "onRichTextStyleChange", "isRedaction", "isRichTextEditMode", "setIsRichTextEditMode", "isWidget", "RichTextStyleEditor", "activeTool", "fonts", "useSelector", "state", "selectors", "getFonts", "shallowEqual", "useState", "format", "setFormat", "editor<PERSON><PERSON>", "useRef", "annotationRef", "propertiesRef", "dispatch", "useDispatch", "oldSelectionRef", "richTextEditModeRef", "current", "t", "useTranslation", "useEffect", "handleSelectionChange", "range", "oldRange", "setSelection", "index", "getFormat", "handleTextChange", "getSelection", "core", "addEventListener", "actions", "disableElements", "DataElements", "ANNOTATION_STYLE_POPUP", "removeEventListener", "enableElements", "StrokeStyle", "err", "console", "error", "stylesTemp", "getRichTextStyle", "Font", "FontSize", "TextAlign", "TextVerticalAlign", "bold", "italic", "underline", "includes", "strikeout", "size", "font", "calculatedFontSize", "getCalculatedFontSize", "handleEditorBlur", "handleEditorFocus", "properties", "color", "Core", "Annotations", "Color", "Array", "isArray", "lastSelectedColor", "prop", "undefined", "applyFormat", "formatKey", "value", "handlePropertyChange", "property", "blur", "adjustFreeTextBoundingBox", "setTimeout", "getAnnotationManager", "getEditBoxManager", "focusBox", "defaults", "strike", "quillFont", "quillFontSize", "originalSize", "commonProps", "stateless", "isFreeText", "nonWidgetProps", "propertyTranslation", "freeText", "isAutoSized", "resizeAnnotation", "newSelection", "currentFormat", "handleTextFormatChange", "widgetProps", "className", "onMouseDown", "e", "type", "preventDefault", "TextStylePicker", "onColorChange", "name", "toHexString", "handleColorChange", "ariaTypeLabel", "React", "memo"], "mappings": "4FAAA,aAEeA,MAAW,G,qBCF1B,IAAIC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,sxEAAuxE,KAGhzE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,s0FCGvB,IAAMC,EAAY,CAChBC,WAAYC,IAAUC,OACtBC,OAAQF,IAAUC,OAClBE,MAAOH,IAAUI,MAAM,CACrBC,UAAWL,IAAUM,UAAU,CAC7BN,IAAUO,OACVP,IAAUC,SAEZO,cAAeR,IAAUS,MAE3BC,mBAAoBV,IAAUW,KAC9BC,qBAAsBZ,IAAUa,KAChCC,iBAAkBd,IAAUa,KAC5BE,sBAAuBf,IAAUa,KACjCG,YAAahB,IAAUW,KACvBM,mBAAoBjB,IAAUW,KAC9BO,sBAAuBlB,IAAUa,KACjCM,SAAUnB,IAAUW,MAGhBS,EAAsB,SAAH,GAYnB,8BAXJrB,EAAU,EAAVA,WAAYG,EAAM,EAANA,OACZC,EAAK,EAALA,MACAO,EAAkB,EAAlBA,mBACAE,EAAoB,EAApBA,qBACAE,EAAgB,EAAhBA,iBACAC,EAAqB,EAArBA,sBACAE,EAAkB,EAAlBA,mBACAC,EAAqB,EAArBA,sBACAF,EAAW,EAAXA,YACAG,EAAQ,EAARA,SACAE,EAAU,EAAVA,WAGEC,EAMD,EALGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,SAASF,MAErBG,KACD,GANM,GAQiC,IAAZC,mBAAS,IAAG,GAAjCC,EAAM,KAAEC,EAAS,KAClBC,EAAYC,iBAAO,MACnBC,EAAgBD,iBAAO,MACvBE,EAAgBF,iBAAO,IACvBG,EAAWC,cACXC,EAAkBL,mBAClBM,EAAsBN,mBAC5BM,EAAoBC,QAAUtB,EAC9B,IAAOuB,EAAqB,EAAhBC,cAAgB,GAApB,GAERC,qBAAU,WACR,IAAMC,EAAwB,SAACC,EAAOC,IACAD,GAASC,GAAYd,EAAUQ,SAEjER,EAAUQ,QAAQO,aAAaD,EAASE,MAAOF,EAASjE,QAEtDgE,GAASb,EAAUQ,SACrBT,EAAUkB,EAAUJ,KAGlBK,EAAmB,WAAM,MAC7BnB,EAAUkB,EAA2B,QAAlB,EAACjB,EAAUQ,eAAO,aAAjB,EAAmBW,kBAMzC,OAJAC,IAAKC,iBAAiB,yBAA0BT,GAChDQ,IAAKC,iBAAiB,oBAAqBH,GAE3Cd,EAASkB,IAAQC,gBAAgB,CAACC,IAAaC,0BACxC,WACLL,IAAKM,oBAAoB,yBAA0Bd,GACnDQ,IAAKM,oBAAoB,oBAAqBR,GAC9Cd,EAASkB,IAAQK,eAAe,CAACH,IAAaC,6BAE/C,IAEHd,qBAAU,WAAM,MAGd,GAFAX,EAAUQ,QAAUrC,EACpB+B,EAAcM,QAAUxC,EACpBkB,GAAsBlB,EAAY,iBAChC4D,EAAc,QAClB,IACEA,EAAuC,SAAxB5D,EAAkB,MAAY,UACtCA,EAAkB,MAAC,YAAIA,EAAmB,QAC7CA,EAAkB,MACtB,MAAO6D,GACPC,QAAQC,MAAMF,GAEhB,IACMG,EADiBhE,EAAWiE,mBACA,GAElC9B,EAAcK,QAAU,CACtB0B,KAAMlE,EAAWkE,KACjBC,SAAUnE,EAAWmE,SACrBC,UAAWpE,EAAWoE,UACtBC,kBAAmBrE,EAAWqE,kBAC9BC,KAA4C,QAAxC,EAAkC,UAAhCN,aAAU,EAAVA,EAAa,uBAAyB,SAC5CO,OAA+C,QAAzC,EAAiC,YAA/BP,aAAU,EAAVA,EAAa,sBAA0B,SAC/CQ,WAAWR,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,gBAChDT,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,SAC/CC,UAAoE,QAA3D,EAAEV,SAA+B,QAArB,EAAVA,EAAa,0BAAkB,WAArB,EAAV,EAAiCS,SAAS,uBAAe,SACpEE,KAAMX,aAAU,EAAVA,EAAa,aACnBY,KAAMZ,aAAU,EAAVA,EAAa,eACnBJ,cACAiB,mBAAoB7E,EAAW8E,yBAInC/C,EAAUkB,EAA2B,QAAlB,EAACjB,EAAUQ,eAAO,aAAjB,EAAmBW,iBAEnCb,EAAgBE,UAClBR,EAAUQ,QAAQO,aAAaT,EAAgBE,SAC/CF,EAAgBE,QAAU,QAE3B,CAACxC,EAAYG,EAAQe,IAExByB,qBAAU,WACR,IAAMoC,EAAmB,WACvB/C,EAAUQ,QAAU,KACpBN,EAAcM,QAAU,KACxBrB,GAAsB,IAElB6D,EAAoB,WACxB7D,GAAsB,IAKxB,OAFAiC,IAAKC,iBAAiB,aAAc0B,GACpC3B,IAAKC,iBAAiB,cAAe2B,GAC9B,WACL5B,IAAKM,oBAAoB,aAAcqB,GACvC3B,IAAKM,oBAAoB,cAAesB,MAEzC,CAAC5C,IAGJ,IAqHI6C,EArHEhC,EAAY,SAACJ,GACjB,IAAKA,EACH,MAAO,GAGT,IAAMf,EAASE,EAAUQ,QAAQS,UAAUJ,EAAMG,MAAOH,EAAMhE,QAE9D,GAA4B,iBAAjBiD,EAAOoD,MAChBpD,EAAOoD,MAAQ,IAAI5G,OAAO6G,KAAKC,YAAYC,MAAMvD,EAAOoD,YACnD,GAAII,MAAMC,QAAQzD,EAAOoD,OAAQ,CAEtC,IAAMM,EAAoB,IAAIlH,OAAO6G,KAAKC,YAAYC,MAAMvD,EAAOoD,MAAMpD,EAAOoD,MAAMrG,OAAS,IAC/FiD,EAAOoD,MAAQM,OACL1D,EAAOoD,QACjBpD,EAAOoD,MAAQhD,EAAcM,QAAQlC,WAKvC,IAFA,IAEA,MAF0B,CAAC,OAAQ,OAAQ,gBAEP,eAAE,CAAjC,IAAMmF,EAAI,KACT3D,EAAO2D,IAASH,MAAMC,QAAQzD,EAAO2D,MACvC3D,EAAO2D,QAAQC,GAInB,OAAO5D,GAwBH6D,GAAc,SAACC,EAAWC,GACJ,MAEnB,EAFW,SAAdD,EACe,QAAjB,EAAA5D,EAAUQ,eAAO,OAAjB,EAAmBV,OAAO,sBAAuB+D,GAEhC,QAAjB,EAAA7D,EAAUQ,eAAO,OAAjB,EAAmBV,OAAO8D,EAAWC,GAGrB,UAAdD,IACFC,EAAQ,IAAIvH,OAAO6G,KAAKC,YAAYC,MAAMQ,IAI5C9D,EAAU,EAAD,KACJD,GAAM,QACR8D,EAAYC,MAKXC,GAAuB,SAACC,EAAUF,GACtC,GAAKtD,EAAoBC,QAAzB,CAKA,MAA0BR,EAAUQ,QAAQW,eAApCH,EAAK,EAALA,MAAOnE,EAAM,EAANA,OACTmB,EAAakC,EAAcM,QACjCxC,EAAW+F,GAAYF,EACvB7D,EAAUQ,QAAQwD,OACD,aAAbD,GAAwC,SAAbA,GAC7BE,YAA0BjG,GAG5BkG,YAAW,WACT5D,EAAgBE,QAAU,CAAEQ,QAAOnE,UACZuE,IAAK+C,uBAAuBC,oBACpCC,SAASrG,KACvB,QAhBDe,EAAiBgF,EAAUF,IAiDvBpF,GAAkBL,EAAlBK,cACF6F,GAAW,CACfhC,KAAoD,QAAhD,EAA0C,UAAxC7D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,uBAAyB,SACpD8D,OAAuD,QAAjD,EAAyC,YAAvC9D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,sBAA0B,SACvD+D,WAAW/D,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,gBAAgBhE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,SAC/HC,UAA4E,QAAnE,EAAEjE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,OAAqB,QAArB,EAAlB,EAAqB,0BAAkB,WAA1B,EAAb,EAAyCgE,SAAS,uBAAe,SAC5EG,KAAMnE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,eAC3BkE,KAAMlE,UAAkB,QAAL,EAAbA,GAAgB,UAAE,WAAL,EAAb,EAAqB,aAC3BmD,YAAa,SAGfqB,EAAa,EAAH,KACL7E,GACAkG,IAGDpF,GAAsBlB,IACxBmC,EAAcK,QAAQ8B,KAAOxC,EAAOwC,KACpCnC,EAAcK,QAAQ+B,OAASzC,EAAOyC,OACtCpC,EAAcK,QAAQgC,UAAY1C,EAAO0C,UACzCrC,EAAcK,QAAQkC,UAAY5C,EAAOyE,OACzCpE,EAAcK,QAAQgE,UAAY1E,EAAO8C,MAAQzC,EAAcK,QAAQ0B,KACvE/B,EAAcK,QAAQiE,cAAgB3E,EAAO4E,cAAgBvE,EAAcK,QAAQ2B,UAGrF,IAAMwC,GAAc,CAClBpF,MAAOA,EACPR,iBAAkB+E,GAClBb,WAAYA,EACZ2B,WAAW,EACXC,YAAa5F,GAGT6F,GAAiB,CACrB9F,sBA9DgC,SAAC+E,EAAUF,GAC3C,GAAKtD,EAAoBC,QAAzB,CAKA,IAAMuE,EAAsB,CAC1B,cAAe,OACf,aAAc,SACd,UAAa,YACb,eAAgB,SAChB,cAAe,OACf,YAAa,QAEf,GAAiB,gBAAbhB,GAA2C,cAAbA,EAA0B,CAC1DJ,GAAYoB,EAAoBhB,GAAWF,GAC3C,IAAMmB,EAAW9E,EAAcM,QAC/B,GAAIwE,EAASC,cACY7D,IAAK+C,uBAAuBC,oBACpCc,iBAAiBF,QAlFP,SAAClF,GAAM,OAAK,WACzC,MAAwBE,EAAUQ,QAAQW,eAApCH,EAAK,EAALA,MAAOnE,EAAM,EAANA,OACb,GAAe,IAAXA,EAAc,CAChByD,EAAgBE,QAAU,CAAEQ,QAAOnE,UACnC,IAAMsI,EAAenF,EAAUQ,QAAQW,eACvCH,EAAQmE,EAAanE,MACrBnE,EAASsI,EAAatI,OAExB,IAAMuI,EAAgBpF,EAAUQ,QAAQS,UAAUD,EAAOnE,GAEzD8G,GAAY7D,GAASsF,EAActF,KA2EjCuF,CAAuBN,EAAoBhB,GAA3CsB,QApBArG,EAAsB+E,EAAUF,IA6DlCZ,WAAY/D,EAAqBiB,EAAcK,QAAUyC,EACzDtE,mBAAoBA,EACpBO,mBAAoBA,EACpBD,YAAaA,EACbJ,qBAAsBA,GAGlByG,GAAc,CAClBtG,sBAAuB8E,GACvBnF,oBAAoB,EACpBO,oBAAoB,EACpBD,aAAa,EACbG,SAAUA,GAGZ,OACE,yBAAKmG,UAAU,sBACbC,YAAa,SAACC,GACG,eAAXA,EAAEC,MAAyBxG,GAC7BuG,EAAEE,mBAIN,yBAAKJ,UAAU,cACb,kBAACK,EAAA,EAAe,KACVjB,GACCvF,EAAWkG,GAAcR,MAGlC,kBAACjJ,EAAA,EAAW,CACVgK,cAAe,SAAC3C,IA/II,SAAC4C,EAAM5C,GAC1B3C,EAAoBC,QAIzBmD,GAAY,QAAST,EAAM6C,eAHzBhH,EAAiB+G,EAAM5C,GA8InB8C,CAAkB,YAAa,IAAI1J,OAAO6G,KAAKC,YAAYC,MAAMH,KAEnEA,MAAOhE,EAAqBY,EAAOoD,MAAQ9E,EAAiB,UAC5DkB,WAAYA,EACZoG,KAAM,OACNO,cAAexF,EAAE,mCAKzBpB,EAAoBtB,UAAYA,EAEjBmI,UAAMC,KAAK9G,GCvVXA", "file": "chunks/chunk.55.js", "sourcesContent": ["import ColorPicker from './ColorPicker';\n\nexport default ColorPicker;", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./RichTextStyleEditor.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport ColorPicker from 'components/StylePicker/ColorPicker';\nimport core from 'core';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport './RichTextStyleEditor.scss';\nimport DataElements from 'constants/dataElement';\nimport TextStylePicker from 'components/TextStylePicker';\nimport adjustFreeTextBoundingBox from 'helpers/adjustFreeTextBoundingBox';\nimport { useTranslation } from 'react-i18next';\n\nconst propTypes = {\n  annotation: PropTypes.object,\n  editor: PropTypes.object,\n  style: PropTypes.shape({\n    TextColor: PropTypes.oneOfType([\n      PropTypes.string,\n      PropTypes.object\n    ]),\n    RichTextStyle: PropTypes.any,\n  }),\n  isFreeTextAutoSize: PropTypes.bool,\n  onFreeTextSizeToggle: PropTypes.func,\n  onPropertyChange: PropTypes.func,\n  onRichTextStyleChange: PropTypes.func,\n  isRedaction: PropTypes.bool,\n  isRichTextEditMode: PropTypes.bool,\n  setIsRichTextEditMode: PropTypes.func,\n  isWidget: PropTypes.bool,\n};\n\nconst RichTextStyleEditor = ({\n  annotation, editor,\n  style,\n  isFreeTextAutoSize,\n  onFreeTextSizeToggle,\n  onPropertyChange,\n  onRichTextStyleChange,\n  isRichTextEditMode,\n  setIsRichTextEditMode,\n  isRedaction,\n  isWidget,\n  activeTool,\n}) => {\n  const [\n    fonts,\n  ] = useSelector(\n    (state) => [\n      selectors.getFonts(state),\n    ],\n    shallowEqual,\n  );\n\n  const [format, setFormat] = useState({});\n  const editorRef = useRef(null);\n  const annotationRef = useRef(null);\n  const propertiesRef = useRef({});\n  const dispatch = useDispatch();\n  const oldSelectionRef = useRef();\n  const richTextEditModeRef = useRef();\n  richTextEditModeRef.current = isRichTextEditMode;\n  const [t] = useTranslation();\n\n  useEffect(() => {\n    const handleSelectionChange = (range, oldRange) => {\n      const shouldRestoreLostSelection = !range && oldRange && editorRef.current;\n      if (shouldRestoreLostSelection) {\n        editorRef.current.setSelection(oldRange.index, oldRange.length);\n      }\n      if (range && editorRef.current) {\n        setFormat(getFormat(range));\n      }\n    };\n    const handleTextChange = () => {\n      setFormat(getFormat(editorRef.current?.getSelection()));\n    };\n    core.addEventListener('editorSelectionChanged', handleSelectionChange);\n    core.addEventListener('editorTextChanged', handleTextChange);\n    // Have to disable instead of closing because annotation popup will reopen itself\n    dispatch(actions.disableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    return () => {\n      core.removeEventListener('editorSelectionChanged', handleSelectionChange);\n      core.removeEventListener('editorTextChanged', handleTextChange);\n      dispatch(actions.enableElements([DataElements.ANNOTATION_STYLE_POPUP]));\n    };\n  }, []);\n\n  useEffect(() => {\n    editorRef.current = editor;\n    annotationRef.current = annotation;\n    if (isRichTextEditMode && annotation) {\n      let StrokeStyle = 'solid';\n      try {\n        StrokeStyle = (annotation['Style'] === 'dash')\n          ? `${annotation['Style']},${annotation['Dashes']}`\n          : annotation['Style'];\n      } catch (err) {\n        console.error(err);\n      }\n      const richTextStyles = annotation.getRichTextStyle();\n      const stylesTemp = richTextStyles[0];\n\n      propertiesRef.current = {\n        Font: annotation.Font,\n        FontSize: annotation.FontSize,\n        TextAlign: annotation.TextAlign,\n        TextVerticalAlign: annotation.TextVerticalAlign,\n        bold: stylesTemp?.['font-weight'] === 'bold' ?? false,\n        italic: stylesTemp?.['font-style'] === 'italic' ?? false,\n        underline: stylesTemp?.['text-decoration']?.includes('underline')\n          || stylesTemp?.['text-decoration']?.includes('word'),\n        strikeout: stylesTemp?.['text-decoration']?.includes('line-through') ?? false,\n        size: stylesTemp?.['font-size'],\n        font: stylesTemp?.['font-family'],\n        StrokeStyle,\n        calculatedFontSize: annotation.getCalculatedFontSize()\n      };\n    }\n\n    setFormat(getFormat(editorRef.current?.getSelection()));\n\n    if (oldSelectionRef.current) {\n      editorRef.current.setSelection(oldSelectionRef.current);\n      oldSelectionRef.current = null;\n    }\n  }, [annotation, editor, isRichTextEditMode]);\n\n  useEffect(() => {\n    const handleEditorBlur = () => {\n      editorRef.current = null;\n      annotationRef.current = null;\n      setIsRichTextEditMode(false);\n    };\n    const handleEditorFocus = () => {\n      setIsRichTextEditMode(true);\n    };\n\n    core.addEventListener('editorBlur', handleEditorBlur);\n    core.addEventListener('editorFocus', handleEditorFocus);\n    return () => {\n      core.removeEventListener('editorBlur', handleEditorBlur);\n      core.removeEventListener('editorFocus', handleEditorFocus);\n    };\n  }, [dispatch]);\n\n\n  const getFormat = (range) => {\n    if (!range) {\n      return {};\n    }\n\n    const format = editorRef.current.getFormat(range.index, range.length);\n\n    if (typeof format.color === 'string') {\n      format.color = new window.Core.Annotations.Color(format.color);\n    } else if (Array.isArray(format.color)) {\n      // the selection contains multiple color, so we set the current color to the last selected color\n      const lastSelectedColor = new window.Core.Annotations.Color(format.color[format.color.length - 1]);\n      format.color = lastSelectedColor;\n    } else if (!format.color) {\n      format.color = annotationRef.current.TextColor;\n    }\n\n    const propertiesToCheck = ['font', 'size', 'originalSize'];\n\n    for (const prop of propertiesToCheck) {\n      if (format[prop] && Array.isArray(format[prop])) {\n        format[prop] = undefined;\n      }\n    }\n\n    return format;\n  };\n\n  const handleTextFormatChange = (format) => () => {\n    let { index, length } = editorRef.current.getSelection();\n    if (length === 0) {\n      oldSelectionRef.current = { index, length };\n      const newSelection = editorRef.current.getSelection();\n      index = newSelection.index;\n      length = newSelection.length;\n    }\n    const currentFormat = editorRef.current.getFormat(index, length);\n\n    applyFormat(format, !currentFormat[format]);\n  };\n\n  const handleColorChange = (name, color) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(name, color);\n      return;\n    }\n    applyFormat('color', color.toHexString());\n  };\n\n  const applyFormat = (formatKey, value) => {\n    if (formatKey === 'size') {\n      editorRef.current?.format('applyCustomFontSize', value);\n    } else {\n      editorRef.current?.format(formatKey, value);\n    }\n\n    if (formatKey === 'color') {\n      value = new window.Core.Annotations.Color(value);\n    }\n\n    // format the entire editor doesn't trigger the editorTextChanged event, so we set the format state here\n    setFormat({\n      ...format,\n      [formatKey]: value\n    });\n  };\n\n  // onPropertyChange\n  const handlePropertyChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onPropertyChange(property, value);\n      return;\n    }\n\n    const { index, length } = editorRef.current.getSelection();\n    const annotation = annotationRef.current;\n    annotation[property] = value;\n    editorRef.current.blur();\n    if (property === 'FontSize' || property === 'Font') {\n      adjustFreeTextBoundingBox(annotation);\n    }\n    // Needs this setTimeout since blur has a slight delay\n    setTimeout(() => {\n      oldSelectionRef.current = { index, length };\n      const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n      editBoxManager.focusBox(annotation);\n    }, 0);\n  };\n\n\n  // onRichTextStyleChange\n  const handleRichTextStyleChange = (property, value) => {\n    if (!richTextEditModeRef.current) {\n      onRichTextStyleChange(property, value);\n      return;\n    }\n\n    const propertyTranslation = {\n      'font-weight': 'bold',\n      'font-style': 'italic',\n      'underline': 'underline',\n      'line-through': 'strike',\n      'font-family': 'font',\n      'font-size': 'size',\n    };\n    if (property === 'font-family' || property === 'font-size') {\n      applyFormat(propertyTranslation[property], value);\n      const freeText = annotationRef.current;\n      if (freeText.isAutoSized()) {\n        const editBoxManager = core.getAnnotationManager().getEditBoxManager();\n        editBoxManager.resizeAnnotation(freeText);\n      }\n    } else {\n      handleTextFormatChange(propertyTranslation[property])();\n    }\n  };\n\n  let properties = {};\n\n  const { RichTextStyle } = style;\n  const defaults = {\n    bold: RichTextStyle?.[0]?.['font-weight'] === 'bold' ?? false,\n    italic: RichTextStyle?.[0]?.['font-style'] === 'italic' ?? false,\n    underline: RichTextStyle?.[0]?.['text-decoration']?.includes('underline') || RichTextStyle?.[0]?.['text-decoration']?.includes('word'),\n    strikeout: RichTextStyle?.[0]?.['text-decoration']?.includes('line-through') ?? false,\n    font: RichTextStyle?.[0]?.['font-family'],\n    size: RichTextStyle?.[0]?.['font-size'],\n    StrokeStyle: 'solid',\n  };\n\n  properties = {\n    ...style,\n    ...defaults,\n  };\n\n  if (isRichTextEditMode && annotation) {\n    propertiesRef.current.bold = format.bold;\n    propertiesRef.current.italic = format.italic;\n    propertiesRef.current.underline = format.underline;\n    propertiesRef.current.strikeout = format.strike;\n    propertiesRef.current.quillFont = format.font || propertiesRef.current.Font;\n    propertiesRef.current.quillFontSize = format.originalSize || propertiesRef.current.FontSize;\n  }\n\n  const commonProps = {\n    fonts: fonts,\n    onPropertyChange: handlePropertyChange,\n    properties: properties,\n    stateless: true,\n    isFreeText: !isRedaction,\n  };\n\n  const nonWidgetProps = {\n    onRichTextStyleChange: handleRichTextStyleChange,\n    properties: isRichTextEditMode ? propertiesRef.current : properties,\n    isFreeTextAutoSize: isFreeTextAutoSize,\n    isRichTextEditMode: isRichTextEditMode,\n    isRedaction: isRedaction,\n    onFreeTextSizeToggle: onFreeTextSizeToggle,\n  };\n\n  const widgetProps = {\n    onRichTextStyleChange: handlePropertyChange,\n    isFreeTextAutoSize: false,\n    isRichTextEditMode: false,\n    isRedaction: false,\n    isWidget: isWidget,\n  };\n\n  return (\n    <div className=\"RichTextStyleEditor\"\n      onMouseDown={(e) => {\n        if (e.type !== 'touchstart' && isRichTextEditMode) {\n          e.preventDefault();\n        }\n      }}\n    >\n      <div className=\"menu-items\">\n        <TextStylePicker\n          {...commonProps}\n          {...(isWidget ? widgetProps : nonWidgetProps)}\n        />\n      </div>\n      <ColorPicker\n        onColorChange={(color) => {\n          handleColorChange('TextColor', new window.Core.Annotations.Color(color));\n        }}\n        color={isRichTextEditMode ? format.color : style['TextColor']}\n        activeTool={activeTool}\n        type={'Text'}\n        ariaTypeLabel={t('option.stylePopup.textStyle')}\n      />\n    </div>\n  );\n};\nRichTextStyleEditor.propTypes = propTypes;\n\nexport default React.memo(RichTextStyleEditor);\n", "import RichTextStyleEditor from './RichTextStyleEditor';\n\nexport default RichTextStyleEditor;"], "sourceRoot": ""}