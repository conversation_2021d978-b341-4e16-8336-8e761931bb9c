ALTER PROC dbo.sub_deleteRate
@orgID int, 
@rateID int,
@isImport bit = 0,
@recordedByMemberID int

AS 

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @depCIDList varchar(max), @conditionID int, @expression varchar(20), @datepart varchar(8), 
		@dateExpression varchar(20), @valueXML xml, @minRuleVersionID int, @ruleID int, 
		@ruleSQL varchar(max), @rateName varchar(200), @scheduleID int, 
		@scheduleName varchar(200), @msgjson varchar(max), @siteID int;
	DECLARE @subRateConditions TABLE (conditionID int);
	DECLARE @subRateDependentConditions TABLE (conditionID int);
	DECLARE @subRateNonDependentConditions TABLE (conditionID int);
	DECLARE @tblRules TABLE (ruleID int, ruleVersionID int);

	SELECT @siteID = srs.siteID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, @scheduleName = srs.scheduleName
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	WHERE rateID = @rateID;

	-- get conditions that use this rate
	INSERT INTO @subRateConditions (conditionID)
	select distinct c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@rateID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
		and k.conditionKey = 'subRate'
	where c.orgID = @orgID;

	-- get conditions that depend on other rates also
	INSERT INTO @subRateNonDependentConditions (conditionID)
	select distinct cv.conditionID
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
	inner join @subRateConditions as tmp on tmp.conditionID = cv.conditionID
	where ck.conditionKey = 'subRate'
	and cv.conditionValue <> cast(@rateID as varchar(10))
	group by cv.conditionID;
	
	-- get conditions that depend on this rate only
	INSERT INTO @subRateDependentConditions (conditionID)
	select conditionID 
	from @subRateConditions
		except
	select conditionID 
	from @subRateNonDependentConditions;

	select @depCIDList = COALESCE(@depCIDList + ',', '') + cast(conditionID as varchar(10)) 
	from @subRateDependentConditions;

	BEGIN TRAN;
		IF EXISTS (select 1 from @subRateNonDependentConditions) BEGIN
			-- delete this subRateID from cv
			DELETE cv
			FROM dbo.ams_virtualGroupConditionValues as cv
			INNER JOIN dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
			INNER JOIN @subRateNonDependentConditions as tmp on tmp.conditionID = cv.conditionID
			WHERE ck.conditionKey = 'subRate'
			AND cv.conditionValue = cast(@rateID as varchar(10));

			select @conditionID = min(conditionID) from @subRateNonDependentConditions;
			WHILE @conditionID IS NOT NULL BEGIN
				select @expression = null, @datepart = null, @dateExpression = null, @valueXML = null;

				select @expression = ge1.expression, @datepart = gc.[datePart], @dateExpression = ge2.expression
				from dbo.ams_virtualGroupConditions as gc
				inner join dbo.ams_virtualGroupExpressions as ge1 on ge1.expressionID = gc.expressionID
				left outer join dbo.ams_virtualGroupExpressions as ge2 on ge2.expressionID = gc.dateExpressionID
				where gc.orgID = @orgID
				and gc.conditionID = @conditionID;

				SELECT @valueXML = ISNULL((
										SELECT DISTINCT 
											k.conditionKey AS [key],
											(SELECT STUFF(( SELECT ',' + tmp.conditionValue
												FROM ams_virtualGroupConditionValues tmp
												WHERE tmp.conditionID = v.conditionID
												AND tmp.conditionKeyID = v.conditionKeyID
												ORDER BY tmp.conditionValue
												FOR XML PATH(''), TYPE
											).value('.', 'VARCHAR(MAX)'), 1, 1, '' )
											) AS [value],  
											NULLIF(v.AFID,0) AS [afid]
										FROM dbo.ams_virtualGroupConditionValues AS v
										INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = v.conditionKeyID
										WHERE v.conditionID = @conditionID
										ORDER BY 1, 2
									FOR XML RAW('value'), ROOT('values'), TYPE),'<values/>');

				EXEC dbo.ams_updateVirtualGroupCondition @orgID=@orgID, @conditionID=@conditionID, @expression=@expression, 
					@datepart=@datepart, @dateExpression=@dateExpression, @value=@valueXML, @bypassQueue=0;

				select @conditionID = min(conditionID) from @subRateNonDependentConditions where conditionID > @conditionID;
			END

			-- get rules these conditions are in
			INSERT INTO @tblRules (ruleID, ruleVersionID)
			select distinct r.ruleID, rv.ruleVersionID
			from dbo.ams_virtualGroupRules as r
			inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
				and rv.ruleID = r.ruleID
			inner join dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = r.ruleID
				and rcs.ruleVersionID = rv.ruleVersionID
			inner join dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
			inner join @subRateNonDependentConditions as tmp on tmp.conditionID = rc.conditionID
			where r.orgID = @orgID;

			-- regenerate SQL for all updated rules
			select @minRuleVersionID = min(ruleVersionID) from @tblRules;
			while @minRuleVersionID is not null BEGIN
				SET @ruleID = NULL;
				
				SELECT @ruleID = ruleID
				FROM @tblRules
				WHERE ruleVersionID = @minRuleVersionID;

				EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@minRuleVersionID;

				select @minRuleVersionID = min(ruleVersionID) from @tblRules where ruleVersionID > @minRuleVersionID;
			END
		END

		IF @depCIDList IS NOT NULL
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@depCIDList, @recordedByMemberID=@recordedByMemberID, @bypassQueue=0;

		UPDATE dbo.sub_rateFrequenciesMerchantProfiles
		SET [status] = 'D'
		WHERE rfid IN (
			SELECT DISTINCT rfid
			FROM dbo.sub_rateFrequencies rf
			WHERE rf.rateID = @rateID
		);

		UPDATE dbo.sub_rateFrequencies
		SET [status] = 'D'
		WHERE rfid IN (
			SELECT DISTINCT rfid
			FROM dbo.sub_rateFrequencies rf
			WHERE rf.rateID = @rateID
		);

		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM dbo.cms_siteResources as sr
		INNER JOIN dbo.sub_rates as r on r.siteResourceID = sr.siteResourceID
		WHERE r.rateID = @rateID;

		UPDATE dbo.sub_rates
		set fallbackRenewalRateID = null
		where fallbackRenewalRateID = @rateID;

		UPDATE dbo.sub_rates
		set [status] = 'D',
			rateOrder = NULL
		where rateID = @rateID;

		EXEC dbo.sub_reorderRates @rateID=@rateID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msgjson = STRING_ESCAPE('Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] has been deleted.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msgjson,
			@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
