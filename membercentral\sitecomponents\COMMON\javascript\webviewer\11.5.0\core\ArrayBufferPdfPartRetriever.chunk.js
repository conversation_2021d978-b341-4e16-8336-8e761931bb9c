/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[2],
		{
			629: function (ya, va, r) {
				r.r(va);
				ya = r(53);
				r = r(547);
				var la = (function () {
					function na(ma) {
						this.buffer = ma;
						this.fileSize =
							null === ma || void 0 === ma ? void 0 : ma.byteLength;
					}
					na.prototype.getFileData = function (ma) {
						ma(new Uint8Array(this.buffer));
					};
					na.prototype.getFile = function () {
						return Promise.resolve(null);
					};
					return na;
				})();
				Object(ya.a)(la);
				Object(r.a)(la);
				Object(r.b)(la);
				va["default"] = la;
			},
		},
	]);
}).call(this || window);
