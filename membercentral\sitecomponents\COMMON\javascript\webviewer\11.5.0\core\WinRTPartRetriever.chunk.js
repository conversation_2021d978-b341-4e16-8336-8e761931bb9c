/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[14],
		{
			634: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(366);
				ya = r(624);
				var ma = r(129);
				r = r(547);
				var ja = {},
					ia = (function (ca) {
						function y(x, n) {
							var h = ca.call(this, x, n) || this;
							h.url = x;
							h.range = n;
							h.status = na.a.NOT_STARTED;
							return h;
						}
						Object(la.c)(y, ca);
						y.prototype.start = function (x) {
							var n = this;
							"undefined" === typeof ja[this.range.start] &&
								((ja[this.range.start] = {
									O0: function (h) {
										var b = atob(h),
											e,
											f = b.length;
										h = new Uint8Array(f);
										for (e = 0; e < f; ++e) h[e] = b.charCodeAt(e);
										b = h.length;
										e = "";
										for (var a = 0; a < b; )
											(f = h.subarray(a, a + 1024)),
												(a += 1024),
												(e += String.fromCharCode.apply(null, f));
										n.O0(e, x);
									},
									CZa: function () {
										n.status = na.a.ERROR;
										x({ code: n.status });
									},
								}),
								window.external.notify(this.url),
								(this.status = na.a.STARTED));
							n.oM();
						};
						return y;
					})(ya.ByteRangeRequest);
				ya = (function (ca) {
					function y(x, n, h, b) {
						x = ca.call(this, x, h, b) || this;
						x.CG = ia;
						return x;
					}
					Object(la.c)(y, ca);
					y.prototype.DD = function (x, n) {
						return ""
							.concat(x, "?")
							.concat(n.start, "&")
							.concat(n.stop ? n.stop : "");
					};
					return y;
				})(ma.a);
				Object(r.a)(ya);
				Object(r.b)(ya);
				va["default"] = ya;
			},
		},
	]);
}).call(this || window);
