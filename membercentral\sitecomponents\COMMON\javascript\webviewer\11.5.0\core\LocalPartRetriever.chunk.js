/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[7],
		{
			626: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(2),
					ma = r(220);
				ya = r(129);
				var ja = r(367);
				r = r(547);
				var ia = window;
				ya = (function (ca) {
					function y(x, n, h) {
						n = ca.call(this, x, n, h) || this;
						if (x.name && "xod" !== x.name.toLowerCase().split(".").pop())
							throw Error("Not an XOD file");
						if (!ia.FileReader || !ia.File || !ia.Blob)
							throw Error("File API is not supported in this browser");
						n.file = x;
						n.EK = [];
						n.bU = 0;
						return n;
					}
					Object(la.c)(y, ca);
					y.prototype.sY = function (x, n, h) {
						var b = this,
							e = new FileReader();
						e.onloadend = function (f) {
							if (0 < b.EK.length) {
								var a = b.EK.shift();
								a.mNa.readAsBinaryString(a.file);
							} else b.bU--;
							if (e.error) {
								f = e.error;
								if (f.code === f.ABORT_ERR) {
									Object(na.i)(
										"Request for chunk "
											.concat(n.start, "-")
											.concat(n.stop, " was aborted"),
									);
									return;
								}
								return h(f);
							}
							if ((f = e.content || f.target.result)) return h(!1, f);
							Object(na.i)("No data was returned from FileReader.");
						};
						n &&
							(x = (x.slice || x.webkitSlice || x.mozSlice || x.DZa).call(
								x,
								n.start,
								n.stop,
							));
						0 === b.EK.length && 50 > b.bU
							? (e.readAsBinaryString(x), b.bU++)
							: b.EK.push({ mNa: e, file: x });
						return function () {
							e.abort();
						};
					};
					y.prototype.yA = function (x) {
						var n = this;
						n.AK = !0;
						var h = ma.a;
						n.sY(n.file, { start: -h, stop: n.file.size }, function (b, e) {
							if (b)
								return (
									Object(na.i)("Error loading end header: %s ".concat(b)), x(b)
								);
							if (e.length !== h)
								throw Error("Zip end header data is wrong size!");
							n.$e = new ja.a(e);
							var f = n.$e.A$();
							n.sY(n.file, f, function (a, w) {
								if (a)
									return (
										Object(na.i)(
											"Error loading central directory: %s ".concat(a),
										),
										x(a)
									);
								if (w.length !== f.stop - f.start)
									throw Error("Zip central directory data is wrong size!");
								n.$e.Cfa(w);
								n.vT = !0;
								n.AK = !1;
								return x(!1);
							});
						});
					};
					y.prototype.VZ = function (x, n) {
						var h = this,
							b = h.Uj[x];
						if (h.$e.I7(x)) {
							var e = h.$e.wE(x),
								f = h.sY(h.file, e, function (a, w) {
									delete h.Uj[x];
									if (a)
										return (
											Object(na.i)(
												'Error loading part "%s": %s, '
													.concat(x, ", ")
													.concat(a),
											),
											n(a)
										);
									if (w.length !== e.stop - e.start)
										throw Error("Part data is wrong size!");
									n(!1, x, w, h.$e.Uba(x));
								});
							b.aja = !0;
							b.cancel = f;
						} else n(Error('File not found: "'.concat(x, '"')), x);
					};
					return y;
				})(ya.a);
				Object(r.a)(ya);
				Object(r.b)(ya);
				va["default"] = ya;
			},
		},
	]);
}).call(this || window);
