<cfcomponent output="false">

	<!--- Sub Types --->

	<cffunction name="checkTypeName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="typeName" type="string" required="true">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select typeID
			from dbo.sub_Types
			where typeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeName#">
			and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			and status = 'A'
			<cfif arguments.typeID neq 0>
				and typeID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success": qryCheck.recordCount eq 0 }>
	</cffunction>

	<cffunction name="addType" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="typeName" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="frontEndCompletedContent" type="string" required="true">
		<cfargument name="feEmailNotification" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'', "typeID":0 }>
		
		<cftry>
			<cfset local.payLoadData = { "_mcrecordedbymemberid":session.cfcuser.memberdata.memberID, "type":arguments.typeName, "frontendcontent":arguments.frontEndContent, 
											"frontendcompletedcontent":arguments.frontEndCompletedContent, "notificationemail":arguments.feEmailNotification }>
			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="POST", endpoint="subscription/type", payload=serializeJSON(local.payloadData))>
			
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			<cfelse>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubType">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select typeID
					from dbo.sub_types
					where [uid] = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.strInsertResponse.data.subscriptiontype.api_id#">
					and status <> 'D';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.returnStruct["typeID"] = local.qrySubType.typeID>
				<cfset local.returnStruct['success'] = true>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateType" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="typeName" type="string" required="true">
		<cfargument name="typeCode" type="string" required="true">
		<cfargument name="typeUID" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="frontEndCompletedContent" type="string" required="true">
		<cfargument name="feEmailNotification" type="string" required="true">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'' }>
	
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubType">
				select [uid]
				from dbo.sub_types
				where typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
				and status <> 'D'
			</cfquery>

			<cfset local.payLoadData = { "_mcrecordedbymemberid":session.cfcuser.memberdata.memberID, "type":arguments.typeName, "typecode":arguments.typeCode, 
				"frontendcontent":arguments.frontEndContent, "frontendcompletedcontent":arguments.frontEndCompletedContent, "notificationemail":arguments.feEmailNotification }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.typeUID))>
				<cfset local.payLoadData["api_id"] = trim(arguments.typeUID)>
			</cfif>
	
			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="PUT", endpoint="subscription/type/#local.qrySubType.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>
	
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeType" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="typeUID" type="string" required="true">
	
		<cfset var local = structNew()>
	
		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.payLoadData = { "_mcrecordedbymemberid":session.cfcuser.memberdata.memberID }>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="subscription/type/#arguments.typeUID#", payload=serializeJSON(local.payloadData))>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>
	
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<!--- Sets and Addons --->

	<cffunction name="checkSetName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="setName" type="string" required="true">

		<cfset var qryCheck = "">

		<cfquery name="qryCheck" datasource="#application.dsn.membercentral.dsn#">
			select setID
			from dbo.sub_sets
			where setName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.setName#">
			and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfif arguments.setID neq 0>
				and setID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">
			</cfif>
		</cfquery>

		<cfreturn { "success": qryCheck.recordCount eq 0 }>
	</cffunction>

	<cffunction name="addSet" access="public" output="false" returntype="numeric">
		<cfargument name="setName" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="chkName" type="boolean" required="false" default="true">

		<cfset var local = structNew()>
		<cfset local.setID = 0>

		<cfset local.chkNameResult = true>

		<cfif arguments.chkName eq 'true'>
			<cfset local.chkSetName = checkSetName(mcproxy_siteID=arguments.siteID, setID=0, setName=arguments.setName)>
			<cfif local.chkSetName.success neq 'true'>
				<cfset local.chkNameResult = false>
			</cfif>
		</cfif>

		<cfif local.chkNameResult eq 'true'>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAdd">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @setID int, @setName varchar(200), @siteID int, @orgID int, 
						@msgjson varchar(max), @recordedByMemberID int;
					SET @setName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.setName#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @orgID = orgID 
					FROM dbo.sites 
					WHERE siteID = @siteID;

					BEGIN TRAN;
						INSERT INTO dbo.sub_sets(setName, siteID)
						VALUES(@setName, @siteID)

						SELECT @setID = SCOPE_IDENTITY();

						-- audit log
						DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS varchar(10))+' }';
						SET @msgjson = 'New Subscription set ' + STRING_ESCAPE(QUOTENAME(@setName),'json') + ' has been created.';
							
						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msgjson,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					COMMIT TRAN;

					SELECT @setID AS setID;
					
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.setID = local.qryAdd.setID>
		</cfif>

		<cfreturn local.setID>
	</cffunction>

	<cffunction name="updateSet" access="public" output="false" returntype="void">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="setName" type="string" required="true">
		<cfargument name="setUID" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.chkSetName = checkSetName(mcproxy_siteID=arguments.siteID, setID=arguments.setID, setName=arguments.setName)>

		<cfif local.chkSetName.success eq 'true'>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @setID int, @setName varchar(200), @setUID varchar(36), @siteID int, 
						@siteCode varchar(100), @orgID int, @msgjson varchar(max), @recordedByMemberID int,
						@oldSetName varchar(200), @oldSetUID varchar(36), @msg varchar(MAX), 
						@crlf varchar(2) = char(13) + char(10);

					SET @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">;
					SET @setName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.setName#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					SET @setUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.setUID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @siteCode = siteCode, @orgID = orgID 
					FROM dbo.sites 
					WHERE siteID = @siteID;
				
					SELECT @oldSetName = setName, @oldSetUID = [uid] 
					FROM dbo.sub_sets 
					WHERE setID = @setID;
				
					BEGIN TRAN;
						update dbo.sub_sets
						set setName = @setName
							<cfif len(arguments.setUID) AND application.objUser.isSuperUser(cfcuser=session.cfcuser) >
								, [uid] = @setUID
							</cfif>
						where setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">
						and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

						IF @oldSetName <> @setName OR @oldSetUID <> @setUID BEGIN
							DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS varchar(10))+' }';

							SET @msg =  'Subscription Set ' + QUOTENAME(@oldSetName) + ' updated. The following changes have been made: ';
							IF @oldSetName <> @setName BEGIN
								SET @msg = @msg + @crlf + 'Name changed from ' + QUOTENAME(@oldSetName) + ' to ' + QUOTENAME(@setName) + '. ';
							END
							<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
								IF @oldSetUID <> @setUID BEGIN
									SET @msg = @msg + @crlf + 'UID changed from ' + QUOTENAME(@oldSetUID) + ' to ' + QUOTENAME(@setUID) + '. ';
								END
							</cfif>

							SET @msg = STRING_ESCAPE(@msg,'json');

							EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
								@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
						END
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

	</cffunction>

	<cffunction name="addSubToSet" access="public" output="false" returntype="struct">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAdd">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @setID int, @siteID int, @subscriptionID int, @setName VARCHAR(200), @orgID int, 
					@recordedByMemberID int, @subscriptionName VARCHAR(300), @orderNum int,
					@msg VARCHAR(MAX), @crlf CHAR(2) = CHAR(13) + CHAR(10);
				
				SET @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

				SELECT @orgID = orgID 
				FROM dbo.sites 
				WHERE siteID = @siteID;
				
				SELECT @setName = setName 
				FROM dbo.sub_sets 
				WHERE setID = @setID;

				SELECT @orderNum = ISNULL(MAX(orderNum), 0) + 1
				FROM dbo.sub_subscriptionSets
				WHERE setID = @setID;

				SET @orderNum = ISNULL(@orderNum,1);

				SELECT @subscriptionName = subscriptionName
				FROM sub_subscriptions
				WHERE subscriptionID = @subscriptionID

				IF NOT EXISTS (select subscriptionSetID from dbo.sub_subscriptionSets where setID = @setID and subscriptionID = @subscriptionID) BEGIN
					BEGIN TRAN;
						INSERT INTO dbo.sub_subscriptionSets(setID, subscriptionID, orderNum)
						VALUES (@setID, @subscriptionID, @orderNum);

						DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS varchar(10))+', "SUBSCRIPTIONID":'+ CAST(@subscriptionID AS VARCHAR(10))+' }';

						SET @msg = 'Subscription ' + QUOTENAME(@subscriptionName) + ' has been added to Set ' + QUOTENAME(@setName);
						SET @msg = STRING_ESCAPE(@msg,'json');

						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					COMMIT TRAN;
				END

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateSubscriptionsInSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="subsInSet" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteSubsFromSubSet">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @setID int, @siteID int, @setName varchar(200), @orgID INT, @recordedByMemberID int, @msg varchar(max);
					
					SET @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					
					SELECT @orgID = orgID 
					FROM dbo.sites
					WHERE siteID = @siteID;

					SELECT @setName = setName 
					FROM dbo.sub_sets 
					WHERE setID = @setID;

					SELECT @msg = STRING_AGG(s.subscriptionName,', ')
					FROM dbo.sub_subscriptionSets as ss
					INNER JOIN dbo.sub_subscriptions as s ON s.subscriptionID = ss.subscriptionID
					WHERE ss.setID = @setID
					<cfif len(arguments.subsInSet)>
						AND s.subscriptionID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.subsInSet#">)
					</cfif>;

					BEGIN TRAN;
						DELETE FROM dbo.sub_subscriptionSets
						WHERE setID = @setID
						<cfif len(arguments.subsInSet)>
							AND subscriptionID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.subsInSet#">)
						</cfif>;

						EXEC subs_reorderSubscriptionSets @setID=@setID;

						IF ISNULL(@msg,'') <> '' BEGIN
							DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS varchar(10))+' }';
							SET @msg = 'Subscription(s) [' + @msg + '] in Set ' + QUOTENAME(@setName) + ' have been deleted';
							SET @msg = STRING_ESCAPE(@msg,'json');

							EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
								@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
						END
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif len(arguments.subsInSet)>
				<cfloop list="#arguments.subsInSet#" index="local.subID">
					<cfset addSubToSet(subID=local.subID, setID=arguments.setID, siteID=arguments.mcproxy_siteID)>
				</cfloop>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSubscriptionSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="setID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemoveSubSet">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @setID int, @setName varchar(200), @siteCode varchar(100), @msgjson varchar(max);
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">;
					SET @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">;
					
					SELECT @setName = setName 
					FROM dbo.sub_sets 
					WHERE setID = @setID;

					BEGIN TRAN;
						DELETE FROM dbo.sub_addons
						WHERE childSetID = @setID;

						DELETE FROM dbo.sub_subscriptionSets
						WHERE setID = @setID;

						DELETE FROM dbo.sub_sets
						WHERE setID = @setID;

						-- audit log
						DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS varchar(10))+' }';

						SET @msgjson = 'Subscription Set ' + STRING_ESCAPE(QUOTENAME(@setName),'json') + ' has been deleted.';

						EXEC dbo.sub_insertAuditLog @orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">, 
							@siteID=@siteID, @areaCode='SUBSET', @msgjson=@msgjson, @subKeyMapJSON=@subKeyMapJSON, 
							@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="chkSubInUse" access="public" output="false" returntype="struct">
		<cfargument name="subID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberNum">
			select count(s.memberID) as subscriberCount
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode <> 'D'
			where s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.subscribercount = val(local.qrySubscriberNum.subscriberCount)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSetHierarchy" access="public" output="false" returntype="query">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySetHierarchy = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySetHierarchy">
			set nocount on;

			declare @siteID int, @setID int, @addonSetCount int;

			select @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			select @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">;

			<!--- step one, insert all the subscriptions for this set --->

			IF OBJECT_ID('tempdb..####SubscriptionSets') IS NOT NULL
				DROP TABLE ####SubscriptionSets;

			select s.setID, s.setName, NULL as parentSetID, ss.subscriptionID, subs.subscriptionName, sa.childSetID,
			CAST(RIGHT('100000'+(s.setID),4) as varchar(max)) + '.' + CAST(RIGHT('100000'+(ss.subscriptionID),4) as varchar(max)) as thePath
			into ####SubscriptionSets
			from dbo.sub_sets s
			left outer join dbo.sub_subscriptionSets ss on ss.setID = s.setID
			left outer join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID and subs.status = 'A'
			left outer join dbo.sub_addons sa on sa.subscriptionID = subs.subscriptionID
			where s.setID = @setID and s.siteID = @siteID and s.status = 'A';

			<!--- step two, count all the base subscriptions --->
			select @addonSetCount = count(ss.childSetID)
			from ####SubscriptionSets ss;

			WHILE @addonSetCount > 0
			BEGIN
				<!--- step three, insert all the base subscriptions --->
				INSERT INTO ####SubscriptionSets(setID, setName, parentSetID, subscriptionID, subscriptionName, childSetID, thePath)
				select s.setID, s.setName, ss.setID as parentSetID, sset.subscriptionID, subs.subscriptionName, sa.childSetID,
				ss.thePath + '.' + CAST(RIGHT('100000'+(s.setID),4) as varchar(max)) + '.' + CAST(RIGHT('100000'+(sset.subscriptionID),4) as varchar(max)) as thePath
				from ####SubscriptionSets ss
				inner join dbo.sub_sets s on s.setID = ss.childSetID and s.siteID = @siteID and s.status = 'A'
				left outer join dbo.sub_subscriptionSets sset on sset.setID = s.setID
				left outer join dbo.sub_subscriptions subs on subs.subscriptionID = sset.subscriptionID and subs.status = 'A'
				left outer join dbo.sub_addons sa on sa.subscriptionID = subs.subscriptionID
				where not exists (select * from ####SubscriptionSets ss2 where thePath like ss.thePath + '.' +  CAST(RIGHT('100000'+(ss.childSetID),4) as varchar(max)) + '%');

				<!--- step four, re-count for included addons --->
				select @addonSetCount = count(ss.childSetID)
				from ####SubscriptionSets ss
				where not exists (select * from ####SubscriptionSets ss2 where thePath like ss.thePath + '.' +  CAST(RIGHT('100000'+(ss.childSetID),4) as varchar(max)) + '%');
			END

			select setID, setName, parentSetID, subscriptionID, subscriptionName, thePath
			from ####SubscriptionSets
			group by setID, setName, parentSetID, subscriptionID, subscriptionName, thePath
			order by thePath;

			IF OBJECT_ID('tempdb..####SubscriptionSets') IS NOT NULL
				DROP TABLE ####SubscriptionSets;

			set nocount off;
		</cfquery>

		<cfreturn qrySetHierarchy>
	</cffunction>

	<cffunction name="chkSubInSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data.subcount = 0>

		<!--- first check if the subscription is in the set at all --->
		<cfset local.qrySetHierarchy = getSetHierarchy(setID=arguments.setID, siteID=arguments.mcproxy_siteID)>

		<cfquery dbtype="query" name="local.qrySubCheck">
			select setID
			from [local].qrySetHierarchy
			where subscriptionID = #arguments.subID#
		</cfquery>

		<cfif local.qrySubCheck.recordCount gt 0>
			<cfset local.data.subcount = 1>
		</cfif>

		<cfif local.data.subcount eq 0>
			<!--- next check if the addons for the sub are in the set at all --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddonSets">
				select sa.childSetID
				from dbo.sub_addons sa
				inner join dbo.sub_sets s on s.setID = sa.childSetID and s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				where sa.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
			</cfquery>

			<cfloop query="local.qryAddonSets">
				<cfset local.qryCurrAddon = getSetHierarchy(setID=local.qryAddonSets.childSetID, siteID=arguments.mcproxy_siteID)>

				<cfquery dbtype="query" name="local.qryAddonChk">
						select subscriptionID
						from [local].qrySetHierarchy
						union all
						select subscriptionID
						from [local].qryCurrAddon
				</cfquery>

				<cfquery dbtype="query" name="local.qryAddonChkCount">
					SELECT subscriptionID
					FROM [local].qryAddonChk
					GROUP BY subscriptionID
					HAVING Count(subscriptionID) > 1
				</cfquery>

				<cfif local.qryAddonChkCount.recordcount gt 0>
					<cfset local.data.subcount = 1>
					<cfbreak>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doSortSubInSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_sortSubInSet">
				<cfprocparam type="In" value="#arguments.mcproxy_siteID#" cfsqltype="cf_sql_integer">
				<cfprocparam type="In" value="#arguments.setID#" cfsqltype="cf_sql_integer">
				<cfprocparam type="In" value="#arguments.dir#" cfsqltype="cf_sql_varchar">
				<cfprocparam type="In" value="#session.cfcuser.memberdata.memberID#" cfsqltype="cf_sql_integer">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doMoveAddonInSub" access="public" output="false" returntype="struct">
		<cfargument name="aoID" type="numeric" required="true">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_moveAddonInSub">
				<cfprocparam type="In" value="#arguments.subID#" cfsqltype="cf_sql_integer">
				<cfprocparam type="In" value="#arguments.aoID#" cfsqltype="cf_sql_integer">
				<cfprocparam type="In" value="#arguments.dir#" cfsqltype="cf_sql_varchar">
				<cfprocparam type="In" value="#session.cfcuser.memberdata.memberID#" cfsqltype="cf_sql_integer">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="chkSetInAddon" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrderNum">
			select count(sa.subscriptionID) as addonCount
			from dbo.sub_addons sa
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = sa.subscriptionID
				and subs.status = 'A'
			inner join dbo.sub_types t on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			where childSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.addoncount = local.qryOrderNum.addonCount>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addAddonToSet" access="public" output="false" returntype="numeric">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="childSetID" type="numeric" required="true">
		<cfargument name="minAllowed" type="string" required="true">
		<cfargument name="maxAllowed" type="string" required="true">
		<cfargument name="useAcctCodeInSet" type="numeric" required="true">
		<cfargument name="useTermEndDateInSet" type="numeric" required="true">
		<cfargument name="PCnum" type="numeric" required="true">
		<cfargument name="PCPctOffEach" type="numeric" required="true">
		<cfargument name="feChangeSelect" type="string" required="true">
		<cfargument name="feAddAdditional" type="string" required="true">
		<cfargument name="feChangePriceSelect" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.addonID = 0>
		<cfset local.chkSetAddon = checkSetAsAddon(addonID=0, subscriptionID=arguments.subscriptionID, setID=arguments.childSetID, siteID=arguments.siteID)>

		<cfif (local.chkSetAddon.success eq 'true')>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrderNum">
				select IsNull(max(orderNum), 0) as orderNum
				from dbo.sub_addons
				where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">
			</cfquery>

			<cfset local.newOrderNum = local.qryOrderNum.orderNum + 1>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAdd">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @addonID int, @subAdminResourceTypeID INT, @appCreatedContentResourceTypeID INT, @languageID INT, 
						@contentID INT, @contentSRID INT, @siteID int, @subscriptionID int, @childSetID int, @subscriptionName VARCHAR(200), @setName VARCHAR(100),
						@newOrderNum INT, @minAllowed INT, @maxAllowed INT, @useAcctCodeInSet BIT, @useTermEndDateInSet BIT, @PCnum INT, @PCPctOffEach DECIMAL(5,2),
						@feChangePriceSelect BIT, @feChangeSelect BIT, @feAddAdditional BIT, @recordedByMemberID INT, @msgjson VARCHAR(MAX), 
						@crlf CHAR(2) = CHAR(13) + CHAR(10), @orgID INT;

					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					
					select @orgID = orgID, @languageID = defaultLanguageID, @subAdminResourceTypeID = subscriptionAdminSiteResourceID 
					from dbo.sites 
					where siteID = @siteID;

					select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
					
					SET @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">;
					SET @childSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.childSetID#">;
					SET @newOrderNum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newOrderNum#">;
					SET @minAllowed = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.minAllowed)#">,0);
					SET @maxAllowed = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxAllowed)#">,0);
					SET @useAcctCodeInSet = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.useAcctCodeInSet#">;
					SET @useTermEndDateInSet = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.useTermEndDateInSet#">;
					SET @PCnum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.PCnum#">;
					SET @PCPctOffEach = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.PCPctOffEach#">;
					SET @feChangePriceSelect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.feChangePriceSelect#">;
					SET @feChangeSelect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.feChangeSelect#">;
					SET @feAddAdditional = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.feAddAdditional#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @subscriptionName = subscriptionName
					FROM dbo.sub_subscriptions
					WHERE subscriptionID = @subscriptionID;

					SELECT @setName = setName
					FROM dbo.sub_sets
					WHERE setID = @childSetID;

					BEGIN TRAN;
						INSERT INTO dbo.sub_addons(subscriptionID, childSetID, orderNum, minAllowed, maxAllowed, useAcctCodeInSet,
							useTermEndDateInSet, PCnum, PCPctOffEach, frontEndAllowSelect, frontEndAllowChangePrice,frontEndAddAdditional)
						VALUES(@subscriptionID, @childSetID, @newOrderNum, @minAllowed, @maxAllowed, @useAcctCodeInSet, 
							@useTermEndDateInSet, @PCnum, @PCPctOffEach, @feChangeSelect, @feChangePriceSelect, @feAddAdditional);

						SELECT @addonID = SCOPE_IDENTITY();

						EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceTypeID, 
							@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
							@rawContent=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frontEndContent#">,
							@memberID=<cfqueryparam value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)#" cfsqltype="cf_sql_integer" />,
							@contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

						UPDATE dbo.sub_addons
						SET frontEndContentID = @contentID
						WHERE addonID = @addonID;

						-- audit log
						DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "ADDONID":'+CAST(@addonID AS VARCHAR(10))+', "CHILDSETID":'+CAST(@childSetID AS VARCHAR(10))+' }';
						SET @msgjson = 'New AddOn created for Subscription ' + QUOTENAME(@subscriptionName) + '.' +
							@crlf + 'Subscription Set: ' + QUOTENAME(@setName) +
							CASE WHEN @newOrderNum IS NOT NULL THEN @crlf + 'Order Number: ' + CAST(@newOrderNum AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @minAllowed IS NOT NULL THEN @crlf + 'Min Allowed: ' + CAST(@minAllowed AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @maxAllowed IS NOT NULL THEN @crlf + 'Max Allowed: ' + CAST(@maxAllowed AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @useAcctCodeInSet IS NOT NULL THEN @crlf + 'Use Set Acct Code: ' + CASE WHEN @useAcctCodeInSet = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
							CASE WHEN @useTermEndDateInSet IS NOT NULL THEN @crlf + 'Use Term End Date In Set: ' + CAST(@useTermEndDateInSet AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @PCnum IS NOT NULL THEN @crlf + 'Items Free in Set: ' + CAST(@PCnum AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @PCPctOffEach IS NOT NULL THEN @crlf + 'PC Percent Off Each: ' + CAST(@PCPctOffEach AS VARCHAR(10)) ELSE '' END +
							CASE WHEN @feChangeSelect IS NOT NULL THEN @crlf + 'Should this Addon appear in front end: ' + CASE WHEN @feChangeSelect = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
							CASE WHEN @feAddAdditional IS NOT NULL THEN @crlf + 'Allow User to add additional selections in front end wizard: ' + CASE WHEN @feAddAdditional = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
							CASE WHEN @feChangePriceSelect IS NOT NULL THEN @crlf + 'Front End Change Price Select: ' + CASE WHEN @feChangePriceSelect = 0 THEN 'No' ELSE 'Yes' END ELSE '' END;

						SET @msgjson = STRING_ESCAPE(@msgjson,'json');

						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBADDON', @msgjson=@msgjson,
								@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					COMMIT TRAN;

					select @addonID as addonID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.addonID = local.qryAdd.addonID>
		</cfif>

		<cfreturn local.addonID>
	</cffunction>

	<cffunction name="updateAddonToSet" access="public" output="false" returntype="void">
		<cfargument name="addonID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="childSetID" type="numeric" required="true">
		<cfargument name="minAllowed" type="string" required="true">
		<cfargument name="maxAllowed" type="string" required="true">
		<cfargument name="useAcctCodeInSet" type="numeric" required="true">
		<cfargument name="useTermEndDateInSet" type="numeric" required="true">
		<cfargument name="PCnum" type="numeric" required="true">
		<cfargument name="PCPctOffEach" type="numeric" required="true">
		<cfargument name="feChangeSelect" type="string" required="true">
		<cfargument name="feAddAdditional" type="string" required="true">
		<cfargument name="feChangePriceSelect" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.chkSetAddon = checkSetAsAddon(addonID=arguments.addonID, subscriptionID=arguments.subscriptionID, setID=arguments.childSetID, siteID=arguments.siteID)>

		<cfif local.chkSetAddon.success eq 'true'>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;
						CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Min Allowed] varchar(max), [Max Allowed] varchar(max), 
							[Use Account Code In Set] varchar(max), [Use Term End Date In Set] varchar(max), [PC Number] varchar(max), 
							[PC Percent Off Each] varchar(max), [Front End Change Select] varchar(max), [Front End Add Additional] varchar(max), 
							[Front End Change Price Select] varchar(max), [Front End Content] varchar(max));

							DECLARE @siteID int, @orgID int, @addonID int, @subscriptionID int, @subscriptionName VARCHAR(300), @setName VARCHAR(200), @childSetID int, 
								@minAllowed INT, @maxAllowed INT, @useAcctCodeInSet BIT, @useTermEndDateInSet BIT, @PCnum INT, @PCPctOffEach DECIMAL(5,2), 
								@feChangePriceSelect BIT, @feChangeSelect BIT, @feAddAdditional BIT, @recordedByMemberID INT, 
								@msgjson VARCHAR(MAX), @crlf CHAR(2) = CHAR(13) + CHAR(10), @feContentID INT, @feContentLanguageID INT, 
								@feContentIsHTML BIT, @feContentTitle VARCHAR(200), @feContentDesc VARCHAR(400);

							SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
							SET @addonID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.addonID#">;
							SET @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">;
							SET @childSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.childSetID#">;
							SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

							SELECT @orgID = orgID 
							FROM dbo.sites 
							WHERE siteID = @siteID;

							SELECT @subscriptionName = subscriptionName
							FROM dbo.sub_subscriptions
							WHERE subscriptionID = @subscriptionID;

							SELECT @setName = setName
							FROM dbo.sub_sets
							WHERE setID = @childSetID;

							select @feContentID = c.contentID, @feContentLanguageID = cl.languageID,
								@feContentIsHTML = c.isHTML, @feContentTitle = cl.contentTitle, @feContentDesc = cl.contentDesc
							from dbo.sub_addons ao
							inner join dbo.cms_content c on c.contentID = ao.frontEndContentID
							inner join dbo.cms_contentLanguages cl on cl.contentID = c.contentID
							where ao.addonID = @addonID;

							-- Insert DATATYPECODE row into audit log table
							INSERT INTO ##tmpAuditLogData ([rowCode], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
								[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select], [Front End Content])
							VALUES ('DATATYPECODE', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'BIT', 'BIT', 'CONTENTOBJ');

							-- Insert old values into audit log table
							INSERT INTO ##tmpAuditLogData ([rowCode], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
								[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select], [Front End Content])
							SELECT 'OLDVAL', ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
								ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice, feContent.rawContent
							FROM dbo.sub_addons ao
							CROSS APPLY dbo.fn_getContent(ao.frontEndContentID,@feContentLanguageID) AS feContent
							WHERE ao.addonID = @addonID;

							BEGIN TRAN;
								UPDATE dbo.sub_addons
								SET
									<cfif len(arguments.minAllowed) eq 0>
										minAllowed = NULL,
									<cfelse>
										minAllowed = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.minAllowed#">,
									</cfif>
									<cfif len(arguments.maxAllowed) eq 0>
										maxAllowed = NULL,
									<cfelse>
										maxAllowed = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.maxAllowed#">,
									</cfif>
									useAcctCodeInSet = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useAcctCodeInSet#">,
									useTermEndDateInSet = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useTermEndDateInSet#">,
									PCnum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.PCnum#">,
									PCPctOffEach = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.PCPctOffEach#">,
									frontEndAllowSelect = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.feChangeSelect#">,
									frontEndAddAdditional = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.feAddAdditional#">,
									frontEndAllowChangePrice = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.feChangePriceSelect#">
								WHERE addonID = @addonID
								AND subscriptionID = @subscriptionID
								AND childSetID = @childSetID;

								EXEC dbo.cms_updateContent @contentID=@feContentID, @languageID=@feContentLanguageID, @isHTML=@feContentIsHTML,
									@contentTitle=@feContentTitle, @contentDesc=@feContentDesc,
									@rawContent=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frontEndContent#">,
									@memberID = <cfqueryparam  value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)#" cfsqltype="cf_sql_integer" />;

								-- Insert new values into audit log table
								INSERT INTO ##tmpAuditLogData ([rowCode], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
									[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select], [Front End Content])
								SELECT 'NEWVAL', ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
									ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice, feContent.rawContent
								FROM dbo.sub_addons ao
								CROSS APPLY dbo.fn_getContent(ao.frontEndContentID,@feContentLanguageID) AS feContent
								WHERE ao.addonID = @addonID;

								-- Generate audit log message
								DECLARE @msg VARCHAR(MAX);
								EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

								IF ISNULL(@msg,'') <> '' BEGIN
									DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "ADDONID":'+CAST(@addonID AS VARCHAR(10))+', "CHILDSETID":'+CAST(@childSetID AS VARCHAR(10))+' }';
									SET @msgjson = STRING_ESCAPE('AddOn for Subscription ' + QUOTENAME(@subscriptionName) + ' and Subscription Set ' + QUOTENAME(@setName) + ' has been updated.','json')
										+ @crlf + 'The following changes have been made:' + @crlf + @msg;

									EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBADDON', @msgjson=@msgjson, 
										@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
								END
							COMMIT TRAN;

							IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
								DROP TABLE ##tmpAuditLogData;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>
	</cffunction>

	<cffunction name="removeSubscriptionAddon" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="addonID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrderNum">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

				DECLARE @siteID int, @orgID int, @addonID int, @subscriptionID int, @childSetID int, @subscriptionName varchar(100), 
					@setName VARCHAR(200), @msgjson varchar(max);
				
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				SET @addonID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.addonID#">;
				
				SELECT @subscriptionID = sa.subscriptionID, @childSetID = sa.childSetID, @setName = ss.setName, @subscriptionName = s.subscriptionName
				FROM dbo.sub_addons as sa
				INNER JOIN dbo.sub_sets as ss ON ss.setID = sa.childSetID
				INNER JOIN dbo.sub_subscriptions as s ON s.subscriptionID = sa.subscriptionID
				WHERE sa.addonID = @addonID;

				BEGIN TRAN;
					UPDATE sr
					SET sr.siteResourceStatusID = 3
					FROM dbo.cms_siteResources sr
					INNER JOIN dbo.cms_content cc on cc.siteResourceID = sr.siteResourceID
					INNER JOIN dbo.sub_addons ao on ao.frontEndContentID = cc.contentID
						AND ao.addonID = @addonID
					WHERE sr.siteID = @siteID
					AND cc.siteID = @siteID;

					DELETE FROM dbo.sub_addons
					WHERE addonID = @addonID;

					-- audit log
					DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "ADDONID":'+CAST(@addonID AS VARCHAR(10))+', "CHILDSETID":'+CAST(@childSetID AS VARCHAR(10))+' }';

					SET @msgjson = 'Add On associated with set ' + QUOTENAME(@setName) + ' under Subscription ' + QUOTENAME(@subscriptionName) + ' has been deleted.';
					SET @msgjson = STRING_ESCAPE(@msgjson,'json');

					EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBADDON', @msgjson=@msgjson, @subKeyMapJSON=@subKeyMapJSON,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="checkSetAsAddon" access="public" output="false" returntype="struct">
		<cfargument name="addonID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrderNum">
			select count(addonID) as addonCount
			from dbo.sub_addons ao
			where ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">
			and ao.childSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.setID#">
			<cfif arguments.addonID neq 0>
				and ao.addonID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.addonID#">
			</cfif>
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.addoncount = local.qryOrderNum.addonCount>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="chkSubAddonWithAddons" access="public" output="false" returntype="struct">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="setID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset local.data.success = true>
		<cfset local.data.setallowed = 0>

		<cfreturn local.data>
	</cffunction>


	<!--- Subscriptions --->

	<cffunction name="checkSubName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="subName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery name="local.qryCheck" datasource="#application.dsn.membercentral.dsn#">
			select s.subscriptionID
			from dbo.sub_subscriptions s
			inner join dbo.sub_types t on t.typeID = s.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			where s.subscriptionName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subName#">
			<cfif arguments.subID neq 0>
				and s.subscriptionID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
				and s.typeID in (select typeID from dbo.sub_subscriptions where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">)
			</cfif>
			and s.status = 'A'
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.subnameinuse = local.qryCheck.recordCount gt 0>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addSubscription" access="public" output="false" returntype="numeric">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="subName" type="string" required="true">
		<cfargument name="reportCode" type="string" required="true">
		<cfargument name="scheduleID" type="numeric" required="true">
		<cfargument name="rateTermDateFlag" type="string" required="true">
		<cfargument name="autoExpire" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="soldSep" type="numeric" required="true">
		<cfargument name="paymentOrder" type="numeric" required="true">
		<cfargument name="accountID" type="numeric" required="true">
		<cfargument name="allowRateGLOverride" type="numeric" required="true">
		<cfargument name="activationOptionCode" type="string" required="true">
		<cfargument name="alternateActivationOptionCode" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="frontEndCompletedContent" type="string" required="true">
		<cfargument name="frontEndParentSubContent" type="string" required="true">
		<cfargument name="emailTemplateID" type="numeric" required="false" default="-1">
		<cfargument name="renewEmailTemplateID" type="numeric" required="false" default="-1">
		<cfargument name="chkName" type="boolean" required="false" default="true">

		<cfset var local = structNew()>
		<cfset local.subID = 0>
		<cfset local.chkNameResult = true>

		<cfif arguments.chkName eq 'true'>
			<cfset local.chkSubName = checkSubName(mcproxy_siteID=arguments.siteID, subID=0, subName=arguments.subName)>
				<cfif (local.chkSubName.success neq 'true') OR (local.chkSubName.subnameinuse neq 'false')>
					<cfset local.chkNameResult = false>
				</cfif>
		</cfif>

		<cfif local.chkNameResult eq 'true'>
			<cftry>
				<cfstoredproc procedure="sub_createSubscription" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.subName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.reportCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateTermDateFlag#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.autoExpire#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.soldSep#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentOrder#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.accountID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.allowRateGLOverride#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.activationOptionCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.alternateActivationOptionCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.frontEndContent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.frontEndCompletedContent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.frontEndParentSubContent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.subID">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
				<cfset local.subID = 0>
			</cfcatch>
			</cftry>
		</cfif>

		<!--- -1, nothing was passed in, make no changes
					0, removed the template
					gt 0, ID to be saved --->
		<cfif local.subID gt 0 and arguments.emailTemplateID gt 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemoveEmailTemplate">
				update dbo.sub_subscriptions
				set emailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailTemplateID#">
				where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			</cfquery>
		</cfif>

		<cfif local.subID gt 0 and arguments.renewEmailTemplateID gt 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemoveEmailTemplate">
				update dbo.sub_subscriptions
				set renewEmailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.renewEmailTemplateID#">
				where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			</cfquery>
		</cfif>

		<cfreturn local.subID>
	</cffunction>

	<cffunction name="updateSubscription" access="public" output="false" returntype="boolean">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="subName" type="string" required="true">
		<cfargument name="reportCode" type="string" required="true">
		<cfargument name="subUID" type="string" required="true">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="scheduleID" type="numeric" required="true">
		<cfargument name="rateTermDateFlag" type="string" required="true">
		<cfargument name="autoExpire" type="numeric" required="true">
		<cfargument name="soldSep" type="numeric" required="true">
		<cfargument name="accountCode" type="numeric" required="true">
		<cfargument name="allowRateGLOverride" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="paymentOrder" type="numeric" required="true">
		<cfargument name="activationOptionCode" type="string" required="true">
		<cfargument name="alternateActivationOptionCode" type="string" required="true">
		<cfargument name="frontEndContent" type="string" required="true">
		<cfargument name="frontEndCompletedContent" type="string" required="true">
		<cfargument name="frontEndParentSubContent" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="emailTemplateID" type="numeric" required="false" default="-1">
		<cfargument name="renewEmailTemplateID" type="numeric" required="false" default="-1">

		<cfset var local = structNew()>
		<cfset local.chkSubName = checkSubName(mcproxy_siteID=arguments.siteID, subID=arguments.subID, subName=arguments.subName)>

		<cfif local.chkSubName.success eq 'true' AND local.chkSubName.subnameinuse eq 'false'>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tblMCQSubCond') IS NOT NULL 
							DROP TABLE ##tblMCQSubCond;
						CREATE TABLE ##tblMCQSubCond (conditionID int);

						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData; 
						CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Subscription Name] varchar(max), [Report Code] varchar(max), 
							[uid] varchar(max), [Subscription Type] varchar(max), [Rate Schedule] varchar(max), [Term Dates] varchar(max), 
							[Auto Expire Subscribers] varchar(max), [Sold Seperately] varchar(max), [Status] varchar(max), [Payment Order] varchar(max), 
							[Subscription GL Account] varchar(max), [Allow Rate GL Account Override] varchar(max), [Email Template] varchar(max), 
							[Renew Email Template] varchar(max), [Activation Option] varchar(max), [Alternate Activation Option] varchar(max), 
							[Front End Content] varchar(max), [Front End Completed Content] varchar(max), [Front End Parent Subscription Content] varchar(max));


						declare @siteID int, @orgID int, @typeID int, @oldTypeID int, @activationOptionID int, 
							@alternateActivationOptionID int, @feContentID int, @feContentLanguageID int, @feContentIsHTML bit, 
							@feContentTitle varchar(200), @feContentDesc varchar(400), @feCompletedContentID int, 
							@feCompletedContentLanguageID int, @feCompletedContentIsHTML bit, 
							@feCompletedContentTitle varchar(200), @feCompletedContentDesc varchar(400), 
							@feParentSubContentID int, @feParentSubContentLanguageID int, @feParentSubContentIsHTML bit, 
							@feParentSubContentTitle varchar(200), @feParentSubContentDesc varchar(400), @subID int,
							@subName varchar(200), @scheduleID int, @oldScheduleID int, @emailTemplateID int, 
							@renewEmailTemplateID int, @suppressJoin bit=0, @suppressRenew bit=0, @orgMemberID int, 
							@typeName varchar(100), @oldSubName varchar(300), @oldTypeName varchar(100), @msg varchar(max), 
							@msgjson varchaR(max), @recordByMemberID INT, @crlf varchar(10);

						set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
						set @subID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">;
						set @scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">;
						set @subName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subName#">;
						set @orgMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)#">;
						set @recordByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

						set @emailTemplateID = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailTemplateID#">,0);
						set @renewEmailTemplateID = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.renewEmailTemplateID#">,0);
						SET @crlf = char(13) + char(10);

						select @suppressJoin = case
								-- if we're clearing the template, no need to suppress
								when @emailTemplateID is null then 0
								-- if the existing saved value is null, and we're setting it to value, then supress
								when emailTemplateID is null then 1
							end,
							@suppressRenew = case
								-- if we're clearing the template, no need to suppress
								when @renewEmailTemplateID is null then 0
								-- if the existing saved value is null, and we're setting it to value, then supress
								when renewEmailTemplateID is null then 1
							end,
							@oldTypeID = typeID,
							@oldScheduleID = scheduleID
						from sub_subscriptions
						where subscriptionID = @subID

						select @orgID = orgID from dbo.sites where siteID = @siteID;

						select @typeID = typeID
						from dbo.sub_types
						where typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
						and siteID = @siteID;

						IF @typeID is NULL
							RAISERROR('Subscription Type not found.',16,1);

						select @activationOptionID = subActivationID
						from dbo.sub_activationOptions
						where subActivationCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.activationOptionCode#">;

						select @alternateActivationOptionID = subActivationID
						from dbo.sub_activationOptions
						where subActivationCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.alternateActivationOptionCode#">;

						select @feContentID = c.contentID, @feContentLanguageID = cl.languageID,
							@feContentIsHTML = c.isHTML, @feContentTitle = cl.contentTitle, @feContentDesc = cl.contentDesc
						from dbo.sub_subscriptions s
						inner join dbo.cms_content c on c.contentID = s.frontEndContentID
						inner join dbo.cms_contentLanguages cl on cl.contentID = c.contentID
						where s.subscriptionID = @subID;

						select @feCompletedContentID = c.contentID, @feCompletedContentLanguageID = cl.languageID,
							@feCompletedContentIsHTML = c.isHTML, @feCompletedContentTitle = cl.contentTitle, @feCompletedContentDesc = cl.contentDesc
						from dbo.sub_subscriptions s
						inner join dbo.cms_content c
							on c.contentID = s.frontEndCompletedContentID
						inner join dbo.cms_contentLanguages cl
							on cl.contentID = c.contentID
						where s.subscriptionID = @subID;

						select @feParentSubContentID = c.contentID, @feParentSubContentLanguageID = cl.languageID,
							@feParentSubContentIsHTML = c.isHTML, @feParentSubContentTitle = cl.contentTitle, 
							@feParentSubContentDesc = cl.contentDesc
						from dbo.sub_subscriptions s
						inner join dbo.cms_content c
							on c.contentID = s.frontEndParentSubContentID
						inner join dbo.cms_contentLanguages cl
							on cl.contentID = c.contentID
						where s.subscriptionID = @subID;

						-- for sub type or rate schedule change repopulate sub split cache
						IF @typeID <> @oldTypeID
							INSERT INTO ##tblMCQSubCond (conditionID)
							SELECT DISTINCT c.conditionID
							FROM dbo.ams_virtualGroupConditions AS c
							INNER JOIN dbo.cache_members_conditions_subTypes AS cs ON cs.orgID = @orgID
								AND cs.conditionID = c.conditionID
								AND cs.typeID IN (@oldTypeID,@typeID)
							WHERE c.orgID = @orgID
							AND c.fieldCode ='sub_entry';
						
						IF @scheduleID <> @oldScheduleID
							INSERT INTO ##tblMCQSubCond (conditionID)
							SELECT DISTINCT c.conditionID
							FROM dbo.ams_virtualGroupConditions AS c
							INNER JOIN dbo.cache_members_conditions_subSubscriptions AS cs ON cs.orgID = @orgID
								AND cs.conditionID = c.conditionID
								AND cs.subscriptionID = @subID
							LEFT OUTER JOIN ##tblMCQSubCond AS tmp ON tmp.conditionID = c.conditionID
							WHERE c.orgID = @orgID
							AND c.fieldCode ='sub_entry'
							AND tmp.conditionID IS NULL;

						INSERT INTO ##tmpAuditLogData ([rowCode], [Subscription Name], [Report Code], [uid], [Subscription Type], [Rate Schedule], [Term Dates], [Auto Expire Subscribers], 
							[Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override], [Email Template], [Renew Email Template], 
							[Activation Option], [Alternate Activation Option], [Front End Content], [Front End Completed Content], [Front End Parent Subscription Content])
						VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 
							'STRING', 'STRING', 'STRING', 'CONTENTOBJ', 'CONTENTOBJ', 'CONTENTOBJ');

						INSERT INTO ##tmpAuditLogData ([rowCode], [Subscription Name], [Report Code], [uid], [Subscription Type], [Rate Schedule], [Term Dates], [Auto Expire Subscribers], 
							[Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override], [Email Template], [Renew Email Template], 
							[Activation Option], [Alternate Activation Option], [Front End Content], [Front End Completed Content], [Front End Parent Subscription Content])
						SELECT 'OLDVAL', ss.subscriptionName, ss.reportCode, ss.uid, t.typeName, srs.scheduleName, 
							CASE 
								WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
								WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
								ELSE 'Calculate term end date for term length (term start date is current day)'
							END,
							CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END, CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END, 
							CASE WHEN ss.status = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
							glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END, etet1.templateName, etet2.templateName, 
							sao.subActivationName, alt_sao.subActivationName, feContent.rawContent, feCompletedContent.rawContent, feParentContent.rawContent
						FROM dbo.sub_subscriptions as ss
						INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
						INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
						INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
						INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
						LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
						LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID
						LEFT OUTER JOIN dbo.et_emailTemplates as etet1 ON etet1.templateID = ss.emailTemplateID
						LEFT OUTER JOIN dbo.et_emailTemplates as etet2 ON etet2.templateID = ss.renewEmailTemplateID
						CROSS APPLY dbo.fn_getContent(ss.frontEndContentID,1) AS feContent 
						CROSS APPLY dbo.fn_getContent(ss.frontEndCompletedContentID,1) AS feCompletedContent
						CROSS APPLY dbo.fn_getContent(ss.frontEndParentSubContentID,1) AS feParentContent
						WHERE ss.subscriptionID = @subID;

						SELECT @oldSubName = [Subscription Name], @oldTypeName = [Subscription Type]
						FROM ##tmpAuditLogData
						WHERE rowCode = 'OLDVAL'

						BEGIN TRAN;
							update dbo.sub_subscriptions
							set subscriptionName = @subName,
								reportCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.reportCode#">,
								<cfif len(arguments.subUID) AND application.objUser.isSuperUser(cfcuser=session.cfcuser) >
									uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subUID#">,
								</cfif>
								typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">,
								scheduleID = @scheduleID,
								rateTermDateFlag = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateTermDateFlag#">,
								autoExpire = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.autoExpire#">,
								soldSeparately = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.soldSep#">,
								status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">,
								paymentOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paymentOrder#">,
								GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.accountCode#">,
								allowRateGLAccountOverride = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.allowRateGLOverride#">,
								emailTemplateID = @emailTemplateID,
								renewEmailTemplateID = @renewEmailTemplateID,
								subActivationID = @activationOptionID,
								subAlternateActivationID = @alternateActivationOptionID
							where subscriptionID = @subID;

							exec dbo.cms_updateContent @contentID=@feContentID, @languageID=@feContentLanguageID,
								@isHTML=@feContentIsHTML, @contentTitle=@feContentTitle, @contentDesc=@feContentDesc,
								@rawContent=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frontEndContent#">,
								@memberID=@orgMemberID;

							exec dbo.cms_updateContent @contentID=@feCompletedContentID, @languageID=@feCompletedContentLanguageID,
								@isHTML=@feCompletedContentIsHTML, @contentTitle=@feCompletedContentTitle, @contentDesc=@feCompletedContentDesc,
								@rawContent=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frontEndCompletedContent#">,
								@memberID=@orgMemberID;

							exec dbo.cms_updateContent @contentID=@feParentSubContentID, @languageID=@feParentSubContentLanguageID,
								@isHTML=@feParentSubContentIsHTML, @contentTitle=@feParentSubContentTitle, @contentDesc=@feParentSubContentDesc,
								@rawContent=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frontEndParentSubContent#">,
								@memberID=@orgMemberID;

							-- audit log
							INSERT INTO ##tmpAuditLogData ([rowCode], [Subscription Name], [Report Code], [uid], [Subscription Type], [Rate Schedule], [Term Dates], [Auto Expire Subscribers], 
								[Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override], [Email Template], [Renew Email Template], 
								[Activation Option], [Alternate Activation Option], [Front End Content], [Front End Completed Content], [Front End Parent Subscription Content])
							SELECT 'NEWVAL', ss.subscriptionName, ss.reportCode, ss.uid, t.typeName, srs.scheduleName, 
								CASE 
									WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
									WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
									ELSE 'Calculate term end date for term length (term start date is current day)'
								END,
								CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END, CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END, 
								CASE WHEN ss.status = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
								glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END, etet1.templateName, etet2.templateName, 
								sao.subActivationName, alt_sao.subActivationName, feContent.rawContent, feCompletedContent.rawContent, feParentContent.rawContent
							FROM dbo.sub_subscriptions as ss
							INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
							INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
							INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
							INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
							LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
							LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID
							LEFT OUTER JOIN dbo.et_emailTemplates as etet1 ON etet1.templateID = ss.emailTemplateID
							LEFT OUTER JOIN dbo.et_emailTemplates as etet2 ON etet2.templateID = ss.renewEmailTemplateID
							CROSS APPLY dbo.fn_getContent(ss.frontEndContentID,1) AS feContent 
							CROSS APPLY dbo.fn_getContent(ss.frontEndCompletedContentID,1) AS feCompletedContent
							CROSS APPLY dbo.fn_getContent(ss.frontEndParentSubContentID,1) AS feParentContent
							WHERE ss.subscriptionID = @subID;

							EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;
		
							IF ISNULL(@msg,'') <> '' BEGIN
								DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';
								SET @msgjson = STRING_ESCAPE('Subscription ' + QUOTENAME(@oldSubName) + ' has been updated under the Subscription Type ' + QUOTENAME(@oldTypeName) + '.','json') + @crlf + 'The following changes have been made:' + @crlf + @msg;

								EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
									@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordByMemberID;
							END
						COMMIT TRAN;

						IF EXISTS (SELECT 1 FROM ##tblMCQSubCond)
							EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

						IF (@suppressJoin=1 OR @suppressRenew=1)
							exec dbo.sub_suppressExistingQualifiedSubscribers 
								@siteID=@siteID, 
								@subscriptionID=@subID, 
								@actorMemberID= <cfqueryparam value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)#" cfsqltype="cf_sql_integer">,
								@suppressJoin=@suppressJoin, 
								@suppressRenew=@suppressRenew;

						-- process conditions and update verbose
						UPDATE dbo.ams_virtualGroupConditions
						SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
						WHERE orgID = @orgID
						and left(fieldCode,4) = 'sub_'
						and conditionID in (
							select c.conditionID
							from dbo.ams_virtualGroupConditions as c
							inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
								and cv.conditionValue = cast(@subID as varchar(10))
							inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
								and k.conditionKey = 'subSubscription'
							where c.orgID = @orgID
						);

						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
						CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

						INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
						SELECT distinct c.orgID, null, c.conditionID
						from dbo.ams_virtualGroupConditions as c
						inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
							and cv.conditionValue = cast(@subID as varchar(10))
						inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
							and k.conditionKey = 'subSubscription'
						where c.orgID = @orgID;

						EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
						IF OBJECT_ID('tempdb..##tblMCQSubCond') IS NOT NULL 
							DROP TABLE ##tblMCQSubCond;
						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.retCode = true>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.retCode = false>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.retCode = false>
		</cfif>

		<cfreturn local.retCode>
	</cffunction>

	<cffunction name="checkMemberSub" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":false, "subscribed":false }>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberSubs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int, @orgID int, @memberID int, @activeMemberID int, @subscriptionID int, @nowDate datetime = getdate();
				DECLARE @tblMembers TABLE (memberID int PRIMARY KEY);

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">; 
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
				SET @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">;
				SELECT @activeMemberID = activeMemberID FROM dbo.ams_members WHERE orgID = @orgID AND memberID = @memberID;

				INSERT INTO @tblMembers (memberID)
				SELECT distinct memberID
				FROM dbo.ams_members
				WHERE orgID = @orgID
				AND activeMemberID = @activeMemberID;

				select subCount = count(*)
				from dbo.sub_subscribers as s
				inner join @tblMembers as m on m.memberID = s.memberID
				inner join dbo.sub_statuses as st on st.statusID = s.statusID 
					and st.excludeRenewal = 1
				inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID 
					and subs.subscriptionID = @subscriptionID
					and subs.subscriptionID = s.subscriptionID
				inner join dbo.sub_types as t on t.siteID = @siteID
					and t.typeID = subs.typeID
				where s.orgID = @orgID
				and s.subEndDate >= @nowDate;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.strReturn.success = true>
			<cfset local.strReturn.subscribed = val(local.qryMemberSubs.subCount) gt 0>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="removeMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="actorStatsSessionID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="AROption" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc procedure="sub_removeSubscriber" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorStatsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
				<cfprocresult name="local.qryMemberSubs">
			</cfstoredproc>

			<cfif local.qryMemberSubs.recordcount>
				<cfset local.arrMessages = ArrayNew(1)>
				<cfloop query="local.qryMemberSubs">
					<cfset arrayAppend(local.arrMessages,"#local.qryMemberSubs.thePathExpanded# was removed.")>
				</cfloop>
				<cfset local.mainMessage = local.arrMessages[1]>
				<cfset arrayDeleteAt(local.arrMessages,1)>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrgID">
					select orgID
					from dbo.sites
					where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfquery>
				<cfset CreateObject('component','model.system.platform.history').addSubUpdateHistory(orgID=local.qryOrgID.orgID,
					actorMemberID=arguments.actorMemberID, receiverMemberID=arguments.memberID, mainMessage=local.mainMessage, 
					messages=local.arrMessages)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cleanupInvoicesSubscription" access="public" output="false" returntype="struct">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="actorStatsSessionID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="AROption" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc procedure="sub_cleanupInvoicesSubscriber" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorStatsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
				<cfprocresult name="local.qryMemberSubs">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>			

		<cfreturn local.data>
	</cffunction>

	<cffunction name="readdMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberSubs">
			select rms.subscriberID, rms.subscriptionID, rms.status, rms.thePathExpanded, sParent.subEndDate, sParent.graceEndDate
			from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">) rms
			left outer join dbo.sub_subscribers sParent on sParent.subscriberID = rms.parentSubscriberID
			where rms.status = 'D'
		</cfquery>

		<cfset local.historyMessages = ArrayNew(1)>
		<cfset local.topSubName = "">

		<cfloop query="local.qryMemberSubs">
			<!--- remove member from active groups --->
			<!--- remove member from subscribers table --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemove">
				set nocount on;
				declare @tempResult int;

				EXEC dbo.sub_updateSubscriberStatus
					@subscriberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMemberSubs.subscriberID#">,
					@newStatusCode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">,
					@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">,
					@bypassQueue=0,
					@result=@tempResult OUTPUT;

				set nocount off;
			</cfquery>

			<cfif Len(local.topSubName) eq 0>
				<cfset local.topSubName = local.qryMemberSubs.thePathExpanded>
			<cfelse>
				<cfset ArrayAppend(local.historyMessages, "#local.qryMemberSubs.thePathExpanded# was re-added.")>
			</cfif>
		</cfloop>

		<cfif Len(local.topSubName) neq 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrgID">
				select orgID
				from dbo.sites
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			</cfquery>
			<cfset CreateObject('component','model.system.platform.history').addSubUpdateHistory(orgID=local.qryOrgID.orgID, 
				actorMemberID=arguments.actorMemberID, receiverMemberID=arguments.memberID, mainMessage="#local.topSubName# was re-added", 
				messages=local.historyMessages)>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="markBilledMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasSubscriptionTypeRights(siteID=arguments.mcproxy_siteID, subscriberID=arguments.subscriberID, functionName="MarkBilled")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions">
				select subscriberID, subscriptionID, memberID, memberName, thePathExpanded, subscriptionName
				from dbo.fn_getRecursiveSubscriptionsByID(
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">)
				where status <> 'D'
				order by thePath
			</cfquery>

			<cfset local.arrOffers = ArrayNew(1)>

			<cfset local.updateHistory = false>
			<cfloop query="local.qrySubscriptions">
				<cfset local.updateHistory = true>

				<cfstoredproc procedure="sub_updateSubscriberStatus" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscriptions.subscriberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="O">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.tmpResult">
				</cfstoredproc>

				<cfset local.errStruct = { memberID = local.qrySubscriptions.memberID,
					memberName = local.qrySubscriptions.memberName,
					errType = "Marked Billed",
					errMessage = "#local.qrySubscriptions.subscriptionName#."
					}>
				<cfset ArrayAppend(local.arrOffers, local.errStruct)>
			</cfloop>

			<cfif local.updateHistory>
				<cfset CreateObject('component', 'model.system.platform.history').addSubOfferUpdateHistory(orgID=arguments.mcproxy_orgID,
					actorMemberID=arguments.actorMemberID, mainMessage="Billed Results:<br>1 subscriber processed.", offers=local.arrOffers)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="inactivateMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasSubscriptionTypeRights(siteID=arguments.mcproxy_siteID, subscriberID=arguments.subscriberID, functionName="ManageActivation")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberSubs">
				select subscriberID, subscriptionID, thePathExpanded
				from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">)
				where status <> 'D'
			</cfquery>

			<cfset local.historyMessages = ArrayNew(1)>
			<cfset local.topSubName = "">

			<cfloop query="local.qryMemberSubs">
				<!--- remove member from active groups --->
				<!--- remove member from subscribers table --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemove">
					set nocount on;
					declare @tempResult int;

					EXEC dbo.sub_updateSubscriberStatus
						@subscriberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMemberSubs.subscriberID#">,
						@newStatusCode='I',
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">,
						@bypassQueue=0, @result=@tempResult OUTPUT;

					set nocount off;
				</cfquery>

				<cfif Len(local.topSubName) eq 0>
					<cfset local.topSubName = local.qryMemberSubs.thePathExpanded>
				<cfelse>
					<cfset ArrayAppend(local.historyMessages, "#local.qryMemberSubs.thePathExpanded# was inactivated.")>
				</cfif>
			</cfloop>

			<cfif Len(local.topSubName) neq 0>
				<cfset CreateObject('component','model.system.platform.history').addSubUpdateHistory(orgID=arguments.mcproxy_orgID, 
					actorMemberID=arguments.actorMemberID, receiverMemberID=arguments.memberID, mainMessage="#local.topSubName# was inactivated",
					messages=local.historyMessages)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="activateMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasSubscriptionTypeRights(siteID=arguments.mcproxy_siteID, subscriberID=arguments.subscriberID, functionName="ManageActivation")>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberSubs">
				select subscriberID, subscriptionID, thePathExpanded
				from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">)
				where status <> 'D'
			</cfquery>

			<cfset local.historyMessages = ArrayNew(1)>
			<cfset local.topSubName = "">

			<cfloop query="local.qryMemberSubs">
				<!--- remove member from active groups --->
				<!--- remove member from subscribers table --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemove">
					set nocount on;
					declare @tempResult int;

					EXEC dbo.sub_updateSubscriberStatus
						@subscriberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMemberSubs.subscriberID#">,
						@newStatusCode='A',
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">,
						@bypassQueue=0,
						@result=@tempResult OUTPUT;

					set nocount off;
				</cfquery>

				<cfif Len(local.topSubName) eq 0>
					<cfset local.topSubName = local.qryMemberSubs.thePathExpanded>
				<cfelse>
					<cfset ArrayAppend(local.historyMessages, "#local.qryMemberSubs.thePathExpanded# was activated.")>
				</cfif>
			</cfloop>

			<cfif Len(local.topSubName) neq 0>
				<cfset CreateObject('component','model.system.platform.history').addSubUpdateHistory(orgID=arguments.mcproxy_orgID,
					actorMemberID=arguments.actorMemberID, receiverMemberID=arguments.memberID, mainMessage="#local.topSubName# was activated",
					messages=local.historyMessages)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="expireMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="actorStatsSessionID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="AROption" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc procedure="sub_expireSubscriber" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.actorStatsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
				<cfprocresult name="local.qryMemberSubs">
			</cfstoredproc>

			<cfif local.qryMemberSubs.recordcount>
				<cfset local.arrMessages = ArrayNew(1)>
				<cfloop query="local.qryMemberSubs">
					<cfset arrayAppend(local.arrMessages,"#local.qryMemberSubs.thePathExpanded# was expired.")>
				</cfloop>
				<cfset local.mainMessage = local.arrMessages[1]>
				<cfset arrayDeleteAt(local.arrMessages,1)>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrgID">
					select orgID
					from dbo.sites
					where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfquery>
				<cfset CreateObject('component','model.system.platform.history').addSubUpdateHistory(orgID=local.qryOrgID.orgID,
					actorMemberID=arguments.actorMemberID, receiverMemberID=arguments.memberID, mainMessage=local.mainMessage, 
					messages=local.arrMessages)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>	

		<cfreturn local.data>
	</cffunction>

	<cffunction name="overrideActivationMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfif not hasSubscriptionTypeRights(siteID=arguments.mcproxy_siteID, subscriberID=arguments.subscriberID, functionName="ManageActivation")>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_overrideActivationMemberSubscription">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.subscriberID#">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="in" cfsqltype="cf_sql_integer" value="#arguments.actorMemberID#">
				<cfprocparam type="in" cfsqltype="cf_sql_bit" value="0">
				<cfprocparam type="out" cfsqltype="cf_sql_bit" variable="local.statusUpdated">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.statusUpdated = 0>
		</cfcatch>
		</cftry>

		<cfset local.data.success = local.statusUpdated is 1>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasSubscriptionTypeRights" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="functionName" type="string" required="true">

		<cfset var qrySubType = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubType">
			select t.siteResourceID
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on t.typeID = sc.typeID
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">;
		</cfquery>

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=val(qrySubType.siteResourceID), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn structKeyExists(tmpRights, arguments.functionName) AND tmpRights[arguments.functionName] eq 1>
	</cffunction>

	<cffunction name="updateOfferExpireDate" access="public" output="false" returntype="boolean">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="offerExpireDate" type="date" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.offerExpireDate = DateFormat(arguments.offerExpireDate, "m/d/yyyy") & " 23:59:59.997">
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateDate" result="local.updateDateResults">
				update ss set
					offerRescindDate = <cfqueryparam cfsqltype="cf_sql_date" value="#local.offerExpireDate#">
				from dbo.sub_types t
				inner join dbo.sub_subscriptions subs 
					on t.typeID = subs.typeID
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				inner join dbo.sub_subscribers ss
					on ss.subscriptionID = subs.subscriptionID
					and ss.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">
				inner join dbo.sub_statuses st
					on st.statusID = ss.statusID
					and st.statuscode in ('R','O')
			</cfquery>
			<cfset local.success = (local.updateDateResults.recordCount eq 1) />
		<cfcatch type="Any">
			<cfset local.success = false />
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=arguments,customMessage="Error in updateOfferExpireDate function")>
		</cfcatch>
		</cftry>
		<cfreturn local.success>
	</cffunction>


	<cffunction name="getSubscriptionsForSubType" access="public" output="false" returntype="string">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="soldSeparately" type="boolean" required="false">

		<cfset var qrySubscriptions = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriptions">
				select subscriptionID, subscriptionName, 2 as sortOrder
				from dbo.sub_subscriptions
				where typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
				and [status] = 'A'
				<cfif arguments.keyExists('soldSeparately')>
					and soldSeparately = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.soldSeparately#">
				</cfif>
					UNION
				SELECT '0', 'Any Subscription', 1 as sortOrder
				order by sortOrder, subscriptionName
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qrySubscriptions = QueryNew("subscriptionID,subscriptionName","integer,varchar")>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(qrySubscriptions)>
	</cffunction>

	<cffunction name="getSubRatesForSub" access="public" output="false" returntype="string">
		<cfargument name="subID" type="numeric" required="true">
		<cfset var qrySubRates = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubRates">
				select rateID, rateName, isRenewalRate
				from (
				select r.rateID, r.rateName, r.status, r.isRenewalRate, srs.siteResourceStatusDesc, count (s.subscriberID) as subCount
				from dbo.sub_rates r
				inner join dbo.sub_subscriptions subs
					on subs.scheduleID = r.scheduleID
					and subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
				inner join dbo.sub_rateFrequencies rf
					on rf.rateID = r.rateID
				inner join cms_siteResources sr on sr.siteResourceID = r.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses srs
					on sr.siteResourceStatusID = srs.siteResourceStatusID
				left outer join dbo.sub_subscribers s
					on s.rfid = rf.rfid
				group by r.rateID, r.rateName, r.status, r.isRenewalRate, srs.siteResourceStatusDesc
				) tmp
				where (siteResourceStatusDesc = 'Active') OR
					(siteResourceStatusDesc <> 'Active' AND subCount > 0)
				order by rateName
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qrySubRates = QueryNew("rateID,rateName,isRenewalRate","integer,varchar,bit")>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(qrySubRates)>
	</cffunction>

	<cffunction name="removeSubscription" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="subID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sub_deleteSubscription" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
				<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
				<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.subID#">
				<cfprocparam cfsqltype="cf_sql_bit" value="0">
				<cfprocparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryChkSub">
				select subscriptionID
				from dbo.sub_subscriptions
				where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
				and status <> 'D'
			</cfquery>

			<cfset local.data.success = local.qryChkSub.recordCount eq 0>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getOfferMemberData" access="public" output="false" returntype="struct">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="emailTemplateID" type="numeric" required="true">
		<cfargument name="contentVersionID" type="numeric" required="false" default="0">
		<cfargument name="overrideEmail" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		<cfset local.retStruct.subscriberID = arguments.subscriberID>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTemplateData">
			select et.subjectLine, et.emailFromName, et.emailFrom, cv.rawContent
			from dbo.et_emailTemplates et
			inner join dbo.cms_contentLanguages as cl on cl.contentID = et.contentID and cl.languageID = 1
			inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID
			<cfif arguments.contentVersionID>
				and cv.contentVersionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contentVersionID#">
			<cfelse>
				and cv.isActive = 1
			</cfif>
			where et.templateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.emailTemplateID#">
		</cfquery>
		<cfset local.retStruct.emailFromName = local.qryTemplateData.emailFromName>
		<cfset local.retStruct.emailFrom = local.qryTemplateData.emailFrom>
		<cfset local.retStruct.subjectLine = local.qryTemplateData.subjectLine>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberInfo">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select sites.orgID, sites.sitecode, t.siteID, mActive.memberID, mActive.firstName, mActive.lastName, t.siteResourceID
			from dbo.sub_subscribers as s
			inner join dbo.ams_members as m on m.memberID = s.memberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types as t on t.typeID = sub.typeID
			inner join dbo.sites sites on sites.siteID = t.siteID
			where s.subscriberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.subscriberID#">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.retStruct.siteID = local.qrySubscriberInfo.siteID>
		<cfset local.retStruct.siteResourceID = local.qrySubscriberInfo.siteResourceID>
		<cfset local.retStruct.memberID = local.qrySubscriberInfo.memberID>
		<cfset local.retStruct.memberName = "#local.qrySubscriberInfo.firstName# #local.qrySubscriberInfo.lastName#">

		<cfquery name="local.qryMemberEmail" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#local.qrySubscriberInfo.orgID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT TOP 1 me.email, metag.emailTypeID
			FROM dbo.ams_memberEmails AS me
			INNER JOIN dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
				and metag.memberID = me.memberID 
				and metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID 
				and metagt.emailTagTypeID = metag.emailTagTypeID 
				and metagt.emailTagType = 'Primary'
			WHERE me.orgID = @orgID
			AND me.memberID = <cfqueryparam value="#local.qrySubscriberInfo.memberID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.retStruct.emailTypeID = local.qryMemberEmail.emailTypeID>

		<cfif len(arguments.overrideEmail)>
			<cfset local.retStruct.toEmail = arguments.overrideEmail>
		<cfelse>
			<cfset local.retStruct.toEmail = local.qryMemberEmail.email>
		</cfif>

		<cfset local.parseContent = parseContentWithMergeCodes(content=local.qryTemplateData.rawContent, memberID=local.qrySubscriberInfo.memberID, subscriberID=arguments.subscriberID)>
		<cfset local.retStruct.templateDisp = local.parseContent.content>
		<cfset local.retStruct.templateDispWrap = application.objEmailWrapper.wrapMessage(emailTitle=local.retStruct.subjectLine, emailContent=local.parseContent.content, sitecode=local.qrySubscriberInfo.sitecode)>
		<cfset local.retStruct.qrySubscriptions = local.parseContent.qrySubscriptions>
		<cfset local.retStruct.mergeFields = local.parseContent.strMergeFields>

		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="parseContentWithMergeCodes" access="public" output="no" returntype="struct">
		<cfargument name="content" type="string" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="subscriberID" type="numeric" required="yes">

		<cfset var local = StructNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberInfo">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select sites.siteCode, CONVERT(VARCHAR(10), subStartDate, 101) as startDate, CONVERT(VARCHAR(10), subEndDate, 101) as endDate, CONVERT(VARCHAR(10), offerRescindDate, 101) as offerExpireDate
			from dbo.sub_subscribers as s
			inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types as t on t.typeID = sub.typeID
			inner join dbo.sites on sites.siteID = t.siteID
			where subscriberID = <cfqueryparam value="#arguments.subscriberID#" cfsqltype="cf_sql_integer">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select grs.subscriberID, grs.memberID, grs.memberName, grs.subscriptionID, grs.typeName, grs.subscriptionName,
				case when s.PCFree = 1 then 0.00 else grs.subPrice end as subPrice,
				CONVERT(VARCHAR(10), grs.subStartDate, 101) as startDate, CONVERT(VARCHAR(10), grs.subEndDate, 101) as endDate, r.rateName
			from dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam value="#application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).siteID#" cfsqltype="cf_sql_integer">, <cfqueryparam value="#arguments.subscriberID#" cfsqltype="cf_sql_integer">) as grs
			inner join dbo.sub_subscribers as s on s.subscriberID = grs.subscriberID
			inner join dbo.sub_rateFrequencies as rf on rf.RFID = grs.RFID
			inner join dbo.sub_rates as r on r.rateID = rf.rateID
			where grs.status <> 'D'
			order by grs.thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qrySubscriptions.recordCount>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRenewLink">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select isnull(directLinkCode,'') as directLinkCode
				from dbo.sub_subscribers
				where subscriberID = <cfqueryparam value="#arguments.subscriberID#" cfsqltype="cf_sql_integer">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif len(local.qryRenewLink.directLinkCode)>
				<cfset local.renewLink = "#application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).mainhostName#/renewsub/#local.qryRenewLink.directLinkCode#">
				<cfset local.renewLinkCode = local.qryRenewLink.directLinkCode>
			<cfelse>
				<cfset local.renewLink = "">
				<cfset local.renewLinkCode = "">
			</cfif>
		<cfelse>
			<cfset local.renewLink = "">
			<cfset local.renewLinkCode = "">
		</cfif>

		<cfset local.subTotal = 0>
		<cfquery dbtype="query" name="local.qryTotalSubPrice">
			select sum(subPrice) as totalSubPrice
			from [local].qrySubscriptions
		</cfquery>
		<cfif len(local.qryTotalSubPrice.totalSubPrice)>
			<cfset local.subTotal = local.qryTotalSubPrice.totalSubPrice>
		</cfif>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>

		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).orgID, 
			memberID=local.memberInfo.memberID, content=arguments.content)>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
							 			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix, prefix=local.memberInfo.Prefix, 
							 			professionalSuffix=local.memberInfo.professionalSuffix, membernumber=local.memberInfo.membernumber, orgcode=local.memberInfo.orgcode,
							 			siteID=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).siteID,
							 			hostname=local.thisHostname, useRemoteLogin=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).useRemoteLogin }>
		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>	
		</cfloop>
		 			
		<cfset local.tempSubscriptionData = { siteID=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).siteID,
											  mainhostName=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).mainhostName,
											  siteName=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).siteName,
								 			  actorMemberID=local.memberInfo.memberid,
								 			  memberID=local.memberInfo.memberid,
								 			  subStartDate=local.qrySubscriberInfo.startDate,
								 			  subEndDate=local.qrySubscriberInfo.endDate,
								 			  subOfferExpireDate=local.qrySubscriberInfo.offerExpireDate,
								 			  subTotal=local.subTotal,
								 			  subRenewURL=local.renewLink,
								 			  subRenewLinkCode=local.renewLinkCode,
								 			  subTableQuery=local.qrySubscriptions }>

		<cfset local.strArgs = { content=arguments.content, memberdata=local.tempMemberData, subscriptiondata=local.tempSubscriptionData, orgcode=application.objSiteInfo.getSiteInfo(local.qrySubscriberInfo.sitecode).orgcode, sitecode=local.qrySubscriberInfo.sitecode }>
		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset structInsert(local.strMergedContent.strMergeFields, "memberid", local.memberInfo.memberid, true)>
		<!--- ensure firstname and lastname are there even if not needed by merge codes --->
		<cfset structInsert(local.strMergedContent.strMergeFields, "firstname", local.memberInfo.FirstName, true)>
		<cfset structInsert(local.strMergedContent.strMergeFields, "lastname", local.memberInfo.LastName, true)>

		<cfset local.strReturn = { content=local.strMergedContent.content, qrySubscriptions=local.qrySubscriptions, strMergeFields=local.strMergedContent.strMergeFields }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="sendTestEmail" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="emailTemplateID" type="numeric" required="true">
		<cfargument name="testSubscriberID" type="numeric" required="true">
		<cfargument name="testEmail" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		<cfset local.retStruct.success = true>

		<cfif Len(session.cfcuser.memberdata.email) gt 0 AND val(arguments.emailTemplateID) neq 0 AND val(arguments.testSubscriberID) neq 0>
			<cfset local.offerMemberData = getOfferMemberData(subscriberID=arguments.testSubscriberID, emailTemplateID=arguments.emailTemplateID)>

			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>
			<cfset local.supportProviderEmail = local.mc_siteInfo.supportProviderEmail>
			<cfset local.networkEmailFrom = local.mc_siteInfo.networkEmailFrom>
			<cfset local.orgName = local.mc_siteInfo.orgName>
			<cfset local.siteName = local.mc_siteInfo.siteName>

			<cfset local.replyto = trim(local.offerMemberData.emailFrom)>
			<cfif NOT len(local.replyto) or NOT isValid("regex",local.replyto,application.regEx.email)>
				<cfset local.replyto = local.supportProviderEmail>
			</cfif>

			<cfset local.subscriptionAdminSiteResourceID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].subscriptionAdminSiteResourceID>

			<cfset local.arrEmailTo = []>
			<cfset local.toEmailArr = listToArray(arguments.testEmail,';')>
			<cfloop array="#local.toEmailArr#" item="local.thisEmail">
				<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
			</cfloop>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=local.offerMemberData.emailFromName, email=local.networkEmailFrom},
				emailto=local.arrEmailTo,
				emailreplyto=local.replyto,
				emailsubject="TEST: #local.offerMemberData.subjectLine#",
				emailtitle=local.offerMemberData.subjectLine,
				emailhtmlcontent=local.offerMemberData.templateDisp,
				emailAttachments=[],
				siteID=local.mc_siteInfo.siteID,
				memberID=local.mc_siteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SUBEMLOFF"),
				sendingSiteResourceID=local.subscriptionAdminSiteResourceID,
				isTestMessage=1)>
			
			<cfset local.retStruct.success = local.responseStruct.success>
		<cfelse>
			<cfset local.retStruct.success = false>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<!--- Schedules --->
	<cffunction name="addSchedule" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="scheduleName" type="string" required="true">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'' }>
	
		<cftry>
			<cfset local.payLoadData = { "schedulename":arguments.scheduleName, _mcrecordedbymemberid: session.cfcuser.memberdata.memberID }>
			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="POST", endpoint="subscription/rateschedule", payload=serializeJSON(local.payloadData))>
			
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, ' ')#">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateSchedule">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select scheduleID
				from dbo.sub_rateSchedules
				where [uid] = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#local.strInsertResponse.data.subscriptionrateschedule.api_id#">
				and [status] <> 'D';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
	
			<cfset local.returnStruct['scheduleID'] = local.qryRateSchedule.scheduleID>
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateSchedule" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="scheduleID" type="numeric" required="true">
		<cfargument name="scheduleName" type="string" required="true">
		<cfargument name="scheduleUID" type="string" required="false">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'' }>
	
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateSchedule">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select [uid]
				from dbo.sub_rateSchedules
				where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">
				and [status] <> 'D';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.payLoadData = { "schedulename":arguments.scheduleName, "_mcrecordedbymemberid":session.cfcuser.memberdata.memberID }>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(trim(arguments.scheduleUID))>
				<cfset local.payLoadData["api_id"] = trim(arguments.scheduleUID)>
			</cfif>
	
			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="PUT", endpoint="subscription/rateschedule/#local.qryRateSchedule.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>
	
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeSchedule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "errmsg":'' }>
	
		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.payLoadData = { "_mcrecordedbymemberid":session.cfcuser.memberdata.memberID }>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="subscription/rateschedule/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>
	
			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRatesByScheduleIDFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>		
		<cfset arguments.event.setValue('direct',form['order[0][dir]'] ?: 'asc')>
		<cfset arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)))>
		<cfset arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))))>
		<cfset arguments.event.setValue('count',int(val(arguments.event.getValue('length',100))))>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>
		
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRates">
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			IF OBJECT_ID('tempdb..##tmpRates') IS NOT NULL
				DROP TABLE ##tmpRates;

			DECLARE @rfid INT, @scheduleID INT;
			SET @rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			SET @scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('schedID')#">;

			SELECT DISTINCT CASE WHEN LEN(r.rateName) = 0 THEN '(name not set)' ELSE r.rateName END AS rateName, 
				r.rateID, r.status, r.rateOrder,
				r.rateStartDate, r.rateEndDate, r.rateAFStartDate, r.rateAFEndDate,
				r.termStartDate, r.termEndDate, r.termAFStartDate, r.termAFEndDate,
				r.graceEndDate, r.recogStartDate, r.recogEndDate, r.recogAFStartDate, r.recogAFEndDate,
				r.siteResourceID, r.isRenewalRate, r.forceUpfront, g.groupID, g.groupName, srrc.include
			into ##tmpRates	
			FROM dbo.sub_rates r
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID AND srs.siteResourceStatusDesc IN ('Active', 'Deleted')
			LEFT OUTER JOIN dbo.cms_siteResourceRightsCache AS srrc
				INNER JOIN dbo.ams_groups AS g ON g.groupID = srrc.groupID 
				ON srrc.resourceid = r.siteresourceID AND srrc.functionID = @rfid AND g.status = 'A' and srrc.siteID = sr.siteID
			LEFT OUTER JOIN sub_rateFrequencies rf ON rf.rateID = r.rateID
			WHERE r.scheduleID = @scheduleID
			<cfif arguments.event.getValue('fFrequencyID','') NEQ ''>
				AND rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fFrequencyID')#">
			</cfif>
			<cfif arguments.event.getValue('fIsRenewalRate','') NEQ ''>
				AND r.isRenewalRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('fIsRenewalRate')#">
			</cfif>
			<cfif arguments.event.getValue('fStatus','') NEQ ''>
				AND r.status = <cfqueryparam cfsqltype="CF_SQL_CHAR" value="#arguments.event.getValue('fStatus')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('fKeyword','')) gt 0>
				AND r.rateName LIKE '%' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fKeyword')#"> + '%'
			</cfif>
			<cfif arguments.event.getTrimValue('fAmtFrom','') NEQ ''>
				AND rf.rateAmt >= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#val(rereplace(arguments.event.getValue('fAmtFrom'),"[^0-9.]","","ALL"))#">
			</cfif>
			<cfif arguments.event.getTrimValue('fAmtTo','') NEQ ''>
				AND rf.rateAmt <= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#val(rereplace(arguments.event.getValue('fAmtTo'),"[^0-9.]","","ALL"))#">
			</cfif>
			<cfif arguments.event.getTrimValue('fAvailFrom','') NEQ ''>
				AND r.rateAFStartDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fAvailFrom')#">
			</cfif>
			<cfif arguments.event.getTrimValue('fAvailTo','') NEQ ''>
				AND r.rateAFEndDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fAvailTo')# 23:59:59.997">
			</cfif>
			<cfif arguments.event.getTrimValue('fTermFrom','') NEQ ''>
				AND r.termAFStartDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('fTermFrom')#">
			</cfif>
			<cfif arguments.event.getTrimValue('fTermTo','') NEQ ''>
				AND r.termAFEndDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fTermTo')# 23:59:59.997">
			</cfif>
			<cfif arguments.event.getValue('frateUID','') NEQ ''>
				AND r.uid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#arguments.event.getValue('frateUID')#">
			</cfif>
			<cfif arguments.event.getValue('fforceUpfront','') NEQ ''>
				AND r.forceUpfront = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('fforceUpfront')#">
			</cfif>
			<cfif arguments.event.getValue('fFrontEndAllowChangePrice','') NEQ ''>
				AND r.frontEndAllowChangePrice = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('fFrontEndAllowChangePrice')#">
			</cfif>
			<cfif arguments.event.getValue('fKeepChangedPriceOnRenewal','') NEQ ''>
				AND r.keepChangedPriceOnRenewal = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('fKeepChangedPriceOnRenewal')#">
			</cfif>;
			
				SELECT tmp.*
				FROM ##tmpRates AS tmp
				ORDER BY status, rateOrder
				;

				IF OBJECT_ID('tempdb..##tmpRates') IS NOT NULL
					DROP TABLE ##tmpRates;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryRates>
	</cffunction>


	<!--- Rates --->

	<cffunction name="getRateByRateID" access="public" output="false" returntype="query">
		<cfargument name="rateID" type="numeric" required="true">
		<cfset var qryRate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRate">
			select top 1 r.rateid, r.rateName, r.rateAFStartDate, r.rateAFEndDate
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
		</cfquery>

		<cfreturn qryRate>
	</cffunction>

	<cffunction name="addRate" access="public" output="false" returntype="struct">
		<cfargument name="scheduleID" type="numeric" required="true">
		<cfargument name="rateName" type="string" required="true">
		<cfargument name="reportCode" type="string" required="true">
		<cfargument name="rateStatus" type="string" required="true">
		<cfargument name="rateStartDateTime" type="date" required="true">
		<cfargument name="rateEndDateTime" type="date" required="true">
		<cfargument name="rateStartAFID" type="numeric" required="true">
		<cfargument name="rateEndAFID" type="numeric" required="true">
		<cfargument name="termStartDateTime" type="date" required="true">
		<cfargument name="termEndDateTime" type="date" required="true">
		<cfargument name="termStartAFID" type="numeric" required="true">
		<cfargument name="termEndAFID" type="numeric" required="true">
		<cfargument name="graceEndDateTime" type="string" required="true">
		<cfargument name="graceEndAFID" type="numeric" required="true">
		<cfargument name="recogStartDateTime" type="date" required="true">
		<cfargument name="recogEndDateTime" type="date" required="true">
		<cfargument name="recogStartAFID" type="numeric" required="true">
		<cfargument name="recogEndAFID" type="numeric" required="true">
		<cfargument name="rateAdvanceOnTermEnd" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="numeric" required="true">
		<cfargument name="forceUpfront" type="numeric" required="true">
		<cfargument name="accountID" type="numeric" required="true">
		<cfargument name="frontEndAllowChangePrice" type="numeric" required="true">
		<cfargument name="linkedNonRenewalRateID" type="numeric" required="true">
		<cfargument name="fallbackRenewalRateID" type="numeric" required="true">
		<cfargument name="keepChangedPriceOnRenewal" type="numeric" required="true">
		<cfargument name="frontEndChangePriceMin" type="numeric" required="true">
		<cfargument name="frontEndChangePriceMax" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_createRate">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.scheduleID#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.rateName#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.reportCode#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.rateStatus#">
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.rateStartDateTime#">
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.rateEndDateTime#">
			<cfif arguments.rateStartAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateStartAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif arguments.rateEndAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateEndAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.termStartDateTime#">
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.termEndDateTime#">
			<cfif arguments.termStartAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.termStartAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif arguments.termEndAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.termEndAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif len(arguments.graceEndDateTime)>
				<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.graceEndDateTime#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_timestamp" null="yes">
			</cfif>
			<cfif arguments.graceEndAFID neq 0>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.graceEndAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.recogStartDateTime#">
			<cfprocparam type="In" cfsqltype="cf_sql_timestamp" value="#arguments.recogEndDateTime#">
			<cfif arguments.recogStartAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.recogStartAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif arguments.recogEndAFID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.recogEndAFID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rateAdvanceOnTermEnd#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.isRenewalRate#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.forceUpfront#">
			<cfif arguments.accountID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.accountID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.frontEndAllowChangePrice#">
			<cfif arguments.linkedNonRenewalRateID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.linkedNonRenewalRateID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif arguments.fallbackRenewalRateID>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.fallbackRenewalRateID#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif arguments.keepChangedPriceOnRenewal>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="1">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="0">
			</cfif>
			<cfif val(arguments.frontEndChangePriceMin)>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#val(arguments.frontEndChangePriceMin)#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfif val(arguments.frontEndChangePriceMax)>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#val(arguments.frontEndChangePriceMax)#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="cf_sql_integer" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.rateID">
			<cfprocparam type="Out" cfsqltype="cf_sql_integer" variable="local.siteResourceID">
		</cfstoredproc>

		<cfreturn local>
	</cffunction>

	<cffunction name="updateRate" access="public" output="false" returntype="void">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="rateName" type="string" required="true">
		<cfargument name="reportCode" type="string" required="true">
		<cfargument name="rateUID" type="string" required="true">
		<cfargument name="rateStatus" type="string" required="true">
		<cfargument name="rateStartDateTime" type="date" required="true">
		<cfargument name="rateEndDateTime" type="date" required="true">
		<cfargument name="rateStartAFID" type="numeric" required="true">
		<cfargument name="rateEndAFID" type="numeric" required="true">
		<cfargument name="termStartDateTime" type="date" required="true">
		<cfargument name="termEndDateTime" type="date" required="true">
		<cfargument name="termStartAFID" type="numeric" required="true">
		<cfargument name="termEndAFID" type="numeric" required="true">
		<cfargument name="graceEndDateTime" type="string" required="true">
		<cfargument name="graceEndAFID" type="numeric" required="true">
		<cfargument name="recogStartDateTime" type="date" required="true">
		<cfargument name="recogEndDateTime" type="date" required="true">
		<cfargument name="recogStartAFID" type="numeric" required="true">
		<cfargument name="recogEndAFID" type="numeric" required="true">
		<cfargument name="rateAdvanceOnTermEnd" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="numeric" required="true">
		<cfargument name="forceUpfront" type="numeric" required="true">
		<cfargument name="accountID" type="numeric" required="true">
		<cfargument name="frontEndAllowChangePrice" type="numeric" required="true">
		<cfargument name="linkedNonRenewalRateID" type="numeric" required="true">
		<cfargument name="fallbackRenewalRateID" type="numeric" required="true">
		<cfargument name="keepChangedPriceOnRenewal" type="numeric" required="true">
		<cfargument name="frontEndChangePriceMin" type="numeric" required="true">
		<cfargument name="frontEndChangePriceMax" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;
				CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Status] varchar(max), [Rate Start Date] varchar(max), [Rate End Date] varchar(max),
					[Rate Start Date Advance Formula] varchar(max), [Rate End Date Advance Formula] varchar(max), [Rate Start Date Advance Date] varchar(max), [Rate End Date Advance Date] varchar(max), 
					[Term Start Date] varchar(max), [Term End Date] varchar(max), [Term Start Date Advance Formula] varchar(max), [Term End Date Advance Formula] varchar(max), 
					[Term Start Date Advance Date] varchar(max), [Term End Date Advance Date] varchar(max), [Grace End Date] varchar(max), [Grace End Date Advance Formula] varchar(max), 
					[Rate Name] varchar(max), [When to Advance Rates] varchar(max), [API ID] varchar(max), [Is Renewal Rate] varchar(max), [Force Up front] varchar(max),
					[Report Code] varchar(max), [GL Account] varchar(max), [Front End Allow Change Price] varchar(max), [Front End Change Price Min] varchar(max), [Front End Change Price Max] varchar(max),
					[Linked Non Renewal Rate] varchar(max), [Fallback Renewal Rate] varchar(max), [Recog Start Date] varchar(max),
					[Recog End Date] varchar(max), [Recog Start Date Advance Formula] varchar(max), [Recog End Date Advance Formula] varchar(max), 
					[Recog Start Date Advance Date] varchar(max), [Recog End Date Advance Date] varchar(max), [Renewal Keeps Changed Price] varchar(max));
				
				declare @orgID int, @siteID int, @rateID int, @rateName varchar(200), @scheduleID int, @scheduleName varchar(200), 
					@frontEndChangePriceMin decimal(18,2), @frontEndChangePriceMax decimal(18,2), @recordedByMemberID int, 
					@msg varchar(max), @crlf varchar(2);

				set @rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">;
				set @frontEndChangePriceMin = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.frontEndChangePriceMin)#">,0);
				set @frontEndChangePriceMax = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.frontEndChangePriceMax)#">,0);
				
				set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;

				select @orgID = s.orgID, @siteID = s.siteID, @rateName = r.rateName, @scheduleID = rs.scheduleID, @scheduleName = rs.scheduleName
				from dbo.sub_rates as r
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID
				inner join dbo.sites as s on s.siteID = rs.siteID
				where r.rateID = @rateID;

				if (@frontEndChangePriceMin is not null) and (@frontEndChangePriceMax is not null) and (@frontEndChangePriceMin > @frontEndChangePriceMax) BEGIN
					set @frontEndChangePriceMin = null;
					set @frontEndChangePriceMax = null;
				END

				-- datatypes
				INSERT INTO ##tmpAuditLogData ([rowCode], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula], 
					[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula], 
					[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula], 
					[Rate Name], [When to Advance Rates], [API ID], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price], 
					[Front End Change Price Min], [Front End Change Price Max], [Linked Non Renewal Rate], [Fallback Renewal Rate], [Recog Start Date],
					[Recog End Date], [Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date], 
					[Renewal Keeps Changed Price])
				VALUES ('DATATYPECODE','STRING', 'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'DATE', 'DATE', 'STRING', 'STRING', 
					'DATE', 'DATE', 'DATE', 'STRING', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'DECIMAL2', 'DECIMAL2',
					'STRING', 'STRING', 'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'BIT');

				-- old values
				INSERT INTO ##tmpAuditLogData ([rowCode], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula], 
					[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula], 
					[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula], 
					[Rate Name], [When to Advance Rates], [API ID], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price], 
					[Front End Change Price Min], [Front End Change Price Max], [Linked Non Renewal Rate], [Fallback Renewal Rate], [Recog Start Date],
					[Recog End Date], [Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
					[Renewal Keeps Changed Price])
				SELECT 'OLDVAL', CASE WHEN r.status = 'A' THEN 'Active' ELSE 'Inactive' END,
					r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
					r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
					af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
					af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
					r.[uid], r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax, 
					rl.rateName AS [Linked Non Renewal Rate], rf.rateName AS [Fallback Renewal Rate], 
					r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
					af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
				FROM dbo.sub_rates AS r
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
				LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
				LEFT OUTER JOIN dbo.sub_rates AS rl ON rl.rateID = r.linkedNonRenewalRateID
				LEFT OUTER JOIN dbo.sub_rates AS rf ON rf.rateID = r.fallbackRenewalRateID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
				LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
				WHERE r.rateID = @rateID;

				BEGIN TRAN;
					UPDATE dbo.sub_rates
					SET rateName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateName#">,
						<cfif listFind('A,I',arguments.rateStatus)>
							status = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateStatus#">,
						</cfif>
						reportCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.reportCode#">,
						<cfif len(arguments.rateUID) AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
							uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateUID#">,
						</cfif>
						<cfif arguments.rateStartAFID>
							rateStartDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateStartAFID#">,
						<cfelse>
							rateStartDateAFID = NULL,
						</cfif>
						<cfif arguments.rateEndAFID>
							rateEndDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateEndAFID#">,
						<cfelse>
							rateEndDateAFID = NULL,
						</cfif>
						rateAFStartDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rateStartDateTime#">,
						rateAFEndDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rateEndDateTime#">,
						termAFStartDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.termStartDateTime#">,
						termAFEndDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.termEndDateTime#">,
						<cfif arguments.termStartAFID>
							termStartDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.termStartAFID#">,
						<cfelse>
							termStartDateAFID = NULL,
						</cfif>
						<cfif arguments.termEndAFID>
							termEndDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.termEndAFID#">,
						<cfelse>
							termEndDateAFID = NULL,
						</cfif>
						<cfif len(arguments.graceEndDateTime)>
							graceEndDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.graceEndDateTime#">,
						<cfelse>
							graceEndDate = NULL,
						</cfif>
						<cfif arguments.graceEndAFID>
							graceAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.graceEndAFID#">,
						<cfelse>
							graceAFID = NULL,
						</cfif>
						recogAFStartDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.recogStartDateTime#">,
						recogAFEndDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.recogEndDateTime#">,
						<cfif arguments.recogStartAFID>
							recogStartDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.recogStartAFID#">,
						<cfelse>
							recogStartDateAFID = NULL,
						</cfif>
						<cfif arguments.recogEndAFID>
							recogEndDateAFID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.recogEndAFID#">,
						<cfelse>
							recogEndDateAFID = NULL,
						</cfif>
						rateAdvanceOnTermEnd = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateAdvanceOnTermEnd#">,
						isRenewalRate = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.isRenewalRate#">,
						forceUpfront = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.forceUpfront#">,
						<cfif arguments.accountID neq 0>
							GLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.accountID#">,
						<cfelse>
							GLAccountID = NULL,
						</cfif>
						frontEndAllowChangePrice = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.frontEndAllowChangePrice#">,
						<cfif arguments.linkedNonRenewalRateID neq 0>
							linkedNonRenewalRateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.linkedNonRenewalRateID#">,
						<cfelse>
							linkedNonRenewalRateID = NULL,
						</cfif>
						<cfif arguments.fallbackRenewalRateID neq 0>
							fallbackRenewalRateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fallbackRenewalRateID#">,
						<cfelse>
							fallbackRenewalRateID = NULL,
						</cfif>
						<cfif arguments.frontEndAllowChangePrice>
							keepChangedPriceOnRenewal = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.keepChangedPriceOnRenewal#">,
						<cfelse>
							keepChangedPriceOnRenewal = 0,
						</cfif>
						frontEndChangePriceMin = @frontEndChangePriceMin,
						frontEndChangePriceMax = @frontEndChangePriceMax
					WHERE rateID = @rateID;
		
					<cfif arguments.rateStatus EQ 'D'>
						EXEC dbo.sub_deleteRate @orgID=@orgID, @rateID=@rateID, @isImport=0, @recordedByMemberID=@recordedByMemberID;
					<cfelse>
						<!--- update any virtual group conditions --->
						<!--- yes, the rateID here should be varchar --->
						UPDATE dbo.ams_virtualGroupConditions
						SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
						WHERE orgID = @orgID
						and LEFT(fieldCode,4) = 'sub_'
						and conditionID in (
								select c.conditionID
								from dbo.ams_virtualGroupConditions as c
								inner join dbo.ams_virtualGroupConditionValues cv 
									on cv.conditionID = c.conditionID
									and cv.conditionValue = cast(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.rateID#"> as varchar(10))
								inner join dbo.ams_virtualGroupConditionKeys as k 
									on k.conditionKeyID = cv.conditionKeyID
									and k.conditionKey = 'subRate'
								where c.orgID = @orgID
						);
					</cfif>
		
					-- new values
					INSERT INTO ##tmpAuditLogData ([rowCode], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula], 
						[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula], 
						[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula], 
						[Rate Name], [When to Advance Rates], [API ID], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price], 
						[Front End Change Price Min], [Front End Change Price Max], [Linked Non Renewal Rate], [Fallback Renewal Rate], [Recog Start Date],
						[Recog End Date], [Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
						[Renewal Keeps Changed Price])
					SELECT 'NEWVAL', CASE WHEN r.status = 'A' THEN 'Active' ELSE 'Inactive' END,
						r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
						r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
						af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
						af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
						r.[uid], r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax, 
						rl.rateName AS [Linked Non Renewal Rate], rf.rateName AS [Fallback Renewal Rate], 
						r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
						af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
					FROM dbo.sub_rates AS r
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
					LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
					LEFT OUTER JOIN dbo.sub_rates AS rl ON rl.rateID = r.linkedNonRenewalRateID
					LEFT OUTER JOIN dbo.sub_rates AS rf ON rf.rateID = r.fallbackRenewalRateID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
					LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
					WHERE r.rateID = @rateID;
		
					-- audit log
					EXEC dbo.ams_getAuditLogMsg @auditLogTable = '##tmpAuditLogData', @msg=@msg OUTPUT;
					
					IF ISNULL(@msg, '') <> '' BEGIN
						set @crlf = char(13) + char(10);
						DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';
						SET @msg = STRING_ESCAPE('Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] updated.','json') + @crlf 
								+ 'The following changes have been made:' + @crlf + @msg;
		
						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					END
		
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>	

	<cffunction name="deleteRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="sub_deleteRate" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="restoreRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="rateID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRestore">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @orgID int, @rateID int, @rateOrder int, @scheduleID int, 
						@recordedByMemberID int, @msgjson varchar(max), @rateName varchar(200), @scheduleName varchar(200);
					
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">;
					
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					
					select @rateName = r.rateName, @scheduleID = rs.scheduleID, @scheduleName = rs.scheduleName
					from dbo.sub_rates as r
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID
					where r.rateID = @rateID;
					
					SELECT @orgID = orgID	
					FROM dbo.sites
					WHERE siteID = @siteID;

					SELECT @rateOrder = isNull(max(r.rateOrder),0)+1
					FROM dbo.sub_rates as r
					INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
					WHERE r.scheduleID = @scheduleID AND r.status = 'A'
					AND sr.siteResourceStatusID = 1;

					BEGIN TRAN
						UPDATE dbo.sub_rateFrequenciesMerchantProfiles
						SET [status] = 'A'
						WHERE rfid IN (
							SELECT DISTINCT rfid
							FROM dbo.sub_rateFrequencies rf
							WHERE rf.rateID = @rateID
						);

						UPDATE dbo.sub_rateFrequencies
						SET [status] = 'A'
						WHERE rfid IN (
							SELECT DISTINCT rfid
							FROM dbo.sub_rateFrequencies rf
							WHERE rf.rateID = @rateID
						);

						UPDATE sr
						SET sr.siteResourceStatusID = 1
						FROM dbo.cms_siteResources as sr
						INNER JOIN dbo.sub_rates as r on r.siteResourceID = sr.siteResourceID
						WHERE r.rateID = @rateID
						AND sr.siteID = @siteID;

						UPDATE dbo.sub_rates
						SET [status] = 'A',
							rateOrder = @rateOrder
						WHERE rateID = @rateID;

						-- audit log
						DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

						SET @msgjson = 'Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] has been restored.';
						SET @msgjson = STRING_ESCAPE(@msgjson,'json');

						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msgjson,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					COMMIT TRAN

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
			<cfset local.data.delResult = 1>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.delResult = 0>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doRateMove" access="public" output="false" returntype="struct" hint="re-order rate">
		<cfargument name="scheduleID" type="numeric" required="true" />
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="dir" type="string" required="true" />

		<cfset var local = structNew() />

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_moveRate">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true />
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="doRateMoveToPosition" access="public" output="false" returntype="struct" hint="Re-Order Fields Downwards">
		<cfargument name="rateID" type="numeric" required="true" />
		<cfargument name="pos" type="numeric" required="true" />

		<cfset var local = structNew() />

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateCount">
			declare @scheduleID int, @rateID int;

			set @rateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">;

			select @scheduleID = scheduleID
			from dbo.sub_rates
			where rateID = @rateID;

			select count(r.rateID) as rateCount
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.scheduleID = @scheduleID;
		</cfquery>

		<cfset local.data.success = true />
		<cfset local.data.errormsg = "" />

		<cfif local.qryRateCount.rateCount lt arguments.pos>
			<cfset local.data.success = false />
			<cfset local.data.errormsg = "The position provided is invalid. The number entered is greater than the total number of rates." />
		<cfelse>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_moveRateToPosition">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pos#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			</cfstoredproc>
		</cfif>

		<cfreturn local.data />
	</cffunction>

	<!--- Misc --->
	<cffunction name="deleteMemberGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="rateid" type="numeric" required="true">
		<cfargument name="groupid" type="numeric" required="true">
		<cfargument name="include" type="numeric" required="false" default="1">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qrySiteResourceRights" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
					@rateID int = <cfqueryparam value="#arguments.rateid#" cfsqltype="CF_SQL_INTEGER">,
					@siteResourceID int;

				select @siteResourceID = siteResourceID
				from dbo.sub_rates
				where rateID = @rateID;

				select srr.resourceRightsID, srr.resourceID, g.groupPathExpanded
				from dbo.cms_siteResourceRights as srr
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID
					and srr.siteID = sr.siteID
				inner join dbo.ams_groups as g on g.orgID = @orgID
					and g.groupID = srr.groupID
				where srr.resourceID = @siteResourceID
				AND srr.groupID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.groupID#">
				AND srr.include = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.include#">;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qrySiteResourceRights.recordCount>
				<cfquery name="local.qryDeleteRoleAssignment" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
	
						DECLARE @siteID int, @orgID int, @siteResourceID int, @siteResourceRightID int, @rateID int,
							@scheduleID int, @rateName varchar(max), @scheduleName varchar(200), @recordedByMemberID int,
							@msg varchar(max) = '', @crlf varchar(2) = char(13) + char(10);
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
						SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
						SET @rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">;
						SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

						SELECT @scheduleID = rs.scheduleID, @rateName = r.rateName, @scheduleName = rs.scheduleName
						FROM dbo.sub_rates AS r
						INNER JOIN dbo.sub_rateSchedules AS rs ON rs.scheduleID = r.scheduleID
						WHERE r.rateID = @rateID;
						
						BEGIN TRAN;
							<cfloop query="local.qrySiteResourceRights">
								SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySiteResourceRights.resourceID#">;
								SET @siteResourceRightID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySiteResourceRights.resourceRightsID#">;

								EXEC dbo.cms_deleteSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @siteResourceRightID=@siteResourceRightID;

								SET @msg = @msg + @crlf + 'Group [' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qrySiteResourceRights.groupPathExpanded#"> + ']' +
									' deleted from Rate Schedule / Rate ' + QUOTENAME(@scheduleName) + ' / ' + QUOTENAME(@rateName) + '.';
							</cfloop>

							IF ISNULL(@msg,'') <> '' BEGIN
								DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';
								SET @msg = STRING_ESCAPE(@msg,'json');
								
								EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
									@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
							END
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<cfset local.data = { success=true }>
		<cfcatch type="Any">
			<cfset local.data = { success=false }>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSubReportListFilters" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfscript>
			var strFilters = { "fSubType": 0, "fSubscription": 0, "fFreq": 0, "fRate": 0, "fTermStartFrom": '', "fTermStartTo": '', 
				"fTermEndFrom": '', "fTermEndTo": '', "fOffrExpFrom": '', "fOffrExpTo": '', "fSubStatus": 'A', "fSubPaymentStatus": 0, 
				"fHasCardOnFile": '', "associatedMemberID": 0, "associatedGroupID": 0, "associatedMemberName": '', "associatedMemberNum": '', 
				"associatedGroupName": '', "fRevGL": 0, "linkedRecords": 'all', "fChkAll": 0, "fChkedSubs": '', "fUnchkedSubs": '' };

			for (var filterKey in strFilters) {
				if (arguments.event.valueExists(filterKey))
				strFilters[filterKey] = arguments.event.getValue(filterKey);
			}

			return strFilters;
		</cfscript>
	</cffunction>

	<cffunction name="getSubReportFilter" access="public" output="false" returntype="struct">
		<cfscript>
		var local = structNew();

		local.tmpStr = { fSubType=0, fSubscription=0, fFreq=0, fRate=0, fTermStartFrom='', fTermStartTo='', fTermEndFrom='', fTermEndTo='', 
			fOffrExpFrom='', fOffrExpTo='', fSubStatus='A', fSubPaymentStatus=0, fHasCardOnFile='', associatedMemberID=0, associatedGroupID=0, 
			associatedMemberName='', associatedMemberNum='', associatedGroupName='', fRevGL=0, linkedRecords='all' };

		local.tmpChangeLogExtraFilterStr = { updtByMemberID=0, updtByGroupID=0, updtByMemberName='', updtByMemberNum='', updtByGroupName='',
			updtByLinkedRecords='all', fSubStatusFrom='0', fSubStatusTo='0', fActivityFrom='', fActivityTo='', fRecordedFrom='', fRecordedTo='' };

		local.SubReportFilter = application.mcCacheManager.sessionGetValue(keyname='SubReportFilter', defaultValue={});
	
		// calculate hash so that we can later test to see if changes need to be saved
		local.SubReportFilter_hash = hash(serializeJSON(local.SubReportFilter), "SHA", "UTF-8");

		if (NOT structKeyExists(local.SubReportFilter,"listFilter"))
			local.SubReportFilter.listFilter = duplicate(local.tmpStr);
		if (NOT structKeyExists(local.SubReportFilter,"accountingFilter"))
			local.SubReportFilter.accountingFilter = duplicate(local.tmpStr);
		if (NOT structKeyExists(local.SubReportFilter,"scheduleFilter"))
			local.SubReportFilter.scheduleFilter = duplicate(local.tmpStr);
		if (NOT structKeyExists(local.SubReportFilter,"memberFilter"))
			local.SubReportFilter.memberFilter = duplicate(local.tmpStr);
		if (NOT structKeyExists(local.SubReportFilter,"changeLogFilter")){
			local.changeLogFilterStr = duplicate(local.tmpStr);			
			StructAppend(local.changeLogFilterStr, local.tmpChangeLogExtraFilterStr);
			local.SubReportFilter.changeLogFilter = duplicate(local.changeLogFilterStr);
		}

		for (local.thiskey in local.tmpStr) {
			if (not structKeyExists(local.SubReportFilter.listFilter, local.thiskey))
				structInsert(local.SubReportFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			if (not structKeyExists(local.SubReportFilter.accountingFilter, local.thiskey))
				structInsert(local.SubReportFilter.accountingFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			if (not structKeyExists(local.SubReportFilter.scheduleFilter, local.thiskey))
				structInsert(local.SubReportFilter.scheduleFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			if (not structKeyExists(local.SubReportFilter.memberFilter, local.thiskey))
				structInsert(local.SubReportFilter.memberFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			if (not structKeyExists(local.SubReportFilter.changeLogFilter, local.thiskey))
				structInsert(local.SubReportFilter.changeLogFilter, local.thiskey, local.tmpStr[local.thiskey], true);
		}

		for (local.thiskey in local.tmpChangeLogExtraFilterStr) {
			if (not structKeyExists(local.SubReportFilter.changeLogFilter, local.thiskey))
				structInsert(local.SubReportFilter.changeLogFilter, local.thiskey, local.tmpChangeLogExtraFilterStr[local.thiskey], true);
		}

		// update cache if object was changed
		if (local.SubReportFilter_hash NEQ hash(serializeJSON(local.SubReportFilter), "SHA", "UTF-8"))
			application.mcCacheManager.sessionSetValue(keyname='SubReportFilter', value=local.SubReportFilter);
		</cfscript>

		<cfreturn local.SubReportFilter>
	</cffunction>

	<cffunction name="saveSubReportFilter" access="public" output="false" returntype="struct">
		<cfargument name="fType" type="string" required="true">
		<cfargument name="stID" type="string" required="true">
		<cfargument name="subID" type="string" required="true">
		<cfargument name="freqID" type="string" required="true">
		<cfargument name="rateID" type="string" required="true">
		<cfargument name="dtTSf" type="string" required="true">"
		<cfargument name="dtTSt" type="string" required="true">
		<cfargument name="dtTEf" type="string" required="true">"
		<cfargument name="dtTEt" type="string" required="true">
		<cfargument name="sID" type="string" required="true">
		<cfargument name="spID" type="string" required="true">
		<cfargument name="fCard" type="string" required="true">
		<cfargument name="fRevGL" type="numeric" required="true">
		<cfargument name="associatedMemberID" type="numeric" required="true">
		<cfargument name="associatedGroupID" type="numeric" required="true">
		<cfargument name="associatedMemberName" type="string" required="true">
		<cfargument name="associatedMemberNum" type="string" required="true">
		<cfargument name="associatedGroupName" type="string" required="true">
		<cfargument name="linkedRecords" type="string" required="false" default="">
		<cfargument name="updtByMemberID" type="numeric" required="false" default="0">
		<cfargument name="updtByGroupID" type="numeric" required="false" default="0">
		<cfargument name="updtByMemberName" type="string" required="false" default="">
		<cfargument name="updtByMemberNum" type="string" required="false" default="">
		<cfargument name="updtByGroupName" type="string" required="false" default="">
		<cfargument name="updtByLinkedRecords" type="string" required="false" default="">
		<cfargument name="dtOffrExpT" type="string" required="false" default="">
		<cfargument name="dtOffrExpF" type="string" required="false" default="">
		<cfargument name="subStatusFrom" type="string" required="false" default="0">
		<cfargument name="subStatusTo" type="string" required="false" default="0">
		<cfargument name="activityFrom" type="string" required="false" default="">
		<cfargument name="activityTo" type="string" required="false" default="">
		<cfargument name="recordedFrom" type="string" required="false" default="">
		<cfargument name="recordedTo" type="string" required="false" default="">

		<cfscript>
			var local = structNew();

			// just to make sure the fields exist
			local.SubReportFilter = getSubReportFilter();

			local.tmpStr = {};
			local.tmpStr.fSubType = arguments.stID;
			local.tmpStr.fSubscription = arguments.subID;
			local.tmpStr.fFreq = arguments.freqID;
			local.tmpStr.fRate = arguments.rateID;
			local.tmpStr.fTermStartFrom = arguments.dtTSf;
			local.tmpStr.fTermStartTo = arguments.dtTSt;
			local.tmpStr.fTermEndFrom = arguments.dtTEf;
			local.tmpStr.fTermEndTo = arguments.dtTEt;
			local.tmpStr.fOffrExpFrom = arguments.dtOffrExpF;
			local.tmpStr.fOffrExpTo = arguments.dtOffrExpT;
			local.tmpStr.fSubStatus = arguments.sID;
			local.tmpStr.fSubPaymentStatus = arguments.spID;
			local.tmpStr.fHasCardOnFile = arguments.fCard;
			local.tmpStr.associatedMemberID = arguments.associatedMemberID;
			local.tmpStr.associatedGroupID = arguments.associatedGroupID;
			local.tmpStr.associatedMemberName = arguments.associatedMemberName;
			local.tmpStr.associatedMemberNum = arguments.associatedMemberNum;
			local.tmpStr.associatedGroupName = arguments.associatedGroupName;
			local.tmpStr.linkedRecords = arguments.linkedRecords;
			local.tmpStr.fRevGL = arguments.fRevGL;

			if (arguments.fType is 'changeLog') {
				local.tmpStr.updtByMemberID = arguments.updtByMemberID;
				local.tmpStr.updtByGroupID = arguments.updtByGroupID;
				local.tmpStr.updtByMemberName = arguments.updtByMemberName;
				local.tmpStr.updtByMemberNum = arguments.updtByMemberNum;
				local.tmpStr.updtByGroupName = arguments.updtByGroupName;
				local.tmpStr.updtByLinkedRecords = arguments.updtByLinkedRecords;
				local.tmpStr.fSubStatusFrom = arguments.subStatusFrom;
				local.tmpStr.fSubStatusTo = arguments.subStatusTo;
				local.tmpStr.fActivityFrom = arguments.activityFrom;
				local.tmpStr.fActivityTo = arguments.activityTo;
				local.tmpStr.fRecordedFrom = arguments.recordedFrom;
				local.tmpStr.fRecordedTo = arguments.recordedTo;
			}

			if (arguments.fType eq 'list') {
				local.SubReportFilter.listFilter = duplicate(local.tmpStr);
			} else if (arguments.fType eq 'accounting') {
				local.SubReportFilter.accountingFilter = duplicate(local.tmpStr);
			} else if (arguments.fType eq 'schedule') {
				local.SubReportFilter.scheduleFilter = duplicate(local.tmpStr);
			} else if (arguments.fType is 'member') {
				local.SubReportFilter.memberFilter = duplicate(local.tmpStr);
			} else if (arguments.fType is 'changeLog') {
				local.SubReportFilter.changeLogFilter = duplicate(local.tmpStr);
			}

			application.mcCacheManager.sessionSetValue(keyname='SubReportFilter', value=local.SubReportFilter);

			local.data.success = true;
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSubRenewalsFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();

			local.tmpStr = { fSubType=0, fSubscription=0, fFreq=0, fRate=0, fTermStartFrom='', fTermStartTo='', fTermEndFrom='', fTermEndTo='', 
			fSubStatus='A', fSubPaymentStatus=0, fHasCardOnFile='', associatedMemberID=0, associatedGroupID=0, 
			associatedMemberName='', associatedMemberNum='', associatedGroupName='', fRevGL=0, linkedRecords='all' };

		local.SubRenewalsFilter = application.mcCacheManager.sessionGetValue(keyname='SubRenewalsFilter', defaultValue={});
	
		// calculate hash so that we can later test to see if changes need to be saved
		local.SubRenewalsFilter_hash = hash(serializeJSON(local.SubRenewalsFilter), "SHA", "UTF-8");

		// if the struct is not there, add it.
		if (NOT structKeyExists(local.SubRenewalsFilter,"listFilter"))
			local.SubRenewalsFilter.listFilter = duplicate(local.tmpStr);
		if (NOT structKeyExists(local.SubRenewalsFilter,"memberFilter"))
			local.SubRenewalsFilter.memberFilter = duplicate(local.tmpStr);

		for (local.thiskey in local.tmpStr) {
			if (not structKeyExists(local.SubRenewalsFilter.listFilter, local.thiskey))
				structInsert(local.SubRenewalsFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			if (not structKeyExists(local.SubRenewalsFilter.memberFilter, local.thiskey))
				structInsert(local.SubRenewalsFilter.memberFilter, local.thiskey, local.tmpStr[local.thiskey], true);
		}

		// update cache if object was changed
		if (local.SubRenewalsFilter_hash NEQ hash(serializeJSON(local.SubRenewalsFilter), "SHA", "UTF-8"))
			application.mcCacheManager.sessionSetValue(keyname='SubRenewalsFilter', value=local.SubRenewalsFilter);
		</cfscript>

		<cfreturn local.SubRenewalsFilter>
	</cffunction>

	<cffunction name="saveSubRenewalsFilter" access="public" output="false" returntype="struct">
		<cfargument name="fType" type="string" required="true">
		<cfargument name="stID" type="string" required="true">
		<cfargument name="subID" type="string" required="true">
		<cfargument name="freqID" type="string" required="true">
		<cfargument name="rateID" type="string" required="true">
		<cfargument name="dtTSf" type="string" required="true">
		<cfargument name="dtTSt" type="string" required="true">
		<cfargument name="dtTEf" type="string" required="true">
		<cfargument name="dtTEt" type="string" required="true">
		<cfargument name="sID" type="string" required="true">
		<cfargument name="spID" type="string" required="true">
		<cfargument name="fCard" type="string" required="true">
		<cfargument name="associatedMemberID" type="numeric" required="true">
		<cfargument name="associatedGroupID" type="numeric" required="true">
		<cfargument name="associatedMemberName" type="string" required="true">
		<cfargument name="associatedMemberNum" type="string" required="true">
		<cfargument name="associatedGroupName" type="string" required="true">
		<cfargument name="linkedRecords" type="string" required="false" default="">

		<cfscript>
			var local = structNew();

			// just to make sure the fields exist
			local.SubRenewalsFilter = getSubRenewalsFilter();

			if (arguments.fType is 'list')
			{
				local.SubRenewalsFilter.listFilter.fSubType = arguments.stID;
				local.SubRenewalsFilter.listFilter.fSubscription = arguments.subID;
				local.SubRenewalsFilter.listFilter.fFreq = arguments.freqID;
				local.SubRenewalsFilter.listFilter.fRate = arguments.rateID;
				local.SubRenewalsFilter.listFilter.fTermStartFrom = arguments.dtTSf;
				local.SubRenewalsFilter.listFilter.fTermStartTo = arguments.dtTSt;
				local.SubRenewalsFilter.listFilter.fTermEndFrom = arguments.dtTEf;
				local.SubRenewalsFilter.listFilter.fTermEndTo = arguments.dtTEt;
				local.SubRenewalsFilter.listFilter.fSubStatus = arguments.sID;
				local.SubRenewalsFilter.listFilter.fSubPaymentStatus = arguments.spID;
				local.SubRenewalsFilter.listFilter.fHasCardOnFile = arguments.fCard;
				local.SubRenewalsFilter.listFilter.associatedMemberID = arguments.associatedMemberID;
				local.SubRenewalsFilter.listFilter.associatedGroupID = arguments.associatedGroupID;
				local.SubRenewalsFilter.listFilter.associatedMemberName = arguments.associatedMemberName;
				local.SubRenewalsFilter.listFilter.associatedMemberNum = arguments.associatedMemberNum;
				local.SubRenewalsFilter.listFilter.associatedGroupName = arguments.associatedGroupName;
				local.SubRenewalsFilter.listFilter.linkedRecords = arguments.linkedRecords;
			}
			else if (arguments.fType is 'member')
			{
				local.SubRenewalsFilter.memberFilter.fSubType = arguments.stID;
				local.SubRenewalsFilter.memberFilter.fSubscription = arguments.subID;
				local.SubRenewalsFilter.memberFilter.fFreq = arguments.freqID;
				local.SubRenewalsFilter.memberFilter.fRate = arguments.rateID;
				local.SubRenewalsFilter.memberFilter.fTermStartFrom = arguments.dtTSf;
				local.SubRenewalsFilter.memberFilter.fTermStartTo = arguments.dtTSt;
				local.SubRenewalsFilter.memberFilter.fTermEndFrom = arguments.dtTEf;
				local.SubRenewalsFilter.memberFilter.fTermEndTo = arguments.dtTEt;
				local.SubRenewalsFilter.memberFilter.fSubStatus = arguments.sID;
				local.SubRenewalsFilter.memberFilter.fSubPaymentStatus = arguments.spID;
				local.SubRenewalsFilter.memberFilter.fHasCardOnFile = arguments.fCard;
				local.SubRenewalsFilter.memberFilter.associatedMemberID = arguments.associatedMemberID;
				local.SubRenewalsFilter.memberFilter.associatedGroupID = arguments.associatedGroupID;
				local.SubRenewalsFilter.memberFilter.associatedMemberName = arguments.associatedMemberName;
				local.SubRenewalsFilter.memberFilter.associatedMemberNum = arguments.associatedMemberNum;
				local.SubRenewalsFilter.memberFilter.associatedGroupName = arguments.associatedGroupName;
				local.SubRenewalsFilter.memberFilter.linkedRecords = arguments.linkedRecords;
			}

			application.mcCacheManager.sessionSetValue(keyname='SubRenewalsFilter', value=local.SubRenewalsFilter);

			local.data.success = true;
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSubReportsQuery" access="public" output="false" returntype="struct">
		<cfargument name="subStatus" type="string" required="yes">
		<cfargument name="subPaymentStatus" type="string" required="yes">
		<cfargument name="subStartFromDate" type="string" required="yes">
		<cfargument name="subStartToDate" type="string" required="yes">
		<cfargument name="subEndFromDate" type="string" required="yes">
		<cfargument name="subEndToDate" type="string" required="yes">
		<cfargument name="subType" type="numeric" required="yes">
		<cfargument name="subID" type="numeric" required="yes">
		<cfargument name="freqID" type="numeric" required="yes">
		<cfargument name="rateID" type="string" required="yes">
		<cfargument name="hasCard" type="string" required="yes">
		<cfargument name="associatedMemberID" type="numeric" required="yes">
		<cfargument name="associatedGroupID" type="numeric" required="yes">		
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="offerEndFromDate" type="string" required="false">
		<cfargument name="offerEndToDate" type="string" required="false">
		<cfargument name="linkedRecords" type="string" required="false" default="selected">
		<cfargument name="countOnly" type="boolean" required="false" default="false">
		<cfargument name="posStart" type="numeric" required="false" default="0">
		<cfargument name="rowLimit" type="numeric" required="false" default="0">
		<cfargument name="notSubscribers" type="string" required="false" default="">
		<cfargument name="orderby" type="string" required="false" default="RTRIM(m2.lastname + ' ' + isnull(m2.suffix, '')) + ', ' + m2.firstname">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="mode" type="string" required="false" default="default" hint="default or emailsub">
		
		<cfset var local = StructNew()>
		<cfset local.retStruct = structNew()>

		<cfif len(arguments.rateID) eq 0>
			<cfset arguments.rateID = "0">
		</cfif>

		<cfset local.startFromDate = ''>
		<cfif len(arguments.subStartFromDate) gt 0>
			<cfset local.startFromDate = DateFormat(arguments.subStartFromDate, "m/d/yyyy")>
		</cfif>

		<cfset local.startToDate = ''>
		<cfif len(arguments.subStartToDate) gt 0>
			<cfset local.startToDate = DateFormat(arguments.subStartToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.endFromDate = ''>
		<cfif len(arguments.subEndFromDate) gt 0>
			<cfset local.endFromDate = DateFormat(arguments.subEndFromDate, "m/d/yyyy")>
		</cfif>

		<cfset local.endToDate = ''>
		<cfif len(arguments.subEndToDate) gt 0>
			<cfset local.endToDate = DateFormat(arguments.subEndToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>
		
		<cfset local.offerEndFromDate = ''>
		<cfif structKeyExists(arguments, "offerEndFromDate") and len(arguments.offerEndFromDate)>
			<cfset local.offerEndFromDate = DateFormat(arguments.offerEndFromDate, "m/d/yyyy")>
		</cfif>

		<cfset local.offerEndToDate = ''>
		<cfif structKeyExists(arguments, "offerEndToDate") and len(arguments.offerEndToDate)>
			<cfset local.offerEndToDate = DateFormat(arguments.offerEndToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.rfidList = ''>
		<cfif (val(arguments.rateID) gt 0) OR (val(arguments.freqID) gt 0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select rf.rfid
				from dbo.sub_rateFrequencies rf
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				<cfif val(arguments.rateID) gt 0>
					and r.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#" list="yes">)
				</cfif>
				<cfif val(arguments.freqID) gt 0>
					where rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.freqID#">
				</cfif>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
			<cfif len(local.rfidList) eq 0>
				<cfset local.rfidList = 0>
			</cfif>
		</cfif>
					
		<cfif arguments.countOnly>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubsCount">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int;
				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);

				<cfif arguments.associatedMemberID gt 0>
					DECLARE @memberID INT;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY, firstname VARCHAR(255),lastname VARCHAR(255), company VARCHAR(255),membernumber VARCHAR(255), status CHAR(2));

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedMemberID#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID, m.firstname, m.lastname, m.company, m.membernumber, m.status
					FROM dbo.ams_members as m WHERE memberID = @memberID
					
					<cfif arguments.linkedRecords is "all">
						UNION
							
						SELECT allchildMember.memberID, allchildMember.firstname, allchildMember.lastname, allchildMember.company, allchildMember.membernumber, allchildMember.status
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>
				
				select count(s.subscriberID) as subscriberCount
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">
				inner join dbo.ams_members as m on s.memberID = m.memberID
				<cfif NOT arguments.associatedMemberID gt 0>
					inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID and m2.status <> 'D'
				<cfelse>
					inner join @tblMembers as m2 on m2.memberID = m.activeMemberID
				</cfif>
				
				<cfif arguments.associatedGroupID gt 0>
					inner join dbo.cache_members_groups mg ON mg.memberID = m2.memberID AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedGroupID#">
				</cfif>
				where s.rootSubscriberID in (select rs.rootSubscriberID
											from dbo.sub_subscribers rs
											inner join dbo.sub_subscriptions rsub
														on rsub.subscriptionID = rs.subscriptionID
															and rs.rootSubscriberID = s.subscriberID
														<cfif len(arguments.notSubscribers) gt 0>
															and rs.rootSubscriberID not in (#arguments.notSubscribers#)
														</cfif>
														<cfif len(local.startFromDate) gt 0>
															AND rs.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFromDate#">
														</cfif>
														<cfif len(local.startToDate) gt 0>
															AND rs.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startToDate#">
														</cfif>
														<cfif len(local.endFromDate) gt 0>
															AND rs.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFromDate#">
														</cfif>
														<cfif len(local.endToDate) gt 0>
															AND rs.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endToDate#">
														</cfif>
														
														<cfif len(local.offerEndFromDate) gt 0>
															AND s.offerRescindDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndFromDate#">
														</cfif>
														<cfif len(local.offerEndToDate) gt 0>
															AND s.offerRescindDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndToDate#">
														</cfif>
														<cfif arguments.subID neq "0">
															AND rsub.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
														</cfif>
														<cfif len(local.rfidList) gt 0>
															AND rs.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
														</cfif>
														<cfif (len(arguments.subType) eq 0) or (arguments.subType eq "0")>
															AND rs.parentSubscriberID is null
														</cfif>
											inner join dbo.sub_statuses rst
														on rst.statusID = rs.statusID
													<cfif arguments.subStatus neq "0">
														 AND rst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subStatus#">
													</cfif>
											inner join dbo.sub_paymentStatuses rpst
														on rpst.statusID = rs.paymentStatusID
														<cfif arguments.subPaymentStatus neq "0">
															 AND rpst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subPaymentStatus#">
														</cfif>
											inner join sub_types rt
													on rt.typeID = rsub.typeID
														<cfif (len(arguments.subType) gt 0) AND (arguments.subType neq "0")>
															AND rt.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subType#">
														</cfif>
														and rt.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">)
				
				<cfif arguments.hasCard eq 'Y'>
					AND s.payProfileID is not null
				<cfelseif arguments.hasCard eq 'N'>
					AND s.payProfileID is null
				</cfif>;
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.retStruct.qry = local.qrySubsCount>
			<cfset local.retStruct.totalCount = local.qrySubsCount.subscriberCount>
		<cfelse>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs" result="local.qrySubsResult">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int;
				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);

				<cfif arguments.associatedMemberID gt 0>
					DECLARE @memberID INT;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY, firstname VARCHAR(255),lastname VARCHAR(255), suffix VARCHAR(50), company VARCHAR(255),membernumber VARCHAR(255), status CHAR(2));

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedMemberID#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID, m.firstname, m.lastname, m.suffix, m.company, m.membernumber, m.status
					FROM dbo.ams_members as m WHERE memberID = @memberID
					
					<cfif arguments.linkedRecords is "all">
						UNION
							
						SELECT allchildMember.memberID, allchildMember.firstname, allchildMember.lastname, allchildMember.suffix, allchildMember.company, allchildMember.membernumber, allchildMember.status
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>
				
				select tmp.subscriberID, tmp.memberID, tmp.memberName, tmp.company, tmp.subscriptionID, tmp.typeID, tmp.typeName, tmp.subscriptionName, tmp.statusCode, tmp.statusName, 
					tmp.paymentStatusCode, tmp.subStartDate, tmp.subEndDate, tmp.graceEndDate, tmp.rootSubscriberID, tmp.subscriberPath, tmp.ratename, tmp.offerRescindDate
					<cfif arguments.mode is 'emailsub'>, me.email</cfif>
				from (
					select s.subscriberID, m2.memberID,
						m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' AS memberName, m2.company,
						s.subscriptionID,s.offerRescindDate, t.typeID, t.typeName, sub.subscriptionName, st.statusCode, st.statusName, pst.statusCode as paymentStatusCode,
						s.subStartDate, s.subEndDate, s.graceEndDate, s.rootSubscriberID, s.subscriberPath, r.rateName,
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(arguments.orderby)# #arguments.direct#) as row
						from dbo.sub_subscribers s
						inner join dbo.sub_rateFrequencies rf
							on rf.rfid = s.rfid
						inner join dbo.sub_rates r
							on r.rateID = rf.rateID
						inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
						inner join dbo.sub_statuses st on st.statusID = s.statusID
						inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
						inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">
						inner join dbo.ams_members as m on s.memberID = m.memberID
						<cfif NOT arguments.associatedMemberID gt 0>
							inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID and m2.status <> 'D'
						<cfelse>
							inner join @tblMembers as m2 on m2.memberID = m.activeMemberID
						</cfif>
						<cfif arguments.associatedGroupID gt 0>
							inner join dbo.cache_members_groups mg ON mg.memberID = m2.memberID AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedGroupID#">
						</cfif>
						where s.rootSubscriberID in (select rs.rootSubscriberID
													from dbo.sub_subscribers rs
													inner join dbo.sub_subscriptions rsub
																on rsub.subscriptionID = rs.subscriptionID
																	and rs.rootSubscriberID = s.subscriberID
																<cfif len(arguments.notSubscribers) gt 0>
																	and rs.rootSubscriberID not in (#arguments.notSubscribers#)
																</cfif>
																<cfif len(local.startFromDate) gt 0>
																	AND rs.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFromDate#">
																</cfif>
																<cfif len(local.startToDate) gt 0>
																	AND rs.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startToDate#">
																</cfif>
																<cfif len(local.endFromDate) gt 0>
																	AND rs.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFromDate#">
																</cfif>
																<cfif len(local.endToDate) gt 0>
																	AND rs.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endToDate#">
																</cfif>
																<cfif len(local.offerEndFromDate) gt 0>
																	AND s.offerRescindDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndFromDate#">
																</cfif>
																<cfif len(local.offerEndToDate) gt 0>
																	AND s.offerRescindDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndToDate#">
																</cfif>
																<cfif arguments.subID neq "0">
																	AND rsub.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
																</cfif>
																<cfif len(local.rfidList) gt 0>
																	AND rs.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
																</cfif>
																<cfif (len(arguments.subType) eq 0) or (arguments.subType eq "0")>
																	AND rs.parentSubscriberID is null
																</cfif>
													inner join dbo.sub_statuses rst
																on rst.statusID = rs.statusID
															<cfif arguments.subStatus neq "0">
																 AND rst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subStatus#">
															</cfif>
													inner join dbo.sub_paymentStatuses rpst
																on rpst.statusID = rs.paymentStatusID
																<cfif arguments.subPaymentStatus neq "0">
																	 AND rpst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subPaymentStatus#">
																</cfif>
													inner join sub_types rt
															on rt.typeID = rsub.typeID
																<cfif (len(arguments.subType) gt 0) AND (arguments.subType neq "0")>
																	AND rt.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subType#">
																</cfif>
																and rt.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">)
					<cfif arguments.hasCard eq 'Y'>
						AND s.payProfileID is not null
					<cfelseif arguments.hasCard eq 'N'>
						AND s.payProfileID is null
					</cfif>
					
				) tmp
				<cfif arguments.mode is 'emailsub'>
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = tmp.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgId = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
				</cfif>
				<cfif arguments.rowLimit gt 0>
					where row > #arguments.posStart# and row <= #arguments.posStart + arguments.rowLimit#
				</cfif>
				order by row;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.retStruct.qry = local.qrySubs>
			<cfset local.retStruct.qryResult = local.qrySubsResult>
			<cfset local.retStruct.totalCount = local.qrySubs.recordCount>
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSubRenewalsQuery" access="public" output="false" returntype="struct">
		<cfargument name="subStatus" type="string" required="yes">
		<cfargument name="subPaymentStatus" type="string" required="yes">
		<cfargument name="subStartFromDate" type="string" required="yes">
		<cfargument name="subStartToDate" type="string" required="yes">
		<cfargument name="subEndFromDate" type="string" required="yes">
		<cfargument name="subEndToDate" type="string" required="yes">
		<cfargument name="subType" type="numeric" required="yes">
		<cfargument name="subID" type="numeric" required="yes">
		<cfargument name="freqID" type="numeric" required="yes">
		<cfargument name="rateID" type="string" required="yes">
		<cfargument name="hasCard" type="string" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="countOnly" type="boolean" required="false" default="false">
		<cfargument name="posStart" type="numeric" required="false" default="0">
		<cfargument name="rowLimit" type="numeric" required="false" default="0">
		<cfargument name="orderBy" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="notSubscribers" type="string" required="false" default="">
		<cfargument name="associatedMemberID" type="numeric" required="yes">
		<cfargument name="associatedGroupID" type="numeric" required="yes">		
		<cfargument name="linkedRecords" type="string" required="false" default="selected">		

		<cfset var local = StructNew()>
		<cfset local.retStruct = structNew()>

		<cfif len(arguments.subStatus) eq 0>
			<cfset arguments.subStatus = "0">
		</cfif>

		<cfif len(arguments.rateID) eq 0>
			<cfset arguments.rateID = "0">
		</cfif>

		<cfset local.startFromDate = ''>
		<cfif len(arguments.subStartFromDate) gt 0>
			<cfset local.startFromDate = DateFormat(arguments.subStartFromDate, "m/d/yyyy")>
		</cfif>

		<cfset local.startToDate = ''>
		<cfif len(arguments.subStartToDate) gt 0>
			<cfset local.startToDate = DateFormat(arguments.subStartToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.endFromDate = ''>
		<cfif len(arguments.subEndFromDate) gt 0>
			<cfset local.endFromDate = DateFormat(arguments.subEndFromDate, "m/d/yyyy")>
		</cfif>

		<cfset local.endToDate = ''>
		<cfif len(arguments.subEndToDate) gt 0>
			<cfset local.endToDate = DateFormat(arguments.subEndToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.rfidList = ''>
		<cfif (val(arguments.rateID) gt 0) OR (val(arguments.freqID) gt 0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
				select rf.rfid
				from dbo.sub_rateFrequencies rf
				inner join dbo.sub_rates r
					on r.rateID = rf.rateID
				<cfif val(arguments.rateID) gt 0>
					and r.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#" list="yes">)
				</cfif>
				<cfif val(arguments.freqID) gt 0>
					where rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.freqID#">
				</cfif>
			</cfquery>
			<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
			<cfif len(local.rfidList) eq 0>
				<cfset local.rfidList = 0>
			</cfif>
		</cfif>

		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		<cfset local.generateMassRenewalsSubscriptionTypeRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionType", functionName="GenerateMassRenewals")>

		<cfif arguments.countOnly>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubsCount">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @FID int, @siteID int, @orgID int, @generateMassRenewalFID int, @enteredByMemberID int, @enteredByMemberGroupPrintID int
				declare @allowedSubs TABLE (subscriptionID int PRIMARY KEY, rateTermDateFlag char(1), scheduleID int, subscriptionName varchar(300), typeID int, typeName varchar(300));
				declare @queuedRenewals TABLE (subscriberID int PRIMARY KEY);
				declare @dtNow datetime;

				select @dtNow = getdate();
				select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
				select @generateMassRenewalFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.generateMassRenewalsSubscriptionTypeRFID#">;
				select @siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">;
				select @orgID = orgID from dbo.sites where siteID = @siteID;
				select @enteredByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
				
				select @enteredByMemberGroupPrintID=groupPrintID
				from ams_members m 
				where m.memberID = @enteredByMemberID

				<cfif arguments.associatedMemberID gt 0>
					DECLARE @memberID INT;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY, firstname VARCHAR(255),lastname VARCHAR(255), suffix varchar(50), company VARCHAR(255),membernumber VARCHAR(255), status CHAR(2), groupPrintID int);

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedMemberID#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID, m.firstname, m.lastname, m.suffix, m.company, m.membernumber, m.status, m.groupPrintID
					FROM dbo.ams_members as m 
					WHERE m.orgID = @orgID and m.memberID = @memberID
					
					<cfif arguments.linkedRecords is "all">
						UNION
							
						SELECT allchildMember.memberID, allchildMember.firstname, allchildMember.lastname, allchildMember.suffix, allchildMember.company, allchildMember.membernumber, allchildMember.status, allchildMember.groupPrintID
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON m.orgID = @orgID 
							and rr.orgID = @orgID 
							and rr.masterMemberID = m.memberID
							AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON childMember.orgID = @orgID
							and rr.childMemberID = childMember.memberID
							and childMember.status <> 'D'
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.orgID = @orgID
							and allchildMember.activeMemberID = childMember.memberID
						WHERE m.memberID = @memberID
					</cfif>
				</cfif>				

				INSERT INTO @allowedSubs (subscriptionID, rateTermDateFlag, scheduleID, subscriptionName, typeID, typename)
				SELECT subs.subscriptionID, subs.rateTermDateFlag, subs.scheduleID, subs.subscriptionName, t.typeID, t.typeName
				FROM dbo.sub_Types t
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp ON srfrp.siteID = @siteID
					AND srfrp.siteResourceID = t.siteResourceID
					AND srfrp.functionID = @generateMassRenewalFID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
					and gprp.groupPrintID = @enteredByMemberGroupPrintID
					AND srfrp.rightPrintID = gprp.rightPrintID
				INNER JOIN dbo.sub_subscriptions subs on subs.typeID = t.typeID
					<cfif arguments.subID neq "0">
						AND subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
					</cfif>
				WHERE t.siteID = @siteID
				AND t.status = 'A'
				<cfif (len(arguments.subType) gt 0) AND (arguments.subType neq "0")>
					AND t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subType#">
				</cfif>;
				
				insert into @queuedRenewals (subscriberID)
				select qid.subscriberID
				from platformQueue.dbo.queue_subscriptionRenew as qid
				inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qid.statusID
					and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
				where qid.orgID = @orgID;

				select count(distinct ss.subscriberID) as subscriberCount
				from @allowedSubs subs 
				inner join dbo.sub_subscribers ss on ss.orgID = @orgID
					and ss.subscriptionID = subs.subscriptionID
					<cfif len(local.rfidList) gt 0>
						AND ss.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
					</cfif>
					and ss.parentSubscriberID is null
					<cfif len(local.startFromDate) gt 0>
						AND ss.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFromDate#">
					</cfif>
					<cfif len(local.startToDate) gt 0>
						AND ss.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startToDate#">
					</cfif>
					<cfif len(local.endFromDate) gt 0>
						AND ss.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFromDate#">
					</cfif>
					<cfif len(local.endToDate) gt 0>
						AND ss.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endToDate#">
					</cfif>
					<cfif arguments.hasCard eq 'Y'>
						AND ss.payProfileID is not null
					<cfelseif arguments.hasCard eq 'N'>
						AND ss.payProfileID is null
					</cfif>
				inner join sub_statuses st on st.statusID = ss.statusID
					<cfif arguments.subStatus neq "0">
						AND st.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#arguments.subStatus#">)
					<cfelse>
						AND st.canRenew = 1
					</cfif>
				inner join sub_paymentStatuses pst on pst.statusID = ss.paymentStatusID
					<cfif arguments.subPaymentStatus neq "0">
						AND pst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subPaymentStatus#">
					</cfif>
				inner join dbo.ams_members m on m.orgID = @orgID 
					and ss.memberID = m.memberID
				<cfif NOT arguments.associatedMemberID gt 0>
					inner join dbo.ams_members as activeMember on activeMember.orgID = @orgID 
						and activeMember.memberID = m.activeMemberID 
						and activeMember.status <> 'D'
				<cfelse>
					inner join @tblMembers as activeMember on activeMember.memberID = m.activeMemberID
				</cfif>
				<cfif arguments.associatedGroupID gt 0>
					inner join dbo.cache_members_groups mg ON mg.orgID = @orgID 
						and mg.memberID = activeMember.memberID 
						AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedGroupID#">
				</cfif>						
				left outer join sub_rates r
					inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
						and srfrp.siteResourceID = r.siteResourceID
						and srfrp.functionID = @FID 
					inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID 
						and srfrp.rightPrintID = gprp.rightPrintID
					on r.scheduleID = subs.scheduleID
					and r.isRenewalRate = 1 
					and r.status <> 'D'
					and activeMember.groupPrintID = gprp.groupPrintID
				left outer join dbo.sub_subscribers sP
					inner join dbo.sub_statuses st2 on sp.orgID = @orgID
						and st2.statusID = sP.statusID 
						and st2.statusCode not in ('D','X')
					inner join dbo.ams_members m2 on m2.orgID = @orgID 
						and m2.memberID = sP.memberID
					on sP.subscriptionID = ss.subscriptionID
					and sP.subscriberID <> ss.subscriberID
					and m2.activeMemberID = m.activeMemberID
					and sP.subEndDate >= ss.subEndDate				
				left outer join @queuedRenewals rq on rq.subscriberID = ss.subscriberID
				where sP.subscriberID is null
				and rq.subscriberID is null
				and (select case
					when subs.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
					when subs.rateTermDateFlag = 'C' then dateAdd(dd, 1, ss.subEndDate) end) > ss.subEndDate
				and (sP.subscriberID is null OR
					(st2.statusCode = 'P' AND
						((select case
							when subs.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
							when subs.rateTermDateFlag = 'C' then dateAdd(dd, 1, ss.subEndDate) end) > sP.subEndDate)));
				
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.retStruct.qry = local.qrySubsCount>
			<cfset local.retStruct.totalCount = local.qrySubsCount.subscriberCount>
		<cfelse>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"tmprs.memberName #arguments.direct#")>
			<cfset arrayAppend(local.arrCols,"tmprs.memberName #arguments.direct#")>
			<cfset arrayAppend(local.arrCols,"tmprs.memberName #arguments.direct#")>
			<cfset arrayAppend(local.arrCols,"tmprs.subStartDate #arguments.direct#")>
			<cfset arrayAppend(local.arrCols,"tmprs.subEndDate #arguments.direct#")>
			<cfset arrayAppend(local.arrCols,"tmprs.graceEndDate #arguments.direct#")>
			<cfset local.orderby = local.arrcols[arguments.orderby+1]>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs" result="local.qrySubsResult">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
					DROP TABLE ##tmpSubscribers;
				IF OBJECT_ID('tempdb..##tmpAllowedSubs') IS NOT NULL
					DROP TABLE ##tmpAllowedSubs;
				IF OBJECT_ID('tempdb..##tmpRenewSubscribers') IS NOT NULL
					DROP TABLE ##tmpRenewSubscribers;
				CREATE TABLE ##tmpAllowedSubs (subscriptionID int PRIMARY KEY, rateTermDateFlag char(1), scheduleID int, subscriptionName varchar(300), typeID int, typeName varchar(300));

				declare @FID int, @siteID int, @orgID int, @generateMassRenewalFID int, @enteredByMemberID int, @enteredByMemberGroupPrintID int, @totalCount int;
				declare @queuedRenewals TABLE (subscriberID int PRIMARY KEY);
				declare @dtNow datetime;

				select @dtNow = getdate();
				select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
				select @generateMassRenewalFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.generateMassRenewalsSubscriptionTypeRFID#">;
				select @siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">;
				select @orgID = orgID from dbo.sites where siteID = @siteID;
				select @enteredByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
				
				select @enteredByMemberGroupPrintID=groupPrintID
				from ams_members m 
				where m.memberID = @enteredByMemberID

				<cfif arguments.associatedMemberID gt 0>
					DECLARE @memberID INT;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY, firstname VARCHAR(255),lastname VARCHAR(255), suffix varchar(50), company VARCHAR(255),membernumber VARCHAR(255), status CHAR(2), groupPrintID int);

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedMemberID#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID, m.firstname, m.lastname, m.suffix, m.company, m.membernumber, m.status, m.groupPrintID
					FROM dbo.ams_members as m 
					WHERE m.orgID = @orgID and m.memberID = @memberID
					
					<cfif arguments.linkedRecords is "all">
						UNION
							
						SELECT allchildMember.memberID, allchildMember.firstname, allchildMember.lastname, allchildMember.suffix, allchildMember.company, allchildMember.membernumber, allchildMember.status, allchildMember.groupPrintID
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON m.orgID = @orgID 
							and rr.orgID = @orgID 
							and rr.masterMemberID = m.memberID
							AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON childMember.orgID = @orgID
							and rr.childMemberID = childMember.memberID
							and childMember.status <> 'D'
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.orgID = @orgID
							and allchildMember.activeMemberID = childMember.memberID
						WHERE m.memberID = @memberID
					</cfif>
				</cfif>				

				INSERT INTO ##tmpAllowedSubs (subscriptionID, rateTermDateFlag, scheduleID, subscriptionName, typeID, typename)
				SELECT subs.subscriptionID, subs.rateTermDateFlag, subs.scheduleID, subs.subscriptionName, t.typeID, t.typeName
				FROM dbo.sub_Types t
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp ON srfrp.siteID = @siteID
					AND srfrp.siteResourceID = t.siteResourceID
					AND srfrp.functionID = @generateMassRenewalFID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
					and gprp.groupPrintID = @enteredByMemberGroupPrintID
					AND srfrp.rightPrintID = gprp.rightPrintID
				INNER JOIN dbo.sub_subscriptions subs on subs.typeID = t.typeID
					<cfif arguments.subID neq "0">
						AND subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subID#">
					</cfif>
				WHERE t.siteID = @siteID
				AND t.status = 'A'
				<cfif (len(arguments.subType) gt 0) AND (arguments.subType neq "0")>
					AND t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subType#">
				</cfif>;

				insert into @queuedRenewals (subscriberID)
				select qid.subscriberID
				from platformQueue.dbo.queue_subscriptionRenew as qid
				inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qid.statusID
					and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
				where qid.orgID = @orgID;

				select ss.subscriberID, activeMember.memberID, activeMember.groupPrintID, 
					activeMember.lastname + ', ' + activeMember.firstname + ' (' + activeMember.membernumber + ')' AS memberName, 
					activeMember.company, ss.subscriptionID, subs.typeID, subs.typeName, subs.subscriptionName, st.statusCode, 
					st.statusName, pst.statusCode as paymentStatusCode, ss.subStartDate, ss.subEndDate, ss.graceEndDate, subs.scheduleID, 
					subs.rateTermDateFlag
				INTO ##tmpRenewSubscribers
				from ##tmpAllowedSubs subs 
				inner join dbo.sub_subscribers ss on ss.orgID = @orgID
					and ss.subscriptionID = subs.subscriptionID
					<cfif len(local.rfidList) gt 0>
						AND ss.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
					</cfif>
					and ss.parentSubscriberID is null
					<cfif len(local.startFromDate) gt 0>
						AND ss.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFromDate#">
					</cfif>
					<cfif len(local.startToDate) gt 0>
						AND ss.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startToDate#">
					</cfif>
					<cfif len(local.endFromDate) gt 0>
						AND ss.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFromDate#">
					</cfif>
					<cfif len(local.endToDate) gt 0>
						AND ss.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endToDate#">
					</cfif>
					<cfif arguments.hasCard eq 'Y'>
						AND ss.payProfileID is not null
					<cfelseif arguments.hasCard eq 'N'>
						AND ss.payProfileID is null
					</cfif>
					<cfif len(arguments.notSubscribers) gt 0>
						and ss.subscriberID not in (#arguments.notSubscribers#)
					</cfif>
				inner join sub_statuses st on st.statusID = ss.statusID
					<cfif arguments.subStatus neq "0">
						AND st.statusCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#arguments.subStatus#">)
					<cfelse>
						AND st.canRenew = 1
					</cfif>
				inner join sub_paymentStatuses pst on pst.statusID = ss.paymentStatusID
					<cfif arguments.subPaymentStatus neq "0">
						AND pst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.subPaymentStatus#">
					</cfif>
				inner join dbo.ams_members m on m.orgID = @orgID 
					and ss.memberID = m.memberID
				<cfif NOT arguments.associatedMemberID gt 0>
					inner join dbo.ams_members as activeMember on activeMember.orgID = @orgID 
						and activeMember.memberID = m.activeMemberID 
						and activeMember.status <> 'D'
				<cfelse>
					inner join @tblMembers as activeMember on activeMember.memberID = m.activeMemberID
				</cfif>
				<cfif arguments.associatedGroupID gt 0>
					inner join dbo.cache_members_groups mg ON mg.orgID = @orgID 
						and mg.memberID = activeMember.memberID 
						AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.associatedGroupID#">
				</cfif>;				

				select subscriberID, memberID, memberName, company, subscriptionID, typeID, typeName, subscriptionName, statusCode, 
					statusName, paymentStatusCode, subStartDate, subEndDate, graceEndDate, row
				into ##tmpSubscribers
				from (
					select tmprs.subscriberID, tmprs.memberID, tmprs.memberName, tmprs.company, tmprs.subscriptionID, tmprs.typeID, tmprs.typeName, 
						tmprs.subscriptionName, tmprs.statusCode, tmprs.statusName, tmprs.paymentStatusCode, tmprs.subStartDate, tmprs.subEndDate, 
						tmprs.graceEndDate, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
					from ##tmpRenewSubscribers as tmprs
					left outer join sub_rates r
						inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
							and srfrp.siteResourceID = r.siteResourceID
							and srfrp.functionID = @FID 
						inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID 
							and srfrp.rightPrintID = gprp.rightPrintID
						on r.scheduleID = tmprs.scheduleID
						and r.isRenewalRate = 1 
						and r.status <> 'D'
						and tmprs.groupPrintID = gprp.groupPrintID
					left outer join dbo.sub_subscribers sP
						inner join dbo.sub_statuses st2 on sp.orgID = @orgID
							and st2.statusID = sP.statusID 
							and st2.statusCode not in ('D','X')
						inner join dbo.ams_members m2 on m2.orgID = @orgID 
							and m2.memberID = sP.memberID
						on sP.subscriptionID = tmprs.subscriptionID
						and sP.subscriberID <> tmprs.subscriberID
						and m2.activeMemberID = tmprs.memberID
						and sP.subEndDate >= tmprs.subEndDate
					left outer join @queuedRenewals rq on rq.subscriberID = tmprs.subscriberID
					where sP.subscriberID is null
					and rq.subscriberID is null
					and (select case
						when tmprs.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
						when tmprs.rateTermDateFlag = 'C' then dateAdd(dd, 1, tmprs.subEndDate) end) > tmprs.subEndDate
					and (sP.subscriberID is null OR
						(st2.statusCode = 'P' AND
							((select case
								when tmprs.rateTermDateFlag in ('A','S') then (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end)
								when tmprs.rateTermDateFlag = 'C' then dateAdd(dd, 1, tmprs.subEndDate) end) > sP.subEndDate)))
					group by tmprs.subscriberID, tmprs.memberID, tmprs.memberName, tmprs.company, tmprs.subscriptionID, tmprs.typeID, tmprs.typeName, 
						tmprs.subscriptionName, tmprs.statusCode, tmprs.statusName, tmprs.paymentStatusCode, tmprs.subStartDate, tmprs.subEndDate, 
						tmprs.graceEndDate
				) as tmp;
					
				SET @totalCount = @@ROWCOUNT;
			
				SELECT tmp.*, @totalCount as totalCount
				FROM ##tmpSubscribers AS tmp
				<cfif arguments.rowLimit gt 0>
					WHERE row > #arguments.posStart# and row <= #arguments.posStart + arguments.rowLimit#
				</cfif>
				ORDER BY ROW;

				IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
					DROP TABLE ##tmpSubscribers;
				IF OBJECT_ID('tempdb..##tmpAllowedSubs') IS NOT NULL
					DROP TABLE ##tmpAllowedSubs;
				IF OBJECT_ID('tempdb..##tmpRenewSubscribers') IS NOT NULL
					DROP TABLE ##tmpRenewSubscribers;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.retStruct.qry = local.qrySubs>
			<cfset local.retStruct.qryResult = local.qrySubsResult>
			<cfset local.retStruct.totalCount = local.qrySubs.recordCount>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSubscriptionStructureXML" access="public" output="false" returntype="xml">
		<cfargument name="subscriptionID" type="numeric" required="yes">
		<cfargument name="frontEndAllowSelect" type="boolean" required="yes">
		<cfargument name="nullFrontEndAllowSelect" type="boolean" required="no" default="false">
		<cfscript>
			var local = structNew();
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qSubStructureXML">
			select dbo.fn_sub_getSubscriptionStructureXML(
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">,
				<cfif not nullFrontEndAllowSelect>
					<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.frontEndAllowSelect#">
				<cfelse>
					<cfqueryparam cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				) as subXML
			option(recompile)
		</cfquery>
		<cfreturn local.qSubStructureXML.subXML>
	</cffunction>

	<cffunction name="getQualifiedRatesXML" access="public" output="false" returntype="xml">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="isRenewalRate" type="boolean" required="yes">
		<cfscript>
			var local = structNew();
		</cfscript>

		<cfstoredproc procedure="sub_getQualifiedRatesXML" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.scheduleID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			<cfprocparam cfsqltype="cf_sql_bit" value="#arguments.isRenewalRate#">
			<cfprocresult name="local.qryRateXML">
		</cfstoredproc>

		<cfreturn xmlParse(local.qryRateXML.ratesXML)>
	</cffunction>

	<cffunction name="showSubscriptionStatusLegend" access="public" returnType="string" hint="shows nonactivated/deleted legend">
		<cfargument name="statusList" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.statusLegendHtml = "">
		
		<cfif ListLen(arguments.statusList)>
			<cfsavecontent variable="local.statusLegendHtml">
				<cfoutput>
					<style>
						.nonactivated{ font-size:8pt; background-color:##ffff00; border:1px solid ##ffcc00; padding:1px 5px 1px 5px; }
						.deleted{ font-size:8pt; background-color:##fcd9c4; border:1px solid ##e77979; padding:1px 5px 1px 5px; }
					</style>
					<div style="width:100%" class="text-right">
						<div style="float:right;margin-top:3px;">
							<small>Key: </small>
							<cfif listFindNoCase(arguments.statusList,'nonactivated')>
								<span class="nonactivated">Activation Not Met</span>
							</cfif>
							<cfif listFindNoCase(arguments.statusList,'deleted')>
								<span class="deleted">Deleted</span>
							</cfif>
						</div>
					</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.statusLegendHtml>
	</cffunction>

	<!--- Frequencies --->
	<cffunction name="checkFrequencyName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true" />
		<cfargument name="fid" type="numeric" required="true" />
		<cfargument name="frequencyName" type="string" required="true" />
		<cfargument name="frequencyShortName" type="string" required="true" />

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCheckFreq">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
				@frequencyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fid#">,
				@frequencyName varchar(50) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frequencyName#">,
				@frequencyShortName varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frequencyShortName#">;

			SELECT frequencyID
			FROM dbo.sub_frequencies
			WHERE siteID = @siteID
			AND [status] <> 'D'
			AND (frequencyName = @frequencyName OR frequencyShortName = @frequencyShortName)
			<cfif arguments.fid>
				AND frequencyID <> @frequencyID
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.success = true>
		<cfset local.data.freqnameinuse = local.qryCheckFreq.recordCount gt 0>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addFrequency" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">	
		<cfargument name="frequencyName" type="string" required="true">
		<cfargument name="frequencyShortName" type="string" required="true">
		<cfargument name="frequency" type="numeric" required="true">
		<cfargument name="rateRequired" type="numeric" required="true">
		<cfargument name="monthlyInterval" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { frequencyname: arguments.frequencyName, frequencyshortname: arguments.frequencyShortName, numberinstallments: arguments.frequency, 
											neededbyallrates: arguments.rateRequired, monthsbetweenpayments: arguments.monthlyInterval, _mcrecordedbymemberid:session.cfcuser.memberdata.memberID }>
			<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="POST", endpoint="subscription/frequency", payload=serializeJSON(local.payloadData))>
			
			<cfif local.strInsertResponse.error>
				<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateFrequency" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">		
		<cfargument name="frequencyName" type="string" required="false">
		<cfargument name="frequencyShortName" type="string" required="false">
		<cfargument name="frequency" type="numeric" required="false">
		<cfargument name="rateRequired" type="numeric" required="false">
		<cfargument name="monthlyInterval" type="numeric" required="false">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.payLoadData = { frequencyname: arguments.frequencyName, frequencyshortname: arguments.frequencyShortName, numberinstallments: arguments.frequency, 
											neededbyallrates: arguments.rateRequired, monthsbetweenpayments: arguments.monthlyInterval, _mcrecordedbymemberid:session.cfcuser.memberdata.memberID}>

			<cfset local.strUpdateResponse = application.objMCAPI.api(siteCode=arguments.siteCode, method="PUT", endpoint="subscription/frequency/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateResponse.error>
				<cfthrow message="#arrayToList(local.strUpdateResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeFrequency" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="uid" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.payLoadData = {_mcrecordedbymemberid:session.cfcuser.memberdata.memberID}>

			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="subscription/frequency/#arguments.uid#", payload=serializeJSON(local.payloadData))>
			
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateFrequencyUID" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="frequencyID" type="numeric" required="true">
		<cfargument name="frequencyUID" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) AND len(trim(arguments.frequencyUID))>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateFreqUID">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
						DROP TABLE ##tmpAuditLogData;
					CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Frequency UID] varchar(MAX));

					DECLARE @frequencyID int, @frequencyName varchar(50), @freqUID uniqueidentifier, @existingDeletedFrequencyID int, @crlf varchar(10), 
						@msg varchar(max), @siteID int, @orgID int, @recordedByMemberID int;

					SET @frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.frequencyID#">;
					SET @freqUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.frequencyUID#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @crlf = char(13) + char(10);
					select @orgID = orgID from dbo.sites where siteID = @siteID;

					SELECT @existingDeletedFrequencyID = frequencyID
					FROM dbo.sub_frequencies
					WHERE [status] = 'D'
					AND [uid] = @freqUID;

					SELECT @frequencyName = frequencyName
					FROM dbo.sub_frequencies
					WHERE frequencyID = @frequencyID;

					IF @existingDeletedFrequencyID IS NOT NULL
						UPDATE dbo.sub_frequencies
						SET [uid] = NEWID()
						WHERE frequencyID = @existingDeletedFrequencyID;

					INSERT INTO ##tmpAuditLogData ([rowCode], [Frequency UID])
					SELECT 'DATATYPECODE', 'STRING'
						UNION
					SELECT 'OLDVAL', CAST([uid] as varchar(36))
					FROM dbo.sub_frequencies
					WHERE frequencyID = @frequencyID
						UNION
					SELECT 'NEWVAL', CAST(@freqUID as varchar(36));

					EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

					BEGIN TRAN;
						-- update uid
						UPDATE dbo.sub_frequencies
						SET [uid] = @freqUID
						WHERE frequencyID = @frequencyID;
				
						IF ISNULL(@msg,'') <> '' BEGIN
							DECLARE @subKeyMapJSON varchar(100) = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
							SET @msg = 'Frequency [' + STRING_ESCAPE(@frequencyName,'json') + '] updated.' + @crlf + @msg;

							EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msg,
								@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
						END
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
						DROP TABLE ##tmpAuditLogData;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct = { "success":true }>
		<cfelse>
			<cfset local.returnStruct = { "success":false }>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addMissingRenewLinks" access="public" output="false" returnType="void">
		<cfset var local = structNew()>

		<cfquery name="local.qryMissing" datasource="#application.dsn.membercentral.dsn#">
			select distinct s.rootSubscriberID, st.siteID, s.memberID
			from dbo.sub_subscribers as s
			inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types as st on st.typeID = sub.typeID
			where s.statusID in (3,4,5)
			and s.directLinkCode is null
			and s.subscriberID = s.rootSubscriberID
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateLinks">
			SET NOCOUNT ON;

			DECLARE @uniqueCode char(8), @rootSubscriberID int;

			<cfloop query="local.qryMissing">
				SET @rootSubscriberID = #local.qryMissing.rootSubscriberID#;
				SET @uniqueCode = null;
				EXEC dbo.getUniqueCode @uniqueCode=@uniqueCode OUTPUT;
			
				update dbo.sub_subscribers
				set directLinkCode = @uniqueCode
				where subscriberID = @rootSubscriberID;
			</cfloop>
		</cfquery>
	</cffunction>

	<cffunction name="getSubscriptionsForMemberDate" access="public" output="false" returntype="string">
		<cfargument name="typeID" type="numeric" required="true">
		<cfset var qrySubscriptions = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriptions">
				select subscriptionID, subscriptionName, 2 as sortOrder
				from dbo.sub_subscriptions
				where typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.typeID#">
				and status = 'A'
				UNION
				SELECT '0', '', 1 as sortOrder
				order by sortOrder, subscriptionName
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qrySubscriptions = QueryNew("subscriptionID,subscriptionName","integer,varchar")>
		</cfcatch>
		</cftry>

		<cfreturn SerializeJSON(qrySubscriptions)>
	</cffunction>
	
	<cffunction name="updateMemberDate" access="public" output="false" returntype="void">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="udid" type="numeric" required="true">
		<cfargument name="joinDate" type="string" required="true">
		<cfargument name="rejoinDate" type="string" required="true">
		<cfargument name="dropDate" type="string" required="true">
		<cfargument name="paidThruDate" type="string" required="true">
		<cfargument name="renewalDate" type="string" required="true">
		<cfargument name="isActive" type="numeric" required="true">

		<cfset var qryUpdate = "">
		
		<cfquery datasource="#application.dsn.customApps.dsn#" name="qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;
				CREATE TABLE ##tmpAuditLogData([rowCode] varchar(20) PRIMARY KEY, [Join Date Custom Field] varchar(max), [Rejoin Date Custom Field] varchar(max), 
					[Dropped Date Custom Field] varchar(max), [Paid Through Date Custom Field] varchar(max), [Renewal Date Custom Field] varchar(max), [Is Active] varchar(max));

				DECLARE @orgID int, @siteID int, @siteCode varchar(10), @udid int, @enteredByMemberID int,
					@msg varchar(max), @crlf varchar(2) = char(13) + char(10);

				SET @udid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.udid#">;
				SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

				SELECT @orgID = orgID, @siteID = siteID
				FROM membercentral.dbo.sites
				WHERE siteCode = @siteCode;

				INSERT INTO ##tmpAuditLogData([rowCode], [Join Date Custom Field], [Rejoin Date Custom Field], [Dropped Date Custom Field], [Paid Through Date Custom Field], [Renewal Date Custom Field], [Is Active])
				VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'BIT');

				INSERT INTO ##tmpAuditLogData([rowCode], [Join Date Custom Field], [Rejoin Date Custom Field], [Dropped Date Custom Field], [Paid Through Date Custom Field], [Renewal Date Custom Field], [Is Active])
				SELECT 'OLDVAL', ISNULL(joinDateFieldName,''), ISNULL(rejoinDateFieldName,''), ISNULL(droppedDateFieldName,''), ISNULL(paidThruDateFieldName,''), 
					ISNULL(renewalDateFieldName,''), isActive
				FROM dbo.schedTask_memberJoinDates
				WHERE udid = @udid;
				
				BEGIN TRAN;
					UPDATE dbo.schedTask_memberJoinDates
					SET joinDateFieldName = nullif(<cfqueryparam value="#arguments.joinDate#" cfsqltype="cf_sql_varchar">,''),
						rejoinDateFieldName = nullif(<cfqueryparam value="#arguments.rejoinDate#" cfsqltype="cf_sql_varchar">,''),
						droppedDateFieldName = nullif(<cfqueryparam value="#arguments.dropDate#" cfsqltype="cf_sql_varchar">,''),
						paidThruDateFieldName = nullif(<cfqueryparam value="#arguments.paidThruDate#" cfsqltype="cf_sql_varchar">,''),
						renewalDateFieldName = nullif(<cfqueryparam value="#arguments.renewalDate#" cfsqltype="cf_sql_varchar">,''),
						isActive = <cfqueryparam value="#arguments.isActive#" cfsqltype="cf_sql_bit">
					WHERE udid = @udid;

					INSERT INTO ##tmpAuditLogData([rowCode], [Join Date Custom Field], [Rejoin Date Custom Field], [Dropped Date Custom Field], [Paid Through Date Custom Field], [Renewal Date Custom Field], [Is Active])
					SELECT 'NEWVAL', ISNULL(joinDateFieldName,''), ISNULL(rejoinDateFieldName,''), ISNULL(droppedDateFieldName,''), ISNULL(paidThruDateFieldName,''), 
						ISNULL(renewalDateFieldName,''), isActive
					FROM dbo.schedTask_memberJoinDates
					WHERE udid = @udid;

					EXEC membercentral.dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

					-- audit log
					IF ISNULL(@msg,'') <> '' BEGIN
						DECLARE @subKeyMapJSON varchar(100) = '{ "UDID":'+CAST(@udid AS varchar(10))+' }';
						SET @msg = 'Member Date Rule [UDID:'+CAST(@udid AS varchar(10))+'] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

						EXEC membercentral.dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='MEMDATERULE', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
					END
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateMemberDateSubTypes" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="udid" type="numeric" required="true">
		<cfargument name="arrSubTypes" type="array" required="true">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int, @siteCode varchar(10), @udid int, @subTypeUID uniqueidentifier, @subUID uniqueidentifier, 
					@enteredByMemberID int, @currSubTypeSubIDs varchar(800), @newSubTypeSubIDs varchar(800), 
					@subKeyMapJSON varchar(100), @msg varchar(max), @crlf varchar(2) = char(13) + char(10);
				DECLARE @tmpCurrSubTypes TABLE (typeID int, typeName varchar(100), subscriptionID int, subscriptionName varchar(300));
				DECLARE @tmpNewSubTypes TABLE (typeID int, typeName varchar(100), subscriptionID int, subscriptionName varchar(300));

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SELECT @orgID = membercentral.dbo.fn_getOrgIDFromSiteID(@siteID);
				SET @udid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.udid#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

				INSERT INTO @tmpCurrSubTypes (typeID, typeName, subscriptionID, subscriptionName)
				SELECT t.typeID, t.typeName, s.subscriptionID, s.subscriptionName
				FROM dbo.schedTask_memberJoinDateSubTypes AS st
				INNER JOIN membercentral.dbo.sub_Types AS t ON t.siteID = @siteID
					AND t.[uid] = st.subscriptionTypeUID
				LEFT OUTER JOIN membercentral.dbo.sub_subscriptions AS s ON s.orgID = @orgID
					AND s.typeID = t.typeID
					AND s.[uid] = st.subscriptionUID
				WHERE st.memberJoinDateUDID = @udid;

				SELECT @currSubTypeSubIDs = STRING_AGG(CONCAT(typeID,'|',ISNULL(subscriptionID,0)),',')  WITHIN GROUP (ORDER BY typeID, subscriptionID)
				FROM @tmpCurrSubTypes;

				BEGIN TRAN;
					DELETE FROM dbo.schedTask_memberJoinDateSubTypes 
					WHERE memberJoinDateUDID = @udid;
				
					<cfloop array="#arguments.arrSubTypes#" index="local.thisSubType">
						select @subTypeUID = null, @subUID = null;
						select @subTypeUID = st.uid, @subUID = ss.uid
						FROM membercentral.dbo.sub_Types as st
						LEFT OUTER JOIN membercentral.dbo.sub_subscriptions as ss on st.typeID = ss.typeID 
							AND ss.subscriptionid = #val(local.thisSubType.subID)#
							AND ss.status = 'A'
						WHERE st.typeID = #val(local.thisSubType.typeID)#
						AND st.status = 'A';

						INSERT INTO dbo.schedTask_memberJoinDateSubTypes (memberJoinDateUDID, subscriptionTypeUID, subscriptionUID)
						VALUES (@udid, @subTypeUID, @subUID);
					</cfloop>

					INSERT INTO @tmpNewSubTypes (typeID, typeName, subscriptionID, subscriptionName)
					SELECT t.typeID, t.typeName, s.subscriptionID, s.subscriptionName
					FROM dbo.schedTask_memberJoinDateSubTypes AS st
					INNER JOIN membercentral.dbo.sub_Types AS t ON t.siteID = @siteID
						AND t.[uid] = st.subscriptionTypeUID
					LEFT OUTER JOIN membercentral.dbo.sub_subscriptions AS s ON s.orgID = @orgID
						AND s.typeID = t.typeID
						AND s.[uid] = st.subscriptionUID
					WHERE st.memberJoinDateUDID = @udid;

					SELECT @newSubTypeSubIDs = STRING_AGG(CONCAT(typeID,'|',ISNULL(subscriptionID,0)),',')  WITHIN GROUP (ORDER BY typeID, subscriptionID)
					FROM @tmpNewSubTypes;

					IF ISNULL(@currSubTypeSubIDs,'') <> ISNULL(@newSubTypeSubIDs,'') BEGIN
						SET @msg = 'Member Date Rule [UDID:'+CAST(@udid AS varchar(10))+'] updated. Updated the subscription types and subscriptions to use for this rule:';
						SET @subKeyMapJSON = '{ "UDID":'+CAST(@udid AS varchar(10))+' }';

						IF EXISTS (SELECT 1 FROM @tmpCurrSubTypes)
							SELECT @msg = @msg + @crlf + 'Previous:' + @crlf + STRING_AGG('[' + typeName + ISNULL(' / ' + subscriptionName,'') +']',@crlf)  WITHIN GROUP (ORDER BY typeName, subscriptionName)
							FROM @tmpCurrSubTypes;
						ELSE
							SELECT @msg = @msg + @crlf + 'Previous:' + @crlf + 'No Subscription Types/Subscriptions selected.';

						IF EXISTS (SELECT 1 FROM @tmpNewSubTypes)
							SELECT @msg = @msg + @crlf + 'New:' + @crlf + STRING_AGG('[' + typeName + ISNULL(' / ' + subscriptionName,'') +']',@crlf)  WITHIN GROUP (ORDER BY typeName, subscriptionName)
							FROM @tmpNewSubTypes;
						ELSE
							SELECT @msg = @msg + @crlf + 'New:' + @crlf + 'No Subscription Types/Subscriptions selected.';

						SET @msg = STRING_ESCAPE(@msg,'json');

						EXEC membercentral.dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='MEMDATERULE', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
					END
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="insertMemberDate" access="public" output="false" returntype="string">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="joinDate" type="string" required="true">
		<cfargument name="rejoinDate" type="string" required="true">
		<cfargument name="dropDate" type="string" required="true">
		<cfargument name="paidThruDate" type="string" required="true">
		<cfargument name="renewalDate" type="string" required="true">
		<cfargument name="isActive" type="numeric" required="true">
		<cfargument name="lastMemberRunDate" type="date" required="true">

		<cfset var qryInsertMemberDate = "">

		<cfquery datasource="#application.dsn.customApps.dsn#" name="qryInsertMemberDate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int, @siteCode varchar(10), @udid int, @enteredByMemberID int,
					@lastSuccessDate date, @msg varchar(max), @crlf varchar(2) = char(13) + char(10);

				SET @siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.siteCode#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				SET @lastSuccessDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#dateformat(arguments.lastMemberRunDate,'m/d/yyyy')#">;

				SELECT @orgID = orgID, @siteID = siteID
				FROM membercentral.dbo.sites
				WHERE siteCode = @siteCode;

				BEGIN TRAN;
					INSERT INTO dbo.schedTask_memberJoinDates (siteCode, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, lastSuccessDate, isActive, renewalDateFieldName)
					VALUES (
						@siteCode,
						nullif(<cfqueryparam value="#arguments.joinDate#" cfsqltype="cf_sql_varchar">,''),
						nullif(<cfqueryparam value="#arguments.rejoinDate#" cfsqltype="cf_sql_varchar">,''),
						nullif(<cfqueryparam value="#arguments.dropDate#" cfsqltype="cf_sql_varchar">,''),
						nullif(<cfqueryparam value="#arguments.paidThruDate#" cfsqltype="cf_sql_varchar">,''),
						<cfqueryparam value="#dateformat(arguments.lastMemberRunDate,'m/d/yyyy')#" cfsqltype="cf_sql_date">,
						<cfqueryparam value="#arguments.isActive#" cfsqltype="cf_sql_bit">,
						nullif(<cfqueryparam value="#arguments.renewalDate#" cfsqltype="cf_sql_varchar">,'')
					);

					SELECT @udid = SCOPE_IDENTITY();

					SET @msg = 'New Member Date Rule [UDID:'+CAST(@udid AS varchar(10))+'] has been created.';

					SELECT @msg = @msg + 
						+  CASE WHEN joinDateFieldName IS NOT NULL THEN @crlf + 'Join Date Custom Field: ' + joinDateFieldName ELSE '' END
						+  CASE WHEN rejoinDateFieldName IS NOT NULL THEN @crlf + 'Rejoin Date Custom Field: ' + rejoinDateFieldName ELSE '' END
						+  CASE WHEN droppedDateFieldName IS NOT NULL THEN @crlf + 'Dropped Date Custom Field: ' + droppedDateFieldName ELSE '' END
						+  CASE WHEN paidThruDateFieldName IS NOT NULL THEN @crlf + 'Paid Through Date Custom Field: ' + paidThruDateFieldName ELSE '' END
						+  CASE WHEN renewalDateFieldName IS NOT NULL THEN @crlf + 'Renewal Date Custom Field: ' + renewalDateFieldName ELSE '' END
						+ @crlf + 'Is Active: ' + CASE WHEN isActive = 1 THEN 'Yes' ELSE 'No' END
						+ @crlf + 'Initial Last Run Date: ' + CAST(@lastSuccessDate AS varchar(20))
					FROM dbo.schedTask_memberJoinDates
					WHERE udid = @udid;

					DECLARE @subKeyMapJSON varchar(100) = '{ "UDID":'+CAST(@udid AS varchar(10))+' }';
					SET @msg = STRING_ESCAPE(@msg,'json');

					EXEC membercentral.dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='MEMDATERULE', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
				COMMIT TRAN;

				SELECT @udid AS udid;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfreturn qryInsertMemberDate.udid>
	</cffunction>
	
	<cffunction name="removeMemberDateRule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="udid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasViewSubscriptionSetupRights(siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.customApps.dsn#" name="local.qryRemoveMemberDateRule">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
						@udid int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.udid#">,
						@enteredByMemberID int, @msg varchar(max), @subKeyMapJSON varchar(100), 
						@crlf varchar(2) = char(13) + char(10);

					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SET @msg = 'Member Date Rule [UDID:'+CAST(@udid AS varchar(10))+'] deleted.';
					SET @subKeyMapJSON = '{ "UDID":'+CAST(@udid AS varchar(10))+' }';

					BEGIN TRAN;
						DELETE FROM dbo.schedTask_memberJoinDateSubTypes
						WHERE memberJoinDateUDID = @udid;
				
						DELETE FROM dbo.schedTask_memberJoinDates
						WHERE udid = @udid;

						EXEC membercentral.dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='MEMDATERULE', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSubscriptionTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySubscriptionTypes = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriptionTypes">
			SELECT typeId, typeName, uid
			FROM dbo.sub_Types
			WHERE siteId = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND status = 'A'
			ORDER BY typeName
		</cfquery>

		<cfreturn qrySubscriptionTypes>
	</cffunction>

	<cffunction name="getSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySubscriptions = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubscriptions">
			SELECT s.typeID, s.subscriptionName, s.uid AS subscriptionUID,  st.uid as subscriptionTypeUID, s.subscriptionid
			FROM dbo.sub_subscriptions s
			INNER JOIN dbo.sub_Types st on st.typeID = s.typeID
			WHERE st.siteId = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND s.status = 'A'
			AND st.status = 'A'
			ORDER BY st.typeName, s.subscriptionName
		</cfquery>

		<cfreturn qrySubscriptions>
	</cffunction>

	<cffunction name="getSubStatuses" access="public" output="false" returntype="query">
		<cfset var qrySubStatuses = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qrySubStatuses">
			SELECT statusID, statusCode, statusName
			FROM dbo.sub_statuses
			ORDER BY statusName
		</cfquery>

		<cfreturn qrySubStatuses>
	</cffunction>
	
	<cffunction name="getRedirectMessage" access="public" output="false" returntype="string">
		<cfargument name="contentID" type="numeric" required="yes">
		
		<cfset var qryRedirectMessage = "">

		<cfquery name="qryRedirectMessage" datasource="#application.dsn.membercentral.dsn#">
			select rawContent 
			from dbo.fn_getContent(<cfqueryparam value="#arguments.contentID#" cfsqltype="CF_SQL_INTEGER">,1);
		</cfquery>
		
		<cfreturn qryRedirectMessage.rawContent>
	</cffunction>
	
	<cffunction name="updateSubscriptionSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.redirectSubType = arguments.event.getValue('redirectSubType',0)>
		<cfset local.redirectSubs = arguments.event.getValue('redirectSubs','')>
		<cfset local.contentID = val(arguments.event.getValue('messageContentID_def',0))>
		<cfset local.freeRateDisplay = arguments.event.getValue('freeRateDisplay','')>
		<cfset local.showPhotosInSubsRenew = val(arguments.event.getValue('showPhotosInSubsRenew',0))>
		<cfset local.subRenewalFieldSetID = val(arguments.event.getValue('subRenewalsFE',0))>

		<cfset local.curr_subsAppSettingsXML = getSubscriptionAppSettingsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.curr_freeRateDisplay = xmlSearch(local.curr_subsAppSettingsXML,'string(/settings/setting[@name="freeRateDisplay"]/@value)')>
		<cfset local.curr_showPhotosInSubsRenew = val(xmlSearch(local.curr_subsAppSettingsXML,'string(/settings/setting[@name="showPhotosInSubsRenew"]/@value)'))>

		<cfset local.subscriptionIssuesEmail = application.objcommon.getValidatedEmailAddresses(emailAddressList=arguments.event.getValue('subscriptionIssuesEmail',''), delimiter=';')>
		<cfif len(local.subscriptionIssuesEmail) is 0>
			<cfset local.subscriptionIssuesEmail = "<EMAIL>">
		</cfif>

		<cfxml variable="local.settingsXML">
			<cfoutput>
				<settings>
					<setting name="freeRateDisplay" value="#local.freeRateDisplay#" />
					<setting name="showPhotosInSubsRenew" value="<cfif local.showPhotosInSubsRenew>1<cfelse>0</cfif>" />
				</settings>
			</cfoutput>
		</cfxml>

		<!--- remove the <xml> tag, specifically the encoding. --->
		<cfset local.settingsXML = replaceNoCase(toString(local.settingsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfquery name="local.qryUpdateSettings" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;
				CREATE TABLE ##tmpAuditLogData([rowCode] varchar(20) PRIMARY KEY, [Choose How To Display $0.00 Rates] varchar(max),
					[E-mail Recipient for Subscription Issues] varchar(max), [Member Field Set for Renewals] varchar(max),
					[Include Member Photos in Renewals] varchar(max), [Renewal Redirect Settings - Subscription Type] varchar(max),
					[Renewal Redirect Settings - Subscriptions] varchar(max), [Renewal Redirect Settings Message] varchar(max));
				
				declare @orgID int, @siteID int, @applicationInstanceID int, @appTypeSettingsXML xml, @applicationTypeID int, @subscriptionAdminSRID int,
					@appInstanceSettingsXML xml, @applicationTypeSettingID int, @currentFSUseID int, @currentFSID int, @newFSID int,
					@FSArea varchar(20), @useID int, @htmlContent varchar(max), @appCreatedContentResourceTypeID int,
					@contentID int, @contentSRID int, @enteredByMemberID int, @oldFSName varchar(200), @newFSName varchar(200),
					@curr_subTypeIDs varchar(max), @curr_subTypes varchar(max) = '', @curr_subIDs varchar(max), @curr_subs varchar(max) = '', 
					@new_subTypes varchar(max) = '', @new_subs varchar(max) = '', @currRnwSettingsMsg varchar(max) = '',
					@subscriptionIssuesEmail varchar(max), @msg varchar(max), @crlf varchar(2);
				declare @tmpFreeRateDisplay table (optval varchar(10), valDesc varchar(30));
				
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
				set @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				set @contentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contentID#">;
				set @htmlContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('message_def','')#">;
				set @newFSID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subRenewalFieldSetID#">;
				set @subscriptionAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.subscriptionAdminSiteResourceID')#">;
				set @subscriptionIssuesEmail = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriptionIssuesEmail#">;
				select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
				set @FSArea = 'SubRenewals';
				set @appTypeSettingsXML = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.settingsXML#">;
				select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('subscriptions');
				SET @crlf = char(13) + char(10);

				select @applicationInstanceID = ai.applicationInstanceID, @appInstanceSettingsXML = ai.settingsXML
				from dbo.cms_applicationInstances as ai
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
					and sr.siteResourceID = ai.siteResourceID
				where ai.siteID = @siteID
				and ai.applicationInstanceName = 'admin'
				and sr.siteResourceStatusID = 1;

				select @applicationTypeSettingID = applicationTypeSettingID
				from dbo.cms_applicationTypeSettings
				where siteID = @siteID
				and applicationTypeID = @applicationTypeID;

				select top 1 @currentFSUseID = mfu.useID, @currentFSID = mfu.fieldsetID, @oldFSName = f.fieldSetName
				from dbo.ams_memberFieldUsage as mfu
				inner join dbo.ams_memberFieldSets as f on f.siteID = @siteID
					and f.fieldSetID = mfu.fieldSetID
				where mfu.siteResourceID = @subscriptionAdminSRID
				and mfu.area = @FSArea;

				insert into @tmpFreeRateDisplay (optval, valDesc)
				select 'FREE', 'Display amount as FREE'
					union
				select '$0.00', 'Display amount as $0.00'
					union
				select '', 'Hide amount';

				select @newFSName = fieldSetName
				from dbo.ams_memberFieldSets
				where fieldsetID = @newFSID;

				<cfif local.contentID>
					select @currRnwSettingsMsg = rawContent 
					from dbo.fn_getContent(@contentID,1);
				</cfif>

				-- Sub Types/Subs
				SELECT @curr_subTypeIDs = ISNULL(@appInstanceSettingsXML.value('(/settings/setting[@name="renewRedirect"]/@subscriptiontype)[1]','varchar(max)'),'');
				SELECT @curr_subIDs = ISNULL(@appInstanceSettingsXML.value('(/settings/setting[@name="renewRedirect"]/@subscriptions)[1]','varchar(max)'),'');

				IF @curr_subTypeIDs <> ''
					select @curr_subTypes = STRING_AGG(t.typeName,', ')
					from dbo.sub_Types as t
					inner join dbo.fn_intListToTableInline(@curr_subTypeIDs,',') as tmp on tmp.listitem = t.typeID
					where t.siteID = @siteID
					and t.[status] = 'A';

				IF @curr_subIDs <> ''
					select @curr_subs = STRING_AGG(s.subscriptionName,', ')
					from dbo.sub_subscriptions as s
					inner join dbo.fn_intListToTableInline(@curr_subIDs,',') as tmp on tmp.listitem = s.subscriptionID
					where s.[status] = 'A';

				<cfif listLen(local.redirectSubType)>
					select @new_subTypes = STRING_AGG(typeName,', ')
					from dbo.sub_Types
					where siteID = @siteID
					and typeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.redirectSubType#">)
					and [status] = 'A';
				</cfif>
				<cfif listLen(local.redirectSubs)>
					select @new_subs = STRING_AGG(subscriptionName,', ')
					from dbo.sub_subscriptions
					where subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.redirectSubs#">)
					and [status] = 'A';
				</cfif>

				INSERT INTO ##tmpAuditLogData([rowCode], [Choose How To Display $0.00 Rates], [E-mail Recipient for Subscription Issues], [Member Field Set for Renewals],
					[Include Member Photos in Renewals], [Renewal Redirect Settings - Subscription Type], [Renewal Redirect Settings - Subscriptions], [Renewal Redirect Settings Message])
				VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'BIT', 'STRING', 'STRING', 'CONTENTOBJ');

				INSERT INTO ##tmpAuditLogData([rowCode], [Choose How To Display $0.00 Rates], [E-mail Recipient for Subscription Issues], [Member Field Set for Renewals],
					[Include Member Photos in Renewals], [Renewal Redirect Settings - Subscription Type], [Renewal Redirect Settings - Subscriptions], [Renewal Redirect Settings Message])
				SELECT 'OLDVAL', tmp.[valDesc], s.subscriptionIssuesEmail, @oldFSName, <cfif local.curr_showPhotosInSubsRenew>1<cfelse>0</cfif>, @curr_subTypes, @curr_subs, @currRnwSettingsMsg
				FROM dbo.sites AS s
				INNER JOIN @tmpFreeRateDisplay AS tmp ON tmp.optval = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.curr_freeRateDisplay#">
				WHERE s.siteID = @siteID;
				
				INSERT INTO ##tmpAuditLogData([rowCode], [Choose How To Display $0.00 Rates], [E-mail Recipient for Subscription Issues], [Member Field Set for Renewals],
					[Include Member Photos in Renewals], [Renewal Redirect Settings - Subscription Type], [Renewal Redirect Settings - Subscriptions], [Renewal Redirect Settings Message])
				SELECT 'NEWVAL', [valDesc], @subscriptionIssuesEmail, @newFSName, <cfif local.showPhotosInSubsRenew>1<cfelse>0</cfif>, @new_subTypes, @new_subs, @htmlContent
				FROM @tmpFreeRateDisplay
				WHERE optval = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.freeRateDisplay#">;
				
				BEGIN TRAN;
					<cfif local.contentID eq 0>
						exec dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subscriptionAdminSRID, 
							@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle='', @contentDesc='', 
							@rawContent=@htmlContent, @memberID=@enteredByMemberID, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;
					<cfelse>			
						exec dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 
							@rawContent=@htmlContent, @memberID=@enteredByMemberID;
					</cfif>
					
					IF ISNULL(@currentFSID,0) <> @newFSID BEGIN
						IF @currentFSUseID IS NOT NULL
							delete from dbo.ams_memberFieldUsage
							where useID = @currentFSUseID;

						IF @newFSID > 0
							EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@subscriptionAdminSRID, @fieldsetID=@newFSID,
								@area=@FSArea, @createSiteResourceID=0, @useID=@useID OUTPUT;
					END

					IF @applicationTypeSettingID IS NULL
						insert into dbo.cms_applicationTypeSettings (siteID, applicationTypeID, settingsXML)
						values (@siteID, @applicationTypeID, @appTypeSettingsXML);
					ELSE 
						update dbo.cms_applicationTypeSettings
						set settingsXML = @appTypeSettingsXML
						where applicationTypeSettingID = @applicationTypeSettingID;

					update dbo.cms_applicationInstances
					set settingsXML.modify('delete //setting[@name="renewRedirect"]') 
					where applicationInstanceID = @applicationInstanceID;
				
					update dbo.cms_applicationInstances
					set settingsXML.modify('insert <setting name="renewRedirect" subscriptiontype="#local.redirectSubType#" subscriptions="#local.redirectSubs#" defaultcontentid="{sql:variable("@contentID")}"/> as last into (/settings)[1]')
					where applicationInstanceID = @applicationInstanceID;

					update dbo.sites 
					set subscriptionIssuesEmail = @subscriptionIssuesEmail
					where siteID = @siteID;

					EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

					-- audit log
					IF ISNULL(@msg,'') <> '' BEGIN
						SET @msg = 'Subscription Settings updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSETTINGS', @msgjson=@msg,
							@subKeyMapJSON='{}', @enteredByMemberID=@enteredByMemberID;
					END
				COMMIT TRAN;

				IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
					DROP TABLE ##tmpAuditLogData;
				
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getSubscriberMainEmail" access="public" output="false" returntype="struct">
		<cfargument name="subID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberInfo">
			select memberID
			from dbo.sub_subscribers
			where subscriberID = <cfqueryparam value="#arguments.subID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.subEmailToUse = application.objMember.getMainEmail(memberID=local.qrySubscriberInfo.memberID)>
		<cfset local.retStruct.subEmail = local.subEmailToUse.email>
		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="validateSubscriptionStructureXML" access="public" output="false" returntype="struct">
		<cfargument name="xmlSubscribeMember" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.isValid = true>
		<!--- Include logic for telling whether the user can "Accept" changes when renewing subscriptions. 
			We must ensure the user has met the minimum allowance of addons when applicable.  --->
		<cfreturn local>
	</cffunction>	

	<cffunction name="massUpdateGraceEndDate" access="public" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="subscriberIDList" type="string" required="yes">
		<cfargument name="newGraceEndDate" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.newGraceEndDate = DateFormat(arguments.newGraceEndDate, "m/d/yyyy") & " 23:59:59.997">

		<cfquery name="local.qryMassUpdateGraceEndDate" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;
			CREATE TABLE ##tmpRootSubscribers (rootSubscriberID int PRIMARY KEY);

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@newGraceEndDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.newGraceEndDate#">;

			INSERT INTO ##tmpRootSubscribers (rootSubscriberID)
			SELECT DISTINCT s.rootSubscriberID
			FROM dbo.sub_subscribers AS s
			INNER JOIN dbo.sub_statuses AS ss ON ss.statusID = s.statusID
			WHERE s.orgID = @orgID
			AND s.subscriberID IN (0#arguments.subscriberIDList#)
			AND ss.statusCode NOT IN ('D','X');
			
			UPDATE s 
			SET s.graceEndDate = @newGraceEndDate, s.expectedGraceEndDate = @newGraceEndDate
			FROM dbo.sub_subscribers AS s 
			INNER JOIN ##tmpRootSubscribers AS tmp ON tmp.rootSubscriberID = s.rootSubscriberID
			INNER JOIN dbo.sub_statuses AS ss ON ss.statusID = s.statusID
			WHERE s.orgID = @orgID
			AND ss.statusCode NOT IN ('D','X');

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;
		</cfquery>
	</cffunction>

	<cffunction name="affinipayOneTimeTokenToCardAndSubsAssociate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="row" type="numeric" required="true">
		<cfargument name="memberNumber" type="string" required="true">
		<cfargument name="subscriberID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="nickName" type="string" required="true">
		<cfargument name="token_id" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<!--- Find out which gateway to use based on profileID --->
			<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,5,0)#">
				select pr.siteID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, pr.gatewayMerchantID
				from dbo.mp_profiles as pr
				inner join dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
				where pr.profileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.profileID#">
				and pr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				and pr.status = 'A'
				and ga.isActive = 1
			</cfquery>

			<cfif NOT local.qryGateWayID.recordCount>
				<cfthrow message="Payment Profile is invalid."> 
			</cfif>

			<cfquery name="local.qryGetActiveSubsMember" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @orgID int, @memberID int, @subscriberID int;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

				select @memberID = mActive.memberID
				from dbo.ams_members as m
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				where m.orgID = @orgID
				and m.memberNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.memberNumber#">;

				select @subscriberID = s.subscriberID
				from dbo.sub_subscribers as s
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = s.memberID
					and m.activeMemberID = @memberID
				inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
					and s.subscriberID = s.rootSubscriberID
				where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">;

				select @memberID as memberID, @subscriberID as subscriberID;
			</cfquery>

			<cfif val(local.qryGetActiveSubsMember.memberID) is 0 and val(local.qryGetActiveSubsMember.subscriberID) is 0>
				<cfthrow message="Invalid MemberNumber and SubscriberID.">
			<cfelseif val(local.qryGetActiveSubsMember.memberID) is 0>
				<cfthrow message="Invalid MemberNumber.">
			<cfelseif val(local.qryGetActiveSubsMember.subscriberID) is 0>
				<cfthrow message="Invalid SubscriberID.">
			</cfif>

			<cfset local.strAdd = CreateObject("component","model.system.platform.gateways.AffiniPayCC").insertPaymentProfile(qryGateWayID=local.qryGateWayID, 
						pmid=local.qryGetActiveSubsMember.memberID, token_id=arguments.token_id, nickName=arguments.nickName)>

			<cfif local.strAdd.success>
				<cfquery name="local.qryUpdateSubsPayProfile" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
							DROP TABLE ##tmpAuditLog;
						IF OBJECT_ID('tempdb..##tmpUpdateInvoices') IS NOT NULL 
							DROP TABLE ##tmpUpdateInvoices;
						CREATE TABLE ##tmpAuditLog (auditCode varchar(10), msg varchar(max));
						CREATE TABLE ##tmpUpdateInvoices (invoiceID int);

						DECLARE @orgID int, @siteID int, @subscriberID int, @MPProfileID int, @payProfileID int, @recordedByMemberID int;
						SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
						SET @subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryGetActiveSubsMember.subscriberID#">;
						SET @MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;
						SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strAdd.payProfileID#">;
						SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

						INSERT INTO ##tmpAuditLog (auditCode, msg)
						SELECT 'SUBS', 'Pay Profile ' +
								CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
									WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
									WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
								END + ' Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(@subscriberID AS varchar(10)) + ')'
						FROM dbo.sub_subscribers as s
						INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
						LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = s.payProfileID
						LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
						WHERE s.subscriberID = @subscriberID;

						-- inv
						INSERT INTO ##tmpUpdateInvoices (invoiceID)
						SELECT DISTINCT tri.invoiceID
						FROM dbo.tr_invoices AS tri
						INNER JOIN dbo.tr_invoiceTransactions AS trit on trit.orgID = @orgID and trit.invoiceID = tri.invoiceID
						INNER JOIN dbo.tr_applications AS tra on tra.orgID = @orgID and tra.transactionID = trit.transactionID
							AND tra.applicationTypeID = 17
							AND tra.itemType = 'Dues'
						INNER JOIN dbo.sub_subscribers AS s on s.subscriberID = tra.itemID
							AND s.subscriberID = @subscriberID
						WHERE tri.orgID = @orgID
						AND tri.statusID <> 4
						AND ISNULL(tri.payProfileID,0) <> ISNULL(@payProfileID,0);

						INSERT INTO ##tmpAuditLog (auditCode, msg)
						SELECT 'INV', 'Pay Profile ' + 
								CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
									WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
									WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
								END + ' Invoice ' + o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
						FROM dbo.tr_invoices as i
						INNER JOIN ##tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
						INNER JOIN dbo.organizations as o on o.orgID = i.orgID
						LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
						LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID;

						BEGIN TRAN;
							update dbo.sub_subscribers
							set payProfileID = @payProfileID,
								MPProfileID = @profileID
							where subscriberID = @subscriberID;

							-- update non-paid invoices
							update i
							set i.payProfileID = @payProfileID,
								i.MPProfileID = @profileID
							from dbo.tr_invoices as i
							inner join ##tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID;

							IF EXISTS (SELECT 1 FROM ##tmpAuditLog) BEGIN
								INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
								SELECT '{ "c":"auditLog", "d": {
									"AUDITCODE":"' + auditCode + '",
									"ORGID":' + cast(@orgID as varchar(10)) + ',
									"SITEID":' + cast(@siteID as varchar(10)) + ',
									"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
									"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
									"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
								FROM ##tmpAuditLog;
							END
						COMMIT TRAN;

						IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
							DROP TABLE ##tmpAuditLog;
						IF OBJECT_ID('tempdb..##tmpUpdateInvoices') IS NOT NULL 
							DROP TABLE ##tmpUpdateInvoices;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.data['payprofileid'] = local.strAdd.payProfileID>
				<cfset local.data['success'] = true>
			<cfelse>
				<cfset local.data['success'] = false>
				<cfset local.data['errmsg'] = "Row #arguments.row#: #local.strAdd.error#">
			</cfif>

		<cfcatch type="Any">
			<cfset local.data['success'] = false>
			<cfset local.data['errmsg'] = "Row #arguments.row#: #cfcatch.message#">
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="isValidGraceEndDate" access="public" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="subscriberIDList" type="string" required="yes">
		<cfargument name="newGraceEndDate" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.result = true>
		<cfset local.newGraceEndDate = DateFormat(arguments.newGraceEndDate, "m/d/yyyy") & " 23:59:59.997">

		<cfquery name="local.qryIsValidGraceDate" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;
			CREATE TABLE ##tmpRootSubscribers (rootSubscriberID int PRIMARY KEY);

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@newGraceEndDate datetime = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.newGraceEndDate#">;

			INSERT INTO ##tmpRootSubscribers (rootSubscriberID)
			SELECT DISTINCT s.rootSubscriberID
			FROM dbo.sub_subscribers AS s
			INNER JOIN dbo.sub_statuses AS ss ON ss.statusID = s.statusID
			WHERE s.orgID = @orgID
			AND s.subscriberID IN (0#arguments.subscriberIDList#)
			AND ss.statusCode NOT IN ('D','X');
			
			SELECT COUNT(s.subscriberID) subscriberCount
			FROM dbo.sub_subscribers AS s
			INNER JOIN ##tmpRootSubscribers AS tmp ON tmp.rootSubscriberID = s.rootSubscriberID
			INNER JOIN dbo.sub_statuses AS ss ON ss.statusID = s.statusID
			WHERE s.orgID = @orgID
			AND ss.statusCode NOT IN ('D','X')
			AND s.subEndDate > @newGraceEndDate;

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryIsValidGraceDate.recordCount gt 0 and local.qryIsValidGraceDate.subscriberCount gt 0>
			<cfset local.result = false>
		</cfif>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="getSubscriptionsChangeLogFromFilters" access="public" output="false" returntype="any">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true" hint="grid or export">
		<cfargument name="posStart" type="numeric" required="false">
		<cfargument name="count" type="numeric" required="false">
		<cfargument name="direct" type="string" required="false">
		<cfargument name="orderBy" type="numeric" required="false">
		<cfargument name="fieldsetID" type="numeric" required="false">
		<cfargument name="folderPathUNC" type="string" required="false">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var local = structNew()>
		<cfset local.SubReportFilter = getSubReportFilter()>

		<cfset local.subStatus = local.SubReportFilter.changeLogFilter.fSubStatus>
		<cfset local.subPaymentStatus = local.SubReportFilter.changeLogFilter.fSubPaymentStatus>
		<cfset local.startFromDate = local.SubReportFilter.changeLogFilter.fTermStartFrom>
		<cfset local.startToDate = local.SubReportFilter.changeLogFilter.fTermStartTo>
		<cfset local.endFromDate = local.SubReportFilter.changeLogFilter.fTermEndFrom>
		<cfset local.endToDate = local.SubReportFilter.changeLogFilter.fTermEndTo>
		<cfset local.offerEndFromDate = local.SubReportFilter.changeLogFilter.fOffrExpFrom>
		<cfset local.offerEndToDate = local.SubReportFilter.changeLogFilter.fOffrExpTo>
		<cfset local.subType = local.SubReportFilter.changeLogFilter.fSubType>
		<cfset local.subID = local.SubReportFilter.changeLogFilter.fSubscription>
		<cfset local.freqID = local.SubReportFilter.changeLogFilter.fFreq>
		<cfset local.rateID = local.SubReportFilter.changeLogFilter.fRate>
		<cfset local.hasCard = local.SubReportFilter.changeLogFilter.fHasCardOnFile>
		<cfset local.associatedMemberID = local.SubReportFilter.changeLogFilter.associatedMemberID>
		<cfset local.associatedGroupID = local.SubReportFilter.changeLogFilter.associatedGroupID>
		<cfset local.linkedRecords = local.SubReportFilter.changeLogFilter.linkedRecords>
		<cfset local.updtByMemberID = local.SubReportFilter.changeLogFilter.updtByMemberID>
		<cfset local.updtByGroupID = local.SubReportFilter.changeLogFilter.updtByGroupID>
		<cfset local.updtByLinkedRecords = local.SubReportFilter.changeLogFilter.updtByLinkedRecords>
		<cfset local.subStatusFrom = local.SubReportFilter.changeLogFilter.fSubStatusFrom>
		<cfset local.subStatusTo = local.SubReportFilter.changeLogFilter.fSubStatusTo>
		<cfset local.activityFromDate = local.SubReportFilter.changeLogFilter.fActivityFrom>
		<cfset local.activityToDate = local.SubReportFilter.changeLogFilter.fActivityTo>
		<cfset local.recordedFromDate = local.SubReportFilter.changeLogFilter.fRecordedFrom>
		<cfset local.recordedToDate = local.SubReportFilter.changeLogFilter.fRecordedTo>

		<cfif len(local.startFromDate) gt 0>
			<cfset local.startFromDate = DateFormat(local.startFromDate, "m/d/yyyy")>
		</cfif>
		<cfif len(local.startToDate) gt 0>
			<cfset local.startToDate = DateFormat(local.startToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfif len(local.endFromDate) gt 0>
			<cfset local.endFromDate = DateFormat(local.endFromDate, "m/d/yyyy")>
		</cfif>
		<cfif len(local.endToDate) gt 0>
			<cfset local.endToDate = DateFormat(local.endToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>
		
		<cfif len(local.offerEndFromDate)>
			<cfset local.offerEndFromDate = DateFormat(local.offerEndFromDate, "m/d/yyyy")>
		</cfif>
		<cfif len(local.offerEndToDate)>
			<cfset local.offerEndToDate = DateFormat(local.offerEndToDate, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfif len(local.activityFromDate) gt 0>
			<cfset local.activityFromDate = DateTimeFormat(ParseDateTime("#replace(local.activityFromDate,' - ',' ')#"),"m/d/yyyy h:nn tt")>
		</cfif>
		<cfif len(local.activityToDate) gt 0>
			<cfset local.activityToDate = DateTimeFormat(ParseDateTime("#replace(local.activityToDate,' - ',' ')#"),"m/d/yyyy H:nn:59.997")>
		</cfif>

		<cfif len(local.recordedFromDate) gt 0>
			<cfset local.recordedFromDate = DateTimeFormat(ParseDateTime("#replace(local.recordedFromDate,' - ',' ')#"),"m/d/yyyy h:nn tt")>
		</cfif>
		<cfif len(local.recordedToDate) gt 0>
			<cfset local.recordedToDate = DateTimeFormat(ParseDateTime("#replace(local.recordedToDate,' - ',' ')#"),"m/d/yyyy H:nn:59.997")>
		</cfif>

		<cfset local.rfidList = ''>
		<cfif (val(local.rateID) gt 0) OR (val(local.freqID) gt 0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT rf.rfid
				FROM dbo.sub_rateFrequencies rf
				INNER JOIN dbo.sub_rates r ON r.rateID = rf.rateID
				<cfif val(local.rateID) gt 0>
					AND r.rateID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#" list="yes">)
				</cfif>
				<cfif val(local.freqID) gt 0>
					WHERE rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.freqID#">
				</cfif>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
			<cfif len(local.rfidList) eq 0>
				<cfset local.rfidList = 0>
			</cfif>
		</cfif>

		<cfif arguments.mode is 'grid'>
			<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
			<cfset local.orderDir = arguments.direct>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"m.lastName #local.orderDir#, m.firstName, m.memberNumber")>
			<cfset arrayAppend(local.arrCols,"tmp.[description] #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"tmp.previousStatus #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"tmp.updateDate #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"am.firstName #local.orderDir#, am.lastName, tmp.dateRecorded")>
			<cfset local.orderby = local.arrcols[arguments.orderby+1]>
		<cfelse>
			<cfset local.orderby = "m.lastName, m.firstName, m.memberNumber">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberChangeLogs" result="local.qrySubscriberChangeLogsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID INT, @orgID INT, @totalCount INT;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

				<cfif local.associatedMemberID gt 0>
					DECLARE @memberID INT;
					DECLARE @tblMembers AS TABLE (memberID INT PRIMARY KEY);

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedMemberID#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID
					
					<cfif local.linkedRecords is "all">
						UNION
						SELECT allchildMember.memberID
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>

				<cfif local.updtByMemberID gt 0>
					DECLARE @updtByMemberID INT;
					DECLARE @tblUpdtByMembers AS TABLE (memberID INT PRIMARY KEY);

					SET @updtByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.updtByMemberID#">;

					INSERT INTO @tblUpdtByMembers
					SELECT @updtByMemberID as memberID
					
					<cfif local.updtByLinkedRecords is "all">
						UNION
						SELECT allchildMember.memberID
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @updtByMemberID
					</cfif>
				</cfif>

				IF OBJECT_ID('tempdb..##tblSubscriberSearch') IS NOT NULL 
					DROP TABLE ##tblSubscriberSearch;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpSubReport') IS NOT NULL 
					DROP TABLE ##tmpSubReport;
				IF OBJECT_ID('tempdb..##tmpFinal') IS NOT NULL 
					DROP TABLE ##tmpFinal;
				CREATE TABLE ##tblSubscriberSearch (subscriberID INT, memberID INT, INDEX IX_tblSubscriberSearch (subscriberID,memberID));
				CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);

				INSERT INTO ##tblSubscriberSearch (subscriberID, memberID)
				SELECT s.subscriberID, m2.memberID
				FROM dbo.sub_subscribers s				
				INNER JOIN dbo.sub_subscriptions sub ON sub.subscriptionID = s.subscriptionID
					<cfif len(local.startFromDate) gt 0>
						AND s.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFromDate#">
					</cfif>
					<cfif len(local.startToDate) gt 0>
						AND s.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startToDate#">
					</cfif>
					<cfif len(local.endFromDate) gt 0>
						AND s.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFromDate#">
					</cfif>
					<cfif len(local.endToDate) gt 0>
						AND s.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endToDate#">
					</cfif>
					<cfif len(local.offerEndFromDate) gt 0>
						AND s.offerRescindDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndFromDate#">
					</cfif>
					<cfif len(local.offerEndToDate) gt 0>
						AND s.offerRescindDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndToDate#">
					</cfif>
					<cfif local.subID neq "0">
						AND sub.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
					</cfif>
					<cfif len(local.rfidList) gt 0>
						AND s.rfid IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
					</cfif>
					<cfif local.hasCard eq 'Y'>
						AND s.payProfileID IS NOT NULL
					<cfelseif local.hasCard eq 'N'>
						AND s.payProfileID IS NULL
					</cfif>
				INNER JOIN dbo.sub_statuses st ON st.statusID = s.statusID
					<cfif local.subStatus neq "0">
						AND st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatus#">
					</cfif>
				INNER JOIN dbo.sub_paymentStatuses rpst ON rpst.statusID = s.paymentStatusID
					<cfif local.subPaymentStatus neq "0">
						AND rpst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subPaymentStatus#">
					</cfif>
				INNER JOIN sub_types t ON t.typeID = sub.typeID
					<cfif (len(local.subType) gt 0) AND (local.subType neq "0")>
						AND t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subType#">
					</cfif>
					AND t.siteID = @siteID
				INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND s.memberID = m.memberID
				<cfif NOT local.associatedMemberID gt 0>
					INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID AND m2.status <> 'D'
				<cfelse>
					INNER JOIN @tblMembers AS m2 ON m2.memberID = m.activeMemberID
				</cfif>
				<cfif local.associatedGroupID gt 0>
					INNER JOIN dbo.cache_members_groups mg ON mg.memberID = m2.memberID AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedGroupID#">
				</cfif>
				;

				IF OBJECT_ID('tempdb..##tblSubscriberChangeLogs') IS NOT NULL 
					DROP TABLE ##tblSubscriberChangeLogs;

				CREATE TABLE ##tblSubscriberChangeLogs (dateRecorded DATETIME, updateDate DATETIME, [description] VARCHAR(1000), 
					subStartDate DATETIME, subEndDate DATETIME, previousStatus VARCHAR(30), subscriberID INT, rootSubscriberID INT,
					memberID INT, memberName VARCHAR(250), memberCompany VARCHAR(200), actorMemberName VARCHAR(250), actorMemberID INT, 
					actorMemberNumber VARCHAR(50), row INT);

				;WITH allChangeLogs AS (
					SELECT sh.dateRecorded, sh.updateDate,
						CASE 
							WHEN sh.oldStatusID IS NULL THEN subs.subscriptionName + ' was created'
							WHEN st.statusCode = 'A' THEN subs.subscriptionName + ' was made active'
							WHEN st.statusCode = 'I' THEN subs.subscriptionName + ' was made inactive'
							WHEN st.statusCode = 'O' THEN subs.subscriptionName + ' was changed to billed'
							WHEN st.statusCode = 'P' THEN subs.subscriptionName + ' was accepted'
							WHEN st.statusCode = 'E' THEN subs.subscriptionName + ' was expired'
							WHEN st.statusCode = 'X' THEN subs.subscriptionName + ' offer was expired'
							WHEN st.statusCode = 'D' THEN subs.subscriptionName + ' was deleted'
						ELSE '' END AS [description],
						s.subStartDate, s.subEndDate, CASE WHEN ost.statusID IS NOT NULL THEN ost.statusName ELSE 'N/A' END AS previousStatus,
						am.activeMemberID AS actorMemberID, s.subscriberID, s.rootSubscriberID, s.memberID
					FROM dbo.sub_statusHistory sh
					INNER JOIN dbo.sub_subscribers s ON s.orgID = @orgID and s.subscriberID = sh.subscriberID
					INNER JOIN dbo.ams_members m ON m.orgID = @orgID AND m.memberID = s.memberID
					INNER JOIN ##tblSubscriberSearch tblSub ON tblSub.subscriberID = s.subscriberID AND tblSub.memberID = m.activeMemberID
					INNER JOIN dbo.sub_subscriptions subs ON subs.orgID = @orgID and subs.subscriptionID = s.subscriptionID
					INNER JOIN dbo.sub_statuses st ON st.statusID = sh.statusID
						<cfif local.subStatusTo neq "0">
							AND st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatusTo#">
						</cfif>
					LEFT OUTER JOIN dbo.sub_statuses ost ON ost.statusID = sh.oldStatusID						
					INNER JOIN dbo.ams_members am ON am.memberID = sh.enteredByMemberID
					WHERE sh.orgID = @orgID
					<cfif local.subStatusFrom neq "0">
						AND ost.statusID IS NOT NULL AND ost.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatusFrom#">
					</cfif>

					<cfif local.subStatusFrom eq "0" and local.subStatusTo eq "0">
							UNION ALL
						SELECT psh.dateRecorded, psh.updateDate,
							subs.subscriptionName + ' - ' + ps.statusName AS [description],
							s.subStartDate, s.subEndDate, 'N/A' AS previousStatus,
							am.activeMemberID AS actorMemberID, s.subscriberID, s.rootSubscriberID, s.memberID
						FROM dbo.sub_paymentStatusHistory psh
						INNER JOIN dbo.sub_subscribers s ON s.subscriberID = psh.subscriberID
						INNER JOIN dbo.ams_members m ON m.orgID = @orgID AND m.memberID = s.memberID
						INNER JOIN ##tblSubscriberSearch tblSub ON tblSub.subscriberID = s.subscriberID AND tblSub.memberID = m.activeMemberID
						INNER JOIN dbo.sub_subscriptions subs ON subs.subscriptionID = s.subscriptionID
						INNER JOIN dbo.sub_paymentStatuses ps ON ps.statusID = psh.paymentStatusID
						INNER JOIN dbo.ams_members am ON am.memberID = psh.enteredByMemberID
						WHERE psh.orgID = @orgID
						AND psh.paymentStatusID IN (1,2)
					</cfif>
				)
				INSERT INTO ##tblSubscriberChangeLogs
				SELECT DISTINCT tmp.dateRecorded, tmp.updateDate, tmp.[description], tmp.subStartDate, tmp.subEndDate, tmp.previousStatus, tmp.subscriberID, tmp.rootSubscriberID,
					tmp.memberID, m.lastname + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName, m.company AS memberCompany,
					am.firstName + ' ' + am.lastName AS actorMemberName, am.memberID AS actorMemberID, am.memberNumber AS actorMemberNumber,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
				FROM allChangeLogs tmp
				INNER JOIN dbo.ams_members m ON m.orgID = @orgID AND m.memberID = tmp.memberID
				INNER JOIN dbo.ams_members am ON am.orgID IN (@orgID,1) and am.memberID = tmp.actorMemberID <!--- orgID 1 here because updater could be system or superuser --->
				<cfif local.updtByMemberID gt 0>
					INNER JOIN @tblUpdtByMembers AS m2 ON m2.memberID = am.memberID
				</cfif>
				<cfif local.updtByGroupID gt 0>
					INNER JOIN dbo.cache_members_groups mg ON mg.memberID = am.memberID
						AND mg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.updtByGroupID#">
				</cfif>
				WHERE 1=1				
				<cfif len(local.activityFromDate) gt 0>
					AND tmp.updateDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.activityFromDate#">
				</cfif>
				<cfif len(local.activityToDate) gt 0>
					AND tmp.updateDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.activityToDate#">
				</cfif>
				<cfif len(local.recordedFromDate) gt 0>
					AND tmp.dateRecorded >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.recordedFromDate#">
				</cfif>
				<cfif len(local.recordedToDate) gt 0>
					AND tmp.dateRecorded <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.recordedToDate#">
				</cfif>;

				SELECT @totalCount = @@ROWCOUNT;

				<cfif arguments.mode is 'grid'>
					DECLARE @posStart INT, @posStartAndCount INT;
					SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

					SELECT dateRecorded, updateDate, [description], subStartDate, subEndDate, previousStatus, subscriberID, rootSubscriberID,
						memberID, memberName, memberCompany, actorMemberName, actorMemberID, actorMemberNumber, row, @totalCount AS totalCount
					FROM ##tblSubscriberChangeLogs
					WHERE row > @posStart
					AND row <= @posStartAndCount
					ORDER BY row;
				<cfelseif arguments.mode is 'export'>
					DECLARE @fieldsetID int, @outputFieldsXML xml;
					SET @fieldsetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">;

					-- prep final data
					SELECT memberID, memberName as [Member Name], memberCompany as [Company], [description] as [Description], 
						subStartDate as [Subscription Start Date], subEndDate as [Subscription End Date], previousStatus as [Previous Status],
						dateRecorded as [Activity Date], actorMemberName as [Updated By], updateDate as [Updated Date], row
					INTO ##tmpSubReport
					FROM ##tblSubscriberChangeLogs;

					-- get fieldset data
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
						@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpSubReport', @membersResultTableName='##tmpMembers',
						@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

					SELECT [Member Name], [Company], [Description], [Subscription Start Date], [Subscription End Date], [Previous Status],
						[Activity Date], [Updated By], [Updated Date], row, m.* 
					INTO ##tmpFinal
					FROM ##tmpSubReport as tmp
					INNER JOIN ##tmpMembers as m on m.memberID = tmp.memberID;

					EXEC tempdb..sp_rename '##tmpFinal.memberID', 'MemberCentralID', 'COLUMN';
						
					DECLARE @selectsql varchar(max) = '
						SELECT *, row as mcCSVorder 
						*FROM* ##tmpFinal';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#arguments.folderPathUNC#/#arguments.reportFileName#",'\')#', @returnColumns=0;
				</cfif>

				IF OBJECT_ID('tempdb..##tblSubscriberChangeLogs') IS NOT NULL
					DROP TABLE ##tblSubscriberChangeLogs;
				IF OBJECT_ID('tempdb..##tblSubscriberSearch') IS NOT NULL
					DROP TABLE ##tblSubscriberSearch;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpSubReport') IS NOT NULL 
					DROP TABLE ##tmpSubReport;
				IF OBJECT_ID('tempdb..##tmpFinal') IS NOT NULL 
					DROP TABLE ##tmpFinal;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfif arguments.mode is 'grid'>
			<cfreturn local.qrySubscriberChangeLogs>
		</cfif>
	</cffunction>

	<cffunction name="getSubscriptionRateSchedules" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryRateSched = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRateSched">
			SELECT scheduleID, scheduleName
			FROM dbo.sub_rateSchedules
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND status = 'A'
			ORDER BY scheduleName
		</cfquery>

		<cfreturn qryRateSched>
	</cffunction>

	<cffunction name="getSubscriptionActivationOptions" access="public" output="false" returntype="query">

		<cfset var qryActivationOptions = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryActivationOptions">
			SELECT subActivationID, subActivationName, subActivationCode, uid
			FROM dbo.sub_activationOptions
			ORDER BY subActivationName
		</cfquery>

		<cfreturn qryActivationOptions>
	</cffunction>

	<cffunction name="getSubscriptionAppSettingsXML" access="public" output="false" returntype="xml">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryAppSettings = "">

		<cfquery name="qryAppSettings" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @settingsXML xml;

			SELECT @settingsXML = settingsXML
			FROM dbo.cms_applicationTypeSettings
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			AND applicationTypeID = dbo.fn_getApplicationTypeIDFromName('subscriptions');

			select isnull(@settingsXML,'<settings />') as settingsXML;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAppSettings.settingsXML>
	</cffunction>

	<cffunction name="hasViewSubscriptionSetupRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.siteResourceID = application.objSiteInfo.mc_siteInfo[arguments.siteCode].subscriptionAdminSiteResourceID>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>
		<cfreturn local.tmpRights.ViewSubscriptionSetup is 1>
	</cffunction>

	<cffunction name="getMemberSubscriptions" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="statusCodeList" type="string" required="true">

		<cfset var qrySubs = "">

		<cfquery name="qrySubs" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			EXEC dbo.sub_getMemberSubscriptions
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, 
				@orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@statusCodeList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusCodeList#">;
		</cfquery>

		<cfreturn qrySubs>
	</cffunction>

	<cffunction name="getSubscriptionIssuesEmail" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qrySubscriptionIssuesEmail = "">

		<cfquery name="qrySubscriptionIssuesEmail" datasource="#application.dsn.memberCentral.dsn#">
			SELECT subscriptionIssuesEmail
			FROM dbo.sites
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>

		<cfreturn qrySubscriptionIssuesEmail.subscriptionIssuesEmail>
	</cffunction>

</cfcomponent>