(window.webpackJsonp = window.webpackJsonp || []).push([
	[230],
	{
		1488: function (a, _, t) {
			a.exports = (function (a) {
				"use strict";
				var _ = (function (a) {
						return a && "object" == typeof a && "default" in a
							? a
							: { default: a };
					})(a),
					t = {
						name: "tr",
						weekdays:
							"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split(
								"_",
							),
						weekdaysShort: "Paz_Pts_Sal_Çar_Per_Cum_Cts".split("_"),
						weekdaysMin: "Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),
						months:
							"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split(
								"_",
							),
						monthsShort:
							"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağ<PERSON>_Eyl_Eki_Kas_Ara".split("_"),
						weekStart: 1,
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "DD.MM.YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY HH:mm",
							LLLL: "dddd, D MMMM YYYY HH:mm",
						},
						relativeTime: {
							future: "%s sonra",
							past: "%s önce",
							s: "birkaç saniye",
							m: "bir dakika",
							mm: "%d dakika",
							h: "bir saat",
							hh: "%d saat",
							d: "bir gün",
							dd: "%d gün",
							M: "bir ay",
							MM: "%d ay",
							y: "bir yıl",
							yy: "%d yıl",
						},
						ordinal: function (a) {
							return a + ".";
						},
					};
				return _.default.locale(t, null, !0), t;
			})(t(105));
		},
	},
]);
//# sourceMappingURL=chunk.230.js.map
