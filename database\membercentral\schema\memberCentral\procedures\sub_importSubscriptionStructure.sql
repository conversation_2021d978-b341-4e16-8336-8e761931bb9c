ALTER PROC dbo.sub_importSubscriptionStructure
@siteID int,
@recordedByMemberID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubFrequencies') IS NOT NULL
		DROP TABLE #tmpSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpSets') IS NOT NULL
		DROP TABLE #tmpSets;
	IF OBJECT_ID('tempdb..#tmpSubTypes') IS NOT NULL
		DROP TABLE #tmpSubTypes;
	IF OBJECT_ID('tempdb..#tmpSubscriptions') IS NOT NULL
		DROP TABLE #tmpSubscriptions;
	IF OBJECT_ID('tempdb..#tmpSubAddOns') IS NOT NULL
		DROP TABLE #tmpSubAddOns;
	IF OBJECT_ID('tempdb..#tmpSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpSubRates') IS NOT NULL
		DROP TABLE #tmpSubRates;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequencies') IS NOT NULL
		DROP TABLE #tmpSubRateFrequencies;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequenciesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpSubRateFrequenciesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpSiteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpNewSubRateGroups') IS NOT NULL
		DROP TABLE #tmpNewSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpNewSubContentObjs') IS NOT NULL
		DROP TABLE #tmpNewSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpUpdateSubContentObjs') IS NOT NULL
		DROP TABLE #tmpUpdateSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubFrequencies') IS NOT NULL
		DROP TABLE #tmpDeleteSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpDeleteSets') IS NOT NULL
		DROP TABLE #tmpDeleteSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubAddOns') IS NOT NULL
		DROP TABLE #tmpDeleteSubAddOns;
	IF OBJECT_ID('tempdb..#tmpDeleteSubTypes') IS NOT NULL
		DROP TABLE #tmpDeleteSubTypes;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRates') IS NOT NULL
		DROP TABLE #tmpDeleteSubRates;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqs') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptions') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptions;
	IF OBJECT_ID('tempdb..#tmpLogLabelsType') IS NOT NULL
		DROP TABLE #tmpLogLabelsType;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSub') IS NOT NULL
		DROP TABLE #tmpLogLabelsSub;
	IF OBJECT_ID('tempdb..#tmpLogLabelsAddOn') IS NOT NULL
		DROP TABLE #tmpLogLabelsAddOn;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSet') IS NOT NULL
		DROP TABLE #tmpLogLabelsSet;
	IF OBJECT_ID('tempdb..#tmpLogLabelsFreq') IS NOT NULL
		DROP TABLE #tmpLogLabelsFreq;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSched') IS NOT NULL
		DROP TABLE #tmpLogLabelsSched;
	IF OBJECT_ID('tempdb..#tmpLogLabelsRate') IS NOT NULL
		DROP TABLE #tmpLogLabelsRate;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
		DROP TABLE #tmpAuditLogMessages;

	CREATE TABLE #tmpSubFrequencies (syncFrequencyID int, frequencyID int, frequencyName varchar(50), frequency int, frequencyShortName varchar(10), 
		[uid] uniqueidentifier, rateRequired bit, hasInstallments bit, monthlyInterval int, isSystemRate bit, [status] char(1), finalAction char(1));
	CREATE TABLE #tmpSets (syncSetID int, setID int, setName varchar(100), [status] char(1), [uid] uniqueidentifier, finalAction char(1));
	CREATE TABLE #tmpSubRateSchedules (syncScheduleID int, scheduleID int, scheduleName varchar(200), [status] char(1), [uid] uniqueidentifier, finalAction char(1));
	CREATE TABLE #tmpSubTypes (syncTypeID int, typeID int, typeName varchar(100), [status] varchar(1), [uid] uniqueidentifier, typeCode varchar(30), 
		frontEndContent varchar(max), frontEndCompletedContent varchar(max), feEmailNotification varchar(400), frontEndContentID int, 
		frontEndCompletedContentID int, finalAction char(1));
	CREATE TABLE #tmpSubscriptions (syncSubscriptionID int, syncTypeID int, subscriptionID int, typeID int, [uid] uniqueidentifier, syncTypeUID uniqueidentifier,
		typeUID uniqueidentifier, subscriptionName varchar(300), oldSubscriptionName varchar(300), syncScheduleID int, scheduleUID uniqueidentifier, autoExpire bit, [status] char(1), 
		soldSeparately bit, GLAccountID int, rateTermDateFlag char(1), paymentOrder int, reportCode varchar(15), subActivationID int, 
		subAlternateActivationID int, allowRateGLAccountOverride bit, frontEndContent varchar(max), frontEndCompletedContent varchar(max), frontEndParentSubContent varchar(max),
		frontEndContentID int, frontEndCompletedContentID int, frontEndParentSubContentID int, finalAction char(1));
	CREATE TABLE #tmpSubRates (syncRateID int, syncScheduleID int, rateID int, scheduleID int, scheduleUID uniqueidentifier, [status] char(1), 
		rateStartDate datetime, rateEndDate datetime, rateStartDateAFID int, rateAFStartDate datetime, rateAFEndDate datetime, 
		termStartDate datetime, termEndDate datetime, termStartDateAFID int, termEndDateAFID int, termAFStartDate datetime, 
		termAFEndDate datetime, graceEndDate datetime, graceAFID int, rateName varchar(200), rateEndDateAFID int, 
		rateAdvanceOnTermEnd int, [uid] uniqueidentifier, isRenewalRate bit, forceUpfront bit, reportCode varchar(15),
		GLAccountID int, frontEndAllowChangePrice bit, linkedNonRenewalRateUID uniqueidentifier, fallbackRenewalRateUID uniqueidentifier, 
		keepChangedPriceOnRenewal bit, frontEndChangePriceMin decimal(18,2), frontEndChangePriceMax decimal(18,2),
		recogStartDate datetime, recogEndDate datetime, recogStartDateAFID int, recogEndDateAFID int, 
		recogAFStartDate datetime, recogAFEndDate datetime, rateOrder int, finalAction char(1));
	CREATE TABLE #tmpSubRateFrequencies (syncRFID int, rfid int, rateUID uniqueidentifier, frequencyUID uniqueidentifier, rateAmt decimal(18,2), 
		[status] varchar(1), numInstallments int, allowFrontEnd bit, finalAction char(1));
	CREATE TABLE #tmpSubRateFrequenciesMerchantProfiles (syncRFMPID int, rfmpid int, syncRFID int, profileID int, [status] char(1), finalAction char(1));
	CREATE TABLE #tmpSiteSubRateGroups (resourceRightsID int, rateID int, rateUID uniqueidentifier, groupID int);
	CREATE TABLE #tmpNewSubRateGroups (autoID int IDENTITY(1,1), syncRateID int, rateUID uniqueidentifier, groupID int, include bit);
	CREATE TABLE #tmpSubAddOns (syncAddonID int, addonID int, syncSubscriptionID int, subscriptionUID uniqueidentifier, syncChildSetID int, 
		syncChildSetUID uniqueidentifier, orderNum smallint, minAllowed smallint, maxAllowed smallint, useAcctCodeInSet bit, 
		useTermEndDateInSet bit, PCnum smallint, PCPctOffEach smallint, 
		frontEndAllowSelect bit, frontEndAllowChangePrice bit, frontEndContent varchar(max), frontEndContentID int, 
		frontEndAddAdditional bit, finalAction char(1));
	CREATE TABLE #tmpSubscriptionSets (syncSubscriptionSetID int, subscriptionSetID int, subscriptionUID uniqueidentifier, setUID uniqueidentifier, orderNum int, finalAction char(1));
	CREATE TABLE #tmpNewSubContentObjs (autoID int IDENTITY(1,1), rawContent varchar(max), itemID int, itemType varchar(10));
	CREATE TABLE #tmpUpdateSubContentObjs (contentID int, rawContent varchar(max), areaCode varchar(100), fieldName varchar(100), refID int);
	CREATE TABLE #tmpDeleteSubFrequencies (frequencyID int);
	CREATE TABLE #tmpDeleteSets (setID int);
	CREATE TABLE #tmpDeleteSubscriptionSets (subscriptionSetID int);
	CREATE TABLE #tmpDeleteSubAddOns (addonID int);
	CREATE TABLE #tmpDeleteSubTypes (typeID int);
	CREATE TABLE #tmpDeleteSubscriptions (subscriptionID int);
	CREATE TABLE #tmpDeleteSubRateSchedules (scheduleID int);
	CREATE TABLE #tmpDeleteSubRates (rateID int);
	CREATE TABLE #tmpDeleteSubRateFreqs (rfid int);
	CREATE TABLE #tmpDeleteSubRateFreqMerchantProfiles (rfmpid int);
	CREATE TABLE #tmpDeleteSubRateGroups (resourceRightsID int, siteResourceID int);

	CREATE TABLE #tmpLogLabelsType (typeID int PRIMARY KEY, typeName varchar(100));
	CREATE TABLE #tmpLogLabelsSub (subscriptionID int PRIMARY KEY, subscriptionName varchar(300), typeID int, typeName varchar(100));
	CREATE TABLE #tmpLogLabelsAddOn (addonID int PRIMARY KEY, subscriptionID varchar(300), childSetID int);
	CREATE TABLE #tmpLogLabelsSet (setID int PRIMARY KEY, setName varchar(100));
	CREATE TABLE #tmpLogLabelsFreq (frequencyID int PRIMARY KEY, frequencyName varchar(50));
	CREATE TABLE #tmpLogLabelsSched (scheduleID int PRIMARY KEY, scheduleName varchar(200));
	CREATE TABLE #tmpLogLabelsRate (rateID int PRIMARY KEY, rateName varchar(200), scheduleID int, scheduleName varchar(200));
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), areaCode varchar(100), msg VARCHAR(max), refID int, actionType varchar(10));
	CREATE TABLE #tmpAuditLogMessages (rowID INT IDENTITY(1,1), refID int, msg varchar(max), col varchar(max));

	DECLARE @orgID int, @orgcode varchar(10), @sitecode varchar(10), @defaultLanguageID int, @minTypeID int, @newSubTypeID int,
		@typeName varchar(100), @typeCode varchar(30), @feRawContent varchar(max), @feCompletedRawContent varchar(max), 
		@feEmailNotification varchar(max), @uid uniqueidentifier, @minContentID int, @rawContent varchar(max), @minRateID int, 
		@scheduleID int, @rateName varchar(100), @reportCode varchar(15), @status char(1), @rateStartDate datetime, 
		@rateEndDate datetime, @rateStartAFID int, @rateEndAFID int, @termStartDate datetime, @termEndDate datetime, 
		@termStartAFID int, @termEndAFID int, @graceEndDate datetime, @graceEndAFID int, @recogStartDate datetime, 
		@recogEndDate datetime, @recogStartAFID int, @recogEndAFID int, @rateAdvanceOnTermEnd int, @isRenewalRate int, 
		@forceUpfront int, @accountID int, @frontEndAllowChangePrice int, @linkedNonRenewalRateID int, @fallbackRenewalRateID int, 
		@keepChangedPriceOnRenewal int, @frontEndChangePriceMin decimal(18,2), @frontEndChangePriceMax decimal(18,2), @newRateID int, 
		@newSiteResourceID int, @rateAFStartDate datetime, @rateAFEndDate datetime, @termAFStartDate datetime, @termAFEndDate datetime, 
		@recogAFStartDate datetime, @recogAFEndDate datetime, @minSubID int, @minSetID int,
		@typeID int, @subName varchar(300), @newSubsID int, @rateTermDateFlag varchar(1), @autoExpire bit,
		@soldSeparately bit, @paymentOrder int, @GLAccountID int, @allowRateGLOverride bit, @activationOptionCode varchar(1),
		@alternateActivationOptionCode varchar(1), @subAdminResourceID int, @appCreatedContentResourceTypeID int,
		@contentID int, @contentSRID int, @minAutoID int, @itemID int, @itemType varchar(10), @minResourceRightsID int,
		@include bit, @groupID int, @siteResourceID int, @activeTypeGroupID int, @waitingTypeGroupID int, @expiredTypeGroupID int, @renewableTypeGroupID int, 
		@pendingTypeGroupID int, @activeGroupID int, @waitingGroupID int, @expiredGroupID int, @renewableGroupID int, @pendingGroupID int, @feParentSubRawContent varchar(max),
		@crlf varchar(10), @thisAreaCode varchar(100), @minRefID int, @msgjson varchar(max), @subKeyMapJSON varchar(100), @actionCount int, @changesCount int, @prefixMsg varchar(max),
		@actionType varchar(10), @groupPathExpanded varchar(max), @rateID int;

	DECLARE @insertedEntries TABLE (refID INT);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = o.orgID, @orgcode = o.orgcode, @sitecode = s.sitecode,
		@defaultLanguageID = s.defaultLanguageID, @subAdminResourceID = s.subscriptionAdminSiteResourceID
	FROM dbo.sites as s
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	-- frequencies
	INSERT INTO #tmpSubFrequencies (syncFrequencyID, frequencyID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, 
		hasInstallments, monthlyInterval, isSystemRate, [status], finalAction)
	select distinct ssf.frequencyID, sf.frequencyID, ssf.frequencyName, ssf.frequency, ssf.frequencyShortName, ssf.[uid], 
		ssf.rateRequired, ssf.hasInstallments, ssf.monthlyInterval, ssf.isSystemRate, ssf.[status], ssf.finalAction
	from datatransfer.dbo.sync_sub_frequencies as ssf
	left outer join dbo.sub_frequencies as sf on sf.siteID = @siteID and sf.[uid] = ssf.[uid]
	where ssf.siteID = @siteID
	and ssf.finalAction in ('A','C');

	-- sets
	INSERT INTO #tmpSets (syncSetID, setID, setName, [status], [uid], finalAction)
	select distinct ss.setID, s.setID, ss.setName, ss.[status], ss.uid, ss.finalAction
	from datatransfer.dbo.sync_sub_sets as ss
	left outer join dbo.sub_sets as s on s.siteID = @siteID and s.[uid] = ss.[uid] and s.status <> 'D'
	where ss.siteID = @siteID
	and ss.finalAction in ('A','C');

	-- subTypes
	INSERT INTO #tmpSubTypes (syncTypeID, typeID, typeName, [status], [uid], typeCode, frontEndContent, frontEndCompletedContent, 
		feEmailNotification, frontEndContentID, frontEndCompletedContentID, finalAction)
	select distinct st.typeID, t.typeID, st.typeName, st.[status], st.[uid], st.typeCode, st.frontEndContent, st.frontEndCompletedContent,
		st.feEmailNotification, t.frontEndContentID, t.frontEndCompletedContentID, st.finalAction
	from datatransfer.dbo.sync_sub_types as st
	left outer join dbo.sub_types as t on t.siteID = @siteID and t.status <> 'D' and t.[uid] = st.[uid]
	where st.siteID = @siteID
	and st.finalAction in ('A','C');

	-- rate schedules
	INSERT INTO #tmpSubRateSchedules (syncScheduleID, scheduleID, scheduleName, [status], [uid], finalAction)
	select distinct srs.scheduleID, rs.scheduleID, srs.scheduleName, srs.[status], srs.[uid], srs.finalAction
	from datatransfer.dbo.sync_sub_rateSchedules as srs
	left outer join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = srs.[uid] and rs.status <> 'D'
	where srs.siteID = @siteID
	and srs.finalAction in ('A','C');

	-- rates
	INSERT INTO #tmpSubRates (syncRateID, syncScheduleID, rateID, scheduleID, scheduleUID, [status], 
		rateStartDate, rateEndDate, rateStartDateAFID, rateAFStartDate, rateAFEndDate, 
		termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, 
		termAFEndDate, graceEndDate, graceAFID, rateName, rateEndDateAFID, 
		rateAdvanceOnTermEnd, [uid], isRenewalRate, forceUpfront, reportCode,
		GLAccountID, frontEndAllowChangePrice, linkedNonRenewalRateUID, fallbackRenewalRateUID, 
		keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax,
		recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, 
		recogAFStartDate, recogAFEndDate, rateOrder, finalAction)
	select distinct sync_sr.rateID, sync_sr.scheduleID, r.rateID, r.scheduleID, sync_srs.[uid], sync_sr.[status],
		sync_sr.rateStartDate, sync_sr.rateEndDate, sync_rateStartDateAFID.useID, 
		sync_sr.rateAFStartDate, sync_sr.rateAFEndDate, sync_sr.termStartDate, sync_sr.termEndDate, 
		sync_termStartDateAFID.useID, sync_termEndDateAFID.useID, sync_sr.termAFStartDate, 
		sync_sr.termAFEndDate, sync_sr.graceEndDate, sync_graceAFID.useID, sync_sr.rateName, 
		sync_rateEndDateAFID.useID, sync_sr.rateAdvanceOnTermEnd, sync_sr.[uid], 
		sync_sr.isRenewalRate, sync_sr.forceUpfront, sync_sr.reportCode, sync_GLAccountID.useID, 
		sync_sr.frontEndAllowChangePrice, sync_linkedNonRenewalRateID.[uid], sync_fallbackRenewalRateID.[uid], 
		sync_sr.keepChangedPriceOnRenewal, sync_sr.frontEndChangePriceMin, sync_sr.frontEndChangePriceMax, sync_sr.recogStartDate, 
		sync_sr.recogEndDate, sync_recogStartDateAFID.useID, sync_recogEndDateAFID.useID, sync_sr.recogAFStartDate, 
		sync_sr.recogAFEndDate, sync_sr.rateOrder, sync_sr.finalAction
	from dataTransfer.dbo.sync_sub_rates as sync_sr
	inner join datatransfer.dbo.sync_sub_rateSchedules as sync_srs on sync_srs.siteID = @siteID and sync_srs.scheduleID = sync_sr.scheduleID
	left outer join dbo.sub_rates as r on r.[uid] = sync_sr.[uid] and r.[status] <> 'D'
	left outer join datatransfer.dbo.sync_sub_supporting as sync_rateStartDateAFID on sync_rateStartDateAFID.siteID = @siteID 
		and sync_rateStartDateAFID.cat = 'af' 
		and sync_rateStartDateAFID.itemID = sync_sr.rateStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_termStartDateAFID on sync_termStartDateAFID.siteID = @siteID 
		and sync_termStartDateAFID.cat = 'af' 
		and sync_termStartDateAFID.itemID = sync_sr.termStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_termEndDateAFID on sync_termEndDateAFID.siteID = @siteID 
		and sync_termEndDateAFID.cat = 'af' 
		and sync_termEndDateAFID.itemID = sync_sr.termEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_graceAFID on sync_graceAFID.siteID = @siteID 
		and sync_graceAFID.cat = 'af' 
		and sync_graceAFID.itemID = sync_sr.graceAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_rateEndDateAFID on sync_rateEndDateAFID.siteID = @siteID 
		and sync_rateEndDateAFID.cat = 'af' 
		and sync_rateEndDateAFID.itemID = sync_sr.rateEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_recogStartDateAFID on sync_recogStartDateAFID.siteID = @siteID 
		and sync_recogStartDateAFID.cat = 'af' 
		and sync_recogStartDateAFID.itemID = sync_sr.recogStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_recogEndDateAFID on sync_recogEndDateAFID.siteID = @siteID 
		and sync_recogEndDateAFID.cat = 'af' 
		and sync_recogEndDateAFID.itemID = sync_sr.recogEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_GLAccountID on sync_GLAccountID.siteID = @siteID 
		and sync_GLAccountID.cat = 'gl' 
		and sync_GLAccountID.itemID = sync_sr.GLAccountID
	left outer join dataTransfer.dbo.sync_sub_rates as sync_linkedNonRenewalRateID on sync_linkedNonRenewalRateID.siteID = @siteID 
		and sync_linkedNonRenewalRateID.rateID = sync_sr.linkedNonRenewalRateID
	left outer join dataTransfer.dbo.sync_sub_rates as sync_fallbackRenewalRateID on sync_fallbackRenewalRateID.siteID = @siteID 
		and sync_fallbackRenewalRateID.rateID = sync_sr.fallbackRenewalRateID
	where sync_sr.siteID = @siteID
	and sync_sr.finalAction in ('A','C');

	-- subscriptions
	INSERT INTO #tmpSubscriptions (syncSubscriptionID, syncTypeID, subscriptionID, typeID, subscriptionName,
		[uid], syncTypeUID, typeUID, syncScheduleID, scheduleUID, autoExpire, [status], soldSeparately, 
		GLAccountID, rateTermDateFlag, paymentOrder, reportCode, subActivationID, 
		subAlternateActivationID, allowRateGLAccountOverride, frontEndContent, frontEndCompletedContent, 
		frontEndParentSubContent, oldSubscriptionName, frontEndContentID, frontEndCompletedContentID, 
		frontEndParentSubContentID, finalAction)
	select distinct s.subscriptionID, s.typeID, sub.subscriptionID, sub.typeID, s.subscriptionName,
		s.[uid], t.[uid], st.[uid], s.scheduleID, rs.[uid], s.autoExpire, s.[status], s.soldSeparately, 
		ss.useID, s.rateTermDateFlag, s.paymentOrder, s.reportCode, s.subActivationID, 
		s.subAlternateActivationID, s.allowRateGLAccountOverride, s.frontEndContent, s.frontEndCompletedContent,
		s.frontEndParentSubContent, sub.subscriptionName, sub.frontEndContentID, sub.frontEndCompletedContentID, 
		sub.frontEndParentSubContentID, s.finalAction
	from datatransfer.dbo.sync_sub_subscriptions as s
	inner join datatransfer.dbo.sync_sub_types as t on t.typeID = s.typeID and t.siteID = @siteID
	inner join datatransfer.dbo.sync_sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = s.scheduleID
	left outer join datatransfer.dbo.sync_sub_supporting as ss on ss.siteID = @siteID and ss.cat = 'gl' and ss.itemID = s.GLAccountID
	left outer join dbo.sub_subscriptions as sub
		inner join dbo.sub_types as st on st.typeID = sub.typeID and st.siteID = @siteID
		on sub.[uid] = s.[uid]
	where s.siteID = @siteID
	and s.finalAction in ('A','C');

	-- sub addons
	INSERT INTO #tmpSubAddOns (syncAddonID, addonID, syncSubscriptionID, subscriptionUID, syncChildSetID, syncChildSetUID, 
		orderNum, minAllowed, maxAllowed, useAcctCodeInSet, useTermEndDateInSet, PCnum, PCPctOffEach, frontEndAllowSelect, 
		frontEndAllowChangePrice, frontEndContent, frontEndContentID, frontEndAddAdditional, finalAction)
	select distinct sao.addonID, ao.addonID, sao.subscriptionID, subs.[uid], sao.childSetID, cs.[uid], sao.orderNum, 
		sao.minAllowed, sao.maxAllowed, sao.useAcctCodeInSet, sao.useTermEndDateInSet, sao.PCnum, sao.PCPctOffEach, 
		sao.frontEndAllowSelect, sao.frontEndAllowChangePrice, sao.frontEndContent, ao.frontEndContentID, 
		sao.frontEndAddAdditional, sao.finalAction
	from datatransfer.dbo.sync_sub_addons as sao
	inner join datatransfer.dbo.sync_sub_sets as cs on cs.setID = sao.childSetID 
		and cs.siteID = @siteID
		and cs.status <> 'D'
	inner join datatransfer.dbo.sync_sub_subscriptions as subs on subs.subscriptionID = sao.subscriptionID 
		and subs.siteID = @siteID
		and subs.status <> 'D'
	left outer join dbo.sub_addons as ao on ao.addonID = sao.useID
	where sao.siteID = @siteID
	and sao.finalAction in ('A','C');

	-- new rate frequencies
	INSERT INTO #tmpSubRateFrequencies (syncRFID, rfid, rateUID, frequencyUID, rateAmt, [status], numInstallments, allowFrontEnd, finalAction)
	select distinct rf.rfid, srf.rfid, r.[uid], f.[uid], rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd, rf.finalAction
	from datatransfer.dbo.sync_sub_rateFrequencies as rf
	inner join datatransfer.dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rf.rateID and r.status <> 'D'
	inner join datatransfer.dbo.sync_sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID 
		and f.status <> 'D'
	left outer join dbo.sub_rateFrequencies as srf on srf.rfid = rf.useID
	where rf.siteID = @siteID
	and rf.status <> 'D'
	and rf.finalAction in ('A','C');

	-- new rate frequency merchant profiles
	INSERT INTO #tmpSubRateFrequenciesMerchantProfiles (syncRFMPID, rfmpid, syncRFID, profileID, [status], finalAction)
	select distinct srfmp.rfmpid, rfmp.rfmpid, srfmp.rfid, ss.useID, srfmp.[status], srfmp.finalAction
	from datatransfer.dbo.sync_sub_rateFrequenciesMerchantProfiles as srfmp
	inner join datatransfer.dbo.sync_sub_rateFrequencies as srf on srf.siteID = @siteID and srf.rfid = srfmp.rfid
	inner join datatransfer.dbo.sync_sub_supporting as ss on ss.siteID = @siteID and ss.cat = 'mp' and ss.itemID = srfmp.profileID
	left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = srfmp.useID and rfmp.[status] <> 'D'
	where srfmp.siteID = @siteID
	and srfmp.finalAction = 'A';

	-- site rate groups
	INSERT INTO #tmpSiteSubRateGroups (resourceRightsID, rateID, rateUID, groupID)
	select srr.resourceRightsID, r.rateID, r.[uid], g.groupID
	from dbo.sub_rates as r
	inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID and rs.siteID = @siteID
	inner join dbo.cms_siteResourceRights as srr on srr.resourceID = r.siteResourceID and srr.siteID = @siteID and srr.functionID = @qualifyFID
	inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = srr.groupID and g.status <> 'D'
	where r.status <> 'D';

	-- sync rate groups
	INSERT INTO #tmpNewSubRateGroups (syncRateID, rateUID, groupID, include)
	select rateID, rateUID, useGroupID, include
	from datatransfer.dbo.sync_sub_rateGroups
	where siteID = @siteID
	and useGroupID is not null
	and finalAction = 'A';

	-- subscription sets
	INSERT INTO #tmpSubscriptionSets (syncSubscriptionSetID, subscriptionSetID, subscriptionUID, setUID, orderNum, finalAction)
	select distinct ss.subscriptionSetID, ss.useID, subs.[uid], s.[uid], ss.orderNum, ss.finalAction
	from datatransfer.dbo.sync_sub_subscriptionSets as ss
	inner join datatransfer.dbo.sync_sub_sets as s on s.setID = ss.setID and s.siteID = @siteID
	inner join datatransfer.dbo.sync_sub_subscriptions as subs on subs.siteID = @siteID and subs.subscriptionID = ss.subscriptionID
	where ss.siteID = @siteID
	and ss.finalAction in ('A','C');
	
	-- delete frequencies
	INSERT INTO #tmpDeleteSubFrequencies (frequencyID)
	select distinct sf.frequencyID
	from dbo.sub_frequencies as sf
	left outer join dataTransfer.dbo.sync_sub_frequencies as ssf on ssf.siteID = @siteID and ssf.uid = sf.uid
	where sf.siteID = @siteID
	and sf.[status] <> 'D'
	and ssf.frequencyID is null;

	-- delete sets
	INSERT INTO #tmpDeleteSets (setID)
	select distinct s.setID
	from dbo.sub_sets as s
	left outer join dataTransfer.dbo.sync_sub_sets as ss on ss.siteID = @siteID and ss.uid = s.uid
	where s.siteID = @siteID
	and ss.setID is null;

	IF EXISTS (select 1 from #tmpDeleteSets) BEGIN
		-- delete sub sets
		INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
		select ss.subscriptionSetID
		from dbo.sub_subscriptionSets as ss
		inner join #tmpDeleteSets as tmp on tmp.setID = ss.setID;

		-- delete sub addons
		INSERT INTO #tmpDeleteSubAddOns (addonID)
		select sa.addonID
		from dbo.sub_addons as sa
		inner join #tmpDeleteSets as tmp on tmp.setID = sa.childSetID;
	END

	-- delete subTypes
	INSERT INTO #tmpDeleteSubTypes (typeID)
	select distinct t.typeID
	from dbo.sub_types as t
	left outer join dataTransfer.dbo.sync_sub_types as st on st.siteID = @siteID and st.uid = t.uid
	where t.siteID = @siteID
	and t.[status] <> 'D'
	and st.typeID is null;

	-- delete subscriptions
	INSERT INTO #tmpDeleteSubscriptions (subscriptionID)
	select s.subscriptionID
	from dbo.sub_subscriptions as s
	inner join #tmpDeleteSubTypes as tmp on tmp.typeID = s.typeID
	where s.[status] <> 'D';

	INSERT INTO #tmpDeleteSubscriptions (subscriptionID)
	select distinct s.subscriptionID
	from dbo.sub_subscriptions as s
	inner join dbo.sub_types as st on st.siteID = @siteID and st.typeID = s.typeID and st.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_subscriptions as ss on ss.siteID = @siteID and ss.uid = s.uid
	where ss.subscriptionID is null
	and s.[status] <> 'D'
		except
	select subscriptionID
	from #tmpDeleteSubscriptions;

	-- delete sub sets
	INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
	select s.subscriptionSetID
	from dbo.sub_subscriptionSets as s
	inner join dbo.sub_sets as st on st.setID = s.setID and st.siteID = @siteID
	left outer join dataTransfer.dbo.sync_sub_subscriptionSets as ss on ss.siteID = @siteID and ss.useID = s.subscriptionSetID
	where ss.subscriptionSetID is null
		except
	select subscriptionSetID
	from #tmpDeleteSubscriptionSets;

	INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
	select ss.subscriptionSetID
	from dbo.sub_subscriptionSets as ss
	inner join #tmpDeleteSubscriptions as tmp on tmp.subscriptionID = ss.subscriptionID
		except
	select subscriptionSetID
	from #tmpDeleteSubscriptionSets;

	-- delete sub addons
	INSERT INTO #tmpDeleteSubAddOns (addonID)
	select sa.addonID
	from dbo.sub_addons as sa
	inner join #tmpDeleteSubscriptions as tmp on tmp.subscriptionID = sa.subscriptionID
		except
	select addonID
	from #tmpDeleteSubAddOns;

	INSERT INTO #tmpDeleteSubAddOns (addonID)
	select distinct ao.addonID
	from dbo.sub_addons as ao
	inner join dbo.sub_sets as s on s.setID = ao.childSetID 
		and s.siteID = @siteID
		and s.status <> 'D'
	inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ao.subscriptionID and subs.status <> 'D'
	left outer join datatransfer.dbo.sync_sub_addons as sao on sao.siteID = @siteID and sao.useID = ao.addonID
	where sao.addonID is null
		except
	select addonID
	from #tmpDeleteSubAddOns;

	-- delete rate schedules
	INSERT INTO #tmpDeleteSubRateSchedules (scheduleID)
	select distinct rs.scheduleID
	from dbo.sub_rateSchedules as rs
	left outer join dataTransfer.dbo.sync_sub_rateSchedules as srs on srs.siteID = @siteID and srs.uid = rs.uid
	where rs.siteID = @siteID
	and srs.scheduleID is null
	and rs.[status] <> 'D';

	-- delete rates
	INSERT INTO #tmpDeleteSubRates (rateID)
	select distinct r.rateID
	from dbo.sub_rates as r
	inner join #tmpDeleteSubRateSchedules as rs on rs.scheduleID = r.scheduleID
	where r.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRates (rateID)
	select distinct r.rateID
	from dbo.sub_rates as r
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rates as sr on sr.siteID = @siteID and sr.uid = r.uid
	where sr.rateID is null
	and r.[status] <> 'D'
		except
	select rateID
	from #tmpDeleteSubRates;

	-- delete rate freqs
	INSERT INTO #tmpDeleteSubRateFreqs (rfid)
	select distinct rf.rfid
	from dbo.sub_rateFrequencies as rf
	inner join #tmpDeleteSubRates as tmp on tmp.rateID = rf.rateID
	where rf.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRateFreqs (rfid)
	select distinct rf.rfid
	from dbo.sub_rateFrequencies as rf
	inner join dbo.sub_rates as r on r.rateID = rf.rateID
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rateFrequencies as srf on srf.siteID = @siteID and srf.useID = rf.rfid
	where srf.rfid is null
	and rf.[status] <> 'D'
		except
	select rfid
	from #tmpDeleteSubRateFreqs;

	-- delete rate freq merchant profiles
	INSERT INTO #tmpDeleteSubRateFreqMerchantProfiles (rfmpid)
	select distinct rfmp.rfmpid
	from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
	inner join #tmpDeleteSubRateFreqs as tmp on tmp.rfid = rfmp.rfid
	where rfmp.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRateFreqMerchantProfiles (rfmpid)
	select distinct rfmp.rfmpid
	from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
	inner join dbo.sub_rateFrequencies as rf on rf.rfid = rfmp.rfid and rf.[status] <> 'D'
	inner join dbo.sub_rates as r on r.rateID = rf.rateID
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rateFrequenciesMerchantProfiles as srfmp on srfmp.siteID = @siteID and srfmp.useID = rfmp.rfmpid
	where srfmp.rfmpid is null
	and rfmp.[status] <> 'D'
		except
	select rfmpid
	from #tmpDeleteSubRateFreqMerchantProfiles;

	-- remove rate groups
	INSERT INTO #tmpDeleteSubRateGroups (resourceRightsID, siteResourceID)
	select distinct tmp.resourceRightsID, r.siteResourceID
	from #tmpSiteSubRateGroups as tmp
	inner join dbo.sub_rates as r on r.rateID = tmp.rateID
	left outer join dataTransfer.dbo.sync_sub_rateGroups as srg on srg.siteID = @siteID and srg.useGroupID = tmp.groupID and srg.rateUID = tmp.rateUID
	where srg.groupID is null;

	-- store labels to be used in logging - before updating entries
	INSERT INTO #tmpLogLabelsType (typeID, typeName)
	SELECT typeID, typeName
	FROM dbo.sub_types
	WHERE siteID = @siteID
	AND [status] <> 'D';

	INSERT INTO #tmpLogLabelsSub (subscriptionID, subscriptionName, typeID, typeName)
	SELECT s.subscriptionID, s.subscriptionName, t.typeID, t.typeName
	FROM dbo.sub_subscriptions AS s
	INNER JOIN dbo.sub_types AS t ON t.typeID = s.typeID
	WHERE s.orgID = @orgID
	AND s.[status] <> 'D';

	INSERT INTO #tmpLogLabelsAddOn (addonID, subscriptionID, childSetID)
	SELECT ao.addonID, ao.subscriptionID, ao.childSetID
	FROM dbo.sub_addons AS ao
	INNER JOIN dbo.sub_subscriptions AS s ON s.subscriptionID = ao.subscriptionID
		AND s.orgID = @orgID
		AND s.[status] <> 'D';

	INSERT INTO #tmpLogLabelsSet (setID, setName)
	SELECT setID, setName
	FROM dbo.sub_sets
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsFreq (frequencyID, frequencyName)
	SELECT frequencyID, frequencyName
	FROM dbo.sub_frequencies
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsSched (scheduleID, scheduleName)
	SELECT scheduleID, scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsRate (rateID, rateName, scheduleID, scheduleName)
	SELECT r.rateID, r.rateName, sch.scheduleID, sch.scheduleName
	FROM dbo.sub_rates AS r
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
		AND sr.siteID = @siteID
	INNER JOIN dbo.sub_rateSchedules AS sch ON sch.scheduleID = r.scheduleID
	WHERE r.[status] <> 'D';

	-- new content objects
	INSERT INTO #tmpNewSubContentObjs (rawContent, itemID, itemType)
	select frontEndContent, syncAddonID, 'ao'
	from #tmpSubAddOns
	where finalAction = 'A';

	select @minAutoID = min(autoID) from #tmpNewSubContentObjs;
	while @minAutoID is not null BEGIN
		select @rawContent = null, @itemID = null, @itemType = null, @contentID = null, @contentSRID = null;

		select @rawContent = rawContent, @itemID = itemID, @itemType = itemType
		from #tmpNewSubContentObjs
		where autoID = @minAutoID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@rawContent, @memberID=@recordedByMemberID,	@contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		IF @itemType = 'ao'
			UPDATE #tmpSubAddOns
			SET frontEndContentID = @contentID
			WHERE syncAddonID = @itemID;

		select @minAutoID = min(autoID) from #tmpNewSubContentObjs where autoID > @minAutoID;
	END

	-- update content objects
	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBTYPE', 'Front End Content', tmp.typeID
	from #tmpSubTypes as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndCompletedContentID, tmp.frontEndCompletedContent, 'SUBTYPE', 'Front End Completed Content', tmp.typeID
	from #tmpSubTypes as tmp
	cross apply dbo.fn_getContent(tmp.frontEndCompletedContentID,@defaultLanguageID) as frontEndCompletedContent
	where tmp.finalAction = 'C'
	and frontEndCompletedContent.rawContent <> tmp.frontEndCompletedContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBSCRIPTION', 'Front End Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndParentSubContentID, tmp.frontEndParentSubContent, 'SUBSCRIPTION', 'Front End Parent Subscription Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndParentSubContentID,@defaultLanguageID) as frontEndParentSubContent
	where tmp.finalAction = 'C'
	and frontEndParentSubContent.rawContent <> tmp.frontEndParentSubContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndCompletedContentID, tmp.frontEndCompletedContent, 'SUBSCRIPTION', 'Front End Completed Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndCompletedContentID,@defaultLanguageID) as frontEndCompletedContent
	where tmp.finalAction = 'C'
	and frontEndCompletedContent.rawContent <> tmp.frontEndCompletedContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBADDON', 'Front End Content', tmp.addonID
	from #tmpSubAddOns as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
	SELECT areaCode, fieldName + ' updated.', refID, 'UPDATE'
	FROM #tmpUpdateSubContentObjs;

	select @minContentID = min(contentID) from #tmpUpdateSubContentObjs;
	while @minContentID is not null BEGIN
		select @rawContent = null;

		select @rawContent = rawContent
		from #tmpUpdateSubContentObjs
		where contentID = @minContentID;

		EXEC dbo.cms_updateContent @contentID=@minContentID, @languageID=@defaultLanguageID, @isHTML=1, @contentTitle='', 
			@contentDesc='', @rawContent=@rawContent, @memberID=@recordedByMemberID;

		select @minContentID = min(contentID) from #tmpUpdateSubContentObjs where contentID > @minContentID;
	END


	EXEC dbo.cache_perms_setStatus @orgID=@orgID, @status='disabled-needsRebuild';

	-- delete sub sets
	IF EXISTS (select 1 from #tmpDeleteSubscriptionSets) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', 'Subscription(s) ' + STRING_AGG(QUOTENAME(sub.subscriptionName),', ') + ' removed from set.', ss.setID, 'UPDATE'
		FROM #tmpDeleteSubscriptionSets AS tmp
		INNER JOIN dbo.sub_subscriptionSets as ss on ss.subscriptionSetID = tmp.subscriptionSetID
		INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
		LEFT OUTER JOIN #tmpDeleteSets as tmpDel on tmpDel.setID = ss.setID
		WHERE tmpDel.setID IS NULL
		GROUP BY ss.setID;

		DELETE ss
		FROM dbo.sub_subscriptionSets as ss
		INNER JOIN #tmpDeleteSubscriptionSets as tmp on tmp.subscriptionSetID = ss.subscriptionSetID;
	END

	-- delete sub addons
	IF EXISTS (select 1 from #tmpDeleteSubAddOns) BEGIN
		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_content AS cc on cc.siteResourceID = sr.siteResourceID
		INNER JOIN dbo.sub_addons AS ao on ao.frontEndContentID = cc.contentID
		INNER JOIN #tmpDeleteSubAddOns AS tmp on tmp.addonID = ao.addonID
		WHERE sr.siteID = @siteID;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBADDON', 'Addon ' + QUOTENAME(s.setName) + ' was removed from subscription ' + QUOTENAME(sub.subscriptionName) + '.', sa.addonID, 'DELETE'
		FROM #tmpDeleteSubAddOns AS tmp
		INNER JOIN dbo.sub_addons as sa on sa.addonID = tmp.addonID
		INNER JOIN dbo.sub_sets as s on s.setID = sa.childSetID
		INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = sa.subscriptionID;
		
		DELETE sa
		FROM dbo.sub_addons as sa
		INNER JOIN #tmpDeleteSubAddOns as tmp on tmp.addonID = sa.addonID;
	END

	-- delete subscriptions
	IF EXISTS (select 1 from #tmpDeleteSubscriptions) BEGIN
		select @minSubID = min(subscriptionID) from #tmpDeleteSubscriptions;
		WHILE @minSubID is not null BEGIN
			EXEC dbo.sub_deleteSubscription @orgID=@orgID, @siteID=@siteID, @subscriptionID=@minSubID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minSubID = min(subscriptionID) from #tmpDeleteSubscriptions where subscriptionID > @minSubID;
		END
	END

	-- delete subTypes
	IF EXISTS (select 1 from #tmpDeleteSubTypes) BEGIN
		select @minTypeID = min(typeID) from #tmpDeleteSubTypes;
		WHILE @minTypeID is not null BEGIN
			EXEC dbo.sub_deleteSubscriptionType @orgID=@orgID, @siteID=@siteID, @subTypeID=@minTypeID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minTypeID = min(typeID) from #tmpDeleteSubTypes where typeID > @minTypeID;
		END
	END

	-- delete rate freqs merchant profiles
	IF EXISTS (select 1 from #tmpDeleteSubRateFreqMerchantProfiles) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Payment Method(s) ' + STRING_AGG(QUOTENAME(p.profileName),', ') + ' were removed from Rate Frequency ' + QUOTENAME(f.frequencyName) + '.', rf.rateID, 'UPDATE'
		FROM #tmpDeleteSubRateFreqMerchantProfiles as tmp
		INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = tmp.rfmpid
		inner join dbo.sub_rateFrequencies as rf on rf.RFID = rfmp.rfid
		inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		inner join dbo.mp_profiles as p on p.profileID = rfmp.profileID
		left outer join #tmpDeleteSubRates as tmpDel on tmpDel.rateID = rf.rateID
		where tmpDel.rateID IS NULL
		group by rf.rateID, f.frequencyName;

		UPDATE rfmp
		SET rfmp.[status] = 'D'
		FROM dbo.sub_rateFrequenciesMerchantProfiles as rfmp 
		INNER JOIN #tmpDeleteSubRateFreqMerchantProfiles as tmp on tmp.rfmpid = rfmp.rfmpid;
	END

	-- delete rate freqs
	IF EXISTS (select 1 from #tmpDeleteSubRateFreqs) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' has been deleted.', rf.rateID, 'UPDATE'
		FROM #tmpDeleteSubRateFreqs as tmp
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.RFID = tmp.rfid
		INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		left outer join #tmpDeleteSubRates as tmpDel on tmpDel.rateID = rf.rateID
		where tmpDel.rateID IS NULL;

		UPDATE rf
		SET rf.[status] = 'D'
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpDeleteSubRateFreqs as tmp on tmp.rfid = rf.rfid;
	END

	-- delete rates
	IF EXISTS (select 1 from #tmpDeleteSubRates) BEGIN
		select @minRateID = min(rateID) from #tmpDeleteSubRates;
		WHILE @minRateID is not null BEGIN
			EXEC dbo.sub_deleteRate @orgID=@orgID, @rateID=@minRateID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minRateID = min(rateID) from #tmpDeleteSubRates where rateID > @minRateID;
		END
	END

	-- delete rate schedules
	IF EXISTS (select 1 from #tmpDeleteSubRateSchedules) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'RATESCH', 'Rate Schedule ' + QUOTENAME(rs.scheduleName) + ' has been deleted.', tmp.scheduleID, 'DELETE'
		FROM #tmpDeleteSubRateSchedules as tmp
		INNER JOIN dbo.sub_rateSchedules as rs on rs.scheduleID = tmp.scheduleID
		WHERE rs.siteID = @siteID;

		UPDATE rs
		SET rs.[status] = 'D'
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpDeleteSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID;
	END
	
	-- delete frequencies
	IF EXISTS (select 1 from #tmpDeleteSubFrequencies) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'FREQ', 'Frequency ' + QUOTENAME(sf.frequencyName) + ' has been deleted.', tmp.frequencyID, 'DELETE'
		FROM #tmpDeleteSubFrequencies as tmp
		INNER JOIN dbo.sub_frequencies as sf on sf.frequencyID = tmp.frequencyID
		WHERE sf.siteID = @siteID;

		UPDATE sf
		SET sf.[status] = 'D'
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpDeleteSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
		WHERE sf.siteID = @siteID;
	END

	-- delete sets
	IF EXISTS (select 1 from #tmpDeleteSets) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', 'Subscription Set ' + QUOTENAME(ss.setName) + ' has been deleted.', tmp.setID, 'DELETE'
		FROM #tmpDeleteSets as tmp
		INNER JOIN dbo.sub_sets as ss on ss.setID = tmp.setID
		WHERE ss.siteID = @siteID;

		DELETE ss
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpDeleteSets as tmp on tmp.setID = ss.setID
		WHERE ss.siteID = @siteID;
	END

	-- delete rate groups
	IF EXISTS (select 1 from #tmpDeleteSubRateGroups) BEGIN
		select @minResourceRightsID = min(resourceRightsID) from #tmpDeleteSubRateGroups;
		WHILE @minResourceRightsID is not null BEGIN
			set @siteResourceID = null;

			select @siteResourceID = tmp.siteResourceID, @groupPathExpanded = g.groupPathExpanded, @rateID = r.rateID
			from #tmpDeleteSubRateGroups as tmp
			inner join #tmpSiteSubRateGroups as tmp2 on tmp2.resourceRightsID = tmp.resourceRightsID
			inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = tmp2.groupID
			inner join dbo.sub_rates as r on r.rateID = tmp2.rateID
			where tmp.resourceRightsID = @minResourceRightsID;

			IF NOT EXISTS (SELECT 1 FROM #tmpDeleteSubRates WHERE rateID = @rateID)
				INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
				SELECT 'SUBRATE', 'Group ' + QUOTENAME(@groupPathExpanded) + ' removed from Rate.', @rateID, 'UPDATE';

			EXEC dbo.cms_deleteSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @siteResourceRightID=@minResourceRightsID;

			select @minResourceRightsID = min(resourceRightsID) from #tmpDeleteSubRateGroups where resourceRightsID > @minResourceRightsID;
		END
	END

	-- new frequencies
	IF EXISTS (select 1 from #tmpSubFrequencies where finalAction = 'A') BEGIN
		-- update uids of deleted freqs having same UID
		UPDATE sf
		SET sf.[uid] = NEWID()
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.[uid] = sf.[uid]
		WHERE sf.[status] = 'D'
		AND tmp.finalAction = 'A';

		DELETE FROM @insertedEntries;

		-- insert freqs
		INSERT INTO dbo.sub_frequencies (siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status])
		OUTPUT inserted.frequencyID
		INTO @insertedEntries
		select @siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status]
		from #tmpSubFrequencies 
		where finalAction = 'A'
			except
		select siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status]
		from dbo.sub_frequencies
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'FREQ', 'New Frequency ' + QUOTENAME(f.frequencyName) + ' has been created.'
				+ CASE WHEN f.frequency IS NOT NULL THEN @crlf + 'Number of Installments: ' + CAST(f.frequency AS VARCHAR(10)) ELSE '' END
				+ CASE WHEN NULLIF(f.frequencyShortName,'') IS NOT NULL THEN @crlf + 'Short Name: ' + f.frequencyShortName ELSE '' END
				+ CASE WHEN f.rateRequired IS NOT NULL THEN @crlf + 'Rate Required: ' + CASE WHEN f.rateRequired = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
				+ CASE WHEN f.frequency IS NOT NULL THEN @crlf + 'Has Installments: ' + CASE WHEN f.frequency > 0 THEN 'Yes' ELSE 'No' END ELSE '' END
				+ CASE WHEN f.monthlyInterval IS NOT NULL THEN @crlf + 'Number of Months Between Payments: ' + CAST(f.monthlyInterval AS VARCHAR(10)) ELSE '' END,
				f.frequencyID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_frequencies AS f ON f.frequencyID = tmp.refID;
		END
	END

	-- new sets
	IF EXISTS (select 1 from #tmpSets where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_sets (siteID, setName, [uid], [status])
		OUTPUT inserted.setID
		INTO @insertedEntries
		select @siteID, setName, [uid], [status]
		from #tmpSets 
		where finalAction = 'A'
			except
		select siteID, setName, [uid], [status]
		from dbo.sub_sets
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBSET', 'New Subscription Set ' + QUOTENAME(s.setName) + ' has been created.', s.setID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_sets AS s ON s.setID = tmp.refID;
		END
	END

	-- new subTypes
	IF EXISTS (select 1 from #tmpSubTypes where finalAction = 'A') BEGIN
		-- update uids of deleted subTypes having same UID
		UPDATE st
		SET st.[uid] = NEWID()
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.[uid] = st.[uid]
		WHERE st.[status] = 'D'
		AND tmp.finalAction = 'A';

		select @minTypeID = min(syncTypeID) from #tmpSubTypes where finalAction = 'A';

		WHILE @minTypeID is not null BEGIN
			select @uid = null, @newSubTypeID = null, @typeName = null, @typeCode = null, @feRawContent = null, @feCompletedRawContent = null, @feEmailNotification = null;

			select @uid = [uid], @typeName = typeName, @typeCode = typeCode, @feRawContent = frontEndContent, 
				@feCompletedRawContent = frontEndCompletedContent, @feEmailNotification = feEmailNotification
			from #tmpSubTypes
			where finalAction = 'A' 
			and syncTypeID = @minTypeID;

			EXEC dbo.sub_createSubscriptionType @orgID=@orgID, @siteID=@siteID, @typeName=@typeName, @feRawContent=@feRawContent, 
				@feCompletedRawContent=@feCompletedRawContent, @feEmailNotification=@feEmailNotification, @isImport=1, 
				@recordedByMemberID=@recordedByMemberID, @subTypeID=@newSubTypeID OUTPUT;

			UPDATE dbo.sub_types
			SET [uid] = @uid,
				typeCode = @typeCode
			WHERE typeID = @newSubTypeID;
			
			select @minTypeID = min(syncTypeID) from #tmpSubTypes where finalAction = 'A' and syncTypeID > @minTypeID;
		END
	END

	-- new rate schedules
	IF EXISTS (select 1 from #tmpSubRateSchedules where finalAction = 'A') BEGIN
		-- update uids of deleted schedules having same UID
		UPDATE rs
		SET rs.[uid] = NEWID()
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.[uid] = rs.[uid]
		WHERE rs.[status] = 'D'
		AND tmp.finalAction = 'A';

		DELETE FROM @insertedEntries;

		-- insert schedules
		INSERT INTO dbo.sub_rateSchedules (siteID, scheduleName, [uid], [status])
		OUTPUT inserted.scheduleID
		INTO @insertedEntries
		select @siteID, scheduleName, [uid], [status]
		from #tmpSubRateSchedules 
		where finalAction = 'A'
			except
		select siteID, scheduleName, [uid], [status]
		from dbo.sub_rateSchedules
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'RATESCH', 'New Subscription Rate Schedule ' + QUOTENAME(s.scheduleName) + ' has been created.', s.scheduleID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateSchedules AS s ON s.scheduleID = tmp.refID;
		END
	END

	-- new subscriptions
	IF EXISTS (select 1 from #tmpSubscriptions where finalAction = 'A') BEGIN
		-- update uids of deleted subscriptions having same UID
		UPDATE s
		SET s.[uid] = NEWID()
		FROM dbo.sub_subscriptions as s
		INNER JOIN dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		INNER JOIN #tmpSubscriptions as tmp on tmp.[uid] = s.[uid]
		WHERE s.[status] = 'D'
		AND tmp.finalAction = 'A';

		select @minSubID = min(syncSubscriptionID) from #tmpSubscriptions where finalAction = 'A';

		WHILE @minSubID is not null BEGIN
			select @uid = null, @newSubsID = null, @subName = null, @feRawContent = null, @feCompletedRawContent = null, @feParentSubRawContent = null,
				@reportCode = null, @scheduleID = null, @rateTermDateFlag = null, @autoExpire = null, @status = null,
				@soldSeparately = null, @paymentOrder = null, @GLAccountID = null, @allowRateGLOverride = null, @activationOptionCode = null,
				@alternateActivationOptionCode = null, @typeID = null;

			select @uid = s.[uid], @subName = s.subscriptionName, @feRawContent = s.frontEndContent, @feCompletedRawContent = s.frontEndCompletedContent, 
				@feParentSubRawContent = s.frontEndParentSubContent, @reportCode = s.reportCode, @scheduleID = rs.scheduleID, @rateTermDateFlag = s.rateTermDateFlag, 
				@autoExpire = s.autoExpire, @status = s.[status], @soldSeparately = s.soldSeparately, @paymentOrder = s.paymentOrder, @GLAccountID = s.GLAccountID, 
				@allowRateGLOverride = s.allowRateGLAccountOverride, @activationOptionCode = ao.subActivationCode,
				@alternateActivationOptionCode = ao_alt.subActivationCode, @typeID = t.typeID
			from #tmpSubscriptions as s
			inner join dbo.sub_types as t on t.siteID = @siteID and t.[uid] = s.syncTypeUID
			inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = s.scheduleUID
			inner join dbo.sub_activationOptions as ao on ao.subActivationID = s.subActivationID
			inner join dbo.sub_activationOptions as ao_alt on ao_alt.subActivationID = s.subAlternateActivationID
			where s.finalAction = 'A' 
			and s.syncSubscriptionID = @minSubID;

			EXEC dbo.sub_createSubscription @orgID=@orgID, @siteID=@siteID, @typeID=@typeID, @subName=@subName, 
				@reportCode=@reportCode, @scheduleID=@scheduleID, @rateTermDateFlag=@rateTermDateFlag,
				@autoExpire=@autoExpire, @status=@status,
				@soldSeparately=@soldSeparately, @paymentOrder=@paymentOrder, @GLAccountID=@GLAccountID,
				@allowRateGLOverride=@allowRateGLOverride, @activationOptionCode=@activationOptionCode,
				@alternateActivationOptionCode=@alternateActivationOptionCode, @feRawContent=@feRawContent,
				@feCompletedRawContent=@feCompletedRawContent, @feParentSubRawContent=@feParentSubRawContent,
				@isImport=1, @recordedByMemberID=@recordedByMemberID, @subID=@newSubsID OUTPUT;

			UPDATE dbo.sub_subscriptions
			SET [uid] = @uid
			WHERE subscriptionID = @newSubsID;
			
			select @minSubID = min(syncSubscriptionID) from #tmpSubscriptions where finalAction = 'A' and syncSubscriptionID > @minSubID;
		END
	END

	-- new rates
	IF EXISTS (select 1 from #tmpSubRates where finalAction = 'A') BEGIN
		-- update uids of deleted rates having same UID
		UPDATE r
		SET r.[uid] = NEWID()
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.[uid] = r.[uid]
		WHERE r.[status] = 'D'
		AND tmp.finalAction = 'A';

		-- insert rates
		select @minRateID = min(syncRateID) from #tmpSubRates where finalAction = 'A';
		WHILE @minRateID is not null BEGIN
			select @scheduleID = null, @rateName = null, @reportCode = null, @status = null, @rateStartDate = null, @rateEndDate = null, 
				@rateStartAFID = null, @rateEndAFID = null, @termStartDate = null, @termEndDate = null, @termStartAFID = null, @termEndAFID = null,
				@graceEndDate = null, @graceEndAFID = null, @recogStartDate = null, @recogEndDate = null, @recogStartAFID = null,
				@recogEndAFID = null, @rateAdvanceOnTermEnd = null, @isRenewalRate = null, @forceUpfront = null, @accountID = null,
				@frontEndAllowChangePrice = null, @keepChangedPriceOnRenewal = null, @frontEndChangePriceMin = null, @frontEndChangePriceMax = null, 
				@newRateID = null, @newSiteResourceID = null, @uid = null, @rateAFStartDate = null, @rateAFEndDate = null, @termAFStartDate = null, 
				@termAFEndDate = null, @recogAFStartDate = null, @recogAFEndDate = null;

			select @scheduleID = rs.scheduleID, @rateName = r.rateName, @reportCode = r.reportCode, @status = r.[status], @rateStartDate = r.rateStartDate, 
				@rateEndDate = r.rateEndDate, @rateStartAFID = r.rateStartDateAFID, @rateEndAFID = r.rateEndDateAFID, @termStartDate = r.termStartDate, 
				@termEndDate = r.termEndDate, @termStartAFID = r.termStartDateAFID, @termEndAFID = r.termEndDateAFID,
				@graceEndDate = r.graceEndDate, @graceEndAFID = r.graceAFID, @recogStartDate = r.recogStartDate, @recogEndDate = r.recogEndDate, 
				@recogStartAFID = r.recogStartDateAFID, @recogEndAFID = r.recogEndDateAFID, @rateAdvanceOnTermEnd = r.rateAdvanceOnTermEnd, 
				@isRenewalRate = r.isRenewalRate, @forceUpfront = r.forceUpfront, @accountID = r.GLAccountID,
				@frontEndAllowChangePrice = r.frontEndAllowChangePrice, @keepChangedPriceOnRenewal = r.keepChangedPriceOnRenewal,
				@frontEndChangePriceMin = r.frontEndChangePriceMin, @frontEndChangePriceMax = r.frontEndChangePriceMax, @uid = r.[uid],
				@rateAFStartDate = r.rateAFStartDate, @rateAFEndDate = r.rateAFEndDate, @termAFStartDate = r.termAFStartDate, 
				@termAFEndDate = r.termAFEndDate, @recogAFStartDate = r.recogAFStartDate, @recogAFEndDate = r.recogAFEndDate
			from #tmpSubRates as r
			inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = r.scheduleUID
			where r.finalAction = 'A'
			and r.syncRateID = @minRateID;

			-- create rate
			EXEC dbo.sub_createRate @scheduleID=@scheduleID, @rateName=@rateName, @reportCode=@reportCode, @status=@status,
				@rateStartDate=@rateStartDate, @rateEndDate=@rateEndDate, @rateStartAFID=@rateStartAFID,
				@rateEndAFID=@rateEndAFID, @termStartDate=@termStartDate, @termEndDate=@termEndDate,
				@termStartAFID=@termStartAFID, @termEndAFID=@termEndAFID, @graceEndDate=@graceEndDate,
				@graceEndAFID=@graceEndAFID, @recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate,
				@recogStartAFID=@recogStartAFID, @recogEndAFID=@recogEndAFID, @rateAdvanceOnTermEnd=@rateAdvanceOnTermEnd,
				@isRenewalRate=@isRenewalRate, @forceUpfront=@forceUpfront, @accountID=@accountID,
				@frontEndAllowChangePrice=@frontEndAllowChangePrice, @linkedNonRenewalRateID=NULL, @fallbackRenewalRateID=NULL,
				@keepChangedPriceOnRenewal=@keepChangedPriceOnRenewal, @frontEndChangePriceMin=@frontEndChangePriceMin,
				@frontEndChangePriceMax=@frontEndChangePriceMax, @isImport=1, @recordedByMemberID=@recordedByMemberID, 
				@rateID=@newRateID OUTPUT, @siteResourceID=@newSiteResourceID OUTPUT;

			UPDATE dbo.sub_rates 
			SET [uid] = @uid,
				rateAFStartDate = @rateAFStartDate,
				rateAFEndDate = @rateAFEndDate,
				termAFStartDate = @termAFStartDate,
				termAFEndDate = @termAFEndDate,
				recogAFStartDate = @recogAFStartDate,
				recogAFEndDate = @recogAFEndDate
			WHERE rateID = @newRateID;

			select @minRateID = min(syncRateID) from #tmpSubRates where finalAction = 'A' and syncRateID > @minRateID;
		END
	END

	-- new sub addons
	IF EXISTS (select 1 from #tmpSubAddOns where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_addons (subscriptionID, childSetID, orderNum, minAllowed, maxAllowed, useAcctCodeInSet, useTermEndDateInSet,
			PCnum, PCPctOffEach, frontEndAllowSelect, frontEndAllowChangePrice, frontEndContentID, frontEndAddAdditional)
		OUTPUT inserted.addonID
		INTO @insertedEntries
		select sub.subscriptionID, s.setID, ao.orderNum, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet,
			ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, ao.frontEndAddAdditional
		from #tmpSubAddOns as ao
		inner join dbo.sub_sets as s on s.[uid] = ao.syncChildSetUID
		inner join dbo.sub_subscriptions as sub on sub.[uid] = ao.subscriptionUID
		where ao.finalAction = 'A'
			except
		select subs.subscriptionID, s.setID, ao.orderNum, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet,
			ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, ao.frontEndAddAdditional
		from dbo.sub_addons as ao
		inner join dbo.sub_sets as s on s.setID = ao.childSetID 
			and s.siteID = @siteID
			and s.status <> 'D'
		inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ao.subscriptionID and subs.status <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBADDON', 'New AddOn created for Subscription ' + QUOTENAME(sub.subscriptionName) + '.' +
					@crlf + 'Subscription Set: ' + QUOTENAME(ss.setName) +
					CASE WHEN ao.orderNum IS NOT NULL THEN @crlf + 'Order Number: ' + CAST(ao.orderNum AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.minAllowed IS NOT NULL THEN @crlf + 'Min Allowed: ' + CAST(ao.minAllowed AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.maxAllowed IS NOT NULL THEN @crlf + 'Max Allowed: ' + CAST(ao.maxAllowed AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.useAcctCodeInSet IS NOT NULL THEN @crlf + 'Use Set Acct Code: ' + CASE WHEN ao.useAcctCodeInSet = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.useTermEndDateInSet IS NOT NULL THEN @crlf + 'Use Term End Date In Set: ' + CASE WHEN ao.useTermEndDateInSet = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.PCnum IS NOT NULL THEN @crlf + 'Items Free in Set: ' + CAST(ao.PCnum AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.PCPctOffEach IS NOT NULL THEN @crlf + 'PC Percent Off Each: ' + CAST(ao.PCPctOffEach AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.frontEndAllowSelect IS NOT NULL THEN @crlf + 'Should this Addon appear in front end: ' + CASE WHEN ao.frontEndAllowSelect = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.frontEndAddAdditional IS NOT NULL THEN @crlf + 'Allow User to add additional selections in front end wizard: ' + CASE WHEN ao.frontEndAddAdditional = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.frontEndAllowChangePrice IS NOT NULL THEN @crlf + 'Front End Change Price Select: ' + CASE WHEN ao.frontEndAllowChangePrice = 0 THEN 'No' ELSE 'Yes' END ELSE '' END,
				ao.addonID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_addons AS ao ON ao.addonID = tmp.refID
			INNER JOIN dbo.sub_sets AS ss ON ss.setID = ao.childSetID
			INNER JOIN dbo.sub_subscriptions AS sub ON sub.subscriptionID = ao.subscriptionID;
		END
	END

	-- new rate freqs
	IF EXISTS (select 1 from #tmpSubRateFrequencies where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_rateFrequencies (rateID, frequencyID, rateAmt, status, numInstallments, allowFrontEnd)
		OUTPUT inserted.rfid
		INTO @insertedEntries
		select r.rateID, f.frequencyID, rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd
		from #tmpSubRateFrequencies as rf
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		where rf.finalAction = 'A'
			except
		select r.rateID, f.frequencyID, rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd
		from dbo.sub_rateFrequencies as rf
		inner join dbo.sub_rates as r on r.rateID = rf.rateID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID;

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' is added to Rate.'+
					@crlf + 'Amount: ' + CAST(rf.rateAmt AS VARCHAR(20)) +
					CASE WHEN rf.allowFrontEnd IS NOT NULL THEN @crlf + 'Allow Front End: ' + CASE WHEN rf.allowFrontEnd = 0 THEN 'No' ELSE 'Yes' END ELSE '' END,
				r.rateID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rfid = tmp.refID
			INNER JOIN dbo.sub_rates as r on r.rateID = rf.rateID
			INNER JOIN dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID;
		END
	END

	-- new rate freq merchant profiles
	IF EXISTS (select 1 from #tmpSubRateFrequenciesMerchantProfiles where finalAction = 'A') BEGIN
		-- update matching rate freq merchant profiles from deleted status to active status
		UPDATE rfmp
		SET rfmp.[status] = 'A'
		from #tmpSubRateFrequenciesMerchantProfiles as tmpRFMP
		inner join #tmpSubRateFrequencies as rf on rf.syncRFID = tmpRFMP.syncRFID
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		inner join dbo.sub_rateFrequencies as srf on srf.rateID = r.rateID and srf.frequencyID = f.frequencyID
		inner join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = srf.rfid
			and rfmp.profileID = tmpRFMP.profileID
			and rfmp.[status] = 'D'
		where tmpRFMP.finalAction = 'A';

		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_rateFrequenciesMerchantProfiles (rfid, profileID, [status])
		OUTPUT inserted.rfmpid
		INTO @insertedEntries
		select distinct srf.rfid, rfmp.profileID, rfmp.[status]
		from #tmpSubRateFrequenciesMerchantProfiles as rfmp
		inner join #tmpSubRateFrequencies as rf on rf.syncRFID = rfmp.syncRFID
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		inner join dbo.sub_rateFrequencies as srf on srf.rateID = r.rateID and srf.frequencyID = f.frequencyID
		where rfmp.finalAction = 'A'
			except
		select srf.rfid, rfmp.profileID, rfmp.[status]
		from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
		inner join dbo.sub_rateFrequencies as srf on srf.rfid = rfmp.rfid
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = srf.frequencyID
		where rfmp.[status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBRATE', 'Payment Method(s) ' + STRING_AGG(QUOTENAME(p.profileName),', ') + ' were added to Rate Frequency ' + QUOTENAME(f.frequencyName) + '.',
				rf.rateID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = tmp.refID
			INNER JOIN dbo.sub_rateFrequencies as rf on rf.RFID = rfmp.rfid
			INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
			INNER JOIN dbo.mp_profiles as p on p.profileID = rfmp.profileID
			WHERE rfmp.[status] = 'A'
			GROUP BY rf.rateID, f.frequencyName;
		END
	END

	-- new rate groups
	IF EXISTS (select 1 from #tmpNewSubRateGroups) BEGIN
		select @minAutoID = min(autoID) from #tmpNewSubRateGroups;
		WHILE @minAutoID IS NOT NULL BEGIN
			select @siteResourceID = null, @include = null, @groupID = null;

			select @siteResourceID = r.siteResourceID, @include = rg.include, @groupID = rg.groupID
			from #tmpNewSubRateGroups as rg
			inner join dbo.sub_rates as r on r.[uid] = rg.rateUID
			where rg.autoID = @minAutoID;

			EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=@include,
				@functionIDList=@qualifyFID, @roleID=null, @groupID=@groupID, @inheritedRightsResourceID=null,
				@inheritedRightsFunctionID=null;

			select @minAutoID = min(autoID) from #tmpNewSubRateGroups where autoID > @minAutoID;
		END

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Group(s) ' + STRING_AGG(QUOTENAME(g.groupPathExpanded) + ' (' + CASE rg.[include] WHEN 1 THEN 'Allow' ELSE 'Deny' END + ')',', ') + ' added to Rate.', r.rateID, 'UPDATE'
		FROM #tmpNewSubRateGroups as rg
		inner join dbo.sub_rates as r on r.[uid] = rg.rateUID
		inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = rg.groupID
		group by r.rateID;
	END

	-- new subscription sets
	IF EXISTS (select 1 from #tmpSubscriptionSets where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_subscriptionSets (subscriptionID, setID, orderNum)
		OUTPUT inserted.subscriptionSetID
		INTO @insertedEntries
		select subs.subscriptionID, ss.setID, s.orderNum
		from #tmpSubscriptionSets as s
		inner join dbo.sub_subscriptions as subs on subs.[uid] = s.subscriptionUID
		inner join dbo.sub_sets as ss on ss.siteID = @siteID and ss.[uid] = s.setUID
		where s.finalAction = 'A'
			except
		select s.subscriptionID, s.setID, s.orderNum
		from dbo.sub_subscriptionSets as s
		inner join dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
		inner join dbo.sub_sets as ss on ss.siteID = @siteID and ss.setID = s.setID;

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBSET', 'Subscription(s) ' + STRING_AGG(QUOTENAME(sub.subscriptionName),', ') + ' has been added to set.', ss.setID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_subscriptionSets AS ss ON ss.subscriptionSetID = tmp.refID
			INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
			LEFT OUTER JOIN #tmpDeleteSets as tmpDel on tmpDel.setID = ss.setID
			WHERE tmpDel.setID IS NULL
			GROUP BY ss.setID;
		END
	END

	-- update frequencies
	IF EXISTS (select 1 from #tmpSubFrequencies where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataFreq;
		CREATE TABLE #tmpAuditLogDataFreq (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Frequency Name] varchar(max), [Number of Installments] varchar(max), 
			[Frequency Short Name] varchar(max), [Rate Required] varchar(max), [Has Installments] varchar(max), [Monthly Interval] varchar(max));

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		VALUES ('DATATYPECODE', 'STRING', 'INTEGER', 'STRING', 'BIT', 'BIT', 'INTEGER');

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [refID], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		SELECT 'OLDVAL', sf.frequencyID, sf.frequencyName, sf.frequency, sf.frequencyShortName, sf.rateRequired, sf.hasInstallments, sf.monthlyInterval
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
			AND tmp.finalAction = 'C'
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D';

		UPDATE sf
		SET sf.frequencyName = tmp.frequencyName,
			sf.frequency = tmp.frequency,
			sf.frequencyShortName = tmp.frequencyShortName,
			sf.rateRequired = tmp.rateRequired,
			sf.hasInstallments = tmp.hasInstallments,
			sf.monthlyInterval = tmp.monthlyInterval,
			sf.isSystemRate = tmp.isSystemRate,
			sf.[status] = tmp.[status]
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [refID], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		SELECT 'NEWVAL', sf.frequencyID, sf.frequencyName, sf.frequency, sf.frequencyShortName, sf.rateRequired, sf.hasInstallments, sf.monthlyInterval
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
			AND tmp.finalAction = 'C'
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataFreq', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'FREQ', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataFreq;
	END

	-- update sets
	IF EXISTS (select 1 from #tmpSets where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSet') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSet;
		CREATE TABLE #tmpAuditLogDataSet (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Name] varchar(max), [Status] varchar(max));

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [Name], [Status])
		VALUES ('DATATYPECODE', 'STRING', 'BIT');

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [refID], [Name], [Status])
		SELECT 'OLDVAL', ss.setID, ss.setName, CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
			AND tmp.finalAction = 'C'
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D';

		UPDATE ss
		SET ss.setName = tmp.setName,
			ss.[status] = tmp.[status]
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [refID], [Name], [Status])
		SELECT 'NEWVAL', ss.setID, ss.setName, CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
			AND tmp.finalAction = 'C'
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSet', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSet') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSet;
	END

	-- update subTypes
	IF EXISTS (select 1 from #tmpSubTypes where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSubType') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSubType;
		CREATE TABLE #tmpAuditLogDataSubType (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Type Name] varchar(max),
			[Type Code] varchar(max), [Renewal Notification Email(s)] varchar(max));

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [Type Name], [Type Code], [Renewal Notification Email(s)])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [refID], [Type Name], [Type Code], [Renewal Notification Email(s)])
		SELECT 'OLDVAL', st.typeID, st.typeName, st.typeCode, st.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';
		
		UPDATE st
		SET st.typeName = tmp.typeName,
			st.typeCode = tmp.typeCode,
			st.feEmailNotification = tmp.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [refID], [Type Name], [Type Code], [Renewal Notification Email(s)])
		SELECT 'NEWVAL', st.typeID, st.typeName, st.typeCode, st.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSubType', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBTYPE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSubType') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSubType;
	END

	-- update rate schedules
	IF EXISTS (select 1 from #tmpSubRateSchedules where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSched') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSched;
		CREATE TABLE #tmpAuditLogDataSched (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Rate Schedule Name] varchar(max), [Status] varchar(max));

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [Rate Schedule Name], [Status])
		VALUES ('DATATYPECODE', 'STRING', 'BIT');

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [refID], [Rate Schedule Name], [Status])
		SELECT 'OLDVAL', rs.scheduleID, rs.scheduleName, CASE WHEN rs.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		UPDATE rs
		SET rs.scheduleName = tmp.scheduleName,
			rs.[status] = tmp.[status]
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [refID], [Rate Schedule Name], [Status])
		SELECT 'NEWVAL', rs.scheduleID, rs.scheduleName, CASE WHEN rs.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSched', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'RATESCH', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSched') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSched;
	END

	-- update subscriptions
	IF EXISTS (select 1 from #tmpSubscriptions where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSub') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSub;
		CREATE TABLE #tmpAuditLogDataSub (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Subscription Name] varchar(max), [Report Code] varchar(max), 
			[Subscription Type] varchar(max), [Rate Schedule] varchar(max), [Term Dates] varchar(max), 
			[Auto Expire Subscribers] varchar(max), [Sold Seperately] varchar(max), [Status] varchar(max), [Payment Order] varchar(max), 
			[Subscription GL Account] varchar(max), [Allow Rate GL Account Override] varchar(max), [Activation Option] varchar(max),
			[Alternate Activation Option] varchar(max));

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates], [Auto Expire Subscribers], 
			[Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [refID], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates],
			[Auto Expire Subscribers], [Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		SELECT 'OLDVAL', ss.subscriptionID, ss.subscriptionName, ss.reportCode, t.typeName, srs.scheduleName, 
			CASE 
				WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
				WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
				ELSE 'Calculate term end date for term length (term start date is current day)'
			END,
			CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END, 
			CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
			glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END,
			sao.subActivationName, alt_sao.subActivationName
		FROM dbo.sub_subscriptions as ss
		INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
		INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
		INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
		INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
		LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
		LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID;

		UPDATE s
		SET s.typeID = st.typeID,
			s.subscriptionName = tmp.subscriptionName,
			s.scheduleID = rs.scheduleID,
			s.autoExpire = tmp.autoExpire,
			s.[status] = tmp.[status],
			s.soldSeparately = tmp.soldSeparately,
			s.GLAccountID = tmp.GLAccountID,
			s.rateTermDateFlag = tmp.rateTermDateFlag,
			s.paymentOrder = tmp.paymentOrder,
			s.reportCode = tmp.reportCode,
			s.subActivationID = tmp.subActivationID,
			s.subAlternateActivationID = tmp.subAlternateActivationID,
			s.allowRateGLAccountOverride = tmp.allowRateGLAccountOverride
		FROM dbo.sub_subscriptions as s
		INNER JOIN dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		INNER JOIN #tmpSubscriptions as tmp on tmp.subscriptionID = s.subscriptionID
		INNER JOIN dbo.sub_types as st on st.siteID = @siteID and st.[uid] = tmp.syncTypeUID
		INNER JOIN dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = tmp.scheduleUID
		WHERE s.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [refID], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates],
			[Auto Expire Subscribers], [Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		SELECT 'NEWVAL', ss.subscriptionID, ss.subscriptionName, ss.reportCode, t.typeName, srs.scheduleName,
			CASE
				WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
				WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
				ELSE 'Calculate term end date for term length (term start date is current day)'
			END,
			CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
			glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END,
			sao.subActivationName, alt_sao.subActivationName
		FROM dbo.sub_subscriptions as ss
		INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
		INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
		INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
		INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
		LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
		LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID;

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSub', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSCRIPTION', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSub') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSub;
	END

	-- update rates
	IF EXISTS (select 1 from #tmpSubRates where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRate') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRate;
		CREATE TABLE #tmpAuditLogDataRate (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Status] varchar(max), [Rate Start Date] varchar(max), [Rate End Date] varchar(max),
			[Rate Start Date Advance Formula] varchar(max), [Rate End Date Advance Formula] varchar(max), [Rate Start Date Advance Date] varchar(max), [Rate End Date Advance Date] varchar(max), 
			[Term Start Date] varchar(max), [Term End Date] varchar(max), [Term Start Date Advance Formula] varchar(max), [Term End Date Advance Formula] varchar(max), 
			[Term Start Date Advance Date] varchar(max), [Term End Date Advance Date] varchar(max), [Grace End Date] varchar(max), [Grace End Date Advance Formula] varchar(max), 
			[Rate Name] varchar(max), [When to Advance Rates] varchar(max), [Is Renewal Rate] varchar(max), [Force Up front] varchar(max),
			[Report Code] varchar(max), [GL Account] varchar(max), [Front End Allow Change Price] varchar(max), [Front End Change Price Min] varchar(max), [Front End Change Price Max] varchar(max),
			[Recog Start Date] varchar(max), [Recog End Date] varchar(max), [Recog Start Date Advance Formula] varchar(max), [Recog End Date Advance Formula] varchar(max), 
			[Recog Start Date Advance Date] varchar(max), [Recog End Date Advance Date] varchar(max), [Renewal Keeps Changed Price] varchar(max));

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date],
			[Recog End Date], [Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		VALUES ('DATATYPECODE','STRING', 'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'DATE', 'DATE', 'STRING', 'STRING',
			'DATE', 'DATE', 'DATE', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'DECIMAL2', 'DECIMAL2',
			'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'BIT');

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [refID], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date], [Recog End Date],
			[Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		SELECT 'OLDVAL', r.rateID, CASE WHEN r.[status] = 'A' THEN 'Active' WHEN r.[status] = 'D' THEN 'Deleted' ELSE 'Inactive' END,
			r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
			r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
			af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
			af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
			r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax,
			r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
			af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
		LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
		WHERE r.[status] <> 'D';

		UPDATE r
		SET r.[status] = tmp.[status],
			r.rateStartDate = tmp.rateStartDate,
			r.rateEndDate = tmp.rateEndDate,
			r.rateStartDateAFID = tmp.rateStartDateAFID,
			r.rateEndDateAFID = tmp.rateEndDateAFID,
			r.rateAFStartDate = tmp.rateAFStartDate,
			r.rateAFEndDate = tmp.rateAFEndDate,
			r.termStartDate = tmp.termStartDate,
			r.termEndDate = tmp.termEndDate,
			r.termStartDateAFID = tmp.termStartDateAFID,
			r.termEndDateAFID = tmp.termEndDateAFID,
			r.termAFStartDate = tmp.termAFStartDate,
			r.termAFEndDate = tmp.termAFEndDate,
			r.graceEndDate = tmp.graceEndDate,
			r.graceAFID = tmp.graceAFID,
			r.rateName = tmp.rateName,
			r.rateAdvanceOnTermEnd = tmp.rateAdvanceOnTermEnd,
			r.isRenewalRate = tmp.isRenewalRate,
			r.forceUpfront = tmp.forceUpfront,
			r.reportCode = tmp.reportCode,
			r.GLAccountID = tmp.GLAccountID,
			r.frontEndAllowChangePrice = tmp.frontEndAllowChangePrice,
			r.keepChangedPriceOnRenewal = tmp.keepChangedPriceOnRenewal,
			r.frontEndChangePriceMin = tmp.frontEndChangePriceMin,
			r.frontEndChangePriceMax = tmp.frontEndChangePriceMax,
			r.recogStartDate = tmp.recogStartDate,
			r.recogEndDate = tmp.recogEndDate,
			r.recogStartDateAFID = tmp.recogStartDateAFID,
			r.recogEndDateAFID = tmp.recogEndDateAFID,
			r.recogAFStartDate = tmp.recogAFStartDate,
			r.recogAFEndDate = tmp.recogAFEndDate,
			r.rateOrder = tmp.rateOrder
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
		WHERE r.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [refID], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date], [Recog End Date],
			[Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		SELECT 'NEWVAL', r.rateID, CASE WHEN r.[status] = 'A' THEN 'Active' WHEN r.[status] = 'D' THEN 'Deleted' ELSE 'Inactive' END,
			r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
			r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
			af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
			af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
			r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax,
			r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
			af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
		LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
		WHERE r.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRate', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRate') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRate;
	END

	-- update rates linkedNonRenewalRateID and fallbackRenewalRateID
	IF EXISTS (select 1 from #tmpSubRates where linkedNonRenewalRateUID is not null or fallbackRenewalRateUID is not null) BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateRenew') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateRenew;
		CREATE TABLE #tmpAuditLogDataRateRenew (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Linked Non Renewal Rate] varchar(max), [Fallback Renewal Rate] varchar(max));

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		VALUES ('DATATYPECODE','STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [refID], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		SELECT 'OLDVAL', r.rateID, lr.rateName AS [Linked Non Renewal Rate], fr.rateName AS [Fallback Renewal Rate]
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.sub_rates AS lr ON lr.rateID = r.linkedNonRenewalRateID
		LEFT OUTER JOIN dbo.sub_rates AS fr ON fr.rateID = r.fallbackRenewalRateID
		WHERE r.[status] <> 'D';

		UPDATE r
		SET r.linkedNonRenewalRateID = lr.rateID,
			r.fallbackRenewalRateID = fr.rateID
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.[uid] = r.[uid]
		LEFT OUTER JOIN dbo.sub_rates as lr on lr.[uid] = tmp.linkedNonRenewalRateUID
		LEFT OUTER JOIN dbo.sub_rates as fr on fr.[uid] = tmp.fallbackRenewalRateUID
		WHERE r.[status] <> 'D';

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [refID], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		SELECT 'OLDVAL', r.rateID, lr.rateName AS [Linked Non Renewal Rate], fr.rateName AS [Fallback Renewal Rate]
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.sub_rates AS lr ON lr.rateID = r.linkedNonRenewalRateID
		LEFT OUTER JOIN dbo.sub_rates AS fr ON fr.rateID = r.fallbackRenewalRateID
		WHERE r.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRateRenew', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateRenew') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateRenew;
	END

	-- update sub addons
	IF EXISTS (select 1 from #tmpSubAddOns where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataAO') IS NOT NULL
			DROP TABLE #tmpAuditLogDataAO;
		CREATE TABLE #tmpAuditLogDataAO (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Min Allowed] varchar(max), [Max Allowed] varchar(max),
			[Use Account Code In Set] varchar(max), [Use Term End Date In Set] varchar(max), [PC Number] varchar(max), 
			[PC Percent Off Each] varchar(max), [Front End Change Select] varchar(max), [Front End Add Additional] varchar(max),
			[Front End Change Price Select] varchar(max));

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'BIT', 'BIT');

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [refID], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		SELECT 'OLDVAL', ao.addonID, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
			ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		UPDATE ao
		SET ao.minAllowed = sao.minAllowed, 
			ao.maxAllowed = sao.maxAllowed, 
			ao.useAcctCodeInSet = sao.useAcctCodeInSet, 
			ao.useTermEndDateInSet = sao.useTermEndDateInSet,
			ao.PCnum = sao.PCnum,
			ao.PCPctOffEach = sao.PCPctOffEach,
			ao.frontEndAllowSelect = sao.frontEndAllowSelect, 
			ao.frontEndAllowChangePrice = sao.frontEndAllowChangePrice, 
			ao.frontEndAddAdditional = sao.frontEndAddAdditional
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [refID], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		SELECT 'NEWVAL', ao.addonID, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
			ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataAO', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBADDON', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataAO') IS NOT NULL
			DROP TABLE #tmpAuditLogDataAO;
	END

	-- update rate freqs
	IF EXISTS (select 1 from #tmpSubRateFrequencies where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateFreq;
		CREATE TABLE #tmpAuditLogDataRateFreq (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Status] varchar(max), [Amount] varchar(max), [Installments] varchar(max), [Allow Front End] varchar(max));

		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [Status], [Amount], [Installments], [Allow Front End])
		VALUES ('DATATYPECODE', 'STRING', 'DECIMAL2', 'INTEGER', 'BIT');
		
		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [refID], [Amount], [Status], [Installments], [Allow Front End])
		SELECT 'OLDVAL', rf.rfid, rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, CAST(rf.numInstallments AS VARCHAR(10)), rf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		UPDATE rf
		SET rf.rateAmt = srf.rateAmt, 
			rf.[status] = srf.[status], 
			rf.numInstallments = srf.numInstallments, 
			rf.allowFrontEnd = srf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [refID], [Amount], [Status], [Installments], [Allow Front End])
		SELECT 'NEWVAL', rf.rfid, rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, CAST(rf.numInstallments AS VARCHAR(10)), rf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRateFreq', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		-- grouping differences for a single rate frequency changes into one entry, so it falls under the changes for SUBRATE areacode
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' is updated. ' + STRING_AGG(tmp.msg,' '), rf.rateID, 'UPDATE'
		FROM #tmpAuditLogMessages as tmp
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.rfid = tmp.refID
		INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		GROUP BY rf.rateID, rf.frequencyID, f.frequencyName;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateFreq;
	END

	-- update subscription sets
	IF EXISTS (select 1 from #tmpSubscriptionSets where finalAction = 'C')
		UPDATE s
		SET s.orderNum = tmp.orderNum
		FROM dbo.sub_subscriptionSets as s
		INNER JOIN #tmpSubscriptionSets as tmp on tmp.subscriptionSetID = s.subscriptionSetID
		WHERE tmp.finalAction = 'C';

	-- repopulate sub split cache
	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;
	CREATE TABLE #tblMCQSubCond (conditionID int);
	
	INSERT INTO #tblMCQSubCond (conditionID)
	SELECT conditionID
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND fieldCode ='sub_entry';

	EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;

	EXEC dbo.cache_perms_setStatus @orgID=@orgID, @status='enabled';

	-- re-order addons & sub sets
	select @minSubID = min(s.subscriptionID)
	from dbo.sub_subscriptions as s 
	inner join dbo.sub_types as t on t.typeID = s.typeID
	where t.siteID = @siteID;
	
	WHILE @minSubID is not null BEGIN
		EXEC dbo.subs_reorderAddons @subscriptionID=@minSubID;

		select @minSubID = min(s.subscriptionID)
		from dbo.sub_subscriptions as s 
		inner join dbo.sub_types as t on t.typeID = s.typeID
			and t.siteID = @siteID
		where s.subscriptionID > @minSubID;
	END
	
	select @minSetID = min(setID)
	from dbo.sub_sets
	where siteID = @siteID;
	
	WHILE @minSetID is not null BEGIN
		EXEC dbo.subs_reorderSubscriptionSets @setID=@minSetID;
	
		select @minSetID = min(setID)
		from dbo.sub_sets
		where siteID = @siteID
		and setID > @minSetID;
	END

	-- labels from newly added entries to be used in logging
	INSERT INTO #tmpLogLabelsType (typeID, typeName)
	SELECT t.typeID, t.typeName
	FROM dbo.sub_types AS t
	LEFT OUTER JOIN #tmpLogLabelsType AS tmp ON tmp.typeID = t.typeID
	WHERE t.siteID = @siteID
	AND t.[status] <> 'D'
	AND tmp.typeID IS NULL;

	INSERT INTO #tmpLogLabelsSub (subscriptionID, subscriptionName, typeID, typeName)
	SELECT s.subscriptionID, s.subscriptionName, t.typeID, t.typeName
	FROM dbo.sub_subscriptions AS s
	INNER JOIN dbo.sub_types AS t ON t.typeID = s.typeID
	LEFT OUTER JOIN #tmpLogLabelsSub AS tmp ON tmp.subscriptionID = s.subscriptionID
	WHERE s.orgID = @orgID
	AND s.[status] <> 'D'
	AND tmp.subscriptionID IS NULL;

	INSERT INTO #tmpLogLabelsAddOn (addonID, subscriptionID, childSetID)
	SELECT ao.addonID, ao.subscriptionID, ao.childSetID
	FROM dbo.sub_addons AS ao
	INNER JOIN dbo.sub_subscriptions AS s ON s.subscriptionID = ao.subscriptionID
		AND s.orgID = @orgID
		AND s.[status] <> 'D'
	LEFT OUTER JOIN #tmpLogLabelsAddOn AS tmp ON tmp.addonID = ao.addonID
	WHERE tmp.addonID IS NULL;

	INSERT INTO #tmpLogLabelsSet (setID, setName)
	SELECT s.setID, s.setName
	FROM dbo.sub_sets AS s
	LEFT OUTER JOIN #tmpLogLabelsSet AS tmp ON tmp.setID = s.setID
	WHERE s.siteID = @siteID
	AND tmp.setID IS NULL;

	INSERT INTO #tmpLogLabelsFreq (frequencyID, frequencyName)
	SELECT f.frequencyID, f.frequencyName
	FROM dbo.sub_frequencies AS f
	LEFT OUTER JOIN #tmpLogLabelsFreq AS tmp ON tmp.frequencyID = f.frequencyID
	WHERE f.siteID = @siteID
	AND tmp.frequencyID IS NULL;

	INSERT INTO #tmpLogLabelsSched (scheduleID, scheduleName)
	SELECT s.scheduleID, s.scheduleName
	FROM dbo.sub_rateSchedules AS s
	LEFT OUTER JOIN #tmpLogLabelsSched AS tmp ON tmp.scheduleID = s.scheduleID
	WHERE s.siteID = @siteID
	AND tmp.scheduleID IS NULL;

	INSERT INTO #tmpLogLabelsRate (rateID, rateName, scheduleID, scheduleName)
	SELECT r.rateID, r.rateName, sch.scheduleID, sch.scheduleName
	FROM dbo.sub_rates AS r
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
		AND sr.siteID = @siteID
	INNER JOIN dbo.sub_rateSchedules AS sch ON sch.scheduleID = r.scheduleID
	LEFT OUTER JOIN #tmpLogLabelsRate AS tmp ON tmp.rateID = r.rateID
	WHERE r.[status] <> 'D'
	AND tmp.rateID IS NULL;

	-- audit logging
	SELECT @thisAreaCode = MIN(areaCode) FROM #tmpLogMessages
	WHILE @thisAreaCode IS NOT NULL BEGIN
		SELECT @minRefID = MIN(refID) FROM #tmpLogMessages WHERE areaCode = @thisAreaCode;

		WHILE @minRefID IS NOT NULL BEGIN
			SET @actionCount = 0;
			SET @changesCount = 0;
			SET @msgjson = NULL;
			SET @subKeyMapJSON = NULL;

			-- there is case where create & update entries are present for same entity
			SELECT @actionCount = COUNT(DISTINCT actionType)
			FROM #tmpLogMessages
			WHERE areaCode = @thisAreaCode
			AND refID = @minRefID;

			SELECT @changesCount = COUNT(rowID), @actionType = MAX(actionType)
			FROM #tmpLogMessages
			WHERE areaCode = @thisAreaCode
			AND refID = @minRefID;

			IF @thisAreaCode = 'SUBTYPE' BEGIN
				SET @subKeyMapJSON = '{ "TYPEID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBSCRIPTION' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "SUBSCRIPTIONID":' + CAST(s.subscriptionID AS varchar(10)) + ', "SUBSCRIPTIONTYPEID":' + CAST(s.typeID AS varchar(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END
			ELSE IF @thisAreaCode = 'SUBADDON' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "SUBSCRIPTIONID":' + CAST(a.subscriptionID AS varchar(10)) + ', "ADDONID":' + CAST(a.addonID AS VARCHAR(10)) + ', "CHILDSETID":' + CAST(a.childSetID AS VARCHAR(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsAddOn AS a ON a.addonID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END
			ELSE IF @thisAreaCode = 'FREQ' BEGIN
				SET @subKeyMapJSON = '{ "FREQUENCYID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBSET' BEGIN
				SET @subKeyMapJSON = '{ "SETID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'RATESCH' BEGIN
				SET @subKeyMapJSON = '{ "SCHEDULEID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBRATE' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "RATEID":' + CAST(r.rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(r.scheduleID AS varchar(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsRate AS r ON r.rateID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END

			IF @actionType = 'UPDATE' AND @actionCount = 1 BEGIN
				SET @prefixMsg = '';

				SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID;

				IF @thisAreaCode = 'SUBTYPE' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription Type ' + QUOTENAME(t.typeName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsType AS t ON t.typeID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBSCRIPTION' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription ' + QUOTENAME(s.subscriptionName) + ' under the Subscription Type ' + QUOTENAME(s.typeName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBADDON' BEGIN
					SELECT TOP 1 @prefixMsg = 'Addon ' + QUOTENAME(st.setName) + ' for Subscription ' + QUOTENAME(s.subscriptionName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsAddOn AS a ON a.addonID = tmp.refID
					INNER JOIN #tmpLogLabelsSet AS st ON st.setID = a.childSetID
					INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = a.subscriptionID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'FREQ' BEGIN
					SELECT TOP 1 @prefixMsg = 'Frequency ' + QUOTENAME(f.frequencyName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsFreq AS f ON f.frequencyID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBSET' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription Set ' + QUOTENAME(s.setName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSet AS s ON s.setID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'RATESCH' BEGIN
					SELECT TOP 1 @prefixMsg = 'Rate Schedule ' + QUOTENAME(s.scheduleName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSched AS s ON s.scheduleID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBRATE' BEGIN
					SELECT TOP 1 @prefixMsg = 'Rate ' + QUOTENAME(r.rateName) + ' under Rate Schedule ' + QUOTENAME(r.scheduleName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsRate AS r ON r.rateID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;

					-- if this rate was just created from this import
					IF EXISTS(
						SELECT 1
						FROM sub_rates AS r
						INNER JOIN #tmpSubRates AS tmp ON tmp.[uid] = r.[uid]
							AND tmp.finalAction = 'A'
						WHERE r.rateID = @minRefID
					)
						SET @prefixMsg = 'Newly added ' + @prefixMsg;
				END

				IF @actionCount = 1 AND @changesCount > 1
					SET @prefixMsg = @prefixMsg + ' The following changes have been made:'

				SET @msgjson = @prefixMsg + @crlf + @msgjson;
			END
			ELSE IF @actionType = 'UPDATE' AND @actionCount > 1 BEGIN
				SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID
				ORDER BY actionType;
			END
			ELSE BEGIN
				SELECT TOP 1 @msgjson = msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID;
			END

			SET @subKeyMapJSON = ISNULL(@subKeyMapJSON,'{}');

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode=@thisAreaCode, @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=1, @enteredByMemberID=@recordedByMemberID;
			
			SELECT @minRefID = MIN(refID) FROM #tmpLogMessages WHERE areaCode = @thisAreaCode and refID > @minRefID;
		END

		SELECT @thisAreaCode = MIN(areaCode) FROM #tmpLogMessages WHERE areaCode > @thisAreaCode;
	END

	on_done:
	IF OBJECT_ID('tempdb..#tmpSubFrequencies') IS NOT NULL
		DROP TABLE #tmpSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpSets') IS NOT NULL
		DROP TABLE #tmpSets;
	IF OBJECT_ID('tempdb..#tmpSubTypes') IS NOT NULL
		DROP TABLE #tmpSubTypes;
	IF OBJECT_ID('tempdb..#tmpSubscriptions') IS NOT NULL
		DROP TABLE #tmpSubscriptions;
	IF OBJECT_ID('tempdb..#tmpSubAddOns') IS NOT NULL
		DROP TABLE #tmpSubAddOns;
	IF OBJECT_ID('tempdb..#tmpSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpSubRates') IS NOT NULL
		DROP TABLE #tmpSubRates;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequencies') IS NOT NULL
		DROP TABLE #tmpSubRateFrequencies;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequenciesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpSubRateFrequenciesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpSiteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpNewSubRateGroups') IS NOT NULL
		DROP TABLE #tmpNewSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpNewSubContentObjs') IS NOT NULL
		DROP TABLE #tmpNewSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpUpdateSubContentObjs') IS NOT NULL
		DROP TABLE #tmpUpdateSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubFrequencies') IS NOT NULL
		DROP TABLE #tmpDeleteSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpDeleteSets') IS NOT NULL
		DROP TABLE #tmpDeleteSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubAddOns') IS NOT NULL
		DROP TABLE #tmpDeleteSubAddOns;
	IF OBJECT_ID('tempdb..#tmpDeleteSubTypes') IS NOT NULL
		DROP TABLE #tmpDeleteSubTypes;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRates') IS NOT NULL
		DROP TABLE #tmpDeleteSubRates;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqs') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptions') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptions;
	IF OBJECT_ID('tempdb..#tmpLogLabelsType') IS NOT NULL
		DROP TABLE #tmpLogLabelsType;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSub') IS NOT NULL
		DROP TABLE #tmpLogLabelsSub;
	IF OBJECT_ID('tempdb..#tmpLogLabelsAddOn') IS NOT NULL
		DROP TABLE #tmpLogLabelsAddOn;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSet') IS NOT NULL
		DROP TABLE #tmpLogLabelsSet;
	IF OBJECT_ID('tempdb..#tmpLogLabelsFreq') IS NOT NULL
		DROP TABLE #tmpLogLabelsFreq;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSched') IS NOT NULL
		DROP TABLE #tmpLogLabelsSched;
	IF OBJECT_ID('tempdb..#tmpLogLabelsRate') IS NOT NULL
		DROP TABLE #tmpLogLabelsRate;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
		DROP TABLE #tmpAuditLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
