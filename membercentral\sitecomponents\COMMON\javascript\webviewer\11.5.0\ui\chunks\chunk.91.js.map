{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellAdjustmentButton.js"], "names": ["propTypes", "type", "PropTypes", "string", "isFlyoutItem", "bool", "style", "object", "className", "buttonType", "CellAdjustmentButton", "forwardRef", "props", "ref", "menuItems", "dataElement", "icon", "title", "handleClick", "workbook", "core", "getDocument", "getSpreadsheetEditorDocument", "getWorkbook", "activeSheet", "getSheetAt", "activeSheetIndex", "console", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDocumentViewer", "getSpreadsheetEditorManager", "getSelectedCellRange", "CELL_ADJUSTMENT_BUTTONS", "INSERT_COLUMN_LEFT", "createColumns", "firstColumn", "INSERT_COLUMN_RIGHT", "lastColumn", "INSERT_ROW_TOP", "createRow", "firstRow", "INSERT_ROW_BOTTOM", "lastRow", "INSERT_COLUMN_SHIFT_DOWN", "INSERT_COLUMN_SHIFT_RIGHT", "rowIndex", "row", "getRowAt", "columnIndex", "createCell", "DELETE_COLUMN", "removeColumns", "DELETE_ROW", "removeRows", "DELETE_COLUMN_SHIFT_UP", "DELETE_COLUMN_SHIFT_LEFT", "onClick", "additionalClass", "key", "isActive", "img", "ariaPressed", "displayName"], "mappings": "4ZAQA,IAAMA,EAAY,CAChBC,KAAMC,IAAUC,OAChBC,aAAcF,IAAUG,KACxBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUC,OACrBM,WAAYP,IAAUC,QAKlBO,EAAuBC,sBAAW,SAACC,EAAOC,GAC9C,IAAQT,EAAqDQ,EAArDR,aAAcH,EAAuCW,EAAvCX,KAAMK,EAAiCM,EAAjCN,MAAOE,EAA0BI,EAA1BJ,UAAWC,EAAeG,EAAfH,WAG9C,EAAqCK,IAAUL,GAAvCM,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAAM,QAClBC,EAA6B,QAArB,EAAGC,IAAKC,qBAAa,OAAgC,QAAhC,EAAlB,EAAoBC,sCAA8B,WAAhC,EAAlB,EAAoDC,cAC/DC,EAAcL,aAAQ,EAARA,EAAUM,WAAWN,EAASO,kBAClD,IAAKF,EACH,OAAOG,QAAQC,MAZP,2CAcV,IAAMC,EAAoBT,IAAKU,oBAAoBC,8BAA8BC,uBAEjF,OAAQvB,GACN,KAAKwB,IAAwBC,mBAE3B,YADAV,EAAYW,cAAcN,EAAkBO,YAAa,GAE3D,KAAKH,IAAwBI,oBAE3B,YADAb,EAAYW,cAAcN,EAAkBS,WAAa,EAAG,GAE9D,KAAKL,IAAwBM,eAE3B,YADAf,EAAYgB,UAAUX,EAAkBY,UAE1C,KAAKR,IAAwBS,kBAE3B,YADAlB,EAAYgB,UAAUX,EAAkBc,QAAU,GAEpD,KAAKV,IAAwBW,yBAE3B,OACF,KAAKX,IAAwBY,0BAE3B,IAAK,IAAIC,EAAWjB,EAAkBY,SAAUK,GAAYjB,EAAkBc,QAASG,IAErF,IADA,IAAMC,EAAMvB,EAAYwB,SAASF,GACxBG,EAAcpB,EAAkBO,YAAaa,GAAepB,EAAkBS,WAAYW,IACjGF,EAAIG,WAAWD,GAGnB,OACF,KAAKhB,IAAwBkB,cAE3B,YADA3B,EAAY4B,cAAcvB,EAAkBO,YAAaP,EAAkBS,WAAaT,EAAkBO,YAAc,GAE1H,KAAKH,IAAwBoB,WAE3B,YADA7B,EAAY8B,WAAWzB,EAAkBY,SAAUZ,EAAkBc,QAAUd,EAAkBY,SAAW,GAE9G,KAAKR,IAAwBsB,uBAG7B,KAAKtB,IAAwBuB,yBAG7B,QACE,SAIN,OACEpD,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACL4C,QAASvC,EACTwC,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,IAAK1D,EACL2D,UAjES,EAkETH,QAASvC,EACTH,YAAaA,EACbE,MAAOA,EACP4C,IAAK7C,EACL8C,aAtES,EAuETxD,MAAOA,EACPE,UAAWA,OAMrBE,EAAqBV,UAAYA,EACjCU,EAAqBqD,YAAc,uBAEpBrD", "file": "chunks/chunk.91.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { CELL_ADJUSTMENT_BUTTONS } from 'constants/customizationVariables';\nimport core from 'core';\n\nconst propTypes = {\n  type: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n  buttonType: PropTypes.string,\n};\n\nconst ERROR = 'SpreadsheetEditorDocument is not loaded';\n\nconst CellAdjustmentButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, type, style, className, buttonType } = props;\n  const isActive = false;\n\n  const { dataElement, icon, title } = menuItems[buttonType];\n\n  const handleClick = () => {\n    const workbook = core.getDocument()?.getSpreadsheetEditorDocument()?.getWorkbook();\n    const activeSheet = workbook?.getSheetAt(workbook.activeSheetIndex);\n    if (!activeSheet) {\n      return console.error(ERROR);\n    }\n    const selectedCellRange = core.getDocumentViewer().getSpreadsheetEditorManager().getSelectedCellRange();\n\n    switch (buttonType) {\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_COLUMN_LEFT:\n        activeSheet.createColumns(selectedCellRange.firstColumn, 1);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_COLUMN_RIGHT:\n        activeSheet.createColumns(selectedCellRange.lastColumn + 1, 1);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_ROW_TOP:\n        activeSheet.createRow(selectedCellRange.firstRow);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_ROW_BOTTOM:\n        activeSheet.createRow(selectedCellRange.lastRow + 1);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_COLUMN_SHIFT_DOWN:\n        // Finish once leadtools api is implemented\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.INSERT_COLUMN_SHIFT_RIGHT:\n        // Ensure this works or adjust once Leadtools API is updated to work\n        for (let rowIndex = selectedCellRange.firstRow; rowIndex <= selectedCellRange.lastRow; rowIndex++) {\n          const row = activeSheet.getRowAt(rowIndex);\n          for (let columnIndex = selectedCellRange.firstColumn; columnIndex <= selectedCellRange.lastColumn; columnIndex++) {\n            row.createCell(columnIndex);\n          }\n        }\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.DELETE_COLUMN:\n        activeSheet.removeColumns(selectedCellRange.firstColumn, selectedCellRange.lastColumn - selectedCellRange.firstColumn + 1);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.DELETE_ROW:\n        activeSheet.removeRows(selectedCellRange.firstRow, selectedCellRange.lastRow - selectedCellRange.firstRow + 1);\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.DELETE_COLUMN_SHIFT_UP:\n        // Finish once leadtools api is implemented\n        return;\n      case CELL_ADJUSTMENT_BUTTONS.DELETE_COLUMN_SHIFT_LEFT:\n        // Finish once leadtools api is implemented\n        return;\n      default:\n        return;\n    }\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={type}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellAdjustmentButton.propTypes = propTypes;\nCellAdjustmentButton.displayName = 'CellAdjustmentButton';\n\nexport default CellAdjustmentButton;\n"], "sourceRoot": ""}