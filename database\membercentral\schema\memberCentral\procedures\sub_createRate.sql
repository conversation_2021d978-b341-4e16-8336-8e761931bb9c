ALTER PROCEDURE dbo.sub_createRate
@scheduleID int,
@rateName varchar(100),
@reportCode varchar(15),
@status char(1),
@rateStartDate datetime,
@rateEndDate datetime,
@rateStartAFID INT,
@rateEndAFID INT,
@termStartDate datetime,
@termEndDate datetime,
@termStartAFID INT,
@termEndAFID INT,
@graceEndDate datetime,
@graceEndAFID INT,
@recogStartDate datetime,
@recogEndDate datetime,
@recogStartAFID INT,
@recogEndAFID INT,
@rateAdvanceOnTermEnd INT,
@isRenewalRate bit,
@forceUpfront bit,
@accountID INT,
@frontEndAllowChangePrice bit,
@linkedNonRenewalRateID INT,
@fallbackRenewalRateID INT,
@keepChangedPriceOnRenewal bit,
@frontEndChangePriceMin decimal(18,2),
@frontEndChangePriceMax decimal(18,2),
@isImport bit = 0,
@recordedByMemberID INT,
@rateID int OUTPUT,
@siteResourceID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- null output variables
	select @rateID = null, @siteResourceID = null;

	declare @siteID int, @orgID int, @siteResourceTypeID int, @rateOrder int, 
		@linkedNonRenewalRateName varchar(100), @fallbackRenewalRateName varchar(100), @scheduleName varchar(200),
		@crlf varchar(10), @msgjson varchar(max);

	select @siteID = siteid from dbo.sub_rateSchedules where scheduleID = @scheduleID;
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('SubscriptionRate');
	
	SET @crlf = char(13) + char(10);

	if (@frontEndChangePriceMin is not null) and (@frontEndChangePriceMax is not null) and (@frontEndChangePriceMin > @frontEndChangePriceMax) BEGIN
		set @frontEndChangePriceMin = null;
		set @frontEndChangePriceMax = null;
	END

	-- get new rate's sort order
	select @rateOrder = isNull(max(r.rateOrder),0)+1 
	from dbo.sub_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	where r.scheduleID = @scheduleID
	and sr.siteResourceStatusID = 1;

	IF @linkedNonRenewalRateID IS NOT NULL
		SELECT @linkedNonRenewalRateName = rateName
		FROM dbo.sub_rates
		WHERE rateID = @linkedNonRenewalRateID;

	IF @fallbackRenewalRateID IS NOT NULL
		SELECT @fallbackRenewalRateName = rateName
		FROM dbo.sub_rates
		WHERE rateID = @fallbackRenewalRateID;

	SELECT @scheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE scheduleID = @scheduleID;

	BEGIN TRAN;
		-- create a resourceID for the rate
		exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
			@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT;

		-- add rate
		INSERT INTO dbo.sub_rates (scheduleID, siteResourceID, [status], 
			rateStartDate, rateEndDate, rateStartDateAFID, rateEndDateAFID, rateAFStartDate, rateAFEndDate, 
			termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, 
			graceEndDate, graceAFID, 
			recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, recogAFStartDate, recogAFEndDate, 
			rateName, rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, reportCode, GLAccountID, 
			frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, 
			frontEndChangePriceMin, frontEndChangePriceMax, rateOrder)
		VALUES (@scheduleID, @siteResourceID, @status, 
			@rateStartDate, @rateEndDate, @rateStartAFID, @rateEndAFID, @rateStartDate, @rateEndDate, 
			@termStartDate, @termEndDate, @termStartAFID, @termEndAFID, @termStartDate, @termEndDate, 
			@graceEndDate, @graceEndAFID, 
			@recogStartDate, @recogEndDate, @recogStartAFID, @recogEndAFID, @recogStartDate, @recogEndDate, 
			@rateName, @rateAdvanceOnTermEnd, @isRenewalRate, @forceUpfront, @reportCode, @accountID,
			@frontEndAllowChangePrice, @linkedNonRenewalRateID, @fallbackRenewalRateID, @keepChangedPriceOnRenewal,
			@frontEndChangePriceMin, @frontEndChangePriceMax, @rateOrder);

		select @rateID = SCOPE_IDENTITY();

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msgjson = 'New Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] has been created.'
			+ CASE WHEN NULLIF(@reportCode,'') IS NOT NULL THEN @crlf + 'Report Code: ' + @reportCode ELSE '' END
			+ @crlf + 'Rate Start Date: ' + CONVERT(varchar(20), @rateStartDate, 120)
			+ @crlf + 'Rate End Date: ' + CONVERT(varchar(20), @rateEndDate, 120)
			+ CASE WHEN @termStartDate IS NOT NULL THEN @crlf + 'Term Start Date: ' + CONVERT(varchar(20), @termStartDate, 120) ELSE '' END
			+ CASE WHEN @termEndDate IS NOT NULL THEN @crlf + 'Term End Date: ' + CONVERT(varchar(20), @termEndDate, 120) ELSE '' END
			+ CASE WHEN @graceEndDate IS NOT NULL THEN @crlf + 'Grace End Date: ' + CONVERT(varchar(20), @graceEndDate, 120) ELSE '' END
			+ CASE WHEN @recogStartDate IS NOT NULL THEN @crlf + 'Recognition Start Date: ' + CONVERT(varchar(20), @recogStartDate, 120) ELSE '' END
			+ CASE WHEN @recogEndDate IS NOT NULL THEN @crlf + 'Recognition End Date: ' + CONVERT(varchar(20), @recogEndDate, 120) ELSE '' END
			+ CASE WHEN @rateAdvanceOnTermEnd IS NOT NULL THEN @crlf + 'Rate Advance On Term End: ' + CAST(@rateAdvanceOnTermEnd AS VARCHAR(10)) ELSE '' END
			+ CASE WHEN @isRenewalRate IS NOT NULL THEN @crlf + 'Is Renewal Rate: ' + CASE WHEN @isRenewalRate = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @forceUpfront IS NOT NULL THEN @crlf + 'Force Upfront: ' + CASE WHEN @forceUpfront = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frontEndAllowChangePrice IS NOT NULL THEN @crlf + 'Front End Allow Change Price: ' + CASE WHEN @frontEndAllowChangePrice = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @linkedNonRenewalRateID IS NOT NULL THEN @crlf + 'Linked Non Renewal Rate: ' + @linkedNonRenewalRateName ELSE '' END
			+ CASE WHEN @fallbackRenewalRateID IS NOT NULL THEN @crlf + 'Fallback Renewal Rate: ' + @fallbackRenewalRateName ELSE '' END
			+ CASE WHEN @keepChangedPriceOnRenewal IS NOT NULL THEN @crlf + 'Keep Changed Price On Renewal: ' + CASE WHEN @keepChangedPriceOnRenewal = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frontEndChangePriceMin IS NOT NULL THEN @crlf + 'Front End Change Price Min: ' + CAST(@frontEndChangePriceMin AS VARCHAR(20)) ELSE '' END
			+ CASE WHEN @frontEndChangePriceMax IS NOT NULL THEN @crlf + 'Front End Change Price Max: ' + CAST(@frontEndChangePriceMax AS VARCHAR(20)) ELSE '' END;

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msgjson, 
			@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
