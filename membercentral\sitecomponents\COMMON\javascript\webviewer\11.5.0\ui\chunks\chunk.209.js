(window.webpackJsonp = window.webpackJsonp || []).push([
	[209],
	{
		1467: function (a, u, n) {
			a.exports = (function (a) {
				"use strict";
				var u = (function (a) {
						return a && "object" == typeof a && "default" in a
							? a
							: { default: a };
					})(a),
					n = {
						name: "rw",
						weekdays:
							"Ku Cyumweru_Kuwa Mbere_Kuwa Kabiri_Kuwa Gatatu_Kuwa Kane_Kuwa Gatanu_Kuwa Gatandatu".split(
								"_",
							),
						months:
							"Mutarama_Gashyantare_Werurwe_Mata_Gicurasi_Kamena_Nyakanga_Kanama_Nzeri_Ukwakira_Ugushyingo_Ukuboza".split(
								"_",
							),
						relativeTime: {
							future: "mu %s",
							past: "%s",
							s: "amasegonda",
							m: "Umunota",
							mm: "%d iminota",
							h: "isaha",
							hh: "%d amasaha",
							d: "Umunsi",
							dd: "%d iminsi",
							M: "ukwezi",
							MM: "%d amezi",
							y: "umwaka",
							yy: "%d imyaka",
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY HH:mm",
							LLLL: "dddd, D MMMM YYYY HH:mm",
						},
						ordinal: function (a) {
							return a;
						},
					};
				return u.default.locale(n, null, !0), n;
			})(n(105));
		},
	},
]);
//# sourceMappingURL=chunk.209.js.map
