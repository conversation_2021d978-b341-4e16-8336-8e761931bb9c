{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/capitalize.js", "webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CopyPasteCutButton.js"], "names": ["capitalize", "str", "char<PERSON>t", "toUpperCase", "slice", "propTypes", "actionType", "PropTypes", "oneOf", "Object", "values", "CELL_ACTION_OPTIONS", "isRequired", "isFlyoutItem", "bool", "style", "object", "className", "string", "CopyPasteCutButton", "forwardRef", "props", "ref", "buttonSelector", "menuItems", "dataElement", "icon", "title", "handleClick", "FlyoutItemContainer", "onClick", "additionalClass", "ActionButton", "key", "isActive", "img", "ariaPressed", "displayName"], "mappings": "0LAOeA,G,MAPI,SAACC,GAClB,MAAmB,iBAARA,EACF,GAEFA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIG,MAAM,K,mOCIjD,IAAMC,EAAY,CAChBC,WAAYC,IAAUC,MAAMC,OAAOC,OAAOC,MAAsBC,WAChEC,aAAcN,IAAUO,KACxBC,MAAOR,IAAUS,OACjBC,UAAWV,IAAUW,QAGjBC,EAAqBC,sBAAW,SAACC,EAAOC,GAC5C,IAAQT,EAA+CQ,EAA/CR,aAAcP,EAAiCe,EAAjCf,WAAYS,EAAqBM,EAArBN,MAAOE,EAAcI,EAAdJ,UAGnCM,EAAiB,OAAH,OAAUvB,EAAWM,IACzC,EAAqCkB,IAAUD,GAAvCE,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,aAIpB,OACEf,EACE,kBAACgB,EAAA,EAAmB,KACdR,EAAK,CACTC,IAAKA,EACLQ,QAASF,EACTG,gBAAuC,MAGvC,kBAACC,EAAA,EAAY,CACXC,IAAK3B,EACL4B,UApBS,EAqBTJ,QAASF,EACTH,YAAaA,EACbE,MAAOA,EACPQ,IAAKT,EACLU,aAzBS,EA0BTrB,MAAOA,EACPE,UAAWA,OAMrBE,EAAmBd,UAAYA,EAC/Bc,EAAmBkB,YAAc,qBAElBlB", "file": "chunks/chunk.94.js", "sourcesContent": ["const capitalize = (str) => {\n  if (typeof str !== 'string') {\n    return '';\n  }\n  return str.charAt(0).toUpperCase() + str.slice(1);\n};\n\nexport default capitalize;\n", "import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\nimport { CELL_ACTION_OPTIONS } from 'constants/spreadsheetEditor';\nimport capitalize from 'helpers/capitalize';\n\nconst propTypes = {\n  actionType: PropTypes.oneOf(Object.values(CELL_ACTION_OPTIONS)).isRequired,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CopyPasteCutButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, actionType, style, className } = props;\n  const isActive = false;\n\n  const buttonSelector = `cell${capitalize(actionType)}`;\n  const { dataElement, icon, title } = menuItems[buttonSelector];\n\n  const handleClick = () => {\n    // handle button click\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={actionType}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCopyPasteCutButton.propTypes = propTypes;\nCopyPasteCutButton.displayName = 'CopyPasteCutButton';\n\nexport default CopyPasteCutButton;"], "sourceRoot": ""}