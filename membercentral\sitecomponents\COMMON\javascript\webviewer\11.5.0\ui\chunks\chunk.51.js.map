{"version": 3, "sources": ["webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.scss?ba6e", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.scss", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.scss?3684", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.scss", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/HeaderFooterControlsBar.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsBar/index.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/HeaderFooterControlsOverlay.js", "webpack:///./src/ui/src/components/HeaderFooterControlsOverlay/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "type", "PropTypes", "oneOf", "pageNumber", "number", "isActive", "bool", "HeaderFooterControlsBar", "t", "useTranslation", "dispatch", "useDispatch", "doc", "core", "getDocument", "officeEditor", "getOfficeEditor", "blockerRef", "useRef", "dropdownId", "barId", "useState", "containerStyle", "setContainerStyle", "sectionNumber", "setSectionNumber", "headerType", "setHeaderType", "footerType", "setFooterType", "optionsDisabled", "setOptionsDisabled", "barClassName", "classNames", "getHeaderFooterTop", "heightOfBar", "current", "clientHeight", "getHeaderPosition", "getFooterPosition", "updateHeaderFooterSectionNumber", "getSectionNumber", "useEffect", "onBarClick", "event", "actions", "closeElements", "DataElements", "CONTEXT_MENU_POPUP", "stopPropagation", "eventType", "addEventListener", "onHeaderFooterUpdated", "updateHeaderFooterTop", "top", "onEditModeUpdated", "editMode", "isReadOnlyMode", "OfficeEditorEditMode", "VIEW_ONLY", "PREVIEW", "headerFooterStyle", "HEADER_FOOTER_BAR_DEFAULT_POSITION", "bottom", "removeEventListener", "onLayoutChange", "e", "source", "getHeaderPageType", "getFooterPageType", "handlePageOptionsClick", "HeaderFooterModalState", "setPageNumber", "openElement", "HEADER_FOOTER_OPTIONS_MODAL", "dropdownItems", "label", "key", "icon", "onClick", "removeHeaders", "removeFooters", "onClickItem", "itemKey", "item", "find", "sectionLabel", "layoutType", "className", "id", "style", "ref", "Dropdown", "width", "renderItem", "Icon", "glyph", "<PERSON><PERSON><PERSON>", "items", "displayButton", "isOpen", "ActionButton", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ariaControls", "ariaExpanded", "img", "disabled", "stopPropagationOnMouseDown", "React", "memo", "visiblePages", "arrayOf", "isHeaderControlsActive", "isFooterControlsActive", "HeaderFooterControlsOverlay", "portals", "map", "pageSection", "getRootNode", "getElementById", "createPortal"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,mkGAAskG,KAG/lG0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,kGAAmG,M,8uBCJ5H,8lGAAAA,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAeA,IAAM4B,EAAY,CAChBC,KAAMC,IAAUC,MAAM,CAAC,SAAU,WACjCC,WAAYF,IAAUG,OACtBC,SAAUJ,IAAUK,MAGhBC,EAA0B,SAAH,GAAuC,IAAjCP,EAAI,EAAJA,KAAMG,EAAU,EAAVA,WAAYE,EAAQ,EAARA,SAC5CG,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cACXC,EAAMC,IAAKC,cACXC,EAAeH,EAAII,kBACnBC,EAAaC,mBACbC,EAAa,GAAH,OAAMnB,EAAI,6BAAqBG,GACzCiB,EAAQ,GAAH,OAAMpB,EAAI,oBAAYG,GAEuB,IAAZkB,mBAAS,IAAG,GAAjDC,EAAc,KAAEC,EAAiB,KACgB,IAAdF,mBAAS,MAAK,GAAjDG,EAAa,KAAEC,EAAgB,KACS,IAAXJ,mBAAS,GAAE,GAAxCK,EAAU,KAAEC,EAAa,KACe,IAAXN,mBAAS,GAAE,GAAxCO,EAAU,KAAEC,EAAa,KAC6B,IAAfR,oBAAS,GAAM,GAAtDS,EAAe,KAAEC,EAAkB,KAEpCC,EAAeC,IACnB,wBAAuB,UACpBjC,EAAI,YACP,CAAE,OAAUK,IAGR6B,EAAqB,WAAM,MACzBC,GAAgC,QAAlB,EAAAlB,EAAWmB,eAAO,aAAlB,EAAoBC,eAAgB,EACxD,OAAQrC,GACN,IAAK,SACH,OAAOe,EAAauB,kBAAkBnC,GACxC,IAAK,SACH,OAAOY,EAAawB,kBAAkBpC,GAAcgC,EACtD,QACE,OAAO,IAIPK,EAA+B,6BAAG,oGACVzB,EAAa0B,iBAAiBtC,GAAW,OAA/DqB,EAAgB,EAAH,KACnBC,EAAiBD,GAAe,2CACjC,kBAHoC,mCAKrCkB,qBAAU,WAER,IAAMC,EAAa,SAACC,GACC,cAAfA,EAAM5C,MACRU,EAASmC,IAAQC,cAAc,CAACC,IAAaC,sBAE/CJ,EAAMK,mBAGR,CAAC,QAAS,YAAa,UAAW,YAAa,aAAc,aAAc,eAAe9D,SAAQ,SAAC+D,GACjGjC,EAAWmB,QAAQe,iBAAiBD,EAAWP,MAGjD,IAAMS,EAAwB,WACxB/C,IACFgD,IACAb,MAIEa,EAAqB,6BAAG,qFACtBC,EAAMpB,KACF,GAGRX,EAAkB,CAAE+B,QACrB,2CACF,kBAP0B,mCAS3B1C,EAAIuC,iBAAiB,sBAAuBC,GAE5C,IAAMG,EAAoB,SAACC,GACzB,IAAMC,EAAiBD,IAAaE,IAAqBC,WAAaH,IAAaE,IAAqBE,QACxG7B,EAAmB0B,IAErB7C,EAAIuC,iBAAiB,kBAAmBI,GAExC,IACQM,EACAP,EAKR,OAFA/B,GAJQsC,EAA6B,WAAT7D,EAAoB,CAAEsD,IAAKQ,KAAuC,CAAEC,OAAQD,MAChGR,EAAMpB,KACC,EAAI,CAAEoB,OAAQO,IAItB,WACL,CAAC,QAAS,YAAa,UAAW,YAAa,aAAc,aAAc,eAAe1E,SAAQ,SAAC+D,GAAc,MAC7F,QAAlB,EAAAjC,EAAWmB,eAAO,OAAlB,EAAoB4B,oBAAoBd,EAAWP,MAGrD/B,EAAIoD,oBAAoB,sBAAuBZ,GAC/CxC,EAAIoD,oBAAoB,kBAAmBT,MAE5C,CAAClD,IAEJqC,qBAAU,WACR,IAAMuB,EAAiB,SAACC,GACtB,GAAiB,iBAAbA,EAAEC,OAAN,CAGA,IAAMzC,EAAab,IAAKG,kBAAkBoD,kBAAkBjE,GACtDyB,EAAaf,IAAKG,kBAAkBqD,kBAAkBlE,IAC7C,GAAfuB,GAAoBC,EAAcD,IACnB,GAAfE,GAAoBC,EAAcD,KAGpC,OADAhB,EAAIuC,iBAAiB,uBAAwBc,GACtC,WACLrD,EAAIoD,oBAAoB,uBAAwBC,MAEjD,CAAC9D,IAEJuC,qBAAU,WACR,IAAMhB,EAAaX,EAAaqD,kBAAkBjE,GAC5CyB,EAAab,EAAasD,kBAAkBlE,IACnC,GAAfuB,GAAoBC,EAAcD,IACnB,GAAfE,GAAoBC,EAAcD,GAClCY,MACC,CAACnC,EAAUF,IAEd,IAAMmE,EAAsB,6BAAG,8EAC7BC,IAAuBC,cAAcrE,GACrCO,EAASmC,IAAQ4B,YAAY1B,IAAa2B,8BAA8B,2CACzE,kBAH2B,mCAyBtBC,EAAgB,CACpB,CACEC,MAAOpE,EAAE,4BACTqE,IAAK,eACLC,KAAM,eACNC,QAAST,GAEX,CACEM,MAA2BpE,EAAX,WAATR,EAAsB,4BAAiC,6BAC9D6E,IAAK,UAAF,OAAY7E,GACf8E,KAAM,iBACNC,QA/BoC,WACtC,MAAa,WAAT/E,EACKe,EAAaiE,cAAc7E,GAEvB,WAATH,EACKe,EAAakE,cAAc9E,QADpC,KA+BI+E,EAAW,6BAAG,WAAOC,GAAO,uEAC+B,OAAzDC,EAAOT,EAAcU,MAAK,SAACD,GAAI,OAAKA,EAAKP,MAAQM,KAAQ,SACzDC,aAAI,EAAJA,EAAML,UAAS,2CACtB,gBAHgB,sCAiBXO,EAAe9D,EAAgB,MAAH,OAAShB,EAAE,wBAAuB,YAAIgB,GAAkB,GAEpF+D,EAAsB,WAATvF,EAAoB0B,EAAaE,EAEpD,OACE,yBAAK4D,UAAWxD,EAAcyD,GAAIrE,EAAOsE,MAAOpE,GAC9C,yBAAKkE,UAAU,iBAAiBG,IAAK1E,IACrC,yBAAKuE,UAAU,SAAShF,EAAE,gBAAD,OAAiBR,EAAI,YAAIuF,IAAeD,GAEjE,kBAACM,EAAA,EAAQ,CACPC,MAAM,OACNJ,GAAItE,EACJ2E,WAvDqB,SAACV,GAAI,OAC9B,oCACE,kBAACW,EAAA,EAAI,CAACC,MAAOZ,EAAKN,KAAMU,UAAU,wBAClC,yBAAKA,UAAU,2BACb,yBAAKA,UAAU,wBAAwBhF,EAAE4E,EAAKR,SAElC,iBAAbQ,EAAKP,KAA0B,yBAAKW,UAAU,cAkD7CA,UAAU,6BACVS,OAAQ,SAACb,GAAI,OAAKA,EAAKP,KACvBqB,MAAOvB,EACPO,YAAaA,EACbiB,cA7BuB,SAACC,GAAM,OAClC,kBAACC,EAAA,EAAY,CACXb,UAAU,iBACVc,eAAgBlF,EAChBmF,aAAY,UAAKpF,EAAU,aAC3BqF,aAAcJ,EACdK,IAAK,6BACL7B,MAAOpE,EAAE,wBACTH,SAAU+F,EACVM,SAAU5E,KAqBR6E,4BAA4B,EAC5BD,SAAU5E,MAMlBvB,EAAwBR,UAAYA,EAErB6G,IC5NArG,ED4NAqG,IAAMC,KAAKtG,GEtNpBR,G,QAAY,CAChB+G,aAAc7G,IAAU8G,QAAQ9G,IAAUG,QAC1C4G,uBAAwB/G,IAAUK,KAClC2G,uBAAwBhH,IAAUK,OAG9B4G,EAA8B,SAAH,GAAyE,IAAnEJ,EAAY,EAAZA,aAAcE,EAAsB,EAAtBA,uBAAwBC,EAAsB,EAAtBA,uBACrEE,EAAUL,EAAaM,KAAI,SAACjH,GAChC,IAAMkH,EAAcC,cAAcC,eAAe,cAAD,OAAepH,IAC/D,OAAKkH,EAIEG,uBACL,yBAAK3C,IAAK1E,EAAYqF,UAAU,+BAC9B,kBAAC,EAAuB,CAACxF,KAAK,SAASG,WAAYA,EAAYE,SAAU2G,IACzE,kBAAC,EAAuB,CAAChH,KAAK,SAASG,WAAYA,EAAYE,SAAU4G,KAE3EI,GARO,QAYX,OACE,oCACGF,IAKPD,EAA4BnH,UAAYA,EAEzBmH,QCrCAA", "file": "chunks/chunk.51.js", "sourcesContent": ["var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterControlsBar.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.header-footer-edit-ui{position:absolute;height:46px;width:100%;align-items:center;display:flex;visibility:hidden}.header-footer-edit-ui.header-edit-ui{top:143px}.header-footer-edit-ui.footer-edit-ui{bottom:143px}.header-footer-edit-ui.active{visibility:visible}.header-footer-edit-ui .box-shadow-div{width:100%;height:100%;overflow:hidden;position:absolute;display:flex;align-items:center}.header-footer-edit-ui .box-shadow-div:after{content:\\\"\\\";position:absolute;width:100%;height:40px;padding:3px 0;background-color:var(--gray-0);box-shadow:0 0 3px 0 var(--gray-7);z-index:-1}.header-footer-edit-ui .label{color:var(--gray-8);font-size:.8125rem;font-weight:700;margin-left:16px}.header-footer-edit-ui .options-dropdown-container{position:absolute;right:16px}.header-footer-edit-ui .options-dropdown-container .options-button{color:var(--blue-5);padding:8px 32px 8px 8px;background:transparent;width:auto;border:none;cursor:pointer}.header-footer-edit-ui .options-dropdown-container .options-button .Icon{width:16px;height:16px;position:absolute;right:9.5px;top:0;bottom:0;margin:auto;color:var(--blue-5)}.header-footer-edit-ui .options-dropdown-container .options-button.active,.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon,.header-footer-edit-ui .options-dropdown-container .options-button:hover,.header-footer-edit-ui .options-dropdown-container .options-button:hover .Icon{color:var(--blue-6)}.header-footer-edit-ui .options-dropdown-container .options-button.active .Icon{transform:rotate(180deg)}.header-footer-edit-ui .options-dropdown-container .Dropdown__items{padding:4px 0;min-width:157px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item{height:32px;padding:0 10px 0 40px;position:relative;width:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item:first-child{margin-bottom:9px}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Dropdown__item .Icon{position:absolute;left:10px;fill:var(--gray-7);top:0;bottom:0;margin:auto}.header-footer-edit-ui .options-dropdown-container .Dropdown__items .Divider{flex-basis:100%;width:100%;height:1px;margin:0;position:absolute;bottom:-5px;left:0}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./HeaderFooterControlsOverlay.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".HeaderFooterControlsOverlay{position:absolute;top:0;left:0;width:100%;height:100%;z-index:501}\", \"\"]);\n\n// exports\n", "import React, { useEffect, useRef, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport Dropdown from 'components/Dropdown';\nimport classNames from 'classnames';\nimport core from 'core';\nimport HeaderFooterModalState from 'helpers/headerFooterModalState';\nimport { HEADER_FOOTER_BAR_DEFAULT_POSITION, OfficeEditorEditMode } from 'constants/officeEditor';\n\nimport './HeaderFooterControlsBar.scss';\n\nconst propTypes = {\n  type: PropTypes.oneOf(['header', 'footer']),\n  pageNumber: PropTypes.number,\n  isActive: PropTypes.bool,\n};\n\nconst HeaderFooterControlsBar = ({ type, pageNumber, isActive }) => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n  const doc = core.getDocument();\n  const officeEditor = doc.getOfficeEditor();\n  const blockerRef = useRef();\n  const dropdownId = `${type}-options-dropdown-${pageNumber}`;\n  const barId = `${type}-edit-ui-${pageNumber}`;\n\n  const [containerStyle, setContainerStyle] = useState({});\n  const [sectionNumber, setSectionNumber] = useState(null);\n  const [headerType, setHeaderType] = useState(0); // 0 is default for header type all\n  const [footerType, setFooterType] = useState(0); // 0 is default for footer type all\n  const [optionsDisabled, setOptionsDisabled] = useState(false);\n\n  const barClassName = classNames(\n    'header-footer-edit-ui',\n    `${type}-edit-ui`,\n    { 'active': isActive }\n  );\n\n  const getHeaderFooterTop = () => {\n    const heightOfBar = blockerRef.current?.clientHeight || 0;\n    switch (type) {\n      case 'header':\n        return officeEditor.getHeaderPosition(pageNumber);\n      case 'footer':\n        return officeEditor.getFooterPosition(pageNumber) - heightOfBar;\n      default:\n        return 0;\n    }\n  };\n\n  const updateHeaderFooterSectionNumber = async () => {\n    const sectionNumber = await officeEditor.getSectionNumber(pageNumber);\n    setSectionNumber(sectionNumber);\n  };\n\n  useEffect(() => {\n    // This stops the cursor from moving when the user clicks on the bar\n    const onBarClick = (event) => {\n      if (event.type === 'mousedown') {\n        dispatch(actions.closeElements([DataElements.CONTEXT_MENU_POPUP]));\n      }\n      event.stopPropagation();\n    };\n\n    ['click', 'mousedown', 'mouseup', 'mousemove', 'mouseenter', 'mouseleave', 'contextmenu'].forEach((eventType) => {\n      blockerRef.current.addEventListener(eventType, onBarClick);\n    });\n\n    const onHeaderFooterUpdated = () => {\n      if (isActive) {\n        updateHeaderFooterTop();\n        updateHeaderFooterSectionNumber();\n      }\n    };\n\n    const updateHeaderFooterTop = async () => {\n      const top = getHeaderFooterTop();\n      if (top > 0) {\n        // Sometimes the headerTop is 0 when the data isn't loaded.\n        // In this case just ignore it and wait for the next event to update the position\n        setContainerStyle({ top });\n      }\n    };\n\n    doc.addEventListener('headerFooterUpdated', onHeaderFooterUpdated);\n\n    const onEditModeUpdated = (editMode) => {\n      const isReadOnlyMode = editMode === OfficeEditorEditMode.VIEW_ONLY || editMode === OfficeEditorEditMode.PREVIEW;\n      setOptionsDisabled(isReadOnlyMode);\n    };\n    doc.addEventListener('editModeUpdated', onEditModeUpdated);\n\n    const getInitialHeaderFooterStyle = () => {\n      const headerFooterStyle = type === 'header' ? { top: HEADER_FOOTER_BAR_DEFAULT_POSITION } : { bottom: HEADER_FOOTER_BAR_DEFAULT_POSITION };\n      const top = getHeaderFooterTop();\n      return top > 0 ? { top } : headerFooterStyle;\n    };\n    setContainerStyle(getInitialHeaderFooterStyle());\n\n    return () => {\n      ['click', 'mousedown', 'mouseup', 'mousemove', 'mouseenter', 'mouseleave', 'contextmenu'].forEach((eventType) => {\n        blockerRef.current?.removeEventListener(eventType, onBarClick);\n      });\n\n      doc.removeEventListener('headerFooterUpdated', onHeaderFooterUpdated);\n      doc.removeEventListener('editModeUpdated', onEditModeUpdated);\n    };\n  }, [isActive]);\n\n  useEffect(() => {\n    const onLayoutChange = (e) => {\n      if (e.source !== 'headerFooter') {\n        return;\n      }\n      const headerType = core.getOfficeEditor().getHeaderPageType(pageNumber);\n      const footerType = core.getOfficeEditor().getFooterPageType(pageNumber);\n      headerType != -1 && setHeaderType(headerType);\n      footerType != -1 && setFooterType(footerType);\n    };\n    doc.addEventListener('officeDocumentEdited', onLayoutChange);\n    return () => {\n      doc.removeEventListener('officeDocumentEdited', onLayoutChange);\n    };\n  }, [pageNumber]);\n\n  useEffect(() => {\n    const headerType = officeEditor.getHeaderPageType(pageNumber);\n    const footerType = officeEditor.getFooterPageType(pageNumber);\n    headerType != -1 && setHeaderType(headerType);\n    footerType != -1 && setFooterType(footerType);\n    updateHeaderFooterSectionNumber();\n  }, [isActive, pageNumber]);\n\n  const handlePageOptionsClick = async () => {\n    HeaderFooterModalState.setPageNumber(pageNumber);\n    dispatch(actions.openElement(DataElements.HEADER_FOOTER_OPTIONS_MODAL));\n  };\n\n  const handleRemoveHeaderFooterOnClick = () => {\n    if (type === 'header') {\n      return officeEditor.removeHeaders(pageNumber);\n    }\n    if (type === 'footer') {\n      return officeEditor.removeFooters(pageNumber);\n    }\n  };\n\n  const renderDropdownItem = (item) => (\n    <>\n      <Icon glyph={item.icon} className='Dropdown__item-icon' />\n      <div className='Dropdown__item-vertical'>\n        <div className='Dropdown__item-label'>{t(item.label)}</div>\n      </div>\n      {item.key === 'page-options' && <div className='Divider'></div>}\n    </>\n  );\n\n\n  const dropdownItems = [\n    {\n      label: t('officeEditor.pageOptions'),\n      key: 'page-options',\n      icon: 'ic-edit-page',\n      onClick: handlePageOptionsClick,\n    },\n    {\n      label: type === 'header' ? t('officeEditor.removeHeader') : t('officeEditor.removeFooter'),\n      key: `remove-${type}`,\n      icon: 'ic-delete-page',\n      onClick: handleRemoveHeaderFooterOnClick,\n    },\n  ];\n\n  const onClickItem = async (itemKey) => {\n    const item = dropdownItems.find((item) => item.key === itemKey);\n    await item?.onClick();\n  };\n\n  const renderDropdownButton = (isOpen) => (\n    <ActionButton\n      className='options-button'\n      ariaLabelledby={barId}\n      ariaControls={`${dropdownId}-dropdown`}\n      ariaExpanded={isOpen}\n      img={'ic_chevron_down_black_24px'}\n      label={t('officeEditor.options')}\n      isActive={isOpen}\n      disabled={optionsDisabled}\n    />\n  );\n  const sectionLabel = sectionNumber ? ` - ${t('officeEditor.section')} ${sectionNumber}` : '';\n\n  const layoutType = type === 'header' ? headerType : footerType;\n\n  return (\n    <div className={barClassName} id={barId} style={containerStyle}>\n      <div className='box-shadow-div' ref={blockerRef}></div>\n      <div className='label'>{t(`officeEditor.${type}.${layoutType}`)}{sectionLabel}</div>\n\n      <Dropdown\n        width='auto'\n        id={dropdownId}\n        renderItem={renderDropdownItem}\n        className='options-dropdown-container'\n        getKey={(item) => item.key}\n        items={dropdownItems}\n        onClickItem={onClickItem}\n        displayButton={renderDropdownButton}\n        stopPropagationOnMouseDown={true}\n        disabled={optionsDisabled}\n      />\n    </div>\n  );\n};\n\nHeaderFooterControlsBar.propTypes = propTypes;\n\nexport default React.memo(HeaderFooterControlsBar);\n", "import HeaderFooterControlsBar from './HeaderFooterControlsBar';\n\nexport default HeaderFooterControlsBar;\n", "import React from 'react';\nimport { createPortal } from 'react-dom';\nimport getRootNode from 'src/helpers/getRootNode';\nimport HeaderFooterControlsBar from './HeaderFooterControlsBar';\nimport PropTypes from 'prop-types';\n\nimport './HeaderFooterControlsOverlay.scss';\n\nconst propTypes = {\n  visiblePages: PropTypes.arrayOf(PropTypes.number),\n  isHeaderControlsActive: PropTypes.bool,\n  isFooterControlsActive: PropTypes.bool,\n};\n\nconst HeaderFooterControlsOverlay = ({ visiblePages, isHeaderControlsActive, isFooterControlsActive }) => {\n  const portals = visiblePages.map((pageNumber) => {\n    const pageSection = getRootNode().getElementById(`pageSection${pageNumber}`);\n    if (!pageSection) {\n      return null;\n    }\n\n    return createPortal(\n      <div key={pageNumber} className='HeaderFooterControlsOverlay'>\n        <HeaderFooterControlsBar type='header' pageNumber={pageNumber} isActive={isHeaderControlsActive} />\n        <HeaderFooterControlsBar type='footer' pageNumber={pageNumber} isActive={isFooterControlsActive} />\n      </div>,\n      pageSection\n    );\n  });\n\n  return (\n    <>\n      {portals}\n    </>\n  );\n};\n\nHeaderFooterControlsOverlay.propTypes = propTypes;\n\nexport default HeaderFooterControlsOverlay;\n", "import HeaderFooterControlsOverlay from './HeaderFooterControlsOverlay';\n\nexport default HeaderFooterControlsOverlay;\n"], "sourceRoot": ""}