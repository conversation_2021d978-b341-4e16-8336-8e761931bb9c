ALTER PROCEDURE dbo.sub_deleteSubscription 
@orgID INT, 
@siteID INT,
@subscriptionID INT,
@isImport BIT = 0,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @minConditionID int, @CIDList varchar(max), @contentSRID int, @subscriptionName varchar(300), 
		@typeID int, @subscriptionType varchar(100), @msgjson varchar(max);

	SELECT @subscriptionName = ss.subscriptionName, @typeID = st.typeID, @subscriptionType = st.typeName
	FROM dbo.sub_subscriptions as ss
	INNER JOIN dbo.sub_types as st ON st.siteID = @siteID
		AND ss.typeID = st.typeID
	WHERE ss.subscriptionID = @subscriptionID;

	-- is sub not deleted?
	IF EXISTS (
		SELECT s.subscriptionID 
		FROM dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode <> 'D'
		WHERE s.subscriptionID = @subscriptionID)
		GOTO on_done;

	-- delete any conditions that depend on this subscription
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(c.conditionID as varchar(10)) 
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@subscriptionID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubscription'
	where c.orgID = @orgID

	BEGIN TRAN;
		IF @CIDList is not null
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList, 
				@recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

		-- mark sub as deleted
		UPDATE dbo.sub_subscriptions
		set [status] = 'D'
		where subscriptionID = @subscriptionID;

		-- delete content object
		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_subscriptions subs
		inner join dbo.cms_content cc on subs.frontEndContentID = cc.contentID
			and subs.subscriptionID = @subscriptionID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_subscriptions subs
		inner join dbo.cms_content cc on subs.frontEndCompletedContentID = cc.contentID
			and subs.subscriptionID = @subscriptionID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		DELETE FROM dbo.hooks_webhooks_subscriptionStatusChange
		WHERE subscriptionID = @subscriptionID;

		IF @@ROWCOUNT > 0 BEGIN
			DELETE FROM dbo.hooks_webhooks_subscriptionStatusChangeRules
			WHERE ruleID NOT IN (
				SELECT DISTINCT ruleID FROM dbo.hooks_webhooks_subscriptionStatusChange
			);
		END

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Subscription ' + QUOTENAME(@subscriptionName) + ' under the Subscription Type ' + QUOTENAME(@subscriptionType) + ' has been deleted.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
