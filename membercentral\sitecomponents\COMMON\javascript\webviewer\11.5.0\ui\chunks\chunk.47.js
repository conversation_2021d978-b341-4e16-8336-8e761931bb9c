(window.webpackJsonp = window.webpackJsonp || []).push([
	[47],
	{
		1618: function (e, t, n) {
			var r = n(30),
				i = n(1619);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const r = [];
								return (
									n.querySelectorAll(t).forEach((e) => r.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...e(t, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < t.length; r++) {
						const i = t[r];
						if (0 === r)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			r(i, a);
			e.exports = i.locals || {};
		},
		1619: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".SignaturePanel .spinner{margin:10px;border:5px solid #ddd;border-top-color:#aaa;border-radius:50%;width:40px;height:40px;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",
				"",
			]);
		},
		1620: function (e, t, n) {
			var r = n(30),
				i = n(1621);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const r = [];
								return (
									n.querySelectorAll(t).forEach((e) => r.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...e(t, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < t.length; r++) {
						const i = t[r];
						if (0 === r)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			r(i, a);
			e.exports = i.locals || {};
		},
		1621: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".signature-widget-info{--widget-header-indent:49px;--widget-body-indent:22px;--arrow-width:12px;padding:4px;display:flex;flex-direction:column;font-size:13px;margin-bottom:10px;margin-left:5px;box-sizing:border-box;border:1px solid transparent;cursor:pointer;--border-radius-amount:4px;-moz-border-radius-topleft:var(--border-radius-amount);-moz-border-radius-topright:var(--border-radius-amount);-moz-border-radius-bottomright:var(--border-radius-amount);-moz-border-radius-bottomleft:var(--border-radius-amount);border-radius:var(--border-radius-amount)}.signature-widget-info p+p{margin:1em 0 0}.signature-widget-info p.result-for-header{margin-top:0}.signature-widget-info p.bold{font-weight:700;margin-bottom:4px}.signature-widget-info p.underline{text-decoration:underline}.signature-widget-info .signatureProperties{padding:0;margin:0}.signature-widget-info .signatureProperties:focus,.signature-widget-info .signatureProperties:hover{color:var(--blue-5)}.signature-widget-info .link{cursor:pointer;outline:none;border:0;background-color:transparent;white-space:nowrap}.signature-widget-info .link.focus-visible,.signature-widget-info .link:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .link p{margin:0;padding:0}.signature-widget-info .panel-list-text-container{height:100%}.signature-widget-info .panel-list-text-container .panel-list-label-header .Button span{text-align:left;overflow:visible;white-space:normal;text-overflow:inherit}.signature-widget-info .panel-list-icon-container .Icon{width:20px;height:20px}.signature-widget-info .title{padding-left:4px;font-weight:700;display:flex;align-items:center;min-height:32px;margin-top:-5px;margin-bottom:-5px;overflow:hidden;border:0;background-color:transparent}.signature-widget-info .title button+*,.signature-widget-info .title div+*{margin-left:2px}.signature-widget-info .title .arrow{min-width:var(--arrow-width);transition:transform .1s ease;margin-top:0;background-color:transparent;border:none;padding:0;display:flex;justify-content:center;align-items:center}.signature-widget-info .title .arrow.expanded{transform:rotate(90deg)}.signature-widget-info .title .arrow .Icon{width:var(--arrow-width);height:var(--arrow-width)}.signature-widget-info .title .arrow.hidden{visibility:hidden;padding:23px}.signature-widget-info .title .arrow:hover .Icon{color:var(--blue-6)}.signature-widget-info .title .arrow.focus-visible,.signature-widget-info .title .arrow:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .title .signature-icon{margin-right:5px}.signature-widget-info .title.focus-visible,.signature-widget-info .title:focus-visible{outline:var(--focus-visible-outline)}.signature-widget-info .header{margin-left:32px}.signature-widget-info .header ul{padding-left:24px}.signature-widget-info .header .body>div:first-child>p:first-child,.signature-widget-info .header .body>p:first-child{margin-top:.5em}.signature-widget-info .header .body>div:last-child{margin-bottom:.5em}.signature-widget-info .header-with-arrow{margin-left:0}.signature-widget-info .header-with-arrow ul{margin-left:var(--arrow-width)}.signature-widget-info .header-with-arrow ul li{margin-left:18px}.signature-widget-info .panel-list-label-header Button{font-weight:700;font-size:13px}.signature-widget-info .signatureDetails,.signature-widget-info .verificationDetails{cursor:default;padding:0;background-color:transparent;border:none;text-align:left}",
				"",
			]);
		},
		1622: function (e, t, n) {
			var r = n(30),
				i = n(1623);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[e.i, i, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const r = [];
								return (
									n.querySelectorAll(t).forEach((e) => r.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...e(t, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < t.length; r++) {
						const i = t[r];
						if (0 === r)
							i.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							i.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			r(i, a);
			e.exports = i.locals || {};
		},
		1623: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.SignaturePanel{z-index:65;transition:transform .3s ease,visibility 0s ease .3s}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{top:0;width:100%;height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{top:0;width:100%;height:100%}}.open.SignaturePanel{transform:none;visibility:visible;transition:transform .3s ease,visibility 0s ease 0s}.SignaturePanel .empty-panel-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;width:100%;padding:36px;grid-gap:8px;gap:8px}.SignaturePanel .empty-panel-container .empty-icon{width:60px;height:60px;color:var(--gray-6);fill:var(--gray-6)}.SignaturePanel .empty-panel-container .empty-icon svg{width:60px;height:60px}.SignaturePanel .empty-panel-container .empty-message{text-align:center;max-width:131px;font-size:13px}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .empty-panel-container .empty-message{line-height:15px}}.SignaturePanel{margin:0 8px 8px;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel{width:auto;margin:16px;flex-grow:1;overflow-y:auto}}.SignaturePanel .center{display:flex;justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .SignaturePanel .signature-widget-info .title .arrow{min-width:auto;padding:3px}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1640: function (e, t, n) {
			"use strict";
			n.r(t),
				n.d(t, "renderPermissionStatus", function () {
					return G;
				}),
				n.d(t, "SignaturePanel", function () {
					return ee;
				}),
				n.d(t, "Spinner", function () {
					return S;
				});
			n(23),
				n(8),
				n(24),
				n(10),
				n(145),
				n(9),
				n(11),
				n(29),
				n(54),
				n(1577),
				n(41),
				n(32),
				n(15),
				n(19),
				n(12),
				n(13),
				n(14),
				n(16),
				n(20),
				n(18),
				n(22),
				n(61),
				n(62),
				n(63),
				n(64),
				n(37),
				n(39),
				n(40),
				n(60);
			var r = n(0),
				i = n.n(r),
				a = n(6),
				o = n(347),
				s = n(2),
				c = n(1),
				u = n(4);
			n(58),
				n(46),
				n(52),
				n(294),
				n(357),
				n(358),
				n(359),
				n(360),
				n(361),
				n(362),
				n(363),
				n(364),
				n(365),
				n(366),
				n(367),
				n(368),
				n(369),
				n(370),
				n(371),
				n(372),
				n(373),
				n(374),
				n(375),
				n(376),
				n(377),
				n(378),
				n(379),
				n(380),
				n(588),
				n(1637),
				n(1638),
				n(84),
				n(36),
				n(101),
				n(109);
			function l(e) {
				return (l =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function f(e, t) {
				var n =
					("undefined" != typeof Symbol && e[Symbol.iterator]) ||
					e["@@iterator"];
				if (!n) {
					if (
						Array.isArray(e) ||
						(n = (function (e, t) {
							if (!e) return;
							if ("string" == typeof e) return d(e, t);
							var n = Object.prototype.toString.call(e).slice(8, -1);
							"Object" === n && e.constructor && (n = e.constructor.name);
							if ("Map" === n || "Set" === n) return Array.from(e);
							if (
								"Arguments" === n ||
								/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
							)
								return d(e, t);
						})(e)) ||
						(t && e && "number" == typeof e.length)
					) {
						n && (e = n);
						var r = 0,
							i = function () {};
						return {
							s: i,
							n: function () {
								return r >= e.length
									? { done: !0 }
									: { done: !1, value: e[r++] };
							},
							e: function (e) {
								throw e;
							},
							f: i,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var a,
					o = !0,
					s = !1;
				return {
					s: function () {
						n = n.call(e);
					},
					n: function () {
						var e = n.next();
						return (o = e.done), e;
					},
					e: function (e) {
						(s = !0), (a = e);
					},
					f: function () {
						try {
							o || null == n.return || n.return();
						} finally {
							if (s) throw a;
						}
					},
				};
			}
			function d(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			function p() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ p =
					function () {
						return e;
					};
				var e = {},
					t = Object.prototype,
					n = t.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (e, t, n) {
							e[t] = n.value;
						},
					i = "function" == typeof Symbol ? Symbol : {},
					a = i.iterator || "@@iterator",
					o = i.asyncIterator || "@@asyncIterator",
					s = i.toStringTag || "@@toStringTag";
				function c(e, t, n) {
					return (
						Object.defineProperty(e, t, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[t]
					);
				}
				try {
					c({}, "");
				} catch (e) {
					c = function (e, t, n) {
						return (e[t] = n);
					};
				}
				function u(e, t, n, i) {
					var a = t && t.prototype instanceof g ? t : g,
						o = Object.create(a.prototype),
						s = new L(i || []);
					return r(o, "_invoke", { value: E(e, n, s) }), o;
				}
				function f(e, t, n) {
					try {
						return { type: "normal", arg: e.call(t, n) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = u;
				var d = {};
				function g() {}
				function m() {}
				function h() {}
				var y = {};
				c(y, a, function () {
					return this;
				});
				var v = Object.getPrototypeOf,
					b = v && v(v(j([])));
				b && b !== t && n.call(b, a) && (y = b);
				var w = (h.prototype = g.prototype = Object.create(y));
				function x(e) {
					["next", "throw", "return"].forEach(function (t) {
						c(e, t, function (e) {
							return this._invoke(t, e);
						});
					});
				}
				function S(e, t) {
					var i;
					r(this, "_invoke", {
						value: function (r, a) {
							function o() {
								return new t(function (i, o) {
									!(function r(i, a, o, s) {
										var c = f(e[i], e, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == l(d) && n.call(d, "__await")
												? t.resolve(d.__await).then(
														function (e) {
															r("next", e, o, s);
														},
														function (e) {
															r("throw", e, o, s);
														},
													)
												: t.resolve(d).then(
														function (e) {
															(u.value = e), o(u);
														},
														function (e) {
															return r("throw", e, o, s);
														},
													);
										}
										s(c.arg);
									})(r, a, i, o);
								});
							}
							return (i = i ? i.then(o, o) : o());
						},
					});
				}
				function E(e, t, n) {
					var r = "suspendedStart";
					return function (i, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === i) throw a;
							return O();
						}
						for (n.method = i, n.arg = a; ; ) {
							var o = n.delegate;
							if (o) {
								var s = _(o, n);
								if (s) {
									if (s === d) continue;
									return s;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = f(e, t, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function _(e, t) {
					var n = t.method,
						r = e.iterator[n];
					if (void 0 === r)
						return (
							(t.delegate = null),
							("throw" === n &&
								e.iterator.return &&
								((t.method = "return"),
								(t.arg = void 0),
								_(e, t),
								"throw" === t.method)) ||
								("return" !== n &&
									((t.method = "throw"),
									(t.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var i = f(r, e.iterator, t.arg);
					if ("throw" === i.type)
						return (
							(t.method = "throw"), (t.arg = i.arg), (t.delegate = null), d
						);
					var a = i.arg;
					return a
						? a.done
							? ((t[e.resultName] = a.value),
								(t.next = e.nextLoc),
								"return" !== t.method &&
									((t.method = "next"), (t.arg = void 0)),
								(t.delegate = null),
								d)
							: a
						: ((t.method = "throw"),
							(t.arg = new TypeError("iterator result is not an object")),
							(t.delegate = null),
							d);
				}
				function k(e) {
					var t = { tryLoc: e[0] };
					1 in e && (t.catchLoc = e[1]),
						2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
						this.tryEntries.push(t);
				}
				function A(e) {
					var t = e.completion || {};
					(t.type = "normal"), delete t.arg, (e.completion = t);
				}
				function L(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(k, this),
						this.reset(!0);
				}
				function j(e) {
					if (e) {
						var t = e[a];
						if (t) return t.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var r = -1,
								i = function t() {
									for (; ++r < e.length; )
										if (n.call(e, r)) return (t.value = e[r]), (t.done = !1), t;
									return (t.value = void 0), (t.done = !0), t;
								};
							return (i.next = i);
						}
					}
					return { next: O };
				}
				function O() {
					return { value: void 0, done: !0 };
				}
				return (
					(m.prototype = h),
					r(w, "constructor", { value: h, configurable: !0 }),
					r(h, "constructor", { value: m, configurable: !0 }),
					(m.displayName = c(h, s, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var t = "function" == typeof e && e.constructor;
						return (
							!!t &&
							(t === m || "GeneratorFunction" === (t.displayName || t.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, h)
								: ((e.__proto__ = h), c(e, s, "GeneratorFunction")),
							(e.prototype = Object.create(w)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					x(S.prototype),
					c(S.prototype, o, function () {
						return this;
					}),
					(e.AsyncIterator = S),
					(e.async = function (t, n, r, i, a) {
						void 0 === a && (a = Promise);
						var o = new S(u(t, n, r, i), a);
						return e.isGeneratorFunction(n)
							? o
							: o.next().then(function (e) {
									return e.done ? e.value : o.next();
								});
					}),
					x(w),
					c(w, s, "Generator"),
					c(w, a, function () {
						return this;
					}),
					c(w, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var t = Object(e),
							n = [];
						for (var r in t) n.push(r);
						return (
							n.reverse(),
							function e() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in t) return (e.value = r), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = j),
					(L.prototype = {
						constructor: L,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(A),
								!e)
							)
								for (var t in this)
									"t" === t.charAt(0) &&
										n.call(this, t) &&
										!isNaN(+t.slice(1)) &&
										(this[t] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var t = this;
							function r(n, r) {
								return (
									(o.type = "throw"),
									(o.arg = e),
									(t.next = n),
									r && ((t.method = "next"), (t.arg = void 0)),
									!!r
								);
							}
							for (var i = this.tryEntries.length - 1; i >= 0; --i) {
								var a = this.tryEntries[i],
									o = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var s = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (s && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (s) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, t) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var i = this.tryEntries[r];
								if (
									i.tryLoc <= this.prev &&
									n.call(i, "finallyLoc") &&
									this.prev < i.finallyLoc
								) {
									var a = i;
									break;
								}
							}
							a &&
								("break" === e || "continue" === e) &&
								a.tryLoc <= t &&
								t <= a.finallyLoc &&
								(a = null);
							var o = a ? a.completion : {};
							return (
								(o.type = e),
								(o.arg = t),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(o)
							);
						},
						complete: function (e, t) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && t && (this.next = t),
								d
							);
						},
						finish: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.finallyLoc === e)
									return this.complete(n.completion, n.afterLoc), A(n), d;
							}
						},
						catch: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.tryLoc === e) {
									var r = n.completion;
									if ("throw" === r.type) {
										var i = r.arg;
										A(n);
									}
									return i;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, t, n) {
							return (
								(this.delegate = { iterator: j(e), resultName: t, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					e
				);
			}
			function g(e, t, n, r, i, a, o) {
				try {
					var s = e[a](o),
						c = s.value;
				} catch (e) {
					return void n(e);
				}
				s.done ? t(c) : Promise.resolve(c).then(r, i);
			}
			function m(e) {
				return function () {
					var t = this,
						n = arguments;
					return new Promise(function (r, i) {
						var a = e.apply(t, n);
						function o(e) {
							g(a, r, i, o, s, "next", e);
						}
						function s(e) {
							g(a, r, i, o, s, "throw", e);
						}
						o(void 0);
					});
				};
			}
			var h = (function () {
					var e = m(
						p().mark(function e(t, n, r, i, a, o, c) {
							var u;
							return p().wrap(function (e) {
								for (;;)
									switch ((e.prev = e.next)) {
										case 0:
											return (e.next = 2), y(t, n, r, i, a, o);
										case 2:
											return (
												(u = e.sent),
												c(s.a.setVerificationResult(u)),
												e.abrupt("return", u)
											);
										case 5:
										case "end":
											return e.stop();
									}
							}, e);
						}),
					);
					return function (t, n, r, i, a, o, s) {
						return e.apply(this, arguments);
					};
				})(),
				y = (function () {
					var e = m(
						p().mark(function e(t, n, r, i, a, o) {
							var s, c, u, l, d, g, h;
							return p().wrap(function (e) {
								for (;;)
									switch ((e.prev = e.next)) {
										case 0:
											return (
												(s = window.Core.PDFNet),
												(c = s.VerificationResult),
												(u = c.TrustStatus),
												(l = c.DigestStatus),
												(d = c.ModificationPermissionsStatus),
												(g = c.DocumentStatus),
												(h = {}),
												(e.next = 6),
												s.runWithCleanup(
													m(
														p().mark(function e() {
															var c,
																y,
																x,
																S,
																E,
																_,
																k,
																A,
																L,
																j,
																O,
																T,
																P,
																V,
																C,
																N,
																D,
																F,
																I,
																R,
																M,
																G,
																B,
																U,
																H,
																W,
																z,
																q,
																Y,
																$,
																J,
																K,
																Z,
																Q,
																X,
																ee,
																te,
																ne,
																re,
																ie,
																ae,
																oe,
																se,
																ce,
																ue,
																le,
																fe,
																de,
																pe,
																ge,
																me,
																he,
																ye,
																ve,
																be,
																we,
																xe;
															return p().wrap(
																function (e) {
																	for (;;)
																		switch ((e.prev = e.next)) {
																			case 0:
																				return (e.next = 2), t.getPDFDoc();
																			case 2:
																				return (
																					(t = e.sent),
																					(e.next = 5),
																					s.VerificationOptions.create(
																						s.VerificationOptions.SecurityLevel
																							.e_compatibility_and_archiving,
																					)
																				);
																			case 5:
																				if (((c = e.sent), !a)) {
																					e.next = 9;
																					break;
																				}
																				return (
																					(e.next = 9),
																					c.enableOnlineCRLRevocationChecking(
																						!0,
																					)
																				);
																			case 9:
																				if (null !== o) {
																					e.next = 14;
																					break;
																				}
																				return (
																					(e.next = 12),
																					c.setRevocationProxyPrefix(
																						"https://proxy.pdftron.com",
																					)
																				);
																			case 12:
																				e.next = 17;
																				break;
																			case 14:
																				if (void 0 === o) {
																					e.next = 17;
																					break;
																				}
																				return (
																					(e.next = 17),
																					c.setRevocationProxyPrefix(o)
																				);
																			case 17:
																				(y = f(n)),
																					(e.prev = 18),
																					(S = p().mark(function e() {
																						var t, n, r;
																						return p().wrap(
																							function (e) {
																								for (;;)
																									switch ((e.prev = e.next)) {
																										case 0:
																											if (
																												"string" !=
																												typeof (t = x.value)
																											) {
																												e.next = 13;
																												break;
																											}
																											return (
																												(e.prev = 2),
																												(e.next = 5),
																												c.addTrustedCertificateFromURL(
																													t,
																												)
																											);
																										case 5:
																											e.next = 11;
																											break;
																										case 7:
																											return (
																												(e.prev = 7),
																												(e.t0 = e.catch(2)),
																												console.error(
																													"Error encountered when trying to load certificate from URL: ".concat(
																														t,
																														"\n",
																													) +
																														"Certificate will not be used as part of verification process.",
																												),
																												e.abrupt(
																													"return",
																													"continue",
																												)
																											);
																										case 11:
																											e.next = 41;
																											break;
																										case 13:
																											if (
																												!(
																													t instanceof File ||
																													"[object File]" ===
																														Object.prototype.toString.call(
																															t,
																														)
																												)
																											) {
																												e.next = 31;
																												break;
																											}
																											return (
																												(n = new FileReader()),
																												(r = new Promise(
																													function (e, r) {
																														n.addEventListener(
																															"load",
																															(function () {
																																var t = m(
																																	p().mark(
																																		function t(
																																			n,
																																		) {
																																			return p().wrap(
																																				function (
																																					t,
																																				) {
																																					for (;;)
																																						switch (
																																							(t.prev =
																																								t.next)
																																						) {
																																							case 0:
																																								e(
																																									new Uint8Array(
																																										n
																																											.target
																																											.result,
																																									),
																																								);
																																							case 1:
																																							case "end":
																																								return t.stop();
																																						}
																																				},
																																				t,
																																			);
																																		},
																																	),
																																);
																																return function (
																																	e,
																																) {
																																	return t.apply(
																																		this,
																																		arguments,
																																	);
																																};
																															})(),
																														),
																															n.addEventListener(
																																"error",
																																function () {
																																	r(
																																		"Error reading the local certificate",
																																	);
																																},
																															),
																															n.readAsArrayBuffer(
																																t,
																															);
																													},
																												)),
																												(e.prev = 16),
																												(e.t1 = c),
																												(e.next = 20),
																												r
																											);
																										case 20:
																											return (
																												(e.t2 = e.sent),
																												(e.next = 23),
																												e.t1.addTrustedCertificate.call(
																													e.t1,
																													e.t2,
																												)
																											);
																										case 23:
																											e.next = 29;
																											break;
																										case 25:
																											return (
																												(e.prev = 25),
																												(e.t3 = e.catch(16)),
																												console.error(
																													"Error encountered when trying to load certificate: ".concat(
																														e.t3,
																													) +
																														"Certificate will not be used as part of the verification process.",
																												),
																												e.abrupt(
																													"return",
																													"continue",
																												)
																											);
																										case 29:
																											e.next = 41;
																											break;
																										case 31:
																											if (
																												!(
																													t instanceof
																														ArrayBuffer ||
																													t instanceof
																														Int8Array ||
																													t instanceof
																														Uint8Array ||
																													t instanceof
																														Uint8ClampedArray
																												)
																											) {
																												e.next = 41;
																												break;
																											}
																											return (
																												(e.prev = 32),
																												(e.next = 35),
																												c.addTrustedCertificate(
																													t,
																												)
																											);
																										case 35:
																											e.next = 41;
																											break;
																										case 37:
																											return (
																												(e.prev = 37),
																												(e.t4 = e.catch(32)),
																												console.error(
																													"Error encountered when trying to load certificate: ".concat(
																														e.t4,
																													) +
																														"Certificate will not be used as part of the verification process.",
																												),
																												e.abrupt(
																													"return",
																													"continue",
																												)
																											);
																										case 41:
																										case "end":
																											return e.stop();
																									}
																							},
																							e,
																							null,
																							[
																								[2, 7],
																								[16, 25],
																								[32, 37],
																							],
																						);
																					})),
																					y.s();
																			case 21:
																				if ((x = y.n()).done) {
																					e.next = 28;
																					break;
																				}
																				return e.delegateYield(S(), "t0", 23);
																			case 23:
																				if ("continue" !== e.t0) {
																					e.next = 26;
																					break;
																				}
																				return e.abrupt("continue", 26);
																			case 26:
																				e.next = 21;
																				break;
																			case 28:
																				e.next = 33;
																				break;
																			case 30:
																				(e.prev = 30),
																					(e.t1 = e.catch(18)),
																					y.e(e.t1);
																			case 33:
																				return (
																					(e.prev = 33), y.f(), e.finish(33)
																				);
																			case 36:
																				(E = f(r)), (e.prev = 37), E.s();
																			case 39:
																				if ((_ = E.n()).done) {
																					e.next = 70;
																					break;
																				}
																				if (
																					((k = _.value),
																					(A = k.constructor.name),
																					(L = [
																						"ArrayBuffer",
																						"Int8Array",
																						"Uint8Array",
																						"Uint8ClampedArray",
																					]),
																					(j = void 0),
																					"Blob" !== A)
																				) {
																					e.next = 50;
																					break;
																				}
																				return (e.next = 47), k.arrayBuffer();
																			case 47:
																				(j = e.sent), (e.next = 56);
																				break;
																			case 50:
																				if (!L.includes(A)) {
																					e.next = 54;
																					break;
																				}
																				(j = k), (e.next = 56);
																				break;
																			case 54:
																				return (
																					console.error(
																						"The provided TrustList is an unsupported data-structure. Please ensure the TrustList is formatted as one of the following " +
																							"data-structures: ".concat(
																								[]
																									.concat(L, ["Blob"])
																									.join("|"),
																							),
																					),
																					e.abrupt("continue", 68)
																				);
																			case 56:
																				return (
																					(e.prev = 56),
																					(e.next = 59),
																					s.FDFDoc.createFromMemoryBuffer(j)
																				);
																			case 59:
																				return (
																					(O = e.sent),
																					(e.next = 62),
																					c.loadTrustList(O)
																				);
																			case 62:
																				e.next = 68;
																				break;
																			case 64:
																				return (
																					(e.prev = 64),
																					(e.t2 = e.catch(56)),
																					console.error(
																						"Error encountered when trying to load certificate: ".concat(
																							e.t2,
																							". ",
																						) +
																							"Certificate will not be used as part of the verification process.",
																					),
																					e.abrupt("continue", 68)
																				);
																			case 68:
																				e.next = 39;
																				break;
																			case 70:
																				e.next = 75;
																				break;
																			case 72:
																				(e.prev = 72),
																					(e.t3 = e.catch(37)),
																					E.e(e.t3);
																			case 75:
																				return (
																					(e.prev = 75), E.f(), e.finish(75)
																				);
																			case 78:
																				return (
																					(e.next = 80),
																					t.getFieldIteratorBegin()
																				);
																			case 80:
																				T = e.sent;
																			case 81:
																				return (e.next = 83), T.hasNext();
																			case 83:
																				if (!e.sent) {
																					e.next = 275;
																					break;
																				}
																				return (e.next = 86), T.current();
																			case 86:
																				return (
																					(P = e.sent),
																					(e.next = 89),
																					P.isValid()
																				);
																			case 89:
																				if (((e.t4 = !e.sent), e.t4)) {
																					e.next = 96;
																					break;
																				}
																				return (e.next = 93), P.getType();
																			case 93:
																				(e.t5 = e.sent),
																					(e.t6 = s.Field.Type.e_signature),
																					(e.t4 = e.t5 !== e.t6);
																			case 96:
																				if (!e.t4) {
																					e.next = 98;
																					break;
																				}
																				return e.abrupt("continue", 272);
																			case 98:
																				return (
																					(e.next = 100),
																					s.DigitalSignatureField.createFromField(
																						P,
																					)
																				);
																			case 100:
																				return (
																					(V = e.sent),
																					(e.prev = 101),
																					(e.next = 104),
																					V.verify(c)
																				);
																			case 104:
																				return (
																					(C = e.sent),
																					(e.next = 107),
																					V.getSDFObj()
																				);
																			case 107:
																				return (
																					(e.next = 109), e.sent.getObjNum()
																				);
																			case 109:
																				return (
																					(N = e.sent),
																					(D = void 0),
																					(F = void 0),
																					(I = void 0),
																					(R = void 0),
																					(M = void 0),
																					(G = void 0),
																					(B = void 0),
																					(U = void 0),
																					(H = void 0),
																					(W = {}),
																					(z = {}),
																					(e.next = 123),
																					V.hasCryptographicSignature()
																				);
																			case 123:
																				if (!(q = e.sent)) {
																					e.next = 177;
																					break;
																				}
																				return (e.next = 127), V.getSubFilter();
																			case 127:
																				if (
																					(Y = e.sent) !==
																					s.DigitalSignatureField.SubFilterType
																						.e_adbe_pkcs7_detached
																				) {
																					e.next = 142;
																					break;
																				}
																				return (
																					(e.next = 131),
																					V.getSignerCertFromCMS()
																				);
																			case 131:
																				return (
																					($ = e.sent),
																					(e.next = 134),
																					$.getSubjectField()
																				);
																			case 134:
																				return (
																					(J = e.sent), (e.next = 137), w(J)
																				);
																			case 137:
																				if (((e.t7 = e.sent), e.t7)) {
																					e.next = 140;
																					break;
																				}
																				e.t7 = {};
																			case 140:
																				(K = e.t7), (D = K.e_commonName);
																			case 142:
																				if (
																					Y ===
																					s.DigitalSignatureField.SubFilterType
																						.e_ETSI_RFC3161
																				) {
																					e.next = 171;
																					break;
																				}
																				if (D) {
																					e.next = 152;
																					break;
																				}
																				return (
																					(e.next = 146), V.getSignatureName()
																				);
																			case 146:
																				if (((e.t8 = e.sent), e.t8)) {
																					e.next = 151;
																					break;
																				}
																				return (
																					(e.next = 150), V.getContactInfo()
																				);
																			case 150:
																				e.t8 = e.sent;
																			case 151:
																				D = e.t8;
																			case 152:
																				return (
																					(e.next = 154), V.getSigningTime()
																				);
																			case 154:
																				return (
																					(F = e.sent),
																					(e.next = 157),
																					F.isValid()
																				);
																			case 157:
																				if (!e.sent) {
																					e.next = 161;
																					break;
																				}
																				(F = v(F, i)), (e.next = 162);
																				break;
																			case 161:
																				F = null;
																			case 162:
																				return (
																					(e.next = 164), V.getContactInfo()
																				);
																			case 164:
																				return (
																					(M = e.sent),
																					(e.next = 167),
																					V.getLocation()
																				);
																			case 167:
																				return (
																					(G = e.sent),
																					(e.next = 170),
																					V.getReason()
																				);
																			case 170:
																				B = e.sent;
																			case 171:
																				return (
																					(e.next = 173),
																					V.getDocumentPermissions()
																				);
																			case 173:
																				return (
																					(I = e.sent),
																					(e.next = 176),
																					V.isCertification()
																				);
																			case 176:
																				R = e.sent;
																			case 177:
																				return (
																					(e.next = 179),
																					C.getVerificationStatus()
																				);
																			case 179:
																				return (
																					(Z = e.sent),
																					(e.next = 182),
																					C.getDocumentStatus()
																				);
																			case 182:
																				return (
																					(Q = e.sent),
																					(e.next = 185),
																					C.getDigestStatus()
																				);
																			case 185:
																				return (
																					(X = e.sent),
																					(e.next = 188),
																					C.getTrustStatus()
																				);
																			case 188:
																				return (
																					(ee = e.sent),
																					(e.next = 191),
																					C.getPermissionsStatus()
																				);
																			case 191:
																				return (
																					(te = e.sent),
																					(e.next = 194),
																					C.getDigestAlgorithm()
																				);
																			case 194:
																				return (
																					(ne = e.sent),
																					(e.t9 = Promise),
																					(e.next = 198),
																					C.getDisallowedChanges()
																				);
																			case 198:
																				return (
																					(e.t10 = e.sent.map(
																						(function () {
																							var e = m(
																								p().mark(function e(t) {
																									return p().wrap(function (e) {
																										for (;;)
																											switch (
																												(e.prev = e.next)
																											) {
																												case 0:
																													return (
																														(e.next = 2),
																														t.getObjNum()
																													);
																												case 2:
																													return (
																														(e.t0 = e.sent),
																														(e.next = 5),
																														t.getTypeAsString()
																													);
																												case 5:
																													return (
																														(e.t1 = e.sent),
																														e.abrupt("return", {
																															objnum: e.t0,
																															type: e.t1,
																														})
																													);
																												case 7:
																												case "end":
																													return e.stop();
																											}
																									}, e);
																								}),
																							);
																							return function (t) {
																								return e.apply(this, arguments);
																							};
																						})(),
																					)),
																					(e.next = 201),
																					e.t9.all.call(e.t9, e.t10)
																				);
																			case 201:
																				return (
																					(re = e.sent),
																					(ie = ee === u.e_trust_verified),
																					(ae = void 0),
																					(oe = void 0),
																					(se = void 0),
																					(ce = void 0),
																					(e.next = 209),
																					C.hasTrustVerificationResult()
																				);
																			case 209:
																				if (!e.sent) {
																					e.next = 260;
																					break;
																				}
																				return (
																					(e.next = 213),
																					C.getTrustVerificationResult()
																				);
																			case 213:
																				return (
																					(ue = e.sent),
																					(e.next = 216),
																					ue.wasSuccessful()
																				);
																			case 216:
																				return (
																					(oe = e.sent),
																					(e.next = 219),
																					ue.getResultString()
																				);
																			case 219:
																				return (
																					(ae = e.sent),
																					(e.next = 222),
																					ue.getTimeOfTrustVerificationEnum()
																				);
																			case 222:
																				return (
																					(se = e.sent),
																					(e.next = 225),
																					ue.getTimeOfTrustVerification()
																				);
																			case 225:
																				return (
																					(le = e.sent) && (ce = b(le, i)),
																					(e.next = 229),
																					ue.getCertPath()
																				);
																			case 229:
																				if (!(fe = e.sent).length) {
																					e.next = 260;
																					break;
																				}
																				return (
																					(de = fe[0]),
																					(e.next = 234),
																					de.getIssuerField()
																				);
																			case 234:
																				return (
																					(pe = e.sent), (e.next = 237), w(pe)
																				);
																			case 237:
																				return (
																					(ge = e.sent),
																					Object.assign(W, ge),
																					(e.next = 241),
																					de.getSubjectField()
																				);
																			case 241:
																				return (
																					(me = e.sent), (e.next = 244), w(me)
																				);
																			case 244:
																				return (
																					(he = e.sent),
																					Object.assign(z, he),
																					(ye = fe[fe.length - 1]),
																					(e.prev = 247),
																					(e.next = 250),
																					ye.getNotBeforeEpochTime()
																				);
																			case 250:
																				return (
																					(ve = e.sent),
																					(e.next = 253),
																					ye.getNotAfterEpochTime()
																				);
																			case 253:
																				(be = e.sent),
																					(U = be >= le && le >= ve),
																					(e.next = 260);
																				break;
																			case 257:
																				(e.prev = 257),
																					(e.t11 = e.catch(247)),
																					e.t11.includes(
																						"calendar_point::to_std_timepoint() does not support years after",
																					) &&
																						(console.warn(
																							"The following error is a known issue with Botan, and aims to be addressed in a future release of PDFNet. This currently does not impact PDFTron's Digital Signature Verification capabilities.",
																						),
																						console.warn(e.t11));
																			case 260:
																				return (
																					(we = void 0),
																					(we = Z
																						? "digital_signature_valid"
																						: Q !== g.e_no_error ||
																								(X !== l.e_digest_verified &&
																									X !==
																										l.e_digest_verification_disabled) ||
																								ee === u.e_no_trust_status ||
																								(te !== d.e_unmodified &&
																									te !==
																										d.e_has_allowed_changes &&
																									te !==
																										d.e_permissions_verification_disabled)
																							? "digital_signature_error"
																							: "digital_signature_warning"),
																					D
																						? (H = D)
																						: !D &&
																							z.e_commonName &&
																							(H = z.e_commonName),
																					(e.next = 265),
																					P.getName()
																				);
																			case 265:
																				(xe = e.sent),
																					(h[xe] = {
																						signed: q,
																						signer: D,
																						signerName: H,
																						signTime: F,
																						verificationStatus: Z,
																						documentStatus: Q,
																						digestStatus: X,
																						trustStatus: ee,
																						permissionStatus: te,
																						disallowedChanges: re,
																						trustVerificationResultBoolean: oe,
																						trustVerificationResultString: ae,
																						timeOfTrustVerificationEnum: se,
																						trustVerificationTime: ce,
																						id: N,
																						badgeIcon: we,
																						validSignerIdentity: ie,
																						digestAlgorithm: ne,
																						documentPermission: I,
																						isCertification: R,
																						contactInfo: M,
																						location: G,
																						reason: B,
																						issuerField: W,
																						subjectField: z,
																						validAtTimeOfSigning: U,
																					}),
																					(e.next = 272);
																				break;
																			case 269:
																				(e.prev = 269),
																					(e.t12 = e.catch(101)),
																					console.error(e.t12);
																			case 272:
																				T.next(), (e.next = 81);
																				break;
																			case 275:
																			case "end":
																				return e.stop();
																		}
																},
																e,
																null,
																[
																	[18, 30, 33, 36],
																	[37, 72, 75, 78],
																	[56, 64],
																	[101, 269],
																	[247, 257],
																],
															);
														}),
													),
												)
											);
										case 6:
											return e.abrupt("return", h);
										case 7:
										case "end":
											return e.stop();
									}
							}, e);
						}),
					);
					return function (t, n, r, i, a, o) {
						return e.apply(this, arguments);
					};
				})(),
				v = function (e, t) {
					var n = e.year,
						r = e.month,
						i = e.day,
						a = e.hour,
						o = e.minute,
						s = e.second;
					return new Date(Date.UTC(n, r - 1, i, a, o, s)).toLocaleDateString(
						t.replace("_", "-"),
						{
							year: "numeric",
							month: "long",
							weekday: "long",
							day: "numeric",
							hour: "numeric",
							minute: "numeric",
							timeZoneName: "short",
						},
					);
				},
				b = function (e, t) {
					var n = new Date(0);
					return (
						n.setUTCSeconds(e),
						n.toLocaleDateString(t.replace("_", "-"), {
							year: "numeric",
							month: "long",
							weekday: "long",
							day: "numeric",
							hour: "numeric",
							minute: "numeric",
							timeZoneName: "short",
						})
					);
				},
				w = (function () {
					var e = m(
						p().mark(function e(t) {
							var n, r, i, a, o, s, c, u;
							return p().wrap(
								function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												return (
													(n = {}), (e.next = 3), t.getAllAttributesAndValues()
												);
											case 3:
												(r = e.sent), (i = f(r)), (e.prev = 5), i.s();
											case 7:
												if ((a = i.n()).done) {
													e.next = 21;
													break;
												}
												return (
													(o = a.value), (e.next = 11), o.getAttributeTypeOID()
												);
											case 11:
												return (s = e.sent), (e.next = 14), s.getRawValue();
											case 14:
												return (c = e.sent), (e.next = 17), o.getStringValue();
											case 17:
												(u = e.sent), (n[x(c)] = u);
											case 19:
												e.next = 7;
												break;
											case 21:
												e.next = 26;
												break;
											case 23:
												(e.prev = 23), (e.t0 = e.catch(5)), i.e(e.t0);
											case 26:
												return (e.prev = 26), i.f(), e.finish(26);
											case 29:
												return e.abrupt("return", n);
											case 30:
											case "end":
												return e.stop();
										}
								},
								e,
								null,
								[[5, 23, 26, 29]],
							);
						}),
					);
					return function (t) {
						return e.apply(this, arguments);
					};
				})(),
				x = function (e) {
					return {
						"[2,5,4,3]": "e_commonName",
						"[2,5,4,4]": "e_surname",
						"[2,5,4,6]": "e_countryName",
						"[2,5,4,7]": "e_localityName",
						"[2,5,4,8]": "e_stateOrProvinceName",
						"[2,5,4,9]": "e_streetAddress",
						"[2,5,4,10]": "e_organizationName",
						"[2,5,4,11]": "e_organizationalUnitName",
						"[1,2,840,113549,1,9,1]": "e_emailAddress",
					}["string" == typeof e ? e : JSON.stringify(e)];
				},
				S =
					(n(1618),
					function () {
						return i.a.createElement("div", { className: "spinner" });
					}),
				E = (n(27), n(28), n(25), n(3)),
				_ = n.n(E),
				k = n(17),
				A = n.n(k),
				L = (n(90), n(119)),
				j = n.n(L),
				O = n(21);
			function T(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								i,
								a,
								o,
								s = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(s.push(r.value), s.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (i = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((o = n.return()), Object(o) !== o)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return s;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return P(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return P(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function P(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var V = function (e) {
					var t = e.rect,
						n = T(Object(r.useState)(!1), 2),
						a = n[0],
						o = n[1];
					return (
						Object(r.useEffect)(function () {
							var e = c.a.getScrollViewElement(),
								t = function () {
									o(!1);
								};
							return (
								e.addEventListener("scroll", t),
								function () {
									return e.removeEventListener("scroll", t);
								}
							);
						}),
						Object(r.useEffect)(
							function () {
								t &&
									(setTimeout(function () {
										o(!0);
									}, 50),
									setTimeout(function () {
										o(!1);
									}, 700));
							},
							[t],
						),
						a &&
							j.a.createPortal(
								i.a.createElement("div", {
									style: {
										position: "absolute",
										top: t.y1,
										left: t.x1,
										width: t.x2 - t.x1,
										height: t.y2 - t.y1,
										border: "1px solid #00a5e4",
										zIndex: 99,
									},
								}),
								Object(O.a)().querySelector("#app"),
							)
					);
				},
				C = n(95),
				N = n(1538),
				D = n(42);
			n(1620);
			function F(e) {
				return (F =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function I(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								i,
								a,
								o,
								s = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(s.push(r.value), s.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (i = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((o = n.return()), Object(o) !== o)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return s;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return R(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return R(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function R(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			function M(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== F(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var r = n.call(e, t || "default");
								if ("object" !== F(r)) return r;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === F(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			var G = function (e) {
					var t,
						n = e.isCertification,
						r = e.ModificationPermissionsStatus,
						a = e.permissionStatus,
						o = e.translate;
					if (e.digestStatus === e.DigestStatusErrorCodes.e_digest_invalid)
						return i.a.createElement(
							"p",
							null,
							o(
								"digitalSignatureVerification.digestStatus.documentHasBeenAltered",
							),
						);
					var s =
						(M(
							(t = {}),
							r.e_invalidated_by_disallowed_changes,
							"digitalSignatureVerification.permissionStatus.invalidatedByDisallowedChanges",
						),
						M(
							t,
							r.e_has_allowed_changes,
							"digitalSignatureVerification.permissionStatus.hasAllowedChanges",
						),
						M(
							t,
							r.e_unmodified,
							n
								? ""
										.concat(
											o(
												"digitalSignatureVerification.permissionStatus.unmodified",
											),
											" ",
										)
										.concat(o("digitalSignatureVerification.certified"), ".")
								: ""
										.concat(
											o(
												"digitalSignatureVerification.permissionStatus.unmodified",
											),
											" ",
										)
										.concat(o("digitalSignatureVerification.signed"), "."),
						),
						M(
							t,
							r.e_permissions_verification_disabled,
							"digitalSignatureVerification.permissionStatus.permissionsVerificationDisabled",
						),
						M(
							t,
							r.e_no_permissions_status,
							"digitalSignatureVerification.permissionStatus.noPermissionsStatus",
						),
						M(
							t,
							r.e_unsupported_permissions_features,
							"digitalSignatureVerification.permissionStatus.unsupportedPermissionsFeatures",
						),
						t);
					return i.a.createElement("p", null, o(s[a] || ""));
				},
				B = {
					name: _.a.string.isRequired,
					onClick: _.a.func,
					field: _.a.instanceOf(window.Core.Annotations.Forms.Field),
				},
				U = function (e) {
					var t,
						n,
						l = e.name,
						f = e.field,
						d = Object(a.e)(function (e) {
							return u.a.getVerificationResult(e, l);
						}),
						p = I(Object(r.useState)(null), 2),
						g = p[0],
						m = p[1],
						h = I(Object(r.useState)(!1), 2),
						y = h[0],
						v = h[1],
						b = window.Core.PDFNet,
						w = b.VerificationResult,
						x = b.VerificationOptions.TimeMode,
						S = w.ModificationPermissionsStatus,
						E = w.DigestStatus,
						_ = I(Object(o.a)(), 1)[0],
						k = d.signed,
						L = d.signTime,
						j = d.verificationStatus,
						O = d.permissionStatus,
						T = d.disallowedChanges,
						P = d.trustVerificationResultBoolean,
						F = d.timeOfTrustVerificationEnum,
						R = d.trustVerificationTime,
						M = d.badgeIcon,
						B = d.isCertification,
						U = d.contactInfo,
						H = d.location,
						W = d.reason,
						z = d.signerName,
						q = d.digestStatus,
						Y = Object(a.d)(),
						$ = function () {
							!(function (e) {
								if (e.widgets.length) {
									var t = e.widgets[0];
									c.a.jumpToAnnotation(t);
									var n = c.a.getScrollViewElement(),
										r = n.scrollLeft,
										i = n.scrollTop,
										a = t.getRect(),
										o = c.a
											.getDisplayModeObject()
											.pageToWindow({ x: a.x1, y: a.y1 }, t.PageNumber),
										s = c.a
											.getDisplayModeObject()
											.pageToWindow({ x: a.x2, y: a.y2 }, t.PageNumber);
									m({ x1: o.x - r, y1: o.y - i, x2: s.x - r, y2: s.y - i });
								}
							})(f);
						},
						J = Object(C.a)(function () {
							Y(s.a.setSignatureValidationModalWidgetName(l)),
								Y(s.a.openElement("signatureValidationModal"));
						});
					return i.a.createElement(
						"div",
						{ className: "signature-widget-info" },
						k
							? i.a.createElement(
									i.a.Fragment,
									null,
									i.a.createElement(
										N.a,
										{
											labelHeader:
												((n = _(
													B
														? "digitalSignatureVerification.Certified"
														: "digitalSignatureVerification.Signed",
												)),
												(n += " "
													.concat(_("digitalSignatureVerification.by"), " ")
													.concat(z || _("digitalSignatureModal.unknown"))),
												L &&
													(n += " "
														.concat(_("digitalSignatureVerification.on"), " ")
														.concat(L)),
												n),
											iconGlyph: M,
											useI18String: !1,
											onClick: $,
											onKeyDown: $,
										},
										i.a.createElement(
											"div",
											{ className: "verificationDetails", tabIndex: -1 },
											i.a.createElement(
												"div",
												{ className: "header" },
												((t = _(
													B
														? "digitalSignatureVerification.Certification"
														: "digitalSignatureVerification.Signature",
												)),
												i.a.createElement(
													"div",
													{ className: "title" },
													i.a.createElement(
														"p",
														null,
														_(
															j
																? "digitalSignatureVerification.verificationStatus.valid"
																: "digitalSignatureVerification.verificationStatus.failed",
															{ verificationType: t },
														),
													),
												)),
												i.a.createElement(
													"ul",
													{ className: "body" },
													i.a.createElement(
														"li",
														null,
														G({
															isCertification: B,
															ModificationPermissionsStatus: S,
															permissionStatus: O,
															translate: _,
															digestStatus: q,
															DigestStatusErrorCodes: E,
														}),
													),
													T.map(function (e) {
														var t = e.objnum,
															n = e.type;
														return i.a.createElement(
															"li",
															{ key: t },
															i.a.createElement(
																"p",
																null,
																_(
																	"digitalSignatureVerification.disallowedChange",
																	{ type: n, objnum: t },
																),
															),
														);
													}),
													(function () {
														var e;
														switch (F) {
															case x.e_current:
																e = _(
																	"digitalSignatureVerification.trustVerification.current",
																);
																break;
															case x.e_signing:
																e = _(
																	"digitalSignatureVerification.trustVerification.signing",
																);
																break;
															case x.e_timestamp:
																e = _(
																	"digitalSignatureVerification.trustVerification.timestamp",
																);
																break;
															default:
																console.warn(
																	"Unexpected pdftron::PDF::VerificationOptions::TimeMode: ".concat(
																		F,
																	),
																);
														}
														return i.a.createElement(
															i.a.Fragment,
															null,
															i.a.createElement(
																"li",
																null,
																i.a.createElement(
																	"p",
																	null,
																	_(
																		P
																			? "digitalSignatureVerification.trustVerification.verifiedTrust"
																			: "digitalSignatureVerification.trustVerification.noTrustVerification",
																	),
																),
															),
															i.a.createElement(
																"li",
																null,
																i.a.createElement("p", null, R),
															),
															i.a.createElement(
																"li",
																null,
																i.a.createElement("p", null, e),
															),
														);
													})(),
													i.a.createElement(
														"li",
														null,
														i.a.createElement(
															"button",
															{
																"data-element": "signatureProperties-".concat(
																	l,
																),
																onClick: J,
																tabIndex: 0,
																className: "signatureProperties link",
																"aria-label": "Open signature properties modal",
															},
															i.a.createElement(
																"p",
																{ className: "bold underline" },
																_(
																	"digitalSignatureVerification.signatureProperties",
																),
															),
														),
													),
												),
											),
										),
										i.a.createElement(
											"div",
											{ className: "header header-with-arrow" },
											U || H || W
												? i.a.createElement(
														"div",
														{ className: "signatureDetails", tabIndex: -1 },
														i.a.createElement(
															"div",
															{ className: "title collapsible" },
															i.a.createElement(D.a, {
																img: "icon-chevron-right",
																className: A()({ arrow: !0, expanded: y }),
																ariaExpanded: y,
																isActive: y,
																ariaLabel: _(
																	"digitalSignatureVerification.signatureDetails.signatureDetails",
																),
																onClick: function () {
																	return v(!y);
																},
															}),
															i.a.createElement(
																"p",
																null,
																_(
																	"digitalSignatureVerification.signatureDetails.signatureDetails",
																),
															),
														),
														y &&
															i.a.createElement(
																"ul",
																{ className: "body" },
																i.a.createElement(
																	"li",
																	null,
																	i.a.createElement(
																		"p",
																		{ className: "bold" },
																		"".concat(
																			_(
																				"digitalSignatureVerification.signatureDetails.contactInformation",
																			),
																			":",
																		),
																	),
																	i.a.createElement(
																		"p",
																		{ className: "result-for-header" },
																		U ||
																			_(
																				"digitalSignatureVerification.signatureDetails.noContactInformation",
																			),
																	),
																),
																i.a.createElement(
																	"li",
																	null,
																	i.a.createElement(
																		"p",
																		{ className: "bold" },
																		"".concat(
																			_(
																				"digitalSignatureVerification.signatureDetails.location",
																			),
																			":",
																		),
																	),
																	i.a.createElement(
																		"p",
																		{ className: "result-for-header" },
																		H ||
																			_(
																				"digitalSignatureVerification.signatureDetails.noLocation",
																			),
																	),
																),
																i.a.createElement(
																	"li",
																	null,
																	i.a.createElement(
																		"p",
																		{ className: "bold" },
																		"".concat(
																			_(
																				"digitalSignatureVerification.signatureDetails.reason",
																			),
																			":",
																		),
																	),
																	i.a.createElement(
																		"p",
																		{ className: "result-for-header" },
																		W ||
																			_(
																				"digitalSignatureVerification.signatureDetails.noReason",
																			),
																	),
																),
																L &&
																	i.a.createElement(
																		"li",
																		null,
																		i.a.createElement(
																			"p",
																			{ className: "bold" },
																			"".concat(
																				_(
																					"digitalSignatureVerification.signatureDetails.signingTime",
																				),
																				":",
																			),
																		),
																		i.a.createElement(
																			"p",
																			{ className: "result-for-header" },
																			L ||
																				_(
																					"digitalSignatureVerification.signatureDetails.noSigningTime",
																				),
																		),
																	),
															),
													)
												: null,
										),
									),
								)
							: i.a.createElement(N.a, {
									labelHeader: _(
										"digitalSignatureVerification.unsignedSignatureField",
										{ fieldName: f.name },
									),
									iconGlyph: "digital_signature_empty",
									useI18String: !1,
									onClick: $,
									onKeyDown: $,
								}),
						i.a.createElement(V, { rect: g }),
					);
				};
			U.propTypes = B;
			var H = U,
				W = (n(1622), n(43)),
				z = n(34);
			function q(e) {
				return (q =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function Y(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return X(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					Q(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function $() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ $ =
					function () {
						return e;
					};
				var e = {},
					t = Object.prototype,
					n = t.hasOwnProperty,
					r =
						Object.defineProperty ||
						function (e, t, n) {
							e[t] = n.value;
						},
					i = "function" == typeof Symbol ? Symbol : {},
					a = i.iterator || "@@iterator",
					o = i.asyncIterator || "@@asyncIterator",
					s = i.toStringTag || "@@toStringTag";
				function c(e, t, n) {
					return (
						Object.defineProperty(e, t, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[t]
					);
				}
				try {
					c({}, "");
				} catch (e) {
					c = function (e, t, n) {
						return (e[t] = n);
					};
				}
				function u(e, t, n, i) {
					var a = t && t.prototype instanceof d ? t : d,
						o = Object.create(a.prototype),
						s = new k(i || []);
					return r(o, "_invoke", { value: x(e, n, s) }), o;
				}
				function l(e, t, n) {
					try {
						return { type: "normal", arg: e.call(t, n) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = u;
				var f = {};
				function d() {}
				function p() {}
				function g() {}
				var m = {};
				c(m, a, function () {
					return this;
				});
				var h = Object.getPrototypeOf,
					y = h && h(h(A([])));
				y && y !== t && n.call(y, a) && (m = y);
				var v = (g.prototype = d.prototype = Object.create(m));
				function b(e) {
					["next", "throw", "return"].forEach(function (t) {
						c(e, t, function (e) {
							return this._invoke(t, e);
						});
					});
				}
				function w(e, t) {
					var i;
					r(this, "_invoke", {
						value: function (r, a) {
							function o() {
								return new t(function (i, o) {
									!(function r(i, a, o, s) {
										var c = l(e[i], e, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												f = u.value;
											return f && "object" == q(f) && n.call(f, "__await")
												? t.resolve(f.__await).then(
														function (e) {
															r("next", e, o, s);
														},
														function (e) {
															r("throw", e, o, s);
														},
													)
												: t.resolve(f).then(
														function (e) {
															(u.value = e), o(u);
														},
														function (e) {
															return r("throw", e, o, s);
														},
													);
										}
										s(c.arg);
									})(r, a, i, o);
								});
							}
							return (i = i ? i.then(o, o) : o());
						},
					});
				}
				function x(e, t, n) {
					var r = "suspendedStart";
					return function (i, a) {
						if ("executing" === r)
							throw new Error("Generator is already running");
						if ("completed" === r) {
							if ("throw" === i) throw a;
							return L();
						}
						for (n.method = i, n.arg = a; ; ) {
							var o = n.delegate;
							if (o) {
								var s = S(o, n);
								if (s) {
									if (s === f) continue;
									return s;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === r) throw ((r = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							r = "executing";
							var c = l(e, t, n);
							if ("normal" === c.type) {
								if (
									((r = n.done ? "completed" : "suspendedYield"), c.arg === f)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((r = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function S(e, t) {
					var n = t.method,
						r = e.iterator[n];
					if (void 0 === r)
						return (
							(t.delegate = null),
							("throw" === n &&
								e.iterator.return &&
								((t.method = "return"),
								(t.arg = void 0),
								S(e, t),
								"throw" === t.method)) ||
								("return" !== n &&
									((t.method = "throw"),
									(t.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							f
						);
					var i = l(r, e.iterator, t.arg);
					if ("throw" === i.type)
						return (
							(t.method = "throw"), (t.arg = i.arg), (t.delegate = null), f
						);
					var a = i.arg;
					return a
						? a.done
							? ((t[e.resultName] = a.value),
								(t.next = e.nextLoc),
								"return" !== t.method &&
									((t.method = "next"), (t.arg = void 0)),
								(t.delegate = null),
								f)
							: a
						: ((t.method = "throw"),
							(t.arg = new TypeError("iterator result is not an object")),
							(t.delegate = null),
							f);
				}
				function E(e) {
					var t = { tryLoc: e[0] };
					1 in e && (t.catchLoc = e[1]),
						2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
						this.tryEntries.push(t);
				}
				function _(e) {
					var t = e.completion || {};
					(t.type = "normal"), delete t.arg, (e.completion = t);
				}
				function k(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(E, this),
						this.reset(!0);
				}
				function A(e) {
					if (e) {
						var t = e[a];
						if (t) return t.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var r = -1,
								i = function t() {
									for (; ++r < e.length; )
										if (n.call(e, r)) return (t.value = e[r]), (t.done = !1), t;
									return (t.value = void 0), (t.done = !0), t;
								};
							return (i.next = i);
						}
					}
					return { next: L };
				}
				function L() {
					return { value: void 0, done: !0 };
				}
				return (
					(p.prototype = g),
					r(v, "constructor", { value: g, configurable: !0 }),
					r(g, "constructor", { value: p, configurable: !0 }),
					(p.displayName = c(g, s, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var t = "function" == typeof e && e.constructor;
						return (
							!!t &&
							(t === p || "GeneratorFunction" === (t.displayName || t.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, g)
								: ((e.__proto__ = g), c(e, s, "GeneratorFunction")),
							(e.prototype = Object.create(v)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					b(w.prototype),
					c(w.prototype, o, function () {
						return this;
					}),
					(e.AsyncIterator = w),
					(e.async = function (t, n, r, i, a) {
						void 0 === a && (a = Promise);
						var o = new w(u(t, n, r, i), a);
						return e.isGeneratorFunction(n)
							? o
							: o.next().then(function (e) {
									return e.done ? e.value : o.next();
								});
					}),
					b(v),
					c(v, s, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var t = Object(e),
							n = [];
						for (var r in t) n.push(r);
						return (
							n.reverse(),
							function e() {
								for (; n.length; ) {
									var r = n.pop();
									if (r in t) return (e.value = r), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = A),
					(k.prototype = {
						constructor: k,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(_),
								!e)
							)
								for (var t in this)
									"t" === t.charAt(0) &&
										n.call(this, t) &&
										!isNaN(+t.slice(1)) &&
										(this[t] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var t = this;
							function r(n, r) {
								return (
									(o.type = "throw"),
									(o.arg = e),
									(t.next = n),
									r && ((t.method = "next"), (t.arg = void 0)),
									!!r
								);
							}
							for (var i = this.tryEntries.length - 1; i >= 0; --i) {
								var a = this.tryEntries[i],
									o = a.completion;
								if ("root" === a.tryLoc) return r("end");
								if (a.tryLoc <= this.prev) {
									var s = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (s && c) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									} else if (s) {
										if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return r(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, t) {
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var i = this.tryEntries[r];
								if (
									i.tryLoc <= this.prev &&
									n.call(i, "finallyLoc") &&
									this.prev < i.finallyLoc
								) {
									var a = i;
									break;
								}
							}
							a &&
								("break" === e || "continue" === e) &&
								a.tryLoc <= t &&
								t <= a.finallyLoc &&
								(a = null);
							var o = a ? a.completion : {};
							return (
								(o.type = e),
								(o.arg = t),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), f)
									: this.complete(o)
							);
						},
						complete: function (e, t) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && t && (this.next = t),
								f
							);
						},
						finish: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.finallyLoc === e)
									return this.complete(n.completion, n.afterLoc), _(n), f;
							}
						},
						catch: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.tryLoc === e) {
									var r = n.completion;
									if ("throw" === r.type) {
										var i = r.arg;
										_(n);
									}
									return i;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, t, n) {
							return (
								(this.delegate = { iterator: A(e), resultName: t, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								f
							);
						},
					}),
					e
				);
			}
			function J(e, t, n, r, i, a, o) {
				try {
					var s = e[a](o),
						c = s.value;
				} catch (e) {
					return void n(e);
				}
				s.done ? t(c) : Promise.resolve(c).then(r, i);
			}
			function K(e) {
				return function () {
					var t = this,
						n = arguments;
					return new Promise(function (r, i) {
						var a = e.apply(t, n);
						function o(e) {
							J(a, r, i, o, s, "next", e);
						}
						function s(e) {
							J(a, r, i, o, s, "throw", e);
						}
						o(void 0);
					});
				};
			}
			function Z(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								i,
								a,
								o,
								s = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(s.push(r.value), s.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (i = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((o = n.return()), Object(o) !== o)
									)
										return;
								} finally {
									if (u) throw i;
								}
							}
							return s;
						}
					})(e, t) ||
					Q(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Q(e, t) {
				if (e) {
					if ("string" == typeof e) return X(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? X(e, t)
								: void 0
					);
				}
			}
			function X(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var ee = function () {
				var e = Object(a.d)(),
					t = Z(Object(r.useState)([]), 2),
					n = t[0],
					l = t[1],
					f = Z(Object(r.useState)(!1), 2),
					d = f[0],
					p = f[1],
					g = Z(Object(r.useState)(""), 2),
					m = g[0],
					y = g[1],
					v = Z(Object(r.useState)(c.a.getDocument()), 2),
					b = v[0],
					w = v[1],
					x = Z(
						Object(a.e)(function (e) {
							return [
								u.a.isElementDisabled(e, "signaturePanel"),
								u.a.getCertificates(e),
								u.a.getTrustLists(e),
								u.a.getCurrentLanguage(e),
								u.a.getIsRevocationCheckingEnabled(e),
								u.a.getRevocationProxyPrefix(e),
							];
						}),
						6,
					),
					E = x[0],
					_ = x[1],
					k = x[2],
					A = x[3],
					L = x[4],
					j = x[5],
					O = Z(Object(o.a)(), 1)[0],
					T = (function () {
						var e = K(
							$().mark(function e() {
								return $().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												w(c.a.getDocument());
											case 1:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function () {
							return e.apply(this, arguments);
						};
					})(),
					P = Object(r.useCallback)(
						function () {
							p(!0), e(s.a.setVerificationResult({}));
						},
						[p, e],
					),
					V = function (e, t) {
						var n = c.a
							.getAnnotationManager()
							.getFormFieldCreationManager()
							.isInFormFieldCreationMode();
						"add" === t
							? N(c.a.getAnnotationManager().getAnnotationsList())
							: "delete" === t && n && D(e);
					},
					C = function () {
						var e = c.a.getAnnotationManager().getAnnotationsList();
						N(e);
					},
					N = function (e) {
						var t = [];
						e.forEach(function (e) {
							e instanceof window.Core.Annotations.SignatureWidgetAnnotation &&
								t.push(e.getField());
						});
						var n = new Set(t);
						l(Y(n));
					},
					D = function (e) {
						e.forEach(function (e) {
							F(e);
						}),
							C();
					},
					F = function (e) {
						if (e instanceof window.Core.Annotations.WidgetAnnotation) {
							var t = c.a.getAnnotationManager(),
								n = t.getAnnotationsList().filter(function (t) {
									return t.getCustomData("trn-editing-rectangle-id") === e.Id;
								});
							t.deleteAnnotations(n);
						}
					},
					I = function () {
						l([]), C();
					};
				if (
					(Object(r.useEffect)(
						function () {
							return (
								c.a.addEventListener("documentLoaded", T),
								c.a.addEventListener("documentUnloaded", P),
								c.a.addEventListener("annotationChanged", V),
								c.a.addEventListener("formFieldCreationModeStarted", I),
								c.a.addEventListener("formFieldCreationModeEnded", I),
								function () {
									c.a.removeEventListener("documentLoaded", T),
										c.a.removeEventListener("documentUnloaded", P),
										c.a.removeEventListener("annotationChanged", V),
										c.a.removeEventListener("formFieldCreationModeStarted", I),
										c.a.removeEventListener("formFieldCreationModeEnded", I);
								}
							);
						},
						[P],
					),
					Object(r.useEffect)(
						function () {
							b
								? c.a.getAnnotationsLoadedPromise().then(function () {
										p(!0),
											h(b, _, k, A, L, j, e)
												.then(
													(function () {
														var e = K(
															$().mark(function e(t) {
																var n;
																return $().wrap(function (e) {
																	for (;;)
																		switch ((e.prev = e.next)) {
																			case 0:
																				(n = c.a
																					.getAnnotationManager()
																					.getFieldManager()),
																					l(
																						Object.keys(t).map(function (e) {
																							return n.getField(e);
																						}),
																					),
																					y("");
																			case 3:
																			case "end":
																				return e.stop();
																		}
																}, e);
															}),
														);
														return function (t) {
															return e.apply(this, arguments);
														};
													})(),
												)
												.catch(function (e) {
													e && e.message ? y(e.message) : console.error(e);
												})
												.then(function () {
													C();
												})
												.finally(function () {
													p(!1);
												});
									})
								: p(!0);
						},
						[_, b, e, A],
					),
					E)
				)
					return null;
				return i.a.createElement(
					"div",
					{
						className: "Panel SignaturePanel",
						"data-element": "signaturePanel",
					},
					(function () {
						var e;
						if (d) e = i.a.createElement(S, null);
						else if ("Error reading the local certificate" === m)
							e = O(
								"digitalSignatureVerification.panelMessages.localCertificateError",
							);
						else if ("Download Failed" === m)
							e = O(
								"digitalSignatureVerification.panelMessages.certificateDownloadError",
							);
						else {
							if (n.length) return null;
							e = O(
								"digitalSignatureVerification.panelMessages.noSignatureFields",
							);
						}
						return i.a.createElement(
							"div",
							{ className: "empty-panel-container" },
							i.a.createElement(W.a, {
								className: "empty-icon",
								glyph: z.c[z.e.SIGNATURE].icon,
							}),
							i.a.createElement("div", { className: "empty-message" }, e),
						);
					})(),
					!d &&
						n.length > 0 &&
						n.map(function (e, t) {
							return i.a.createElement(H, {
								key: t,
								name: e.name,
								collapsible: !0,
								field: e,
							});
						}),
				);
			};
			t.default = ee;
		},
	},
]);
//# sourceMappingURL=chunk.47.js.map
