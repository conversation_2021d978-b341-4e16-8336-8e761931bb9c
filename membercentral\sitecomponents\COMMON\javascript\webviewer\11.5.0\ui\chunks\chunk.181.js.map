{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ky.js"], "names": ["module", "exports", "_", "t", "default", "e", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,2DAA2DC,MAAM,KAAKC,OAAO,kFAAkFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,8BAA8BH,MAAM,KAAKI,YAAY,qDAAqDJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,YAAYC,KAAK,WAAWC,EAAE,iBAAiBC,EAAE,YAAYC,GAAG,WAAWC,EAAE,WAAWC,GAAG,UAAUxB,EAAE,UAAUyB,GAAG,SAASC,EAAE,SAASC,GAAG,QAAQC,EAAE,UAAUC,GAAG,WAAW,OAAOhC,EAAEC,QAAQgC,OAAO9B,EAAE,MAAK,GAAIA,EAA7gCD,CAAE,EAAQ", "file": "chunks/chunk.181.js", "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ky=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ky\",weekdays:\"Жекшемби_Дүйшөмбү_Шейшемби_Шаршемби_Бейшемби_Жума_Ишемби\".split(\"_\"),months:\"январь_февраль_март_апрель_май_июнь_июль_август_сентябрь_октябрь_ноябрь_декабрь\".split(\"_\"),weekStart:1,weekdaysShort:\"Жек_Дүй_Шей_Шар_Бей_Жум_Ише\".split(\"_\"),monthsShort:\"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек\".split(\"_\"),weekdaysMin:\"Жк_Дй_Шй_Шр_Бй_Жм_Иш\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD.MM.YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},relativeTime:{future:\"%s ичинде\",past:\"%s мурун\",s:\"бирнече секунд\",m:\"бир мүнөт\",mm:\"%d мүнөт\",h:\"бир саат\",hh:\"%d саат\",d:\"бир күн\",dd:\"%d күн\",M:\"бир ай\",MM:\"%d ай\",y:\"бир жыл\",yy:\"%d жыл\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}