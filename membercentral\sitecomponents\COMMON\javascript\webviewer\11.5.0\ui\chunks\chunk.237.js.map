{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/uz-latn.js"], "names": ["module", "exports", "a", "n", "default", "e", "_", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAAkL,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,UAAUC,SAAS,+DAA+DC,MAAM,KAAKC,OAAO,6EAA6ED,MAAM,KAAKE,UAAU,EAAEC,cAAc,kCAAkCH,MAAM,KAAKI,YAAY,oDAAoDJ,MAAM,KAAKK,YAAY,yBAAyBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,GAAGc,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,aAAa,CAACC,OAAO,kBAAkBC,KAAK,qBAAqBC,EAAE,SAASC,EAAE,aAAaC,GAAG,YAAYC,EAAE,WAAWC,GAAG,UAAUC,EAAE,UAAUC,GAAG,SAASC,EAAE,SAASC,GAAG,QAAQC,EAAE,UAAUC,GAAG,WAAW,OAAOjC,EAAEC,QAAQiC,OAAO/B,EAAE,MAAK,GAAIA,EAAriCD,CAAE,EAAQ", "file": "chunks/chunk.237.js", "sourcesContent": ["!function(a,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(a=\"undefined\"!=typeof globalThis?globalThis:a||self).dayjs_locale_uz_latn=e(a.dayjs)}(this,(function(a){\"use strict\";function e(a){return a&&\"object\"==typeof a&&\"default\"in a?a:{default:a}}var n=e(a),_={name:\"uz-latn\",weekdays:\"Yaks<PERSON><PERSON>_Dushanba_Seshanba_Chorshanba_Payshanba_Juma_Shanba\".split(\"_\"),months:\"Yanvar_Fevral_Mart_Aprel_May_Iyun_Iyul_Avgust_Sentabr_Oktabr_Noyabr_Dekabr\".split(\"_\"),weekStart:1,weekdaysShort:\"Yak_Dush_Sesh_Chor_Pay_Jum_Shan\".split(\"_\"),monthsShort:\"Yan_Fev_Mar_Apr_May_Iyun_Iyul_Avg_Sen_Okt_Noy_Dek\".split(\"_\"),weekdaysMin:\"Ya_Du_Se_Cho_Pa_Ju_Sha\".split(\"_\"),ordinal:function(a){return a},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"D MMMM YYYY, dddd HH:mm\"},relativeTime:{future:\"Yaqin %s ichida\",past:\"Bir necha %s oldin\",s:\"soniya\",m:\"bir daqiqa\",mm:\"%d daqiqa\",h:\"bir soat\",hh:\"%d soat\",d:\"bir kun\",dd:\"%d kun\",M:\"bir oy\",MM:\"%d oy\",y:\"bir yil\",yy:\"%d yil\"}};return n.default.locale(_,null,!0),_}));"], "sourceRoot": ""}