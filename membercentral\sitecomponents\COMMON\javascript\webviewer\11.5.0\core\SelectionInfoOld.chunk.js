/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[10],
		{
			643: function (ya, va, r) {
				r.r(va);
				var la = r(653),
					na = r(164),
					ma = r(63),
					ja = r(100);
				ya = (function () {
					function ia() {
						this.hc = this.rg = this.Yc = this.Ad = null;
						this.Mg = !1;
					}
					ia.prototype.clear = function () {
						Object(ma.b)(this.Ad);
						this.Yc = "";
						Object(ma.b)(this.rg);
						Object(ma.b)(this.hc);
						this.Mg = !1;
					};
					ia.prototype.Od = function () {
						this.Ad = [];
						this.rg = [];
						this.hc = [];
						this.Mg = !1;
					};
					ia.prototype.kJ = function (ca) {
						for (var y = "", x = 0, n, h, b; x < ca.length; )
							(n = ca.charCodeAt(x)),
								9 === n
									? ((y += String.fromCharCode(10)), x++)
									: 128 > n
										? ((y += String.fromCharCode(n)), x++)
										: 191 < n && 224 > n
											? ((h = ca.charCodeAt(x + 1)),
												(y += String.fromCharCode(((n & 31) << 6) | (h & 63))),
												(x += 2))
											: ((h = ca.charCodeAt(x + 1)),
												(b = ca.charCodeAt(x + 2)),
												(y += String.fromCharCode(
													((n & 15) << 12) | ((h & 63) << 6) | (b & 63),
												)),
												(x += 3));
						return y;
					};
					ia.prototype.initData = function (ca) {
						this.Ad = [];
						this.rg = [];
						this.hc = [];
						this.Mg = !1;
						try {
							var y = new ja.a(ca);
							this.Yc = "";
							y.$a();
							if (!y.advance()) return;
							var x = y.current.textContent;
							this.Yc = x = this.kJ(x);
							Object(ma.b)(this.rg);
							y.advance();
							x = y.current.textContent;
							for (var n = x.split(","), h = Object(na.a)(n); h.Kq(); ) {
								var b = h.current;
								try {
									var e = parseInt(b.trim(), 10);
									this.rg.push(e);
								} catch (ba) {}
							}
							Object(ma.b)(this.Ad);
							y.advance();
							x = y.current.textContent;
							n = x.split(",");
							for (var f = Object(na.a)(n); f.Kq(); ) {
								b = f.current;
								try {
									(e = parseFloat(b.trim())), this.Ad.push(e);
								} catch (ba) {}
							}
							Object(ma.b)(this.hc);
							y.advance();
							x = y.current.textContent;
							n = x.split(",");
							ca = [];
							y = [];
							x = 0;
							for (var a = Object(na.a)(n); a.Kq(); ) {
								b = a.current;
								switch (b) {
									case "Q":
										x = 1;
										break;
									case "R":
										x = 2;
										break;
									case "S":
										x = 3;
										break;
									default:
										x = 0;
								}
								if (x) ca.push(0), y.push(x);
								else
									try {
										(e = parseFloat(b.trim())), ca.push(e), y.push(x);
									} catch (ba) {
										return;
									}
							}
							x = 0;
							var w = ca.length;
							h = a = b = n = void 0;
							for (var z = (f = 0), aa = 0; aa < w; ) {
								var ea = y[aa];
								if (0 < ea)
									(x = ea),
										++aa,
										3 === x && ((f = ca[aa]), (z = ca[aa + 1]), (aa += 2));
								else if (1 === x)
									for (e = 0; 8 > e; ++e) this.hc.push(ca[aa++]);
								else
									2 === x
										? ((n = ca[aa++]),
											(b = ca[aa++]),
											(a = ca[aa++]),
											(h = ca[aa++]),
											this.hc.push(n),
											this.hc.push(b),
											this.hc.push(a),
											this.hc.push(b),
											this.hc.push(a),
											this.hc.push(h),
											this.hc.push(n),
											this.hc.push(h))
										: 3 === x &&
											((n = ca[aa++]),
											(b = f),
											(a = ca[aa++]),
											(h = z),
											this.hc.push(n),
											this.hc.push(b),
											this.hc.push(a),
											this.hc.push(b),
											this.hc.push(a),
											this.hc.push(h),
											this.hc.push(n),
											this.hc.push(h));
							}
						} catch (ba) {
							return;
						}
						this.Yc.length &&
							this.Yc.length === this.rg.length &&
							8 * this.Yc.length === this.hc.length &&
							(this.Mg = !0);
					};
					ia.prototype.ready = function () {
						return this.Mg;
					};
					ia.prototype.yE = function () {
						var ca = new la.a();
						if (!this.Ad.length)
							return ca.Ij(this.Ad, -1, this.Yc, this.hc, 0), ca;
						ca.Ij(this.Ad, 1, this.Yc, this.hc, 1);
						return ca;
					};
					ia.prototype.ag = function () {
						return this.hc;
					};
					ia.prototype.getData = function () {
						return {
							m_Struct: this.Ad,
							m_Str: this.Yc,
							m_Offsets: this.rg,
							m_Quads: this.hc,
							m_Ready: this.Mg,
						};
					};
					return ia;
				})();
				va["default"] = ya;
			},
			653: function (ya, va, r) {
				var la = r(134),
					na = r(71),
					ma = r(665);
				ya = (function () {
					function ja() {
						this.Nf = 0;
						this.pc = this.Rb = this.gh = null;
						this.pd = 0;
						this.Mf = null;
					}
					ja.prototype.Od = function () {
						this.Nf = -1;
						this.pd = 0;
						this.Mf = [];
					};
					ja.prototype.Ij = function (ia, ca, y, x, n) {
						this.Nf = ca;
						this.pd = n;
						this.Mf = [];
						this.gh = ia;
						this.Rb = y;
						this.pc = x;
					};
					ja.prototype.Xd = function (ia) {
						return this.Nf === ia.Nf;
					};
					ja.prototype.eo = function () {
						return Math.abs(this.gh[this.Nf]);
					};
					ja.prototype.Eq = function () {
						return 0 < this.gh[this.Nf];
					};
					ja.prototype.xj = function () {
						var ia = this.Eq() ? 6 : 10,
							ca = new ma.a();
						ca.Ij(this.gh, this.Nf + ia, this.Nf, this.Rb, this.pc, 1);
						return ca;
					};
					ja.prototype.Saa = function (ia) {
						if (0 > ia || ia >= this.eo())
							return (
								(ia = new ma.a()),
								ia.Ij(this.gh, -1, -1, this.Rb, this.pc, 0),
								ia
							);
						var ca = this.Eq() ? 6 : 10,
							y = this.Eq() ? 5 : 11,
							x = new ma.a();
						x.Ij(
							this.gh,
							this.Nf + ca + y * ia,
							this.Nf,
							this.Rb,
							this.pc,
							1 + ia,
						);
						return x;
					};
					ja.prototype.Hk = function () {
						var ia = this.Nf + parseInt(this.gh[this.Nf + 1], 10);
						if (ia >= this.gh.length)
							return (
								(ia = new ja()), ia.Ij(this.gh, -1, this.Rb, this.pc, 0), ia
							);
						var ca = new ja();
						ca.Ij(this.gh, ia, this.Rb, this.pc, this.pd + 1);
						return ca;
					};
					ja.prototype.getBBox = function (ia) {
						if (this.Eq())
							(ia.x1 = this.gh[this.Nf + 2 + 0]),
								(ia.y1 = this.gh[this.Nf + 2 + 1]),
								(ia.x2 = this.gh[this.Nf + 2 + 2]),
								(ia.y2 = this.gh[this.Nf + 2 + 3]);
						else {
							for (
								var ca = 1.79769e308,
									y = la.a.MIN,
									x = 1.79769e308,
									n = la.a.MIN,
									h = 0;
								4 > h;
								++h
							) {
								var b = this.gh[this.Nf + 2 + 2 * h],
									e = this.gh[this.Nf + 2 + 2 * h + 1];
								ca = Math.min(ca, b);
								y = Math.max(y, b);
								x = Math.min(x, e);
								n = Math.max(n, e);
							}
							ia.x1 = ca;
							ia.y1 = x;
							ia.x2 = y;
							ia.y2 = n;
						}
					};
					ja.prototype.$L = function () {
						if (this.Mf.length) return this.Mf[0];
						var ia = new na.a(),
							ca = new na.a(),
							y = new ma.a();
						y.Od();
						var x = this.xj(),
							n = new ma.a();
						n.Od();
						for (var h = this.xj(); !h.Xd(y); h = h.Bj()) n = h;
						y = Array(8);
						h = Array(8);
						x.yj(0, y);
						ia.x = (y[0] + y[2] + y[4] + y[6]) / 4;
						ia.y = (y[1] + y[3] + y[5] + y[7]) / 4;
						n.yj(n.mq() - 1, h);
						ca.x = (h[0] + h[2] + h[4] + h[6]) / 4;
						ca.y = (h[1] + h[3] + h[5] + h[7]) / 4;
						0.01 > Math.abs(ia.x - ca.x) &&
							0.01 > Math.abs(ia.y - ca.y) &&
							this.Mf.push(0);
						ia = Math.atan2(ca.y - ia.y, ca.x - ia.x);
						ia *= 180 / 3.1415926;
						0 > ia && (ia += 360);
						this.Mf.push(ia);
						return 0;
					};
					return ja;
				})();
				va.a = ya;
			},
			665: function (ya, va, r) {
				var la = r(653),
					na = r(130),
					ma = r(134);
				ya = (function () {
					function ja() {
						this.vp = this.mf = 0;
						this.pc = this.Rb = this.Ad = null;
						this.pd = 0;
					}
					ja.prototype.Od = function () {
						this.vp = this.mf = -1;
						this.pd = 0;
					};
					ja.prototype.Ij = function (ia, ca, y, x, n, h) {
						this.mf = ca;
						this.vp = y;
						this.Ad = ia;
						this.Rb = x;
						this.pc = n;
						this.pd = h;
					};
					ja.prototype.Xd = function (ia) {
						return this.mf === ia.mf;
					};
					ja.prototype.mq = function () {
						return parseInt(this.Ad[this.mf], 10);
					};
					ja.prototype.om = function () {
						return parseInt(this.Ad[this.mf + 2], 10);
					};
					ja.prototype.Ej = function () {
						return parseInt(this.Ad[this.mf + 1], 10);
					};
					ja.prototype.Eq = function () {
						return 0 < this.Ad[this.vp];
					};
					ja.prototype.wDa = function () {
						return Math.abs(this.Ad[this.vp]);
					};
					ja.prototype.Bj = function () {
						var ia = this.Eq(),
							ca = ia ? 5 : 11;
						if (this.mf >= this.vp + (ia ? 6 : 10) + (this.wDa() - 1) * ca)
							return (
								(ca = new ja()), ca.Ij(this.Ad, -1, -1, this.Rb, this.pc, 0), ca
							);
						ia = new ja();
						ia.Ij(
							this.Ad,
							this.mf + ca,
							this.vp,
							this.Rb,
							this.pc,
							this.pd + 1,
						);
						return ia;
					};
					ja.prototype.sCa = function (ia) {
						var ca = this.mq();
						return 0 > ia || ia >= ca
							? -1
							: parseInt(this.Ad[this.mf + 1], 10) + ia;
					};
					ja.prototype.yj = function (ia, ca) {
						ia = this.sCa(ia);
						if (!(0 > ia)) {
							var y = new la.a();
							y.Ij(this.Ad, this.vp, this.Rb, this.pc, 0);
							if (y.Eq()) {
								var x = new na.a();
								y.getBBox(x);
								y = x.y1 < x.y2 ? x.y1 : x.y2;
								x = x.y1 > x.y2 ? x.y1 : x.y2;
								ia *= 8;
								ca[0] = this.pc[ia];
								ca[1] = y;
								ca[2] = this.pc[ia + 2];
								ca[3] = ca[1];
								ca[4] = this.pc[ia + 4];
								ca[5] = x;
								ca[6] = this.pc[ia + 6];
								ca[7] = ca[5];
							} else for (ia *= 8, y = 0; 8 > y; ++y) ca[y] = this.pc[ia + y];
						}
					};
					ja.prototype.Lk = function (ia) {
						var ca = new la.a();
						ca.Ij(this.Ad, this.vp, this.Rb, this.pc, 0);
						if (ca.Eq()) {
							var y = this.Ad[this.mf + 3],
								x = this.Ad[this.mf + 4];
							if (y > x) {
								var n = y;
								y = x;
								x = n;
							}
							n = new na.a();
							ca.getBBox(n);
							ca = n.y1 < n.y2 ? n.y1 : n.y2;
							n = n.y1 > n.y2 ? n.y1 : n.y2;
							ia[0] = y;
							ia[1] = ca;
							ia[2] = x;
							ia[3] = ca;
							ia[4] = x;
							ia[5] = n;
							ia[6] = y;
							ia[7] = n;
						} else
							for (y = this.mf + 3, x = 0; 8 > x; ++x) ia[x] = this.Ad[y + x];
					};
					ja.prototype.getBBox = function (ia) {
						var ca = new la.a();
						ca.Ij(this.Ad, this.vp, this.Rb, this.pc, 0);
						if (ca.Eq()) {
							var y = this.Ad[this.mf + 3],
								x = this.Ad[this.mf + 4];
							if (y > x) {
								var n = y;
								y = x;
								x = n;
							}
							n = new na.a();
							ca.getBBox(n);
							ca = n.y1 < n.y2 ? n.y1 : n.y2;
							n = n.y1 > n.y2 ? n.y1 : n.y2;
							ia[0] = y;
							ia[1] = ca;
							ia[2] = x;
							ia[3] = n;
						} else {
							y = 1.79769e308;
							x = ma.a.MIN;
							ca = 1.79769e308;
							n = ma.a.MIN;
							for (var h = this.mf + 3, b = 0; 4 > b; ++b) {
								var e = this.Ad[h + 2 * b],
									f = this.Ad[h + 2 * b + 1];
								y = Math.min(y, e);
								x = Math.max(x, e);
								ca = Math.min(ca, f);
								n = Math.max(n, f);
							}
							ia[0] = y;
							ia[1] = ca;
							ia[2] = x;
							ia[3] = n;
						}
					};
					return ja;
				})();
				va.a = ya;
			},
		},
	]);
}).call(this || window);
