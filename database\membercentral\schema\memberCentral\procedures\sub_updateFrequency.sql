ALTER PROC dbo.sub_updateFrequency 
@frequencyID int, 
@frequencyName varchar(50), 
@frequency int, 
@frequencyShortName varchar (10), 
@rateRequired bit, 
@hasInstallments bit = 0, 
@monthlyInterval int = null, 
@isSystemRate bit = 0,
@siteID int, 
@status char(1),
@recordedByMemberID int,
@successInd bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @prevFrequencyName varchar(50),  @msg varchar(max), @crlf varchar(2);
	SET @crlf = char(13) + char(10);
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	set @successInd = 0;

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;
	CREATE TABLE #tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Frequency Name] varchar(max), [Number of Installments] varchar(max), 
		[Frequency Short Name] varchar(max), [Rate Required] varchar(max), [Has Installments] varchar(max), [Monthly Interval] varchar(max));

	INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
	VALUES ('DATATYPECODE', 'STRING', 'INTEGER', 'STRING', 'BIT', 'BIT', 'INTEGER');

	INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
	SELECT 'OLDVAL', frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, monthlyInterval
	FROM dbo.sub_frequencies
	WHERE frequencyID = @frequencyID;

	SELECT @prevFrequencyName = [Frequency Name]
	FROM #tmpAuditLogData
	WHERE rowCode = 'OLDVAL';

	IF NOT EXISTS (select frequencyID from dbo.sub_frequencies where siteID = @siteID and frequencyID <> @frequencyID and [status] <> 'D' and (frequencyName = @frequencyName or frequencyShortName = @frequencyShortName)) BEGIN
		BEGIN TRAN;
			UPDATE dbo.sub_frequencies
			SET	frequencyName = @frequencyName, 
				frequency = @frequency, 
				frequencyShortName = @frequencyShortName, 
				rateRequired = @rateRequired, 
				hasInstallments = @hasInstallments, 
				monthlyInterval = @monthlyInterval, 
				[status] =  @status
			WHERE frequencyID = @frequencyID;
	
			-- update any virtual group conditions
			-- yes, the frequencyID here should be varchar
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE orgID = @orgID
			and left(fieldCode,4) = 'sub_'	
			and conditionID in (
				select c.conditionID 
				from dbo.ams_virtualGroupConditions as c
				inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
					and cv.conditionValue = cast(@frequencyID as varchar(10))
				inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
					and k.conditionKey = 'subFrequency'
				where c.orgID = @orgID
			);
			
			set @successInd = 1;

			INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
			SELECT 'NEWVAL', frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, monthlyInterval
			FROM dbo.sub_frequencies
			WHERE frequencyID = @frequencyID;

			EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
		
			IF ISNULL(@msg,'') <> '' BEGIN
				DECLARE @subKeyMapJSON varchar(100) = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
				SET @msg = 'Frequency [' + STRING_ESCAPE(@prevFrequencyName,'json') + '] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

				EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msg,
					@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
			END
		COMMIT TRAN;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
