{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/MergeToggleButton.js"], "names": ["propTypes", "type", "PropTypes", "string", "isFlyoutItem", "bool", "style", "object", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "ref", "useState", "isMerged", "setIsMerged", "menuItems", "dataElement", "icon", "title", "handleClick", "onClick", "additionalClass", "key", "isActive", "img", "ariaPressed", "displayName"], "mappings": "m/CAMA,IAAMA,EAAY,CAChBC,KAAMC,IAAUC,OAChBC,aAAcF,IAAUG,KACxBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUC,QAGjBM,EAAoBC,sBAAW,SAACC,EAAOC,GAC3C,IAAQR,EAAyCO,EAAzCP,aAAcH,EAA2BU,EAA3BV,KAAMK,EAAqBK,EAArBL,MAAOE,EAAcG,EAAdH,UAEY,IAAfK,oBAAS,GAAM,GAAxCC,EAAQ,KAAEC,EAAW,KAE5B,EAAqCC,IAAUF,EAAW,oBAAsB,mBAAxEG,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAElBL,GAAaD,IAGf,OACEV,EACE,kBAAC,IAAmB,KACdO,EAAK,CACTC,IAAKA,EACLS,QAASD,EACTE,gBAAuC,MAGvC,kBAAC,IAAY,CACXC,IAAKtB,EACLuB,UArBS,EAsBTH,QAASD,EACTH,YAAaA,EACbE,MAAOA,EACPM,IAAKP,EACLQ,aA1BS,EA2BTpB,MAAOA,EACPE,UAAWA,OAMrBC,EAAkBT,UAAYA,EAC9BS,EAAkBkB,YAAc,oBAEjBlB", "file": "chunks/chunk.95.js", "sourcesContent": ["import React, { forwardRef, useState } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  type: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst MergeToggleButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, type, style, className } = props;\n  const isActive = false;\n  const [isMerged, setIsMerged] = useState(false);\n\n  const { dataElement, icon, title } = menuItems[isMerged ? 'cellUnmergeToggle' : 'cellMergeToggle'];\n\n  const handleClick = () => {\n    // handle button click\n    setIsMerged(!isMerged);\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={type}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nMergeToggleButton.propTypes = propTypes;\nMergeToggleButton.displayName = 'MergeToggleButton';\n\nexport default MergeToggleButton;"], "sourceRoot": ""}