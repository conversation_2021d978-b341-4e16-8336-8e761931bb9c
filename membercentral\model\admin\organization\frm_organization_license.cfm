<cfsavecontent variable="local.licenseJS">
	<script type="text/javascript">
	var pltID = 0;
	<!--- License Types --->
	function getLicenseTypes() {
		var getResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var templateSource = $('#mc_orgLicenseTypesTemplate').html();
				var template = Handlebars.compile(templateSource);
				$('#mc_orgLicenseTypes').html(template({ arrlicensetypes: r.arrlicensetypes}));
				$('tbody#newprolicensetypelink').removeClass('d-none');
			} else {
				alert('We were unable to load the License Types.');
			}
		};

		$('#mc_orgLicenseTypes').html('<tr><td colspan="6" class="c py-3">'+mca_getLoadingHTML('Loading..')+'</tr>');
		TS_AJX('ADMINORG','getProLicenseTypes',{},getResult,getResult,20000,getResult);
	}
	function addNewProLicenseType() {
		var objLicenseType = { arrlicensetypes:[{ pltypeid:'x', plname:'', alertduplicates:0 }] };
		addProLicenseType(objLicenseType);
		$('#newprolicensetypelink').addClass('d-none');
	}
	function addProLicenseType(obj) {
		var licenseTypeTemplateSource = $('#mc_orgLicenseTypesTemplate').html();
		var licenseTypeTemplate = Handlebars.compile(licenseTypeTemplateSource);		
		$('#mc_orgLicenseTypes').append(licenseTypeTemplate(obj));
	}
	function resetLicenseTypeGridIcons(){
		$('#licenseTypesTable').find('tr.actionRow td i.moveuplink, tr.actionRow td i.movedownlink').addClass('invisible');
		if ($('#licenseTypesTable tbody#mc_orgLicenseTypes tr.actionRow').length > 1) {
			$('#licenseTypesTable tbody#mc_orgLicenseTypes tr.actionRow td').find('i.moveuplink, i.movedownlink').removeClass('invisible');
			$('#licenseTypesTable tbody#mc_orgLicenseTypes tr.actionRow:first').find('i.moveuplink').addClass('invisible');
			$('#licenseTypesTable tbody#mc_orgLicenseTypes tr.actionRow:last').find('i.movedownlink').addClass('invisible');
		}
	}
	function toggleLicenseTypeGridLoading(f){
		$('#divLicenseTypesLoading').toggleClass('invisible', !f);
	}
	function removeProLicenseType(pltid) {
		if (pltid=='x') {
			$('#mc_licenseTypeRow_x').remove();
			$('#newprolicensetypelink').removeClass('d-none');
		} else {
			pltid = parseInt(pltid);
			var arrTypes = $('#originalEarlyLicenseTypes').val().split(',');
			if (arrTypes.length) {
				arrTypes = $.map(arrTypes, function(n){ return parseInt(n) });
				if ( $.inArray(pltid, arrTypes) != -1 ) {
					alert('This professional license type is used in determining earliest license date.');
					return false;
				}
			}
			pltID = pltid;
			var msg = '<div class="alert alert-warning"><b>Are you sure you want to delete this professional license type?</b><br/>Once deleted, the professional license type cannot be recovered.</div>';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Delete Professional License Type',
				iframe: false ,
				strmodalbody: { 
					content: msg
				},
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: 'doRemoveProLicenseType',
					extrabuttonlabel: 'OK'
				}
			});
		}
	}
	function doRemoveProLicenseType() {
		var removeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				MCModalUtils.hideModal();
				$('#mc_licenseTypeRow_' + pltID).find('td').fadeOut(500, 
					function() {
						$(this).parents('tr:first').remove();
						resetLicenseTypeGridIcons();
					}
				);
			} else {
				MCModalUtils.hideModal();
				alert('We were unable to remove the License Type.');
			}
		};
		$('#btnMCModalSave').html('Please wait...').prop('disabled',true);
		var objParams = { licenseTypeID:pltID };
		TS_AJX('ADMINORG','removeProLicenseType',objParams,removeResult,removeResult,10000,removeResult);
	}
	function moveProLicenseType(pltid,dir) {
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var tr = $('#mc_licenseTypeRow_'+pltid);
				if(dir == 'up'){
					var trprev = tr.prev();
					tr.remove().insertBefore(trprev);
				}
				else {
					var trnext = tr.next();
					tr.remove().insertAfter(trnext);
				}
				resetLicenseTypeGridIcons();
			} else {
				alert('We were unable to move '+dir+' this professional license type. Try again.');
			}
			toggleLicenseTypeGridLoading(false);
		};
		toggleLicenseTypeGridLoading(true);
		var objParams = { licenseTypeID:pltid, dir:dir };
		TS_AJX('ADMINORG','moveProLicenseType',objParams,moveResult,moveResult,10000,moveResult);
	}
	function checkLicenseTypeName(pltid) {
		var oldVal = $('#proLicenseType_' + pltid + '_').val();
		var txt2 = oldVal.replace(/[^A-Za-z0-9 \&\_\:\-\/]/g, '');
		if (txt2 != oldVal) {
			$('#proLicenseType_' + pltid + '_').val(txt2);
			MCModalUtils.showModal({
				isslideout: false,
				iframe: false,
				size: 'md',
				title: 'Invalid Professional License Type',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				strmodalbody: { 
					content: 'Professional License Types may only contain:<br/>- letters A-Z<br/>- numbers 0-9<br/>- special characters \& \_ \: \- \/ (space)<br/>All other characters will be removed.'
				},
				strmodalfooter: {
					showclose: true
				}
			});
		}
	}
	function insertLicenseType(pltid) {
		var fieldControl = $('#proLicenseType_' + pltid + '_');
		var licenseType = fieldControl.val().trim();
		if (!licenseType.length){
			fieldControl.focus();
			return false;
		}

		var insertResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var objLicenseType = { arrlicensetypes:[{ pltypeid:r.pltypeid, plname:licenseType, alertduplicates:0 }] };
				var licenseTypeTemplateSource = $('#mc_orgLicenseTypesTemplate').html();
				var licenseTypeTemplate = Handlebars.compile(licenseTypeTemplateSource);
				$('#mc_licenseTypeRow_x').remove();
				$('#mc_orgLicenseTypes').append(licenseTypeTemplate(objLicenseType));
				resetLicenseTypeGridIcons();
				$('#newprolicensetypelink').removeClass('d-none');
			} else {
				alert('We were unable to add the License Type.');
				toggleSaveIcon('btnSaveLicenseType_' + pltid,true);
			}
		};
		
		toggleSaveIcon('btnSaveLicenseType_' + pltid,false);
		var objParams = { licenseType:licenseType };
		TS_AJX('ADMINORG','insertProLicenseType',objParams,insertResult,insertResult,10000,insertResult);
	}
	function updateLicenseType(pltid) {
		var fieldControl = $('#proLicenseType_' + pltid + '_');
		var licenseType = fieldControl.val().trim();
		var alertDuplicates = $('#proLicenseDuplicate_' + pltid + '_').prop("checked");
		if (!licenseType.length){
			fieldControl.focus();
			return false;
		}

		var saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				if(r.earliestlicensedatecalcfields){
					setEarliestLicenseDateCalcFields(r.earliestlicensedatecalcfields);
				}
			} else {
				alert(r.errmsg != 'undefined' ? r.errmsg : 'We were unable to save the License Type.');
			}
			toggleSaveIcon('btnSaveLicenseType_' + pltid,true);
		};
		
		toggleSaveIcon('btnSaveLicenseType_' + pltid,false);
		var objParams = { licenseTypeID:pltid, licenseType:licenseType, alertDuplicates:alertDuplicates };
		TS_AJX('ADMINORG','updateProLicenseType',objParams,saveResult,saveResult,10000,saveResult);
	}
	function updateLicenseTypeDuplicate(pltid) {
		var alertDuplicates = $('#proLicenseDuplicate_' + pltid + '_').prop("checked");

		var saveResult = function(r) {
			if (!(r.success && r.success.toLowerCase() == 'true'))
				alert('We were unable to save the Alert on Duplicates setting.');
			toggleLicenseTypeGridLoading(false);
		};
		
		toggleLicenseTypeGridLoading(true);
		var objParams = { licenseTypeID:pltid, alertDuplicates:alertDuplicates };
		TS_AJX('ADMINORG','updateProLicenseTypeDuplicate',objParams,saveResult,saveResult,10000,saveResult);
	}
	<!--- License Statuses --->
	function getLicenseStatuses() {
		var getResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var templateSource = $('#mc_orgLicenseStatusesTemplate').html();
				var template = Handlebars.compile(templateSource);
				$('#mc_orgLicenseStatuses').html(template({ arrlicensestatuses: r.arrlicensestatuses}));
				$('tbody#newprolicensestatuslink').removeClass('d-none');
			} else {
				alert('We were unable to load the License Statuses.');
			}
		};

		$('#mc_orgLicenseStatuses').html('<tr><td colspan="5" class="c py-3">'+mca_getLoadingHTML('Loading..')+'</tr>');
		TS_AJX('ADMINORG','getProLicenseStatuses',{},getResult,getResult,20000,getResult);
	}
	function addNewProLicenseStatus() {
		var objLicenseStatus = { arrlicensestatuses:[{ plstatusid:'x', statusname:'' }] };
		addProLicenseStatus(objLicenseStatus);
		$('#newprolicensestatuslink').addClass('d-none');
	}
	function addProLicenseStatus(obj) {
		var licenseStatusTemplateSource = $('#mc_orgLicenseStatusesTemplate').html();
		var licenseStatusTemplate = Handlebars.compile(licenseStatusTemplateSource);		
		$('#mc_orgLicenseStatuses').append(licenseStatusTemplate(obj));
	}
	function resetLicenseStatusGridIcons(){
		$('#licenseStatusesTable').find('tr.actionRow td i.moveuplink, tr.actionRow td i.movedownlink').addClass('invisible');
		if ($('#licenseStatusesTable tbody#mc_orgLicenseStatuses tr.actionRow').length > 1) {
			$('#licenseStatusesTable tbody#mc_orgLicenseStatuses tr.actionRow td').find('i.moveuplink, i.movedownlink').removeClass('invisible');
			$('#licenseStatusesTable tbody#mc_orgLicenseStatuses tr.actionRow:first').find('i.moveuplink').addClass('invisible');
			$('#licenseStatusesTable tbody#mc_orgLicenseStatuses tr.actionRow:last').find('i.movedownlink').addClass('invisible');
		}
	}
	function toggleLicenseStatusGridLoading(f){
		$('#divLicenseStatusesLoading').toggleClass('invisible', !f);
	}
	function removeProLicenseStatus(plsid) {
		if (plsid=='x') {
			$('#mc_licenseStatusRow_x').remove();
			$('#newprolicensestatuslink').removeClass('d-none');
		} else {
			plsid = parseInt(plsid);
			var arrStatuses = $('#originalEarlyLicenseStatuses').val().split(',');
			if (arrStatuses.length) {
				arrStatuses = $.map(arrStatuses, function(n){ return parseInt(n) });
				if ( $.inArray(plsid, arrStatuses) != -1 ) {
					alert('This professional license status is used in determining earliest license date.');
					return false;
				}
			}

			MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Delete Professional License Status',
				iframe: true,
				contenturl: '<cfoutput>#this.link.removeProLicenseStatus#</cfoutput>&mode=direct&plsid=' + plsid,
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnDeleteLicStatus").click',
					extrabuttonlabel: 'Save',
				}
			});
		}
	}
	function doRemoveProLicenseStatus(plsid,newplsid) {
		var removeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				MCModalUtils.hideModal();
				$('#mc_licenseStatusRow_' + plsid).find('td').fadeOut(500, 
					function() { 
						$(this).parents('tr:first').remove();
						resetLicenseStatusGridIcons();
					}
				);
			} else {
				MCModalUtils.hideModal();
				alert('We were unable to remove the License Status.');
			}
		};
		
		var objParams = { licenseStatusID:plsid, newLicenseStatusID:newplsid };
		TS_AJX('ADMINORG','removeProLicenseStatus',objParams,removeResult,removeResult,10000,removeResult);
	}
	function moveProLicenseStatus(plsid,dir) {
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var tr = $('#mc_licenseStatusRow_'+plsid);
				if(dir == 'up'){
					var trprev = tr.prev();
					tr.remove().insertBefore(trprev);
				}
				else {
					var trnext = tr.next();
					tr.remove().insertAfter(trnext);
				}
				resetLicenseStatusGridIcons();
				
			} else {
				alert('We were unable to move '+dir+' this professional license status. Try again.');
			}
			toggleLicenseStatusGridLoading(false);
		};
		toggleLicenseStatusGridLoading(true);
		var objParams = { licenseStatusID:plsid, dir:dir };
		TS_AJX('ADMINORG','moveProLicenseStatus',objParams,moveResult,moveResult,10000,moveResult);
	}
	function insertLicenseStatus(plsid) {
		var fieldControl = $('#proLicenseStatus_' + plsid + '_');
		var licenseStatus = fieldControl.val().trim();
		if (!licenseStatus.length){
			fieldControl.focus();
			return false;
		}

		var insertResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var objLicenseStatus = { arrlicensestatuses:[{ plstatusid:r.plstatusid, statusname:licenseStatus }] };
				var licenseStatusTemplateSource = $('#mc_orgLicenseStatusesTemplate').html();
				var licenseStatusTemplate = Handlebars.compile(licenseStatusTemplateSource);
				$('#mc_licenseStatusRow_x').remove();
				$('#mc_orgLicenseStatuses').append(licenseStatusTemplate(objLicenseStatus));
				resetLicenseStatusGridIcons();
				$('#newprolicensestatuslink').removeClass('d-none');
			} else {
				alert('We were unable to add the License Status.');
				toggleSaveIcon('btnSaveLicenseStatus_' + plsid,true);
			}
		};
		
		toggleSaveIcon('btnSaveLicenseStatus_' + plsid,false);
		var objParams = { licenseStatus:licenseStatus };
		TS_AJX('ADMINORG','insertLicenseStatus',objParams,insertResult,insertResult,10000,insertResult);
	}
	function updateLicenseStatus(plsid) {
		var fieldControl = $('#proLicenseStatus_' + plsid + '_');
		var licenseStatus = fieldControl.val().trim();
		if (!licenseStatus.length){
			fieldControl.focus();
			return false;
		}

		var saveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				if(r.earliestlicensedatecalcfields){
					setEarliestLicenseDateCalcFields(r.earliestlicensedatecalcfields);
				}
			}
			else {
				alert(r.errmsg != 'undefined' ? r.errmsg : 'We were unable to save the License Status.');
			}
			toggleSaveIcon('btnSaveLicenseStatus_' + plsid,true);
		};
		
		toggleSaveIcon('btnSaveLicenseStatus_' + plsid,false);
		var objParams = { licenseStatusID:plsid, licenseStatus:licenseStatus };
		TS_AJX('ADMINORG','updateLicenseStatus',objParams,saveResult,saveResult,10000,saveResult);
	}
	function toggleSaveIcon(id, f){
		$('#' + id).prop("disabled", !f).find('.btn-wrapper--icon').html(f ? '<i class="fa fa-floppy-disk"></span>' : '<span class="spinner-border spinner-border-sm"></span>');
	}
	function getNewOpts(arrOpts, optID, optValName) {
		var newOpts = '';
		for (var i=0; i<arrOpts.length; i++) {
			newOpts += '<option value="' + arrOpts[i][optID] + '">' + arrOpts[i][optValName] + '</option>';
		}
		return newOpts;
	}
	<!--- Other Settings --->
	function setEarliestLicenseDateCalcFields(obj) {
		var arrSelectedEarlyLicenseTypes = $('#earlyLicenseTypes').val() || [];
		var arrSelectedEarlyLicenseStatuses = $('#earlyLicenseStatuses').val() || [];

		$('#earlyLicenseTypes').find('option').remove().end().append(getNewOpts(obj.arrlicensetypes, 'pltypeid', 'plname'));
		$('#earlyLicenseStatuses').find('option').remove().end().append(getNewOpts(obj.arrlicensestatuses, 'plstatusid', 'statusname'));

		$('#earlyLicenseTypes').val(arrSelectedEarlyLicenseTypes);
		$('#earlyLicenseStatuses').val(arrSelectedEarlyLicenseStatuses);
	}
	function saveEarliestLicenseDateCalcFields() {
		var arrReq = new Array();
		mca_hideAlert('err_earliestdate');
		arrReq = arrReq.concat(validateRollingDates());
			
			if(arrReq.length){
				mca_showAlert('err_earliestdate', arrReq.join('<br/>'), true);
				return false;
			}

		$("#btnSaveEarliestLicenseDateCalcFields").hide();
		$('.saveEarliestLicenseDateCalcFieldsInfo').css('color','#000').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> <b>Saving...</b>').show();

		var earlyMemberCustomField = $('#earlyMemberCustomField').val() || 0;
		var arrEarlyLicenseTypes = $('#earlyLicenseTypes').val() || [];
		var arrEarlyLicenseStatuses = $('#earlyLicenseStatuses').val() || [];
		var numberOfYearsInLicensureCustomField = $('#numberOfYearsInLicensureCustomField').val() || 0;
		var runDate = $('#runDate').val() || '';
		var runDateAdvanceDateAFID = $('#roll_runDate_afid').val() || 0;
		var advanceDate = $('#roll_adv').val() || '';
		var advanceDateAFID = $('#roll_adv_afid').val() || 0;

		var earlyLicenseTypes = arrEarlyLicenseTypes.join(',');
		var earlyLicenseStatuses = arrEarlyLicenseStatuses.join(',');

		var saveResult = function(r) {
			$("#btnSaveEarliestLicenseDateCalcFields").show();
			$('.saveEarliestLicenseDateCalcFieldsInfo').html('');
			if (r.success && r.success.toLowerCase() == 'true') {
				$('.saveEarliestLicenseDateCalcFieldsInfo').html('<span class="text-green">Saved</span>').show().fadeOut(5000);
				$('#originalEarlyLicenseTypes').val(r.selectedearlyproflicensetypes);
				$('#originalEarlyLicenseStatuses').val(r.selectedearlyproflicensestatuses);

			} else {
				alert('We were unable to save the Earliest License Date Determining Fields.');
			}
		};

		var objParams = { earlyMemberCustomField:earlyMemberCustomField,earlyLicenseTypes:earlyLicenseTypes, earlyLicenseStatuses:earlyLicenseStatuses ,numberOfYearsInLicensureCustomField:numberOfYearsInLicensureCustomField,
			runDate:runDate, runDateAdvanceDateAFID:runDateAdvanceDateAFID, advanceDate:advanceDate, advanceDateAFID:advanceDateAFID};
		TS_AJX('ADMINORG','saveEarliestLicenseDateCalcFields',objParams,saveResult,saveResult,600000,saveResult);
	}
	function saveLicenseTypeLabels() {
		mca_hideAlert('err_licensetypelabels');

		var profLicenseStatusLabel = $('#profLicenseStatusLabel').val().trim();
		var profLicenseNumberLabel = $('#profLicenseNumberLabel').val().trim();
		var profLicenseDateLabel = $('#profLicenseDateLabel').val().trim();

		var arrReq = [];
		if (!profLicenseStatusLabel.length) arrReq.push('Enter the Prof. License Status Label');
		if (!profLicenseNumberLabel.length) arrReq.push('Enter the Prof. License Number Label');
		if (!profLicenseDateLabel.length) arrReq.push('Enter the Prof. License Date Label');

		if (arrReq.length) {
			mca_showAlert('err_licensetypelabels', arrReq.join('<br/>'));
			return false;
		}

		var saveResult = function(r) {
			$("#btnSaveLabels").show();
			$('.saveLabelsInfo').html('');
			if (r.success && r.success.toLowerCase() == 'true') {
				$('.saveLabelsInfo').html('<span class="text-green">Saved</span>').show().fadeOut(5000);
			} else {
				alert('We were unable to save the Labels.');
			}
		};

		$("#btnSaveLabels").hide();
		$('.saveLabelsInfo').css('color','#000').html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> <b>Saving...</b>').show();

		var objParams = { profLicenseStatusLabel:profLicenseStatusLabel, profLicenseNumberLabel:profLicenseNumberLabel, profLicenseDateLabel:profLicenseDateLabel };
		TS_AJX('ADMINORG','saveLicenseTypeLabels',objParams,saveResult,saveResult,10000,saveResult);
	}

	objRptExtra = new Array();
		
	function writeRollDates() {
		var handleResponse = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				$('div#divStepRollDates').show();

				var tempsel = $('<select>');
				tempsel.append('<option value="0">No Advance</option>');
				$.each(r.qryaf, function(i) {
					tempsel.append('<option value="' + r.qryaf[i].afid + '">' + r.qryaf[i].afname + '</option>');
				});

				var rolltbl = '<table cellpadding="4" cellspacing="0">';
				$('input.rolldate').each(function() {
					rolltbl += '<div class="form-group row">\
									<label for="roll_' + $(this).attr('name') + '_afid" class="col-sm-5 col-form-label-sm font-size-md">How should we advance this date?</label>\
									<div class="col-sm-7">\
										<div class="input-group input-group-sm">\
											<select name="roll_' + $(this).attr('name') + '_afid" id="roll_' + $(this).attr('name') + '_afid" onchange="updateAFExample(\'' + $(this).attr('name') + '\',\'roll_' + $(this).attr('name') + '_afid\',\'divroll_' + $(this).attr('name') + '_afid\')" class="form-control rollafid"></select>\
											<div class="input-group-append">\
												<div id="divroll_' + $(this).attr('name') + '_afid" class="input-group-text"></div>\
											</div>\
										</div>\
									</div>\
								</div>';
				});								
				rolltbl += '<div class="form-group row">\
								<label for="roll_adv" class="col-sm-5 col-form-label-sm font-size-md">When should we next advance these dates?</label>\
								<div class="col-sm-7">\
									<div class="input-group input-group-sm">\
										<input type="text" name="roll_adv" id="roll_adv" value="" class="form-control dateControl" onchange="updateAFExample(\'roll_adv\',\'roll_adv_afid\',\'divroll_adv_afid\')" autocomplete="off">\
										<div class="input-group-append">\
											<span class="input-group-text cursor-pointer calendar-button" data-target="roll_adv"><i class="fa-solid fa-calendar"></i></span>\
											<span class="input-group-text"><a href="javascript:clearRollADVDate();"><i class="fa-solid fa-circle-xmark"></i></a></span>\
										</div>\
									</div>\
								</div>\
							</div>';
				rolltbl += '<div class="form-group row">\
								<label for="roll_adv_afid" class="col-sm-5 col-form-label-sm font-size-md">How should we advance this date?</label>\
								<div class="col-sm-7">\
									<div class="input-group input-group-sm">\
										<select name="roll_adv_afid" id="roll_adv_afid" class="form-control rollafid" onchange="updateAFExample(\'roll_adv\',\'roll_adv_afid\',\'divroll_adv_afid\')"></select>\
										<div class="input-group-append">\
											<div id="divroll_adv_afid" class="input-group-text"></div>\
										</div>\
									</div>\
								</div>\
							</div>';
				$('#rolldateYes').html(rolltbl);

				var rollingDates = false;
				var roll_adv = '';
				var roll_afid = '';

				$('input.rolldate').each(function() {
					var mcrdfld = $(this).attr('name');
					roll_afid = '';
					for (var x=0; x<objRptExtra.length; x++) {
						if (objRptExtra[x].key.toLowerCase() == mcrdfld.toLowerCase()) { roll_afid = objRptExtra[x].afid; break; }
					}
					if (roll_afid.length > 0) rollingDates = true;
					tempsel.clone().children().appendTo('#roll_' + mcrdfld + '_afid');
					if (roll_afid.length == 0) $('#roll_' + mcrdfld + '_afid')[0].selectedIndex = 0;
					else $('#roll_' + mcrdfld + '_afid option[value="' + roll_afid + '"]').attr('selected', 'selected'); 
					updateAFExample($(this).attr('name'),'roll_' + mcrdfld + '_afid','divroll_' + mcrdfld + '_afid');
				});

				roll_afid = '';
				for (var x=0; x<objRptExtra.length; x++) {
					if (objRptExtra[x].key.toLowerCase() == 'afrundate') { roll_adv = objRptExtra[x].value; roll_afid = objRptExtra[x].afid; break; }
				}
				if (roll_adv.length > 0 || roll_afid.length > 0) rollingDates = true;
				$('#roll_adv').val(roll_adv);
				tempsel.clone().children().appendTo('#roll_adv_afid');
				if (roll_afid.length == 0) $('#roll_adv_afid')[0].selectedIndex = 0;
				else $('#roll_adv_afid option[value="' + roll_afid + '"]').attr('selected', 'selected');
				updateAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');

				if (rollingDates) {
					rollDateRdo(1);
				}

				mca_setupDatePickerField('roll_adv');
				mca_setupCalendarIcons('frmLicense');
			} else { 
				handleResponseFail();
			}
		};
		var handleResponseFail = function(r) {
			alert('There was a problem loading the advancement formulas. Try again.'); 
		};

		TS_AJX_SYNC('VIRTUALGROUPS','getAdvanceFormula',{ fororg:0 },handleResponse,handleResponseFail,10000,handleResponseFail);
	}
	function rollDateRdo(v) {
		if (v == 1) {
			$('#rolldateYes').removeClass('d-none');
			setDefaultRollDataValue();
		} else {
			$('#rolldateYes').addClass('d-none');
			$('select.rollafid').val('0').trigger('change');
			$('#roll_adv').val('').trigger('change');
		}
	}
	function updateAFExample(dt,af,txt) {
		var jtxt = $('#'+txt);
		var dtVal = $('#'+dt).val();
		var afVal = $('#'+af).val();

		var chkDateExResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') jtxt.html(r.retdate);
			else jtxt.html('Unable to calculate date.');
		};

		if (afVal == '0' || dtVal == '') {
			jtxt.html('&lt;No Advance&gt;');
		} else {
			jtxt.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Calculating...');

			var objParams = { baseDate:dtVal, afid:afVal };
			TS_AJX('ADMADVFORM','getAdvanceFormulaDateforAFID',objParams,chkDateExResult,chkDateExResult,10000,chkDateExResult);
		}
	}
	function clearRollADVDate() {
		mca_clearDateRangeField('roll_adv:enabled');
		updateAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');
	}
	function clearRollRunDate() {
		mca_clearDateRangeField('runDate:enabled');
		updateAFExample('runDate','roll_runDate_afid','divroll_runDate_afid');
	}
	function validateRollingDates() {
		var arrReq = new Array();
		var earlyMemberCustomField = $('#earlyMemberCustomField').val() || 0;
		var numberOfYearsInLicensureCustomField = $('#numberOfYearsInLicensureCustomField').val() || 0;
		if (earlyMemberCustomField > 0 && numberOfYearsInLicensureCustomField > 0) {
			$('input.rolldate').each(function() {
				var mcrdfld = $(this).attr('name');
				if($('#roll_'+mcrdfld+'_afid').val() > 0 && $(this).val() == '') {
					arrReq[arrReq.length] = "To automatically advance dates, choose a valid " + $(this).attr('mcrdtxt')+ ".";
				}
			});
			if ($('#roll_adv').val().length == 0) arrReq[arrReq.length] = 'To automatically advance dates, choose a valid date of advancement.';
			if ($('#roll_adv_afid').val() == 0) arrReq[arrReq.length] = 'To automatically advance dates, choose an advance formula for the date of advancement.';
		}
		return arrReq;
	}
	function setDefaultRollDataValue(){
		if(rollingDataDefaultValue.runDateAdvanceDateAFID != undefined && rollingDataDefaultValue.runDateAdvanceDateAFID != '' && rollingDataDefaultValue.runDateAdvanceDateAFID != null){
			$('#roll_runDate_afid').val(rollingDataDefaultValue.runDateAdvanceDateAFID);
		}
		if(rollingDataDefaultValue.advanceDate != undefined && rollingDataDefaultValue.advanceDate != '' && rollingDataDefaultValue.advanceDate != null){
			$('#roll_adv').val(rollingDataDefaultValue.advanceDate);
		}
		if(rollingDataDefaultValue.advanceDateAFID != undefined && rollingDataDefaultValue.advanceDateAFID != '' && rollingDataDefaultValue.advanceDateAFID != null){
			$('#roll_adv_afid').val(rollingDataDefaultValue.advanceDateAFID);
		}
	}
	function onChangeEarlyCustomField(cID) {
		if(cID == ''){
			$('#numberOfYearsInLicensureCustomField').val('').trigger('change');
			clearRollRunDate();
			
			$('#roll_runDate_afid').val(0);
			clearRollADVDate();
			$('#roll_adv_afid').val(0);
			
			$('#divCustomF').addClass('d-none');
		} else {
			$('#divCustomF').removeClass('d-none');
		}
	}
	function onChangeNOYCustomField(cID) {
		if(cID == ''){
			clearRollRunDate();
			$('#roll_runDate_afid').val(0);
			clearRollADVDate();
			$('#roll_adv_afid').val(0);
			$('#divCustomCompareD').addClass('d-none'); 
			$('#rolldateYes').addClass('d-none');
		}
		else {
			$('#divCustomCompareD').removeClass('d-none'); 
			$('#rolldateYes').removeClass('d-none');
		}
	}
	function quickAddUSStates() {
		var msg = '<div class="alert alert-warning">This will create a professional license type for each of the 50 U.S. States plus the District of Columbia, unless they have already been created.<br/><b>Are you sure you want to proceed?</b></div>';
		MCModalUtils.showModal({
			isslideout: false,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'md',
			title: 'Add US States as License Types',
			iframe: false,
			strmodalbody: {
				content: msg
			},
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: 'doQuickAddUSStates',
				extrabuttonlabel: 'Proceed'
			}
		});
	}
	function doQuickAddUSStates() {
		var addResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				MCModalUtils.hideModal();
				getLicenseTypes();
			} else {
				MCModalUtils.hideModal();
				getLicenseTypes();
				alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to add the US States as license types.');
			}
		};
		$('#btnMCModalSave').html('Please wait...').prop('disabled', true);
		TS_AJX('ADMINORG', 'addUSStatesAsLicenseTypes', {}, addResult, addResult, 30000, addResult);
	}
	$(function(){
		getLicenseTypes();
		getLicenseStatuses();
		mca_setupSelect2();
		mca_setupDatePickerField('runDate');
		mca_setupCalendarIcons('frmLicense');
		<cfoutput>
		rollingDataDefaultValue = {
			"runDateAdvanceDateAFID" : "#local.qryOrgEarlyLicenseSettings.runDateAdvanceDateAFID#",
			"advanceDate" : "#DateFormat(local.qryOrgEarlyLicenseSettings.advanceDate,"m/d/yyyy")#",
			"advanceDateAFID" : "#local.qryOrgEarlyLicenseSettings.advanceDateAFID#"
		};
		setTimeout(function(){
			<cfif len(local.qryOrgEarlyLicenseSettings.columnID)>
				$('##divCustomF').removeClass('d-none');
				$('##numberOfYearsInLicensureCustomField').val('').trigger('change');
				<cfif len(local.qryOrgEarlyLicenseSettings.numberOfYearsInLicensureCFColumnID)>
					$('##divCustomCompareD').removeClass('d-none'); 
					rollDateRdo(1);
					$('##roll_runDate_afid').trigger('change');
					$('##roll_adv_afid').trigger('change');
				</cfif>
			</cfif>
		},1000);	
		if ($('input.rolldate').length > 0) {
			$('input.rolldate').change( function(e) {
				updateAFExample($(this).attr('name'),'roll_' + $(this).attr('name') + '_afid','divroll_' + $(this).attr('name') + '_afid');
			});
			writeRollDates();
		}
		</cfoutput>	
	});
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.licenseJS)#">

<cfoutput>
<form name="frmLicense" id="frmLicense">
<div class="card card-box">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Professional License Types</div>
		<div class="d-flex align-items-center invisible" id="divLicenseTypesLoading">
			<span class="spinner-border spinner-border-sm"></span><span class="ml-1">Saving...</span>
		</div>
	</div>
	<div class="card-body p-3">
		<table class="table table-sm table-borderless" id="licenseTypesTable">
		<thead>
			<tr>
				<th>&nbsp;</th>
				<th>License Type</th>
				<th class="text-center">Alert on Duplicates</th>
				<th class="text-center" colspan="3">Tools</th>
			</tr>
		</thead>
		<tbody id="mc_orgLicenseTypes">
		</tbody>
		<tbody id="newprolicensetypelink" class="d-none">
			<tr>
				<td></td>
				<td class="pt-2" colspan="4">
					<a href="javascript:addNewProLicenseType();">Add another professional license type</a><br/>
					<a href="javascript:quickAddUSStates();" class="mt-1 d-inline-block">Quick Add all 50 US States + DC</a>
				</td>
			</tr>
		</tbody>
		</table>
	</div>
</div>
<div class="card card-box mt-2">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Professional License Statuses</div>
		<div class="d-flex align-items-center invisible" id="divLicenseStatusesLoading">
			<span class="spinner-border spinner-border-sm"></span><span class="ml-1">Please wait</span>
		</div>
	</div>
	<div class="card-body p-3">
		<table class="table table-sm table-borderless"  id="licenseStatusesTable">
		<thead>
			<tr>
				<th>&nbsp;</th>
				<th>License Status</th>
				<th class="text-center" colspan="3">Tools</th>
			</tr>
		</thead>
		<tbody id="mc_orgLicenseStatuses">
		</tbody>
		<tbody id="newprolicensestatuslink" class="d-none">
			<tr>
				<td></td>
				<td class="pt-2" colspan="4"><a href="javascript:addNewProLicenseStatus();">Add another professional license status</a></td>
			</tr>
		</tbody>
		</table>
	</div>
</div>
<div class="card card-box mt-2">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Determining Earliest License Date</div>
	</div>
	<div class="card-body p-3">
		<div class="mb-3">We can automatically update one of your member custom fields with the Earliest License Date amongst the selected license types and statuses below.</div>
		<div id="err_earliestdate" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-group row">
			<label for="earlyMemberCustomField" class="col-sm-5 col-form-label-sm font-size-md">Custom Field to Update</label>
			<div class="col-sm-7">
				<select name="earlyMemberCustomField" id="earlyMemberCustomField" class="form-control form-control-sm" data-toggle="custom-select2" data-allowclear="true" placeholder="Earliest License Date Not Activated" onchange="onChangeEarlyCustomField(this.value);">
					<option value="">-- Earliest License Date Not Activated -- </option>
					<cfloop query="local.qryMemberDataDateCustomFields">
						<option value="#local.qryMemberDataDateCustomFields.columnID#" <cfif local.qryOrgEarlyLicenseSettings.columnID eq local.qryMemberDataDateCustomFields.columnID>selected="selected"</cfif>>#local.qryMemberDataDateCustomFields.columnName#</option>
					</cfloop>
				</select>
			</div>
		</div>
		<div class="form-group row">
			<label for="earlyLicenseTypes" class="col-sm-5 col-form-label-sm font-size-md">License Types</label>
			<div class="col-sm-7">
				<select name="earlyLicenseTypes" id="earlyLicenseTypes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Any License Type">
					<cfloop query="local.qryOrgProLicenses">
						<option value="#local.qryOrgProLicenses.PLTypeID#" <cfif ListFindNoCase(valueList(local.earlyProfLicenseTypes.PLTypeID),local.qryOrgProLicenses.PLTypeID)>selected="selected"</cfif>>#local.qryOrgProLicenses.PLName#</option>
					</cfloop>
				</select>
				<input type="hidden" name="originalEarlyLicenseTypes" id="originalEarlyLicenseTypes" value="#valueList(local.earlyProfLicenseTypes.PLTypeID)#">
			</div>
		</div>
		<div class="form-group row">
			<label for="earlyLicenseStatuses" class="col-sm-5 col-form-label-sm font-size-md">License Statuses</label>
			<div class="col-sm-7">
				<select name="earlyLicenseStatuses" id="earlyLicenseStatuses" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Any License Status">
					<cfloop query="local.qryOrgProLicenseStatuses">
						<option value="#local.qryOrgProLicenseStatuses.PLStatusID#" <cfif ListFindNoCase(valueList(local.earlyProfLicenseStatuses.PLStatusID),local.qryOrgProLicenseStatuses.PLStatusID)>selected="selected"</cfif>>#local.qryOrgProLicenseStatuses.StatusName#</option>
					</cfloop>
				</select>
				<input type="hidden" name="originalEarlyLicenseStatuses" id="originalEarlyLicenseStatuses" value="#valueList(local.earlyProfLicenseStatuses.PLStatusID)#">
			</div>
		</div>
		<div id="divCustomF" class="d-none">
			<div class="my-3">Additionally, we can automatically update a separate member custom field with the number of years in licensure, based on the Earliest License Date set above.</div>
			<div class="form-group row">
				<label for="numberOfYearsInLicensureCustomField" class="col-sm-5 col-form-label-sm font-size-md">Custom Field to Update with Number of Years in Licensure</label>
				<div class="col-sm-7">
					<select name="numberOfYearsInLicensureCustomField" id="numberOfYearsInLicensureCustomField" class="form-control form-control-sm" data-toggle="custom-select2" data-allowclear="true" placeholder="Don't Update with Number of Years in Licensure" onchange="onChangeNOYCustomField(this.value);">
						<option value="">--  Number of Years in Licensure Not Updated-- </option>
						<cfloop query="local.qryMemberDataNumberCustomFields">
							<option value="#local.qryMemberDataNumberCustomFields.columnID#" <cfif local.qryOrgEarlyLicenseSettings.numberOfYearsInLicensureCFColumnID eq local.qryMemberDataNumberCustomFields.columnID>selected="selected"</cfif>>#local.qryMemberDataNumberCustomFields.columnName#</option>
						</cfloop>
					</select>
				</div>
			</div>
		</div>	
		<div id="divCustomCompareD" class="d-none">
			<div class="form-group row">
				<label for="runDate" class="col-sm-5 col-form-label-sm font-size-md">What date do you want to compare it to?</label>
				<div class="col-sm-7">
					<div class="input-group input-group-sm">
						<input type="text" name="runDate" id="runDate" value="#DateFormat(local.qryOrgEarlyLicenseSettings.runDate,"m/d/yyyy")#" class="form-control dateControl rolldate" mcrdtxt="Run Date" autocomplete="off">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="runDate"><i class="fa-solid fa-calendar"></i></span>
							<span class="input-group-text"><a href="javascript:clearRollRunDate();"><i class="fa-solid fa-circle-xmark"></i></a></span>
						</div>			
					</div>
				</div>
			</div>
		</div>
		<div id="divStepRollDates">
			
			<div id="rolldateYes" class="d-none"></div>
		</div>
		<div class="row mt-3">
			<div class="col text-right">
				<span class="saveEarliestLicenseDateCalcFieldsInfo mr-2"></span>
				<button type="button" id="btnSaveEarliestLicenseDateCalcFields" name="btnSaveEarliestLicenseDateCalcFields" class="btn btn-sm btn-primary" onclick="saveEarliestLicenseDateCalcFields();">Save Changes</button>
			</div>
		</div>
	</div>
</div>
<div class="card card-box mt-2">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Labels</div>
	</div>
	<div class="card-body p-3">
		<div id="err_licensetypelabels" class="alert alert-danger mb-2 d-none"></div>
		<div class="mb-3">Each Professional License has a status, a license number, and a date. Change how those are labeled:</div>
		<div class="form-group row">
			<label for="profLicenseStatusLabel" class="col-sm-3 col-form-label-sm font-size-md">Status</label>
			<div class="col-sm-9">
				<input type="text" name="profLicenseStatusLabel" id="profLicenseStatusLabel" class="form-control form-control-sm" value="#local.qryOrganization.profLicenseStatusLabel#" maxlength="100">
			</div>
		</div>
		<div class="form-group row">
			<label for="profLicenseNumberLabel" class="col-sm-3 col-form-label-sm font-size-md">License Number</label>
			<div class="col-sm-9">
				<input type="text" name="profLicenseNumberLabel" id="profLicenseNumberLabel" class="form-control form-control-sm" value="#local.qryOrganization.profLicenseNumberLabel#" maxlength="100">
			</div>
		</div>
		<div class="form-group row">
			<label for="profLicenseDateLabel" class="col-sm-3 col-form-label-sm font-size-md">Date</label>
			<div class="col-sm-9">
				<input type="text" name="profLicenseDateLabel" id="profLicenseDateLabel" class="form-control form-control-sm" value="#local.qryOrganization.profLicenseDateLabel#" maxlength="100">
			</div>
		</div>
		<div class="row mt-3">
			<div class="col text-right">
				<span class="saveLabelsInfo mr-2"></span>
				<button type="button" id="btnSaveLabels" name="btnSaveLabels" class="btn btn-sm btn-primary" onclick="saveLicenseTypeLabels();">Save Changes</button>
			</div>
		</div>
	</div>
</div>
</form>

<script id="mc_orgLicenseTypesTemplate" type="text/x-handlebars-template">
{{##each arrlicensetypes}}
	<tr id="mc_licenseTypeRow_{{pltypeid}}" data-pltypeid="{{pltypeid}}"{{##compare pltypeid '!=' "x"}} class="actionRow"{{/compare}}>
		<td class="text-right pr-2" width="60">{{##compare pltypeid '!=' "x"}}<small>{{pltypeid}}</small>{{/compare}}</td>
		<td class="d-flex">
			<input type="text" id="proLicenseType_{{pltypeid}}_" name="proLicenseType_{{pltypeid}}_" value="{{plname}}" class="form-control form-control-sm" style="min-width:200px;" maxlength="114" onBlur="checkLicenseTypeName({{pltypeid}})">
			<button type="button" id="btnSaveLicenseType_{{pltypeid}}" class="btn btn-xs btn-secondary ml-2"
				{{##compare pltypeid '!=' "x"}}onclick="updateLicenseType({{pltypeid}});" title="Save"{{/compare}}
				{{##compare pltypeid '==' "x"}}onclick="insertLicenseType('{{pltypeid}}');" title="Add"{{/compare}}
				>
				<span class="btn-wrapper--icon">
					<i class="fa fa-floppy-disk"></i>
				</span>
			</button>
		</td>
		<td class="text-center">
			{{##compare pltypeid '!=' "x"}}
				<input type="checkbox" name="proLicenseDuplicate_{{pltypeid}}_" id="proLicenseDuplicate_{{pltypeid}}_" value="1" onclick="updateLicenseTypeDuplicate({{pltypeid}});" {{##compare alertduplicates '==' "1"}}checked="checked"{{/compare}}>
			{{/compare}}
		</td>
		<td width="30">
			{{##compare pltypeid '!=' "x"}}
				<i class="fa-solid fa-circle-arrow-up cursor-pointer text-primary moveuplink{{##compare @index '==' "0"}} invisible{{/compare}}" onclick="moveProLicenseType({{pltypeid}},'up');" title="Move Professional License Type Up"></i>
			{{/compare}}
		</td>
		<td width="30">
			{{##compare pltypeid '!=' "x"}}
				<i class="fa-solid fa-circle-arrow-down cursor-pointer text-primary movedownlink{{##compare (incremented @index) '>=' ../arrlicensetypes.length}} invisible{{/compare}}" onclick="moveProLicenseType({{pltypeid}},'down');" title="Move Professional License Type Down"></i>
			{{/compare}}
		</td>
		<td width="30">
			<i class="fa-solid fa-circle-minus cursor-pointer text-darkred" onclick="removeProLicenseType('{{pltypeid}}');" title="Remove Professional License Type"></i>
		</td>
	</tr>
{{/each}}
</script>

<script id="mc_orgLicenseStatusesTemplate" type="text/x-handlebars-template">
{{##each arrlicensestatuses}}
	<tr id="mc_licenseStatusRow_{{plstatusid}}" data-plstatusid="{{plstatusid}}"{{##compare plstatusid '!=' "x"}} class="actionRow"{{/compare}}>
		<td class="text-right pr-2" width="60">{{##compare plstatusid '!=' "x"}}<small>{{plstatusid}}</small>{{/compare}}</td>
		<td class="d-flex pr-3">
			<input type="text" id="proLicenseStatus_{{plstatusid}}_" name="proLicenseStatus_{{plstatusid}}_" value="{{statusname}}" class="form-control form-control-sm" style="min-width:200px;" maxlength="200">
			<button type="button" id="btnSaveLicenseStatus_{{plstatusid}}" class="btn btn-xs btn-secondary ml-2"
				{{##compare plstatusid '!=' "x"}}onclick="updateLicenseStatus({{plstatusid}});" title="Save"{{/compare}}
				{{##compare plstatusid '==' "x"}}onclick="insertLicenseStatus('{{plstatusid}}');" title="Add"{{/compare}}
				>
				<span class="btn-wrapper--icon">
					<i class="fa fa-floppy-disk"></i>
				</span>
			</button>
		</td>
		<td width="30">
			{{##compare plstatusid '!=' "x"}}
				<i class="fa-solid fa-circle-arrow-up cursor-pointer text-primary moveuplink{{##compare @index '==' "0"}} invisible{{/compare}}" onclick="moveProLicenseStatus({{plstatusid}},'up');" title="Move Professional License Status Up"></i>
			{{/compare}}
		</td>
		<td width="30">
			{{##compare plstatusid '!=' "x"}}
				<i class="fa-solid fa-circle-arrow-down cursor-pointer text-primary movedownlink{{##compare (incremented @index) '>=' ../arrlicensestatuses.length}} invisible{{/compare}}" onclick="moveProLicenseStatus({{plstatusid}},'down');" title="Move Professional License Status Down"></i>
			{{/compare}}
		</td>
		<td width="30">
			<i class="fa-solid fa-circle-minus cursor-pointer text-darkred" onclick="removeProLicenseStatus('{{plstatusid}}');" title="Remove Professional License Status"></i>
		</td>
	</tr>
{{/each}}
</script>
</cfoutput>