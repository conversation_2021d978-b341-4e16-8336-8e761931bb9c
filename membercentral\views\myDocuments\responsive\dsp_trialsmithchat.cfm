<cfoutput>
<style>
##trialsmith-chat-container{
    border: 1px solid ##ccc;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
trialsmith-chat {
    display: flex;
    border: 1px solid ##ccc;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
</style>
<link rel="stylesheet" href="/sitecomponents/COMMON/webcomponents/trialsmith-document-chat/trialsmith-documentchat-ui-component.css">
<script src="/sitecomponents/COMMON/webcomponents/trialsmith-document-chat/trialsmith-chat-component.umd.js"></script>
<h1>
    Document Chat<br>
    <small><a href="/?pg=myDocuments&tab=PTSAI">Back to AI Research Projects</a></small>
</h1>

<div id="trialsmith-chat-container">
    <trialsmith-chat 
        api-base-url="/trialsmithdocumentchatapi" 
        demo-user-id="sample-user-123"
        webviewer-lib-path="/sitecomponents/COMMON/javascript/webviewer/11.5.0/" 
    >
    </trialsmith-chat>
</div>
</cfoutput>