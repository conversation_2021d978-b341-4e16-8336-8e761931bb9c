(function () {
	/*
 @license DOMPurify 3.2.5 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE */
	var $jscomp = $jscomp || {};
	$jscomp.scope = {};
	$jscomp.arrayIteratorImpl = function (v) {
		var A = 0;
		return function () {
			return A < v.length ? { done: !1, value: v[A++] } : { done: !0 };
		};
	};
	$jscomp.arrayIterator = function (v) {
		return { next: $jscomp.arrayIteratorImpl(v) };
	};
	$jscomp.ASSUME_ES5 = !1;
	$jscomp.ASSUME_NO_NATIVE_MAP = !1;
	$jscomp.ASSUME_NO_NATIVE_SET = !1;
	$jscomp.SIMPLE_FROUND_POLYFILL = !1;
	$jscomp.ISOLATE_POLYFILLS = !1;
	$jscomp.FORCE_POLYFILL_PROMISE = !1;
	$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION = !1;
	$jscomp.defineProperty =
		$jscomp.ASSUME_ES5 || "function" == typeof Object.defineProperties
			? Object.defineProperty
			: function (v, A, u) {
					if (v == Array.prototype || v == Object.prototype) return v;
					v[A] = u.value;
					return v;
				};
	$jscomp.getGlobal = function (v) {
		v = [
			"object" == typeof globalThis && globalThis,
			v,
			"object" == typeof window && window,
			"object" == typeof self && self,
			"object" == typeof global && global,
		];
		for (var A = 0; A < v.length; ++A) {
			var u = v[A];
			if (u && u.Math == Math) return u;
		}
		throw Error("Cannot find global object");
	};
	$jscomp.global = $jscomp.getGlobal(this);
	$jscomp.IS_SYMBOL_NATIVE =
		"function" === typeof Symbol && "symbol" === typeof Symbol("x");
	$jscomp.TRUST_ES6_POLYFILLS =
		!$jscomp.ISOLATE_POLYFILLS || $jscomp.IS_SYMBOL_NATIVE;
	$jscomp.polyfills = {};
	$jscomp.propertyToPolyfillSymbol = {};
	$jscomp.POLYFILL_PREFIX = "$jscp$";
	var $jscomp$lookupPolyfilledValue = function (v, A, u) {
		if (!u || null != v) {
			u = $jscomp.propertyToPolyfillSymbol[A];
			if (null == u) return v[A];
			u = v[u];
			return void 0 !== u ? u : v[A];
		}
	};
	$jscomp.polyfill = function (v, A, u, p) {
		A &&
			($jscomp.ISOLATE_POLYFILLS
				? $jscomp.polyfillIsolated(v, A, u, p)
				: $jscomp.polyfillUnisolated(v, A, u, p));
	};
	$jscomp.polyfillUnisolated = function (v, A, u, p) {
		u = $jscomp.global;
		v = v.split(".");
		for (p = 0; p < v.length - 1; p++) {
			var m = v[p];
			if (!(m in u)) return;
			u = u[m];
		}
		v = v[v.length - 1];
		p = u[v];
		A = A(p);
		A != p &&
			null != A &&
			$jscomp.defineProperty(u, v, {
				configurable: !0,
				writable: !0,
				value: A,
			});
	};
	$jscomp.polyfillIsolated = function (v, A, u, p) {
		var m = v.split(".");
		v = 1 === m.length;
		p = m[0];
		p = !v && p in $jscomp.polyfills ? $jscomp.polyfills : $jscomp.global;
		for (var x = 0; x < m.length - 1; x++) {
			var k = m[x];
			if (!(k in p)) return;
			p = p[k];
		}
		m = m[m.length - 1];
		u = $jscomp.IS_SYMBOL_NATIVE && "es6" === u ? p[m] : null;
		A = A(u);
		null != A &&
			(v
				? $jscomp.defineProperty($jscomp.polyfills, m, {
						configurable: !0,
						writable: !0,
						value: A,
					})
				: A !== u &&
					(void 0 === $jscomp.propertyToPolyfillSymbol[m] &&
						((u = (1e9 * Math.random()) >>> 0),
						($jscomp.propertyToPolyfillSymbol[m] = $jscomp.IS_SYMBOL_NATIVE
							? $jscomp.global.Symbol(m)
							: $jscomp.POLYFILL_PREFIX + u + "$" + m)),
					$jscomp.defineProperty(p, $jscomp.propertyToPolyfillSymbol[m], {
						configurable: !0,
						writable: !0,
						value: A,
					})));
	};
	$jscomp.initSymbol = function () {};
	$jscomp.polyfill(
		"Symbol",
		function (v) {
			if (v) return v;
			var A = function (x, k) {
				this.$jscomp$symbol$id_ = x;
				$jscomp.defineProperty(this, "description", {
					configurable: !0,
					writable: !0,
					value: k,
				});
			};
			A.prototype.toString = function () {
				return this.$jscomp$symbol$id_;
			};
			var u = "jscomp_symbol_" + ((1e9 * Math.random()) >>> 0) + "_",
				p = 0,
				m = function (x) {
					if (this instanceof m)
						throw new TypeError("Symbol is not a constructor");
					return new A(u + (x || "") + "_" + p++, x);
				};
			return m;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Symbol.iterator",
		function (v) {
			if (v) return v;
			v = Symbol("Symbol.iterator");
			for (
				var A =
						"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(
							" ",
						),
					u = 0;
				u < A.length;
				u++
			) {
				var p = $jscomp.global[A[u]];
				"function" === typeof p &&
					"function" != typeof p.prototype[v] &&
					$jscomp.defineProperty(p.prototype, v, {
						configurable: !0,
						writable: !0,
						value: function () {
							return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this));
						},
					});
			}
			return v;
		},
		"es6",
		"es3",
	);
	$jscomp.iteratorPrototype = function (v) {
		v = { next: v };
		v[Symbol.iterator] = function () {
			return this;
		};
		return v;
	};
	$jscomp.makeIterator = function (v) {
		var A =
			"undefined" != typeof Symbol && Symbol.iterator && v[Symbol.iterator];
		if (A) return A.call(v);
		if ("number" == typeof v.length) return $jscomp.arrayIterator(v);
		throw Error(String(v) + " is not an iterable or ArrayLike");
	};
	$jscomp.arrayFromIterator = function (v) {
		for (var A, u = []; !(A = v.next()).done; ) u.push(A.value);
		return u;
	};
	$jscomp.arrayFromIterable = function (v) {
		return v instanceof Array
			? v
			: $jscomp.arrayFromIterator($jscomp.makeIterator(v));
	};
	$jscomp.checkEs6ConformanceViaProxy = function () {
		try {
			var v = {},
				A = Object.create(
					new $jscomp.global.Proxy(v, {
						get: function (u, p, m) {
							return u == v && "q" == p && m == A;
						},
					}),
				);
			return !0 === A.q;
		} catch (u) {
			return !1;
		}
	};
	$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS = !1;
	$jscomp.ES6_CONFORMANCE =
		$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS &&
		$jscomp.checkEs6ConformanceViaProxy();
	$jscomp.owns = function (v, A) {
		return Object.prototype.hasOwnProperty.call(v, A);
	};
	$jscomp.polyfill(
		"WeakMap",
		function (v) {
			function A() {
				if (!v || !Object.seal) return !1;
				try {
					var n = Object.seal({}),
						f = Object.seal({}),
						h = new v([
							[n, 2],
							[f, 3],
						]);
					if (2 != h.get(n) || 3 != h.get(f)) return !1;
					h.delete(n);
					h.set(f, 4);
					return !h.has(n) && 4 == h.get(f);
				} catch (g) {
					return !1;
				}
			}
			function u() {}
			function p(n) {
				var f = typeof n;
				return ("object" === f && null !== n) || "function" === f;
			}
			function m(n) {
				if (!$jscomp.owns(n, k)) {
					var f = new u();
					$jscomp.defineProperty(n, k, { value: f });
				}
			}
			function x(n) {
				if (!$jscomp.ISOLATE_POLYFILLS) {
					var f = Object[n];
					f &&
						(Object[n] = function (h) {
							if (h instanceof u) return h;
							Object.isExtensible(h) && m(h);
							return f(h);
						});
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (v && $jscomp.ES6_CONFORMANCE) return v;
			} else if (A()) return v;
			var k = "$jscomp_hidden_" + Math.random();
			x("freeze");
			x("preventExtensions");
			x("seal");
			var q = 0,
				c = function (n) {
					this.id_ = (q += Math.random() + 1).toString();
					if (n) {
						n = $jscomp.makeIterator(n);
						for (var f; !(f = n.next()).done; )
							(f = f.value), this.set(f[0], f[1]);
					}
				};
			c.prototype.set = function (n, f) {
				if (!p(n)) throw Error("Invalid WeakMap key");
				m(n);
				if (!$jscomp.owns(n, k)) throw Error("WeakMap key fail: " + n);
				n[k][this.id_] = f;
				return this;
			};
			c.prototype.get = function (n) {
				return p(n) && $jscomp.owns(n, k) ? n[k][this.id_] : void 0;
			};
			c.prototype.has = function (n) {
				return p(n) && $jscomp.owns(n, k) && $jscomp.owns(n[k], this.id_);
			};
			c.prototype.delete = function (n) {
				return p(n) && $jscomp.owns(n, k) && $jscomp.owns(n[k], this.id_)
					? delete n[k][this.id_]
					: !1;
			};
			return c;
		},
		"es6",
		"es3",
	);
	$jscomp.MapEntry = function () {};
	$jscomp.polyfill(
		"Map",
		function (v) {
			function A() {
				if (
					$jscomp.ASSUME_NO_NATIVE_MAP ||
					!v ||
					"function" != typeof v ||
					!v.prototype.entries ||
					"function" != typeof Object.seal
				)
					return !1;
				try {
					var c = Object.seal({ x: 4 }),
						n = new v($jscomp.makeIterator([[c, "s"]]));
					if (
						"s" != n.get(c) ||
						1 != n.size ||
						n.get({ x: 4 }) ||
						n.set({ x: 4 }, "t") != n ||
						2 != n.size
					)
						return !1;
					var f = n.entries(),
						h = f.next();
					if (h.done || h.value[0] != c || "s" != h.value[1]) return !1;
					h = f.next();
					return h.done ||
						4 != h.value[0].x ||
						"t" != h.value[1] ||
						!f.next().done
						? !1
						: !0;
				} catch (g) {
					return !1;
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (v && $jscomp.ES6_CONFORMANCE) return v;
			} else if (A()) return v;
			var u = new WeakMap(),
				p = function (c) {
					this.data_ = {};
					this.head_ = k();
					this.size = 0;
					if (c) {
						c = $jscomp.makeIterator(c);
						for (var n; !(n = c.next()).done; )
							(n = n.value), this.set(n[0], n[1]);
					}
				};
			p.prototype.set = function (c, n) {
				c = 0 === c ? 0 : c;
				var f = m(this, c);
				f.list || (f.list = this.data_[f.id] = []);
				f.entry
					? (f.entry.value = n)
					: ((f.entry = {
							next: this.head_,
							previous: this.head_.previous,
							head: this.head_,
							key: c,
							value: n,
						}),
						f.list.push(f.entry),
						(this.head_.previous.next = f.entry),
						(this.head_.previous = f.entry),
						this.size++);
				return this;
			};
			p.prototype.delete = function (c) {
				c = m(this, c);
				return c.entry && c.list
					? (c.list.splice(c.index, 1),
						c.list.length || delete this.data_[c.id],
						(c.entry.previous.next = c.entry.next),
						(c.entry.next.previous = c.entry.previous),
						(c.entry.head = null),
						this.size--,
						!0)
					: !1;
			};
			p.prototype.clear = function () {
				this.data_ = {};
				this.head_ = this.head_.previous = k();
				this.size = 0;
			};
			p.prototype.has = function (c) {
				return !!m(this, c).entry;
			};
			p.prototype.get = function (c) {
				return (c = m(this, c).entry) && c.value;
			};
			p.prototype.entries = function () {
				return x(this, function (c) {
					return [c.key, c.value];
				});
			};
			p.prototype.keys = function () {
				return x(this, function (c) {
					return c.key;
				});
			};
			p.prototype.values = function () {
				return x(this, function (c) {
					return c.value;
				});
			};
			p.prototype.forEach = function (c, n) {
				for (var f = this.entries(), h; !(h = f.next()).done; )
					(h = h.value), c.call(n, h[1], h[0], this);
			};
			p.prototype[Symbol.iterator] = p.prototype.entries;
			var m = function (c, n) {
					var f = n && typeof n;
					"object" == f || "function" == f
						? u.has(n)
							? (f = u.get(n))
							: ((f = "" + ++q), u.set(n, f))
						: (f = "p_" + n);
					var h = c.data_[f];
					if (h && $jscomp.owns(c.data_, f))
						for (c = 0; c < h.length; c++) {
							var g = h[c];
							if ((n !== n && g.key !== g.key) || n === g.key)
								return { id: f, list: h, index: c, entry: g };
						}
					return { id: f, list: h, index: -1, entry: void 0 };
				},
				x = function (c, n) {
					var f = c.head_;
					return $jscomp.iteratorPrototype(function () {
						if (f) {
							for (; f.head != c.head_; ) f = f.previous;
							for (; f.next != f.head; )
								return (f = f.next), { done: !1, value: n(f) };
							f = null;
						}
						return { done: !0, value: void 0 };
					});
				},
				k = function () {
					var c = {};
					return (c.previous = c.next = c.head = c);
				},
				q = 0;
			return p;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Promise",
		function (v) {
			function A() {
				this.batch_ = null;
			}
			function u(k) {
				return k instanceof m
					? k
					: new m(function (q, c) {
							q(k);
						});
			}
			if (
				v &&
				(!(
					$jscomp.FORCE_POLYFILL_PROMISE ||
					($jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION &&
						"undefined" === typeof $jscomp.global.PromiseRejectionEvent)
				) ||
					!$jscomp.global.Promise ||
					-1 === $jscomp.global.Promise.toString().indexOf("[native code]"))
			)
				return v;
			A.prototype.asyncExecute = function (k) {
				if (null == this.batch_) {
					this.batch_ = [];
					var q = this;
					this.asyncExecuteFunction(function () {
						q.executeBatch_();
					});
				}
				this.batch_.push(k);
			};
			var p = $jscomp.global.setTimeout;
			A.prototype.asyncExecuteFunction = function (k) {
				p(k, 0);
			};
			A.prototype.executeBatch_ = function () {
				for (; this.batch_ && this.batch_.length; ) {
					var k = this.batch_;
					this.batch_ = [];
					for (var q = 0; q < k.length; ++q) {
						var c = k[q];
						k[q] = null;
						try {
							c();
						} catch (n) {
							this.asyncThrow_(n);
						}
					}
				}
				this.batch_ = null;
			};
			A.prototype.asyncThrow_ = function (k) {
				this.asyncExecuteFunction(function () {
					throw k;
				});
			};
			var m = function (k) {
				this.state_ = 0;
				this.result_ = void 0;
				this.onSettledCallbacks_ = [];
				this.isRejectionHandled_ = !1;
				var q = this.createResolveAndReject_();
				try {
					k(q.resolve, q.reject);
				} catch (c) {
					q.reject(c);
				}
			};
			m.prototype.createResolveAndReject_ = function () {
				function k(n) {
					return function (f) {
						c || ((c = !0), n.call(q, f));
					};
				}
				var q = this,
					c = !1;
				return { resolve: k(this.resolveTo_), reject: k(this.reject_) };
			};
			m.prototype.resolveTo_ = function (k) {
				if (k === this)
					this.reject_(new TypeError("A Promise cannot resolve to itself"));
				else if (k instanceof m) this.settleSameAsPromise_(k);
				else {
					a: switch (typeof k) {
						case "object":
							var q = null != k;
							break a;
						case "function":
							q = !0;
							break a;
						default:
							q = !1;
					}
					q ? this.resolveToNonPromiseObj_(k) : this.fulfill_(k);
				}
			};
			m.prototype.resolveToNonPromiseObj_ = function (k) {
				var q = void 0;
				try {
					q = k.then;
				} catch (c) {
					this.reject_(c);
					return;
				}
				"function" == typeof q
					? this.settleSameAsThenable_(q, k)
					: this.fulfill_(k);
			};
			m.prototype.reject_ = function (k) {
				this.settle_(2, k);
			};
			m.prototype.fulfill_ = function (k) {
				this.settle_(1, k);
			};
			m.prototype.settle_ = function (k, q) {
				if (0 != this.state_)
					throw Error(
						"Cannot settle(" +
							k +
							", " +
							q +
							"): Promise already settled in state" +
							this.state_,
					);
				this.state_ = k;
				this.result_ = q;
				2 === this.state_ && this.scheduleUnhandledRejectionCheck_();
				this.executeOnSettledCallbacks_();
			};
			m.prototype.scheduleUnhandledRejectionCheck_ = function () {
				var k = this;
				p(function () {
					if (k.notifyUnhandledRejection_()) {
						var q = $jscomp.global.console;
						"undefined" !== typeof q && q.error(k.result_);
					}
				}, 1);
			};
			m.prototype.notifyUnhandledRejection_ = function () {
				if (this.isRejectionHandled_) return !1;
				var k = $jscomp.global.CustomEvent,
					q = $jscomp.global.Event,
					c = $jscomp.global.dispatchEvent;
				if ("undefined" === typeof c) return !0;
				"function" === typeof k
					? (k = new k("unhandledrejection", { cancelable: !0 }))
					: "function" === typeof q
						? (k = new q("unhandledrejection", { cancelable: !0 }))
						: ((k = $jscomp.global.document.createEvent("CustomEvent")),
							k.initCustomEvent("unhandledrejection", !1, !0, k));
				k.promise = this;
				k.reason = this.result_;
				return c(k);
			};
			m.prototype.executeOnSettledCallbacks_ = function () {
				if (null != this.onSettledCallbacks_) {
					for (var k = 0; k < this.onSettledCallbacks_.length; ++k)
						x.asyncExecute(this.onSettledCallbacks_[k]);
					this.onSettledCallbacks_ = null;
				}
			};
			var x = new A();
			m.prototype.settleSameAsPromise_ = function (k) {
				var q = this.createResolveAndReject_();
				k.callWhenSettled_(q.resolve, q.reject);
			};
			m.prototype.settleSameAsThenable_ = function (k, q) {
				var c = this.createResolveAndReject_();
				try {
					k.call(q, c.resolve, c.reject);
				} catch (n) {
					c.reject(n);
				}
			};
			m.prototype.then = function (k, q) {
				function c(g, t) {
					return "function" == typeof g
						? function (r) {
								try {
									n(g(r));
								} catch (l) {
									f(l);
								}
							}
						: t;
				}
				var n,
					f,
					h = new m(function (g, t) {
						n = g;
						f = t;
					});
				this.callWhenSettled_(c(k, n), c(q, f));
				return h;
			};
			m.prototype.catch = function (k) {
				return this.then(void 0, k);
			};
			m.prototype.callWhenSettled_ = function (k, q) {
				function c() {
					switch (n.state_) {
						case 1:
							k(n.result_);
							break;
						case 2:
							q(n.result_);
							break;
						default:
							throw Error("Unexpected state: " + n.state_);
					}
				}
				var n = this;
				null == this.onSettledCallbacks_
					? x.asyncExecute(c)
					: this.onSettledCallbacks_.push(c);
				this.isRejectionHandled_ = !0;
			};
			m.resolve = u;
			m.reject = function (k) {
				return new m(function (q, c) {
					c(k);
				});
			};
			m.race = function (k) {
				return new m(function (q, c) {
					for (
						var n = $jscomp.makeIterator(k), f = n.next();
						!f.done;
						f = n.next()
					)
						u(f.value).callWhenSettled_(q, c);
				});
			};
			m.all = function (k) {
				var q = $jscomp.makeIterator(k),
					c = q.next();
				return c.done
					? u([])
					: new m(function (n, f) {
							function h(r) {
								return function (l) {
									g[r] = l;
									t--;
									0 == t && n(g);
								};
							}
							var g = [],
								t = 0;
							do
								g.push(void 0),
									t++,
									u(c.value).callWhenSettled_(h(g.length - 1), f),
									(c = q.next());
							while (!c.done);
						});
			};
			return m;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Array.from",
		function (v) {
			return v
				? v
				: function (A, u, p) {
						u =
							null != u
								? u
								: function (q) {
										return q;
									};
						var m = [],
							x =
								"undefined" != typeof Symbol &&
								Symbol.iterator &&
								A[Symbol.iterator];
						if ("function" == typeof x) {
							A = x.call(A);
							for (var k = 0; !(x = A.next()).done; )
								m.push(u.call(p, x.value, k++));
						} else
							for (x = A.length, k = 0; k < x; k++) m.push(u.call(p, A[k], k));
						return m;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.checkStringArgs = function (v, A, u) {
		if (null == v)
			throw new TypeError(
				"The 'this' value for String.prototype." +
					u +
					" must not be null or undefined",
			);
		if (A instanceof RegExp)
			throw new TypeError(
				"First argument to String.prototype." +
					u +
					" must not be a regular expression",
			);
		return v + "";
	};
	$jscomp.polyfill(
		"String.prototype.endsWith",
		function (v) {
			return v
				? v
				: function (A, u) {
						var p = $jscomp.checkStringArgs(this, A, "endsWith");
						A += "";
						void 0 === u && (u = p.length);
						u = Math.max(0, Math.min(u | 0, p.length));
						for (var m = A.length; 0 < m && 0 < u; )
							if (p[--u] != A[--m]) return !1;
						return 0 >= m;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.underscoreProtoCanBeSet = function () {
		var v = { a: !0 },
			A = {};
		try {
			return (A.__proto__ = v), A.a;
		} catch (u) {}
		return !1;
	};
	$jscomp.setPrototypeOf =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.setPrototypeOf
			? Object.setPrototypeOf
			: $jscomp.underscoreProtoCanBeSet()
				? function (v, A) {
						v.__proto__ = A;
						if (v.__proto__ !== A)
							throw new TypeError(v + " is not extensible");
						return v;
					}
				: null;
	$jscomp.polyfill(
		"Object.setPrototypeOf",
		function (v) {
			return v || $jscomp.setPrototypeOf;
		},
		"es6",
		"es5",
	);
	$jscomp.assign =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.assign
			? Object.assign
			: function (v, A) {
					for (var u = 1; u < arguments.length; u++) {
						var p = arguments[u];
						if (p) for (var m in p) $jscomp.owns(p, m) && (v[m] = p[m]);
					}
					return v;
				};
	$jscomp.polyfill(
		"Object.assign",
		function (v) {
			return v || $jscomp.assign;
		},
		"es6",
		"es3",
	);
	$jscomp.findInternal = function (v, A, u) {
		v instanceof String && (v = String(v));
		for (var p = v.length, m = 0; m < p; m++) {
			var x = v[m];
			if (A.call(u, x, m, v)) return { i: m, v: x };
		}
		return { i: -1, v: void 0 };
	};
	$jscomp.polyfill(
		"Array.prototype.find",
		function (v) {
			return v
				? v
				: function (A, u) {
						return $jscomp.findInternal(this, A, u).v;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Set",
		function (v) {
			function A() {
				if (
					$jscomp.ASSUME_NO_NATIVE_SET ||
					!v ||
					"function" != typeof v ||
					!v.prototype.entries ||
					"function" != typeof Object.seal
				)
					return !1;
				try {
					var p = Object.seal({ x: 4 }),
						m = new v($jscomp.makeIterator([p]));
					if (
						!m.has(p) ||
						1 != m.size ||
						m.add(p) != m ||
						1 != m.size ||
						m.add({ x: 4 }) != m ||
						2 != m.size
					)
						return !1;
					var x = m.entries(),
						k = x.next();
					if (k.done || k.value[0] != p || k.value[1] != p) return !1;
					k = x.next();
					return k.done ||
						k.value[0] == p ||
						4 != k.value[0].x ||
						k.value[1] != k.value[0]
						? !1
						: x.next().done;
				} catch (q) {
					return !1;
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (v && $jscomp.ES6_CONFORMANCE) return v;
			} else if (A()) return v;
			var u = function (p) {
				this.map_ = new Map();
				if (p) {
					p = $jscomp.makeIterator(p);
					for (var m; !(m = p.next()).done; ) this.add(m.value);
				}
				this.size = this.map_.size;
			};
			u.prototype.add = function (p) {
				p = 0 === p ? 0 : p;
				this.map_.set(p, p);
				this.size = this.map_.size;
				return this;
			};
			u.prototype.delete = function (p) {
				p = this.map_.delete(p);
				this.size = this.map_.size;
				return p;
			};
			u.prototype.clear = function () {
				this.map_.clear();
				this.size = 0;
			};
			u.prototype.has = function (p) {
				return this.map_.has(p);
			};
			u.prototype.entries = function () {
				return this.map_.entries();
			};
			u.prototype.values = function () {
				return this.map_.values();
			};
			u.prototype.keys = u.prototype.values;
			u.prototype[Symbol.iterator] = u.prototype.values;
			u.prototype.forEach = function (p, m) {
				var x = this;
				this.map_.forEach(function (k) {
					return p.call(m, k, k, x);
				});
			};
			return u;
		},
		"es6",
		"es3",
	);
	$jscomp.iteratorFromArray = function (v, A) {
		v instanceof String && (v += "");
		var u = 0,
			p = !1,
			m = {
				next: function () {
					if (!p && u < v.length) {
						var x = u++;
						return { value: A(x, v[x]), done: !1 };
					}
					p = !0;
					return { done: !0, value: void 0 };
				},
			};
		m[Symbol.iterator] = function () {
			return m;
		};
		return m;
	};
	$jscomp.polyfill(
		"Array.prototype.keys",
		function (v) {
			return v
				? v
				: function () {
						return $jscomp.iteratorFromArray(this, function (A) {
							return A;
						});
					};
		},
		"es6",
		"es3",
	);
	(function (v) {
		function A(p) {
			if (u[p]) return u[p].exports;
			var m = (u[p] = { i: p, l: !1, exports: {} });
			v[p].call(m.exports, m, m.exports, A);
			m.l = !0;
			return m.exports;
		}
		var u = {};
		A.m = v;
		A.c = u;
		A.d = function (p, m, x) {
			A.o(p, m) || Object.defineProperty(p, m, { enumerable: !0, get: x });
		};
		A.r = function (p) {
			"undefined" !== typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(p, Symbol.toStringTag, { value: "Module" });
			Object.defineProperty(p, "__esModule", { value: !0 });
		};
		A.t = function (p, m) {
			m & 1 && (p = A(p));
			if (m & 8 || (m & 4 && "object" === typeof p && p && p.__esModule))
				return p;
			var x = Object.create(null);
			A.r(x);
			Object.defineProperty(x, "default", { enumerable: !0, value: p });
			if (m & 2 && "string" != typeof p)
				for (var k in p)
					A.d(
						x,
						k,
						function (q) {
							return p[q];
						}.bind(null, k),
					);
			return x;
		};
		A.n = function (p) {
			var m =
				p && p.__esModule
					? function () {
							return p["default"];
						}
					: function () {
							return p;
						};
			A.d(m, "a", m);
			return m;
		};
		A.o = function (p, m) {
			return Object.prototype.hasOwnProperty.call(p, m);
		};
		A.p = "/core/pdf/";
		return A((A.s = 21));
	})([
		function (v, A, u) {
			u.d(A, "d", function () {
				return x;
			});
			u.d(A, "e", function () {
				return m;
			});
			u.d(A, "c", function () {
				return k;
			});
			u.d(A, "a", function () {
				return q;
			});
			u.d(A, "b", function () {
				return c;
			});
			var p = u(2),
				m = function (n, f) {
					Object(p.a)("disableLogs") ||
						(f ? console.warn("".concat(n, ": ").concat(f)) : console.warn(n));
				},
				x = function (n, f) {
					Object(p.a)("disableLogs") ||
						(f ? console.log("".concat(n, ": ").concat(f)) : console.log(n));
				},
				k = function (n) {
					if (!Object(p.a)("disableLogs")) throw (console.error(n), Error(n));
				},
				q = function (n, f) {},
				c = function (n, f) {};
		},
		function (v, A, u) {
			u.d(A, "c", function () {
				return c;
			});
			u.d(A, "a", function () {
				return n;
			});
			u.d(A, "b", function () {
				return f;
			});
			u.d(A, "d", function () {
				return h;
			});
			var p = u(16),
				m = console.log,
				x = console.warn,
				k = console.error,
				q = function (g) {
					void 0 === g && (g = !0);
					g
						? ((console.log = function () {}),
							(console.warn = function () {}),
							(console.error = function () {}))
						: ((console.log = m), (console.warn = x), (console.error = k));
				},
				c = function () {
					var g = Object(p.a)(location.search);
					q("1" === g.disableLogs);
				},
				n = function (g) {
					g.on("disableLogs", function (t) {
						q(t.disabled);
					});
				},
				f = function (g, t) {
					return function () {};
				},
				h = function (g, t) {
					t ? console.warn("".concat(g, ": ").concat(t)) : console.warn(g);
				};
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return x;
			});
			u.d(A, "b", function () {
				return k;
			});
			var p = {},
				m = {
					flattenedResources: !1,
					CANVAS_CACHE_SIZE: void 0,
					maxPagesBefore: void 0,
					maxPagesAhead: void 0,
					disableLogs: !1,
					wvsQueryParameters: {},
					_trnDebugMode: !1,
					_logFiltersEnabled: null,
				},
				x = function (q) {
					return m[q];
				},
				k = function (q, c) {
					var n;
					m[q] = c;
					null === (n = p[q]) || void 0 === n
						? void 0
						: n.forEach(function (f) {
								f(c);
							});
				};
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return E;
			});
			u.d(A, "b", function () {
				return G;
			});
			var p = u(4),
				m = u(0),
				x = u(5),
				k = u(6),
				q = u(8),
				c = "undefined" === typeof window ? self : window,
				n = c.importScripts,
				f = !1,
				h = function (a, b) {
					f ||
						(n(Object(q.a)("".concat(c.basePath, "decode.min.js"))), (f = !0));
					a = Object(k.b)(a);
					a = self.BrotliDecode(a);
					return b ? a : Object(k.a)(a);
				},
				g = function (a, b) {
					return Object(p.b)(void 0, void 0, Promise, function () {
						var d;
						return Object(p.c)(this, function (e) {
							switch (e.label) {
								case 0:
									return f
										? [3, 2]
										: [
												4,
												Object(x.a)(
													"".concat(
														self.Core.getWorkerPath(),
														"external/decode.min.js",
													),
													"Failed to download decode.min.js",
													window,
												),
											];
								case 1:
									e.sent(), (f = !0), (e.label = 2);
								case 2:
									return (
										(d = self.BrotliDecode(Object(k.b)(a))),
										[2, b ? d : Object(k.a)(d)]
									);
							}
						});
					});
				};
			(function () {
				function a() {
					this.remainingDataArrays = [];
				}
				a.prototype.processRaw = function (b) {
					return b;
				};
				a.prototype.processBrotli = function (b) {
					this.remainingDataArrays.push(b);
					return null;
				};
				a.prototype.GetNextChunk = function (b) {
					this.decodeFunction ||
						(this.decodeFunction =
							0 === b[0] && 97 === b[1] && 115 === b[2] && 109 === b[3]
								? this.processRaw
								: this.processBrotli);
					return this.decodeFunction(b);
				};
				a.prototype.End = function () {
					if (this.remainingDataArrays.length) {
						for (var b = this.arrays, d = 0, e = 0; e < b.length; ++e)
							d += b[e].length;
						d = new Uint8Array(d);
						var w = 0;
						for (e = 0; e < b.length; ++e) {
							var B = b[e];
							d.set(B, w);
							w += B.length;
						}
						return h(d, !0);
					}
					return null;
				};
				return a;
			})();
			var t = function (a, b, d) {
					void 0 === b && (b = !0);
					void 0 === d && (d = !1);
					var e = new XMLHttpRequest();
					e.open("GET", a, b);
					a = d && e.overrideMimeType;
					e.responseType = a ? "text" : "arraybuffer";
					a && e.overrideMimeType("text/plain; charset=x-user-defined");
					return e;
				},
				r = function (a, b, d) {
					return new Promise(function (e, w) {
						var B = t(a, b, d);
						B.send();
						B.onload = function () {
							200 === this.status || 0 === this.status
								? e(B.response)
								: w(Error("Download Failed ".concat(a)));
						};
						B.onerror = function () {
							w(Error("Network error occurred ".concat(a)));
						};
					});
				},
				l = function (a, b) {
					var d = b.decompressFunction,
						e = b.shouldOutputArray,
						w = b.compressedMaximum,
						B = "undefined" !== typeof n ? Date.now() : null;
					try {
						var y = e ? z(a) : a.join("");
						Object(m.a)("worker", "Result length is ".concat(y.length));
						if (y.length < w) {
							var D = d(y, e);
							Object(m.e)(
								"There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.",
							);
							n &&
								Object(m.a)(
									"worker",
									"Decompressed length is ".concat(D.length),
								);
							y = D;
						} else e || (y = Object(k.a)(y));
						if (n) {
							var L = b.paths.join(", ");
							Object(m.a)(
								"worker",
								""
									.concat(L, " Decompression took ")
									.concat(Date.now() - B, " ms"),
							);
						}
						return y;
					} catch (N) {
						throw Error("Failed to decompress: ".concat(N));
					}
				},
				z = function (a) {
					a = a.reduce(function (b, d) {
						d = new Uint8Array(d);
						return b.concat(Array.from(d));
					}, []);
					return new Uint8Array(a);
				},
				F = function (a) {
					var b = !a.shouldOutputArray,
						d = a.paths,
						e = a.isAsync;
					e
						? (d = Promise.all(
								d.map(function (w) {
									return r(w, e, b);
								}),
							)
								.then(function (w) {
									return l(w, a);
								})
								.catch(function (w) {
									throw Error(
										"Failed to fetch or decompress files: ".concat(w.message),
									);
								}))
						: ((d = d.map(function (w) {
								var B = t(w, e, b);
								B.send();
								if (200 === B.status || 0 === B.status) return B.response;
								throw Error("Failed to load ".concat(w));
							})),
							(d = l(d, a)));
					return d;
				},
				E = function (a) {
					var b = a.lastIndexOf("/");
					-1 === b && (b = 0);
					var d = a.slice(b).replace(".", ".br.");
					n ||
						(d.endsWith(".js.mem")
							? (d = d.replace(".js.mem", ".mem"))
							: d.endsWith(".js") && (d = d.concat(".mem")));
					return a.slice(0, b) + d;
				},
				H = function (a) {
					return a.map(function (b) {
						return E(b);
					});
				},
				I = function (a, b) {
					b.decompressFunction = n ? h : g;
					b.paths = H(a);
					return F(b);
				},
				J = function (a, b, d, e) {
					return a.catch(function (w) {
						Object(m.e)(w);
						return e(b, d);
					});
				},
				G = function (a, b, d, e) {
					a = Array.isArray(a) ? a : [a];
					a: {
						var w = [I];
						b = { compressedMaximum: b, isAsync: d, shouldOutputArray: e };
						if (b.isAsync) {
							var B = w[0](a, b);
							for (d = 1; d < w.length; ++d) B = J(B, a, b, w[d]);
						} else {
							for (d = 0; d < w.length; d++) {
								e = w[d];
								try {
									B = e(a, b);
									break a;
								} catch (y) {
									Object(m.e)(y.message);
								}
							}
							throw Error("None of the worker files were able to load. ");
						}
					}
					return B;
				};
		},
		function (v, A, u) {
			function p(k, q, c, n) {
				function f(h) {
					return h instanceof c
						? h
						: new c(function (g) {
								g(h);
							});
				}
				return new (c || (c = Promise))(function (h, g) {
					function t(z) {
						try {
							l(n.next(z));
						} catch (F) {
							g(F);
						}
					}
					function r(z) {
						try {
							l(n["throw"](z));
						} catch (F) {
							g(F);
						}
					}
					function l(z) {
						z.done ? h(z.value) : f(z.value).then(t, r);
					}
					l((n = n.apply(k, q || [])).next());
				});
			}
			function m(k, q) {
				function c(l) {
					return function (z) {
						return n([l, z]);
					};
				}
				function n(l) {
					if (h) throw new TypeError("Generator is already executing.");
					for (; r && ((r = 0), l[0] && (f = 0)), f; )
						try {
							if (
								((h = 1),
								g &&
									(t =
										l[0] & 2
											? g["return"]
											: l[0]
												? g["throw"] || ((t = g["return"]) && t.call(g), 0)
												: g.next) &&
									!(t = t.call(g, l[1])).done)
							)
								return t;
							if (((g = 0), t)) l = [l[0] & 2, t.value];
							switch (l[0]) {
								case 0:
								case 1:
									t = l;
									break;
								case 4:
									return f.label++, { value: l[1], done: !1 };
								case 5:
									f.label++;
									g = l[1];
									l = [0];
									continue;
								case 7:
									l = f.ops.pop();
									f.trys.pop();
									continue;
								default:
									if (
										!((t = f.trys), (t = 0 < t.length && t[t.length - 1])) &&
										(6 === l[0] || 2 === l[0])
									) {
										f = 0;
										continue;
									}
									if (3 === l[0] && (!t || (l[1] > t[0] && l[1] < t[3])))
										f.label = l[1];
									else if (6 === l[0] && f.label < t[1])
										(f.label = t[1]), (t = l);
									else if (t && f.label < t[2]) (f.label = t[2]), f.ops.push(l);
									else {
										t[2] && f.ops.pop();
										f.trys.pop();
										continue;
									}
							}
							l = q.call(k, f);
						} catch (z) {
							(l = [6, z]), (g = 0);
						} finally {
							h = t = 0;
						}
					if (l[0] & 5) throw l[1];
					return { value: l[0] ? l[1] : void 0, done: !0 };
				}
				var f = {
						label: 0,
						sent: function () {
							if (t[0] & 1) throw t[1];
							return t[1];
						},
						trys: [],
						ops: [],
					},
					h,
					g,
					t,
					r = Object.create(
						("function" === typeof Iterator ? Iterator : Object).prototype,
					);
				return (
					(r.next = c(0)),
					(r["throw"] = c(1)),
					(r["return"] = c(2)),
					"function" === typeof Symbol &&
						(r[Symbol.iterator] = function () {
							return this;
						}),
					r
				);
			}
			u.d(A, "a", function () {
				return x;
			});
			u.d(A, "b", function () {
				return p;
			});
			u.d(A, "c", function () {
				return m;
			});
			var x = function () {
				x =
					Object.assign ||
					function (k) {
						for (var q, c = 1, n = arguments.length; c < n; c++) {
							q = arguments[c];
							for (var f in q)
								Object.prototype.hasOwnProperty.call(q, f) && (k[f] = q[f]);
						}
						return k;
					};
				return x.apply(this, arguments);
			};
		},
		function (v, A, u) {
			function p(k, q, c) {
				return new Promise(function (n) {
					if (!k) return n();
					var f = c.document.createElement("script");
					f.type = "text/javascript";
					f.onload = function () {
						n();
					};
					f.onerror = function () {
						q && Object(x.e)(q);
						n();
					};
					f.src = k;
					c.document.getElementsByTagName("head")[0].appendChild(f);
				});
			}
			function m(k, q, c, n, f, h) {
				h = h || 0;
				var g = q[f],
					t = n.length,
					r = 100 <= k.length && !g;
				t !== c || n.buffer.byteLength !== t
					? (r
							? ((k = k.shift()),
								(g = q[k]),
								g.length < c && (g = new Int8Array(c)),
								(q[k] = null))
							: (g = g ? q[f] : new Int8Array(c)),
						g.subarray(h, h + t).set(n),
						(n = g))
					: r && ((k = k.shift()), (q[k] = null));
				q[f] = n;
			}
			u.d(A, "a", function () {
				return p;
			});
			u.d(A, "b", function () {
				return m;
			});
			var x = u(0);
		},
		function (v, A, u) {
			u.d(A, "b", function () {
				return p;
			});
			u.d(A, "a", function () {
				return m;
			});
			var p = function (x) {
					if ("string" === typeof x) {
						for (
							var k = new Uint8Array(x.length), q = x.length, c = 0;
							c < q;
							c++
						)
							k[c] = x.charCodeAt(c);
						return k;
					}
					return x;
				},
				m = function (x) {
					if ("string" !== typeof x) {
						for (var k = "", q = 0, c = x.length, n; q < c; )
							(n = x.subarray(q, q + 1024)),
								(q += 1024),
								(k += String.fromCharCode.apply(null, n));
						return k;
					}
					return x;
				};
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return r;
			});
			var p,
				m = "undefined" === typeof window ? self : window;
			v = (function () {
				var l = navigator.userAgent.toLowerCase();
				return (l =
					/(msie) ([\w.]+)/.exec(l) || /(trident)(?:.*? rv:([\w.]+)|)/.exec(l))
					? parseInt(l[2], 10)
					: l;
			})();
			var x = (function () {
				var l = m.navigator.userAgent.match(/OPR/),
					z = m.navigator.userAgent.match(/Maxthon/),
					F = m.navigator.userAgent.match(/Edge/);
				return m.navigator.userAgent.match(/Chrome\/(.*?) /) && !l && !z && !F;
			})();
			(function () {
				if (!x) return null;
				var l = m.navigator.userAgent.match(/Chrome\/([0-9]+)\./);
				return l ? parseInt(l[1], 10) : l;
			})();
			var k =
				!!navigator.userAgent.match(/Edge/i) ||
				(navigator.userAgent.match(/Edg\/(.*?)/) &&
					m.navigator.userAgent.match(/Chrome\/(.*?) /));
			(function () {
				if (!k) return null;
				var l = m.navigator.userAgent.match(/Edg\/([0-9]+)\./);
				return l ? parseInt(l[1], 10) : l;
			})();
			A =
				/iPad|iPhone|iPod/.test(m.navigator.platform) ||
				("MacIntel" === navigator.platform && 1 < navigator.maxTouchPoints) ||
				/iPad|iPhone|iPod/.test(m.navigator.userAgent);
			var q = (function () {
					var l = m.navigator.userAgent.match(
						/.*\/([0-9\.]+)\s(Safari|Mobile).*/i,
					);
					return l ? parseFloat(l[1]) : l;
				})(),
				c =
					/^((?!chrome|android).)*safari/i.test(m.navigator.userAgent) ||
					(/^((?!chrome|android).)*$/.test(m.navigator.userAgent) && A);
			c &&
				/^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
				parseInt(
					null === (p = navigator.userAgent.match(/Version\/(\d+)/)) ||
						void 0 === p
						? void 0
						: p[1],
					10,
				);
			var n = m.navigator.userAgent.match(/Firefox/);
			(function () {
				if (!n) return null;
				var l = m.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
				return l ? parseInt(l[1], 10) : l;
			})();
			v || /Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);
			navigator.userAgent.match(/(iPad|iPhone|iPod)/i);
			m.navigator.userAgent.indexOf("Android");
			var f = /Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(
					m.navigator.userAgent,
				),
				h = m.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)
					? 14 <=
						parseInt(
							m.navigator.userAgent.match(
								/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i,
							)[3],
							10,
						)
					: !1,
				g = !(!self.WebAssembly || !self.WebAssembly.validate),
				t =
					-1 < m.navigator.userAgent.indexOf("Edge/16") ||
					-1 < m.navigator.userAgent.indexOf("MSAppHost"),
				r = function () {
					var l;
					if ((l = g && !t))
						(l = c && null !== q && 14 > q), (l = !(!h && (l || f)));
					return l;
				};
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return f;
			});
			var p = u(4);
			v = u(13);
			var m = u.n(v),
				x = new Map(),
				k = function () {
					return ("undefined" === typeof window ? self : window).trustedTypes;
				},
				q = function (h, g) {
					return k().createPolicy(h, {
						createHTML: function (t) {
							return m.a.sanitize(
								t,
								Object(p.a)(Object(p.a)({}, g), { RETURN_TRUSTED_TYPE: !1 }),
							);
						},
						createScript: function (t) {
							return t;
						},
						createScriptURL: function (t) {
							return t;
						},
					});
				},
				c = function (h) {
					return k().createPolicy(h, {
						createHTML: function (g) {
							return g;
						},
						createScript: function (g) {
							return g;
						},
						createScriptURL: function (g) {
							return g;
						},
					});
				},
				n = function (h, g) {
					var t,
						r = {
							createHTML: function (l) {
								return m.a.sanitize(l, Object(p.a)({}, g));
							},
							createScript: function (l) {
								return l;
							},
							createScriptURL: function (l) {
								return l;
							},
						};
					if (null === (t = k()) || void 0 === t ? 0 : t.createPolicy)
						(t = "".concat(h, "-po")),
							x.has(h) || ((r = c(t)), x.set(t, r), (r = q(h, g)), x.set(h, r)),
							(r = x.get(g.createPolicyOnly ? t : h));
					return r;
				},
				f = function (h, g) {
					void 0 === g &&
						(g = { createPolicyOnly: !0, trustedTypesPolicyName: "webviewer" });
					return n(g.trustedTypesPolicyName, g).createScriptURL(h.toString());
				};
		},
		function (v, A, u) {
			function p(q) {
				"@babel/helpers - typeof";
				return (
					(p =
						"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
							? function (c) {
									return typeof c;
								}
							: function (c) {
									return c &&
										"function" == typeof Symbol &&
										c.constructor === Symbol &&
										c !== Symbol.prototype
										? "symbol"
										: typeof c;
								}),
					p(q)
				);
			}
			var m, x, k;
			!(function (q) {
				"object" === p(A) && "undefined" !== typeof v
					? (v.exports = q())
					: !((x = []),
						(m = q),
						(k = "function" === typeof m ? m.apply(A, x) : m),
						void 0 !== k && (v.exports = k));
			})(function () {
				return (function h(c, n, f) {
					function g(l, z) {
						if (!n[l]) {
							if (!c[l]) {
								if (t) return t(l, !0);
								z = Error("Cannot find module '".concat(l, "'"));
								throw ((z.code = "MODULE_NOT_FOUND"), z);
							}
							z = n[l] = { exports: {} };
							c[l][0].call(
								z.exports,
								function (F) {
									return g(c[l][1][F] || F);
								},
								z,
								z.exports,
								h,
								c,
								n,
								f,
							);
						}
						return n[l].exports;
					}
					for (var t = !1, r = 0; r < f.length; r++) g(f[r]);
					return g;
				})(
					{
						1: [
							function (c, n, f) {
								var h = {}.hasOwnProperty,
									g = function (t, r) {
										function l() {
											this.constructor = t;
										}
										for (var z in r) h.call(r, z) && (t[z] = r[z]);
										l.prototype = r.prototype;
										t.prototype = new l();
										t.__super__ = r.prototype;
										return t;
									};
								f = c("./PriorityQueue/AbstractPriorityQueue");
								c = c("./PriorityQueue/ArrayStrategy");
								f = (function (t) {
									function r(l) {
										l || (l = {});
										l.strategy || (l.strategy = BinaryHeapStrategy);
										l.comparator ||
											(l.comparator = function (z, F) {
												return (z || 0) - (F || 0);
											});
										r.__super__.constructor.call(this, l);
									}
									g(r, t);
									return r;
								})(f);
								f.ArrayStrategy = c;
								n.exports = f;
							},
							{
								"./PriorityQueue/AbstractPriorityQueue": 2,
								"./PriorityQueue/ArrayStrategy": 3,
							},
						],
						2: [
							function (c, n, f) {
								n.exports = (function () {
									function h(g) {
										if (null == (null != g ? g.strategy : void 0))
											throw "Must pass options.strategy, a strategy";
										if (null == (null != g ? g.comparator : void 0))
											throw "Must pass options.comparator, a comparator";
										this.priv = new g.strategy(g);
										this.length = 0;
									}
									h.prototype.queue = function (g) {
										this.length++;
										this.priv.queue(g);
									};
									h.prototype.dequeue = function (g) {
										if (!this.length) throw "Empty queue";
										this.length--;
										return this.priv.dequeue();
									};
									h.prototype.peek = function (g) {
										if (!this.length) throw "Empty queue";
										return this.priv.peek();
									};
									h.prototype.remove = function (g) {
										this.priv.remove(g) && --this.length;
									};
									h.prototype.find = function (g) {
										return 0 <= this.priv.find(g);
									};
									h.prototype.removeAllMatching = function (g, t) {
										g = this.priv.removeAllMatching(g, t);
										this.length -= g;
									};
									return h;
								})();
							},
							{},
						],
						3: [
							function (c, n, f) {
								var h = function (g, t, r) {
									var l;
									var z = 0;
									for (l = g.length; z < l; ) {
										var F = (z + l) >>> 1;
										0 <= r(g[F], t) ? (z = F + 1) : (l = F);
									}
									return z;
								};
								n.exports = (function () {
									function g(t) {
										var r;
										this.options = t;
										this.comparator = this.options.comparator;
										this.data =
											(null != (r = this.options.initialValues)
												? r.slice(0)
												: void 0) || [];
										this.data.sort(this.comparator).reverse();
									}
									g.prototype.queue = function (t) {
										var r = h(this.data, t, this.comparator);
										this.data.splice(r, 0, t);
									};
									g.prototype.dequeue = function () {
										return this.data.pop();
									};
									g.prototype.peek = function () {
										return this.data[this.data.length - 1];
									};
									g.prototype.find = function (t) {
										var r = h(this.data, t, this.comparator) - 1;
										return 0 <= r && !this.comparator(this.data[r], t) ? r : -1;
									};
									g.prototype.remove = function (t) {
										t = this.find(t);
										return 0 <= t ? (this.data.splice(t, 1), !0) : !1;
									};
									g.prototype.removeAllMatching = function (t, r) {
										for (var l = 0, z = this.data.length - 1; 0 <= z; --z)
											if (t(this.data[z])) {
												var F = this.data.splice(z, 1)[0];
												r && r(F);
												++l;
											}
										return l;
									};
									return g;
								})();
							},
							{},
						],
					},
					{},
					[1],
				)(1);
			});
		},
		function (v, A, u) {
			(function (p) {
				function m(q, c) {
					this._id = q;
					this._clearFn = c;
				}
				var x =
						("undefined" !== typeof p && p) ||
						("undefined" !== typeof self && self) ||
						window,
					k = Function.prototype.apply;
				A.setTimeout = function () {
					return new m(k.call(setTimeout, x, arguments), clearTimeout);
				};
				A.setInterval = function () {
					return new m(k.call(setInterval, x, arguments), clearInterval);
				};
				A.clearTimeout = A.clearInterval = function (q) {
					q && q.close();
				};
				m.prototype.unref = m.prototype.ref = function () {};
				m.prototype.close = function () {
					this._clearFn.call(x, this._id);
				};
				A.enroll = function (q, c) {
					clearTimeout(q._idleTimeoutId);
					q._idleTimeout = c;
				};
				A.unenroll = function (q) {
					clearTimeout(q._idleTimeoutId);
					q._idleTimeout = -1;
				};
				A._unrefActive = A.active = function (q) {
					clearTimeout(q._idleTimeoutId);
					var c = q._idleTimeout;
					0 <= c &&
						(q._idleTimeoutId = setTimeout(function () {
							q._onTimeout && q._onTimeout();
						}, c));
				};
				u(25);
				A.setImmediate =
					("undefined" !== typeof self && self.setImmediate) ||
					("undefined" !== typeof p && p.setImmediate) ||
					(this && this.setImmediate);
				A.clearImmediate =
					("undefined" !== typeof self && self.clearImmediate) ||
					("undefined" !== typeof p && p.clearImmediate) ||
					(this && this.clearImmediate);
			}).call(this, u(12));
		},
		function (v, A, u) {
			function p(c, n, f, h) {
				return m(
					c,
					n,
					f,
					h,
					!!WebAssembly.instantiateStreaming,
					void 0,
					void 0,
				).then(function (g) {
					Object(x.a)(
						"load",
						"WASM compilation took ".concat(Date.now() - NaN, " ms"),
					);
					return g;
				});
			}
			function m(c, n, f, h, g, t, r) {
				t = t || Date.now();
				if (g && !h)
					return (
						Object(x.a)("load", "Try instantiateStreaming"),
						fetch(Object(k.a)(c))
							.then(function (l) {
								return WebAssembly.instantiateStreaming(l, n);
							})
							.catch(function (l) {
								Object(x.a)(
									"load",
									"instantiateStreaming Failed "
										.concat(c, " message ")
										.concat(l.message),
								);
								return m(c, n, f, h, !1, t, r);
							})
					);
				g = h
					? h.map(function (l, z) {
							return "".concat(l, "PDFNetCWasm-chunk-").concat(z, ".wasm");
						})
					: c;
				return Object(k.b)(g, f, !0, !0).then(function (l) {
					r = Date.now();
					Object(x.a)("load", "Request took ".concat(r - t, " ms"));
					return WebAssembly.instantiate(l, n);
				});
			}
			u.d(A, "a", function () {
				return p;
			});
			var x = u(0),
				k = u(3),
				q = u(5);
			u.d(A, "b", function () {
				return q.a;
			});
		},
		function (v, A) {
			A = (function () {
				return this;
			})();
			try {
				A = A || new Function("return this")();
			} catch (u) {
				"object" === typeof window && (A = window);
			}
			v.exports = A;
		},
		function (v, A, u) {
			(function (p, m) {
				v.exports = m();
			})(this, function () {
				function p(T) {
					return function (O) {
						O instanceof RegExp && (O.lastIndex = 0);
						for (
							var R = arguments.length, U = Array(1 < R ? R - 1 : 0), W = 1;
							W < R;
							W++
						)
							U[W - 1] = arguments[W];
						return F(T, O, U);
					};
				}
				function m(T, O) {
					var R =
						2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : b;
					f && f(T, null);
					for (var U = O.length; U--; ) {
						var W = O[U];
						if ("string" === typeof W) {
							var ma = R(W);
							ma !== W && (h(O) || (O[U] = ma), (W = ma));
						}
						T[W] = !0;
					}
					return T;
				}
				function x(T) {
					for (
						var O = z(null), R = $jscomp.makeIterator(n(T)), U = R.next();
						!U.done;
						U = R.next()
					) {
						var W = $jscomp.makeIterator(U.value);
						U = W.next().value;
						W = W.next().value;
						if (D(T, U))
							if (Array.isArray(W)) {
								for (var ma = 0; ma < W.length; ma++)
									D(W, ma) || (W[ma] = null);
								O[U] = W;
							} else
								O[U] =
									W && "object" === typeof W && W.constructor === Object
										? x(W)
										: W;
					}
					return O;
				}
				function k(T, O) {
					for (; null !== T; ) {
						var R = t(T, O);
						if (R) {
							if (R.get) return p(R.get);
							if ("function" === typeof R.value) return p(R.value);
						}
						T = g(T);
					}
					return function () {
						return null;
					};
				}
				function q() {
					function T(C, K, M) {
						H(C, function (P) {
							P.call(R, K, M, ta);
						});
					}
					var O =
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: "undefined" === typeof window
									? null
									: window,
						R = function (C) {
							return q(C);
						};
					R.version = "3.2.5";
					R.removed = [];
					if (
						!O ||
						!O.document ||
						O.document.nodeType !== Aa.document ||
						!O.Element
					)
						return (R.isSupported = !1), R;
					var U = O.document,
						W = U,
						ma = W.currentScript,
						mb = O.DocumentFragment,
						Z = O.HTMLTemplateElement,
						Sa = O.Node,
						nb = O.Element,
						Ba = O.NodeFilter,
						Nb =
							void 0 === O.NamedNodeMap
								? O.NamedNodeMap || O.MozNamedAttrMap
								: O.NamedNodeMap,
						Ob = O.HTMLFormElement,
						Pb = O.DOMParser,
						Ia = O.trustedTypes;
					O = nb.prototype;
					var Qb = k(O, "cloneNode"),
						Rb = k(O, "remove"),
						Sb = k(O, "nextSibling"),
						Tb = k(O, "childNodes"),
						Ja = k(O, "parentNode");
					"function" === typeof Z &&
						((Z = U.createElement("template")),
						Z.content &&
							Z.content.ownerDocument &&
							(U = Z.content.ownerDocument));
					var ha,
						Ca = "";
					Z = U;
					var Ta = Z.implementation,
						Ub = Z.createNodeIterator,
						Vb = Z.createDocumentFragment,
						Wb = Z.getElementsByTagName,
						Xb = W.importNode,
						ia = ob();
					R.isSupported =
						"function" === typeof n &&
						"function" === typeof Ja &&
						Ta &&
						void 0 !== Ta.createHTMLDocument;
					Z = Ka;
					var Ua = Z.MUSTACHE_EXPR,
						Va = Z.ERB_EXPR,
						Wa = Z.TMPLIT_EXPR,
						Yb = Z.DATA_ATTR,
						Zb = Z.ARIA_ATTR,
						$b = Z.IS_SCRIPT_OR_DATA,
						pb = Z.ATTR_WHITESPACE,
						qb = Z.CUSTOM_ELEMENT,
						rb = Ka.IS_ALLOWED_URI,
						ca = null,
						sb = m(
							{},
							[].concat(
								$jscomp.arrayFromIterable(Q),
								$jscomp.arrayFromIterable(V),
								$jscomp.arrayFromIterable(X),
								$jscomp.arrayFromIterable(ka),
								$jscomp.arrayFromIterable(na),
							),
						),
						ea = null,
						tb = m(
							{},
							[].concat(
								$jscomp.arrayFromIterable(ra),
								$jscomp.arrayFromIterable(pa),
								$jscomp.arrayFromIterable(aa),
								$jscomp.arrayFromIterable(qa),
							),
						),
						Y = Object.seal(
							z(null, {
								tagNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								attributeNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								allowCustomizedBuiltInElements: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: !1,
								},
							}),
						),
						Da = null,
						Xa = null,
						ub = !0,
						Ya = !0,
						vb = !1,
						wb = !0,
						ua = !1,
						Za = !0,
						sa = !1,
						$a = !1,
						ab = !1,
						va = !1,
						La = !1,
						Ma = !1,
						xb = !0,
						yb = !1,
						bb = !0,
						Ea = !1,
						wa = {},
						xa = null,
						zb = m(
							{},
							"annotation-xml audio colgroup desc foreignobject head iframe math mi mn mo ms mtext noembed noframes noscript plaintext script style svg template thead title video xmp".split(
								" ",
							),
						),
						Ab = null,
						Bb = m({}, "audio video img source image track".split(" ")),
						cb = null,
						Cb = m(
							{},
							"alt class for id label name pattern placeholder role summary title value style xmlns".split(
								" ",
							),
						),
						ya = "http://www.w3.org/1999/xhtml",
						Db = !1,
						db = null,
						ac = m(
							{},
							[
								"http://www.w3.org/1998/Math/MathML",
								"http://www.w3.org/2000/svg",
								"http://www.w3.org/1999/xhtml",
							],
							d,
						),
						Na = m({}, ["mi", "mo", "mn", "ms", "mtext"]),
						Oa = m({}, ["annotation-xml"]),
						bc = m({}, ["title", "style", "font", "a", "script"]),
						Fa = null,
						cc = ["application/xhtml+xml", "text/html"],
						da = null,
						ta = null,
						dc = U.createElement("form"),
						Eb = function (C) {
							return C instanceof RegExp || C instanceof Function;
						},
						eb = function () {
							var C =
								0 < arguments.length && void 0 !== arguments[0]
									? arguments[0]
									: {};
							if (!ta || ta !== C) {
								(C && "object" === typeof C) || (C = {});
								C = x(C);
								Fa =
									-1 === cc.indexOf(C.PARSER_MEDIA_TYPE)
										? "text/html"
										: C.PARSER_MEDIA_TYPE;
								da = "application/xhtml+xml" === Fa ? d : b;
								ca = D(C, "ALLOWED_TAGS") ? m({}, C.ALLOWED_TAGS, da) : sb;
								ea = D(C, "ALLOWED_ATTR") ? m({}, C.ALLOWED_ATTR, da) : tb;
								db = D(C, "ALLOWED_NAMESPACES")
									? m({}, C.ALLOWED_NAMESPACES, d)
									: ac;
								cb = D(C, "ADD_URI_SAFE_ATTR")
									? m(x(Cb), C.ADD_URI_SAFE_ATTR, da)
									: Cb;
								Ab = D(C, "ADD_DATA_URI_TAGS")
									? m(x(Bb), C.ADD_DATA_URI_TAGS, da)
									: Bb;
								xa = D(C, "FORBID_CONTENTS")
									? m({}, C.FORBID_CONTENTS, da)
									: zb;
								Da = D(C, "FORBID_TAGS") ? m({}, C.FORBID_TAGS, da) : {};
								Xa = D(C, "FORBID_ATTR") ? m({}, C.FORBID_ATTR, da) : {};
								wa = D(C, "USE_PROFILES") ? C.USE_PROFILES : !1;
								ub = !1 !== C.ALLOW_ARIA_ATTR;
								Ya = !1 !== C.ALLOW_DATA_ATTR;
								vb = C.ALLOW_UNKNOWN_PROTOCOLS || !1;
								wb = !1 !== C.ALLOW_SELF_CLOSE_IN_ATTR;
								ua = C.SAFE_FOR_TEMPLATES || !1;
								Za = !1 !== C.SAFE_FOR_XML;
								sa = C.WHOLE_DOCUMENT || !1;
								va = C.RETURN_DOM || !1;
								La = C.RETURN_DOM_FRAGMENT || !1;
								Ma = C.RETURN_TRUSTED_TYPE || !1;
								ab = C.FORCE_BODY || !1;
								xb = !1 !== C.SANITIZE_DOM;
								yb = C.SANITIZE_NAMED_PROPS || !1;
								bb = !1 !== C.KEEP_CONTENT;
								Ea = C.IN_PLACE || !1;
								rb = C.ALLOWED_URI_REGEXP || Pa;
								ya = C.NAMESPACE || "http://www.w3.org/1999/xhtml";
								Na = C.MATHML_TEXT_INTEGRATION_POINTS || Na;
								Oa = C.HTML_INTEGRATION_POINTS || Oa;
								Y = C.CUSTOM_ELEMENT_HANDLING || {};
								C.CUSTOM_ELEMENT_HANDLING &&
									Eb(C.CUSTOM_ELEMENT_HANDLING.tagNameCheck) &&
									(Y.tagNameCheck = C.CUSTOM_ELEMENT_HANDLING.tagNameCheck);
								C.CUSTOM_ELEMENT_HANDLING &&
									Eb(C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck) &&
									(Y.attributeNameCheck =
										C.CUSTOM_ELEMENT_HANDLING.attributeNameCheck);
								C.CUSTOM_ELEMENT_HANDLING &&
									"boolean" ===
										typeof C.CUSTOM_ELEMENT_HANDLING
											.allowCustomizedBuiltInElements &&
									(Y.allowCustomizedBuiltInElements =
										C.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements);
								ua && (Ya = !1);
								La && (va = !0);
								wa &&
									((ca = m({}, na)),
									(ea = []),
									!0 === wa.html && (m(ca, Q), m(ea, ra)),
									!0 === wa.svg && (m(ca, V), m(ea, pa), m(ea, qa)),
									!0 === wa.svgFilters && (m(ca, X), m(ea, pa), m(ea, qa)),
									!0 === wa.mathMl && (m(ca, ka), m(ea, aa), m(ea, qa)));
								C.ADD_TAGS &&
									(ca === sb && (ca = x(ca)), m(ca, C.ADD_TAGS, da));
								C.ADD_ATTR &&
									(ea === tb && (ea = x(ea)), m(ea, C.ADD_ATTR, da));
								C.ADD_URI_SAFE_ATTR && m(cb, C.ADD_URI_SAFE_ATTR, da);
								C.FORBID_CONTENTS &&
									(xa === zb && (xa = x(xa)), m(xa, C.FORBID_CONTENTS, da));
								bb && (ca["#text"] = !0);
								sa && m(ca, ["html", "head", "body"]);
								ca.table && (m(ca, ["tbody"]), delete Da.tbody);
								if (C.TRUSTED_TYPES_POLICY) {
									if ("function" !== typeof C.TRUSTED_TYPES_POLICY.createHTML)
										throw N(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.',
										);
									if (
										"function" !== typeof C.TRUSTED_TYPES_POLICY.createScriptURL
									)
										throw N(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.',
										);
									ha = C.TRUSTED_TYPES_POLICY;
									Ca = ha.createHTML("");
								} else
									void 0 === ha && (ha = ec(Ia, ma)),
										null !== ha &&
											"string" === typeof Ca &&
											(Ca = ha.createHTML(""));
								l && l(C);
								ta = C;
							}
						},
						Fb = m(
							{},
							[].concat(
								$jscomp.arrayFromIterable(V),
								$jscomp.arrayFromIterable(X),
								$jscomp.arrayFromIterable(ja),
							),
						),
						Gb = m(
							{},
							[].concat(
								$jscomp.arrayFromIterable(ka),
								$jscomp.arrayFromIterable(fa),
							),
						),
						fc = function (C) {
							var K = Ja(C);
							(K && K.tagName) ||
								(K = { namespaceURI: ya, tagName: "template" });
							var M = b(C.tagName),
								P = b(K.tagName);
							return db[C.namespaceURI]
								? "http://www.w3.org/2000/svg" === C.namespaceURI
									? "http://www.w3.org/1999/xhtml" === K.namespaceURI
										? "svg" === M
										: "http://www.w3.org/1998/Math/MathML" === K.namespaceURI
											? "svg" === M && ("annotation-xml" === P || Na[P])
											: !!Fb[M]
									: "http://www.w3.org/1998/Math/MathML" === C.namespaceURI
										? "http://www.w3.org/1999/xhtml" === K.namespaceURI
											? "math" === M
											: "http://www.w3.org/2000/svg" === K.namespaceURI
												? "math" === M && Oa[P]
												: !!Gb[M]
										: "http://www.w3.org/1999/xhtml" === C.namespaceURI
											? ("http://www.w3.org/2000/svg" === K.namespaceURI &&
													!Oa[P]) ||
												("http://www.w3.org/1998/Math/MathML" ===
													K.namespaceURI &&
													!Na[P])
												? !1
												: !Gb[M] && (bc[M] || !Fb[M])
											: "application/xhtml+xml" === Fa && db[C.namespaceURI]
												? !0
												: !1
								: !1;
						},
						oa = function (C) {
							G(R.removed, { element: C });
							try {
								Ja(C).removeChild(C);
							} catch (K) {
								Rb(C);
							}
						},
						Qa = function (C, K) {
							try {
								G(R.removed, { attribute: K.getAttributeNode(C), from: K });
							} catch (M) {
								G(R.removed, { attribute: null, from: K });
							}
							K.removeAttribute(C);
							if ("is" === C)
								if (va || La)
									try {
										oa(K);
									} catch (M) {}
								else
									try {
										K.setAttribute(C, "");
									} catch (M) {}
						},
						Hb = function (C) {
							var K = null,
								M = null;
							ab
								? (C = "<remove></remove>" + C)
								: (M = (M = e(C, /^[\r\n\t ]+/)) && M[0]);
							"application/xhtml+xml" === Fa &&
								"http://www.w3.org/1999/xhtml" === ya &&
								(C =
									'<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' +
									C +
									"</body></html>");
							var P = ha ? ha.createHTML(C) : C;
							if ("http://www.w3.org/1999/xhtml" === ya)
								try {
									K = new Pb().parseFromString(P, Fa);
								} catch (S) {}
							if (!K || !K.documentElement) {
								K = Ta.createDocument(ya, "template", null);
								try {
									K.documentElement.innerHTML = Db ? Ca : P;
								} catch (S) {}
							}
							P = K.body || K.documentElement;
							C &&
								M &&
								P.insertBefore(U.createTextNode(M), P.childNodes[0] || null);
							return "http://www.w3.org/1999/xhtml" === ya
								? Wb.call(K, sa ? "html" : "body")[0]
								: sa
									? K.documentElement
									: P;
						},
						Ib = function (C) {
							return Ub.call(
								C.ownerDocument || C,
								C,
								Ba.SHOW_ELEMENT |
									Ba.SHOW_COMMENT |
									Ba.SHOW_TEXT |
									Ba.SHOW_PROCESSING_INSTRUCTION |
									Ba.SHOW_CDATA_SECTION,
								null,
							);
						},
						fb = function (C) {
							return (
								C instanceof Ob &&
								("string" !== typeof C.nodeName ||
									"string" !== typeof C.textContent ||
									"function" !== typeof C.removeChild ||
									!(C.attributes instanceof Nb) ||
									"function" !== typeof C.removeAttribute ||
									"function" !== typeof C.setAttribute ||
									"string" !== typeof C.namespaceURI ||
									"function" !== typeof C.insertBefore ||
									"function" !== typeof C.hasChildNodes)
							);
						},
						Jb = function (C) {
							return "function" === typeof Sa && C instanceof Sa;
						},
						Kb = function (C) {
							var K = null;
							T(ia.beforeSanitizeElements, C, null);
							if (fb(C)) return oa(C), !0;
							var M = da(C.nodeName);
							T(ia.uponSanitizeElement, C, { tagName: M, allowedTags: ca });
							if (
								(C.hasChildNodes() &&
									!Jb(C.firstElementChild) &&
									L(/<[/\w!]/g, C.innerHTML) &&
									L(/<[/\w!]/g, C.textContent)) ||
								C.nodeType === Aa.progressingInstruction ||
								(Za && C.nodeType === Aa.comment && L(/<[/\w]/g, C.data))
							)
								return oa(C), !0;
							if (!ca[M] || Da[M]) {
								if (
									!Da[M] &&
									"annotation-xml" !== M &&
									e(M, qb) &&
									((Y.tagNameCheck instanceof RegExp && L(Y.tagNameCheck, M)) ||
										(Y.tagNameCheck instanceof Function && Y.tagNameCheck(M)))
								)
									return !1;
								if (bb && !xa[M]) {
									M = Ja(C) || C.parentNode;
									var P = Tb(C) || C.childNodes;
									if (P && M)
										for (var S = P.length - 1; 0 <= S; --S) {
											var ba = Qb(P[S], !0);
											ba.__removalCount = (C.__removalCount || 0) + 1;
											M.insertBefore(ba, Sb(C));
										}
								}
								oa(C);
								return !0;
							}
							if (
								(C instanceof nb && !fc(C)) ||
								(("noscript" === M || "noembed" === M || "noframes" === M) &&
									L(/<\/no(script|embed|frames)/i, C.innerHTML))
							)
								return oa(C), !0;
							ua &&
								C.nodeType === Aa.text &&
								((K = C.textContent),
								H([Ua, Va, Wa], function (la) {
									K = w(K, la, " ");
								}),
								C.textContent !== K &&
									(G(R.removed, { element: C.cloneNode() }),
									(C.textContent = K)));
							T(ia.afterSanitizeElements, C, null);
							return !1;
						},
						Lb = function (C, K, M) {
							if (xb && ("id" === K || "name" === K) && (M in U || M in dc))
								return !1;
							if (!Ya || Xa[K] || !L(Yb, K))
								if (!ub || !L(Zb, K))
									if (!ea[K] || Xa[K]) {
										if (
											!(
												("annotation-xml" !== C &&
													e(C, qb) &&
													((Y.tagNameCheck instanceof RegExp &&
														L(Y.tagNameCheck, C)) ||
														(Y.tagNameCheck instanceof Function &&
															Y.tagNameCheck(C))) &&
													((Y.attributeNameCheck instanceof RegExp &&
														L(Y.attributeNameCheck, K)) ||
														(Y.attributeNameCheck instanceof Function &&
															Y.attributeNameCheck(K)))) ||
												("is" === K &&
													Y.allowCustomizedBuiltInElements &&
													((Y.tagNameCheck instanceof RegExp &&
														L(Y.tagNameCheck, M)) ||
														(Y.tagNameCheck instanceof Function &&
															Y.tagNameCheck(M))))
											)
										)
											return !1;
									} else if (
										!(
											cb[K] ||
											L(rb, w(M, pb, "")) ||
											(("src" === K || "xlink:href" === K || "href" === K) &&
												"script" !== C &&
												0 === B(M, "data:") &&
												Ab[C]) ||
											(vb && !L($b, w(M, pb, "")))
										) &&
										M
									)
										return !1;
							return !0;
						},
						Mb = function (C) {
							T(ia.beforeSanitizeAttributes, C, null);
							var K = C.attributes;
							if (K && !fb(C)) {
								for (
									var M = {
											attrName: "",
											attrValue: "",
											keepAttr: !0,
											allowedAttributes: ea,
											forceKeepAttr: void 0,
										},
										P = K.length,
										S = {};
									P--;
								) {
									var ba = K[P],
										la = ba.name,
										gb = ba.namespaceURI,
										Ga = ba.value;
									ba = da(la);
									S.$jscomp$loop$prop$value$10 = "value" === la ? Ga : y(Ga);
									M.attrName = ba;
									M.attrValue = S.$jscomp$loop$prop$value$10;
									M.keepAttr = !0;
									M.forceKeepAttr = void 0;
									T(ia.uponSanitizeAttribute, C, M);
									S.$jscomp$loop$prop$value$10 = M.attrValue;
									!yb ||
										("id" !== ba && "name" !== ba) ||
										(Qa(la, C),
										(S.$jscomp$loop$prop$value$10 =
											"user-content-" + S.$jscomp$loop$prop$value$10));
									if (
										Za &&
										L(
											/((--!?|])>)|<\/(style|title)/i,
											S.$jscomp$loop$prop$value$10,
										)
									)
										Qa(la, C);
									else if (!M.forceKeepAttr && (Qa(la, C), M.keepAttr))
										if (!wb && L(/\/>/i, S.$jscomp$loop$prop$value$10))
											Qa(la, C);
										else if (
											(ua &&
												H(
													[Ua, Va, Wa],
													(function (hb) {
														return function (gc) {
															hb.$jscomp$loop$prop$value$10 = w(
																hb.$jscomp$loop$prop$value$10,
																gc,
																" ",
															);
														};
													})(S),
												),
											(Ga = da(C.nodeName)),
											Lb(Ga, ba, S.$jscomp$loop$prop$value$10))
										) {
											if (
												ha &&
												"object" === typeof Ia &&
												"function" === typeof Ia.getAttributeType &&
												!gb
											)
												switch (Ia.getAttributeType(Ga, ba)) {
													case "TrustedHTML":
														S.$jscomp$loop$prop$value$10 = ha.createHTML(
															S.$jscomp$loop$prop$value$10,
														);
														break;
													case "TrustedScriptURL":
														S.$jscomp$loop$prop$value$10 = ha.createScriptURL(
															S.$jscomp$loop$prop$value$10,
														);
												}
											try {
												gb
													? C.setAttributeNS(
															gb,
															la,
															S.$jscomp$loop$prop$value$10,
														)
													: C.setAttribute(la, S.$jscomp$loop$prop$value$10),
													fb(C) ? oa(C) : J(R.removed);
											} catch (hb) {}
										}
									S = {
										$jscomp$loop$prop$value$10: S.$jscomp$loop$prop$value$10,
									};
								}
								T(ia.afterSanitizeAttributes, C, null);
							}
						},
						hc = function M(K) {
							var P,
								S = Ib(K);
							for (T(ia.beforeSanitizeShadowDOM, K, null); (P = S.nextNode()); )
								T(ia.uponSanitizeShadowNode, P, null),
									Kb(P),
									Mb(P),
									P.content instanceof mb && M(P.content);
							T(ia.afterSanitizeShadowDOM, K, null);
						};
					R.sanitize = function (K) {
						var M =
								1 < arguments.length && void 0 !== arguments[1]
									? arguments[1]
									: {},
							P = null,
							S = null;
						S = S = null;
						(Db = !K) && (K = "\x3c!--\x3e");
						if ("string" !== typeof K && !Jb(K))
							if ("function" === typeof K.toString) {
								if (((K = K.toString()), "string" !== typeof K))
									throw N("dirty is not a string, aborting");
							} else throw N("toString is not a function");
						if (!R.isSupported) return K;
						$a || eb(M);
						R.removed = [];
						"string" === typeof K && (Ea = !1);
						if (Ea) {
							if (K.nodeName && ((M = da(K.nodeName)), !ca[M] || Da[M]))
								throw N(
									"root node is forbidden and cannot be sanitized in-place",
								);
						} else if (K instanceof Sa)
							(P = Hb("\x3c!----\x3e")),
								(S = P.ownerDocument.importNode(K, !0)),
								S.nodeType === Aa.element && "BODY" === S.nodeName
									? (P = S)
									: "HTML" === S.nodeName
										? (P = S)
										: P.appendChild(S);
						else {
							if (!va && !ua && !sa && -1 === K.indexOf("<"))
								return ha && Ma ? ha.createHTML(K) : K;
							P = Hb(K);
							if (!P) return va ? null : Ma ? Ca : "";
						}
						P && ab && oa(P.firstChild);
						for (M = Ib(Ea ? K : P); (S = M.nextNode()); )
							Kb(S), Mb(S), S.content instanceof mb && hc(S.content);
						if (Ea) return K;
						if (va) {
							if (La)
								for (S = Vb.call(P.ownerDocument); P.firstChild; )
									S.appendChild(P.firstChild);
							else S = P;
							if (ea.shadowroot || ea.shadowrootmode) S = Xb.call(W, S, !0);
							return S;
						}
						var ba = sa ? P.outerHTML : P.innerHTML;
						sa &&
							ca["!doctype"] &&
							P.ownerDocument &&
							P.ownerDocument.doctype &&
							P.ownerDocument.doctype.name &&
							L(za, P.ownerDocument.doctype.name) &&
							(ba = "<!DOCTYPE " + P.ownerDocument.doctype.name + ">\n" + ba);
						ua &&
							H([Ua, Va, Wa], function (la) {
								ba = w(ba, la, " ");
							});
						return ha && Ma ? ha.createHTML(ba) : ba;
					};
					R.setConfig = function () {
						eb(
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: {},
						);
						$a = !0;
					};
					R.clearConfig = function () {
						ta = null;
						$a = !1;
					};
					R.isValidAttribute = function (K, M, P) {
						ta || eb({});
						K = da(K);
						M = da(M);
						return Lb(K, M, P);
					};
					R.addHook = function (K, M) {
						"function" === typeof M && G(ia[K], M);
					};
					R.removeHook = function (K, M) {
						return void 0 !== M
							? ((M = I(ia[K], M)), -1 === M ? void 0 : a(ia[K], M, 1)[0])
							: J(ia[K]);
					};
					R.removeHooks = function (K) {
						ia[K] = [];
					};
					R.removeAllHooks = function () {
						ia = ob();
					};
					return R;
				}
				var c = Object,
					n = c.entries,
					f = c.setPrototypeOf,
					h = c.isFrozen,
					g = c.getPrototypeOf,
					t = c.getOwnPropertyDescriptor,
					r = Object,
					l = r.freeze;
				c = r.seal;
				var z = r.create;
				r = "undefined" !== typeof Reflect && Reflect;
				var F = r.apply,
					E = r.construct;
				l ||
					(l = function (T) {
						return T;
					});
				c ||
					(c = function (T) {
						return T;
					});
				F ||
					(F = function (T, O, R) {
						return T.apply(O, R);
					});
				E ||
					(E = function (T, O) {
						return new (Function.prototype.bind.apply(
							T,
							[null].concat($jscomp.arrayFromIterable(O)),
						))();
					});
				var H = p(Array.prototype.forEach),
					I = p(Array.prototype.lastIndexOf),
					J = p(Array.prototype.pop),
					G = p(Array.prototype.push),
					a = p(Array.prototype.splice),
					b = p(String.prototype.toLowerCase),
					d = p(String.prototype.toString),
					e = p(String.prototype.match),
					w = p(String.prototype.replace),
					B = p(String.prototype.indexOf),
					y = p(String.prototype.trim),
					D = p(Object.prototype.hasOwnProperty),
					L = p(RegExp.prototype.test),
					N = (function (T) {
						return function () {
							for (var O = arguments.length, R = Array(O), U = 0; U < O; U++)
								R[U] = arguments[U];
							return E(T, R);
						};
					})(TypeError),
					Q = l(
						"a abbr acronym address area article aside audio b bdi bdo big blink blockquote body br button canvas caption center cite code col colgroup content data datalist dd decorator del details dfn dialog dir div dl dt element em fieldset figcaption figure font footer form h1 h2 h3 h4 h5 h6 head header hgroup hr html i img input ins kbd label legend li main map mark marquee menu menuitem meter nav nobr ol optgroup option output p picture pre progress q rp rt ruby s samp section select shadow small source spacer span strike strong style sub summary sup table tbody td template textarea tfoot th thead time tr track tt u ul var video wbr".split(
							" ",
						),
					),
					V = l(
						"svg a altglyph altglyphdef altglyphitem animatecolor animatemotion animatetransform circle clippath defs desc ellipse filter font g glyph glyphref hkern image line lineargradient marker mask metadata mpath path pattern polygon polyline radialgradient rect stop style switch symbol text textpath title tref tspan view vkern".split(
							" ",
						),
					),
					X = l(
						"feBlend feColorMatrix feComponentTransfer feComposite feConvolveMatrix feDiffuseLighting feDisplacementMap feDistantLight feDropShadow feFlood feFuncA feFuncB feFuncG feFuncR feGaussianBlur feImage feMerge feMergeNode feMorphology feOffset fePointLight feSpecularLighting feSpotLight feTile feTurbulence".split(
							" ",
						),
					),
					ja = l(
						"animate color-profile cursor discard font-face font-face-format font-face-name font-face-src font-face-uri foreignobject hatch hatchpath mesh meshgradient meshpatch meshrow missing-glyph script set solidcolor unknown use".split(
							" ",
						),
					),
					ka = l(
						"math menclose merror mfenced mfrac mglyph mi mlabeledtr mmultiscripts mn mo mover mpadded mphantom mroot mrow ms mspace msqrt mstyle msub msup msubsup mtable mtd mtext mtr munder munderover mprescripts".split(
							" ",
						),
					),
					fa = l(
						"maction maligngroup malignmark mlongdiv mscarries mscarry msgroup mstack msline msrow semantics annotation annotation-xml mprescripts none".split(
							" ",
						),
					),
					na = l(["#text"]),
					ra = l(
						"accept action align alt autocapitalize autocomplete autopictureinpicture autoplay background bgcolor border capture cellpadding cellspacing checked cite class clear color cols colspan controls controlslist coords crossorigin datetime decoding default dir disabled disablepictureinpicture disableremoteplayback download draggable enctype enterkeyhint face for headers height hidden high href hreflang id inputmode integrity ismap kind label lang list loading loop low max maxlength media method min minlength multiple muted name nonce noshade novalidate nowrap open optimum pattern placeholder playsinline popover popovertarget popovertargetaction poster preload pubdate radiogroup readonly rel required rev reversed role rows rowspan spellcheck scope selected shape size sizes span srclang start src srcset step style summary tabindex title translate type usemap valign value width wrap xmlns slot".split(
							" ",
						),
					),
					pa = l(
						"accent-height accumulate additive alignment-baseline amplitude ascent attributename attributetype azimuth basefrequency baseline-shift begin bias by class clip clippathunits clip-path clip-rule color color-interpolation color-interpolation-filters color-profile color-rendering cx cy d dx dy diffuseconstant direction display divisor dur edgemode elevation end exponent fill fill-opacity fill-rule filter filterunits flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight fx fy g1 g2 glyph-name glyphref gradientunits gradienttransform height href id image-rendering in in2 intercept k k1 k2 k3 k4 kerning keypoints keysplines keytimes lang lengthadjust letter-spacing kernelmatrix kernelunitlength lighting-color local marker-end marker-mid marker-start markerheight markerunits markerwidth maskcontentunits maskunits max mask media method mode min name numoctaves offset operator opacity order orient orientation origin overflow paint-order path pathlength patterncontentunits patterntransform patternunits points preservealpha preserveaspectratio primitiveunits r rx ry radius refx refy repeatcount repeatdur restart result rotate scale seed shape-rendering slope specularconstant specularexponent spreadmethod startoffset stddeviation stitchtiles stop-color stop-opacity stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke stroke-width style surfacescale systemlanguage tabindex tablevalues targetx targety transform transform-origin text-anchor text-decoration text-rendering textlength type u1 u2 unicode values viewbox visibility version vert-adv-y vert-origin-x vert-origin-y width word-spacing wrap writing-mode xchannelselector ychannelselector x x1 x2 xmlns y y1 y2 z zoomandpan".split(
							" ",
						),
					),
					aa = l(
						"accent accentunder align bevelled close columnsalign columnlines columnspan denomalign depth dir display displaystyle encoding fence frame height href id largeop length linethickness lspace lquote mathbackground mathcolor mathsize mathvariant maxsize minsize movablelimits notation numalign open rowalign rowlines rowspacing rowspan rspace rquote scriptlevel scriptminsize scriptsizemultiplier selection separator separators stretchy subscriptshift supscriptshift symmetric voffset width xmlns".split(
							" ",
						),
					),
					qa = l([
						"xlink:href",
						"xml:id",
						"xlink:title",
						"xml:space",
						"xmlns:xlink",
					]);
				r = c(/\{\{[\w\W]*|[\w\W]*\}\}/gm);
				var ib = c(/<%[\w\W]*|[\w\W]*%>/gm),
					Ra = c(/\$\{[\w\W]*/gm),
					jb = c(/^data-[\-\w.\u00B7-\uFFFF]+$/),
					kb = c(/^aria-[\-\w]+$/),
					Pa = c(
						/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
					),
					Ha = c(/^(?:\w+script|data):/i),
					lb = c(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),
					za = c(/^html$/i);
				c = c(/^[a-z][.\w]*(-[.\w]+)+$/i);
				var Ka = Object.freeze({
						__proto__: null,
						ARIA_ATTR: kb,
						ATTR_WHITESPACE: lb,
						CUSTOM_ELEMENT: c,
						DATA_ATTR: jb,
						DOCTYPE_NAME: za,
						ERB_EXPR: ib,
						IS_ALLOWED_URI: Pa,
						IS_SCRIPT_OR_DATA: Ha,
						MUSTACHE_EXPR: r,
						TMPLIT_EXPR: Ra,
					}),
					Aa = {
						element: 1,
						attribute: 2,
						text: 3,
						cdataSection: 4,
						entityReference: 5,
						entityNode: 6,
						progressingInstruction: 7,
						comment: 8,
						document: 9,
						documentType: 10,
						documentFragment: 11,
						notation: 12,
					},
					ec = function (T, O) {
						if ("object" !== typeof T || "function" !== typeof T.createPolicy)
							return null;
						var R = null;
						O &&
							O.hasAttribute("data-tt-policy-suffix") &&
							(R = O.getAttribute("data-tt-policy-suffix"));
						O = "dompurify" + (R ? "#" + R : "");
						try {
							return T.createPolicy(O, {
								createHTML: function (U) {
									return U;
								},
								createScriptURL: function (U) {
									return U;
								},
							});
						} catch (U) {
							return (
								console.warn(
									"TrustedTypes policy " + O + " could not be created.",
								),
								null
							);
						}
					},
					ob = function () {
						return {
							afterSanitizeAttributes: [],
							afterSanitizeElements: [],
							afterSanitizeShadowDOM: [],
							beforeSanitizeAttributes: [],
							beforeSanitizeElements: [],
							beforeSanitizeShadowDOM: [],
							uponSanitizeAttribute: [],
							uponSanitizeElement: [],
							uponSanitizeShadowNode: [],
						};
					};
				return q();
			});
		},
		function (v, A) {
			function u() {
				throw Error("setTimeout has not been defined");
			}
			function p() {
				throw Error("clearTimeout has not been defined");
			}
			function m(z) {
				if (f === setTimeout) return setTimeout(z, 0);
				if ((f === u || !f) && setTimeout)
					return (f = setTimeout), setTimeout(z, 0);
				try {
					return f(z, 0);
				} catch (F) {
					try {
						return f.call(null, z, 0);
					} catch (E) {
						return f.call(this, z, 0);
					}
				}
			}
			function x(z) {
				if (h === clearTimeout) return clearTimeout(z);
				if ((h === p || !h) && clearTimeout)
					return (h = clearTimeout), clearTimeout(z);
				try {
					return h(z);
				} catch (F) {
					try {
						return h.call(null, z);
					} catch (E) {
						return h.call(this, z);
					}
				}
			}
			function k() {
				t &&
					r &&
					((t = !1), r.length ? (g = r.concat(g)) : (l = -1), g.length && q());
			}
			function q() {
				if (!t) {
					var z = m(k);
					t = !0;
					for (var F = g.length; F; ) {
						r = g;
						for (g = []; ++l < F; ) r && r[l].run();
						l = -1;
						F = g.length;
					}
					r = null;
					t = !1;
					x(z);
				}
			}
			function c(z, F) {
				this.fun = z;
				this.array = F;
			}
			function n() {}
			v = v.exports = {};
			try {
				var f = "function" === typeof setTimeout ? setTimeout : u;
			} catch (z) {
				f = u;
			}
			try {
				var h = "function" === typeof clearTimeout ? clearTimeout : p;
			} catch (z) {
				h = p;
			}
			var g = [],
				t = !1,
				r,
				l = -1;
			v.nextTick = function (z) {
				var F = Array(arguments.length - 1);
				if (1 < arguments.length)
					for (var E = 1; E < arguments.length; E++) F[E - 1] = arguments[E];
				g.push(new c(z, F));
				1 !== g.length || t || m(q);
			};
			c.prototype.run = function () {
				this.fun.apply(null, this.array);
			};
			v.title = "browser";
			v.browser = !0;
			v.env = {};
			v.argv = [];
			v.version = "";
			v.versions = {};
			v.on = n;
			v.addListener = n;
			v.once = n;
			v.off = n;
			v.removeListener = n;
			v.removeAllListeners = n;
			v.emit = n;
			v.prependListener = n;
			v.prependOnceListener = n;
			v.listeners = function (z) {
				return [];
			};
			v.binding = function (z) {
				throw Error("process.binding is not supported");
			};
			v.cwd = function () {
				return "/";
			};
			v.chdir = function (z) {
				throw Error("process.chdir is not supported");
			};
			v.umask = function () {
				return 0;
			};
		},
		function (v, A, u) {
			A.a = function () {
				ArrayBuffer.prototype.slice ||
					(ArrayBuffer.prototype.slice = function (p, m) {
						void 0 === p && (p = 0);
						void 0 === m && (m = this.byteLength);
						p = Math.floor(p);
						m = Math.floor(m);
						0 > p && (p += this.byteLength);
						0 > m && (m += this.byteLength);
						p = Math.min(Math.max(0, p), this.byteLength);
						m = Math.min(Math.max(0, m), this.byteLength);
						if (0 >= m - p) return new ArrayBuffer(0);
						var x = new ArrayBuffer(m - p),
							k = new Uint8Array(x);
						p = new Uint8Array(this, p, m - p);
						k.set(p);
						return x;
					});
			};
		},
		function (v, A, u) {
			A.a = function (p) {
				var m = {};
				decodeURIComponent(p.slice(1))
					.split("&")
					.forEach(function (x) {
						x = x.split("=", 2);
						m[x[0]] = x[1];
					});
				return m;
			};
		},
		function (v, A, u) {
			(function (p) {
				function m(H) {
					"function" !== typeof H && (H = new Function("".concat(H)));
					for (var I = Array(arguments.length - 1), J = 0; J < I.length; J++)
						I[J] = arguments[J + 1];
					r[t] = { callback: H, args: I };
					F(t);
					return t++;
				}
				function x(H) {
					delete r[H];
				}
				function k(H) {
					if (l) setTimeout(k, 0, H);
					else {
						var I = r[H];
						if (I) {
							l = !0;
							try {
								var J = I.callback,
									G = I.args;
								switch (G.length) {
									case 0:
										J();
										break;
									case 1:
										J(G[0]);
										break;
									case 2:
										J(G[0], G[1]);
										break;
									case 3:
										J(G[0], G[1], G[2]);
										break;
									default:
										J.apply(void 0, G);
								}
							} finally {
								x(H), (l = !1);
							}
						}
					}
				}
				function q() {
					F = function (H) {
						p.nextTick(function () {
							k(H);
						});
					};
				}
				function c() {
					if (g.postMessage && !g.importScripts) {
						var H = !0,
							I = g.onmessage;
						g.onmessage = function () {
							H = !1;
						};
						g.postMessage("", "*");
						g.onmessage = I;
						return H;
					}
				}
				function n() {
					var H = "setImmediate$".concat(Math.random(), "$"),
						I = function (J) {
							(J.source !== g && J.source !== g.parent) ||
								"string" !== typeof J.data ||
								0 !== J.data.indexOf(H) ||
								k(+J.data.slice(H.length));
						};
					g.addEventListener
						? g.addEventListener("message", I, !1)
						: g.attachEvent("onmessage", I);
					F = function (J) {
						g.postMessage(H + J, "*");
					};
				}
				function f() {
					var H = z.documentElement;
					F = function (I) {
						var J = z.createElement("script");
						J.onreadystatechange = function () {
							k(I);
							J.onreadystatechange = null;
							H.removeChild(J);
							J = null;
						};
						H.appendChild(J);
					};
				}
				function h() {
					F = function (H) {
						setTimeout(k, 0, H);
					};
				}
				var g = "undefined" === typeof window ? self : window,
					t = 1,
					r = {},
					l = !1,
					z = g.document,
					F,
					E = Object.getPrototypeOf && Object.getPrototypeOf(g);
				E = E && E.setTimeout ? E : g;
				"[object process]" === {}.toString.call(g.process)
					? q()
					: c()
						? n()
						: z && "onreadystatechange" in z.createElement("script")
							? f()
							: h();
				E.setImmediate = m;
				E.clearImmediate = x;
				A.a = { setImmediate: m, clearImmediate: x };
			}).call(this, u(14));
		},
		function (v, A, u) {
			var p = u(0),
				m = u(2);
			v = (function () {
				function x(k, q) {
					this.name = k;
					this.comObj = q;
					this.callbackIndex = 1;
					this.postMessageTransfers = !0;
					this.callbacksCapabilities = {};
					this.actionHandler = {};
					this.actionHandlerAsync = {};
					this.pdfnetCommandChain = this.nextAsync = null;
					this.pdfnetActiveCommands = new Set();
					this.actionHandler.console_log = [
						function (c) {
							Object(p.d)(c);
						},
					];
					this.actionHandler.console_error = [
						function (c) {
							Object(p.c)(c);
						},
					];
					this.actionHandler.workerLoaded = [function () {}];
					this.msgHandler = this.handleMessage.bind(this);
					q.addEventListener("message", this.msgHandler);
				}
				x.prototype.on = function (k, q, c) {
					var n = this.actionHandler;
					n[k] &&
						Object(p.c)(
							'There is already an actionName called "'.concat(k, '"'),
						);
					n[k] = [q, c];
				};
				x.prototype.clearActionHandlers = function () {
					this.actionHandler = {};
					this.comObj.removeEventListener("message", this.msgHandler);
				};
				x.prototype.reset = function () {
					this.clearActionHandlers();
					this.comObj.reset && this.comObj.reset();
				};
				x.prototype.replace = function (k, q, c) {
					this.actionHandler[k] = [q, c];
				};
				x.prototype.onAsync = function (k, q, c) {
					var n = this.actionHandlerAsync;
					n[k] &&
						Object(p.c)(
							'There is already an actionName called "'.concat(k, '"'),
						);
					n[k] = [q, c];
				};
				x.prototype.replaceAsync = function (k, q, c) {
					var n = this.actionHandlerAsync,
						f = this.actionHandler;
					f[k] && delete f[k];
					n[k] = [q, c];
				};
				x.prototype.onNextAsync = function (k) {
					this.nextAsync = k;
				};
				x.prototype.send = function (k, q) {
					this.postMessage({ action: k, data: q });
				};
				x.prototype.getNextId = function () {
					return this.callbackIndex++;
				};
				x.prototype.sendWithPromise = function (k, q, c) {
					var n = this.getNextId();
					k = { action: k, data: q, callbackId: n, priority: c };
					q = window.createPromiseCapability();
					this.callbacksCapabilities[n] = q;
					try {
						this.postMessage(k);
					} catch (f) {
						q.reject(f);
					}
					return q.promise;
				};
				x.prototype.sendWithPromiseReturnId = function (k, q, c) {
					var n = this.getNextId();
					k = { action: k, data: q, callbackId: n, priority: c };
					q = window.createPromiseCapability();
					this.callbacksCapabilities[n] = q;
					try {
						this.postMessage(k);
					} catch (f) {
						q.reject(f);
					}
					return { promise: q.promise, callbackId: n };
				};
				x.prototype.sendWithPromiseWithId = function (k, q, c) {
					q > this.callbackIndex &&
						Object(p.c)(
							"Can't reuse callbackId "
								.concat(q, " lesser than callbackIndex ")
								.concat(this.callbackIndex),
						);
					q in this.callbacksCapabilities &&
						Object(p.c)(
							"Can't reuse callbackId ".concat(
								q,
								". There is a capability waiting to be resolved. ",
							),
						);
					k = { action: k, data: c, callbackId: q };
					c = window.createPromiseCapability();
					this.callbacksCapabilities[q] = c;
					try {
						this.postMessage(k);
					} catch (n) {
						c.reject(n);
					}
					return c.promise;
				};
				x.prototype.sendError = function (k, q) {
					if (k.message || k.errorData) {
						k.message && k.message.message && (k.message = k.message.message);
						var c = k.errorData;
						k = {
							type: k.type ? k.type : "JavascriptError",
							message: k.message,
						};
						c &&
							Object.keys(c).forEach(function (n) {
								c.hasOwnProperty(n) && (k[n] = c[n]);
							});
					}
					this.postMessage({ isReply: !0, callbackId: q, error: k });
				};
				x.prototype.getPromise = function (k) {
					if (k in this.callbacksCapabilities)
						return this.callbacksCapabilities[k];
					Object(p.c)("Cannot get promise for callback ".concat(k));
				};
				x.prototype.cancelPromise = function (k) {
					if (k in this.callbacksCapabilities) {
						var q = this.callbacksCapabilities[k];
						delete this.callbacksCapabilities[k];
						this.pdfnetActiveCommands.has(k) &&
							this.pdfnetActiveCommands.delete(k);
						q.reject({
							type: "Cancelled",
							message: "Request has been cancelled.",
						});
						this.postMessage({
							action: "actionCancel",
							data: { callbackId: k },
						});
					} else Object(p.b)("Cannot cancel callback ".concat(k));
				};
				x.prototype.postMessage = function (k) {
					"officeeditor" !== this.name &&
						Object(m.a)("enableWorkerLogs") &&
						Object(p.d)(
							"PDFWorker",
							"".concat(performance.now(), " Sent ").concat(JSON.stringify(k)),
						);
					if (this.postMessageTransfers) {
						var q = this.getTransfersArray(k);
						this.comObj.postMessage(k, q);
					} else this.comObj.postMessage(k);
				};
				x.prototype.getObjectTransfers = function (k, q) {
					var c = this;
					null !== k &&
						"object" === typeof k &&
						(k instanceof Uint8Array
							? q.push(k.buffer)
							: k instanceof ArrayBuffer
								? q.push(k)
								: Object.keys(k).forEach(function (n) {
										k.hasOwnProperty(n) && c.getObjectTransfers(k[n], q);
									}));
				};
				x.prototype.getTransfersArray = function (k) {
					var q = [];
					this.getObjectTransfers(k, q);
					return 0 === q.length ? void 0 : q;
				};
				x.prototype.handleMessage = function (k) {
					var q = this,
						c = k.data;
					"officeeditor" !== this.name &&
						Object(m.a)("enableWorkerLogs") &&
						Object(p.d)(
							"PDFWorker",
							""
								.concat(performance.now(), " Received ")
								.concat(JSON.stringify(c)),
						);
					var n = this.actionHandler,
						f = this.actionHandlerAsync;
					k = this.callbacksCapabilities;
					var h = this.pdfnetActiveCommands;
					if (c.isReply)
						(n = c.callbackId),
							n in k
								? ((f = k[n]),
									delete k[n],
									h.has(n) && h.delete(n),
									"error" in c ? f.reject(c.error) : f.resolve(c.data))
								: Object(p.a)("Cannot resolve callback ".concat(n));
					else if (c.action in n) {
						var g = n[c.action];
						c.callbackId
							? Promise.resolve()
									.then(function () {
										return g[0].call(g[1], c.data);
									})
									.then(
										function (t) {
											q.postMessage({
												isReply: !0,
												callbackId: c.callbackId,
												data: t,
											});
										},
										function (t) {
											q.sendError(t, c.callbackId);
										},
									)
							: g[0].call(g[1], c.data);
					} else
						c.action in f
							? ((g = f[c.action]),
								c.callbackId
									? g[0].call(g[1], c).then(
											function (t) {
												q.postMessage({
													isReply: !0,
													callbackId: c.callbackId,
													data: t,
												});
												q.nextAsync();
											},
											function (t) {
												q.sendError(t, c.callbackId);
												q.nextAsync();
											},
										)
									: g[0].call(g[1], c).then(
											function () {
												q.nextAsync();
											},
											function () {
												q.nextAsync();
											},
										))
							: Object(p.c)("Unknown action from worker: ".concat(c.action));
				};
				return x;
			})();
			A.a = v;
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return c;
			});
			var p = u(3),
				m = u(11),
				x = u(7),
				k = u(8),
				q = (function () {
					function n(f) {
						var h = this;
						this.promise = f.then(function (g) {
							h.response = g;
							h.status = 200;
						});
					}
					n.prototype.addEventListener = function (f, h) {
						this.promise.then(h);
					};
					return n;
				})(),
				c = function (n, f, h, g) {
					if (Object(x.a)() && !h) {
						self.Module.instantiateWasm = function (r, l) {
							return Object(m.a)(
								"".concat(n, "Wasm.wasm"),
								r,
								f["Wasm.wasm"],
								g,
							).then(function (z) {
								l(z.instance);
							});
						};
						if (f.disableObjectURLBlobs) {
							importScripts("".concat(n, "Wasm.js"));
							return;
						}
						h = Object(p.b)(
							"".concat(n, "Wasm.js.mem"),
							f["Wasm.js.mem"],
							!1,
							!1,
						);
					} else {
						if (f.disableObjectURLBlobs) {
							importScripts(
								"".concat(
									(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + n,
									".js",
								),
							);
							return;
						}
						h = Object(p.b)(
							"".concat(
								(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + n,
								".js.mem",
							),
							f[".js.mem"],
							!1,
						);
						var t = Object(p.b)(
							"".concat(
								(self.Module.memoryInitializerPrefixURL
									? self.Module.memoryInitializerPrefixURL
									: "") + n,
								".mem",
							),
							f[".mem"],
							!0,
							!0,
						);
						self.Module.memoryInitializerRequest = new q(t);
					}
					h = new Blob([h], { type: "application/javascript" });
					importScripts(Object(k.a)(URL.createObjectURL(h)));
				};
		},
		function (v, A, u) {
			u.d(A, "a", function () {
				return p;
			});
			var p = "optimized/";
		},
		function (v, A, u) {
			v.exports = u(22);
		},
		function (v, A, u) {
			u.r(A);
			u(7);
			v = u(15);
			u(23);
			u(24);
			u(27);
			u(28);
			u(29);
			u(30);
			u(31);
			Object(v.a)();
		},
		function (v, A, u) {
			(function (p) {
				"undefined" === typeof p.crypto &&
					(p.crypto = {
						getRandomValues: function (m) {
							for (var x = 0; x < m.length; x++) m[x] = 256 * Math.random();
						},
					});
			})("undefined" === typeof window ? self : window);
		},
		function (v, A, u) {
			(function (p, m) {
				function x(k) {
					"@babel/helpers - typeof";
					return (
						(x =
							"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
								? function (q) {
										return typeof q;
									}
								: function (q) {
										return q &&
											"function" == typeof Symbol &&
											q.constructor === Symbol &&
											q !== Symbol.prototype
											? "symbol"
											: typeof q;
									}),
						x(k)
					);
				}
				(function (k) {
					function q() {
						for (var y = 0; y < w.length; y++) w[y][0](w[y][1]);
						w = [];
						B = !1;
					}
					function c(y, D) {
						w.push([y, D]);
						B || ((B = !0), e(q, 0));
					}
					function n(y, D) {
						function L(Q) {
							g(D, Q);
						}
						function N(Q) {
							r(D, Q);
						}
						try {
							y(L, N);
						} catch (Q) {
							N(Q);
						}
					}
					function f(y) {
						var D = y.owner,
							L = D.state_;
						D = D.data_;
						var N = y[L];
						y = y.then;
						if ("function" === typeof N) {
							L = a;
							try {
								D = N(D);
							} catch (Q) {
								r(y, Q);
							}
						}
						h(y, D) || (L === a && g(y, D), L === b && r(y, D));
					}
					function h(y, D) {
						var L;
						try {
							if (y === D)
								throw new TypeError(
									"A promises callback cannot return that same promise.",
								);
							if (D && ("function" === typeof D || "object" === x(D))) {
								var N = D.then;
								if ("function" === typeof N)
									return (
										N.call(
											D,
											function (Q) {
												L || ((L = !0), D !== Q ? g(y, Q) : t(y, Q));
											},
											function (Q) {
												L || ((L = !0), r(y, Q));
											},
										),
										!0
									);
							}
						} catch (Q) {
							return L || r(y, Q), !0;
						}
						return !1;
					}
					function g(y, D) {
						(y !== D && h(y, D)) || t(y, D);
					}
					function t(y, D) {
						y.state_ === J && ((y.state_ = G), (y.data_ = D), c(z, y));
					}
					function r(y, D) {
						y.state_ === J && ((y.state_ = G), (y.data_ = D), c(F, y));
					}
					function l(y) {
						var D = y.then_;
						y.then_ = void 0;
						for (y = 0; y < D.length; y++) f(D[y]);
					}
					function z(y) {
						y.state_ = a;
						l(y);
					}
					function F(y) {
						y.state_ = b;
						l(y);
					}
					function E(y) {
						if ("function" !== typeof y)
							throw new TypeError(
								"Promise constructor takes a function argument",
							);
						if (!(this instanceof E))
							throw new TypeError(
								"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.",
							);
						this.then_ = [];
						n(y, this);
					}
					k.createPromiseCapability = function () {
						var y = {};
						y.promise = new E(function (D, L) {
							y.resolve = D;
							y.reject = L;
						});
						return y;
					};
					var H = k.Promise,
						I =
							H &&
							"resolve" in H &&
							"reject" in H &&
							"all" in H &&
							"race" in H &&
							(function () {
								var y;
								new H(function (D) {
									y = D;
								});
								return "function" === typeof y;
							})();
					"undefined" !== typeof exports && exports
						? ((exports.Promise = I ? H : E), (exports.Polyfill = E))
						: "function" === typeof define && u(26)
							? define(function () {
									return I ? H : E;
								})
							: I || (k.Promise = E);
					var J = "pending",
						G = "sealed",
						a = "fulfilled",
						b = "rejected",
						d = function () {},
						e = "undefined" !== typeof m ? m : setTimeout,
						w = [],
						B;
					E.prototype = {
						constructor: E,
						state_: J,
						then_: null,
						data_: void 0,
						then: function (y, D) {
							y = {
								owner: this,
								then: new this.constructor(d),
								fulfilled: y,
								rejected: D,
							};
							this.state_ === a || this.state_ === b
								? c(f, y)
								: this.then_.push(y);
							return y.then;
						},
						catch: function (y) {
							return this.then(null, y);
						},
					};
					E.all = function (y) {
						if ("[object Array]" !== Object.prototype.toString.call(y))
							throw new TypeError("You must pass an array to Promise.all().");
						return new this(function (D, L) {
							function N(ka) {
								V++;
								return function (fa) {
									Q[ka] = fa;
									--V || D(Q);
								};
							}
							for (var Q = [], V = 0, X = 0, ja; X < y.length; X++)
								(ja = y[X]) && "function" === typeof ja.then
									? ja.then(N(X), L)
									: (Q[X] = ja);
							V || D(Q);
						});
					};
					E.race = function (y) {
						if ("[object Array]" !== Object.prototype.toString.call(y))
							throw new TypeError("You must pass an array to Promise.race().");
						return new this(function (D, L) {
							for (var N = 0, Q; N < y.length; N++)
								(Q = y[N]) && "function" === typeof Q.then
									? Q.then(D, L)
									: D(Q);
						});
					};
					E.resolve = function (y) {
						return y && "object" === x(y) && y.constructor === this
							? y
							: new this(function (D) {
									D(y);
								});
					};
					E.reject = function (y) {
						return new this(function (D, L) {
							L(y);
						});
					};
				})(
					"undefined" !== typeof window
						? window
						: "undefined" !== typeof p
							? p
							: "undefined" !== typeof self
								? self
								: void 0,
				);
			}).call(this, u(12), u(10).setImmediate);
		},
		function (v, A, u) {
			(function (p, m) {
				(function (x, k) {
					function q(J) {
						delete z[J];
					}
					function c(J) {
						if (F) setTimeout(c, 0, J);
						else {
							var G = z[J];
							if (G) {
								F = !0;
								try {
									var a = G.callback,
										b = G.args;
									switch (b.length) {
										case 0:
											a();
											break;
										case 1:
											a(b[0]);
											break;
										case 2:
											a(b[0], b[1]);
											break;
										case 3:
											a(b[0], b[1], b[2]);
											break;
										default:
											a.apply(k, b);
									}
								} finally {
									q(J), (F = !1);
								}
							}
						}
					}
					function n() {
						H = function (J) {
							m.nextTick(function () {
								c(J);
							});
						};
					}
					function f() {
						if (x.postMessage && !x.importScripts) {
							var J = !0,
								G = x.onmessage;
							x.onmessage = function () {
								J = !1;
							};
							x.postMessage("", "*");
							x.onmessage = G;
							return J;
						}
					}
					function h() {
						var J = "setImmediate$" + Math.random() + "$",
							G = function (a) {
								a.source === x &&
									"string" === typeof a.data &&
									0 === a.data.indexOf(J) &&
									c(+a.data.slice(J.length));
							};
						x.addEventListener
							? x.addEventListener("message", G, !1)
							: x.attachEvent("onmessage", G);
						H = function (a) {
							x.postMessage(J + a, "*");
						};
					}
					function g() {
						var J = new MessageChannel();
						J.port1.onmessage = function (G) {
							c(G.data);
						};
						H = function (G) {
							J.port2.postMessage(G);
						};
					}
					function t() {
						var J = E.documentElement;
						H = function (G) {
							var a = E.createElement("script");
							a.onreadystatechange = function () {
								c(G);
								a.onreadystatechange = null;
								J.removeChild(a);
								a = null;
							};
							J.appendChild(a);
						};
					}
					function r() {
						H = function (J) {
							setTimeout(c, 0, J);
						};
					}
					if (!x.setImmediate) {
						var l = 1,
							z = {},
							F = !1,
							E = x.document,
							H,
							I = Object.getPrototypeOf && Object.getPrototypeOf(x);
						I = I && I.setTimeout ? I : x;
						"[object process]" === {}.toString.call(x.process)
							? n()
							: f()
								? h()
								: x.MessageChannel
									? g()
									: E && "onreadystatechange" in E.createElement("script")
										? t()
										: r();
						I.setImmediate = function (J) {
							"function" !== typeof J && (J = new Function("" + J));
							for (
								var G = Array(arguments.length - 1), a = 0;
								a < G.length;
								a++
							)
								G[a] = arguments[a + 1];
							z[l] = { callback: J, args: G };
							H(l);
							return l++;
						};
						I.clearImmediate = q;
					}
				})(
					"undefined" === typeof self
						? "undefined" === typeof p
							? this
							: p
						: self,
				);
			}).call(this, u(12), u(14));
		},
		function (v, A) {
			v.exports = {};
		},
		function (v, A, u) {
			(function (p) {
				var m = function (x, k) {
					var q = function h(f) {
							f = this["catch"](f);
							return {
								cancel: k,
								promise: f,
								then: c.bind(f),
								catch: h.bind(f),
							};
						},
						c = function t(h, g) {
							h = this.then(h, g);
							return {
								cancel: k,
								promise: h,
								then: t.bind(h),
								catch: q.bind(h),
							};
						};
					return { cancel: k, promise: x, then: c.bind(x), catch: q.bind(x) };
				};
				p.CancellablePromise = function (x, k) {
					var q = !1,
						c,
						n = new Promise(function (f, h) {
							c = function () {
								q || (k(), h("cancelled"));
							};
							new Promise(x).then(
								function (g) {
									q = !0;
									f(g);
								},
								function (g) {
									q = !0;
									h(g);
								},
							);
						});
					return m(n, c);
				};
				p.CancellablePromise.all = function (x) {
					var k = Promise.all(x);
					return m(k, function () {
						x.forEach(function (q) {
							q.cancel && q.cancel();
						});
					});
				};
			})("undefined" === typeof self ? void 0 : self);
		},
		function (v, A, u) {
			(function (p, m) {
				function x(n) {
					"@babel/helpers - typeof";
					return (
						(x =
							"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
								? function (f) {
										return typeof f;
									}
								: function (f) {
										return f &&
											"function" == typeof Symbol &&
											f.constructor === Symbol &&
											f !== Symbol.prototype
											? "symbol"
											: typeof f;
									}),
						x(n)
					);
				}
				function k(n, f) {
					var h = Object.keys(n);
					if (Object.getOwnPropertySymbols) {
						var g = Object.getOwnPropertySymbols(n);
						f &&
							(g = g.filter(function (t) {
								return Object.getOwnPropertyDescriptor(n, t).enumerable;
							}));
						h.push.apply(h, g);
					}
					return h;
				}
				function q(n) {
					for (var f = 1; f < arguments.length; f++) {
						var h = null != arguments[f] ? arguments[f] : {};
						f % 2
							? k(Object(h), !0).forEach(function (g) {
									var t = h[g];
									a: if ("object" === x(g) && null !== g) {
										var r = g[Symbol.toPrimitive];
										if (void 0 !== r) {
											g = r.call(g, "string");
											if ("object" !== x(g)) break a;
											throw new TypeError(
												"@@toPrimitive must return a primitive value.",
											);
										}
										g = String(g);
									}
									g = "symbol" === x(g) ? g : String(g);
									g in n
										? Object.defineProperty(n, g, {
												value: t,
												enumerable: !0,
												configurable: !0,
												writable: !0,
											})
										: (n[g] = t);
								})
							: Object.getOwnPropertyDescriptors
								? Object.defineProperties(
										n,
										Object.getOwnPropertyDescriptors(h),
									)
								: k(Object(h)).forEach(function (g) {
										Object.defineProperty(
											n,
											g,
											Object.getOwnPropertyDescriptor(h, g),
										);
									});
					}
					return n;
				}
				var c = u(1);
				(function (n) {
					n.Module = {
						INITIAL_MEMORY: 50331648,
						noExitRuntime: !0,
						devicePixelRatio: 1,
						cur_doc: null,
						cachePtrSize: 0,
						hasBufOwnership: !0,
						loaded: !1,
						initCb: null,
						cachePtr: null,
						cleanupState: null,
						docs: {},
						postEvent: function (f, h, g) {
							Module.workerMessageHandler.send("event", {
								docId: f,
								type: h,
								data: g,
							});
						},
						postProgressiveRenderingStartEvent: function (f, h) {
							Module.postEvent(f, "progressiveRenderingStart", { pageNum: h });
						},
						postPagesUpdatedEvent: function (f, h, g, t) {
							f = { pageDimensions: Module.GetPageDimensions(f) };
							if (g)
								for (var r = 0; r < g.length; ++r)
									g[r] in f.pageDimensions
										? ((f.pageDimensions[g[r]].contentChanged = !0),
											t && (f.pageDimensions[g[r]].annotationsUnchanged = !0))
										: console.warn("Invalid Page Number ".concat(g[r]));
							Module.postEvent(h, "pagesUpdated", f);
							return f;
						},
						postPagesRenamedEvent: function (f, h) {
							var g = {};
							f = Module.PDFDocGetPageIterator(f, 1);
							for (var t = 1; Module.IteratorHasNext(f); ++t) {
								var r = Module.stackSave(),
									l = Module.IteratorCurrent(f);
								g[t] = Module.PageGetId(l);
								Module.IteratorNext(f);
								Module.stackRestore(r);
							}
							Module.postEvent(h, "pagesRenamed", { pageNumToId: g });
						},
						GetIndividualPageDimensions: function (f, h, g) {
							f = Module.PageGetPageInfo(g);
							f.id = Module.PageGetId(g);
							return f;
						},
						GetPageDimensionsRange: function (f, h, g) {
							for (
								var t = {}, r = Module.PDFDocGetPageIterator(f, h);
								h < g && Module.IteratorHasNext(r);
								++h
							) {
								var l = Module.stackSave(),
									z = Module.IteratorCurrent(r);
								t[h] = this.GetIndividualPageDimensions(f, h, z);
								Module.IteratorNext(r);
								Module.stackRestore(l);
							}
							return t;
						},
						GetPageDimensionsContentChangedList: function (f, h) {
							h.sort(function (H, I) {
								return H - I;
							});
							for (
								var g = {},
									t = h[0],
									r = h[h.length - 1],
									l = 0,
									z = Module.PDFDocGetPageIterator(f, t);
								t <= r && Module.IteratorHasNext(z);
								++t
							) {
								if (h[l] == t) {
									for (++l; h[l] == t; ) ++l;
									var F = Module.stackSave(),
										E = Module.IteratorCurrent(z);
									E = this.GetIndividualPageDimensions(f, t, E);
									E.contentChanged = !0;
									g[t] = E;
									Module.stackRestore(F);
								}
								Module.IteratorNext(z);
							}
							return g;
						},
						GetPageDimensions: function (f) {
							try {
								var h = Module.stackSave();
								var g = Module.GetPageCount(f);
								if (0 === g) throw "This document has no pages.";
								return Module.GetPageDimensionsRange(f, 1, g + 1);
							} finally {
								Module.stackRestore(h);
							}
						},
						loadDoc: function (f, h) {
							"undefined" === typeof Module && this._main();
							var g = null;
							try {
								var t = Module.stackSave();
								f.customHandlerId &&
									Module._TRN_PDFNetAddPDFTronCustomHandler(f.customHandlerId);
								h = Module.CreateDoc(f, h);
								var r = Module.GetDoc(h);
								if (Module.PDFDocInitSecurityHandler(r))
									return {
										docId: h,
										pageDimensions: Module.GetPageDimensions(r),
									};
								g = {
									type: "NeedsPassword",
									errorData: { docId: h },
									message: "This document requires a password",
								};
							} catch (l) {
								g = { type: "InvalidPDF", message: l };
							} finally {
								Module.stackRestore(t);
							}
							throw g;
						},
						loadCanvas: function (f, h, g, t, r, l, z, F) {
							var E = Module.GetDoc(f),
								H = Module.docs[f].chunkStorage,
								I = Promise.resolve();
							if (H && 2 === F.overprintMode) {
								var J = Module.GetDownloadData(E);
								if (!J.docInfoRequested) {
									var G = Module.allocate(4, "i8", Module.ALLOC_STACK);
									REX(Module._TRN_DownloaderPrefetchDocInfo(J.downloader, G));
									0 == Module.getValue(G, "i8") &&
										(I = J.docInfoPromiseCapability.promise);
									J.docInfoRequested = !0;
								}
							}
							return new Promise(function (a, b) {
								var d = h + 1,
									e = function () {
										a(
											I.then(function () {
												return Module.RasterizePage(E, d, g, t, l, r, z, F, f);
											}),
										);
									};
								if (H) {
									var w = Module.GetDownloadData(E).downloader,
										B = H.getRequiredChunkOffsetArrays(w, d);
									H.keepChunks(B.have);
									w = function () {
										var y = H.getChunks(B.missing);
										Module.loadPromise = y
											.then(function () {
												var D = Module.loadPromise.cancelled;
												Module.loadPromise = null;
												D || e();
											})
											["catch"](function (D) {
												"cancelled" !== D ? b(D) : (Module.loadPromise = null);
											});
									};
									Module.loadPromise ? Module.loadPromise.then(w, w) : w();
								} else e();
							});
						},
						loadResources: function (f, h) {
							Module.Initialize(h);
							Object(c.b)("worker", "PDFNet initialized!");
							Module._TRN_PDFNetSetDefaultDiskCachingEnabled(!1);
							f = new Uint8Array(f);
							Module.PDFNetSetResourceData(f);
						},
						onRuntimeInitialized: function () {
							"undefined" === typeof Module &&
								(("undefined" !== typeof window ? window : self).Module = {});
							(function (f) {
								f.PDFDocExportXFDF = function (h, g) {
									h = Module.GetDoc(h);
									var t = Module.stackSave();
									try {
										var r = g
											? Module.PDFDocFDFExtract(h, g)
											: Module.PDFDocFDFExtract(h);
										var l = Module.FDFDocSaveAsXFDF(r);
										Module.stackRestore(t);
									} catch (z) {
										throw (Module.stackRestore(t), z);
									}
									return l;
								};
								f.PageArrayToPageSet = function (h) {
									var g = Module.stackSave();
									try {
										var t = Module.PageSetCreate();
										for (var r = 0; r < h.length; ++r)
											Module.PageSetAddPage(t, h[r]);
										Module.stackRestore(g);
									} catch (l) {
										throw (Module.stackRestore(g), l);
									}
									return t;
								};
								f.cancelCurrent = function () {
									var h = Module.loadPromise;
									return h
										? (h.cancel(), (h.cancelled = !0))
										: (h = Module.cleanupState)
											? (p(h.timeout),
												h.cleanupArr.reverse().forEach(function (g) {
													g();
												}),
												(Module.cleanupState = null),
												!0)
											: !1;
								};
								f.SetWorkerRestartCallback = function (h) {
									Module.workerRestartCallback = h;
								};
								f.XFDFMerge = function (h, g, t) {
									if (g) {
										var r = Module.GetDoc(h),
											l = [];
										try {
											Object(c.b)(
												"worker",
												"Merge XFDF of length ".concat(g.length),
											);
											var z = Module.GetUStringFromJSString(g, !0);
											l.push(function () {
												Module.UStringDestroy(z);
											});
											var F = Module.allocate(4, "i8", Module.ALLOC_STACK);
											REX(Module._TRN_FDFDocCreateFromXFDF(z, F));
											var E = Module.getValue(F, "i8*");
											l.push(function () {
												Module.FDFDocDestroy(E);
											});
											var H = Module.PDFDocFDFUpdate(r, E, t);
											H &&
												H.length &&
												Module.postEvent(h, "apRefChanged", {
													apRefChanges: H,
												});
										} finally {
											l.reverse().forEach(function (I) {
												I();
											});
										}
									}
								};
								f.MergeXFDF = function (h, g, t) {
									return new Promise(function (r, l) {
										var z = [];
										try {
											var F = Module.stackSave();
											z[z.length] = function () {
												Module.stackRestore(F);
											};
											Module.XFDFMerge(h, g, t);
											z.forEach(function (E) {
												E();
											});
											r({});
										} catch (E) {
											z.forEach(function (H) {
												H();
											}),
												l(E);
										}
									});
								};
								f.CreateBufferFile = function (h, g, t) {
									Module.MakeDev(h);
									var r = new ArrayBuffer(g);
									r = new Uint8Array(r);
									t = t ? 0 : 255;
									for (var l = 0; l < g; ++l) r[l] = t;
									Module.docs[h] = { buffer: r };
								};
								f.ReadBufferFile = function (h, g) {
									var t = Module.docs[h].buffer;
									g &&
										(Module.docs[h].buffer = new Uint8Array(t.buffer.slice(0)));
									return t;
								};
								f.RemoveBufferFile = function (h) {
									Module.docs[h] = null;
								};
								f.SaveHelper = function (h, g, t) {
									t = "undefined" === typeof t ? 2 : t;
									Module.MakeDev(g);
									var r = Module._TRN_PDFDocSave(
										h,
										Module.GetUStringFromJSString(g),
										t,
									);
									Module.docs[g].sink = null;
									REX(r);
									t & 16 && Module.postPagesRenamedEvent(h, g);
									return Module.docs[g].buffer.buffer;
								};
								f.SaveDoc = function (h, g, t, r, l, z, F, E, H) {
									return new Promise(function (I, J) {
										var G = [];
										try {
											var a = Module.GetDoc(h),
												b = Module.stackSave();
											G[G.length] = function () {
												Module.stackRestore(b);
											};
											Module.XFDFMerge(h, g, F);
											var d = Module.allocate(8, "i8", Module.ALLOC_STACK),
												e = Module.allocate(
													Module.intArrayFromString(
														'{"UseNonStandardRotation": true}',
													),
													"i8",
													Module.ALLOC_STACK,
												);
											Module.setValue(d, e, "i8*");
											Module.setValue(d + 4, 0, "i32");
											Module._TRN_PDFDocRefreshAnnotAppearances(a, d);
											if (z) {
												d = function (aa) {
													aa = new Uint8Array(aa);
													n.FS.writeFile("watermarkFile", aa);
													aa = Module.ImageCreateFromFile(
														a,
														Module.GetUStringFromJSString("watermarkFile"),
													);
													n.FS.unlink("watermarkFile");
													return aa;
												};
												var w = Module.ElementBuilderCreate();
												G.push(function () {
													Module.ElementBuilderDestroy(w);
												});
												var B = Module.ElementWriterCreate();
												G.push(function () {
													Module.ElementWriterDestroy(B);
												});
												try {
													if (!z.hasOwnProperty("default"))
														throw Error(
															"Watermark dictionary has no 'default' key!",
														);
													var y = d(z["default"]),
														D = Module.PDFDocGetPageIterator(a, 1);
													for (e = -1; Module.IteratorHasNext(D); ) {
														var L = Module.IteratorCurrent(D);
														Module.IteratorNext(D);
														e++;
														var N = e.toString();
														try {
															var Q = void 0;
															if (z.hasOwnProperty(N)) {
																var V = z[N];
																if (V) Q = d(V);
																else continue;
															} else Q = y;
															var X = Module.PageGetPageInfo(L),
																ja = Module.ElementBuilderCreateImage(
																	w,
																	Q,
																	0,
																	0,
																	X.width,
																	X.height,
																);
															Module.ElementWriterBegin(B, L);
															Module.ElementWriterWritePlacedElement(B, ja);
															Module.ElementWriterEnd(B);
														} catch (aa) {
															console.warn(
																"Watermark for page " +
																	N +
																	"can not be added due to error: " +
																	aa,
															);
														}
													}
												} catch (aa) {
													console.warn(
														"Watermarks can not be added due to error: " + aa,
													);
												}
											}
											if (E) {
												var ka = Module.SecurityHandlerCreate(H);
												ka &&
													(Module.SecurityHandlerChangeUserPasswordUString(
														ka,
														E,
													),
													Module.PDFDocSetSecurityHandler(a, ka));
											}
											y = 0;
											if (r) {
												var fa = Module.PDFDocGetRoot(a);
												(y = Module.ObjFindObj(fa, "OpenAction")) &&
													Module.ObjPut(fa, "__OpenActionBackup__", y);
												var na = Module.ObjPutDict(fa, "OpenAction");
												Module.ObjPutName(na, "Type", "Action");
												Module.ObjPutName(na, "S", "JavaScript");
												Module.ObjPutString(na, "JS", "this.print()");
											}
											var ra = Module.SaveHelper(a, h, l);
											r &&
												(y
													? Module.ObjPut(
															fa,
															"OpenAction",
															Module.ObjFindObj(fa, "__OpenActionBackup__"),
														)
													: Module.ObjErase(fa, "OpenAction"));
											G.reverse().forEach(function (aa) {
												aa();
											});
											if (t) I({ fileData: ra });
											else {
												var pa = ra.slice(0);
												I({ fileData: pa });
											}
										} catch (aa) {
											G.reverse().forEach(function (qa) {
												qa();
											}),
												J(aa);
										}
									});
								};
								f.SaveDocFromFixedElements = function (h, g, t, r, l, z) {
									h = Module.PDFDocCreateFromLayoutEls(h);
									h = Module.CreateDoc({ type: "ptr", value: h });
									return Module.SaveDoc(h, g, !0, !1, t, r, l, z);
								};
								f.GetCurrentCanvasData = function (h) {
									var g = Module.currentRenderData;
									if (!g) return null;
									h && REX(Module._TRN_PDFRasterizerUpdateBuffer(g.rast));
									var t = Date.now();
									if (g.bufPtr) {
										h = new Uint8Array(new ArrayBuffer(g.buf_size));
										for (var r = 0, l = 0; l < g.out_height; ++l)
											for (
												var z = g.bufPtr + g.stride * l, F = 0;
												F < g.out_width;
												++F
											)
												(h[r++] = Module.HEAPU8[z + 2]),
													(h[r++] = Module.HEAPU8[z + 1]),
													(h[r++] = Module.HEAPU8[z]),
													(h[r++] = Module.HEAPU8[z + 3]),
													(z += 4);
									} else h = Module.ReadBufferFile("b", h);
									Object(c.b)(
										"bufferTiming",
										"Copy took ".concat(Date.now() - t),
									);
									return {
										pageBuf: h.buffer,
										pageWidth: g.out_width,
										pageHeight: g.out_height,
									};
								};
								f.RasterizePage = function (h, g, t, r, l, z, F, E, H) {
									return new Promise(function (I, J) {
										Module.currentRenderData = {};
										var G = Module.currentRenderData;
										G.out_width = parseInt(t, 10);
										G.out_height = parseInt(r, 10);
										var a = [];
										a.push(function () {
											Module.currentRenderData = null;
										});
										try {
											var b = Module.stackSave();
											a[a.length] = function () {
												Module.stackRestore(b);
											};
											var d = Module.GetPage(h, g),
												e = Module.PageGetPageWidth(d),
												w = Module.PageGetPageHeight(d);
											G.stride = 4 * G.out_width;
											G.buf_size = G.out_width * G.out_height * 4;
											Object(c.b)("Memory", "Created rasterizer");
											G.rast = Module.PDFRasterizerCreate();
											a.push(function () {
												Object(c.b)("Memory", "Destroyed rasterizer");
												Module._TRN_PDFRasterizerDestroy(G.rast);
											});
											if (F) {
												var B = Module.EMSCreateUpdatedLayersContext(h, F);
												0 !== B &&
													(REX(
														Module._TRN_PDFRasterizerSetOCGContext(G.rast, B),
													),
													a.push(function () {
														Module._TRN_OCGContextDestroy(B);
													}));
											}
											var y = !1;
											E.hasOwnProperty("renderAnnots")
												? (E.renderAnnots && (y = !0),
													REX(
														Module._TRN_PDFRasterizerSetDrawAnnotations(
															G.rast,
															E.renderAnnots ? 1 : 0,
														),
													))
												: REX(
														Module._TRN_PDFRasterizerSetDrawAnnotations(
															G.rast,
															0,
														),
													);
											E.hasOwnProperty("highlightFields") &&
												(E.highlightFields && (y = !0),
												REX(
													Module._TRN_PDFRasterizerSetHighlightFields(
														G.rast,
														E.highlightFields,
													),
												));
											E.hasOwnProperty("antiAliasing") &&
												REX(
													Module._TRN_PDFRasterizerSetAntiAliasing(
														G.rast,
														E.antiAliasing,
													),
												);
											E.hasOwnProperty("pathHinting") &&
												REX(
													Module._TRN_PDFRasterizerSetPathHinting(
														G.rast,
														E.pathHinting,
													),
												);
											if (E.hasOwnProperty("thinLinePixelGridFit")) {
												var D = !0;
												E.hasOwnProperty("thinLineStrokeAdjust") &&
													(D = E.thinLineStrokeAdjust);
												REX(
													Module._TRN_PDFRasterizerSetThinLineAdjustment(
														G.rast,
														E.thinLinePixelGridFit,
														D,
													),
												);
											} else
												E.hasOwnProperty("thinLineStrokeAdjust") &&
													REX(
														Module._TRN_PDFRasterizerSetThinLineAdjustment(
															G.rast,
															!1,
															E.thinLineStrokeAdjust,
														),
													);
											E.hasOwnProperty("thinLineScaling") &&
												REX(
													Module._TRN_PDFRasterizerSetThinLineScaling(
														G.rast,
														E.thinLineScaling,
													),
												);
											if (
												E.hasOwnProperty("nightModeTuningContrast") ||
												E.hasOwnProperty("nightModeTuningSaturation") ||
												E.hasOwnProperty("nightModeTuningFlipness")
											) {
												var L = E.hasOwnProperty("nightModeTuningContrast")
														? E.nightModeTuningContrast
														: 0.9,
													N = E.hasOwnProperty("nightModeTuningSaturation")
														? E.nightModeTuningSaturation
														: 0.8,
													Q = E.hasOwnProperty("nightModeTuningFlipness")
														? E.nightModeTuningFlipness
														: 1;
												REX(
													Module._TRN_PDFRasterizerSetNightModeTuning(
														G.rast,
														L,
														N,
														Q,
													),
												);
											}
											E.hasOwnProperty("imageSmoothing")
												? ((D = !1),
													E.hasOwnProperty("hqImageResampling") &&
														(D = E.hqImageResampling),
													REX(
														Module._TRN_PDFRasterizerSetImageSmoothing(
															G.rast,
															E.imageSmoothing,
															D,
														),
													))
												: E.hasOwnProperty("hqImageResampling") &&
													REX(
														Module._TRN_PDFRasterizerSetImageSmoothing(
															G.rast,
															!0,
															E.hqImageResampling,
														),
													);
											E.hasOwnProperty("caching") &&
												REX(
													Module._TRN_PDFRasterizerSetCaching(
														G.rast,
														E.caching,
													),
												);
											E.hasOwnProperty("expGamma") &&
												REX(
													Module._TRN_PDFRasterizerSetGamma(G.rast, E.expGamma),
												);
											E.hasOwnProperty("isPrinting") &&
												(E.isPrinting && (y = !0),
												REX(
													Module._TRN_PDFRasterizerSetPrintMode(
														G.rast,
														E.isPrinting,
													),
												));
											E.hasOwnProperty("colorPostProcessMode") &&
												(E.colorPostProcessMode && (y = !0),
												REX(
													Module._TRN_PDFRasterizerSetColorPostProcessMode(
														G.rast,
														E.colorPostProcessMode,
													),
												));
											var V = Module.PageGetRotation(d);
											D = 1 === z || 3 === z;
											V = (1 === V || 3 === V) !== D;
											var X = Module.allocate(48, "i8", Module.ALLOC_STACK);
											if (l) {
												l.x1 = l[0];
												l.y1 = l[1];
												l.x2 = l[2];
												l.y2 = l[3];
												var ja = Module.PageGetDefaultMatrix(d, 0),
													ka = Module.Matrix2DInverse(ja);
												l = Module.Matrix2DMultBBox(ka, l);
												if (l.x2 < l.x1) {
													var fa = l.x1;
													l.x1 = l.x2;
													l.x2 = fa;
												}
												l.y2 < l.y1 &&
													((fa = l.y1), (l.y1 = l.y2), (l.y2 = fa));
												var na = G.out_width / (V ? l.y2 - l.y1 : l.x2 - l.x1);
												var ra = Module.GetDefaultMatrixBox(d, l, z);
											} else
												(ra = Module.PageGetDefaultMatrix(d, z)),
													(na = G.out_width / (D ? w : e));
											Module.Matrix2DSet(X, na, 0, 0, na, 0, 0);
											Module.Matrix2DConcat(X, ra);
											var pa = Module.allocate(4, "i8", Module.ALLOC_STACK),
												aa = Module.allocate(4, "i8", Module.ALLOC_STACK);
											y
												? ((G.bufPtr = Module._malloc(G.buf_size)),
													Module._memset(
														G.bufPtr,
														E.pageTransparent ? 0 : 255,
														G.buf_size,
													),
													a.push(function () {
														Module._free(G.bufPtr);
													}))
												: (Module.CreateBufferFile(
														"b",
														G.buf_size,
														E.pageTransparent,
													),
													a.push(function () {
														Module.RemoveBufferFile("b");
													}));
											var qa = E.overprintMode;
											if (10 === qa) {
												REX(Module._TRN_PDFRasterizerSetOverprint(G.rast, 1));
												var ib = Module.PDFRasterizerRasterizeSeparations(
													G.rast,
													d,
													G.out_width,
													G.out_height,
													X,
													0,
													0,
												);
												I({
													pageBuf: ib,
													pageWidth: G.out_width,
													pageHeight: G.out_height,
												});
											} else {
												REX(Module._TRN_PDFRasterizerSetOverprint(G.rast, qa));
												y
													? REX(
															Module._TRN_PDFRasterizerGetChunkRenderer(
																G.rast,
																d,
																G.bufPtr,
																G.out_width,
																G.out_height,
																G.stride,
																4,
																!0,
																X,
																0,
																0,
																0,
																pa,
															),
														)
													: REX(
															Module._TRN_PDFRasterizerGetChunkRendererPath(
																G.rast,
																d,
																Module.GetUStringFromJSString("b"),
																G.out_width,
																G.out_height,
																!0,
																X,
																0,
																0,
																0,
																pa,
															),
														);
												var Ra = Module.getValue(pa, "i8*");
												a.push(function () {
													REX(Module._TRN_ChunkRendererDestroy(Ra));
												});
											}
											var jb = new Date().getTime();
											E.useProgress &&
												Module.postProgressiveRenderingStartEvent(H, g);
											var kb = m(function Ha() {
												try {
													if (
														(REX(
															Module._TRN_ChunkRendererRenderForTimePeriod(
																Ra,
																200,
																aa,
															),
														),
														Module.getValue(aa, "i8"))
													)
														Module.cleanupState.timeout = m(Ha);
													else {
														var lb = Module.GetCurrentCanvasData(!1);
														Object(c.b)(
															"worker",
															"Total Page Time ".concat(
																new Date().getTime() - jb,
															),
														);
														a.reverse().forEach(function (za) {
															za();
														});
														I(lb);
													}
												} catch (za) {
													a.reverse().forEach(function (Ka) {
														Ka();
													}),
														J(za);
												}
											});
											Module.cleanupState = { cleanupArr: a, timeout: kb };
											a.push(function () {
												Module.cleanupState = null;
											});
										} catch (Pa) {
											a.reverse().forEach(function (Ha) {
												Ha();
											}),
												J(Pa);
										}
									});
								};
								f.UpdatePassword = function (h, g) {
									try {
										var t = Module.stackSave();
										var r = Module.GetDoc(h);
										return Module.PDFDocInitStdSecurityHandler(r, g)
											? (r in downloadDataMap &&
													REX(
														Module._TRN_PDFDocDownloaderInitialize(
															r,
															downloadDataMap[r].downloader,
														),
													),
												{
													success: !0,
													pageDimensions: Module.GetPageDimensions(r),
												})
											: { success: !1 };
									} finally {
										Module.stackRestore(t);
									}
								};
								f.UpdateCustomHeader = function (h, g) {
									Module.customHeadersMap[h] =
										h in Module.customHeadersMap
											? q(q({}, Module.customHeadersMap[h]), g)
											: g;
									Module.docs[h].chunkStorage &&
										(Module.docs[h].chunkStorage.customHeaders =
											Module.customHeadersMap[h]);
								};
								f.TriggerFullDownload = function (h) {
									return new Promise(function (g, t) {
										var r = Module.GetDoc(h);
										try {
											r in downloadDataMap &&
												REX(
													Module.PDFDocDownloaderTriggerFullDownload(
														r,
														downloadDataMap[r].downloader,
													),
												),
												g({});
										} catch (l) {
											t(l);
										}
									});
								};
								f.InsertBlankPages = function (h, g, t, r) {
									return new Promise(function (l, z) {
										var F = [],
											E = Module.GetDoc(h);
										try {
											var H = Module.stackSave();
											F[F.length] = function () {
												Module.stackRestore(H);
											};
											for (var I = g.length - 1; 0 <= I; --I) {
												var J = Module.PDFDocGetPageIterator(E, g[I]),
													G = Module.PDFDocPageCreate(E, t, r);
												Module.PDFDocPageInsert(E, J, G);
											}
											var a = Module.postPagesUpdatedEvent(E, h);
											F.forEach(function (b) {
												b();
											});
											l(a);
										} catch (b) {
											F.forEach(function (d) {
												d();
											}),
												z(b);
										}
									});
								};
								f.InsertPages = function (h, g, t, r, l, z) {
									return new Promise(function (F, E) {
										var H = [],
											I = Module.GetDoc(h);
										try {
											var J = Module.stackSave();
											H[H.length] = function () {
												Module.stackRestore(J);
											};
											if (g instanceof ArrayBuffer) {
												var G = Module.CreateDoc(g);
												var a = Module.GetDoc(G);
												H[H.length] = function () {
													Module.DeleteDoc(G);
												};
											} else a = Module.GetDoc(g);
											for (
												var b = t.length, d = Module.PageSetCreate(), e = 0;
												e < b;
												++e
											)
												Module.PageSetAddPage(d, t[e]);
											z
												? Module.PDFDocInsertPages2(I, r, a, d, l)
												: Module.PDFDocInsertPages(I, r, a, d, l);
											var w;
											z || (w = Module.postPagesUpdatedEvent(I, h));
											H.reverse().forEach(function (B) {
												B();
											});
											F(w);
										} catch (B) {
											H.reverse().forEach(function (y) {
												y();
											}),
												E(B);
										}
									});
								};
								f.MovePages = function (h, g, t) {
									return new Promise(function (r, l) {
										var z = [],
											F = Module.GetDoc(h);
										try {
											var E = Module.stackSave();
											z[z.length] = function () {
												Module.stackRestore(E);
											};
											for (
												var H = g.length, I = Module.PageSetCreate(), J = 0;
												J < H;
												++J
											)
												Module.PageSetAddPage(I, g[J]);
											Module.PDFDocMovePages(F, t, I);
											var G = Module.postPagesUpdatedEvent(F, h);
											z.forEach(function (a) {
												a();
											});
											r(G);
										} catch (a) {
											z.forEach(function (b) {
												b();
											}),
												l(a);
										}
									});
								};
								f.RemovePages = function (h, g, t) {
									return new Promise(function (r, l) {
										var z = Module.GetDoc(h),
											F = [];
										try {
											var E = Module.stackSave();
											F[F.length] = function () {
												Module.stackRestore(E);
											};
											for (var H = g.length - 1; 0 <= H; --H) {
												var I = Module.PDFDocGetPageIterator(z, g[H]);
												Module.IteratorHasNext(I) &&
													(t
														? Module.PDFDocPageRemove2(z, I)
														: Module.PDFDocPageRemove(z, I));
											}
											var J;
											t || (J = Module.postPagesUpdatedEvent(z, h));
											F.forEach(function (G) {
												G();
											});
											r(J);
										} catch (G) {
											F.forEach(function (a) {
												a();
											}),
												l(G);
										}
									});
								};
								f.RotatePages = function (h, g, t) {
									return new Promise(function (r, l) {
										var z = Module.GetDoc(h),
											F = [];
										try {
											var E = Module.stackSave();
											F[F.length] = function () {
												Module.stackRestore(E);
											};
											var H = g.length,
												I = 0,
												J = Module.PDFDocGetPageIterator(z, g[0]),
												G = [];
											F.push(function () {
												Module._TRN_IteratorDestroy(J);
											});
											for (
												var a = g[0];
												Module.IteratorHasNext(J) && I < g[H - 1];
												++a
											) {
												if (a === g[I]) {
													var b = Module.IteratorCurrent(J),
														d = (Module.PageGetRotation(b) + t) % 4;
													Module.PageSetRotation(b, d);
													G.push(a);
													I++;
												}
												Module.IteratorNext(J);
											}
											var e = Module.postPagesUpdatedEvent(z, h, G, !0);
											F.reverse().forEach(function (w) {
												w();
											});
											r(e);
										} catch (w) {
											F.reverse().forEach(function (B) {
												B();
											}),
												l(w);
										}
									});
								};
								f.ExtractPages = function (h, g, t, r, l, z) {
									return new Promise(function (F, E) {
										var H = [];
										try {
											var I = Module.stackSave();
											H[H.length] = function () {
												Module.stackRestore(I);
											};
											var J = function (b) {
												H.reverse().forEach(function (d) {
													d();
												});
												E(b);
											};
											Module.XFDFMerge(h, t, l);
											var G = Module.CreateEmptyDoc();
											H[H.length] = function () {
												Module.DeleteDoc(G);
											};
											var a = Module.InsertPages(G, h, g, 1, !0, z)
												.then(function () {
													return Module.SaveDoc(G, void 0, !0, !1, void 0, r);
												})
												.then(function (b) {
													H.reverse().forEach(function (d) {
														d();
													});
													return b;
												});
											F(a);
										} catch (b) {
											J(b);
										}
									});
								};
								f.CropPages = function (h, g, t, r, l, z) {
									return new Promise(function (F, E) {
										var H = Module.GetDoc(h),
											I = [];
										try {
											var J = Module.stackSave();
											I[I.length] = function () {
												Module.stackRestore(J);
											};
											var G = g.length,
												a = 0,
												b = Module.PDFDocGetPageIterator(H, g[0]);
											I.push(function () {
												Module._TRN_IteratorDestroy(b);
											});
											for (
												var d = [], e = g[0];
												Module.IteratorHasNext(b) && a < g[G - 1];
												++e
											) {
												if (e === g[a]) {
													var w = Module.IteratorCurrent(b),
														B = Module.allocate(8, "i8", Module.ALLOC_STACK);
													REX(Module._TRN_PageGetCropBox(w, B));
													var y = Module.PageGetRotation(w),
														D = Module.getValue(B, "double"),
														L = Module.getValue(B + 8, "double"),
														N = Module.getValue(B + 16, "double"),
														Q = Module.getValue(B + 24, "double");
													0 === y % 4
														? (Module.setValue(B, D + l, "double"),
															Module.setValue(B + 8, L + r, "double"),
															Module.setValue(B + 16, N - z, "double"),
															Module.setValue(B + 24, Q - t, "double"))
														: 1 === y % 4
															? (Module.setValue(B, D + t, "double"),
																Module.setValue(B + 8, L + l, "double"),
																Module.setValue(B + 16, N - r, "double"),
																Module.setValue(B + 24, Q - z, "double"))
															: 2 === y % 4
																? (Module.setValue(B, D + z, "double"),
																	Module.setValue(B + 8, L + t, "double"),
																	Module.setValue(B + 16, N - l, "double"),
																	Module.setValue(B + 24, Q - r, "double"))
																: 3 === y % 4 &&
																	(Module.setValue(B, D + r, "double"),
																	Module.setValue(B + 8, L + z, "double"),
																	Module.setValue(B + 16, N - t, "double"),
																	Module.setValue(B + 24, Q - l, "double"));
													Module.setValue(B + 32, 0, "double");
													REX(Module._TRN_PageSetBox(w, 0, B));
													REX(Module._TRN_PageSetBox(w, 1, B));
													d.push(e);
													a++;
												}
												Module.IteratorNext(b);
											}
											var V = Module.postPagesUpdatedEvent(H, h, d, !0);
											I.reverse().forEach(function (X) {
												X();
											});
											F(V);
										} catch (X) {
											I.reverse().forEach(function (ja) {
												ja();
											}),
												E(X);
										}
									});
								};
							})("undefined" === typeof self ? this.Module : self.Module);
							this.loaded = !0;
							Module.initCb && Module.initCb();
						},
					};
				})(self);
			}).call(this, u(10).clearImmediate, u(10).setImmediate);
		},
		function (v, A, u) {
			(function (p) {
				function m(c) {
					"@babel/helpers - typeof";
					return (
						(m =
							"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
								? function (n) {
										return typeof n;
									}
								: function (n) {
										return n &&
											"function" == typeof Symbol &&
											n.constructor === Symbol &&
											n !== Symbol.prototype
											? "symbol"
											: typeof n;
									}),
						m(c)
					);
				}
				var x = u(1),
					k = u(5),
					q = "undefined" !== typeof window ? window : self;
				q.global = q;
				(function (c) {
					c.currentFileString = "/current";
					var n = 0,
						f = 0,
						h = {},
						g = null;
					Module.chunkMax = 200;
					var t = function (a, b, d, e, w) {
							var B = new XMLHttpRequest();
							return CancellablePromise(
								function (y, D) {
									B.open("GET", a, !0);
									B.responseType = "arraybuffer";
									B.onerror = function () {
										D("Network error occurred");
									};
									B.onload = function () {
										if (206 === this.status && B.response.byteLength === d) {
											var N = new Int8Array(B.response);
											y(N);
										} else D("Download Failed");
									};
									var L = ["bytes=", b, "-", b + d - 1].join("");
									B.setRequestHeader("Range", L);
									w && (B.withCredentials = w);
									e &&
										Object.keys(e).forEach(function (N) {
											B.setRequestHeader(N, e[N]);
										});
									B.send();
								},
								function () {
									B.abort();
								},
							);
						},
						r = function (a) {
							this.maxChunkNum = a;
							this.lruList = [];
							this.chunkMap = {};
						};
					r.prototype = {
						has: function (a) {
							return a in this.chunkMap;
						},
						insert: function (a, b) {
							this.lruList.length >= this.maxChunkNum &&
								(delete this.chunkMap[this.lruList[0]], this.lruList.shift());
							this.lruList.push(b);
							this.chunkMap[b] = a;
						},
						get: function (a) {
							var b = this.lruList.lastIndexOf(a);
							0 <= b && this.lruList.splice(b, 1);
							this.lruList.push(a);
							return this.chunkMap[a];
						},
					};
					var l = function (a) {
						this.file = a;
						this.filePosition = 0;
						this.fileLength = a.size;
						this.chunkSize = 1048576;
						this.chunkCache = new r(8);
						this.reader = new FileReaderSync();
					};
					l.prototype = {
						read: function (a, b, d) {
							d =
								this.filePosition + d <= this.fileLength
									? d
									: this.fileLength - this.filePosition;
							a = a.subarray(b, b + d);
							b = d;
							for (
								var e = this.filePosition % this.chunkSize,
									w = this.filePosition - e,
									B = 0;
								0 < d;
							) {
								if (this.chunkCache.has(w)) var y = this.chunkCache.get(w);
								else
									(y = new Int8Array(
										this.reader.readAsArrayBuffer(
											this.file.slice(w, w + this.chunkSize),
										),
									)),
										this.chunkCache.insert(y, w);
								var D = y.length,
									L = e + d;
								L <= D
									? (a.set(y.subarray(e, L), B),
										(this.filePosition += d),
										(d = 0))
									: (a.set(y.subarray(e), B),
										(this.filePosition += D - e),
										(e = 0),
										(w = this.filePosition),
										(d = L - D),
										(B = b - d));
							}
							return b;
						},
						seek: function (a) {
							this.filePosition = a;
						},
						close: function () {
							this.reader = this.file = null;
						},
						getPos: function () {
							return this.filePosition;
						},
						getTotalSize: function () {
							return this.fileLength;
						},
					};
					var z = function (a) {
						this.data = a;
						this.position = 0;
						this.length = this.data.length;
					};
					z.prototype = {
						read: function (a, b, d) {
							d =
								this.position + d <= this.length
									? d
									: this.length - this.position;
							a = a.subarray(b, b + d);
							b = this.data.subarray(this.position, this.position + d);
							a.set(b);
							this.position += d;
							return d;
						},
						write: function (a, b, d) {
							d =
								this.position + d <= this.length
									? d
									: this.length - this.position;
							a = a.subarray(b, b + d);
							this.data.subarray(this.position, this.position + d).set(a);
							this.position += d;
							return d;
						},
						seek: function (a) {
							this.position = a;
						},
						close: function () {
							this.data = null;
						},
						getPos: function () {
							return this.position;
						},
						getTotalSize: function () {
							return this.length;
						},
					};
					var F = function (a, b, d, e, w, B) {
						"object" === m(a)
							? ((this.lruList = a.lruList),
								(this.chunkMap = a.chunkMap),
								(this.length = a.length),
								(this.url = a.url),
								(this.customHeaders = a.customHeaders),
								(this.withCredentials = a.withCredentials),
								(this.chunkSize = a.chunkSize))
							: ((this.lruList = []),
								(this.chunkMap = {}),
								(this.chunkMap[b] = B),
								(this.length = b),
								(this.url = a),
								(this.customHeaders = e),
								(this.withCredentials = w),
								(this.chunkSize = d));
					};
					F.prototype = {
						lruUpdate: function (a) {
							var b = this.lruList.lastIndexOf(a);
							0 <= b && this.lruList.splice(b, 1);
							this.lruList.push(a);
						},
						getChunk: function (a) {
							var b = this;
							if (this.chunkMap[a]) this.lruUpdate(a);
							else {
								var d = Math.min(a + this.chunkSize, this.length) - 1,
									e = new XMLHttpRequest();
								e.open("GET", this.url, !1);
								e.responseType = "arraybuffer";
								e.setRequestHeader("Range", ["bytes=", a, "-", d].join(""));
								this.withCredentials &&
									(e.withCredentials = this.withCredentials);
								this.customHeaders &&
									Object.keys(this.customHeaders).forEach(function (w) {
										e.setRequestHeader(w, b.customHeaders[w]);
									});
								e.send();
								if (200 === e.status || 206 === e.status)
									this.writeChunk(new Int8Array(e.response), a);
								else throw Error("Failed to load data from");
							}
							return this.chunkMap[a];
						},
						hadChunk: function (a) {
							return a in this.chunkMap;
						},
						hasChunk: function (a) {
							return this.chunkMap[a];
						},
						getCacheData: function () {
							return this.chunkMap[this.length];
						},
						getRequiredChunkOffsetArrays: function (a, b) {
							var d = { have: [], downloading: [], missing: [] };
							try {
								var e = Module.stackSave();
								var w = Module.allocate(4, "i8", Module.ALLOC_STACK);
								REX(Module._TRN_DownloaderGetRequiredChunksSize(a, b, w));
								var B = Module.getValue(w, "i8*");
								if (B) {
									var y = Module._malloc(8 * B);
									REX(Module._TRN_DownloaderGetRequiredChunks(a, b, y, B));
									for (a = 0; a < B; ++a) {
										var D = Module.getValue(y + 8 * a, "double");
										this.hasChunk(D)
											? d.have.push(D)
											: this.hadChunk(D)
												? d.missing.push(D)
												: d.downloading.push(D);
									}
								}
							} finally {
								y && Module._free(y), Module.stackRestore(e);
							}
							return d;
						},
						keepVisibleChunks: function (a, b) {
							for (
								var d = b.length, e = Module.chunkMax / 2, w = 0, B = 0;
								B < d;
								++B
							) {
								var y = this.getRequiredChunkOffsetArrays(a, b[B]),
									D = y.have,
									L = D.length;
								w += L;
								if (w > e) {
									this.keepChunks(D.slice(0, L - w + e));
									break;
								}
								this.keepChunks(y.have);
							}
						},
						getChunkAsync: function (a) {
							var b = this,
								d = a + this.chunkSize,
								e = this.chunkSize;
							d > this.length && (e -= d - this.length);
							return t(
								this.url,
								a,
								e,
								this.customHeaders,
								this.withCredentials,
							).then(function (w) {
								b.writeChunk(w, a);
							});
						},
						getChunks: function (a) {
							for (var b = a.length, d = Array(b), e = 0; e < b; ++e)
								d[e] = this.getChunkAsync(a[e]);
							return CancellablePromise.all(d);
						},
						keepChunks: function (a) {
							for (var b = a.length, d = 0; d < b; ++d) this.lruUpdate(a[d]);
						},
						writeChunk: function (a, b, d) {
							Object(k.b)(this.lruList, this.chunkMap, this.chunkSize, a, b, d);
							this.lruUpdate(b);
						},
					};
					var E = function (a) {
						this.chunkStorage = a;
						this.position = 0;
						this.length = this.chunkStorage.length;
						this.chunkSize = a.chunkSize;
					};
					E.prototype = {
						read: function (a, b, d) {
							var e = this.position + d <= this.length,
								w = e ? d : this.length - this.position;
							if (this.position < this.length) {
								var B;
								for (B = 0; B < w; ) {
									var y = this.position % this.chunkSize;
									var D = this.position - y;
									var L = w - B,
										N = a.subarray(b + B, b + B + L);
									if (this.chunkStorage.hadChunk(D))
										(D = this.chunkStorage.getChunk(D).subarray(y, y + L)),
											N.set(D),
											(N = D.length),
											(B += N),
											(this.position += N);
									else for (this.position += L; B < w; ++B) N[B] = 0;
								}
							}
							if (!e) {
								b += w;
								if ((d -= w))
									(e = this.chunkStorage.getCacheData()),
										d > e.length && (d = e.length),
										(B = this.position - this.length),
										(a = a.subarray(b, b + d)),
										(D = e.subarray(B, B + d)),
										a.set(D);
								this.position += d;
								return w + d;
							}
							return w;
						},
						write: function (a, b, d) {
							var e = this.position + d <= this.length,
								w =
									this.position + d <= this.length
										? d
										: this.length - this.position,
								B = a.subarray(b, b + w),
								y = this.position % this.chunkSize;
							this.chunkStorage.writeChunk(B, this.position - y, y);
							this.position += w;
							if (!e) {
								B = b + w;
								if ((d -= w))
									(b = this.chunkStorage.getCacheData()),
										d > b.length && (d = b.length),
										(e = this.position - this.length),
										(B = a.subarray(B, B + d)),
										b.subarray(e, e + d).set(B);
								this.position += d;
								return w + d;
							}
							return w;
						},
						seek: function (a) {
							this.position = a;
						},
						close: function () {
							this.chunkStorage = null;
						},
						getPos: function () {
							return this.position;
						},
						getTotalSize: function () {
							return this.length;
						},
					};
					var H = function (a) {
						this.docId = a;
						this.length = 0;
						this.data = new Int8Array(8192);
						this.position = 0;
					};
					H.prototype = {
						seek: function (a) {
							this.position = a;
						},
						close: function () {
							var a = new Int8Array(this.data.buffer.slice(0, this.length));
							Module.ChangeDocBackend(this.docId, {
								ptr: Module.GetDoc(this.docId),
								buffer: a,
							});
							this.data = null;
						},
						getPos: function () {
							return this.position;
						},
						getTotalSize: function () {
							return this.length;
						},
						read: function (a, b, d) {
							var e = this.data.length;
							d = d + b < e ? d : e - b;
							a = a.subarray(b, b + d);
							b = this.data.subarray(this.position, this.position + d);
							a.set(b);
							this.position += d;
							return d;
						},
						write: function (a, b, d) {
							for (var e = this.position + d, w = this.data.length; e > w; ) {
								w = Math.max(w * (16777216 < w ? 1.5 : 2), e);
								var B = new Int8Array(w);
								B.set(this.data.subarray(0, this.length), 0);
								this.data = B;
							}
							a = a.subarray(b, b + d);
							this.data.set(a, this.position);
							this.position += d;
							this.position > this.length && (this.length = this.position);
							return d;
						},
					};
					var I = {
						IsSink: function (a) {
							return 66 === (a.flags & 255);
						},
						open: function (a) {
							var b = a.path.slice(1);
							this.IsSink(a)
								? ((a.provider = new H(b)), (Module.docs[b].sink = a.provider))
								: (a.provider = Module.docs[b].sink
										? new z(Module.docs[b].sink.data)
										: Module.docs[b].chunkStorage
											? new E(Module.docs[b].chunkStorage)
											: Module.docs[b].buffer
												? new z(Module.docs[b].buffer)
												: new l(Module.docs[b].file));
						},
						close: function (a) {
							a.provider.close();
						},
						read: function (a, b, d, e, w) {
							return a.provider.read(b, d, e);
						},
						llseek: function (a, b, d) {
							a = a.provider;
							1 === d
								? (b += a.getPos())
								: 2 === d && (b = a.getTotalSize() + b);
							if (0 > b) throw new FS.ErrnoError(q.ERRNO_CODES.EINVAL);
							a.seek(b);
							return b;
						},
						write: function (a, b, d, e, w) {
							return e ? a.provider.write(b, d, e) : 0;
						},
					};
					q.THROW = function (a) {
						throw { type: "PDFWorkerError", message: a };
					};
					var J = function (a) {
						return "Exception: \n\t Message: "
							.concat(
								c.GetJSStringFromCString(Module._TRN_GetMessage(a)),
								"\n\t Filename: ",
							)
							.concat(
								c.GetJSStringFromCString(Module._TRN_GetFileName(a)),
								"\n\t Function: ",
							)
							.concat(
								c.GetJSStringFromCString(Module._TRN_GetFunction(a)),
								"\n\t Linenumber: ",
							)
							.concat(c.GetJSStringFromCString(Module._TRN_GetLineNum(a)));
					};
					c.GetErrToString = J;
					q.REX = function (a) {
						a && THROW(J(a));
					};
					c.Initialize = function (a) {
						var b = Module.stackSave();
						a = a
							? Module.allocate(
									Module.intArrayFromString(a),
									"i8",
									Module.ALLOC_STACK,
								)
							: 0;
						REX(Module._TRN_PDFNetInitialize(a));
						Module.stackRestore(b);
					};
					c.GetDoc = function (a) {
						if (a in Module.docs) return Module.docs[a].ptr;
						throw {
							type: "InvalidDocReference",
							message: "Unable to access Document id=".concat(
								a,
								". The document appears to be invalid or was deleted.",
							),
						};
					};
					c.clearDocBackend = function () {
						null !== Module.cachePtr
							? (Module.hasBufOwnership && Module._free(Module.cachePtr),
								(Module.cachePtr = null))
							: Module.docs[c.currentFileString] &&
								delete Module.docs[c.currentFileString];
					};
					c.MakeDev = function (a) {
						if (!h[a]) {
							var b = FS.makedev(3, 5);
							FS.registerDevice(b, I);
							FS.mkdev(a, 511, b);
							h[a] = !0;
						}
					};
					c.CreateDocFileBackend = function (a, b) {
						Module.MakeDev(b);
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						Module.docs[b] = { file: a };
						a = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						REX(Module._TRN_PDFDocCreateFromFilePath(a, d));
						d = Module.getValue(d, "i8*");
						Module.docs[b].ptr = d;
					};
					c.InsertImageIntoDoc = function (a, b, d) {
						var e = [];
						try {
							var w = Module.ElementBuilderCreate();
							e.push(function () {
								Module.ElementBuilderDestroy(w);
							});
							var B = Module.ElementWriterCreate();
							e.push(function () {
								Module.ElementWriterDestroy(B);
							});
							if (d) {
								var y = d.width;
								var D = d.height;
							} else
								(y = Module.ImageGetImageWidth(b)),
									(D = Module.ImageGetImageHeight(b)),
									(d = y / D),
									d > 612 / 792
										? ((y = 612), (D = parseInt(y / d, 10)))
										: ((D = 792), (y = parseInt(D * d, 10)));
							var L = Module.ElementBuilderCreateImage(w, b, 0, 0, y, D),
								N = Module.PDFDocPageCreate(a, y, D);
							Module.ElementWriterBegin(B, N);
							Module.ElementWriterWritePlacedElement(B, L);
							Module.ElementWriterEnd(B);
							Module.PDFDocPagePushBack(a, N);
						} finally {
							e.reverse().forEach(function (Q) {
								Q();
							});
						}
					};
					var G = function (a, b, d) {
						"object" === m(a)
							? ((this.m_pages = a.m_pages),
								(this.m_has_named_dests = a.m_has_named_dests),
								(this.m_finished_download = a.m_finished_download),
								(this.m_has_outline = a.m_has_outline),
								(this.m_current_page = a.m_current_page),
								(this.m_id = a.m_id),
								(this.size = a.size),
								(this.timeout = a.timeout),
								(this.eventPageArray = a.eventPageArray),
								(this.requirePageCallbacks = a.requirePageCallbacks))
							: ((this.m_pages = []),
								(this.m_has_outline =
									this.m_finished_download =
									this.m_has_named_dests =
										!1),
								(this.m_current_page = 1),
								(this.m_id = d),
								(this.size = a),
								(this.timeout = null),
								(this.eventPageArray = []),
								(this.requirePageCallbacks = {}));
						this.downloadUserData = Module.createDownloadUserData(b, d);
					};
					G.prototype = {
						getJSUrl: function () {
							return Module.extractDownloadUserData(this.downloadUserData).url;
						},
						getDocId: function () {
							return Module.extractDownloadUserData(this.downloadUserData)
								.docId;
						},
						destroyUserData: function () {
							this.m_id in Module.withCredentials &&
								delete Module.withCredentials[this.m_id];
							this.m_id in Module.customHeadersMap &&
								delete Module.customHeadersMap[this.m_id];
							Module.destroyDownloadUserData(this.downloadUserData);
						},
					};
					c.createDownloadUserData = function (a, b) {
						a = Module.allocate(
							Module.intArrayFromString(a),
							"i8",
							Module.ALLOC_NORMAL,
						);
						var d = Module.allocate(8, "i8", Module.ALLOC_NORMAL);
						Module.setValue(d, a, "i8*");
						Module.setValue(d + 4, parseInt(b, 10), "i32");
						return (this.downloadUserData = d);
					};
					c.extractDownloadUserData = function (a) {
						var b = Module.getValue(a, "i8*");
						b = c.GetJSStringFromCString(b);
						a = Module.getValue(a + 4, "i32").toString();
						return { url: b, docId: a };
					};
					c.destroyDownloadUserData = function (a) {
						Module._free(Module.getValue(a, "i8*"));
						Module._free(a);
					};
					q.downloadDataMap = {};
					Module.customHeadersMap = {};
					Module.withCredentials = {};
					c.GetDownloadData = function (a) {
						if (a in downloadDataMap) return downloadDataMap[a];
					};
					c.DownloaderHint = function (a, b) {
						var d = Module.GetDoc(a),
							e = downloadDataMap[d];
						b.currentPage && (e.m_current_page = b.currentPage);
						if (b.visiblePages) {
							var w = b.visiblePages;
							for (b = 0; b < w.length; ++b) ++w[b];
							Object.keys(e.requirePageCallbacks).forEach(function (y) {
								e.requirePageCallbacks.hasOwnProperty(y) &&
									w.push(parseInt(y, 10));
							});
							(b = Module.docs[a].chunkStorage) &&
								b.keepVisibleChunks(e.downloader, w);
							a = w.length;
							var B = Module.allocate(4 * a, "i8", Module.ALLOC_STACK);
							for (b = 0; b < a; ++b) Module.setValue(B + 4 * b, w[b], "i32");
							REX(Module._TRN_PDFDocDownloadPages(d, B, a, 1, 0));
						}
					};
					c.RequirePage = function (a, b) {
						return new Promise(function (d, e) {
							e = Module.GetDoc(a);
							var w = downloadDataMap[e];
							!w || w.m_finished_download || w.m_pages[b]
								? d()
								: (b in w.requirePageCallbacks
										? w.requirePageCallbacks[b].push(d)
										: (w.requirePageCallbacks[b] = [d]),
									(d = Module.allocate(4, "i8", Module.ALLOC_STACK)),
									Module.setValue(d, b, "i32"),
									Module._TRN_PDFDocDownloadPages(e, d, 1, 0, 0));
						});
					};
					c.IsLinearizationValid = function (a) {
						a = Module.GetDoc(a);
						if ((a = downloadDataMap[a])) {
							var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
							REX(Module._TRN_DownloaderIsLinearizationValid(a.downloader, b));
							return 0 !== Module.getValue(b, "i8");
						}
						return !1;
					};
					c.ShouldRunRender = function (a, b) {
						a = Module.GetDoc(a);
						return (a = downloadDataMap[a])
							? a.m_finished_download
								? !0
								: a.m_pages[b]
							: !0;
					};
					c.postPagesDownloadedEvent = function (a, b, d) {
						a = {
							pageDimensions: Module.GetPageDimensionsContentChangedList(a, d),
							pageNumbers: d,
						};
						Module.postEvent(b, "pagesDownloaded", a);
						return a;
					};
					c.createCallbacksStruct = function (a) {
						if (!g) {
							var b = function (d) {
								return function (e) {
									var w = arguments;
									e in downloadDataMap
										? d.apply(this, w)
										: p(function () {
												e in downloadDataMap && d.apply(this, w);
											}, 0);
								};
							};
							g = {
								downloadProc: Module.addFunction(function (d, e, w, B, y) {
									B = Module.extractDownloadUserData(B);
									var D = B.docId;
									t(
										B.url,
										e,
										w,
										Module.customHeadersMap[D],
										Module.withCredentials[D],
									).then(function (L) {
										D in Module.docs &&
											Module.docs[D].chunkStorage &&
											Module.docs[D].chunkStorage.writeChunk(L, e);
										Module._TRN_DownloadComplete(0, e, w, d);
									});
								}, "vidiii"),
								notifyUpdatePage: Module.addFunction(
									b(function (d, e, w, B) {
										var y = downloadDataMap[d];
										y.m_pages[e] = !0;
										var D = y.eventPageArray;
										if (e in y.requirePageCallbacks)
											for (
												w = y.requirePageCallbacks[e], B = 0;
												B < w.length;
												++B
											)
												w[B]();
										y.timeout
											? D.push(e)
											: ((D = y.eventPageArray = [e]),
												(y.timeout = setTimeout(function () {
													Module.postPagesDownloadedEvent(d, y.m_id, D);
													y.timeout = null;
												}, 100)));
									}),
									"viiii",
								),
								notifyUpdateOutline: Module.addFunction(
									b(function (d, e) {
										d = downloadDataMap[d];
										d.m_has_outline ||
											((d.m_has_outline = !0),
											Module.postEvent(d.m_id, "bookmarksUpdated", {}));
									}),
									"vii",
								),
								notifyUpdateNamedDests: Module.addFunction(
									b(function (d, e) {
										d = downloadDataMap[d];
										d.m_has_named_dests || (d.m_has_named_dests = !0);
									}),
									"vii",
								),
								notifyDocInfoDownloaded: Module.addFunction(
									b(function (d, e) {
										downloadDataMap[d].docInfoPromiseCapability.resolve();
									}),
									"vii",
								),
								notifyUpdateThumb: Module.addFunction(
									b(function (d, e) {}),
									"viiii",
								),
								notifyFinishedDownload: Module.addFunction(
									b(function (d, e) {
										d = downloadDataMap[d];
										d.m_finished_download ||
											((d.m_finished_download = !0),
											Module.postEvent(d.m_id, "documentComplete", {}));
									}),
									"vii",
								),
								notifyDocumentError: Module.addFunction(function (
									d,
									e,
								) {}, "viii"),
								getCurrentPage: Module.addFunction(function (d, e) {
									return downloadDataMap[d].m_current_page;
								}, "iii"),
							};
						}
						b = Module.allocate(44, "i8", Module.ALLOC_STACK);
						Module.setValue(b, g.downloadProc, "i8*");
						Module.setValue(b + 4, a, "i8*");
						Module.setValue(b + 8, g.notifyUpdatePage, "i8*");
						Module.setValue(b + 12, g.notifyUpdateOutline, "i8*");
						Module.setValue(b + 16, g.notifyUpdateNamedDests, "i8*");
						Module.setValue(b + 20, g.notifyDocInfoDownloaded, "i8*");
						Module.setValue(b + 24, g.notifyUpdateThumb, "i8*");
						Module.setValue(b + 28, g.notifyFinishedDownload, "i8*");
						Module.setValue(b + 32, g.notifyDocumentError, "i8*");
						Module.setValue(b + 36, g.getCurrentPage, "i8*");
						Module.setValue(b + 40, 0, "i8*");
						return b;
					};
					c.CreateDocDownloaderBackend = function (a, b, d) {
						var e = a.url,
							w = a.size,
							B = a.customHeaders,
							y = a.withCredentials,
							D = a.shouldUseMinimumDownloads,
							L = a.chunkSize;
						B && (Module.customHeadersMap[d] = B);
						y && (Module.withCredentials[d] = y);
						var N = a.downloadData
							? new G(a.downloadData, e, d)
							: new G(a.size, e, d);
						var Q = Module.createCallbacksStruct(N.downloadUserData),
							V = Module.allocate(4, "i8", Module.ALLOC_STACK);
						Module.MakeDev(d);
						a.chunkStorage
							? (e = new F(a.chunkStorage))
							: ((a = new Int8Array(
									new ArrayBuffer(Math.ceil((a.size + L - 1) / L / 8)),
								)),
								(e = new F(e, w, L, B, y, a)));
						Module.docs[d] = { chunkStorage: e };
						REX(
							Module._TRN_DownloaderCreate(
								Q,
								w,
								Module.GetUStringFromJSString(d),
								D,
								e.chunkSize,
								V,
							),
						);
						N.downloader = Module.getValue(V, "i8*");
						N.docInfoPromiseCapability = createPromiseCapability();
						N.docInfoRequested = !1;
						if ((w = Module._TRN_PDFDocCreateFromFilter(N.downloader, b)))
							Module._TRN_FilterDestroy(N.downloader), REX(w);
						b = Module.getValue(b, "i8*");
						Module.docs[d].ptr = b;
						Module.PDFDocInitSecurityHandler(b) &&
							REX(Module._TRN_PDFDocDownloaderInitialize(b, N.downloader));
						downloadDataMap[b] = N;
					};
					c.CreateDocBackend = function (a, b) {
						var d = a.value,
							e = a.extension,
							w = a.type,
							B = Module.allocate(4, "i8", Module.ALLOC_STACK),
							y = Module.stackSave();
						try {
							if (d)
								if ("ptr" === w) Module.docs[b] = { ptr: d };
								else {
									d.shouldUseMinimumDownloads = a.shouldUseMinimumDownloads;
									d.chunkSize = a.chunkSize;
									var D = "object" === m(d) && d.url;
									w = e && "pdf" !== e;
									if (D) c.CreateDocDownloaderBackend(d, B, b);
									else {
										var L = d instanceof ArrayBuffer;
										D = L ? "buffer" : "file";
										if (
											L &&
											((d = new Uint8Array(d)), 10485760 > d.length + n && !w)
										) {
											n += d.length;
											var N = d.length,
												Q = Module._malloc(d.length);
											Module.HEAPU8.set(d, Q);
											REX(Module._TRN_PDFDocCreateFromBuffer(Q, N, B));
											var V = Module.getValue(B, "i8*");
											Module.docs[b] = {
												ptr: V,
												bufPtr: Q,
												bufPtrSize: N,
												ownership: !0,
											};
											Module.docs[b].extension = e;
											return;
										}
										Module.MakeDev(b);
										L = {};
										L[D] = d;
										Module.docs[b] = L;
										if (w) {
											if (a.pageSizes && a.pageSizes.length)
												var X = a.pageSizes[0];
											else a.defaultPageSize && (X = a.defaultPageSize);
											var ja = Module.GetUStringFromJSString(b);
											REX(Module._TRN_PDFDocCreate(B));
											V = Module.getValue(B, "i8*");
											var ka = Module.ImageCreateFromFile(V, ja);
											Module.InsertImageIntoDoc(V, ka, X);
										} else {
											var fa = Module.allocate(
												Module.intArrayFromString(b),
												"i8",
												Module.ALLOC_STACK,
											);
											REX(Module._TRN_PDFDocCreateFromFilePath(fa, B));
											V = Module.getValue(B, "i8*");
										}
										Module.docs[b].extension = e;
										Module.docs[b].ptr = V;
									}
								}
							else
								REX(Module._TRN_PDFDocCreate(B)),
									(V = Module.getValue(B, "i8*")),
									(Module.docs[b] = { ptr: V }),
									(Module.docs[b].extension = e);
						} finally {
							Module.stackRestore(y);
						}
					};
					c.ChangeDocBackend = function (a, b) {
						var d = Module.docs[a];
						d
							? (d.bufPtr &&
									d.ownership &&
									(Module._free(d.bufPtr), (n -= d.bufPtrSize)),
								delete Module.docs[a])
							: Object(x.d)(
									"Trying to delete document ".concat(
										a,
										" that does not exist.",
									),
								);
						Module.docs[a] = b;
					};
					c.DeleteDoc = function (a) {
						var b = Module.docs[a];
						b
							? (b.ptr &&
									(b.ptr in downloadDataMap &&
										(clearTimeout(downloadDataMap[b.ptr].timeout),
										downloadDataMap[b.ptr].destroyUserData(),
										delete downloadDataMap[b.ptr]),
									Module.PDFDocDestroy(b.ptr)),
								b.bufPtr &&
									b.ownership &&
									(Module._free(b.bufPtr), (n -= b.bufPtrSize)),
								delete Module.docs[a])
							: Object(x.d)(
									"Trying to delete document ".concat(
										a,
										" that does not exist.",
									),
								);
					};
					c.CreateDoc = function (a, b) {
						if ("id" === a.type) {
							if (
								Module.docPtrStringToIdMap &&
								a.value in Module.docPtrStringToIdMap
							)
								return Module.docPtrStringToIdMap[a.value];
							a.type = "ptr";
							a.value = Number("0x".concat(a.value));
						}
						if (!b) {
							do b = (++f).toString();
							while (b in Module.docs);
						}
						Module.hasBufOwnership = !0;
						c.CreateDocBackend(a, b);
						return b;
					};
					c.CreateEmptyDoc = function () {
						var a = (++f).toString(),
							b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocCreate(b));
						b = Module.getValue(b, "i8*");
						Module.docs[a] = { ptr: b };
						return a;
					};
					c.PDFDocCreateFromLayoutEls = function (a) {
						var b = new Uint8Array(a);
						a = Module._malloc(b.length);
						var d = Module.stackSave(),
							e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						Module.HEAPU8.set(b, a);
						b = Module._TRN_PDFDocCreateFromLayoutEls(a, b.length, e);
						e = Module.getValue(e, "i8*");
						Module._free(a);
						Module.stackRestore(d);
						REX(b);
						return e;
					};
					c.GetPageCount = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetPageCount(a, b));
						return Module.getValue(b, "i8*");
					};
					c.GetPage = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetPage(a, b, d));
						a = Module.getValue(d, "i8*");
						Module.PageIsValid(a) ||
							THROW(
								"Trying to access page that doesn't exist at index ".concat(b),
							);
						return a;
					};
					c.PageGetPageWidth = function (a) {
						var b = Module.allocate(8, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetPageWidth(a, 1, b));
						return Module.getValue(b, "double");
					};
					c.PageGetPageHeight = function (a) {
						var b = Module.allocate(8, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetPageHeight(a, 1, b));
						return Module.getValue(b, "double");
					};
					c.PageGetDefaultMatrix = function (a, b) {
						var d = Module.allocate(48, "i8", Module.ALLOC_STACK);
						b || (b = 0);
						REX(Module._TRN_PageGetDefaultMatrix(a, !0, 1, b, d));
						return d;
					};
					c.GetMatrixAsArray = function (a) {
						for (var b = [], d = 0; 6 > d; ++d)
							b[d] = Module.getValue(a + 8 * d, "double");
						return b;
					};
					c.PageGetPageInfo = function (a) {
						var b = Module.allocate(48, "i8", Module.ALLOC_STACK),
							d = Module.allocate(8, "i8", Module.ALLOC_STACK),
							e = Module.allocate(8, "i8", Module.ALLOC_STACK),
							w = Module.allocate(4, "i8", Module.ALLOC_STACK),
							B = Module.allocate(4, "i8", Module.ALLOC_STACK),
							y = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetPageInfo(a, !0, 1, 0, d, e, b, w, B, y));
						return {
							rotation: Module.getValue(w, "i8*"),
							width: Module.getValue(d, "double"),
							height: Module.getValue(e, "double"),
							matrix: Module.GetMatrixAsArray(b),
							linkAnnotCount: Module.getValue(B, "i8*"),
							otherExceptPopupAnnotCount: Module.getValue(y, "i8*"),
						};
					};
					c.GetUStringFromJSString = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK),
							e = 2 * (a.length + 1),
							w = Module.allocate(
								e,
								"i8",
								b ? Module.ALLOC_NORMAL : Module.ALLOC_STACK,
							);
						Module.stringToUTF16(a, w, e);
						a = Module._TRN_UStringCreateFromString(w, d);
						b && Module._free(w);
						REX(a);
						return Module.getValue(d, "i8*");
					};
					c.GetJSStringFromUString = function (a) {
						var b = Module.allocate(4, "i16*", Module.ALLOC_STACK);
						REX(Module._TRN_UStringCStr(a, b));
						return Module.UTF16ToString(Module.getValue(b, "i16*"));
					};
					c.GetJSStringFromCString = function (a) {
						return Module.UTF8ToString(a);
					};
					c.PDFNetSetResourceData = function (a) {
						Module.res_ptr = Module._malloc(a.length);
						Module.HEAPU8.set(a, Module.res_ptr);
						REX(Module._TRN_PDFNetSetResourceData(Module.res_ptr, a.length));
						Module.res_ptr_size = a.length;
					};
					c.PDFDocDestroy = function (a) {
						REX(Module._TRN_PDFDocDestroy(a));
					};
					c.VectorGetSize = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_VectorGetSize(a, b));
						return Module.getValue(b, "i32");
					};
					c.VectorGetAt = function (a, b) {
						var d = Module.allocate(1, "i8*", Module.ALLOC_STACK);
						REX(Module._TRN_VectorGetAt(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.VectorDestroy = function (a) {
						REX(Module._TRN_VectorDestroy(a));
					};
					c.PDFRasterizerCreate = function () {
						var a = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFRasterizerCreate(0, a));
						return Module.getValue(a, "i8*");
					};
					c.ExtractSeparationData = function (a) {
						var b = Module.getValue(a, "i8*"),
							d = Module.getValue(a + 4, "i32"),
							e = Module.getValue(a + 8, "i8*"),
							w = Module.HEAPU8[a + 12],
							B = Module.HEAPU8[a + 13],
							y = Module.HEAPU8[a + 14];
						a = Module.HEAPU8[a + 15];
						var D = new Uint8Array(d);
						D.set(Module.HEAPU8.subarray(b, b + d));
						b = Module.GetJSStringFromUString(e);
						return { color: [w, B, y, a], data: D.buffer, name: b };
					};
					c.ExtractApRefChangeData = function (a) {
						var b = Module.getValue(a, "i32"),
							d = Module.getValue(a + 4, "i32"),
							e = Module.getValue(a + 8, "i32"),
							w = Module.getValue(a + 12, "i32");
						a = 0 !== Module.getValue(a + 16, "i8");
						return {
							oldObjNum: b,
							discardAppearance: a,
							newObjNum: d,
							genNum: e,
							pageNum: w,
						};
					};
					c.PDFRasterizerRasterizeSeparations = function (a, b, d, e, w, B, y) {
						var D = Module.allocate(8, "i8", Module.ALLOC_STACK);
						REX(
							Module._TRN_PDFRasterizerRasterizeSeparations(
								a,
								b,
								d,
								e,
								w,
								B,
								y,
								D,
							),
						);
						a = Module.getValue(D, "i8*");
						b = Module.VectorGetSize(a);
						d = Array(b);
						for (e = 0; e < b; ++e)
							(w = Module.VectorGetAt(a, e)),
								(d[e] = Module.ExtractSeparationData(w));
						Module.VectorDestroy(a);
						return d;
					};
					c.PageGetRotation = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetRotation(a, b));
						return Module.getValue(b, "i8*");
					};
					c.PageGetId = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetSDFObj(a, b));
						b = Module.getValue(b, "i8*");
						a = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjGetObjNum(b, a));
						a = Module.getValue(a, "i32");
						var d = Module.allocate(2, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjGetGenNum(b, d));
						d = Module.getValue(d, "i16");
						return "".concat(a, "-").concat(d);
					};
					c.PageSetRotation = function (a, b) {
						REX(Module._TRN_PageSetRotation(a, b));
					};
					c.GetDefaultMatrixBox = function (a, b, d) {
						var e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetRotation(a, e));
						a = (Module.getValue(e, "i32") + d) % 4;
						d = Module.allocate(48, "i8", Module.ALLOC_STACK);
						switch (a) {
							case 0:
								return (
									REX(Module._TRN_Matrix2DSet(d, 1, 0, 0, -1, -b.x1, b.y2)), d
								);
							case 1:
								return (
									REX(Module._TRN_Matrix2DSet(d, 0, 1, 1, 0, -b.y1, -b.x1)), d
								);
							case 2:
								return (
									REX(Module._TRN_Matrix2DSet(d, -1, 0, 0, 1, b.x2, -b.y1)), d
								);
							case 3:
								return (
									REX(Module._TRN_Matrix2DSet(d, 0, -1, -1, 0, b.y2, b.x2)), d
								);
						}
						throw Error("Yikes, we don't support that rotation type");
					};
					c.Matrix2DMultBBox = function (a, b) {
						var d = Module.allocate(8, "i8", Module.ALLOC_STACK),
							e = Module.allocate(8, "i8", Module.ALLOC_STACK);
						Module.setValue(d, b.x1, "double");
						Module.setValue(e, b.y1, "double");
						REX(Module._TRN_Matrix2DMult(a, d, e));
						b.x1 = Module.getValue(d, "double");
						b.y1 = Module.getValue(e, "double");
						Module.setValue(d, b.x2, "double");
						Module.setValue(e, b.y2, "double");
						REX(Module._TRN_Matrix2DMult(a, d, e));
						b.x2 = Module.getValue(d, "double");
						b.y2 = Module.getValue(e, "double");
						return b;
					};
					c.Matrix2DMult = function (a, b) {
						var d = Module.allocate(8, "i8", Module.ALLOC_STACK),
							e = Module.allocate(8, "i8", Module.ALLOC_STACK);
						Module.setValue(d, b.x, "double");
						Module.setValue(e, b.y, "double");
						REX(Module._TRN_Matrix2DMult(a, d, e));
						b.x = Module.getValue(d, "double");
						b.y = Module.getValue(e, "double");
						return b;
					};
					c.Matrix2DConcat = function (a, b) {
						var d = Module.getValue(b, "double"),
							e = Module.getValue(b + 8, "double"),
							w = Module.getValue(b + 16, "double"),
							B = Module.getValue(b + 24, "double"),
							y = Module.getValue(b + 32, "double");
						b = Module.getValue(b + 40, "double");
						REX(Module._TRN_Matrix2DConcat(a, d, e, w, B, y, b));
					};
					c.Matrix2DSet = function (a, b, d, e, w, B, y) {
						REX(Module._TRN_Matrix2DSet(a, b, d, e, w, B, y));
					};
					c.IteratorHasNext = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_IteratorHasNext(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.IteratorCurrent = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_IteratorCurrent(a, b));
						return Module.getValue(Module.getValue(b, "i8*"), "i8*");
					};
					c.IteratorNext = function (a) {
						REX(Module._TRN_IteratorNext(a));
					};
					c.PageGetNumAnnots = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetNumAnnots(a, b));
						return Module.getValue(b, "i32");
					};
					c.PageGetAnnot = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetAnnot(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.PageAnnotRemove = function (a, b) {
						REX(Module._TRN_PageAnnotRemoveByIndex(a, b));
					};
					c.AnnotGetType = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_AnnotGetType(a, b));
						return Module.getValue(b, "i32");
					};
					c.AnnotHasAppearance = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_AnnotGetAppearance(a, 0, 0, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.AnnotRefreshAppearance = function (a) {
						REX(Module._TRN_AnnotRefreshAppearance(a));
					};
					c.ObjErase = function (a, b) {
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						REX(Module._TRN_ObjEraseFromKey(a, b));
					};
					c.GetJSDoubleArrFromCore = function (a, b) {
						for (var d = Array(b), e = 0; e < b; ++e)
							(d[e] = Module.getValue(a, "double")), (a += 8);
						return d;
					};
					c.GetJSIntArrayFromCore = function (a, b) {
						for (var d = Array(b), e = 0; e < b; ++e)
							(d[e] = Module.getValue(a, "i32")), (a += 4);
						return d;
					};
					c.BookmarkIsValid = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkIsValid(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.BookmarkGetNext = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkGetNext(a, b));
						return Module.getValue(b, "i8*");
					};
					c.BookmarkGetFirstChild = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkGetFirstChild(a, b));
						return Module.getValue(b, "i8*");
					};
					c.BookmarkHasChildren = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkHasChildren(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.BookmarkGetAction = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkGetAction(a, b));
						return Module.getValue(b, "i8*");
					};
					c.BookmarkGetTitle = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_BookmarkGetTitle(a, b));
						a = Module.getValue(b, "i8*");
						return Module.GetJSStringFromUString(a);
					};
					c.ActionIsValid = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ActionIsValid(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.ActionGetType = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ActionGetType(a, b));
						return Module.getValue(b, "i32");
					};
					c.ActionGetDest = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ActionGetDest(a, b));
						return Module.getValue(b, "i8*");
					};
					c.DestinationIsValid = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_DestinationIsValid(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.DestinationGetPage = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_DestinationGetPage(a, b));
						return Module.getValue(b, "i8*");
					};
					c.PageIsValid = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageIsValid(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.PageGetIndex = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageGetIndex(a, b));
						return Module.getValue(b, "i32");
					};
					c.ObjGetAsPDFText = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjGetAsPDFText(a, b));
						a = Module.getValue(b, "i8*");
						return Module.GetJSStringFromUString(a);
					};
					c.ObjFindObj = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						REX(Module._TRN_ObjFindObj(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.PDFDocGetFirstBookmark = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetFirstBookmark(a, b));
						return Module.getValue(b, "i8*");
					};
					c.DestinationGetExplicitDestObj = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_DestinationGetExplicitDestObj(a, b));
						return Module.getValue(b, "i8*");
					};
					c.DestinationGetFitType = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_DestinationGetFitType(a, b));
						return Module.getValue(b, "i32");
					};
					c.ObjIsNumber = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjIsNumber(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.ObjGetNumber = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjGetNumber(a, b));
						return Module.getValue(b, "double");
					};
					c.PDFDocGetRoot = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetRoot(a, b));
						return Module.getValue(b, "i8*");
					};
					c.ObjPutName = function (a, b, d) {
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						d = Module.allocate(
							Module.intArrayFromString(d),
							"i8",
							Module.ALLOC_STACK,
						);
						var e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjPutName(a, b, d, e));
						return Module.getValue(e, "i8*");
					};
					c.ObjPutDict = function (a, b) {
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjPutDict(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.ObjPutString = function (a, b, d) {
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						d = Module.allocate(
							Module.intArrayFromString(d),
							"i8",
							Module.ALLOC_STACK,
						);
						var e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjPutString(a, b, d, e));
						return Module.getValue(e, "i8*");
					};
					c.ObjPut = function (a, b, d) {
						b = Module.allocate(
							Module.intArrayFromString(b),
							"i8",
							Module.ALLOC_STACK,
						);
						var e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjPut(a, b, d, e));
						return Module.getValue(e, "i8*");
					};
					c.ObjGetAt = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ObjGetAt(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.Matrix2DInverse = function (a) {
						var b = Module.allocate(48, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_Matrix2DInverse(a, b));
						return b;
					};
					c.PDFDocInitSecurityHandler = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocInitSecurityHandler(a, 0, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.PDFDocSetSecurityHandler = function (a, b) {
						REX(Module._TRN_PDFDocSetSecurityHandler(a, b));
					};
					c.SecurityHandlerCreate = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_SecurityHandlerCreate(a, b));
						return Module.getValue(b, "i8*");
					};
					c.SecurityHandlerChangeUserPasswordUString = function (a, b) {
						REX(
							Module._TRN_SecurityHandlerChangeUserPasswordUString(
								a,
								Module.GetUStringFromJSString(b),
							),
						);
					};
					c.PDFDocInitStdSecurityHandler = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(
							Module._TRN_PDFDocInitStdSecurityHandlerUString(
								a,
								Module.GetUStringFromJSString(b),
								d,
							),
						);
						return 0 !== Module.getValue(d, "i8");
					};
					c.PDFDocDownloaderTriggerFullDownload = function (a, b) {
						REX(Module._TRN_PDFDocDownloaderTriggerFullDownload(a, b));
					};
					c.PDFDocInsertPages = function (a, b, d, e, w) {
						REX(Module._TRN_PDFDocInsertPageSet(a, b, d, e, w ? 1 : 0, 0));
					};
					c.PDFDocInsertPages2 = function (a, b, d, e, w) {
						REX(Module._TRN_PDFDocInsertPageSet2(a, b, d, e, w ? 1 : 0, 0));
					};
					c.PDFDocMovePages = function (a, b, d) {
						REX(Module._TRN_PDFDocMovePageSet(a, b, a, d, 0, 0));
					};
					c.PDFDocGetPageIterator = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetPageIterator(a, b, d));
						return Module.getValue(d, "i8*");
					};
					c.PDFDocPageRemove = function (a, b) {
						REX(Module._TRN_PDFDocPageRemove(a, b));
					};
					c.PDFDocPageRemove2 = function (a, b) {
						REX(Module._TRN_PDFDocPageRemove2(a, b));
					};
					c.PDFDocPageCreate = function (a, b, d) {
						var e = Module.allocate(40, "i8", Module.ALLOC_STACK);
						Module.setValue(e, 0, "double");
						Module.setValue(e + 8, 0, "double");
						Module.setValue(e + 16, b, "double");
						Module.setValue(e + 24, d, "double");
						Module.setValue(e + 32, 0, "double");
						b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocPageCreate(a, e, b));
						return Module.getValue(b, "i8*");
					};
					c.PDFDocPageInsert = function (a, b, d) {
						REX(Module._TRN_PDFDocPageInsert(a, b, d));
					};
					c.PageSetCreate = function () {
						var a = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageSetCreate(a));
						return Module.getValue(a, "i8*");
					};
					c.PageSetCreateRange = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PageSetCreateRange(d, a, b));
						return Module.getValue(d, "i8*");
					};
					c.PageSetAddPage = function (a, b) {
						REX(Module._TRN_PageSetAddPage(a, b));
					};
					c.ElementBuilderCreate = function () {
						var a = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ElementBuilderCreate(a));
						return Module.getValue(a, "i8*");
					};
					c.ElementBuilderDestroy = function (a) {
						REX(Module._TRN_ElementBuilderDestroy(a));
					};
					c.ElementBuilderCreateImage = function (a, b, d, e, w, B) {
						var y = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(
							Module._TRN_ElementBuilderCreateImageScaled(a, b, d, e, w, B, y),
						);
						return Module.getValue(y, "i8*");
					};
					c.ElementWriterCreate = function () {
						var a = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ElementWriterCreate(a));
						return Module.getValue(a, "i8*");
					};
					c.ElementWriterDestroy = function (a) {
						REX(Module._TRN_ElementWriterDestroy(a));
					};
					c.ElementWriterBegin = function (a, b) {
						REX(Module._TRN_ElementWriterBeginOnPage(a, b, 1, 1, 1, 0));
					};
					c.ElementWriterWritePlacedElement = function (a, b) {
						REX(Module._TRN_ElementWriterWritePlacedElement(a, b));
					};
					c.ElementWriterEnd = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ElementWriterEnd(a, b));
					};
					c.ImageGetImageWidth = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ImageGetImageWidth(a, b));
						return Module.getValue(b, "i32");
					};
					c.ImageGetImageHeight = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ImageGetImageHeight(a, b));
						return Module.getValue(b, "i32");
					};
					c.ImageCreateFromMemory2 = function (a, b, d) {
						var e = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ImageCreateFromMemory2(a, b, d, 0, e));
						return Module.getValue(e, "i8*");
					};
					c.ImageCreateFromFile = function (a, b) {
						var d = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_ImageCreateFromFile(a, b, 0, d));
						return Module.getValue(d, "i8*");
					};
					c.PDFDocPagePushBack = function (a, b) {
						REX(Module._TRN_PDFDocPagePushBack(a, b));
					};
					c.PDFDocHasOC = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocHasOC(a, b));
						return 0 !== Module.getValue(b, "i8");
					};
					c.PDFDocGetOCGConfig = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_PDFDocGetOCGConfig(a, b));
						return Module.getValue(b, "i8*");
					};
					c.OCGContextCreate = function (a) {
						var b = Module.allocate(4, "i8", Module.ALLOC_STACK);
						REX(Module._TRN_OCGContextCreateFromConfig(a, b));
						return Module.getValue(b, "i8*");
					};
					c.UStringDestroy = function (a) {
						REX(Module._TRN_UStringDestroy(a));
					};
					c.PDFDocFDFUpdate = function (a, b, d) {
						if (d) {
							for (
								var e = Object.keys(d),
									w = e.length,
									B = Module._malloc(8 * w),
									y = 0;
								y < w;
								++y
							) {
								var D = 8 * y,
									L = e[y],
									N = Module.GetDoc(d[L]);
								L = Module.GetUStringFromJSString(L);
								Module.setValue(B + D, N, "i8*");
								Module.setValue(B + D + 4, L, "i8*");
							}
							d = Module.allocate(8, "i8", Module.ALLOC_STACK);
							REX(Module._TRN_PDFDocFDFUpdateAppearanceDocs(a, b, B, w, d));
							a = Module.getValue(d, "i8*");
							b = Module.VectorGetSize(a);
							w = Array(b);
							for (B = 0; B < b; ++B)
								(d = Module.VectorGetAt(a, B)),
									(w[B] = Module.ExtractApRefChangeData(d));
							Module.VectorDestroy(a);
							if (b) return w;
						} else REX(Module._TRN_PDFDocFDFUpdate(a, b));
					};
					c.FDFDocDestroy = function (a) {
						REX(Module._TRN_FDFDocDestroy(a));
					};
				})(q.Module);
			}).call(this, u(10).setImmediate);
		},
		function (v, A, u) {
			function p(m) {
				"@babel/helpers - typeof";
				return (
					(p =
						"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
							? function (x) {
									return typeof x;
								}
							: function (x) {
									return x &&
										"function" == typeof Symbol &&
										x.constructor === Symbol &&
										x !== Symbol.prototype
										? "symbol"
										: typeof x;
								}),
					p(m)
				);
			}
			(function (m) {
				m.SetupPDFNetFunctions = function (x) {
					Module._IB_ = [];
					for (
						var k = function F(z) {
								if ("object" === p(z) && null !== z)
									if ("undefined" !== typeof z.byteLength) {
										var E = Module._IB_.length;
										Module._IB_[E] = new Uint8Array(z);
										z = { handle: E, isArrayBufferRef: !0 };
									} else
										Object.keys(z).forEach(function (H) {
											z.hasOwnProperty(H) && (z[H] = F(z[H]));
										});
								return z;
							},
							q = function E(F) {
								"object" === p(F) &&
									null !== F &&
									(F.buffer
										? (F = F.buffer.slice(
												F.byteOffset,
												F.byteOffset + F.length,
											))
										: F.isArrayBufferRef
											? (F = Module._IB_[F.handle].buffer)
											: Object.keys(F).forEach(function (H) {
													F.hasOwnProperty(H) && (F[H] = E(F[H]));
												}));
								return F;
							},
							c = Module._TRN_EMSCreateSharedWorkerInstance(),
							n,
							f = Module._TRN_EMSWorkerInstanceGetFunctionIterator(c),
							h = function (F, E) {
								return new Promise(function (H, I) {
									F = k(F);
									var J = F.docId;
									J = J ? Module.GetDoc(J) : 0;
									(J = Module.EMSCallSharedFunction(c, E, J))
										? I({
												type: "PDFWorkerError",
												message: Module.GetErrToString(J),
											})
										: ((I = Module.EMSGetLastResponse(c)), (I = q(I)), H(I));
								});
							};
						(n = Module._TRN_EMSFunctionIteratorGetNextCommandName(f));
					)
						(n = Module.GetJSStringFromCString(n)), m.queue.onAsync(n, h);
					Module._TRN_EMSFunctionIteratorDestroy(f);
					if (Module._TRN_EMSCreatePDFNetWorkerInstance) {
						var g = {};
						f = function (l, z) {
							x.on(l, z);
							g[l] = !0;
						};
						Module.docPtrStringToIdMap = {};
						var t = function (l) {
							if (l in Module.docPtrStringToIdMap)
								return Module.docPtrStringToIdMap[l];
							throw Error("Couldn't find document ".concat(l));
						};
						m.queue.onAsync("PDFDoc.RequirePage", function (l) {
							return Module.RequirePage(t(l.docId), l.pageNum);
						});
						f("pdfDocCreateFromBuffer", function (l) {
							l = Module.CreateDoc({ type: "array", value: l.buf });
							var z = Module.GetDoc(l).toString(16);
							Module.docPtrStringToIdMap[z] = l;
							return z;
						});
						f("PDFDoc.destroy", function (l) {
							l = t(l.auto_dealloc_obj);
							Module.DeleteDoc(l);
						});
						f("PDFDoc.saveMemoryBuffer", function (l) {
							var z = t(l.doc);
							return Module.SaveHelper(Module.GetDoc(z), z, l.flags).slice(0);
						});
						f("pdfDocCreate", function () {
							var l = Module.CreateDoc({ type: "new" }),
								z = Module.GetDoc(l).toString(16);
							Module.docPtrStringToIdMap[z] = l;
							return z;
						});
						f("GetPDFDoc", function (l) {
							l = l.docId;
							var z = Module.GetDoc(l).toString(16);
							Module.docPtrStringToIdMap[z] = l;
							return z;
						});
						f("ExtractPDFNetLayersContext", function (l) {
							var z = l.layers;
							l = Module.GetDoc(l.docId);
							var F = 0;
							z
								? (F = Module.EMSCreateUpdatedLayersContext(l, z))
								: Module.PDFDocHasOC(l) &&
									((z = Module.PDFDocGetOCGConfig(l)),
									(F = Module.OCGContextCreate(z)));
							return F.toString(16);
						});
						var r = Module._TRN_EMSCreatePDFNetWorkerInstance();
						f = Module._TRN_EMSWorkerInstanceGetFunctionIterator(r);
						for (
							h = function (l) {
								return new Promise(function (z, F) {
									l = k(l);
									var E = Module.EMSCallPDFNetFunction(r, l);
									E
										? F(Module.GetErrToString(E))
										: ((F = Module.EMSGetLastResponse(r)), (F = q(F)), z(F));
								});
							};
							(n = Module._TRN_EMSFunctionIteratorGetNextCommandName(f));
						)
							if (((n = Module.GetJSStringFromCString(n)), !g[n]))
								x.onAsync(n, h);
						Module._TRN_EMSFunctionIteratorDestroy(f);
					}
				};
			})(self);
		},
		function (v, A, u) {
			v = u(9);
			var p = u.n(v),
				m = u(17),
				x = u(18),
				k = u(7),
				q = u(19),
				c = u(1),
				n = u(20);
			(function (f) {
				var h = null;
				f.basePath = "../";
				var g = function () {
					var r = f.pdfWorkerPath || "";
					f.workerBasePath && (f.basePath = f.workerBasePath);
					var l = f.isFull,
						z = l ? "full/" : "lean/";
					f.useOptimizedWorker && (z += n.a);
					var F = f.wasmDisabled,
						E = f.disableObjectURLBlobs,
						H = f.pdfWorkerChunkPaths;
					Object(c.c)();
					f.overriddenPdfWorkerPath &&
						((r = f.overriddenPdfWorkerPath),
						(f.basePath = "../"),
						!Object(k.a)() || F) &&
						(r = "");
					f.basePath = f.externalPath
						? f.externalPath
						: f.basePath + "external/";
					Object(q.a)(
						"".concat(r + z, "PDFNetC"),
						{
							"Wasm.wasm": l ? 1e7 : 4e6,
							"Wasm.js.mem": 1e5,
							".js.mem": 12e6,
							".mem": l ? 2e6 : 6e5,
							disableObjectURLBlobs: E,
						},
						F,
						H,
					);
				};
				f.EmscriptenPDFManager = function () {};
				f.EmscriptenPDFManager.prototype = {
					OnInitialized: function (r) {
						Module.loaded
							? r()
							: ((Module.initCb = function () {
									r();
								}),
								Object(c.b)("worker", "PDFNet is not initialized yet!"));
					},
					NewDoc: function (r, l) {
						return new Promise(function (z, F) {
							try {
								z(Module.loadDoc(r, l));
							} catch (E) {
								F(E);
							}
						});
					},
					Initialize: function (r, l, z, F) {
						r && (Module.TOTAL_MEMORY = r);
						Module.memoryInitializerPrefixURL = l;
						Module.asmjsPrefix = z;
						f.basePath = F;
						g();
					},
					shouldRunRender: function (r) {
						return Module.ShouldRunRender(r.docId, r.pageIndex + 1);
					},
				};
				var t = {
					setup: function (r) {
						function l(e) {
							var w = e.data,
								B = e.action;
							var y =
								"GetCanvas" === B || "GetCanvasPartial" === B
									? H.shouldRunRender(w)
									: !0;
							if (y) {
								h = e;
								var D = e.asyncCallCapability;
								Object(c.b)("Memory", "Worker running command: ".concat(B));
								I.actionMap[B](w, e).then(
									function (L) {
										"BeginOperation" !== h.action && (h = null);
										D.resolve(L);
									},
									function (L) {
										h = null;
										D.reject(L);
									},
								);
							} else f.deferredQueue.queue(e), E();
						}
						function z(e) {
							e.asyncCallCapability = createPromiseCapability();
							h || I.length ? I.queue(e) : l(e);
							return e.asyncCallCapability.promise;
						}
						function F(e) {
							self.shouldResize &&
								H.Initialize(
									e.options.workerHeapSize,
									e.options.pdfResourcePath,
									e.options.pdfAsmPath,
									e.options.parentUrl,
								);
							Module.chunkMax = e.options.chunkMax;
							if (e.array instanceof Uint8Array) {
								var w = 255 === e.array[0];
								r.postMessageTransfers = w;
								"response" in new XMLHttpRequest()
									? H.OnInitialized(function () {
											f.SetupPDFNetFunctions(r);
											a();
											r.send("test", {
												supportTypedArray: !0,
												supportTransfers: w,
											});
										})
									: r.send("test", !1);
							} else r.send("test", !1);
						}
						function E() {
							m.a.setImmediate(function () {
								if ((!h || "BeginOperation" !== h.action) && I.length && !h) {
									var e = I.dequeue();
									l(e);
								}
							});
						}
						var H = new f.EmscriptenPDFManager(),
							I,
							J = !1,
							G = !1;
						Module.workerMessageHandler = r;
						var a = function () {
							J ? G || (r.send("workerLoaded", {}), (G = !0)) : (J = !0);
						};
						H.OnInitialized(a);
						(function () {
							f.queue = I = new p.a({
								strategy: p.a.ArrayStrategy,
								comparator: function (e, w) {
									return e.priority === w.priority
										? e.callbackId - w.callbackId
										: w.priority - e.priority;
								},
							});
							f.deferredQueue = new p.a({
								strategy: p.a.ArrayStrategy,
								comparator: function (e, w) {
									return e.priority === w.priority
										? e.callbackId - w.callbackId
										: w.priority - e.priority;
								},
							});
							I.actionMap = {};
							I.onAsync = function (e, w) {
								r.onAsync(e, z);
								I.actionMap[e] = w;
							};
						})();
						r.on("test", F);
						r.on("InitWorker", F);
						var b = function (e) {
								h && e(h) && (Module.cancelCurrent(), (h = null));
								I.removeAllMatching(e, function (w) {
									w.asyncCallCapability.reject({
										type: "Cancelled",
										message:
											"Operation was cancelled due to a change affecting the loaded document.",
									});
								});
							},
							d = function (e) {
								b(function (w) {
									return w.data && w.data.docId === e;
								});
							};
						r.on("UpdatePassword", function (e) {
							return Module.UpdatePassword(e.docId, e.password);
						});
						r.on("UpdateCustomHeader", function (e) {
							return Module.UpdateCustomHeader(e.docId, e.customHeader);
						});
						r.on("LoadRes", function (e) {
							Module.loadResources(e.array, e.l);
							return {};
						});
						r.on("DownloaderHint", function (e) {
							Module.DownloaderHint(e.docId, e.hint);
						});
						r.on("IsLinearized", function (e) {
							return Module.IsLinearizationValid(e.docId);
						});
						r.onNextAsync(E);
						I.onAsync("NewDoc", function (e) {
							return H.NewDoc(e);
						});
						I.onAsync("GetCanvas", function (e) {
							Object(c.b)(
								"workerdetails",
								"Run GetCanvas PageIdx: "
									.concat(e.pageIndex, " Width: ")
									.concat(e.width),
							);
							Object(c.b)(
								"Memory",
								"loadCanvas with potential memory usage ".concat(
									e.width * e.height * 8,
								),
							);
							return Module.loadCanvas(
								e.docId,
								e.pageIndex,
								e.width,
								e.height,
								e.rotation,
								null,
								e.layers,
								e.renderOptions,
							);
						});
						I.onAsync("GetCanvasPartial", function (e) {
							Object(c.b)(
								"Memory",
								"GetCanvasPartial with potential memory usage ".concat(
									e.width * e.height * 8,
								),
							);
							return Module.loadCanvas(
								e.docId,
								e.pageIndex,
								e.width,
								e.height,
								e.rotation,
								e.bbox,
								e.layers,
								e.renderOptions,
							);
						});
						I.onAsync("SaveDoc", function (e) {
							return Module.SaveDoc(
								e.docId,
								e.xfdfString,
								e.finishedWithDocument,
								e.printDocument,
								e.flags,
								e.watermarks,
								e.apdocs,
								e.password,
								e.encryptionAlgorithmType,
							);
						});
						I.onAsync("SaveDocFromFixedElements", function (e) {
							return Module.SaveDocFromFixedElements(
								e.bytes,
								e.xfdfString,
								e.flags,
								e.watermarks,
								e.password,
								e.encryptionAlgorithmType,
							);
						});
						I.onAsync("MergeXFDF", function (e) {
							return Module.MergeXFDF(e.docId, e.xfdf, e.apdocs);
						});
						I.onAsync("InsertPages", function (e) {
							return Module.InsertPages(
								e.docId,
								e.doc,
								e.pageArray,
								e.destPos,
								e.insertBookmarks,
								e.skipUpdateEvent,
							);
						});
						I.onAsync("MovePages", function (e) {
							return Module.MovePages(e.docId, e.pageArray, e.destPos);
						});
						I.onAsync("RemovePages", function (e) {
							return Module.RemovePages(
								e.docId,
								e.pageArray,
								e.skipUpdateEvent,
							);
						});
						I.onAsync("RotatePages", function (e) {
							return Module.RotatePages(e.docId, e.pageArray, e.rotation);
						});
						I.onAsync("ExtractPages", function (e) {
							return Module.ExtractPages(
								e.docId,
								e.pageArray,
								e.xfdfString,
								e.watermarks,
								e.apdocs,
								e.skipUpdateEvent,
							);
						});
						I.onAsync("CropPages", function (e) {
							return Module.CropPages(
								e.docId,
								e.pageArray,
								e.topMargin,
								e.botMargin,
								e.leftMargin,
								e.rightMargin,
							);
						});
						I.onAsync("TriggerFullDownload", function (e) {
							return Module.TriggerFullDownload(e.docId);
						});
						I.onAsync("InsertBlankPages", function (e) {
							return Module.InsertBlankPages(
								e.docId,
								e.pageArray,
								e.width,
								e.height,
							);
						});
						I.onAsync("BeginOperation", function () {
							return Promise.resolve();
						});
						I.onAsync("RequirePage", function (e, w) {
							return Module.RequirePage(e.docId, e.pageNum);
						});
						r.on("FinishOperation", function () {
							if (h && "BeginOperation" === h.action) (h = null), E();
							else throw { message: "Operation has not started." };
						});
						r.on("DeleteDocument", function (e) {
							e = e.docId;
							d(e);
							Module.DeleteDoc(e);
						});
						r.on("GetCanvasProgressive", function (e) {
							if (h && h.callbackId === e.callbackId) {
								Object(c.b)("worker", "Progressive request in progress");
								var w = Module.GetCurrentCanvasData(!0);
							} else {
								if (I.find({ priority: 0, callbackId: e.callbackId }))
									throw (
										(Object(c.b)("worker", "Progressive request Queued"),
										{
											type: "Queued",
											message: "Rendering has not started yet.",
										})
									);
								if (
									f.deferredQueue.find({
										priority: 0,
										callbackId: e.callbackId,
									})
								)
									throw (
										(Object(c.b)("worker", "Progressive request Deferred"),
										{
											type: "Queued",
											message: "Rendering has not started yet.",
										})
									);
							}
							if (!w)
								throw (
									(Object(c.b)(
										"worker",
										"Progressive request invalid (render already complete)",
									),
									{
										type: "Unavailable",
										message: "Rendering is complete or was cancelled.",
									})
								);
							return w;
						});
						r.on("actionCancel", function (e) {
							h && h.callbackId === e.callbackId
								? (Object(c.b)("workerdetails", "Cancelled Current Operation"),
									Module.cancelCurrent() && ((h = null), E()))
								: (Object(c.b)("workerdetails", "Cancelled queued operation"),
									I.remove({ priority: 0, callbackId: e.callbackId }),
									f.deferredQueue.remove({
										priority: 0,
										callbackId: e.callbackId,
									}));
						});
					},
				};
				f.onmessage = function (r) {
					if ("init" === r.data.action) {
						var l = r.data.shouldResize;
						f.shouldResize = l;
						f.isFull = r.data.isFull;
						f.wasmDisabled = !r.data.wasm;
						f.externalPath = r.data.externalPath;
						f.useOptimizedWorker = r.data.useOptimizedWorker;
						f.disableObjectURLBlobs = r.data.disableObjectURLBlobs;
						f.pdfWorkerChunkPaths = r.data.pdfWorkerChunkPaths;
						if ((r = r.data.pdfWorkerPath)) f.overriddenPdfWorkerPath = r;
						l || g();
						l = new x.a("worker_processor", self);
						Object(c.a)(l);
						t.setup(l);
					}
				};
			})("undefined" === typeof window ? self : window);
		},
	]);
}).call(this || window);
