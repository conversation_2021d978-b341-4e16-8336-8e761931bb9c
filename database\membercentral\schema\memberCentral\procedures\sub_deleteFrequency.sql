ALTER PROC dbo.sub_deleteFrequency
@siteID int, 
@frequencyID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @frequencyName varchar(50), @msgjson varchar(max), @crlf varchar(2), @subKeyMapJSON varchar(100);

	SELECT @orgID = orgID FROM dbo.sites WHERE siteID = @siteID;
	SET @crlf = char(13) + char(10);
	
	SELECT @frequencyName = frequencyName
	FROM dbo.sub_frequencies
	WHERE frequencyID = @frequencyID;
	
	SET @subKeyMapJSON = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
	SET @msgjson = 'Frequency [' + STRING_ESCAPE(@frequencyName,'json') + '] has been Deleted.';
	
	BEGIN TRAN;
		UPDATE dbo.sub_frequencies
		SET [status] = 'D'
		WHERE frequencyID = @frequencyID
		AND siteID = @siteID
		AND [status] = 'A'
		AND isSystemRate = 0;

		IF @@ROWCOUNT > 0
			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
