<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = buildRightAssignments(this.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

		this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);

		// build quick links -------------------------------------------------------------------------- ::
		this.link.message = buildCurrentLink(arguments.event,"message");
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.add = buildCurrentLink(arguments.event,"add");
		this.link.edit = buildCurrentLink(arguments.event,"edit")& "&mode=direct";
		this.link.save = buildCurrentLink(arguments.event,"save");

		// Run Assigned Method ---------------------------------------------------------------------- ::
		local.methodToRun = this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it. ----------------------- ::
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
		
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="prepResult" type="string" required="false">

		<cfscript>
			var local = structNew();
			local.listInvoiceProfilesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=invoiceProfilesJSON&meth=getInvoiceProfiles&mode=stream";
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceProfileManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfset local.exportInvoiceProfilesZIPLink = buildCurrentLink(arguments.event,"exportInvoiceProfilesZIP") & "&mode=stream">
		<cfset local.prepareInvoiceProfilesImportLink = buildCurrentLink(arguments.event,"prepareInvoiceProfilesImport")>
		<cfset local.doImportInvoiceProfilesLink = buildCurrentLink(arguments.event,"doImportInvoiceProfiles") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		arguments.event.paramValue('profileID',0);

		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		if (arguments.event.getValue('profileID')) { local.pageTitle = 'Edit Invoice Profile'; }
		else { local.pageTitle = 'Add Invoice Profile'; }
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceProfileManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<cfquery name="local.invoiceProfile" datasource="#application.dsn.memberCentral.dsn#">
			SELECT profileID, profileName, imageExt, enableAutoPay, enforcePayOldest, allowPartialPayment, 
				notifyEmail, numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect,
				solicitationMessageID, orgIdentityID
			FROM dbo.tr_invoiceProfiles
			WHERE profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(
				orgID=arguments.event.getValue('mc_siteinfo.orgID'),
				selectorID="orgIdentityID",
				selectedValueID=val(local.invoiceProfile.orgIdentityID),
				allowBlankOption=false
			)>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qrySelectedMerchantProfiles">
			select merchantProfileID
			from dbo.tr_invoiceProfilesMerchantProfiles
			where invoiceProfileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfquery name="local.qryPayProfilesSupportingProcessingFees" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileID, mp.profileName
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
			WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			AND mp.[status] IN ('A','I')
			AND mp.enableProcessingFeeDonation = 1
			AND g.gatewayType = 'AuthorizeCCCIM'
			AND g.isActive = 1
			ORDER BY mp.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_invoiceProfile.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="save" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceProfileManage') is not 1>
			<cflocation url="#this.link.message#&ec=LNP" addtoken="no">
		</cfif>

		<!--- ensure there isnt a profile with that name already --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCheckName">
			SELECT profileID
			FROM dbo.tr_invoiceProfiles
			WHERE profileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('profileName')#">
			AND profileID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">
			AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND status = 'A'
		</cfquery>
		<cfif local.qryCheckName.recordCount>
			<cflocation url="#this.link.edit#&profileID=#arguments.event.getValue('profileID')#&err=2" addtoken="no">
		</cfif>

		<cfif arguments.event.getValue('profileID',0) GT 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryupdate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @invoiceProfileID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('profileID')#">;
					DECLARE @numDaysDelinquent int, @delinDate date, @currentnumDaysDelinquent int, @ins_delinquent int, @ins_closed int;

					<cfif val(arguments.event.getValue('numDaysDelinquent')) gt 0>
						SET @numDaysDelinquent = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('numDaysDelinquent')#">;
						SET @delinDate = DateAdd(DD,@numDaysDelinquent*-1,getdate());
					</cfif>
					SELECT @ins_delinquent = statusID from dbo.tr_invoiceStatuses where [status] = 'Delinquent';
					SELECT @ins_closed = statusID from dbo.tr_invoiceStatuses where [status] = 'Closed';
					SELECT @currentnumDaysDelinquent = numDaysDelinquent from dbo.tr_invoiceProfiles where profileID = @invoiceProfileID;

					BEGIN TRAN;
						UPDATE dbo.tr_invoiceProfiles 
						SET profileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('profileName')#">,
							enableAutoPay = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('enableAutoPay')#">,
							enforcePayOldest = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('enforcePayOldest')#">,
							allowPartialPayment = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('allowPartialPayment')#">,
							notifyEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(arguments.event.getTrimValue('notifyEmail'),100)#">,
							numDaysDelinquent = @numDaysDelinquent,
							enableProcessingFeeDonation = 
								<cfif listFind("0,1",arguments.event.getValue('enableProcessingFeeDonation',''))>
									<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('enableProcessingFeeDonation')#">,
								<cfelse>
									NULL,
								</cfif>
							<cfif arguments.event.getValue('enableProcessingFeeDonation','') EQ 1>
								processFeeDonationDefaultSelect = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('processFeeDonationDefaultSelect')#">,
								solicitationMessageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('solicitationMessageID')#">,
							<cfelse>
								processFeeDonationDefaultSelect = NULL,
								solicitationMessageID = NULL,
							</cfif>
							orgIdentityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('orgIdentityID',0)#">
						WHERE profileID = @invoiceProfileID
						AND orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
						AND status = 'A';

						<!--- if numDaysDelinquent is blank, ensure no invoices are delinquent - move them to closed --->
						<cfif NOT val(arguments.event.getValue('numDaysDelinquent')) gt 0>
							IF @currentnumDaysDelinquent is not null BEGIN
								IF EXISTS (
									select invoiceID 
									from dbo.tr_invoices 
									where invoiceProfileID = @invoiceProfileID
									and statusID = @ins_delinquent
									) 
								BEGIN
									INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
									SELECT invoiceID, getdate(), @ins_closed, @ins_delinquent, <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
									FROM dbo.tr_invoices 
									WHERE invoiceProfileID = @invoiceProfileID
									AND statusID = @ins_delinquent;

									UPDATE dbo.tr_invoices
									SET statusID = @ins_closed
									WHERE invoiceProfileID = @invoiceProfileID
									AND statusID = @ins_delinquent;
								END
							END
						
						<!--- else look at the invoices to see if we need to move to/from delinq --->
						<cfelse>
							IF isnull(@currentnumDaysDelinquent,0) <> @numDaysDelinquent BEGIN
								INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								SELECT invoiceID, getdate(), @ins_delinquent, @ins_closed, <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
								FROM dbo.tr_invoices 
								WHERE invoiceProfileID = @invoiceProfileID
								AND statusID = @ins_closed
								AND dateDue <= @delinDate;

								UPDATE dbo.tr_invoices
								SET statusID = @ins_delinquent
								WHERE invoiceProfileID = @invoiceProfileID
								AND statusID = @ins_closed
								AND dateDue <= @delinDate;

								INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
								SELECT invoiceID, getdate(), @ins_closed, @ins_delinquent, <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">
								FROM dbo.tr_invoices 
								WHERE invoiceProfileID = @invoiceProfileID
								AND statusID = @ins_delinquent
								AND dateDue > @delinDate;

								UPDATE dbo.tr_invoices
								SET statusID = @ins_closed
								WHERE invoiceProfileID = @invoiceProfileID
								AND statusID = @ins_delinquent
								AND dateDue > @delinDate;
							END
						</cfif>
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfelse>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryinsert">
				set nocount on;
				
				declare @profileID int;
				
				INSERT INTO dbo.tr_invoiceProfiles (orgID, profileName, status, imageExt, enableAutoPay, enforcePayOldest, allowPartialPayment, 
					notifyEmail, numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityID)
				VALUES (
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
					<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('profileName')#">,
					'A', NULL,
					<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('enableAutoPay')#">,
					<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('enforcePayOldest')#">,
					<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getTrimValue('allowPartialPayment')#">,
					<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.event.getTrimValue('notifyEmail'),100)#">,
					<cfif val(arguments.event.getValue('numDaysDelinquent')) gt 0>
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('numDaysDelinquent')#">,
					<cfelse>
						NULL,
					</cfif>
					<cfif listFind("0,1",arguments.event.getValue('enableProcessingFeeDonation',''))>
						<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('enableProcessingFeeDonation')#">,
					<cfelse>
						NULL,
					</cfif>
					<cfif arguments.event.getValue('enableProcessingFeeDonation','') EQ 1>
						<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('processFeeDonationDefaultSelect')#">,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('solicitationMessageID')#">,
					<cfelse>
						NULL,
						NULL,
					</cfif>
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('orgIdentityID',0)#">
				);
				SELECT @profileID = SCOPE_IDENTITY();

				select @profileID as profileID;
			</cfquery>
			<cfset arguments.event.setValue('profileID',local.qryinsert.profileID)>
		</cfif>
		
		<!--- handle pay profiles --->
		<cfquery name="local.qryUpdateInvProfileMPProfileIDs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @invoiceProfileID int = <cfqueryparam value="#arguments.event.getValue('profileID')#" cfsqltype="CF_SQL_INTEGER">;

			MERGE dbo.tr_invoiceProfilesMerchantProfiles AS target  
			USING (
				SELECT @invoiceProfileID as invoiceProfileID, mp.profileID as merchantProfileID
				from dbo.fn_IntListToTable(<cfqueryparam value="#arguments.event.getValue('invpMPProfiles','')#" cfsqltype="CF_SQL_VARCHAR">,',') as li
				inner join dbo.mp_profiles as mp on mp.profileID = li.listItem
				) AS source (invoiceProfileID, merchantProfileID)
			ON (target.invoiceProfileID = @invoiceProfileID and target.merchantProfileID = source.merchantProfileID)
			WHEN NOT MATCHED BY TARGET THEN
				INSERT (invoiceProfileID, merchantProfileID)
				VALUES (@invoiceProfileID, source.merchantProfileID)
			WHEN NOT MATCHED BY SOURCE AND target.invoiceProfileID = @invoiceProfileID THEN
				DELETE;
		</cfquery>

		<!--- handle image deletion --->
		<cfif arguments.event.getValue('profileID',0) GT 0 AND (Len(arguments.event.getValue('deleteImage','')) OR len(arguments.event.getValue('invoiceImage',"")))>
			<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
				select imageExt 
				from dbo.tr_invoiceProfiles
				where profileID = <cfqueryparam value="#arguments.event.getValue('profileID')#" cfsqltype="CF_SQL_INTEGER">
				AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
				AND status = 'A'
			</cfquery>
			<cfif len(local.qryImageExt.imageExt) AND fileExists("#application.paths.RAIDUserAssetRoot.path#common/invoices/#arguments.event.getValue('profileID')#.#local.qryImageExt.imageExt#")>
				<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/invoices/#arguments.event.getValue('profileID')#.#local.qryImageExt.imageExt#">
				<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
					update dbo.tr_invoiceProfiles
					set imageExt = null
					where profileID = <cfqueryparam value="#arguments.event.getValue('profileID')#" cfsqltype="CF_SQL_INTEGER">
					AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
					AND status = 'A'
				</cfquery>
			</cfif>
		</cfif>
		
		<!--- handle image uploading --->
		<cfif len(arguments.event.getValue('invoiceImage',""))>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.destination = "#application.paths.RAIDUserAssetRoot.path#common/invoices/">
			<cffile action="upload" filefield="invoiceImage" destination="#local.strFolder.folderPath#" nameConflict="makeUnique" result="local.upload">
			
			<cfif listFindNoCase("png,gif",local.upload.ServerFileExt)>
				<cfscript>
				local.imageOK = true;
				try {
					local.origPhoto = "#local.upload.ServerDirectory#/#local.upload.ServerFile#";
					local.origPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.origPhoto);

					if (local.origPhotoInfo.imageInfo.source.width neq 400 OR local.origPhotoInfo.imageInfo.source.height neq 150) {
						local.imageOK = false;
					}
				}
				catch( any excpt ){
					local.imageOK = false;
					application.objError.sendError(cfcatch=excpt);
				}
				</cfscript>

				<cfif local.imageOK>
					<cffile action="move" destination="#local.destination##arguments.event.getValue('profileID')#.#local.upload.ServerFileExt#" source="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
					<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
						update dbo.tr_invoiceProfiles
						set imageExt = <cfqueryparam value="#local.upload.ServerFileExt#" cfsqltype="CF_SQL_VARCHAR">
						where profileID = <cfqueryparam value="#arguments.event.getValue('profileID')#" cfsqltype="CF_SQL_INTEGER">
						AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
						AND status = 'A'
					</cfquery>
				</cfif>
			<cfelse>
				<cfset local.imageOK = false>
			</cfif>

			<cfif NOT local.imageOK and FileExists("#local.upload.ServerDirectory#/#local.upload.ServerFile#")>
				<cffile action="delete" file="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
				<cflocation url="#this.link.edit#&profileID=#arguments.event.getValue('profileID')#&err=1" addtoken="no">
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
							top.reloadProfiles();
							top.$('.modal-backdrop').remove();
							
							top.editProfile(#arguments.event.getValue('profileID')#);
						</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo") />	
			</cfif>

		</cfif>
		
		<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.reloadProfiles();				
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="deleteProfile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
			select imageExt 
			from dbo.tr_invoiceProfiles
			where profileID = <cfqueryparam value="#arguments.profileID#" cfsqltype="CF_SQL_INTEGER">
			AND orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_orgID#">
			AND status = 'A'
		</cfquery>

		<cftry>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceProfilesAdmin', siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif  local.tmpRights.invoiceProfileManage is not 1>
				<cfthrow message="invalid request">
 			</cfif>

			<cfstoredproc procedure="tr_deleteInvoiceProfile" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
			</cfstoredproc>
			<cfif FileExists("#application.paths.RAIDUserAssetRoot.path#common/invoices/#arguments.profileID#.#local.qryImageExt.imageExt#")>
				<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/invoices/#arguments.profileID#.#local.qryImageExt.imageExt#">
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry!</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="LNP">You do not have the necessary permissions to manage Invoice Profiles.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- export/import --->
	<cffunction name="exportInvoiceProfilesZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "InvoiceProfilesStructure.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_exportInvoiceProfilesStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="false">
		</cfif>
	</cffunction>

	<cffunction name="prepareInvoiceProfilesImport" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>
		
		<cfsetting requesttimeout="500">
	
		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>
			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/InvoiceProfilesStructure.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/InvoiceProfilesStructure.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>
		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/InvoiceProfilesStructure.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name in ('sync_tr_invoiceProfiles.bcp','sync_tr_invoiceProfilesMerchantProfiles.bcp','sync_tr_solicitationMessages.bcp')
				</cfquery>
				<cfif local.qryFiles.recordcount neq 3>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain three.">
				<cfelseif local.qryFilesCheck.theCount neq 3>
					<cfthrow message="Required files in the backup file is missing.">
				</cfif>
				<cfzip file="#local.strImportFile.strFolder.folderPath#/InvoiceProfilesStructure.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/InvoiceProfilesStructure.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>

  		<!--- prepare import --->
  		<cfif local.rs.success>

			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Invoice Profiles Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>

			<cfset local.InvoiceProfilesImportStruct = application.mcCacheManager.sessionGetValue(keyname='InvoiceProfilesImportStruct', defaultValue=structNew())>
			<cfset structInsert(local.InvoiceProfilesImportStruct, local.threadID, local.strImportFile.strFolder, true)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='InvoiceProfilesImportStruct', value=local.InvoiceProfilesImportStruct)>
			
			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareInvoiceProfilesImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>
			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isInvoiceProfilesImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.prepResult">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.data = list(event=arguments.event, prepResult=local.prepResult)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareInvoiceProfilesImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.tr_prepareInvoiceProfilesImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					SET @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showInvoiceProfilesImportCompareResults(orgID=arguments.paramStruct.orgID, siteID=arguments.paramStruct.siteID, threadID=arguments.paramStruct.threadID, 
			strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/InvoiceProfilesImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showInvoiceProfilesImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewInvoiceProfiles = XMLSearch(arguments.strResult.importResultXML,"/import/newinvoiceprofiles/invoiceprofile")>
			<cfset local.strImportResult.arrUpdateInvoiceProfiles = XMLSearch(arguments.strResult.importResultXML,"/import/updateinvoiceprofiles/invoiceprofile")>
			<cfset local.strImportResult.arrRemoveInvoiceProfiles = XMLSearch(arguments.strResult.importResultXML,"/import/removeinvoiceprofiles/invoiceprofile")>
			<cfset local.strImportResult.arrNewSolicitationmsgs = XMLSearch(arguments.strResult.importResultXML,"/import/newsolicitationmsgs/solicitationmsg")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importReport = generateInvoiceProfilesImportResultsReport(orgID=arguments.orgID, siteID=arguments.siteID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateInvoiceProfilesImportErrorReport(orgID=arguments.orgID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Invoice Profiles Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelInvoiceProfilesImport(orgID=arguments.orgID, siteID=arguments.siteID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Invoice Profiles Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
				</cfif>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col-xl-12">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Invoice Profiles Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateInvoiceProfilesImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateInvoiceProfiles)>
			<cfquery name="local.qryImportFileUpdateInvoiceProfiles" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct sip.profileID as syncInvoiceProfileID, sip.profileName, sip.enableAutoPay, sip.enforcePayOldest, sip.notifyEmail, 
					sip.allowPartialPayment, sip.numDaysDelinquent, sip.enableProcessingFeeDonation, sip.processFeeDonationDefaultSelect, 
					spfm.title, spfm.message, sip.orgIdentityOrgName
				from dbo.sync_tr_invoiceProfiles as sip
				left outer join dbo.sync_tr_solicitationMessages as spfm on spfm.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					and spfm.messageID = sip.solicitationMessageID
				where sip.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and sip.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateInvoiceProfiles" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				SELECT ip.profileID, ip.profileName, ip.enableAutoPay, ip.enforcePayOldest, isnull(ip.notifyEmail,'') as notifyEmail, 
					ip.allowPartialPayment, isnull(ip.numDaysDelinquent,0) as numDaysDelinquent, ip.enableProcessingFeeDonation,
					ip.processFeeDonationDefaultSelect, pfm.title, pfm.message, oi.organizationName AS orgIdentityOrgName
				FROM dbo.tr_invoiceProfiles as ip
				INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
					AND oi.orgIdentityID = ip.orgIdentityID
				LEFT OUTER JOIN dbo.tr_solicitationMessages as pfm on pfm.siteID = @siteID
					AND pfm.messageID = ip.solicitationMessageID
				WHERE ip.orgID = @orgID
				AND ip.profileName in (#listQualify(valueList(local.qryImportFileUpdateInvoiceProfiles.profileName), "'")#)
				ORDER BY ip.profileName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileInvoiceProfilesMerchantProfiles" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select sipmp.invoiceProfileID as syncInvoiceProfileID, mp.profileName
				from dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
				inner join memberCentral.dbo.mp_profiles as mp on mp.profileID = sipmp.useMerchantProfileID
				where sipmp.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and sipmp.invoiceProfileID in (0#valueList(local.qryImportFileUpdateInvoiceProfiles.syncInvoiceProfileID)#);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgInvoiceProfilesMerchantProfiles" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select ipmp.invoiceProfileID, mp.profileName
				from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
				inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
				inner join dbo.sites as s on s.siteID = mp.siteID
				where s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				and mp.[status] = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateInvoiceProfilesImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportInvoiceProfiles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">
		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>

		<cfsetting requesttimeout="500">
		
		<cfset local.InvoiceProfilesImportStruct = application.mcCacheManager.sessionGetValue(keyname='InvoiceProfilesImportStruct', defaultValue=structNew())>
		<cfif NOT structKeyExists(local.InvoiceProfilesImportStruct, local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Invoice Profiles. The import data is no longer available.">
		<cfelse>
			<cfquery name="local.qryInvoiceProfilesHavingImageExt" datasource="#application.dsn.membercentral.dsn#">
				select profileID, imageExt
				from dbo.tr_invoiceProfiles
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
				and status = 'A'
				and imageExt is not null
			</cfquery>

			<cftry>
				<cfstoredproc procedure="tr_importInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Invoice Profiles file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- delete images for deleted invoice profiles --->
			<cfif local.success AND local.qryInvoiceProfilesHavingImageExt.recordCount>
				<cftry>
					<cfquery name="local.qryDeletedInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
						select tmp.listitem as profileID 
						from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#valueList(local.qryInvoiceProfilesHavingImageExt.profileID)#">,',') as tmp
						left outer join dbo.tr_invoiceProfiles as i on i.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
							and i.status = 'A'
							and i.profileID = tmp.listitem
						where i.profileID is null
					</cfquery>

					<cfloop query="local.qryDeletedInvoiceProfiles">
						<cfquery name="local.qryImageExt" dbtype="query">
							select imageExt
							from [local].qryInvoiceProfilesHavingImageExt
							where profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDeletedInvoiceProfiles.profileID#">
						</cfquery>

						<cfif FileExists("#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryDeletedInvoiceProfiles.profileID#.#local.qryImageExt.imageExt#")>
							<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryDeletedInvoiceProfiles.profileID#.#local.qryImageExt.imageExt#">
						</cfif>
					</cfloop>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.success = false>
					<cfset local.resultMessage = "There was a problem deleting the Invoice Profiles images.<br/>" & cfcatch.message>
				</cfcatch>
				</cftry>
			</cfif>

			<!--- when done, remove from struct --->
			<cfset StructDelete(local.InvoiceProfilesImportStruct, local.threadID)>
			<cfif structCount(local.InvoiceProfilesImportStruct)>
				<cfset application.mcCacheManager.sessionSetValue(keyname='InvoiceProfilesImportStruct', value=local.InvoiceProfilesImportStruct)>
			<cfelse>
				<cfset application.mcCacheManager.sessionDeleteValue(keyname='InvoiceProfilesImportStruct')>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelInvoiceProfilesImport" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_tr_invoiceProfiles WHERE orgID = @orgID;
					DELETE FROM dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgID = @orgID;
					DELETE FROM dbo.sync_tr_solicitationMessages WHERE siteID = @siteID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		<cfset local.InvoiceProfilesImportStruct = application.mcCacheManager.sessionGetValue(keyname='InvoiceProfilesImportStruct', defaultValue=structNew())>

		<cftry>
			<cfif structKeyExists(local.InvoiceProfilesImportStruct, arguments.reportuid)>
				<cfset local.reportFileName = local.InvoiceProfilesImportStruct[arguments.reportuid].folderPath & "/InvoiceProfilesImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>