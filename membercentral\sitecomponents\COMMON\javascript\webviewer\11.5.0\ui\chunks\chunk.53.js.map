{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageReplacementModal/FileListPanel/FileListPanel.scss?fadd", "webpack:///./src/ui/src/components/PageReplacementModal/FileListPanel/FileListPanel.scss", "webpack:///./src/ui/src/components/PageReplacementModal/FileSelectedPanel/FileSelectedPanel.scss?bd97", "webpack:///./src/ui/src/components/PageReplacementModal/FileSelectedPanel/FileSelectedPanel.scss", "webpack:///./src/ui/src/components/PageReplacementModal/FileListPanel/index.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileListPanel/FileListPanel.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileSelectedPanel/FileSelectedPanel.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileSelectedPanel/FileSelectedPanelContainer.js", "webpack:///./src/ui/src/components/PageReplacementModal/FileSelectedPanel/index.js", "webpack:///./src/ui/src/components/PageReplacementModal/PageReplacementModal.js", "webpack:///./src/ui/src/components/PageReplacementModal/PageReplacementModalContainer.js", "webpack:///./src/ui/src/components/PageReplacementModal/PageReplacementModalRedux.js", "webpack:///./src/ui/src/components/PageReplacementModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FileListPanel", "defaultValue", "onFileSelect", "list", "id", "map", "item", "isSelected", "modalClass", "classNames", "selected", "thumbnail", "hasOwnProperty", "src", "url", "toDataURL", "img", "className", "getFileThumbnail", "tabIndex", "key", "onClick", "find", "onRowClick", "onKeyDown", "event", "onRowKeyDown", "filename", "FileSelectedPanel", "React", "forwardRef", "ref", "closeThisModal", "clearLoadedFile", "pageIndicesToReplace", "sourceDocument", "replacePagesHandler", "documentInViewer", "closeModalWarning", "t", "useTranslation", "useState", "index", "currentDocSelectedPageNumbers", "setCurrentDocSelectedPageNumbers", "sourceDocSelectedPageNumbers", "setSourceDocSelectedPageNumbers", "selectedThumbnails", "setSelectedThumbnails", "isLoading", "setIsLoading", "sourceDocumentName", "setSourceDocumentName", "currentDocumentName", "setCurrentDocumentName", "sourceDocumentPageCount", "setSourceDocumentPageCount", "pageNumberError", "setPageNumberError", "sourceDocPagesNumberError", "setSourceDocPagesNumberError", "isTablet", "isTabletSize", "isMobile", "isMobileSize", "getPageNumbersFromSelectedThumbnails", "selectedPageNumbers", "pageNumber", "parseInt", "useEffect", "pageCount", "getPageCount", "selectedPages", "getTruncatedName", "documentName", "slice", "getFilename", "onSourceDocumentNumberInputChange", "selectedPagesMap", "reduce", "onCloseHandler", "loadedDocumentPageCount", "onMouseDown", "e", "stopPropagation", "ModalWrapper", "title", "closeButtonDataElement", "onCloseClick", "swipeToClose", "<PERSON><PERSON><PERSON><PERSON>", "backButtonDataElement", "onBackClick", "PageNumberInput", "onSelectedPageNumbersChange", "pageNumbers", "onBlurHandler", "onError", "PageThumbnailsGrid", "onThumbnailSelected", "undefined", "onfileLoa<PERSON><PERSON><PERSON><PERSON>", "isFileSelected", "disabled", "<PERSON><PERSON>", "pagesToReplaceIntoDocument", "label", "pageIndex", "isReplaceButtonDisabled", "displayName", "FileSelectedPanelContainer", "props", "core", "getDocument", "replacePages", "isValidUrlRegex", "RegExp", "loadAsPDF", "PageReplacementModal", "closeModal", "selectableFiles", "isOpen", "selectedThumbnailPageIndexes", "selectedTab", "source", "setSource", "selectedDoc", "setSelectedDoc", "setIsFileSelected", "selectedTabInternal", "setSelectedTabInternal", "dispatch", "useDispatch", "customizableUI", "useSelector", "state", "selectors", "getFeatureFlags", "getRootNode", "querySelector", "value", "exitPageReplacementWarning", "Modal", "open", "closed", "srcString", "handleSelection", "onSelect", "createDocument", "fileProcessedHandler", "file", "getInstanceNode", "instance", "Core", "Document", "isSelectBtnDisabled", "test", "isFilePanelEnabled", "clearDocument", "data-element", "Tabs", "dataElement", "selection", "FileInputPanel", "FilePickerPanel", "fileInputId", "onFileProcessed", "noFile", "PageReplacementModalContainer", "closePageReplacement", "rest", "useCallback", "newProps", "PageReplacementModalRedux", "getPageReplacementFileList", "isElementOpen", "DataElements", "PAGE_REPLACEMENT_MODAL", "getSelectedThumbnailPageIndexes", "getSelectedTab", "actions", "closeElement"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,u+DAAw+D,KAGjgE0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,qBCVvB,IAAIhC,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAqE,IAKxFR,KAAK,CAACnB,EAAOC,EAAI,+8QAAg9Q,KAGz+Q0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,uUCRRC,G,oBCEO,SAAH,GAAkD,IAA5CC,EAAY,EAAZA,aAAcC,EAAY,EAAZA,aAAY,IAAEC,YAAI,IAAG,KAAE,EACpDC,GAAOH,GAAgB,IAAvBG,GA4BFlB,EAAWiB,EAAKE,KAAI,SAACC,EAAMlC,GAC/B,IAAMmC,EAAaH,IAAOE,EAAKF,GACzBI,EAAaC,IAAW,CAC5BC,SAAUH,IAGNI,EAtBiB,SAACL,GACxB,GAAIA,EAAKM,eAAe,aAAc,CACpC,IAAIC,EAAM,KACJF,EAAYL,EAAKK,UACnBA,EAAUG,IACZD,EAAMF,EAAUG,IACPH,EAAUI,YACnBF,EAAMF,EAAUI,aAGlB,IAAMC,EAAM,yBAAKH,IAAKA,EAAKI,UAAU,eACrC,OAAQ,yBAAKA,UAAU,UAAUD,GAEnC,OAAO,KASWE,CAAiBZ,GAEnC,OACE,wBAAIa,SAAS,IAAIC,IAAKhD,EAAGiD,QAAS,kBAnCnB,SAACjB,GAClBF,EAAaC,EAAKmB,MAAK,SAAChB,GAAI,OAAKA,EAAKF,KAAOA,MAkCHmB,CAAWjB,EAAKF,KAAKoB,UAAW,SAACC,GAAK,OA/B7D,SAACA,EAAOrB,GACT,UAAdqB,EAAML,KACRlB,EAAaC,EAAKmB,MAAK,SAAChB,GAAI,OAAKA,EAAKF,KAAOA,MA6BsCsB,CAAaD,EAAOnB,EAAKF,KAAKa,UAAWT,GACzHG,EACD,yBAAKM,UAAU,cAAeX,EAAKqB,cAKzC,OACE,yBAAKV,UAAU,iBACb,4BACG/B,M,83ECzCT,IAKM0C,EAAoBC,IAAMC,YAAW,WAStCC,GAAQ,IAPTC,EAAc,EAAdA,eACAC,EAAe,EAAfA,gBACAC,EAAoB,EAApBA,qBACAC,EAAc,EAAdA,eACAC,EAAmB,EAAnBA,oBACAC,EAAgB,EAAhBA,iBACAC,EAAiB,EAAjBA,kBAEKC,EAAqB,EAAhBC,cAAgB,GAApB,GAE0H,IAAxDC,mBAASP,EAAqB7B,KAAI,SAACqC,GAAK,OAAKA,EAAQ,MAAG,GAA3HC,EAA6B,KAAEC,EAAgC,KACc,IAAZH,mBAAS,IAAG,GAA7EI,EAA4B,KAAEC,EAA+B,KACJ,IAAZL,mBAAS,IAAG,GAAzDM,EAAkB,KAAEC,EAAqB,KACA,IAAdP,oBAAS,GAAK,GAAzCQ,EAAS,KAAEC,EAAY,KACoC,IAAdT,mBAAS,MAAK,GAA3DU,EAAkB,KAAEC,EAAqB,KACoB,IAAdX,mBAAS,MAAK,GAA7DY,EAAmB,KAAEC,EAAsB,KACuB,IAAXb,mBAAS,GAAE,GAAlEc,EAAuB,KAAEC,EAA0B,KACA,IAAZf,mBAAS,IAAG,GAAnDgB,EAAe,KAAEC,EAAkB,KACoC,IAAZjB,mBAAS,IAAG,GAAvEkB,EAAyB,KAAEC,EAA4B,KAExDC,EAAWC,cAEXC,EAAWC,cAEXC,EAAuC,WAC3C,IAAMC,EAAsB,GAE5B,IAAK,IAAMC,KAAcpB,EACnBA,EAAmBoB,IACrBD,EAAoB5E,KAAK8E,SAASD,IAGtC,OAAOD,GAGTG,qBAAU,WACR,GAAIlC,EAAgB,CAGlB,IAFA,IAAMmC,EAAYnC,EAAeoC,eAC3BC,EAAgB,GACbpG,EAAI,EAAGA,GAAKkG,EAAWlG,IAC9BoG,EAAcpG,IAAK,EAErB4E,EAAsBwB,MAEvB,CAACrC,IAEJkC,qBAAU,WACRvB,EAAgCmB,OAC/B,CAAClB,IAEJsB,qBAAU,WACR,SAASI,EAAiBC,GAYxB,OAVIA,EAAa5F,OA5DmB,GA6D9B+E,IAAaE,EACC,IAAH,OAAOW,EAAaC,MAAM,EA5DhB,GA4D4C,cAAMD,EAAaC,MAAMD,EAAa5F,QAAO,KAEhG,IAAH,OAAO4F,EAAaC,MAAM,EA/DvB,IA+D4C,cAAMD,EAAaC,MAAMD,EAAa5F,OA/DlF,IA+D6G,KAG/G,IAAH,OAAO4F,EAAY,KAMpC,GAAIvC,EAAgB,CAClB,IAAMmC,EAAYnC,EAAeoC,eACjCf,EAA2Bc,GAC3BlB,EAAsBqB,EAAiBtC,EAAeyC,gBACtDtB,EAAuBmB,EAAiBpC,EAAiBuC,mBAE1D,CAACzC,IAEJ,IA+BM0C,EAAoC,SAACX,GACrCA,EAAoBpF,OAAS,GAC/B8E,EAA6B,IAE/B,IAAMkB,EAAmBZ,EAAoBa,QAAO,SAAC1E,EAAK8D,GAAU,cAAW9D,GAAG,QAAG8D,GAAa,MAAS,IAC3GnB,EAAsB,EAAD,GAAM8B,KA8BvBE,EAAiB,WACrB1C,KAGI2C,GAA0B5C,EAAiBkC,eAEjD,OACE,yBAAKtD,UAAU,8BAA8BiE,YAAa,SAACC,GAAC,OAAKA,EAAEC,mBAAmBrD,IAAKA,GACzF,kBAACsD,EAAA,EAAY,CACXC,MAAO/C,EAAE,mCACTgD,uBAAwB,4BACxBC,aAAcR,EACdS,cAAY,EACZC,aAAcV,EACdW,sBAAuB,2BACvBC,YAAa3D,GAEb,yBAAKhB,UAAU,cACb,yBAAKA,UAAU,gCACb,yBAAKA,UAAU,yBAAyBsB,EAAE,qDAAqD,KAC/F,yBAAKtB,UAAU,6CACb,kBAAC4E,EAAA,EAAe,CACd3B,oBAAqBvB,EACrB2B,UAAWW,GACXa,4BAnDmB,SAACC,GAC5BA,EAAYjH,OAAS,IACvB4E,EAAmB,IACnBd,EAAiCmD,KAiDvBC,cAAepD,EACfqD,QA9CgB,SAAC9B,GACzBA,GACFT,EAAmB,GAAD,OAAInB,EAAE,2BAA0B,YAAI0C,MA6C5CxB,gBAAiBA,KAGrB,yBAAKxC,UAAU,sBAAqB,0BAAMA,UAAU,yBAAyBoC,IAC7E,yBAAKpC,UAAU,yBAAyBsB,EAAE,0DAA0D,KACpG,kBAACsD,EAAA,EAAe,CACd3B,oBAAqBrB,EACrByB,UAAWf,EACXuC,4BAjD2B,SAACC,GAClCA,EAAYjH,OAAS,GACvBgE,EAAgCiD,GAChClB,EAAkCkB,IAElC9B,KA6CQ+B,cAAenB,EACfoB,QA3C4B,SAAC9B,GACnCA,GACFP,EAA6B,GAAD,OAAIrB,EAAE,2BAA0B,YAAIgB,KA0CxDE,gBAAiBE,IAEnB,yBAAK1C,UAAU,sBAAqB,0BAAMA,UAAU,yBAAyBkC,KAE/E,yBAAKlC,UAAWR,IAAW,uBAAwB,CAAEwC,eACnD,kBAACiD,EAAA,EAAkB,CACjBzH,SAAU0D,EACVgE,oBArGgB,SAAChC,QACYiC,IAAnCrD,EAAmBoB,GACrBpB,EAAmBoB,IAAc,EAEjCpB,EAAmBoB,IAAepB,EAAmBoB,GAEvDnB,EAAsB,EAAD,GAAMD,KAgGjBA,mBAAoBA,EACpBsD,oBAAqBnD,MAI3B,yBAAKjC,UAAU,6BACf,yBAAKA,UAAWR,IAAW,SAAU,CAAE6F,gBAAiBrD,KACtD,4BAAQhC,UAAWR,IAAW,sBAAuB,CAAE8F,SAAUtD,IAAc5B,QAjHzD,WAC5B2B,EAAsB,KAgH+FuD,SAAUtD,GACtHV,EAAE,uBAEL,kBAACiE,EAAA,EAAM,CACLvF,UAAU,wBACVI,QAAS,kBA3HXoF,EAA6BxC,IACnC7B,EAAoBD,EAAgBQ,EAA+B8D,QACnEzE,IAHmB,IACbyE,GA4HEC,MAAOnE,EAAE,kBACTgE,SA3GsB,WAC9B,GAAI5D,EAA8B7D,OAAS,GAAK2E,GAAmBE,EACjE,OAAO,EAET,IAAK,IAAMgD,KAAa5D,EACtB,GAAIA,EAAmB4D,GACrB,OAAO,EAGX,OAAO,EAkGWC,WAQtBhF,EAAkBiF,YAAc,oBAEjBjF,Q,4OC9Nf,IAAMkF,EAA6BjF,IAAMC,YAAW,SAACiF,EAAOhF,GAC1D,IAAMM,EAAmB2E,IAAKC,cAE9B,OACE,kBAAC,EAAiB,KACZF,EAAK,CACT1E,iBAAkBA,EAClBD,oBAAqB8E,IACrBnF,IAAKA,QAKX+E,EAA2BD,YAAc,6BAE1BC,ICnBAA,EDmBAA,E,60BEpBf,8lGAAA1I,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAiBA,IAAM+I,EAAkB,IAAIC,OAAO,iFAAkF,KAC/G/I,EAAU,CAAEgJ,WAAW,GAoMdC,EAlMc,SAAH,GAMpB,IALJC,EAAU,EAAVA,WACAC,EAAe,EAAfA,gBACAC,EAAM,EAANA,OACAC,EAA4B,EAA5BA,6BACAC,EAAW,EAAXA,YAEOpF,EAAqB,EAAhBC,cAAgB,GAApB,GACgC,IAAZC,mBAAS,IAAG,GAAjCmF,EAAM,KAAEC,EAAS,KAC4B,IAAdpF,mBAAS,MAAK,GAA7CqF,EAAW,KAAEC,EAAc,KACyB,IAAftF,oBAAS,GAAM,GAApD6D,EAAc,KAAE0B,EAAiB,KAC4B,IAAdvF,mBAAS,MAAK,GAA7DwF,EAAmB,KAAEC,EAAsB,KAI5CC,EAAWC,cACXC,EAAiBC,aAAY,SAACC,GAAK,aAAqC,QAArC,EAAKC,IAAUC,gBAAgBF,UAAM,aAAhC,EAAkCF,kBAEhFhE,qBAAU,WACJoD,GAAUQ,IAAwBN,GACpCO,EAAuBP,KAExB,CAACF,EAAQQ,EAAqBN,IAEjC,IAAM3F,EAAiB,WACrB+F,EAAe,MACfC,GAAkB,GAClB,IAAM3I,EAAKqJ,cAAcC,cAAc,IAAD,OAdpB,+BAedtJ,IACFA,EAAGuJ,MAAQ,MAEbrB,IACAW,EAAuB,MACvBL,EAAU,KAGNvF,EAAoB,WAAH,OAASuG,YAA2B7G,EAAgBmG,IAErE3H,EAAaC,IAAW,CAC5BqI,OAAO,EACPxB,sBAAsB,EACtByB,KAAMtB,EACNuB,QAASvB,EACT,aAAcY,IAGVY,EAAYrB,EAAOK,GACnBiB,EAAe,6BAAG,oFACE,GAAxBlB,GAAkB,IAEdiB,GAAqC,8BAAxBhB,EAAmD,oBAC9DgB,EAAUE,SAAU,CAAF,+BACHF,EAAUE,WAAU,OAArC1K,EAAW,EAAH,KACRsJ,EAAetJ,GAAU,kCAElBwK,EAAW,CAAF,iCACDjC,IAAKoC,eAAeH,EAAW5K,GAAQ,QAAxDI,EAAW,EAAH,KACRsJ,EAAetJ,GAAU,4CAE5B,kBAZoB,mCAgBf4K,EAAoB,6BAAG,WAAOC,GAAI,4EAGlCA,aAAgBC,cAAkBC,SAASC,KAAKC,UAAQ,gBAC1DjL,EAAW6K,EAAK,sCAECtC,IAAKoC,eAAeE,EAAMjL,GAAQ,OAAnDI,EAAW,EAAH,YAEVsJ,EAAetJ,GACfuJ,GAAkB,GAAM,2CACzB,gBAVyB,sCAYtB2B,OAAoCvD,IAAd6C,EAEE,wBAAxBhB,GAAkDd,EAAgByC,KAAKX,KACzEU,GAAsB,GAGxB,IAkBQE,EAlBFC,EAAgB,WACpB/B,EAAe,MACfC,GAAkB,IAmGpB,OAAOP,EACL,yBACExG,UAAWT,EACXuJ,eAAa,uBACb7E,YAAaoB,EAAiBhE,EAAoBN,EAClD5B,GAAG,wBAEFkG,EArGD,kBAAC,EAAiB,CAChBtE,eAAgBA,EAChBC,gBAAiB6H,EACjB5H,qBAAsBwF,EACtBvF,eAAgB2F,EAChBxF,kBAAmBA,KAMjBuH,EAAqBrC,GAAmBA,EAAgB1I,OAAS,EAGrE,yBAAKmC,UAAU,iBAAiBiE,YAAa,SAACC,GAAC,OAAKA,EAAEC,oBACpD,kBAACC,EAAA,EAAY,CACXoC,OAAQA,EACRnC,MAAO/C,EAAE,mCACTgD,uBAAwB,4BACxBC,aAAcxD,EACdyD,cAAY,EACZC,aAAc1D,GAEd,yBAAKf,UAAU,oBACf,kBAAC+I,EAAA,EAAI,CAAC/I,UAAU,wBAAwBb,GAAG,wBACzC,yBAAKa,UAAU,yBACb,yBAAKA,UAAU,YACZ4I,GACC,oCACE,kBAAC,IAAG,CAACI,YAAY,6BACf,4BAAQhJ,UAAU,sBACfsB,EAAE,2CAGP,yBAAKtB,UAAU,yBAGnB,kBAAC,IAAG,CAACgJ,YAAY,uBACf,4BAAQhJ,UAAU,sBACfsB,EAAE,cAGP,yBAAKtB,UAAU,wBACf,kBAAC,IAAG,CAACgJ,YAAY,yBACf,4BAAQhJ,UAAU,sBACfsB,EAAE,6CAKX,kBAAC,IAAQ,CAAC0H,YAAY,uBACpB,yBAAKhJ,UAAU,cACb,kBAAC,EAAa,CACZf,aAAc,SAACgK,GACbrC,EAAU,KAAGI,EAAsBiC,KAErC/J,KAAMqH,EACNvH,aAAcgJ,MAIpB,kBAAC,IAAQ,CAACgB,YAAY,iBACpB,yBAAKhJ,UAAU,cACb,kBAACkJ,EAAA,EAAc,CACbjK,aAAc,SAACY,GACb+G,EAAU,KAAGI,EAAsBnH,KAErCb,aAAc2H,EAA4B,wBAIhD,kBAAC,IAAQ,CAACqC,YAAY,mBACpB,yBAAKhJ,UAAU,qBACb,kBAACmJ,EAAA,EAAe,CACdC,YArJI,6BAsJJC,gBAAiB,SAAChB,GAAI,OAAKD,EAAqBC,SAKxD,yBAAKrI,UAAU,6BACf,yBAAKA,UAAU,UACb,kBAACuF,EAAA,EAAM,CACLvF,UAAWR,IAAW,YAAa,CAAE8J,OAAQZ,IAC7CtI,QAAS,kBAAOsI,EAAsB,KAAOT,KAC7CxC,MAAOnE,EAAE,iBACTgE,SAAUoD,SAiBlB,M,snDClMSa,MAff,SAAuCzD,GACrC,IAAQ0D,EAAkC1D,EAAlC0D,qBAAyBC,EAAI,EAAK3D,EAAK,GAEzCQ,EAAaoD,uBAAY,WAC7BF,MACC,CAACA,IAEEG,EAAW,OACZF,GAAI,IACPnD,eAGF,OAAO,kBAAC,EAAyBqD,I,gxECsBpBC,OA9Bf,SAAmC9D,GACjC,IAAMoB,EAAWC,cAKXZ,EAAkBc,aAAY,SAACC,GAAK,OAAKC,IAAUsC,2BAA2BvC,MAE7Ed,EAEL,GAFea,aAAY,SAACC,GAAK,MAAK,CACtCC,IAAUuC,cAAcxC,EAAOyC,IAAaC,4BAC5C,GAFW,GAIPvD,EAA+BY,aAAY,SAACC,GAAK,OAAKC,IAAU0C,gCAAgC3C,MAGhGZ,EAAcW,aAAY,SAACC,GAAK,OAAKC,IAAU2C,eAAe5C,EAAOyC,IAAaC,2BAGlFL,EAAW,SACZ7D,GAAK,IACR0D,qBAlB2B,WAC3BtC,EAASiD,IAAQC,aAAaL,IAAaC,0BAkB3CzD,kBACAC,SACAC,+BACAC,gBAGF,OAAO,kBAAC,EAAkCiD,IChC7BC", "file": "chunks/chunk.53.js", "sourcesContent": ["var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./FileListPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.FileListPanel{box-sizing:border-box;display:flex;flex-direction:column;align-items:center;width:100%;height:100%;overflow:auto;padding-top:20px;padding-bottom:20px;background-color:var(--document-background-color);border-radius:4px}.FileListPanel ul{display:flex;flex-wrap:wrap;justify-content:center;align-content:flex-start;list-style:none;margin:0;padding:16px;height:30em;width:100%}.FileListPanel ul li.selected{border:1px solid #48a4e0}.FileListPanel ul li{background:var(--gray-1);border:1px solid var(--blue-1);box-sizing:border-box;border-radius:6px;width:100%;height:60px;margin:4px 0;padding:8px 10px;display:flex;flex-direction:row;cursor:pointer}.FileListPanel ul li:hover{border:1px solid #48a4e0}.FileListPanel ul li .li-div{background:#fff;width:42px;height:100%;float:left;border-radius:4px;position:relative}.FileListPanel ul li .li-div-img{position:absolute;top:50%;left:50%;transform:translateY(-50%) translateX(-50%);width:28px}.FileListPanel ul li .li-div-img.with-border{height:34px;border:1px solid var(--blue-1)}.FileListPanel ul li .li-div-txt{margin-left:10px;line-height:40px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "var api = require(\"!../../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../../node_modules/css-loader/index.js!../../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../../node_modules/sass-loader/dist/cjs.js!./FileSelectedPanel.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.fileSelectedPanel.container{width:791px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel.container{width:494px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel.container .page-number-input{width:92px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel.container{width:494px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel.container .page-number-input{width:92px}}.fileSelectedPanel .modal-container .wrapper .header-container{padding-bottom:16px!important}.fileSelectedPanel .header .left-header{display:flex;align-items:center;grid-gap:4px;gap:4px}.fileSelectedPanel .header .Button{justify-content:center}.fileSelectedPanel .header .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.fileSelectedPanel .page-number-error{margin-top:8px;font-size:13px;color:var(--color-message-error)}.fileSelectedPanel .page-replacement-text{padding:0 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .page-replacement-text{padding:0;display:flex;justify-content:center;align-items:center}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .page-replacement-text{padding:0;display:flex;justify-content:center;align-items:center}}.fileSelectedPanel .modal-body{padding:16px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .thumb-card{width:132px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .thumb-card{width:132px}}.fileSelectedPanel .modal-body .modal-body-container{display:flex}.fileSelectedPanel .modal-body .modal-body-container.isLoading{justify-content:center;align-items:center}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container{padding:18px;height:calc(100vh - 296px)}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-card{flex:0 0 calc(50% - 10px);box-sizing:border-box}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-card-img{height:110px;width:83px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-body{align-items:center}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .thumb-image{width:100%;padding-right:0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .ui__choice__input{position:absolute;top:6px;right:6px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body .modal-body-container .ui__choice{margin:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container{padding:18px;height:calc(100vh - 296px)}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-card{flex:0 0 calc(50% - 10px);box-sizing:border-box}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-card-img{height:110px;width:83px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-body{align-items:center}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .thumb-image{width:100%;padding-right:0}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .ui__choice__input{position:absolute;top:6px;right:6px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body .modal-body-container .ui__choice{margin:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .modal-body{height:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .modal-body{height:100%}}.fileSelectedPanel .replace-page-input-container{height:60px;margin-bottom:8px;display:flex;align-items:baseline}.fileSelectedPanel .replace-page-input-container :first-child{padding-left:0}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container{justify-content:space-between}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container :first-child{padding-right:8px;padding-left:0}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container{justify-content:space-between}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container :first-child{padding-right:8px;padding-left:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container{display:grid;grid-template-columns:auto auto auto;grid-gap:16px;grid-row-gap:24px;row-gap:24px;margin-bottom:32px;height:88px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input-container .page-number-error{margin-top:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container{display:grid;grid-template-columns:auto auto auto;grid-gap:16px;grid-row-gap:24px;row-gap:24px;margin-bottom:32px;height:88px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input-container .page-number-error{margin-top:4px}}.fileSelectedPanel .replace-page-input{padding-left:8px;padding-right:8px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input{padding-right:0;padding-left:4px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input .page-replace-doc-name{word-break:break-all}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input{padding-right:0;padding-left:4px}.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input .page-replace-doc-name{word-break:break-all}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .replace-page-input{display:flex;justify-content:left;align-items:center;padding-left:0;padding-right:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .replace-page-input{display:flex;justify-content:left;align-items:center;padding-left:0;padding-right:0}}.fileSelectedPanel .page-replace-doc-name{font-weight:700}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .header-container{height:72px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .header-container{height:72px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .fileSelectedPanel .deselect-thumbnails{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .fileSelectedPanel .deselect-thumbnails{font-size:13px}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import FileListPanel from './FileListPanel';\n\nexport default FileListPanel;", "import React from 'react';\nimport classNames from 'classnames';\nimport './FileListPanel.scss';\n\nconst FileListPanel = ({ defaultValue, onFileSelect, list = [] }) => {\n  const { id } = defaultValue || {};\n\n  const onRowClick = (id) => {\n    onFileSelect(list.find((item) => item.id === id));\n  };\n\n  const onRowKeyDown = (event, id) => {\n    if (event.key === 'Enter') {\n      onFileSelect(list.find((item) => item.id === id));\n    }\n  };\n\n  const getFileThumbnail = (item) => {\n    if (item.hasOwnProperty('thumbnail')) {\n      let src = null;\n      const thumbnail = item.thumbnail;\n      if (thumbnail.url) {\n        src = thumbnail.url;\n      } else if (thumbnail.toDataURL) {\n        src = thumbnail.toDataURL();\n      }\n      // If thumbnail doesnt have a url or is canvas then we just show a blank square\n      const img = <img src={src} className=\"li-div-img\" />;\n      return (<div className=\"li-div\">{img}</div>);\n    }\n    return null;\n  };\n\n  const elements = list.map((item, i) => {\n    const isSelected = id === item.id;\n    const modalClass = classNames({\n      selected: isSelected\n    });\n\n    const thumbnail = getFileThumbnail(item);\n\n    return (\n      <li tabIndex=\"0\" key={i} onClick={() => onRowClick(item.id)} onKeyDown={(event) => onRowKeyDown(event, item.id)} className={modalClass}>\n        {thumbnail}\n        <div className=\"li-div-txt\" >{item.filename}</div>\n      </li>\n    );\n  });\n\n  return (\n    <div className=\"FileListPanel\">\n      <ul>\n        {elements}\n      </ul>\n    </div>\n  );\n};\n\nexport default FileListPanel;\n", "import React, { useEffect, useState } from 'react';\nimport classNames from 'classnames';\nimport Button from 'components/Button';\nimport { useTranslation } from 'react-i18next';\nimport PageNumberInput from '../PageNumberInput';\nimport '../PageReplacementModal.scss';\nimport './FileSelectedPanel.scss';\nimport PageThumbnailsGrid from 'src/components/PageThumbnailsGrid';\nimport { isMobileSize, isTabletSize } from 'helpers/getDeviceSize';\nimport ModalWrapper from 'components/ModalWrapper';\n\nconst MAX_NAME_LENGTH_BEFORE_TRUNCATION = 25;\nconst TRUNCATION_LENGTH = 10;\nconst TABLET_TRUNCATION_LENGTH = 4;\n\n// Need to forward the ref so the FocusTrap works correctly\nconst FileSelectedPanel = React.forwardRef((\n  {\n    closeThisModal,\n    clearLoadedFile,\n    pageIndicesToReplace,\n    sourceDocument,\n    replacePagesHandler,\n    documentInViewer,\n    closeModalWarning,\n  }, ref) => {\n  const [t] = useTranslation();\n\n  const [currentDocSelectedPageNumbers, setCurrentDocSelectedPageNumbers] = useState(pageIndicesToReplace.map((index) => index + 1));\n  const [sourceDocSelectedPageNumbers, setSourceDocSelectedPageNumbers] = useState([]);\n  const [selectedThumbnails, setSelectedThumbnails] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [sourceDocumentName, setSourceDocumentName] = useState(null);\n  const [currentDocumentName, setCurrentDocumentName] = useState(null);\n  const [sourceDocumentPageCount, setSourceDocumentPageCount] = useState(0);\n  const [pageNumberError, setPageNumberError] = useState('');\n  const [sourceDocPagesNumberError, setSourceDocPagesNumberError] = useState('');\n\n  const isTablet = isTabletSize();\n\n  const isMobile = isMobileSize();\n\n  const getPageNumbersFromSelectedThumbnails = () => {\n    const selectedPageNumbers = [];\n\n    for (const pageNumber in selectedThumbnails) {\n      if (selectedThumbnails[pageNumber]) {\n        selectedPageNumbers.push(parseInt(pageNumber));\n      }\n    }\n    return selectedPageNumbers;\n  };\n\n  useEffect(() => {\n    if (sourceDocument) {\n      const pageCount = sourceDocument.getPageCount();\n      const selectedPages = {};\n      for (let i = 1; i <= pageCount; i++) {\n        selectedPages[i] = true;\n      }\n      setSelectedThumbnails(selectedPages);\n    }\n  }, [sourceDocument]);\n\n  useEffect(() => {\n    setSourceDocSelectedPageNumbers(getPageNumbersFromSelectedThumbnails());\n  }, [selectedThumbnails]);\n\n  useEffect(() => {\n    function getTruncatedName(documentName) {\n      let truncatedName;\n      if (documentName.length > MAX_NAME_LENGTH_BEFORE_TRUNCATION) {\n        if (isTablet && !isMobile) {\n          truncatedName = `\"${documentName.slice(0, TABLET_TRUNCATION_LENGTH)}...${documentName.slice(documentName.length)}\"`;\n        } else {\n          truncatedName = `\"${documentName.slice(0, TRUNCATION_LENGTH)}...${documentName.slice(documentName.length - TRUNCATION_LENGTH)}\"`;\n        }\n      } else {\n        truncatedName = `\"${documentName}\"`;\n      }\n\n      return truncatedName;\n    }\n\n    if (sourceDocument) {\n      const pageCount = sourceDocument.getPageCount();\n      setSourceDocumentPageCount(pageCount);\n      setSourceDocumentName(getTruncatedName(sourceDocument.getFilename()));\n      setCurrentDocumentName(getTruncatedName(documentInViewer.getFilename()));\n    }\n  }, [sourceDocument]);\n\n  const replacePages = () => {\n    const pagesToReplaceIntoDocument = getPageNumbersFromSelectedThumbnails();\n    replacePagesHandler(sourceDocument, currentDocSelectedPageNumbers, pagesToReplaceIntoDocument);\n    closeThisModal();\n  };\n\n  const deselectAllThumbnails = () => {\n    setSelectedThumbnails({});\n  };\n\n  const onThumbnailSelected = (pageNumber) => {\n    if (selectedThumbnails[pageNumber] === undefined) {\n      selectedThumbnails[pageNumber] = true;\n    } else {\n      selectedThumbnails[pageNumber] = !selectedThumbnails[pageNumber];\n    }\n    setSelectedThumbnails({ ...selectedThumbnails });\n  };\n\n  const isReplaceButtonDisabled = () => {\n    if (currentDocSelectedPageNumbers.length < 1 || pageNumberError || sourceDocPagesNumberError) {\n      return true;\n    }\n    for (const pageIndex in selectedThumbnails) {\n      if (selectedThumbnails[pageIndex]) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  const onSourceDocumentNumberInputChange = (selectedPageNumbers) => {\n    if (selectedPageNumbers.length > 0) {\n      setSourceDocPagesNumberError('');\n    }\n    const selectedPagesMap = selectedPageNumbers.reduce((map, pageNumber) => ({ ...map, [pageNumber]: true }), {});\n    setSelectedThumbnails({ ...selectedPagesMap });\n  };\n\n  const handlePageNumbersChanged = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setPageNumberError('');\n      setCurrentDocSelectedPageNumbers(pageNumbers);\n    }\n  };\n\n  const handlePageNumberError = (pageNumber) => {\n    if (pageNumber) {\n      setPageNumberError(`${t('message.errorPageNumber')} ${loadedDocumentPageCount}`);\n    }\n  };\n\n  const handleSourcePageNumbersChanged = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setSourceDocSelectedPageNumbers(pageNumbers);\n      onSourceDocumentNumberInputChange(pageNumbers);\n    } else {\n      getPageNumbersFromSelectedThumbnails();\n    }\n  };\n  const handleSourceDocPagesNumberError = (pageNumber) => {\n    if (pageNumber) {\n      setSourceDocPagesNumberError(`${t('message.errorPageNumber')} ${sourceDocumentPageCount}`);\n    }\n  };\n\n  const onCloseHandler = () => {\n    closeModalWarning();\n  };\n\n  const loadedDocumentPageCount = documentInViewer.getPageCount();\n\n  return (\n    <div className=\"fileSelectedPanel container\" onMouseDown={(e) => e.stopPropagation()} ref={ref}>\n      <ModalWrapper\n        title={t('component.pageReplaceModalTitle')}\n        closeButtonDataElement={'pageReplacementModalClose'}\n        onCloseClick={onCloseHandler}\n        swipeToClose\n        closeHandler={onCloseHandler}\n        backButtonDataElement={'insertFromFileBackButton'}\n        onBackClick={clearLoadedFile}\n      >\n        <div className=\"modal-body\">\n          <div className=\"replace-page-input-container\">\n            <div className=\"page-replacement-text\">{t('option.pageReplacementModal.pageReplaceInputLabel')}:</div>\n            <div className=\"replace-page-input-current-doc-containers\">\n              <PageNumberInput\n                selectedPageNumbers={currentDocSelectedPageNumbers}\n                pageCount={loadedDocumentPageCount}\n                onSelectedPageNumbersChange={handlePageNumbersChanged}\n                onBlurHandler={setCurrentDocSelectedPageNumbers}\n                onError={handlePageNumberError}\n                pageNumberError={pageNumberError}\n              />\n            </div>\n            <div className=\"replace-page-input\"><span className=\"page-replace-doc-name\">{currentDocumentName}</span></div>\n            <div className=\"page-replacement-text\">{t('option.pageReplacementModal.pageReplaceInputFromSource')}:</div>\n            <PageNumberInput\n              selectedPageNumbers={sourceDocSelectedPageNumbers}\n              pageCount={sourceDocumentPageCount}\n              onSelectedPageNumbersChange={handleSourcePageNumbersChanged}\n              onBlurHandler={onSourceDocumentNumberInputChange}\n              onError={handleSourceDocPagesNumberError}\n              pageNumberError={sourceDocPagesNumberError}\n            />\n            <div className=\"replace-page-input\"><span className=\"page-replace-doc-name\">{sourceDocumentName}</span></div>\n          </div>\n          <div className={classNames('modal-body-container', { isLoading })}>\n            <PageThumbnailsGrid\n              document={sourceDocument}\n              onThumbnailSelected={onThumbnailSelected}\n              selectedThumbnails={selectedThumbnails}\n              onfileLoadedHandler={setIsLoading}\n            />\n          </div>\n        </div>\n        <div className=\"page-replacement-divider\" />\n        <div className={classNames('footer', { isFileSelected: !isLoading })}>\n          <button className={classNames('deselect-thumbnails', { disabled: isLoading })} onClick={deselectAllThumbnails} disabled={isLoading}>\n            {t('action.deselectAll')}\n          </button>\n          <Button\n            className=\"modal-btn replace-btn\"\n            onClick={() => replacePages()}\n            label={t('action.replace')}\n            disabled={isReplaceButtonDisabled()}\n          />\n        </div>\n      </ModalWrapper>\n    </div >\n  );\n});\n\nFileSelectedPanel.displayName = 'FileSelectedPanel';\n\nexport default FileSelectedPanel;\n", "import React from 'react';\nimport FileSelectedPanel from './FileSelectedPanel';\nimport { replacePages } from '../../../helpers/pageManipulationFunctions';\nimport core from 'core';\n\n// Need to forward the ref to keep the focus trap working correctly\nconst FileSelectedPanelContainer = React.forwardRef((props, ref) => {\n  const documentInViewer = core.getDocument();\n\n  return (\n    <FileSelectedPanel\n      {...props}\n      documentInViewer={documentInViewer}\n      replacePagesHandler={replacePages}\n      ref={ref}\n    />\n  );\n});\n\nFileSelectedPanelContainer.displayName = 'FileSelectedPanelContainer';\n\nexport default FileSelectedPanelContainer;\n", "import FileSelectedPanelContainer from './FileSelectedPanelContainer';\n\nexport default FileSelectedPanelContainer;", "import React, { useState, useEffect } from 'react';\nimport classNames from 'classnames';\nimport selectors from 'selectors';\nimport core from 'core';\nimport { useTranslation } from 'react-i18next';\nimport FileListPanel from './FileListPanel';\nimport FileInputPanel from './FileInputPanel';\nimport FilePickerPanel from './FilePickerPanel';\nimport { Tabs, Tab, TabPanel } from 'components/Tabs';\nimport Button from 'components/Button';\nimport FileSelectedPanel from './FileSelectedPanel';\nimport { exitPageReplacementWarning } from 'helpers/pageManipulationFunctions';\nimport { useDispatch, useSelector } from 'react-redux';\nimport ModalWrapper from '../ModalWrapper';\nimport getRootNode, { getInstanceNode } from 'helpers/getRootNode';\n\nimport './PageReplacementModal.scss';\n\nconst isValidUrlRegex = new RegExp(/^(?:http(s)?:\\/\\/)?[\\w.-]+(?:\\.[\\w\\.-]+)+[\\w\\-\\._~:/?#[\\]@!\\$&'\\(\\)\\*\\+,;=.]+$/, 'm');\nconst options = { loadAsPDF: true };\n\nconst PageReplacementModal = ({\n  closeModal,\n  selectableFiles,\n  isOpen,\n  selectedThumbnailPageIndexes,\n  selectedTab,\n}) => {\n  const [t] = useTranslation();\n  const [source, setSource] = useState({});\n  const [selectedDoc, setSelectedDoc] = useState(null);\n  const [isFileSelected, setIsFileSelected] = useState(false);\n  const [selectedTabInternal, setSelectedTabInternal] = useState(null);\n\n  const fileInputId = 'pageReplacementFileInputId';\n\n  const dispatch = useDispatch();\n  const customizableUI = useSelector((state) => selectors.getFeatureFlags(state)?.customizableUI);\n\n  useEffect(() => {\n    if (isOpen && selectedTabInternal !== selectedTab) {\n      setSelectedTabInternal(selectedTab);\n    }\n  }, [isOpen, selectedTabInternal, selectedTab]);\n\n  const closeThisModal = () => {\n    setSelectedDoc(null);\n    setIsFileSelected(false);\n    const el = getRootNode().querySelector(`#${fileInputId}`);\n    if (el) {\n      el.value = null;\n    }\n    closeModal();\n    setSelectedTabInternal(null);\n    setSource({});\n  };\n\n  const closeModalWarning = () => exitPageReplacementWarning(closeThisModal, dispatch);\n\n  const modalClass = classNames({\n    Modal: true,\n    PageReplacementModal: true,\n    open: isOpen,\n    closed: !isOpen,\n    'modular-ui': customizableUI,\n  });\n\n  const srcString = source[selectedTabInternal];\n  const handleSelection = async () => {\n    setIsFileSelected(true);\n    let document;\n    if (srcString && selectedTabInternal === 'customFileListPanelButton') {\n      if (srcString.onSelect) {\n        document = await srcString.onSelect();\n        setSelectedDoc(document);\n      }\n    } else if (srcString) {\n      document = await core.createDocument(srcString, options);\n      setSelectedDoc(document);\n    }\n  };\n\n  // File picker can merge docs, in which case the callback gets\n  // executed with a Document not a file\n  const fileProcessedHandler = async (file) => {\n    let document;\n    // eslint-disable-next-line no-undef\n    if (file instanceof getInstanceNode().instance.Core.Document) {\n      document = file;\n    } else {\n      document = await core.createDocument(file, options);\n    }\n    setSelectedDoc(document);\n    setIsFileSelected(true);\n  };\n\n  let isSelectBtnDisabled = srcString === undefined;\n\n  if (selectedTabInternal === 'urlInputPanelButton' && !isValidUrlRegex.test(srcString)) {\n    isSelectBtnDisabled = true;\n  }\n\n  const clearDocument = () => {\n    setSelectedDoc(null);\n    setIsFileSelected(false);\n  };\n\n  const renderFileSelectedPanel = () => {\n    return (\n      <FileSelectedPanel\n        closeThisModal={closeThisModal}\n        clearLoadedFile={clearDocument}\n        pageIndicesToReplace={selectedThumbnailPageIndexes}\n        sourceDocument={selectedDoc}\n        closeModalWarning={closeModalWarning}\n      />\n    );\n  };\n\n  const renderSelectionTabs = () => {\n    const isFilePanelEnabled = selectableFiles && selectableFiles.length > 0;\n\n    return (\n      <div className=\"container tabs\" onMouseDown={(e) => e.stopPropagation()}>\n        <ModalWrapper\n          isOpen={isOpen}\n          title={t('component.pageReplaceModalTitle')}\n          closeButtonDataElement={'pageReplacementModalClose'}\n          onCloseClick={closeThisModal}\n          swipeToClose\n          closeHandler={closeThisModal}\n        >\n          <div className=\"swipe-indicator\" />\n          <Tabs className=\"page-replacement-tabs\" id=\"pageReplacementModal\">\n            <div className=\"tabs-header-container\">\n              <div className=\"tab-list\">\n                {isFilePanelEnabled &&\n                  <>\n                    <Tab dataElement=\"customFileListPanelButton\">\n                      <button className=\"tab-options-button\">\n                        {t('option.pageReplacementModal.yourFiles')}\n                      </button>\n                    </Tab>\n                    <div className=\"tab-options-divider\" />\n                  </>\n                }\n                <Tab dataElement=\"urlInputPanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('link.url')}\n                  </button>\n                </Tab>\n                <div className=\"tab-options-divider\" />\n                <Tab dataElement=\"filePickerPanelButton\">\n                  <button className=\"tab-options-button\">\n                    {t('option.pageReplacementModal.localFile')}\n                  </button>\n                </Tab>\n              </div>\n            </div>\n            <TabPanel dataElement=\"customFileListPanel\">\n              <div className=\"panel-body\">\n                <FileListPanel\n                  onFileSelect={(selection) => {\n                    setSource({ [selectedTabInternal]: selection });\n                  }}\n                  list={selectableFiles}\n                  defaultValue={srcString}\n                />\n              </div>\n            </TabPanel>\n            <TabPanel dataElement=\"urlInputPanel\">\n              <div className=\"panel-body\">\n                <FileInputPanel\n                  onFileSelect={(url) => {\n                    setSource({ [selectedTabInternal]: url });\n                  }}\n                  defaultValue={source['urlInputPanelButton']}\n                />\n              </div>\n            </TabPanel>\n            <TabPanel dataElement=\"filePickerPanel\">\n              <div className=\"panel-body upload\">\n                <FilePickerPanel\n                  fileInputId={fileInputId}\n                  onFileProcessed={(file) => fileProcessedHandler(file)}\n                />\n              </div>\n            </TabPanel>\n          </Tabs>\n          <div className=\"page-replacement-divider\" />\n          <div className=\"footer\">\n            <Button\n              className={classNames('modal-btn', { noFile: isSelectBtnDisabled })}\n              onClick={() => (isSelectBtnDisabled ? null : handleSelection())}\n              label={t('action.select')}\n              disabled={isSelectBtnDisabled}\n            />\n          </div>\n        </ModalWrapper>\n      </div>\n    );\n  };\n\n  return isOpen ? (\n    <div\n      className={modalClass}\n      data-element=\"pageReplacementModal\"\n      onMouseDown={isFileSelected ? closeModalWarning : closeThisModal}\n      id=\"pageReplacementModal\"\n    >\n      {isFileSelected ? renderFileSelectedPanel() : renderSelectionTabs()}\n    </div>\n  ) : null;\n};\n\nexport default PageReplacementModal;\n", "import React, { useCallback } from 'react';\nimport PageReplacementModal from './PageReplacementModal';\n\nfunction PageReplacementModalContainer(props) {\n  const { closePageReplacement, ...rest } = props;\n\n  const closeModal = useCallback(() => {\n    closePageReplacement();\n  }, [closePageReplacement]);\n\n  const newProps = {\n    ...rest,\n    closeModal,\n  };\n\n  return <PageReplacementModal {...newProps} />;\n}\n\nexport default PageReplacementModalContainer;", "import React from 'react';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport { useSelector, useDispatch } from 'react-redux';\nimport PageReplacementModalContainer from './PageReplacementModalContainer';\nimport DataElements from 'constants/dataElement';\n\nfunction PageReplacementModalRedux(props) {\n  const dispatch = useDispatch();\n  const closePageReplacement = () => {\n    dispatch(actions.closeElement(DataElements.PAGE_REPLACEMENT_MODAL));\n  };\n\n  const selectableFiles = useSelector((state) => selectors.getPageReplacementFileList(state));\n\n  const [isOpen] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.PAGE_REPLACEMENT_MODAL),\n  ]);\n\n  const selectedThumbnailPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state)\n  );\n\n  const selectedTab = useSelector((state) => selectors.getSelectedTab(state, DataElements.PAGE_REPLACEMENT_MODAL)\n  );\n\n  const newProps = {\n    ...props,\n    closePageReplacement,\n    selectableFiles,\n    isOpen,\n    selectedThumbnailPageIndexes,\n    selectedTab,\n  };\n\n  return <PageReplacementModalContainer {...newProps} />;\n}\n\nexport default PageReplacementModalRedux;", "import PageReplacementModalRedux from './PageReplacementModalRedux';\n\nexport default PageReplacementModalRedux;"], "sourceRoot": ""}