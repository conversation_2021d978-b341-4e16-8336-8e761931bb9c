{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/es.js"], "names": ["module", "exports", "e", "s", "default", "o", "d", "name", "monthsShort", "split", "weekdays", "weekdaysShort", "weekdaysMin", "months", "weekStart", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "ordinal", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,YAAY,kDAAkDC,MAAM,KAAKC,SAAS,uDAAuDD,MAAM,KAAKE,cAAc,qCAAqCF,MAAM,KAAKG,YAAY,uBAAuBH,MAAM,KAAKI,OAAO,2FAA2FJ,MAAM,KAAKK,UAAU,EAAEC,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,6BAA6BC,KAAK,oCAAoCC,aAAa,CAACC,OAAO,QAAQC,KAAK,UAAUrB,EAAE,gBAAgBsB,EAAE,YAAYC,GAAG,aAAaC,EAAE,WAAWC,GAAG,WAAWtB,EAAE,SAASuB,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,WAAWC,QAAQ,SAAShC,GAAG,OAAOA,EAAE,MAAM,OAAOC,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAApjCD,CAAE,EAAQ", "file": "chunks/chunk.148.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_es=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(e),d={name:\"es\",monthsShort:\"ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic\".split(\"_\"),weekdays:\"domingo_lunes_martes_miércoles_jueves_viernes_sábado\".split(\"_\"),weekdaysShort:\"dom._lun._mar._mié._jue._vie._sáb.\".split(\"_\"),weekdaysMin:\"do_lu_ma_mi_ju_vi_sá\".split(\"_\"),months:\"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre\".split(\"_\"),weekStart:1,formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY H:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY H:mm\"},relativeTime:{future:\"en %s\",past:\"hace %s\",s:\"unos segundos\",m:\"un minuto\",mm:\"%d minutos\",h:\"una hora\",hh:\"%d horas\",d:\"un día\",dd:\"%d días\",M:\"un mes\",MM:\"%d meses\",y:\"un año\",yy:\"%d años\"},ordinal:function(e){return e+\"º\"}};return s.default.locale(d,null,!0),d}));"], "sourceRoot": ""}