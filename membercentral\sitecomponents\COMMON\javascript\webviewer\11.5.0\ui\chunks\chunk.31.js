(window.webpackJsonp = window.webpackJsonp || []).push([
	[31],
	{
		1562: function (e, t, n) {
			"use strict";
			var o = n(0),
				r = n.n(o),
				a =
					(n(1579),
					function (e) {
						var t = e.height,
							n = void 0 === t ? "50px" : t,
							o = e.width,
							a = { height: n, width: void 0 === o ? "54px" : o };
						return r.a.createElement("div", { className: "spinner", style: a });
					});
			t.a = a;
		},
		1568: function (e, t, n) {
			"use strict";
			n(18),
				n(109),
				n(19),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20);
			var o = n(0),
				r = n.n(o),
				a = n(3),
				i = n.n(a),
				c = n(347),
				l = n(17),
				s = n.n(l);
			n(1575);
			function d(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return u(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return u(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function u(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			function p(e) {
				var t = e.children.replace(/\n$/, ""),
					n = e.panelWidth,
					a = e.linesToBreak,
					i = e.renderRichText,
					l = e.richTextStyle,
					u = e.resize,
					p = e.style,
					m = e.comment,
					f = void 0 !== m && m,
					h = e.beforeContent,
					b = void 0 === h ? function () {} : h,
					y = d(Object(o.useState)(!1), 2),
					g = y[0],
					v = y[1],
					x = d(Object(o.useState)(null), 2),
					w = x[0],
					O = x[1],
					S = d(Object(o.useState)(null), 2),
					A = S[0],
					j = S[1],
					E = d(Object(o.useState)(!1), 2),
					R = E[0],
					k = E[1],
					P = r.a.useRef(null),
					C = Object(c.a)().t,
					T = g ? t : t.substring(0, A * a),
					I = C(g ? "action.showLess" : "action.showMore"),
					N = s()("note-text-preview", { "preview-comment": f });
				return (
					Object(o.useEffect)(
						function () {
							var e = P.current.clientWidth;
							O(e);
						},
						[n],
					),
					Object(o.useLayoutEffect)(
						function () {
							var e = (function (e) {
									var t = document.createElement("canvas").getContext("2d");
									return (t.font = "13px sans-serif"), t.measureText(e).width;
								})(t),
								n = e / t.length,
								o = Math.floor(w / n);
							j(o), k(e / w > a);
						},
						[t, w],
					),
					r.a.createElement(
						"div",
						{ className: N, ref: P, style: p, "aria-live": "polite" },
						b(),
						i && l ? i(T, l, 0) : T,
						" ",
						R &&
							r.a.createElement(
								"button",
								{
									className: "note-text-preview-prompt",
									onClick: function (e) {
										e.stopPropagation(), v(!g), u && u();
									},
								},
								I,
							),
					)
				);
			}
			(p.propTypes = {
				panelWidth: i.a.number,
				linesToBreak: i.a.number,
				renderRichText: i.a.func,
				richTextStyle: i.a.any,
				resize: i.a.func,
				style: i.a.any,
				comment: i.a.bool,
				beforeContent: i.a.func,
			}),
				(t.a = p);
		},
		1575: function (e, t, n) {
			var o = n(30),
				r = n(1576);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1576: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".note-text-preview{font-size:13px;color:var(--gray-7);padding-right:var(--note-content-right-padding-width);-webkit-user-select:text!important;-moz-user-select:text!important;user-select:text!important;cursor:text;height:-moz-fit-content;height:fit-content;width:calc(100% - var(--note-content-right-padding-width));overflow:hidden}.note-text-preview>*{pointer-events:all}.preview-comment{color:var(--text-color)}.note-text-preview-prompt{cursor:pointer;color:var(--primary-button);text-decoration:underline;position:relative;pointer-events:auto;background:transparent;border:none;padding:0}.note-text-preview-prompt:hover{color:var(--primary-button-hover)}.trackedChangePopup .note-text-preview{max-height:400px;overflow-y:auto;width:calc(100% + var(--note-content-right-padding-width))}",
				"",
			]);
		},
		1579: function (e, t, n) {
			var o = n(30),
				r = n(1580);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1580: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.spinner{border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1586: function (e, t, n) {
			"use strict";
			var o = n(0),
				r = n.n(o),
				a = n(1533),
				i = n(1517),
				c = n(21),
				l = function (e) {
					var t = e.children,
						n = Object(o.useMemo)(function () {
							var e = Object(c.a)();
							return Object(a.a)({
								key: "wv-react-select-emotion",
								container: e,
							});
						}, []);
					return window.isApryseWebViewerWebComponent
						? r.a.createElement(i.a, { value: n }, t)
						: t;
				};
			t.a = l;
		},
		1594: function (e, t, n) {
			"use strict";
			n(8), n(54);
			var o = n(1),
				r = n(33),
				a = n(2),
				i = n(67),
				c = n(178);
			function l() {}
			t.a = function (e) {
				var t =
						arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : l,
					n =
						arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;
				return function (r) {
					return o.a.isWebViewerServerDocument() ? s(e, r, n) : d(e, t, r, n);
				};
			};
			var s = function (e, t, n) {
					return o.a.applyRedactions(e, n).then(function (e) {
						if (e && e.url)
							return Object(c.a)(t, {
								filename: "redacted.pdf",
								includeAnnotations: !0,
								externalURL: e.url,
							});
						console.warn("WebViewer Server did not return a valid result");
					});
				},
				d = function (e, t, n, c) {
					var l = {
						message: r.a.t("warning.redaction.applyMessage"),
						title: r.a.t("warning.redaction.applyTile"),
						confirmBtnText: r.a.t("action.apply"),
						onConfirm: function () {
							return (
								o.a
									.applyRedactions(e, c)
									.then(function () {
										t();
									})
									.catch(function (e) {
										return Object(i.b)(e);
									}),
								Promise.resolve()
							);
						},
					};
					return n(a.a.showWarningMessage(l));
				};
		},
		1753: function (e, t, n) {
			var o = n(30),
				r = n(1754);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1754: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.RedactionPanel{padding:16px 16px 0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container{outline:none}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel{width:100%;height:100%;min-width:100%;padding:8px 0 0}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container{display:flex;align-items:center;justify-content:flex-end;height:32px;width:100%;padding-right:12px}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{padding:0;border:none;background-color:transparent;z-index:3;cursor:pointer}:host(:not([data-tabbing=true])) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container,html:not([data-tabbing=true]) .App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container{outline:none}.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .close-container .close-icon-container .close-icon{width:24px;height:24px}}.RedactionPanel .marked-redaction-counter{flex:0 1 19px;margin-top:24px;margin-bottom:12px;font-size:16px}.RedactionPanel .marked-redaction-counter span{font-weight:400}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .marked-redaction-counter{margin:16px;font-size:16px}}.RedactionPanel .no-marked-redactions{display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.RedactionPanel .no-marked-redactions .msg{text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .msg{line-height:15px;width:146px;margin-bottom:32px}}.RedactionPanel .no-marked-redactions .empty-icon,.RedactionPanel .no-marked-redactions .empty-icon svg{width:65px;height:83px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon,.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .no-marked-redactions .empty-icon svg{width:60px;height:60px}}.RedactionPanel .no-marked-redactions .empty-icon *{fill:var(--gray-5);color:var(--gray-5)}.RedactionPanel .redaction-panel-controls{flex:0 0 57px;margin-left:-16px;padding-top:13px;border-top:1px solid var(--divider);display:flex;background-color:var(--component-background);width:inherit;justify-content:flex-end;padding-right:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{margin:0 0 16px;padding:16px}}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked{padding:0;border:none;background-color:transparent;background-color:var(--primary-button);color:var(--primary-button-text);border-radius:4px;height:32px;width:90px}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked:hover:not(.disabled){background-color:var(--primary-button-hover)}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled{opacity:.5}.RedactionPanel .redaction-panel-controls .Button.redact-all-marked.disabled span{color:var(--primary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .Button.redact-all-marked{font-size:13px}}.RedactionPanel .redaction-panel-controls .clear-all-marked{padding:0;color:var(--secondary-button-text);background-color:transparent;border:none;height:32px;width:70px;margin-right:8px;cursor:pointer}:host(:not([data-tabbing=true])) .RedactionPanel .redaction-panel-controls .clear-all-marked,html:not([data-tabbing=true]) .RedactionPanel .redaction-panel-controls .clear-all-marked{outline:none}.RedactionPanel .redaction-panel-controls .clear-all-marked:hover:not(.disabled){color:var(--secondary-button-hover)}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled{opacity:.5;cursor:not-allowed}.RedactionPanel .redaction-panel-controls .clear-all-marked.disabled span{color:var(--secondary-button-text)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls .clear-all-marked{font-size:13px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-panel-controls{left:0;margin-bottom:16px}}.RedactionPanel .redaction-group-container{flex:1 1 auto}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionPanel .redaction-group-container{margin-right:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionPanel .redaction-group-container{margin-right:4px}}.RedactionPanel button.focus-visible,.RedactionPanel button:focus-visible{outline:var(--focus-visible-outline)}.ModularPanel-container .RedactionPanel{height:100%;padding:unset}.ModularPanel-container .RedactionPanel .redaction-panel-controls{margin-right:-16px;padding-bottom:16px}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1755: function (e, t, n) {
			var o = n(30),
				r = n(1756);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1756: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".redaction-item{display:flex;align-items:center;padding:12px 16px;position:relative}.redaction-item:hover{background-color:var(--view-header-button-hover);cursor:pointer}.redaction-item.focus-visible,.redaction-item:focus-visible{outline:var(--focus-visible-outline)}.redaction-item.modular-ui:hover:not(:disabled):not(.disabled){background-color:transparent;box-shadow:inset 0 0 0 1px var(--hover-border)}.redaction-item .redaction-item-button{position:absolute;width:100%;height:100%;top:0;left:0}.redaction-item-selected{background-color:var(--view-header-button-active)!important}.redaction-item-selected.modular-ui{box-shadow:inset 0 0 0 1px var(--focus-border)}.redaction-item-info{flex:1;padding-left:18px;padding-right:20px}.redaction-item-preview{font-size:13px;color:var(--text-color)}.redaction-item-date-author{font-size:10px;color:var(--faded-text)}.redaction-item-label-text{font-size:10px;margin:2px 0}.redaction-item-delete.customUI:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}",
				"",
			]);
		},
		1757: function (e, t, n) {
			var o = n(30),
				r = n(1758);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1758: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-items{margin:8px 2px 1px;background-color:var(--component-background);box-shadow:0 0 3px 0 var(--box-shadow);border-radius:4px;padding:0}.redaction-items>:first-child{padding-top:16px;border-radius:4px 4px 0 0}.redaction-items>:last-child{padding-bottom:16px;border-radius:0 0 4px 4px}.redaction-items>:only-child{padding-top:16px;padding-bottom:16px;border-radius:4px}.redaction-page-group{padding-top:12px;padding-bottom:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-page-group{padding-top:8px;padding-right:4px;padding-left:16px}}.redaction-page-group h2{margin:0}.redaction-page-group h2 button{margin:0;font-size:13px;font-weight:400;color:var(--faded-text)}.redaction-page-group-header{display:flex;justify-content:space-between;align-items:baseline}.expand-arrow{height:16px;width:16px;display:flex;align-items:center;cursor:pointer}.expand-arrow .Icon{width:12px;height:12px}.expand-arrow.Button.custom-ui.icon-only:hover{box-shadow:inset 0 0 0 1px var(--hover-border)}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1759: function (e, t, n) {
			var o = n(30),
				r = n(1760);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1760: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".creatable-multi-select-label{display:inline-block;font-weight:700;margin-bottom:var(--padding-small)}",
				"",
			]);
		},
		1761: function (e, t, n) {
			var o = n(30),
				r = n(1762);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1762: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-multi-select{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-multi-select{font-size:13px}}.redaction-search-multi-select-search-icon-container{height:28px;align-self:flex-start;display:flex;align-items:center;margin:0 var(--padding-tiny)}.redaction-search-multi-select-search-icon-container .Icon{width:16px;height:16px}.custom-remove-button{display:flex;align-items:center}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1763: function (e, t, n) {
			var o = n(30),
				r = n(1764);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1764: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay{margin-left:16px;margin-right:16px}}.RedactionSearchOverlay input{width:100%;padding:6px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RedactionSearchOverlay .creatable-multi-select-label{font-size:13px}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1765: function (e, t, n) {
			var o = n(30),
				r = n(1766);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1766: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".redaction-search-result{display:flex;align-items:center;padding:12px;background-color:var(--component-background);border:1px solid transparent;border-radius:4px;box-shadow:0 0 3px var(--document-box-shadow);margin:8px 0;position:relative}.redaction-search-result .redaction-search-result-button{position:absolute;width:100%;height:100%;top:0;left:0;background:transparent;border:none;cursor:pointer}.redaction-search-result .Icon svg{transform:scale(1.2);padding-top:2px}.redaction-search-result .search-value{word-break:break-all;color:var(--secondary-button-text);font-weight:700}.redaction-search-result.active{background-color:transparent!important;border:1px solid var(--focus-border)}.redaction-search-result-info{font-size:13px;color:var(--text-color)}",
				"",
			]);
		},
		1767: function (e, t, n) {
			var o = n(30),
				r = n(1768);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1768: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".redaction-search-results-page-number{display:flex;align-items:start;padding:8px 12px 4px}.redaction-search-results-page-number .redaction-search-results-page-number-checkbox{position:absolute;left:24px}.redaction-search-results-page-number .collapsible-page-group-header button{font-size:13px;font-weight:400;color:var(--faded-text);margin:0 0 0 32px;width:calc(100% - 32px)}.redaction-search-results-page-number .redaction-search-results{padding:0;margin:0}",
				"",
			]);
		},
		1769: function (e, t, n) {
			var o = n(30),
				r = n(1770);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1770: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.redaction-search-counter-controls{display:flex;flex-direction:row;margin-top:36px;font-size:var(--font-size-default);padding:16px;border:1px solid var(--lighter-border);background-color:var(--gray-0);border-radius:4px 4px 0 0;max-height:50px;min-height:50px;grid-column-gap:var(--padding-medium);-moz-column-gap:var(--padding-medium);column-gap:var(--padding-medium)}.redaction-search-counter-controls .redaction-search-results-counter{flex:2 1 auto}.redaction-search-counter-controls .redaction-search-results-counter span{font-weight:400}.redaction-search-counter-controls .spinner{margin:auto;flex:3 1 "25px"}.redaction-search-counter-controls button{padding:0;background-color:transparent;flex:1 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;height:100%;white-space:nowrap}:host(:not([data-tabbing=true])) .redaction-search-counter-controls button,html:not([data-tabbing=true]) .redaction-search-counter-controls button{outline:none}.redaction-search-counter-controls button:hover:not(:disabled){color:var(--secondary-button-hover)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-counter-controls button{font-size:var(--font-size-default)}}.redaction-search-counter-controls button.disabled{opacity:.5}.redaction-search-counter-controls button.disabled span{color:var(--secondary-button-text)}.redaction-search-results-container{flex:1 1 auto;background-color:var(--gray-2);color:var(--faded-text);font-size:13px;border-left:1px solid var(--lighter-border);border-right:1px solid var(--lighter-border);display:flex;flex-direction:column}.redaction-search-no-results,.redaction-search-results-container.emptyList{justify-content:center;align-items:center}.redaction-search-panel-controls{display:flex;flex-direction:row;flex:0 1 52px;padding:12px;background-color:var(--component-background);border:1px solid var(--lighter-border);margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls{margin-bottom:30px;font-size:13px}}.redaction-search-panel-controls button{border:none;background-color:transparent;height:28px;padding:0 16px;cursor:pointer}:host(:not([data-tabbing=true])) .redaction-search-panel-controls button,html:not([data-tabbing=true]) .redaction-search-panel-controls button{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls button{height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls button{height:32px}}.redaction-search-panel-controls .Button{white-space:nowrap}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .redaction-search-panel-controls .Button{font-size:var(--font-size-default)}}.redaction-search-panel-controls .Button.cancel{flex:2 1 auto;color:var(--secondary-button-text);border:none;cursor:pointer;margin-right:20px}.redaction-search-panel-controls .Button.cancel:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected{flex:1 1 auto;border:1px solid var(--secondary-button-text);border-radius:4px;margin-right:8px}.redaction-search-panel-controls .Button.redact-all-selected span{color:var(--secondary-button-text)}.redaction-search-panel-controls .Button.redact-all-selected.disabled{opacity:.5}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled){border-color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.redact-all-selected:hover:not(.disabled) span{color:var(--secondary-button-hover)}.redaction-search-panel-controls .Button.mark-all-selected{flex:2 1 auto;background-color:var(--primary-button)!important;border:1px solid var(--primary-button);border-radius:4px}.redaction-search-panel-controls .Button.mark-all-selected span{color:var(--primary-button-text)}.redaction-search-panel-controls .Button.mark-all-selected:hover:not(.disabled){border-color:var(--primary-button-hover);background-color:var(--primary-button-hover)!important}.redaction-search-panel-controls .Button.mark-all-selected.disabled{opacity:.5}',
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1803: function (e, t, n) {
			"use strict";
			n.r(t);
			n(120),
				n(8),
				n(32),
				n(90),
				n(19),
				n(12),
				n(13),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22),
				n(29),
				n(47),
				n(23),
				n(24),
				n(49),
				n(48);
			var o = n(0),
				r = n.n(o),
				a = n(3),
				i = n.n(a),
				c = (n(36), n(58), n(44), n(347)),
				l = n(17),
				s = n.n(l),
				d = n(43),
				u = n(1584),
				p = (n(124), n(1));
			function m(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return f(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return f(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function f(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var h = r.a.createContext(),
				b = function (e) {
					var t = e.children,
						n = m(Object(o.useState)(null), 2),
						a = n[0],
						i = n[1],
						c = m(Object(o.useState)(!1), 2),
						l = c[0],
						s = c[1],
						d = m(Object(o.useState)(-1), 2),
						u = d[0],
						f = d[1];
					Object(o.useEffect)(function () {
						var e = function (e, t) {
								if ("selected" === t) {
									var n = e.filter(function (e) {
											return "Redact" === e.Subject;
										}),
										o = n.length > 0 ? n[0].Id : null;
									i(o);
								} else i(null);
							},
							t = function (e) {
								if (e) {
									var t = (p.a.getPageSearchResults() || []).findIndex(
										function (t) {
											return p.a.isSearchResultEqual(t, e);
										},
									);
									f(t);
								}
							};
						return (
							p.a.addEventListener("annotationSelected", e),
							p.a.addEventListener("activeSearchResultChanged", t),
							function () {
								p.a.removeEventListener("annotationSelected", e),
									p.a.removeEventListener("activeSearchResultChanged", t);
							}
						);
					}, []);
					var b = {
						selectedRedactionItemId: a,
						setSelectedRedactionItemId: i,
						isRedactionSearchActive: l,
						setIsRedactionSearchActive: s,
						activeSearchResultIndex: u,
					};
					return r.a.createElement(h.Provider, { value: b }, t);
				},
				y = n(147),
				g = n(42),
				v = (n(1753), n(41), n(101), n(112), n(6)),
				x = n(4),
				w = n(225),
				O = n(105),
				S = n.n(O),
				A = (n(1755), n(1568));
			function j() {
				return (j = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function E(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return R(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return R(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function R(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var k = function (e) {
				var t = E(
					Object(v.e)(function (e) {
						return [x.a.getRedactionPanelWidth(e)];
					}, v.c),
					1,
				)[0];
				return r.a.createElement(A.a, j({}, e, { panelWidth: t, comment: !0 }));
			};
			function P(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return C(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return C(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function C(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var T = function (e) {
					var t = P(
							Object(v.e)(function (e) {
								var t;
								return [
									null === (t = x.a.getFeatureFlags(e)) || void 0 === t
										? void 0
										: t.customizableUI,
								];
							}),
							1,
						)[0],
						n = e.iconColor,
						o = e.annotation,
						a = e.author,
						i = e.dateFormat,
						l = e.language,
						u = e.onRedactionItemDelete,
						p = e.onRedactionItemSelection,
						m = e.textPreview,
						f = e.isSelected,
						h = e.timezone,
						b = Object(c.a)().t,
						O = Object(w.a)(o);
					if (h) {
						var A = O.toLocaleString("en-US", { timeZone: h });
						O = new Date(A);
					}
					var j,
						E = O
							? S()(O).locale(l).format(i)
							: b("option.notesPanel.noteContent.noDate"),
						R = "".concat(a, " - ").concat(E),
						C = s()(
							"redaction-item",
							{ "redaction-item-selected": f },
							{ "modular-ui": t },
						),
						T = o.label,
						I = o.icon,
						N = void 0 === I ? "icon-text-redaction" : I,
						M = o.redactionType;
					return (
						(j =
							M === y.c.TEXT
								? r.a.createElement(k, { linesToBreak: 2 }, m)
								: M === y.c.FULL_PAGE ||
										M === y.c.FULL_VIDEO_FRAME ||
										M === y.c.REGION ||
										M === y.c.AUDIO_REDACTION ||
										M === y.c.FULL_VIDEO_FRAME_AND_AUDIO
									? b(T)
									: o.getContents()),
						r.a.createElement(
							"li",
							{ className: C },
							r.a.createElement(g.a, {
								className: "redaction-item-button",
								onClick: p,
								ariaLabel: ""
									.concat(j, " ")
									.concat(R, " ")
									.concat(b("action.select")),
								ariaCurrent: f,
							}),
							r.a.createElement(
								"div",
								{ className: "redaction-icon-container" },
								r.a.createElement(d.a, { glyph: N, color: n }),
							),
							r.a.createElement(
								"div",
								{ className: "redaction-item-info" },
								r.a.createElement(
									"div",
									{ className: "redaction-item-preview" },
									j,
								),
								o.OverlayText
									? r.a.createElement(
											"div",
											{ className: "redaction-item-label-text" },
											b("option.stylePopup.labelText"),
											": ",
											o.OverlayText,
										)
									: null,
								r.a.createElement(
									"div",
									{ className: "redaction-item-date-author" },
									R,
								),
							),
							r.a.createElement(g.a, {
								className: "redaction-item-delete",
								style: { marginLeft: "auto" },
								img: "icon-close",
								onClick: u,
								ariaLabel: ""
									.concat(j, " ")
									.concat(R, " ")
									.concat(b("action.delete")),
							}),
						)
					);
				},
				I = r.a.memo(T);
			function N(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return M(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return M(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function M(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var D = function (e) {
					var t = e.annotation,
						n = Object(o.useContext)(h),
						a = n.selectedRedactionItemId,
						i = n.setSelectedRedactionItemId,
						c = N(
							Object(v.e)(function (e) {
								return [
									x.a.getNoteDateFormat(e),
									x.a.getCurrentLanguage(e),
									x.a.getCustomNoteSelectionFunction(e),
									x.a.getTimezone(e),
								];
							}, v.c),
							4,
						),
						l = c[0],
						s = c[1],
						d = c[2],
						u = c[3],
						m = t.getCustomData("trn-annot-preview"),
						f = t.StrokeColor.toString(),
						b = p.a.getDisplayAuthor(t.Author),
						y = Object(o.useCallback)(
							function () {
								d && d(t),
									p.a.deselectAllAnnotations(),
									p.a.selectAnnotation(t),
									p.a.jumpToAnnotation(t),
									i(t.Id);
							},
							[t],
						),
						g = Object(o.useCallback)(
							function () {
								p.a.deleteAnnotations([t]);
							},
							[t],
						);
					return r.a.createElement(I, {
						dateFormat: l,
						language: s,
						timezone: u,
						author: b,
						annotation: t,
						iconColor: f,
						textPreview: m,
						onRedactionItemDelete: g,
						onRedactionItemSelection: y,
						isSelected: a === t.Id,
					});
				},
				_ = n(197),
				L =
					(n(1757),
					function (e) {
						var t = e.pageNumber,
							n = e.redactionItems,
							o = Object(c.a)().t;
						return r.a.createElement(
							_.a,
							{
								className: "redaction-page-group",
								header: function () {
									return "".concat(o("option.shared.page"), " ").concat(t);
								},
								expansionDescription: ""
									.concat(o("option.shared.page"), " ")
									.concat(t, " ")
									.concat(o("redactionPanel.redactionItems")),
								headingLevel: 2,
							},
							r.a.createElement(
								"ul",
								{ className: "redaction-items" },
								n.map(function (e) {
									return r.a.createElement(D, {
										annotation: e,
										key: "".concat(e.Id, "-").concat(t),
									});
								}),
							),
						);
					});
			L.propTypes = {
				pageNumber: i.a.oneOfType([i.a.number, i.a.string]),
				redactionItems: i.a.array,
			};
			var H = L,
				z = n(174);
			function W() {
				return (W = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function B(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return F(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return F(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function F(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var q = function (e) {
					var t = e.redactionItems,
						n = B(Object(o.useState)([]), 2),
						a = n[0],
						i = n[1];
					return (
						Object(o.useEffect)(
							function () {
								i(Object(z.c)().position.getSortedNotes(t));
							},
							[t],
						),
						r.a.createElement(H, W({ redactionItems: a }, e))
					);
				},
				U = n(5),
				V = n(95);
			function $() {
				return ($ = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function G(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return J(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					Y(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function X(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					Y(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Y(e, t) {
				if (e) {
					if ("string" == typeof e) return J(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? J(e, t)
								: void 0
					);
				}
			}
			function J(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var K = function (e) {
					var t = e.redactionAnnotations,
						n = e.applyAllRedactions,
						a = e.deleteAllRedactionAnnotations,
						i = e.redactionTypesDictionary,
						l = Object(c.a)().t,
						p = X(Object(o.useState)({}), 2),
						m = p[0],
						f = p[1],
						b = X(Object(o.useState)([]), 2),
						v = b[0],
						x = b[1],
						w = Object(o.useContext)(h).isTestMode;
					Object(o.useEffect)(
						function () {
							var e = {};
							t.forEach(function (t) {
								var n = Object(y.b)(t),
									o = i[n] || {
										icon: "icon-tool-redaction-area",
										label: "redactionPanel.redactionItem.regionRedaction",
									},
									r = o.label,
									a = o.icon;
								(t.label = r), (t.icon = a), (t.redactionType = n);
								var c = t.PageNumber;
								void 0 === e[c] ? (e[c] = [t]) : (e[c] = [t].concat(G(e[c])));
							}),
								f(e),
								x(Object.keys(e));
						},
						[t],
					);
					var O,
						S = r.a.createElement(
							"div",
							{ className: "no-marked-redactions" },
							r.a.createElement(
								"div",
								null,
								r.a.createElement(d.a, {
									className: "empty-icon",
									glyph: "icon-no-marked-redactions",
								}),
							),
							r.a.createElement(
								"div",
								{ className: "msg" },
								l("redactionPanel.noMarkedRedactions"),
							),
						),
						A = s()("redact-all-marked", { disabled: 0 === t.length }),
						j = s()("clear-all-marked", { disabled: 0 === t.length }),
						E = Object(V.a)(n);
					return r.a.createElement(
						r.a.Fragment,
						null,
						r.a.createElement(
							"h2",
							{ className: "marked-redaction-counter" },
							l("redactionPanel.redactionCounter"),
							r.a.createElement("span", null, " (".concat(t.length, ")")),
						),
						v.length > 0
							? ((O = w ? { initialItemCount: v.length } : {}),
								r.a.createElement(
									"div",
									{ className: "redaction-group-container" },
									r.a.createElement(
										u.a,
										$(
											{
												data: v,
												itemContent: function (e, t) {
													return r.a.createElement(q, {
														key: e,
														pageNumber: t,
														redactionItems: m[t],
													});
												},
											},
											O,
										),
									),
								))
							: S,
						r.a.createElement(
							"div",
							{ className: "redaction-panel-controls" },
							r.a.createElement(g.a, {
								disabled: 0 === t.length,
								className: j,
								onClick: a,
								label: l("redactionPanel.clearMarked"),
							}),
							r.a.createElement(g.a, {
								disabled: 0 === t.length,
								className: A,
								onClick: E,
								dataElement: U.a.REDACT_ALL_MARKED_BUTTON,
								label: l("redactionPanel.redactAllMarked"),
							}),
						),
					);
				},
				Q = n(2),
				Z = n(1594),
				ee = n(66),
				te = n(75),
				ne = (n(97), n(79), n(1528)),
				oe = n(1629),
				re = n(1586),
				ae = (n(1759), ["id", "label"]);
			function ie() {
				return (ie = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function ce(e, t) {
				if (null == e) return {};
				var n,
					o,
					r = (function (e, t) {
						if (null == e) return {};
						var n,
							o,
							r = {},
							a = Object.keys(e);
						for (o = 0; o < a.length; o++)
							(n = a[o]), t.indexOf(n) >= 0 || (r[n] = e[n]);
						return r;
					})(e, t);
				if (Object.getOwnPropertySymbols) {
					var a = Object.getOwnPropertySymbols(e);
					for (o = 0; o < a.length; o++)
						(n = a[o]),
							t.indexOf(n) >= 0 ||
								(Object.prototype.propertyIsEnumerable.call(e, n) &&
									(r[n] = e[n]));
				}
				return r;
			}
			var le = function (e) {
				var t = e.id,
					n = e.label,
					o = ce(e, ae);
				return r.a.createElement(
					re.a,
					null,
					r.a.createElement(
						"label",
						{ htmlFor: t, className: "creatable-multi-select-label" },
						n,
					),
					r.a.createElement(
						"div",
						{
							onTouchEndCapture: function (e) {
								return e.stopPropagation();
							},
						},
						r.a.createElement(oe.a, ie({ isMulti: !0 }, o, { inputId: t })),
					),
				);
			};
			(le.propTypes = { id: i.a.string, label: i.a.string }),
				(le.defaultProps = { id: "", label: "" });
			var se = le,
				de = n(106),
				ue = (n(1761), ["children"]);
			function pe(e) {
				return (pe =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function me() {
				return (me = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function fe(e, t) {
				if (null == e) return {};
				var n,
					o,
					r = (function (e, t) {
						if (null == e) return {};
						var n,
							o,
							r = {},
							a = Object.keys(e);
						for (o = 0; o < a.length; o++)
							(n = a[o]), t.indexOf(n) >= 0 || (r[n] = e[n]);
						return r;
					})(e, t);
				if (Object.getOwnPropertySymbols) {
					var a = Object.getOwnPropertySymbols(e);
					for (o = 0; o < a.length; o++)
						(n = a[o]),
							t.indexOf(n) >= 0 ||
								(Object.prototype.propertyIsEnumerable.call(e, n) &&
									(r[n] = e[n]));
				}
				return r;
			}
			function he(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function be(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? he(Object(n), !0).forEach(function (t) {
								ye(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: he(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function ye(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== pe(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== pe(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === pe(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			var ge = function (e, t, n) {
					var o =
						!(arguments.length > 3 && void 0 !== arguments[3]) || arguments[3];
					return o ? (e ? t : n) : "transparent";
				},
				ve = function (e) {
					var t = e.data;
					return r.a.createElement(
						ne.o.Option,
						e,
						t.icon && r.a.createElement(d.a, { glyph: t.icon }),
						t.label,
					);
				};
			ve.propTypes = { data: i.a.object.isRequired };
			var xe = function (e) {
				var t = e.data;
				return r.a.createElement(
					"div",
					{ tabIndex: 0, style: { display: "flex", height: "18px" } },
					t.icon && r.a.createElement(d.a, { glyph: t.icon }),
					t.label,
				);
			};
			xe.propTypes = { data: i.a.object.isRequired };
			var we = function (e) {
					var t = e.children,
						n = fe(e, ue);
					return r.a.createElement(
						ne.o.Control,
						n,
						r.a.createElement(
							"div",
							{
								className:
									"redaction-search-multi-select-search-icon-container",
							},
							r.a.createElement(d.a, {
								className: "redaction-search-multi-select-search-icon",
								glyph: "icon-header-search",
							}),
						),
						t,
					);
				},
				Oe = function (e) {
					var t = Object(c.a)().t,
						n = e.data.label,
						o = be(
							be({}, e),
							{},
							{
								innerProps: be(
									be({}, e.innerProps),
									{},
									{
										"aria-label": "".concat(t("action.remove"), " ").concat(n),
										tabIndex: 0,
										onKeyDown: function (t) {
											("Enter" !== t.key && " " !== t.key) ||
												(t.stopPropagation(), e.innerProps.onClick());
										},
									},
								),
							},
						);
					return r.a.createElement(ne.o.MultiValueRemove, o);
				};
			we.propTypes = { children: i.a.node };
			var Se = function (e) {
				var t = Object(c.a)().t,
					n = e.activeTheme,
					o = e.redactionSearchOptions,
					a = [{ label: t("redactionPanel.search.pattern"), options: o }],
					i = (function (e) {
						return {
							groupHeading: function (t) {
								return be(
									be({}, t),
									{},
									{
										textTransform: "none",
										fontSize: "13px",
										fontWeight: "bold",
										color: ge(e, de.b.white, de.c["text-color"]),
										paddingBottom: "8px",
										paddingLeft: "8px",
										paddingTop: "10px",
									},
								);
							},
							group: function (e) {
								return be(be({}, e), {}, { padding: "0px" });
							},
							menu: function (e) {
								return be(
									be({}, e),
									{},
									{
										padding: "0px 0px 0px 0px",
										borderRadius: "4px",
										overflowY: "visible",
										margin: "0",
									},
								);
							},
							menuList: function (t) {
								return be(
									be({}, t),
									{},
									{
										padding: "0px",
										backgroundColor: ge(e, de.b.black, de.b.gray0),
										overflowY: "visible",
										borderRadius: "4px",
									},
								);
							},
							multiValue: function (t) {
								return be(
									be({}, t),
									{},
									{
										backgroundColor: ge(e, de.b.blue1DarkMode, de.b.gray2),
										padding: "2px 8px",
										fontSize: "13px",
										borderRadius: "4px",
										overflowY: "hidden",
										whiteSpace: "nowrap",
										color: ge(e, de.b.white, de.c["text-color"]),
									},
								);
							},
							multiValueRemove: function (e) {
								return be(
									be({}, e),
									{},
									{
										color: de.b.gray6,
										borderRadius: "4px",
										marginLeft: "4px",
										padding: "0px",
										"&:hover": {
											backgroundColor: de.b.gray2,
											boxShadow: "inset 0 0 0 1px ".concat(de.b.blue6),
											color: de.b.gray6,
										},
										svg: { color: de.b.gray6, height: "16px", width: "16px" },
									},
								);
							},
							option: function (t, n) {
								var o = n.isFocused;
								return be(
									be({}, t),
									{},
									{
										display: "flex",
										fontSize: "13px",
										padding: "6px 8px 0",
										outline: o ? "var(--focus-visible-outline)" : void 0,
										"&:hover": {
											backgroundColor: ge(
												e,
												de.b.blue1DarkMode,
												de.c["primary-button-hover"],
											),
											color: de.b.gray0,
										},
										backgroundColor: ge(e, de.b.blue1DarkMode, de.b.gray0),
										overflowY: "visible",
										whiteSpace: "normal",
										"&:last-child": {
											borderRadius: "0 0 4px 4px",
											paddingBottom: "6px",
										},
									},
								);
							},
							noOptionsMessage: function (e) {
								return be(be({}, e), {}, { color: de.c["text-color"] });
							},
							valueContainer: function (e) {
								return be(
									be({}, e),
									{},
									{
										padding: "1px",
										maxHeight: Object(ee.b)() ? "55px" : "70px",
										overflowY: "scroll",
									},
								);
							},
							control: function (t) {
								return be(
									be({}, t),
									{},
									{
										backgroundColor: ge(e, de.b.gray10, de.b.white),
										minHeight: "28px",
										borderColor: ge(e, de.b.gray8, de.b.gray6),
										"&:focus-within": {
											borderColor: ge(e, de.b.gray8, de.b.blue5),
										},
										"&:hover": { borderColor: ge(e, de.b.gray8, de.b.gray6) },
										boxShadow: "none !important",
									},
								);
							},
							placeholder: function (t) {
								return be(
									be({}, t),
									{},
									{
										fontSize: "13px",
										color: ge(e, de.b.gray7, de.b.gray5),
										paddingLeft: "4px",
									},
								);
							},
							input: function (t) {
								return be(
									be({}, t),
									{},
									{
										fontSize: "13px",
										color: ge(e, de.b.white, de.c["text-color"]),
										paddingLeft: "3px",
									},
								);
							},
						};
					})("dark" === n);
				return r.a.createElement(
					se,
					me(
						{
							options: a,
							styles: i,
							components: {
								Option: ve,
								MultiValueLabel: xe,
								IndicatorsContainer: function () {
									return null;
								},
								Control: we,
								MultiValueRemove: Oe,
							},
							placeholder: "",
							formatCreateLabel: function (e) {
								return "".concat(t("component.searchPanel"), " ").concat(e);
							},
							id: "redaction-search-multi-select",
							label: t("redactionPanel.redactionSearchPlaceholder"),
						},
						e,
					),
				);
			};
			Se.propTypes = {
				activeTheme: i.a.string.isRequired,
				redactionSearchOptions: i.a.array.isRequired,
			};
			var Ae = Se;
			n(1763);
			function je(e) {
				return (je =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function Ee(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return Ie(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					Te(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Re(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function ke(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? Re(Object(n), !0).forEach(function (t) {
								Pe(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: Re(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function Pe(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== je(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== je(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === je(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function Ce(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					Te(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Te(e, t) {
				if (e) {
					if ("string" == typeof e) return Ie(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? Ie(e, t)
								: void 0
					);
				}
			}
			function Ie(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Ne = function (e) {
					var t = { textSearch: [], caseSensitive: !0 };
					return e
						? (e.forEach(function (e) {
								var n = e.type;
								n === y.c.TEXT ? t.textSearch.push(e.label) : (t[n] = !0),
									e.regex &&
										(t.caseSensitive = t.caseSensitive && !e.regex.ignoreCase);
							}),
							t)
						: t;
				},
				Me = function (e) {
					var t = e.setIsRedactionSearchActive,
						n = e.searchTerms,
						o = e.setSearchTerms,
						a = e.executeRedactionSearch,
						i = e.activeTheme,
						l = e.redactionSearchOptions,
						s = Ce(Object(c.a)(), 1)[0],
						d = l.map(function (e) {
							return ke(ke({}, e), {}, { label: s(e.label) });
						});
					return r.a.createElement(
						te.a,
						{
							className: "RedactionSearchOverlay",
							dataElement: "redactionSearchOverlay",
						},
						r.a.createElement(Ae, {
							onFocus: function () {
								return t(!0);
							},
							value: n,
							onCreateOption: function (e) {
								var t = { label: e, value: e, type: y.c.TEXT },
									r = [].concat(Ee(n || []), [t]);
								o(r);
								var i = Ne(r);
								(i.caseSensitive = !1), a(i);
							},
							onChange: function (e) {
								o(e);
								var t = Ne(e);
								a(t);
							},
							activeTheme: i,
							redactionSearchOptions: d,
						}),
					);
				},
				De = (n(84), n(451));
			function _e(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return Le(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return Le(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return Le(e, t);
					})(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Le(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var He = function (e) {
				return function (t) {
					var n = (0, e.getState)(),
						o = x.a.getRedactionSearchPatterns(n),
						r = Object.keys(o).reduce(function (e, t) {
							var n = o[t],
								r = n.regex;
							return (e[n.type] = r), e;
						}, {}),
						a = { regex: !0, caseSensitive: t.caseSensitive },
						i = _e(t.textSearch);
					Object.keys(t).forEach(function (e) {
						var t = r[e];
						t && i.push(t.source);
					});
					var c = i.join("|");
					"" !== c ? Object(De.a)()(c, a) : p.a.clearSearchResults();
				};
			};
			function ze(e) {
				return (ze =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function We() {
				return (We = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function Be(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function Fe(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? Be(Object(n), !0).forEach(function (t) {
								qe(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: Be(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function qe(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== ze(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== ze(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === ze(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function Ue(e, t) {
				He(t)(e);
			}
			var Ve = function (e) {
					var t = Object(o.useContext)(h).setIsRedactionSearchActive,
						n = Object(v.f)(),
						a = Object(v.e)(function (e) {
							return x.a.getActiveTheme(e);
						}),
						i = Object(v.e)(function (e) {
							return x.a.getRedactionSearchPatterns(e);
						}, v.c),
						c = Object.values(i).map(function (e) {
							return Fe(Fe({}, e), {}, { value: e.type });
						});
					return r.a.createElement(
						Me,
						We(
							{
								setIsRedactionSearchActive: t,
								executeRedactionSearch: function () {
									var e =
										arguments.length > 0 && void 0 !== arguments[0]
											? arguments[0]
											: {};
									return Ue(e, n);
								},
								activeTheme: a,
								redactionSearchOptions: c,
							},
							e,
						),
					);
				},
				$e = (n(46), n(52), n(1355)),
				Ge =
					(n(1765),
					function (e) {
						var t = e.isChecked,
							n = e.onChange,
							o = e.onClickResult,
							a = e.isActive,
							i = e.icon,
							c = e.ambientStr,
							l = (function (e) {
								var t = e.ambientStr,
									n = e.resultStrStart,
									o = e.resultStrEnd,
									a = e.resultStr;
								if (e.type === y.c.TEXT) {
									var i = "" === t ? a : t.slice(n, o),
										c = t.slice(0, n),
										l = t.slice(o);
									return r.a.createElement(
										r.a.Fragment,
										null,
										c,
										r.a.createElement("span", { className: "search-value" }, i),
										l,
									);
								}
								return a;
							})(e),
							u = s()("redaction-search-result", { active: a });
						return r.a.createElement(
							"li",
							{ className: u },
							r.a.createElement("button", {
								className: "redaction-search-result-button",
								onClick: o,
								"aria-label": c,
								"aria-current": a,
							}),
							r.a.createElement(
								"div",
								{ style: { paddingRight: "14px" } },
								r.a.createElement($e.a, {
									"aria-label": "".concat(c),
									checked: t,
									onChange: n,
								}),
							),
							r.a.createElement(
								"div",
								{ style: { paddingRight: "14px" } },
								r.a.createElement(d.a, { glyph: i }),
							),
							r.a.createElement(
								"div",
								{ className: "redaction-search-result-info" },
								l,
							),
						);
					});
			Ge.propTypes = {
				isChecked: i.a.bool,
				onChange: i.a.func,
				onClickResult: i.a.func,
				isActive: i.a.bool,
				icon: i.a.string,
				ambientStr: i.a.string,
			};
			var Xe = r.a.memo(Ge),
				Ye = function (e) {
					var t = e.searchResult,
						n = e.checked,
						a = e.checkResult,
						i = Object(o.useContext)(h).activeSearchResultIndex,
						c = t.ambientStr,
						l = t.resultStrStart,
						s = t.resultStrEnd,
						d = t.resultStr,
						u = t.icon,
						m = t.index,
						f = t.type,
						b = Object(o.useCallback)(
							function (e) {
								a(e, m);
							},
							[m, a],
						),
						y = Object(o.useCallback)(
							function () {
								p.a.setActiveSearchResult(t);
							},
							[t],
						);
					return r.a.createElement(Xe, {
						ambientStr: c,
						resultStrStart: l,
						resultStrEnd: s,
						resultStr: d,
						icon: u,
						type: f,
						isChecked: n,
						onChange: b,
						onClickResult: y,
						isActive: i === m,
					});
				};
			n(1767);
			function Je(e) {
				return (Je =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function Ke(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function Qe(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? Ke(Object(n), !0).forEach(function (t) {
								Ze(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: Ke(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function Ze(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== Je(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== Je(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === Je(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function et(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return tt(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return tt(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function tt(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var nt = function (e) {
					var t = e.pageNumber,
						n = e.searchResults,
						a = e.selectedSearchResultIndexes,
						i = e.setSelectedSearchResultIndexes,
						l = Object(c.a)().t,
						s = n.map(function (e) {
							return e.index;
						}),
						d = et(Object(o.useState)(!1), 2),
						u = d[0],
						p = d[1];
					Object(o.useEffect)(
						function () {
							var e = s.reduce(function (e, t) {
								return a[t] && e;
							}, !0);
							p(e);
						},
						[a, s],
					);
					var m = Object(o.useCallback)(
							function (e) {
								var t = e.target.checked;
								s.forEach(function (e) {
									a[e] = t;
								}),
									p(t),
									i(Qe({}, a));
							},
							[a, s],
						),
						f = Object(o.useCallback)(
							function (e, t) {
								var n = e.target.checked;
								(a[t] = n), i(Qe({}, a));
							},
							[a],
						);
					return r.a.createElement(
						"div",
						{ className: "redaction-search-results-page-number" },
						r.a.createElement($e.a, {
							className: "redaction-search-results-page-number-checkbox",
							"aria-label": "".concat(l("option.shared.page"), " ").concat(t),
							checked: u,
							onChange: function (e) {
								e.stopPropagation(), m(e);
							},
						}),
						r.a.createElement(
							_.a,
							{
								header: function () {
									return "".concat(l("option.shared.page"), " ").concat(t);
								},
								style: { width: "100%" },
								expansionDescription: ""
									.concat(l("option.shared.page"), " ")
									.concat(t),
							},
							r.a.createElement(
								"ul",
								{ className: "redaction-search-results" },
								n.map(function (e, n) {
									return r.a.createElement(Ye, {
										checked: a[e.index],
										checkResult: f,
										searchResult: e,
										key: "".concat(n, "-").concat(t),
									});
								}),
							),
						),
					);
				},
				ot = n(1562),
				rt = (n(1769), "SEARCH_NOT_INITIATED"),
				at = "SEARCH_IN_PROGRESS",
				it = "SEARCH_DONE";
			function ct() {
				return (ct = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function lt(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return ut(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					dt(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function st(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					dt(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function dt(e, t) {
				if (e) {
					if ("string" == typeof e) return ut(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? ut(e, t)
								: void 0
					);
				}
			}
			function ut(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var pt = function (e) {
				var t = e.redactionSearchResults,
					n = e.searchStatus,
					a = e.onCancelSearch,
					i = e.isProcessingRedactionResults,
					l = e.markSelectedResultsForRedaction,
					d = e.redactSelectedResults,
					p = Object(c.a)().t,
					m = st(Object(o.useState)({}), 2),
					f = m[0],
					b = m[1],
					y = st(Object(o.useState)({}), 2),
					v = y[0],
					x = y[1],
					w = st(Object(o.useState)([]), 2),
					O = w[0],
					S = w[1],
					A = Object(o.useContext)(h).isTestMode;
				Object(o.useEffect)(
					function () {
						var e = {};
						t.forEach(function (t, n) {
							var o = t.pageNum;
							(t.index = n),
								void 0 === e[o]
									? (e[o] = [t])
									: (e[o] = [].concat(lt(e[o]), [t]));
						}),
							b(e);
						var n = {};
						t.forEach(function (e, t) {
							n[t] = !1;
						}),
							x(n);
					},
					[t],
				),
					Object(o.useEffect)(
						function () {
							var e = t.filter(function (e, t) {
								return v[t];
							});
							S(e);
						},
						[v],
					);
				var j = r.a.createElement(
						"div",
						{ "aria-label": p("message.noResults") },
						r.a.createElement(
							"p",
							{
								"aria-live": "assertive",
								role: "alert",
								className: "no-margin",
							},
							p("message.noResults"),
						),
					),
					E = 0 === t.length,
					R = s()("redaction-search-results-container", { emptyList: E }),
					k = s()("redact-all-selected", { disabled: 0 === O.length }),
					P = s()("mark-all-selected", { disabled: 0 === O.length }),
					C = (n === it && !i) || n === rt;
				return r.a.createElement(
					r.a.Fragment,
					null,
					r.a.createElement(
						"div",
						{ className: "redaction-search-counter-controls" },
						n === at &&
							r.a.createElement(
								"div",
								{ style: { flexGrow: 1 } },
								r.a.createElement(ot.a, { height: "18px", width: "18px" }),
							),
						C &&
							r.a.createElement(
								r.a.Fragment,
								null,
								r.a.createElement(
									"div",
									{ className: "redaction-search-results-counter" },
									r.a.createElement(
										"h4",
										{
											"aria-live": "assertive",
											role: "alert",
											className: "no-margin",
										},
										p("redactionPanel.searchResults"),
										r.a.createElement("span", null, " (".concat(t.length, ")")),
									),
								),
								r.a.createElement(g.a, {
									className: s()({ inactive: O.length < 1 }),
									onClick: function () {
										var e = {};
										t.forEach(function (t, n) {
											e[n] = !0;
										}),
											x(e);
									},
									disabled: E,
									label: p("action.selectAll"),
								}),
								r.a.createElement(g.a, {
									className: s()({ inactive: O.length < 1 }),
									disabled: E,
									onClick: function () {
										var e = {};
										t.forEach(function (t, n) {
											e[n] = !1;
										}),
											x(e);
									},
									label: p("action.unselect"),
								}),
							),
					),
					r.a.createElement(
						"div",
						{ className: R, role: "list" },
						n === rt &&
							r.a.createElement(
								"div",
								{ "aria-label": p("redactionPanel.search.start") },
								p("redactionPanel.search.start"),
							),
						n === at &&
							E &&
							i &&
							r.a.createElement(
								"div",
								null,
								r.a.createElement(ot.a, { height: "25px", width: "25px" }),
							),
						n === it && E && !i && j,
						(n === at || n === it) &&
							(function () {
								var e = Object.keys(f);
								if (e.length > 0) {
									var t = A ? { initialItemCount: e.length } : {};
									return r.a.createElement(
										u.a,
										ct(
											{
												data: e,
												itemContent: function (e, t) {
													return r.a.createElement(nt, {
														key: e,
														pageNumber: t,
														searchResults: f[t],
														selectedSearchResultIndexes: v,
														setSelectedSearchResultIndexes: x,
													});
												},
											},
											t,
										),
									);
								}
							})(),
					),
					r.a.createElement(
						"div",
						{ className: "redaction-search-panel-controls" },
						r.a.createElement(g.a, {
							onClick: function () {
								b({}), a();
							},
							label: p("action.cancel"),
							className: "cancel",
						}),
						r.a.createElement(g.a, {
							disabled: 0 === O.length,
							label: p("annotation.redact"),
							className: k,
							onClick: function () {
								d(O);
							},
						}),
						r.a.createElement(g.a, {
							disabled: 0 === O.length,
							label: p("action.addMark"),
							ariaLabel: p("action.addMark"),
							className: P,
							onClick: function () {
								l(O), a();
							},
						}),
					),
				);
			};
			function mt() {
				return (mt = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function ft(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return ht(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return ht(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function ht(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var bt = window.Core.Tools.ToolNames,
				yt = {
					OverlayText: "",
					StrokeColor: new window.Core.Annotations.Color(255, 0, 0),
					TextColor: new window.Core.Annotations.Color(255, 0, 0, 1),
					Font: "Helvetica",
				};
			function gt(e) {
				var t =
						arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : yt,
					n = t.StrokeColor,
					o = t.OverlayText,
					r = t.FillColor,
					a = t.Font,
					i = void 0 === a ? "Helvetica" : a,
					c = t.TextColor,
					l = t.FontSize,
					s = t.TextAlign,
					d = e.map(function (e) {
						var t = new window.Core.Annotations.RedactionAnnotation();
						return (
							(t.PageNumber = e.page_num),
							(t.Quads = e.quads.map(function (e) {
								return e.getPoints();
							})),
							(t.StrokeColor = n),
							(t.OverlayText = o),
							(t.FillColor = r),
							(t.Font = i),
							(t.FontSize = l),
							window.Core.Annotations.Utilities.calculateAutoFontSize &&
								(t.FontSize =
									window.Core.Annotations.Utilities.calculateAutoFontSize(t)),
							(t.TextColor = c),
							(t.TextAlign = s),
							t.setContents(e.result_str),
							(t.type = e.type),
							(t.Author = p.a.getCurrentUser()),
							"text" === e.type &&
								t.setCustomData("trn-annot-preview", e.result_str),
							t.setCustomData("trn-redaction-type", e.type),
							t
						);
					});
				return d;
			}
			var vt = function (e) {
				var t = e.onCancelSearch,
					n = Object(v.d)(),
					a = ft(
						Object(v.e)(function (e) {
							return [x.a.getActiveToolStyles(e), x.a.getActiveToolName(e)];
						}, v.c),
						2,
					),
					i = a[0],
					c = a[1],
					l = Object(o.useCallback)(
						function (e) {
							var t = p.a.getTool(bt.REDACTION),
								n = t && t.defaults ? t.defaults : yt,
								o = gt(e, c.includes("Redaction") ? i : n);
							p.a.getAnnotationManager().addAnnotations(o);
						},
						[i, c],
					);
				return r.a.createElement(
					pt,
					mt(
						{
							markSelectedResultsForRedaction: l,
							redactSelectedResults: function (e) {
								var o = gt(e, yt);
								n(Object(Z.a)(o, t));
							},
						},
						e,
					),
				);
			};
			function xt(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return wt(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return wt(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function wt(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Ot = function (e) {
				var t = Object(v.d)(),
					n = xt(Object(o.useState)([]), 2),
					a = n[0],
					i = n[1],
					c = Object(o.useContext)(h),
					l = c.isRedactionSearchActive,
					s = c.setIsRedactionSearchActive,
					u = e.redactionSearchResults,
					p = e.isProcessingRedactionResults,
					m = e.clearRedactionSearchResults,
					f = e.searchStatus,
					b = Object(ee.b)();
				return r.a.createElement(
					r.a.Fragment,
					null,
					b &&
						r.a.createElement(
							"div",
							{ className: "close-container" },
							r.a.createElement(
								"button",
								{
									className: "close-icon-container",
									onClick: function () {
										t(Q.a.closeElement("redactionPanel"));
									},
								},
								r.a.createElement(d.a, {
									glyph: "ic_close_black_24px",
									className: "close-icon",
								}),
							),
						),
					r.a.createElement(Ve, { searchTerms: a, setSearchTerms: i }),
					l &&
						r.a.createElement(vt, {
							redactionSearchResults: u,
							onCancelSearch: function () {
								i([]), m(), s(!1);
							},
							searchStatus: f,
							isProcessingRedactionResults: p,
						}),
				);
			};
			n(88);
			function St(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return At(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return At(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function At(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var jt = function () {
					var e = St(Object(o.useState)(rt), 2),
						t = e[0],
						n = e[1],
						r = St(Object(o.useState)([]), 2),
						a = r[0],
						i = r[1],
						c = St(Object(o.useState)(!1), 2),
						l = c[0],
						s = c[1],
						d = Object(v.e)(function (e) {
							return x.a.getRedactionSearchPatterns(e);
						}, v.c),
						u = Object(o.useMemo)(
							function () {
								return Object.keys(d).reduce(function (e, t) {
									var n = d[t],
										o = n.regex,
										r = n.type,
										a = n.icon;
									return (e[r] = { regex: o, icon: a }), e;
								}, {});
							},
							[d],
						),
						m = Object(o.useCallback)(
							function (e) {
								var t = e.resultStr,
									n = Object.keys(u).find(function (e) {
										return u[e].regex.test(t);
									});
								e.type = void 0 === n ? y.c.TEXT : n;
								var o = (u[e.type] || {}).icon,
									r = void 0 === o ? "icon-text-redaction" : o;
								return (e.icon = r), e;
							},
							[u],
						),
						f = Object(o.useCallback)(function () {
							i([]), p.a.clearSearchResults(), s(!1);
						});
					return (
						Object(o.useEffect)(
							function () {
								function e(e) {
									var t = e.map(m);
									s(!0), i(t);
								}
								return (
									p.a.addEventListener("searchResultsChanged", e),
									function () {
										p.a.removeEventListener("searchResultsChanged", e);
									}
								);
							},
							[t],
						),
						Object(o.useEffect)(function () {
							function e(e) {
								null == e
									? n(rt)
									: e
										? n(at)
										: (n(it),
											setTimeout(function () {
												s(!1);
											}, 100));
							}
							return (
								p.a.addEventListener("searchInProgress", e),
								function () {
									p.a.removeEventListener("searchInProgress", e);
								}
							);
						}, []),
						{
							redactionSearchResults: a,
							isProcessingRedactionResults: l,
							clearRedactionSearchResults: f,
							searchStatus: t,
						}
					);
				},
				Et = function () {
					var e = jt(),
						t = e.redactionSearchResults,
						n = e.isProcessingRedactionResults,
						o = e.clearRedactionSearchResults,
						a = e.searchStatus;
					return r.a.createElement(Ot, {
						redactionSearchResults: t,
						isProcessingRedactionResults: n,
						clearRedactionSearchResults: o,
						searchStatus: a,
					});
				};
			function Rt(e) {
				return (Rt =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function kt(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function Pt(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? kt(Object(n), !0).forEach(function (t) {
								Ct(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: kt(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function Ct(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== Rt(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== Rt(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === Rt(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function Tt(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									l = !1;
								} else
									for (
										;
										!(l = (o = a.call(n)).done) &&
										(c.push(o.value), c.length !== t);
										l = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!l &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return c;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return It(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return It(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function It(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Nt = function (e) {
				var t = Tt(
						Object(v.e)(function (e) {
							return [
								x.a.isElementOpen(e, "redactionPanel"),
								x.a.isElementDisabled(e, "redactionPanel"),
								x.a.getRedactionPanelWidth(e),
								x.a.isInDesktopOnlyMode(e),
								x.a.getCustomApplyRedactionsHandler(e),
								x.a.getRedactionSearchPatterns(e),
							];
						}, v.c),
						6,
					),
					n = t[0],
					a = t[1],
					i = t[2],
					c = t[3],
					l = t[4],
					s = t[5],
					u = Object(ee.b)(),
					m = e.redactionAnnotationsList,
					f = e.isCustomPanel,
					b = e.dataElement,
					g = Object(o.useMemo)(
						function () {
							return Pt(
								Pt(
									{},
									Object.keys(s).reduce(function (e, t) {
										var n = s[t],
											o = n.label,
											r = n.type,
											a = n.icon;
										return (e[r] = { label: o, icon: a }), e;
									}, {}),
								),
								y.a,
							);
						},
						[s],
					),
					w = Object(v.d)(),
					O = function () {
						var e = f ? b : "redactionPanel";
						w(Q.a.closeElement(e));
					},
					S =
						f || (!c && u)
							? {}
							: { width: "".concat(i, "px"), minWidth: "".concat(i, "px") },
					A = Object(o.useContext)(h).isRedactionSearchActive,
					j = Tt(Object(o.useState)(!1), 2),
					E = j[0],
					R = j[1];
				if (
					(Object(o.useEffect)(
						function () {
							var e = setTimeout(function () {
								R(!n);
							}, 500);
							return function () {
								clearTimeout(e);
							};
						},
						[n],
					),
					a || (!n && E && !f))
				)
					return null;
				var k = f ? b : "redactionPanel";
				return r.a.createElement(
					te.a,
					{ dataElement: k, className: "Panel RedactionPanel", style: S },
					!c &&
						u &&
						!f &&
						r.a.createElement(
							"div",
							{ className: "close-container" },
							r.a.createElement(
								"div",
								{ className: "close-icon-container", onClick: O },
								r.a.createElement(d.a, {
									glyph: "ic_close_black_24px",
									className: "close-icon",
								}),
							),
						),
					r.a.createElement(Et, null),
					!A &&
						r.a.createElement(K, {
							redactionAnnotations: m,
							redactionTypesDictionary: g,
							applyAllRedactions: function () {
								var e = function () {
									var e = f ? O : function () {};
									w(Object(Z.a)(m, e));
								};
								l ? l(m, e) : e();
							},
							deleteAllRedactionAnnotations: function () {
								p.a.deleteAnnotations(m);
							},
						}),
				);
			};
			(Nt.propTypes = {
				redactionAnnotationsList: i.a.array,
				isCustomPanel: i.a.bool,
				dataElement: i.a.string,
			}),
				(Nt.defaultProps = { isCustomPanel: !1, dataElement: "" });
			var Mt = function (e) {
				return r.a.createElement(b, null, r.a.createElement(Nt, e));
			};
			t.default = Mt;
		},
	},
]);
//# sourceMappingURL=chunk.31.js.map
