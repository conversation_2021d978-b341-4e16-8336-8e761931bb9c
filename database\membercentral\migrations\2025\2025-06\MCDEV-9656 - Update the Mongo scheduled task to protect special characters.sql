USE membercentral;
GO

ALTER PROC dbo.ams_getAuditLogMsg
@auditLogTable varchar(60),
@outputAsChangesArray bit = 0,
@isBulkUpdate bit = 0,
@msg varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogCol_oldVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_oldVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_newVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_newVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_dataTypeCode') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_dataTypeCode;
	CREATE TABLE #tmpAuditLogCol_oldVal (refID int, col varchar(max), oldValue varchar(max));
	CREATE TABLE #tmpAuditLogCol_newVal (refID int, col varchar(max), newValue varchar(max));
	CREATE TABLE #tmpAuditLogCol_dataTypeCode (col varchar(max), dataTypeCode varchar(30));

	IF @isBulkUpdate = 0 BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
			DROP TABLE #tmpAuditLogMessages;
		CREATE TABLE #tmpAuditLogMessages (rowID INT IDENTITY(1,1), refID int, msg varchar(MAX), col varchar(max));
	END
	ELSE BEGIN
		-- ensure tmpAuditLogMessages exists
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NULL 
			RAISERROR('Designated result table does not exist.',16,1);

		TRUNCATE TABLE #tmpAuditLogMessages;
	END

	DECLARE @colNameList varchar(max), @updateColList varchar(max), @count int, 
		@dynSQL nvarchar(max), @output nvarchar(50), @delimiter varchar(2);

	-- ensure auditLogTable exists
	IF OBJECT_ID('tempdb..'+@auditLogTable) IS NULL 
		RAISERROR('Designated source table does not exist.',16,1);
	
	SET @dynSQL = N'SELECT @count=COUNT(*) FROM '+@auditLogTable;
	SET @outPut = N'@count int OUTPUT';
	EXEC sp_executesql @dynSQL, @outPut, @count=@count OUTPUT;

	IF (@isBulkUpdate = 0 AND @count <> 3) OR (@isBulkUpdate = 1 AND @count < 3)
		GOTO on_auditLog_done;

	-- cols
	SELECT @colNameList = COALESCE(@colNameList + ',','') + '[' + [name] + ']'
	FROM tempdb.sys.columns 
	WHERE object_id = object_id('tempdb..'+@auditLogTable)
	AND [name] NOT IN ('autoID','rowCode','refID')
	ORDER BY column_id;

	IF @colNameList IS NULL
		GOTO on_auditLog_done;

	-- update all cols datatype to varchar(max) to not cause conflicts with the type of other columns specified in the UNPIVOT list.
	SET @updateColList = NULL;

	SELECT @updateColList = COALESCE(@updateColList + '; ', '') + 'ALTER TABLE ' + @auditLogTable + ' ALTER COLUMN ' + tbl.listitem + ' varchar(max) NULL'
	FROM dbo.fn_varcharListToTableInline(@colNameList,',') as tbl;
	
	EXEC(@updateColList);

	-- columns with null values
	SET @updateColList = NULL;

	SELECT @updateColList = COALESCE(@updateColList + ', ', '') + tbl.listitem + ' = ISNULL(' + tbl.listitem + ','''')'
	FROM dbo.fn_varcharListToTableInline(@colNameList,',') as tbl;

	EXEC('UPDATE ' + @auditLogTable + ' SET ' +  @updateColList + ';');

	IF @isBulkUpdate = 1 BEGIN
		INSERT INTO #tmpAuditLogCol_oldVal (refID, col, oldValue)
		EXEC('SELECT refID, col, oldValue FROM (SELECT refID, '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''OLDVAL'') p
				UNPIVOT (oldValue FOR col IN('+ @colNameList +')) AS upvt');

		INSERT INTO #tmpAuditLogCol_newVal (refID, col, newValue)
		EXEC('SELECT refID, col, newValue FROM (SELECT refID, '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''NEWVAL'') p
				UNPIVOT (newValue FOR col IN('+ @colNameList +')) AS upvt');
	END
	ELSE BEGIN
		-- adding dummy value for refID as there will be only one entry in the source table
		INSERT INTO #tmpAuditLogCol_oldVal (refID, col, oldValue)
		EXEC('SELECT 0, col, oldValue FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''OLDVAL'') p
				UNPIVOT (oldValue FOR col IN('+ @colNameList +')) AS upvt');

		INSERT INTO #tmpAuditLogCol_newVal (refID, col, newValue)
		EXEC('SELECT 0, col, newValue FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''NEWVAL'') p
				UNPIVOT (newValue FOR col IN('+ @colNameList +')) AS upvt');
	END

	INSERT INTO #tmpAuditLogCol_dataTypeCode (col, dataTypeCode)
	EXEC('SELECT col, dataTypeCode FROM (SELECT '+ @colNameList +' FROM ' + @auditLogTable + ' WHERE rowCode = ''DATATYPECODE'') p
			UNPIVOT (dataTypeCode FOR col IN('+ @colNameList +')) AS upvt');

	-- handle NULL
	UPDATE #tmpAuditLogCol_oldVal SET oldValue = ISNULL(oldValue,'') WHERE oldValue IS NULL;
	UPDATE #tmpAuditLogCol_newVal SET newValue = ISNULL(newValue,'') WHERE newValue IS NULL;

	-- BIT
	UPDATE tmpOld 
	SET tmpOld.oldValue = CASE WHEN tmpOld.oldValue = '1' THEN 'Yes' ELSE 'No' END
	FROM #tmpAuditLogCol_oldVal AS tmpOld
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
	WHERE tmpDT.dataTypeCode = 'BIT'
	AND tmpOld.oldValue IN ('0','1');

	UPDATE tmpNew 
	SET tmpNew.newValue = CASE WHEN tmpNew.newValue = '1' THEN 'Yes' ELSE 'No' END
	FROM #tmpAuditLogCol_newVal AS tmpNew
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpNew.col
	WHERE tmpDT.dataTypeCode = 'BIT'
	AND tmpNew.newValue IN ('0','1');

	-- empty
	UPDATE #tmpAuditLogCol_oldVal SET oldValue = 'blank' WHERE oldValue = '';
	UPDATE #tmpAuditLogCol_newVal SET newValue = 'blank' WHERE newValue = '';

	-- escape special chars
	UPDATE tmp
	SET tmp.oldValue = STRING_ESCAPE(tmp.oldValue,'json')
	FROM #tmpAuditLogCol_oldVal AS tmp
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmp.col
	WHERE tmpDT.dataTypeCode <> 'CONTENTOBJ';

	UPDATE tmp
	SET tmp.newValue = STRING_ESCAPE(tmp.newValue,'json')
	FROM #tmpAuditLogCol_newVal AS tmp
	INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmp.col
	WHERE tmpDT.dataTypeCode <> 'CONTENTOBJ';

	-- auditLog msg
	IF @outputAsChangesArray = 1 BEGIN
		SET @delimiter = ',';

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, '{' +
				'"ITEM":"'+ tmpOld.col +'",' +
				'"NEWVALUE":"'+ tmpNew.newValue +'",' +
				'"OLDVALUE":"'+ tmpOld.oldValue +'"' +
			'}',
			tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode IN ('STRING','INTEGER','DECIMAL2','DATE','BIT');
	END
	ELSE BEGIN
		SET @delimiter = char(13) + char(10);

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, tmpOld.col + ' changed from [' + tmpOld.oldValue + '] to [' + tmpNew.newValue + '].', tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode IN ('STRING','INTEGER','DECIMAL2','DATE','BIT');

		INSERT INTO #tmpAuditLogMessages (refID, msg, col)
		SELECT tmpOld.refID, tmpOld.col + ' updated.', tmpOld.col
		FROM #tmpAuditLogCol_oldVal AS tmpOld
		INNER JOIN #tmpAuditLogCol_newVal AS tmpNew ON tmpNew.refID = tmpOld.refID
			AND tmpNew.col = tmpOld.col
		INNER JOIN #tmpAuditLogCol_dataTypeCode AS tmpDT ON tmpDT.col = tmpOld.col
		WHERE tmpOld.oldValue <> tmpNew.newValue
		AND tmpDT.dataTypeCode = 'CONTENTOBJ';
	END

	IF NOT EXISTS (SELECT 1 FROM #tmpAuditLogMessages)
		GOTO on_auditLog_done;

	SET @msg = NULL;

	IF @isBulkUpdate = 0 BEGIN
		SELECT @msg = COALESCE(@msg + @delimiter,'') + tmp.msg
		FROM #tmpAuditLogMessages AS tmp
		INNER JOIN tempdb.sys.columns AS tmpSys ON object_id = object_id('tempdb..'+@auditLogTable)
			AND tmpSys.[name] = tmp.col
		WHERE tmp.msg IS NOT NULL
		ORDER BY tmpSys.column_id;
	END

	on_auditLog_done:

	SET @msg = ISNULL(@msg,'');

	IF OBJECT_ID('tempdb..#tmpAuditLogCol_oldVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_oldVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_newVal') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_newVal;
	IF OBJECT_ID('tempdb..#tmpAuditLogCol_dataTypeCode') IS NOT NULL
		DROP TABLE #tmpAuditLogCol_dataTypeCode;
	IF @isBulkUpdate = 0 BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
			DROP TABLE #tmpAuditLogMessages;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ams_updateMemberDataColumn
@siteID int,
@columnID int,
@columnName varchar(128),
@columnDesc varchar(255),
@allowMultiple bit,
@allowNull bit,
@defaultValue varchar(max),
@allowNewValuesOnImport bit,
@dataTypeCode varchar(20),
@displayTypeCode varchar(20),
@isReadOnly bit,
@minChars int,
@maxChars int,
@minSelected int,
@maxSelected int,
@minValueInt int,
@maxValueInt int,
@minValueDecimal2 decimal(14,2),
@maxValueDecimal2 decimal(14,2),
@minValueDate date,
@maxValueDate date,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	CREATE TABLE #tmpAuditLogData (
		[rowCode] varchar(20) PRIMARY KEY, [Name] varchar(128), [Data Stored As] varchar(max), [Display Field As] varchar(max), [Description] varchar(max),
		[New values can be added during Member Import] varchar(max), [Allow null values or no selection for this field] varchar(max),
		[Default Value] varchar(max), [Prevent editing field values] varchar(max), [Members can have multiple values for this field] varchar(max),
		[Min Characters] varchar(max), [Max Characters] varchar(max), [Min Selectable Options] varchar(max), [Max Selectable Options] varchar(max),
		[Min Value Integer] varchar(max), [Max Value Integer] varchar(max), [Min Value Decimal] varchar(max), [Max Value Decimal] varchar(max),
		[Min Value Date] varchar(max), [Max Value Date] varchar(max)
	);

	DECLARE @valueID int, @orgID int, @displayTypeID int, @dataTypeID int, @newbitvalueID int, 
		@oldColumnName varchar(128), @olddataTypeCode varchar(20), @olddisplayTypeCode varchar(20), 
		@memberIDList varchar(max), @conditionIDList varchar(max), @msg varchar(max), @crlf varchar(2);

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	SET @crlf = char(13) + char(10);
	SELECT @oldColumnName = columnName from dbo.ams_memberDataColumns where columnID = @columnID;

	IF @columnID is null OR @columnName is null
		GOTO on_done;

	IF NOT EXISTS (SELECT 1 FROM dbo.ams_memberdataColumns where columnID = @columnID AND orgID = @orgID)
		RAISERROR('Invalid column.', 16, 1);

	-- new column name (if applicable) cannot be a reserved name
	IF (@columnName <> @oldColumnName AND (select dbo.fn_ams_isValidNewMemberViewColumn(@orgID, @columnName)) = 1)
		RAISERROR('That column name is invalid or already in use.', 16, 1);

	-- validate display type when multiple
	IF @allowMultiple = 1 and @displayTypeCode = 'RADIO'
		SET @displayTypeCode = 'CHECKBOX';
	IF @allowMultiple = 0 and @displayTypeCode = 'CHECKBOX'
		SET @displayTypeCode = 'RADIO';

	-- validations
	SELECT @olddataTypeCode = dt.dataTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = c.dataTypeID
		and c.columnID = @columnID;
	SELECT @olddisplayTypeCode = dt.displayTypeCode
		from dbo.ams_memberDataColumns as c
		inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = c.displayTypeID
		and c.columnID = @columnID;
	SELECT @displayTypeID = displayTypeID
		from dbo.ams_memberDataColumnDisplayTypes
		where displayTypeCode = @displayTypeCode;
	SELECT @dataTypeID = dataTypeID
		from dbo.ams_memberDataColumnDataTypes
		where dataTypeCode = @dataTypeCode;
	IF @displayTypeCode IN ('DOCUMENT','TEXTAREA','HTMLCONTENT') OR @dataTypeCode in ('CONTENTOBJ','DOCUMENTOBJ')
		SET @allowNull = 1;
	IF @allowNull = 0 and len(isnull(@defaultValue,'')) = 0
		SET @allowNull = 1;
	IF @allowNull = 1
		SET @defaultValue = '';

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date]
	)
	VALUES (
		'DATATYPECODE', 'STRING', 'STRING', 'STRING', 'CONTENTOBJ', 'BIT', 'BIT', 'STRING', 'BIT',
		'BIT', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'INTEGER', 'DECIMAL2',
		'DECIMAL2', 'DATE', 'DATE'
	);

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date]
	)
	SELECT 'OLDVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate
	FROM ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	BEGIN TRAN;
		-- set default valueID if necessary
		IF len(isnull(@defaultValue,'')) > 0 BEGIN
			EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@defaultValue, @valueID=@valueID OUTPUT;
				IF @valueID = 0 SET @allowNull = 1;
		END 

		-- update column info
		UPDATE dbo.ams_memberDataColumns 
		SET columnName = @columnName,
			columnDesc = @columnDesc,
			allowNull = @allowNull,
			defaultValueID = nullif(@valueID,0),
			allowNewValuesOnImport = @allowNewValuesOnImport,
			isReadOnly = @isReadOnly,
			allowMultiple = @allowMultiple,
			minChars = @minChars,
			maxChars = @maxChars,
			minSelected = @minSelected,
			maxSelected = @maxSelected,
			minValueInt = @minValueInt,
			maxValueInt = @maxValueInt,
			minValueDecimal2 = @minValueDecimal2,
			maxValueDecimal2 = @maxValueDecimal2,
			minValueDate = @minValueDate,
			maxValueDate = @maxValueDate
		WHERE columnID = @columnID;

		IF @displayTypeCode <> @olddisplayTypeCode OR @dataTypeCode <> @olddataTypeCode 
			UPDATE vgc
			SET processValuesSection = 
						case
						when vge.expression in ('eq','neq') and @dataTypeCode in ('STRING','DECIMAL2','DATE') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') then 1
						when vge.expression in ('eq','neq','lt','lte','gt','gte') and @dataTypeCode = 'INTEGER' then 1
						when vge.expression in ('datepart','datediff') then 1
						when vge.expression in ('eq','neq') and @dataTypeCode = 'STRING' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 2
						when vge.expression in ('lt','lte','gt','gte','contains','contains_regex') and @dataTypeCode = 'STRING' then 2
						when vge.expression in ('eq','neq') and @dataTypeCode = 'BIT' then 3
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DECIMAL2' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 4
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DECIMAL2' then 4
						when vge.expression in ('eq','neq') and @dataTypeCode = 'DATE' and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') then 5
						when vge.expression in ('lt','lte','gt','gte') and @dataTypeCode = 'DATE' then 5
						else null end
			FROM dbo.ams_virtualGroupConditions as vgc
			INNER JOIN dbo.ams_virtualGroupExpressions as vge on vge.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10));

		-- if changing the display type
		IF @displayTypeCode <> @olddisplayTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET displayTypeID = @displayTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- if was a radio/select/checkbox (not bit) and is no longer that, we need to convert valueID to value
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode not in ('RADIO','SELECT','CHECKBOX') BEGIN
				UPDATE vgcv
				SET vgcv.conditionValue = coalesce(mdcv.columnValueString, cast(mdcv.columnValueDecimal2 as varchar(15)), cast(mdcv.columnValueInteger as varchar(15)), convert(varchar(10),mdcv.columnvalueDate,101))
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			-- if was NOT a radio/select/checkbox (not bit) and is now that, we need to convert value to valueID
			IF @olddataTypeCode <> 'BIT' and @dataTypeCode <> 'BIT' and @olddisplayTypeCode not in ('RADIO','SELECT','CHECKBOX') and @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
				
				-- err if any expressions are not 1,2,7,8 now that it will be a select
				IF EXISTS (select conditionID from dbo.ams_virtualGroupConditions where fieldCode = 'md_' + cast(@columnID as varchar(10)) and expressionID not in (1,2,7,8))
					RAISERROR('There are group assignment conditions that are not compatible with the selected display type.', 16, 1);

				-- create column values for those condition values that dont yet exist as column values
				declare @tblVals TABLE (newVal varchar(max));
				insert into @tblVals (newVal)
				select vgcv.conditionValue
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 1
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueString = vgcv.conditionValue)
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 2
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDecimal2 = cast(vgcv.conditionValue as decimal(14,2)))
					union
				select cast(vgcv.conditionValue as varchar(15))
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 3
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueInteger = cast(vgcv.conditionValue as int))
					union
				select convert(varchar(10),vgcv.conditionValue,101)
				from dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.dataTypeID = 4
				and vgc.expressionID in (1,2)
				and not exists (select valueID from dbo.ams_memberDataColumnValues where columnID = @columnID and columnvalueDate = cast(vgcv.conditionValue as date));

				DECLARE @newvalueID int, @minValue varchar(max);
				select @minValue = min(newVal) from @tblVals;
				while @minValue is not null BEGIN
					EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue=@minValue, @valueID=@newvalueID OUTPUT;
					select @minValue = min(newVal) from @tblVals where newVal > @minValue;
				END

				-- get the valueID
				UPDATE vgcv
				SET vgcv.conditionValue = tmp.valueID
				FROM dbo.ams_virtualGroupConditions as vgc
				INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
				INNER JOIN (
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 1 
					and vgcv.conditionValue = mdcv.columnvalueString
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 2 
					and cast(vgcv.conditionValue as decimal(14,2)) = mdcv.columnvalueDecimal2
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 3 
					and cast(vgcv.conditionValue as int) = mdcv.columnvalueInteger
						union
					select vgc.conditionID, mdcv.valueID
					from dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
					where vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2)
					and vgc.dataTypeID = 4 
					and cast(vgcv.conditionValue as date) = mdcv.columnvalueDate
				) as tmp on tmp.conditionID = vgc.conditionID
				WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
				and vgc.expressionID in (1,2);
			END

			UPDATE dbo.ams_virtualGroupConditions
			SET displayTypeID = @displayTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE dbo.ams_virtualGroupConditions
			set [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END

		-- if changing the data type
		IF @dataTypeCode <> @olddataTypeCode BEGIN
			UPDATE dbo.ams_memberDataColumns
			SET dataTypeID = @dataTypeID
			WHERE columnID = @columnID;

			UPDATE dbo.ams_memberFields
			SET dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- check ams_virtualGroupConditions for expression conflicts
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueString as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Decimal Number (2) data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueString as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Whole Number data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'DATE' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDate = cast(columnValueString as date)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Date data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Date data type.', 16, 1);
			END
			IF @olddataTypeCode = 'STRING' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueString as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are string values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueString = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6,9,10)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DECIMAL2' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueDecimal2 as varchar(255))
					where columnID = @columnID
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are decimal values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDecimal2 = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueInteger as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueInteger as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;
			END
			IF @olddataTypeCode = 'INTEGER' AND @dataTypeCode = 'BIT' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueBit = cast(columnValueInteger as bit)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are whole number values not compatible with the Boolean data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueInteger = null
				where columnID = @columnID;

				-- ensure both bit values are there					
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='1', @valueID=@newbitvalueID OUTPUT;
				EXEC dbo.ams_createMemberDataColumnValue @columnID=@columnID, @columnValue='0', @valueID=@newbitvalueID OUTPUT;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (3,4,5,6)
				) RAISERROR('There are group assignment conditions that are not compatible with the Boolean data type.', 16, 1);

				-- if was a radio/select/checkbox, we need to convert valueID to value because BIT doesnt store valueID
				IF @olddisplayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.columnValueBit
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.valueID as varchar(10)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'DATE' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = convert(varchar(10),columnValueDate,101)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are date values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueDate = null
				where columnID = @columnID;

				IF EXISTS (
					select conditionID
					from dbo.ams_virtualGroupConditions
					where fieldCode = 'md_' + cast(@columnID as varchar(10))
					and expressionID in (11,12)
				) RAISERROR('There are group assignment conditions that are not compatible with the Text String data type.', 16, 1);
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'STRING' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueString = cast(columnValueBit as varchar(255))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Text String data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueString = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'DECIMAL2' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueDecimal2 = cast(columnValueBit as decimal(14,2))
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Decimal Number (2) data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and mdcv.columnvalueDecimal2 = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END
			IF @olddataTypeCode = 'BIT' AND @dataTypeCode = 'INTEGER' BEGIN
				BEGIN TRY
					SET XACT_ABORT OFF;
					UPDATE dbo.ams_memberDataColumnValues
					SET columnValueInteger = cast(columnValueBit as int)
					where columnID = @columnID;
					SET XACT_ABORT ON;
				END TRY
				BEGIN CATCH
					SET XACT_ABORT ON;
					RAISERROR('There are boolean values not compatible with the Whole Number data type.', 16, 1);
				END CATCH					

				UPDATE dbo.ams_memberDataColumnValues
				SET columnValueBit = null
				where columnID = @columnID;

				-- if going to be radio/select/checkbox, we need to convert value to valueID because BIT doesnt store valueID
				IF @displayTypeCode in ('RADIO','SELECT','CHECKBOX') BEGIN
					UPDATE vgcv
					SET vgcv.conditionValue = mdcv.valueID
					FROM dbo.ams_virtualGroupConditions as vgc
					INNER JOIN dbo.ams_virtualGroupConditionValues as vgcv on vgcv.conditionID = vgc.conditionID
					inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @columnID
						and cast(mdcv.columnvalueInteger as varchar(15)) = vgcv.conditionValue
					WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
					and vgc.expressionID in (1,2);
				END
			END

			UPDATE dbo.ams_virtualGroupConditions
			set dataTypeID = @dataTypeID
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
			CREATE TABLE #tmpAffectedConditions (orgID int, conditionID int);

			UPDATE vgc
			SET vgc.subProc = CASE WHEN e.expression='datediff' then 'MD_DATEDIFF'
								WHEN e.expression='datepart' then 'MD_DATEPART'
								WHEN e.expression='contains' and @dataTypeCode='STRING' then 'MD_CONTAINS_STRING'
								WHEN e.expression='contains_regex' and @dataTypeCode='STRING' then 'MD_CONTAINSREGEX_STRING'
								WHEN e.expression='eq' and @dataTypeCode='STRING' then 'MD_EQ_STRING'
								WHEN e.expression='eq' and @dataTypeCode='BIT' then 'MD_EQ_BIT'
								WHEN e.expression='eq' and @dataTypeCode='INTEGER' then 'MD_EQ_INTEGER'
								WHEN e.expression='eq' and @dataTypeCode='DECIMAL2' then 'MD_EQ_DECIMAL2'
								WHEN e.expression='eq' and @dataTypeCode='DATE' then 'MD_EQ_DATE'
								WHEN e.expression='exists' and @dataTypeCode='STRING' then 'MD_EXISTS_STRING'
								WHEN e.expression='exists' and @dataTypeCode='BIT' then 'MD_EXISTS_BIT'
								WHEN e.expression='exists' and @dataTypeCode='INTEGER' then 'MD_EXISTS_INTEGER'
								WHEN e.expression='exists' and @dataTypeCode='DECIMAL2' then 'MD_EXISTS_DECIMAL2'
								WHEN e.expression='exists' and @dataTypeCode='DATE' then 'MD_EXISTS_DATE'
								WHEN e.expression='exists' and @dataTypeCode='CONTENTOBJ' then 'MD_EXISTS_CONTENTOBJ'
								WHEN e.expression='exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_EXISTS_DOCUMENTOBJ'
								WHEN e.expression='gt' and @dataTypeCode='STRING' then 'MD_GT_STRING'
								WHEN e.expression='gt' and @dataTypeCode='INTEGER' then 'MD_GT_INTEGER'
								WHEN e.expression='gt' and @dataTypeCode='DECIMAL2' then 'MD_GT_DECIMAL2'
								WHEN e.expression='gt' and @dataTypeCode='DATE' then 'MD_GT_DATE'
								WHEN e.expression='gte' and @dataTypeCode='STRING' then 'MD_GTE_STRING'
								WHEN e.expression='gte' and @dataTypeCode='INTEGER' then 'MD_GTE_INTEGER'
								WHEN e.expression='gte' and @dataTypeCode='DECIMAL2' then 'MD_GTE_DECIMAL2'
								WHEN e.expression='gte' and @dataTypeCode='DATE' then 'MD_GTE_DATE'
								WHEN e.expression='lt' and @dataTypeCode='STRING' then 'MD_LT_STRING'
								WHEN e.expression='lt' and @dataTypeCode='INTEGER' then 'MD_LT_INTEGER'
								WHEN e.expression='lt' and @dataTypeCode='DECIMAL2' then 'MD_LT_DECIMAL2'
								WHEN e.expression='lt' and @dataTypeCode='DATE' then 'MD_LT_DATE'
								WHEN e.expression='lte' and @dataTypeCode='STRING' then 'MD_LTE_STRING'
								WHEN e.expression='lte' and @dataTypeCode='INTEGER' then 'MD_LTE_INTEGER'
								WHEN e.expression='lte' and @dataTypeCode='DECIMAL2' then 'MD_LTE_DECIMAL2'
								WHEN e.expression='lte' and @dataTypeCode='DATE' then 'MD_LTE_DATE'
								WHEN e.expression='neq' and @dataTypeCode='STRING' then 'MD_NEQ_STRING'
								WHEN e.expression='neq' and @dataTypeCode='BIT' then 'MD_NEQ_BIT'
								WHEN e.expression='neq' and @dataTypeCode='INTEGER' then 'MD_NEQ_INTEGER'
								WHEN e.expression='neq' and @dataTypeCode='DECIMAL2' then 'MD_NEQ_DECIMAL2'
								WHEN e.expression='neq' and @dataTypeCode='DATE' then 'MD_NEQ_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='STRING' then 'MD_NOTEXISTS_STRING'
								WHEN e.expression='not_exists' and @dataTypeCode='INTEGER' then 'MD_NOTEXISTS_INTEGER'
								WHEN e.expression='not_exists' and @dataTypeCode='DECIMAL2' then 'MD_NOTEXISTS_DECIMAL2'
								WHEN e.expression='not_exists' and @dataTypeCode='DATE' then 'MD_NOTEXISTS_DATE'
								WHEN e.expression='not_exists' and @dataTypeCode='BIT' then 'MD_NOTEXISTS_BIT'
								WHEN e.expression='not_exists' and @dataTypeCode='CONTENTOBJ' then 'MD_NOTEXISTS_CONTENTOBJ'
								WHEN e.expression='not_exists' and @dataTypeCode='DOCUMENTOBJ' then 'MD_NOTEXISTS_DOCUMENTOBJ'
							END
				OUTPUT INSERTED.orgID, INSERTED.conditionID INTO #tmpAffectedConditions (orgID, conditionID)
			FROM dbo.ams_virtualGroupConditions AS vgc
			INNER JOIN dbo.ams_virtualGroupExpressions AS e ON e.expressionID = vgc.expressionID
			WHERE vgc.fieldCode = 'md_' + cast(@columnID as varchar(10))
			AND vgc.orgID = @orgID;

			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- reprocess conditions based on field
			IF EXISTS (SELECT 1 FROM #tmpAffectedConditions) BEGIN
				INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
				SELECT orgID, NULL, conditionID
				FROM #tmpAffectedConditions;

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				TRUNCATE TABLE #tblMCQRun;
			END

			IF OBJECT_ID('tempdb..#tmpAffectedConditions') IS NOT NULL 
				DROP TABLE #tmpAffectedConditions;
		END

		-- if valueID is not null, there is a def value. 
		-- Anyone who doesnt have a value for this column needs this value.
		IF nullif(@valueID,0) is not null BEGIN
			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF;
			CREATE TABLE #tblMDDEF (memberID int PRIMARY KEY);

			insert into #tblMDDEF (memberID)
			select distinct m.memberid
			from dbo.ams_members as m
			where m.orgID = @orgID
			and m.memberid = m.activememberid
			and m.status <> 'D'
				except
			select distinct md.memberID
			from dbo.ams_memberData as md 
			inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
			where mdcv.columnID = @columnID;

			INSERT INTO dbo.ams_memberData (memberid, valueID)
			select memberid, @valueID
			from #tblMDDEF;

			UPDATE m
			SET m.dateLastUpdated = getdate()
			FROM dbo.ams_members as m
			INNER JOIN #tblMDDEF as tmp on tmp.memberID = m.memberID;


			-- reprocess conditions based on field
			INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
			SELECT distinct c.orgID, m.memberID, c.conditionID
			from dbo.ams_virtualGroupConditions as C
			cross join #tblMDDEF as m
			where c.orgID = @orgID
			and C.fieldcode = 'md_' + Cast(@columnID as varchar(10));

			EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

			TRUNCATE TABLE #tblMCQRun;

			IF OBJECT_ID('tempdb..#tblMDDEF') IS NOT NULL 
				DROP TABLE #tblMDDEF
		END

		
		-- check custom field validation ranges against existing data
		IF @minChars is not null and @maxChars is not null BEGIN
			IF @dataTypeCode = 'STRING' BEGIN
				IF EXISTS(
					select top 1 valueID
					from dbo.ams_memberdatacolumnValues
					where columnID = @columnID
					and len(columnValueString) > 0
					and len(columnValueString) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
			IF @dataTypeCode = 'CONTENTOBJ' BEGIN
				IF EXISTS(
					select top 1 mdcv.valueID
					from dbo.ams_memberdatacolumnValues as mdcv
					inner join dbo.cms_content as c on c.siteResourceID = mdcv.columnValueSiteResourceID
					inner join dbo.cms_contentLanguages as cl ON cl.contentID = c.contentID and cl.languageID = 1
					inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
					where mdcv.columnID = @columnID
					and len(cv.rawContent) > 0
					and len(cv.rawContent) not between @minChars and @maxChars
				)
				RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
			END
		END
		IF @minSelected is not null and @maxSelected is not null BEGIN
			IF EXISTS(select columnID from dbo.ams_memberDataColumns where columnID = @columnID and allowMultiple = 1)
			AND EXISTS(
				select top 1 md.memberid
				from dbo.ams_memberData as md
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
				where mdcv.columnID = @columnID
				group by md.memberid
				having count(*) not between @minSelected and @maxSelected
			)
			RAISERROR('There are existing members that have field options that are outside the data validation range.', 16, 1);
		END
		IF @minValueInt is not null and @maxValueInt is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueInteger is not null
				and columnValueInteger not between @minValueInt and @maxValueInt
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDecimal2 is not null and @maxValueDecimal2 is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDecimal2 is not null
				and columnValueDecimal2 not between @minValueDecimal2 and @maxValueDecimal2
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END
		IF @minValueDate is not null and @maxValueDate is not null BEGIN
			IF EXISTS(
				select top 1 valueID
				from dbo.ams_memberdatacolumnValues
				where columnID = @columnID
				and columnValueDate is not null
				and columnValueDate not between @minValueDate and @maxValueDate
			)
			RAISERROR('There are existing values for this column that are outside the data validation range.', 16, 1);
		END


		-- if there was a change in columnname
		IF @oldColumnName <> @columnName COLLATE Latin1_General_CS_AI BEGIN
			-- update member fields
			UPDATE dbo.ams_memberFields
			SET dbField = @columnName
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));

			-- update saved query fields
			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedQueriesFields AS qf
			INNER JOIN dbo.ams_savedQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));

			UPDATE qf
			SET qf.columnName = @columnName
			FROM dbo.ams_savedLinkedRecordsQueriesFields AS qf
			INNER JOIN dbo.ams_savedLinkedRecordsQueries AS q ON q.queryID = qf.queryID
			INNER JOIN dbo.sites AS s ON s.orgID = @orgID
				AND s.siteID = q.siteID
			WHERE qf.fieldCode = 'md_' + cast(@columnID as varchar(10));
		
			-- update virtual group conditions
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE fieldCode = 'md_' + cast(@columnID as varchar(10));
		END
		
	COMMIT TRAN;

	-- new data
	INSERT INTO #tmpAuditLogData (
		[rowCode], [Name], [Data Stored As], [Display Field As], [Description],
		[New values can be added during Member Import], [Allow null values or no selection for this field],
		[Default Value], [Prevent editing field values], [Members can have multiple values for this field],
		[Min Characters], [Max Characters], [Min Selectable Options], [Max Selectable Options],
		[Min Value Integer], [Max Value Integer], [Min Value Decimal], [Max Value Decimal],
		[Min Value Date], [Max Value Date]
	)
	SELECT 'NEWVAL', c.columnName, dt.dataType, ds.displayType, c.ColumnDesc,
		c.allowNewValuesOnImport, c.allowNull,
		CASE dt.dataTypeCode
			WHEN 'STRING' THEN defV.columnValueString
			WHEN 'DECIMAL2' THEN convert(varchar(255), defV.columnValueDecimal2)
			WHEN 'INTEGER' THEN convert(varchar(255), defV.columnValueInteger)
			WHEN 'DATE' THEN convert(varchar(10), defV.columnValueDate, 101)
			WHEN 'BIT' THEN convert(varchar(255), defV.columnValueBit)
			ELSE NULL
		END AS defaultValue,
		c.isReadOnly, c.allowMultiple,
		c.minChars, c.maxChars, c.minSelected, c.maxSelected, c.minValueInt, c.maxValueInt,
		c.minValueDecimal2, c.maxValueDecimal2, c.minValueDate, c.maxValueDate
	FROM ams_memberDataColumns AS c
	INNER JOIN dbo.ams_memberDataColumnDataTypes AS dt ON dt.dataTypeID = c.dataTypeID
	INNER JOIN dbo.ams_memberDataColumnDisplayTypes AS ds ON ds.displayTypeID = c.displayTypeID
	LEFT OUTER JOIN dbo.ams_memberDataColumnValues AS defV ON defV.valueID = c.defaultValueID
	WHERE c.orgID = @orgID
	AND c.columnID = @columnID;

	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

	-- audit log
	IF ISNULL(@msg,'') <> '' BEGIN
		SET @msg = 'Custom Field ' + STRING_ESCAPE(QUOTENAME(@oldColumnName),'json') + ' has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		VALUES('{ "c":"auditLog", "d": {
			"AUDITCODE":"MEMCF",
			"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
			"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
			"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
			"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
			"MESSAGE":"' + @msg + '" } }');
	END

	-- recreate the view and cache tables	
	EXEC dbo.ams_createVWMemberData	@orgID=@orgID;

	-- if this is a date field that is changing its name, check for subscription member date rules we may need to update
	IF @oldColumnName <> @columnName AND @dataTypeCode = 'DATE' BEGIN
	
		declare @tblJD TABLE (udid int PRIMARY KEY);
		insert into @tblJD (udid)
		select distinct udid 
		from customapps.dbo.schedTask_memberJoinDates as jd
		inner join membercentral.dbo.sites as s on s.siteCode = jd.siteCode
		where s.orgID = @orgID
		and ( joinDateFieldName = @oldColumnName 
			or rejoinDateFieldName = @oldColumnName 
			or droppedDateFieldName = @oldColumnName 
			or paidThruDateFieldName = @oldColumnName
			or renewalDateFieldName = @oldColumnName )

		IF @@ROWCOUNT = 0 GOTO on_done;

		UPDATE jd
		SET jd.joinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.joinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.rejoinDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.rejoinDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.droppedDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.droppedDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.paidThruDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.paidThruDateFieldName = @oldColumnName;

		UPDATE jd
		SET jd.renewalDateFieldName = @columnName
		FROM customapps.dbo.schedTask_memberJoinDates as jd
		INNER JOIN @tblJD as tmp on tmp.udid = jd.udid
		WHERE jd.renewalDateFieldName = @oldColumnName;
	END

	on_done:
	
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.cf_auditLogAddField
@fieldID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @siteID int, @orgID int, @usageID int, @controllingSiteResourceID int,@controllingResourceType varchar(50),
		@areaName varchar(20), @resourceType varchar(50), @fieldText varchar(max), @isRequired bit,
		@msgjson varchar(max), @crlf varchar(10);

	SET @crlf = char(13) + char(10);

	IF @enteredByMemberID IS NULL
		SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

	SELECT @siteID = s.siteID, @orgID = s.orgID, @controllingSiteResourceID = f.controllingSiteResourceID,
		@controllingResourceType = rt.resourceType, @usageID = f.usageID, @fieldText = f.fieldText,
		@isRequired = f.isRequired
	FROM dbo.cf_fields AS f
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = f.controllingSiteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = sr.resourceTypeID
	INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
	WHERE f.fieldID = @fieldID;

	IF @usageID IS NULL
		GOTO on_done;

	SELECT @areaName = fu.areaName, @resourceType = rt.resourceType
	FROM dbo.cf_fieldUsages AS fu
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = fu.siteResourceTypeID
	WHERE fu.usageID = @usageID;

	IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
		IF @controllingResourceType = 'ReferralPanel'
			SELECT @msgjson = 'New Subpanel Question [' + @fieldText + '] has been added for Panel [' + [name] + '].'
			FROM dbo.ref_panels
			WHERE siteResourceID = @controllingSiteResourceID;
		ELSE	
			SET @msgjson = 'New Question [' + @fieldText + '] has been added for Panel Selections.';

		SET @msgjson = @msgjson + CASE @isRequired WHEN 1 THEN @crlf + 'Is Required?: Yes' ELSE '' END;

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
			@enteredByMemberID=@enteredByMemberID;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.cf_auditLogAddFieldValue
@valueID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @siteID int, @orgID int, @usageID int, @controllingSiteResourceID int,@controllingResourceType varchar(50),
		@areaName varchar(20), @resourceType varchar(50), @fieldText varchar(max), @valueString varchar(max),
		@msgjson varchar(max), @crlf varchar(10);

	SET @crlf = char(13) + char(10);

	IF @enteredByMemberID IS NULL
		SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

	SELECT @siteID = s.siteID, @orgID = s.orgID, @controllingSiteResourceID = f.controllingSiteResourceID,
		@controllingResourceType = rt.resourceType, @usageID = f.usageID, @fieldText = f.fieldText,
		@valueString = v.valueString
	FROM dbo.cf_fieldValues AS v
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = v.fieldID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = f.controllingSiteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = sr.resourceTypeID
	INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
	WHERE v.valueID = @valueID;

	IF @usageID IS NULL
		GOTO on_done;

	SELECT @areaName = fu.areaName, @resourceType = rt.resourceType
	FROM dbo.cf_fieldUsages AS fu
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = fu.siteResourceTypeID
	WHERE fu.usageID = @usageID;

	IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
		IF @controllingResourceType = 'ReferralPanel'
			SELECT @msgjson = 'New Answer [' + @valueString + '] has been added to the Subpanel Question within Panel [' + [name] + '].'
			FROM dbo.ref_panels
			WHERE siteResourceID = @controllingSiteResourceID;
		ELSE	
			SET @msgjson = 'New Answer [' + @valueString + '] has been added to the Panel Selections Question.';

		SET @msgjson = @msgjson + @crlf + 'Related Question: [' + @fieldText + ']';

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
			@enteredByMemberID=@enteredByMemberID;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.cf_auditLogDeleteField
@fieldID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @siteID int, @orgID int, @usageID int, @controllingSiteResourceID int,@controllingResourceType varchar(50),
		@areaName varchar(20), @resourceType varchar(50), @fieldText varchar(max), @msgjson varchar(max);

	IF @enteredByMemberID IS NULL
		SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

	SELECT @siteID = s.siteID, @orgID = s.orgID, @controllingSiteResourceID = f.controllingSiteResourceID,
		@controllingResourceType = rt.resourceType, @usageID = f.usageID, @fieldText = f.fieldText
	FROM dbo.cf_fields AS f
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = f.controllingSiteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = sr.resourceTypeID
	INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
	WHERE f.fieldID = @fieldID;
	
	IF @usageID IS NULL
		GOTO on_done;

	SELECT @areaName = fu.areaName, @resourceType = rt.resourceType
	FROM dbo.cf_fieldUsages AS fu
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = fu.siteResourceTypeID
	WHERE fu.usageID = @usageID;

	IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
		IF @controllingResourceType = 'ReferralPanel'
			SELECT @msgjson = 'Subpanel Question [' + @fieldText + '] has been deleted from Panel [' + [name] + '].'
			FROM dbo.ref_panels
			WHERE siteResourceID = @controllingSiteResourceID;
		ELSE	
			SET @msgjson = 'Question [' + @fieldText + '] has been deleted from Panel Selections.';

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
			@enteredByMemberID=@enteredByMemberID;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.cf_auditLogDeleteFieldValue
@valueID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @siteID int, @orgID int, @usageID int, @controllingSiteResourceID int,@controllingResourceType varchar(50),
		@areaName varchar(20), @resourceType varchar(50), @fieldText varchar(max), @valueString varchar(max),
		@msgjson varchar(max), @crlf varchar(10);

	SET @crlf = char(13) + char(10);

	IF @enteredByMemberID IS NULL
		SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

	SELECT @siteID = s.siteID, @orgID = s.orgID, @controllingSiteResourceID = f.controllingSiteResourceID,
		@controllingResourceType = rt.resourceType, @usageID = f.usageID, @fieldText = f.fieldText,
		@valueString = v.valueString
	FROM dbo.cf_fieldValues AS v
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = v.fieldID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = f.controllingSiteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = sr.resourceTypeID
	INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
	WHERE v.valueID = @valueID;

	IF @usageID IS NULL
		GOTO on_done;

	SELECT @areaName = fu.areaName, @resourceType = rt.resourceType
	FROM dbo.cf_fieldUsages AS fu
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = fu.siteResourceTypeID
	WHERE fu.usageID = @usageID;

	IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
		IF @controllingResourceType = 'ReferralPanel'
			SELECT @msgjson = 'Removed Answer [' + @valueString + '] from Subpanel Question within Panel [' + [name] + '].'
			FROM dbo.ref_panels
			WHERE siteResourceID = @controllingSiteResourceID;
		ELSE	
			SET @msgjson = 'Removed Answer [' + @valueString + '] from Panel Selections Question';

		SET @msgjson = @msgjson + @crlf + 'Related Question: [' + @fieldText + ']';

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
			@enteredByMemberID=@enteredByMemberID;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.ref_createAgency
@referralID int,
@name varchar(255) = NULL,
@importCode varchar(75) = NULL,
@description varchar(255) = NULL,
@isActive bit = NULL,
@address1 varchar(255) = NULL,
@address2 varchar(255) = NULL,
@address3 varchar(255) = NULL,
@city varchar(100) = NULL,
@stateID int = NULL,
@postalCode varchar(25) = NULL,
@phone varchar(40) = NULL,
@alternatePhone varchar(40) = NULL,
@email varchar(255) = NULL,
@website varchar(255) = NULL,
@createdBy int,
@agencyID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @stateName varchar(40), @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	IF @stateID IS NOT NULL
		SELECT @stateName = name FROM dbo.ams_states WHERE stateID = @stateID;

	INSERT INTO dbo.ref_agencies(referralID, [name], importCode, [description], isActive, createdBy, address1,
		address2, address3, city, [state], postalCode, phone, alternatePhone, email, website, dateCreated)
	VALUES (@referralID, @name, NULLIF(@importCode,''), NULLIF(@description,''), NULLIF(@isActive,0), @createdBy,
		NULLIF(@address1,''), NULLIF(@address2,''), NULLIF(@address3,''), NULLIF(@city,''), NULLIF(@stateID,0),
		NULLIF(@postalCode,''), NULLIF(@phone,''), NULLIF(@alternatePhone,''), NULLIF(@email,''), NULLIF(@website,''),
		GETDATE());

	SELECT @agencyID = SCOPE_IDENTITY();
					
	SET @msgjson = 'New Agency [' + @name + '] has been created.'
		+ CASE WHEN NULLIF(@importCode,'') IS NOT NULL THEN @crlf + 'Import Code: ' + @importCode ELSE '' END
		+ CASE WHEN NULLIF(@address1,'') IS NOT NULL THEN @crlf + 'Address 1: ' + @address1 ELSE '' END
		+ CASE WHEN NULLIF(@address2,'') IS NOT NULL THEN @crlf + 'Address 2: ' + @address2 ELSE '' END
		+ CASE WHEN NULLIF(@address3,'') IS NOT NULL THEN @crlf + 'Address 3: ' + @address3 ELSE '' END
		+ CASE WHEN NULLIF(@city,'') IS NOT NULL THEN @crlf + 'City: ' + @city ELSE '' END
		+ CASE WHEN NULLIF(@stateName,'') IS NOT NULL THEN @crlf + 'State: ' + @stateName ELSE '' END
		+ CASE WHEN NULLIF(@postalCode,'') IS NOT NULL THEN @crlf + 'Postal Code: ' + @postalCode ELSE '' END
		+ CASE WHEN NULLIF(@email,'') IS NOT NULL THEN @crlf + 'Email: ' + @email ELSE '' END
		+ CASE WHEN NULLIF(@phone,'') IS NOT NULL THEN @crlf + 'Phone: ' + @phone ELSE '' END
		+ CASE WHEN NULLIF(@alternatePhone,'') IS NOT NULL THEN @crlf + 'Alternate Phone: ' + @alternatePhone ELSE '' END
		+ CASE WHEN NULLIF(@website,'') IS NOT NULL THEN @crlf + 'Website: ' + @website ELSE '' END
		+ @crlf + 'Is Active?: ' + CASE @isActive WHEN 1 THEN 'Yes' ELSE 'No' END;

	SET @msgjson = STRING_ESCAPE(@msgjson,'json');

	EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='AGENCIES', @msgjson=@msgjson, @enteredByMemberID=@createdBy;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_createClientReferral
@referralID int,
@clientID int,
@representativeID int = NULL,
@memberID int = NULL,
@enteredByMemberID int,
@sourceID int,
@otherSource varchar(100) = NULL,
@communicateLanguageID int,
@issueDesc varchar(max),
@agencyID int = NULL,
@sendSurvey	bit = NULL,
@statusID int,
@typeID	int,
@sendNewsBlog bit = NULL,
@panelID int = NULL,
@clientReferralID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @clientReferralDate datetime, @nowDate datetime = getDate(),
		@isReferred bit, @thisUUID varchar(36), @crlf varchar(10), @message varchar(max),
		@statusName varchar(255), @uniqueCode char(8);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	SELECT @isReferred = isReferred, @statusName = statusName
	FROM dbo.ref_clientReferralStatus
	WHERE clientReferralStatusID = @statusID;
		
	IF @isReferred = 1
		SET @clientReferralDate = @nowDate;

	SET @thisUUID = cast(newid() as varchar(36));
	SET @thisUUID = left(@thisUUID, 23) + right(@thisUUID,12);

	EXEC dbo.getUniqueCode @uniqueCode=@uniqueCode OUTPUT;

	INSERT INTO dbo.ref_clientReferrals (clientID, representativeID, memberID, enteredByMemberID, sourceID, otherSource, communicateLanguageID, issueDesc,
		agencyID, sendSurvey, statusID, typeID, clientReferralDate, dateCreated, sendNewsBlog, callUID, panelID, referralID,referralCode, [uid])
	VALUES (@clientID, @representativeID, @memberID, @enteredByMemberID, @sourceID, @otherSource, @communicateLanguageID, @issueDesc,
		@agencyID, @sendSurvey, @statusID, @typeID, @clientReferralDate, @nowDate, @sendNewsBlog, @thisUUID, @panelID, @referralID,@uniqueCode, NEWID());

	SELECT @clientReferralID = SCOPE_IDENTITY();

	SELECT @message = 'Referral Created'
		+ @crlf + 'Client: ' + c.firstName + ' ' + c.lastName
		+ CASE WHEN rep.clientID IS NOT NULL THEN @crlf + 'Representative: ' + rep.firstName + ' ' + rep.lastName ELSE '' END
		+ CASE WHEN s.clientReferralSourceID IS NOT NULL THEN @crlf + 'Source: ' + s.clientReferralSource ELSE '' END
		+ CASE WHEN m.memberID IS NOT NULL THEN @crlf + 'Counselor Name: ' + m.firstName + ' ' + m.lastName ELSE '' END
		+ CASE WHEN t.clientReferralTypeID IS NOT NULL THEN @crlf + 'Call Type: ' + t.clientReferralType ELSE '' END
		+ CASE WHEN a.agencyID IS NOT NULL THEN @crlf + 'Agency: ' + a.[name] ELSE '' END
		+ CASE WHEN s.clientReferralSourceID IS NOT NULL THEN @crlf + 'Source: ' + s.clientReferralSource ELSE '' END
		+ CASE WHEN @clientReferralDate IS NOT NULL THEN @crlf + 'Referral Date: ' + convert(varchar(20),@clientReferralDate,120) ELSE '' END
		+ @crlf + 'Referral Status: ' + @statusName
		+ @crlf + 'Referral Code: ' + @uniqueCode
	FROM dbo.ref_clientReferrals AS cr
	INNER JOIN dbo.ref_clients AS c ON c.clientID = cr.clientID
	LEFT OUTER JOIN dbo.ref_clients AS rep ON rep.clientID = cr.representativeID
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS s ON s.clientReferralSourceID = cr.sourceID
	LEFT OUTER JOIN dbo.ams_members AS m ON m.memberID = cr.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS t ON t.clientReferralTypeID = cr.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.agencyID = cr.agencyID
	WHERE cr.clientReferralID = @clientReferralID;

	SET @message = STRING_ESCAPE(@message,'json');

	EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
		@message=@message, @enteredByMemberID=@enteredByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ref_createNote
@referralNote varchar(max),
@noteType char(1),
@clientReferralID int,
@createdDate datetime,
@createdBy int,
@followUpDate datetime,
@followUpStatus char(1),
@referralNoteID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @crlf varchar(10), @message varchar(max);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_clientReferrals AS cr
	INNER JOIN dbo.ref_referrals AS r ON r.referralID = cr.referralID
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE cr.clientReferralID = @clientReferralID;

	INSERT INTO dbo.ref_notes(referralNote, noteType, clientReferralID, createdDate, createdBy, followUpDate, followUpStatus)
	VALUES (@referralNote, @noteType, @clientReferralID, @createdDate, @createdBy, @followUpDate, @followUpStatus);

	SELECT @referralNoteID = SCOPE_IDENTITY();

	SET @message = 'New ' + CASE @noteType WHEN 'C' THEN 'Counselor Note' ELSE 'Attorney Note' END + ' has been added.'
		+ @crlf + 'Date Created: ' + convert(varchar(20),@createdDate,120)
		+ @crlf + 'Description: ' + CASE WHEN LEN(@referralNote) <= 30 THEN @referralNote ELSE LEFT(@referralNote, CHARINDEX(' ', @referralNote + ' ', 30) - 1) + '...' END
		+ CASE WHEN @followUpDate IS NOT NULL THEN @crlf + 'Follow Up Date: ' + convert(varchar(20),@followUpDate,23) ELSE '' END
		+ CASE WHEN @followUpStatus IS NOT NULL THEN @crlf + 'Follow Up Status: ' + CASE @followUpStatus WHEN 'P' THEN 'Pending' ELSE 'Completed' END ELSE '' END;

	SET @message = STRING_ESCAPE(@message,'json');
	
	EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
		@message=@message, @enteredByMemberID=@createdBy;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_createPanel
@uid varchar(50),
@referralID int,
@name varchar(255),
@shortDesc varchar(255) = NULL,
@longDesc varchar(max) = NULL,
@statusID int,
@internalNotes varchar(max) = NULL,
@dateCreated datetime = NULL,
@dateCommitteeApproved datetime = NULL,
@dateBoardApproved datetime = NULL,
@dateBoardNotified datetime = NULL,
@dateReviewed datetime = NULL,
@sendMail bit = NULL,
@maxNumMembers int = NULL,
@referralFeePercent decimal(4,2) = NULL,
@deductExpenseDesc varchar(max) = NULL,
@referralAmount decimal(18,2) = NULL,
@panelParentID int = NULL,
@GLAccountID int = NULL,
@clientFeeGLAccountID int = NULL,
@clientReferralAmount decimal(18,2) = NULL,
@sendReceiptEmail bit = NULL,
@allowPanelMgmt bit = NULL,
@feAllowClientReferral bit = NULL,
@feDspClientReferral bit = NULL,
@feConfirmReferralContent varchar(max) = NULL,
@feReviewSubmissionContent varchar(max) = NULL,
@ovFeeStructure bit = 0,
@memberID int,
@isActive bit = NULL,	
@panelID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @resourceTypeID int, @orgID int, @siteID int, @parentSiteResourceID int, @siteResourceID int, @feConfirmReferralContentID int,
		@feReviewSubmissionContentID int, @contentSiteResourceID int, @appCreatedContentResourceTypeID int, 
		@defaultLanguageID int, @parentPanelName varchar(255), @crlf varchar(10), @msgjson varchar(max);

	IF EXISTS (select panelID FROM dbo.ref_panels where [name] = @name and referralID = @referralID and statusID = 1 and panelParentID = @panelParentID)
		SET @panelID = 0;
	ELSE BEGIN
		set @crlf = char(13) + char(10);
		set @parentSiteResourceID = NULL;
		select @resourceTypeID = dbo.fn_getResourceTypeID('ReferralPanel');

		select @orgID = s.orgID, @siteID = s.siteID
		from dbo.cms_applicationInstances ai
		inner join dbo.ref_referrals r on r.applicationInstanceID = ai.applicationInstanceID
			and r.referralID = @referralID
		inner join dbo.sites s on s.siteID = ai.siteID;

		select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID;
		select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

		IF @panelParentID is not null
			select @parentSiteResourceID = siteResourceID, @parentPanelName = [name]
			from dbo.ref_panels
			where panelid = @panelParentID;

		BEGIN TRAN;
			exec cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,
				@isVisible=1, @parentSiteResourceID=@parentSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;

			-- create feConfirmReferralContentID
			exec dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
				@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, @isActive=1,
				@contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@feConfirmReferralContentID OUTPUT,
				@siteResourceID=@contentSiteResourceID OUTPUT;

			-- create feReviewSubmissionContentID
			exec dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
				@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@feReviewSubmissionContentID OUTPUT, 
				@siteResourceID=@contentSiteResourceID OUTPUT;

			insert into dbo.ref_panels ([uid], referralID, [name], shortDesc, longDesc, statusID, internalNotes, dateCreated,
				dateCommitteeApproved, dateBoardApproved, dateBoardNotified, dateReviewed, sendMail, maxNumMembers,
				referralFeePercent, deductExpenseDesc, referralAmount, panelParentID, GLAccountID, isActive, siteResourceID,
				clientFeeGLAccountID, clientReferralAmount, sendReceiptEmail, allowPanelMgmt, feAllowClientReferral, 
				feDspClientReferral, feConfirmReferralContentID, feReviewSubmissionContentID, ovFeeStructure)
			values (@uid, @referralID, @name, @shortDesc, @longDesc, @statusID, @internalNotes, @dateCreated, 
				@dateCommitteeApproved, @dateBoardApproved, @dateBoardNotified, @dateReviewed, @sendMail,
				@maxNumMembers, @referralFeePercent, @deductExpenseDesc, @referralAmount, @panelParentID, @GLAccountID,
				@isActive, @siteResourceID, @clientFeeGLAccountID, @clientReferralAmount, @sendReceiptEmail, @allowPanelMgmt,
				@feAllowClientReferral, @feDspClientReferral, @feConfirmReferralContentID, @feReviewSubmissionContentID, 
				@ovFeeStructure);

			SELECT @panelID = SCOPE_IDENTITY();

			if @feConfirmReferralContentID is not null
				EXEC dbo.cms_updateContent @contentID=@feConfirmReferralContentID, @languageID=1, @isHTML=1, @contentTitle='', 
					@contentDesc='', @rawContent=@feConfirmReferralContent, @memberID=@memberID;

			if @feReviewSubmissionContentID is not null
				EXEC dbo.cms_updateContent @contentID=@feReviewSubmissionContentID, @languageID=1, @isHTML=1, @contentTitle='', 
					@contentDesc='', @rawContent=@feReviewSubmissionContent, @memberID=@memberID;
		
			IF @parentPanelName IS NOT NULL
				SET @msgjson = 'New Sub-Panel [' + @name + '] has been created under [' + @parentPanelName + '].';
			ELSE
				SET @msgjson = 'New Panel [' + @name + '] has been created.';

			SET @msgjson = @msgjson
				+ CASE WHEN len(isnull(@shortDesc,'')) > 0 THEN @crlf + 'Short Description: ' + @shortDesc ELSE '' END
				+ @crlf + 'Display Panel in Front-End?: ' + CASE @feDspClientReferral WHEN 1 THEN 'Yes' ELSE 'No' END;

			SET @msgjson = STRING_ESCAPE(@msgjson,'json');

			EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
				@enteredByMemberID=@memberID;
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@msgjson varchar(max),
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog_REF", "d": {
		"AUDITCODE":"REF",
		"AREACODE":"' + @areaCode + '",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + @msgjson + '" } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_insertReferralUpdateHistory
@orgID int,
@siteID int,
@clientReferralID int,
@message varchar(max),
@changesArray varchar(max) = NULL,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @changesArray = ISNULL(NULLIF(@changesArray,''),'[]');
	
	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"historyEntries_SYS_ADMIN_REFERRALUPDATE", "d": {
		"HISTORYCODE":"SYS_ADMIN_REFERRALUPDATE",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
		"CLIENTREFERRALID":' + cast(@clientReferralID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MAINMESSAGE":"' + @message + '",
		"CHANGES":'+ @changesArray +'} }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ref_moveClassifications
@classificationID int,
@recordedByMemberID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @classificationOrder int, @referralID int,
		@name varchar(200), @msg varchar(500);

	select @classificationOrder = rc.classificationOrder, @referralID = r.referralID,
		@name = CASE WHEN len(rc.[name]) > 0 THEN rc.[name] ELSE mgs.groupSetName END,
		@orgID = s.orgID, @siteID = s.siteID
	from dbo.ref_classifications as rc
	inner join dbo.ref_referrals as r on r.referralID = rc.referralID
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
	inner join dbo.sites s on s.siteID = ai.siteID
	left join dbo.ams_memberGroupSets as mgs on mgs.groupSetID = rc.groupSetID
	where classificationID = @classificationID;

	IF @classificationOrder IS NULL
		RAISERROR('invalid Classification',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			update dbo.ref_classifications
			set classificationOrder = classificationOrder + 1
			where referralID = @referralID
			and classificationOrder >= @classificationOrder - 1;

			update dbo.ref_classifications
			set classificationOrder = classificationOrder  - 2
			where classificationID = @classificationID;
		END
		ELSE BEGIN
			update dbo.ref_classifications
			set classificationOrder = classificationOrder - 1
			where referralID = @referralID
			AND classificationOrder <= @classificationOrder + 1;

			update dbo.ref_classifications
			set classificationOrder = classificationOrder  + 2
			where classificationID = @classificationID;
		END

		EXEC dbo.ref_reorderClassifications @referralID=@referralID;
	COMMIT TRAN;

	SET @msg = STRING_ESCAPE('Classification [' + @name + '] moved ' + @dir + '.','json');

	EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='CLASSIFICATIONS',
		@msgjson=@msg, @enteredByMemberID=@recordedByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ref_saveClientReferralFilterXML
@siteID int,
@clientID int,
@panelID1 int,
@subPanelID1 varchar(500),
@xmlSearch xml,
@statsSessionID int,
@enteredByMemberID int,
@searchID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @clientReferralID int, @currentSearchID int, @existingSearchXML xml, @primaryPanel varchar(255),
		@primarySubPanelList varchar(max), @secondaryPanel varchar(255), @secondarySubPanelList varchar(max), @tertiaryPanel varchar(255),
		@tertiarySubPanelList varchar(max), @crlf varchar(10), @msgjson varchar(max), @message varchar(max);

	SET @crlf = char(13) + char(10);
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT TOP 1 @clientReferralID = clientReferralID
	FROM dbo.ref_clientReferrals
	WHERE clientID = @clientID;
			
	SELECT TOP 1 @currentSearchID = searchID, @existingSearchXML = searchXML
	FROM searchMC.dbo.tblSearchReferralHistory
	WHERE clientID = @clientID
	ORDER BY dateEntered DESC;

	IF @currentSearchID IS NOT NULL BEGIN
		UPDATE searchMC.dbo.tblSearchReferralHistory 
		SET statsSessionID = NULLIF(@statsSessionID,0), 
			searchXML = @xmlSearch, 
			panelID1 = @panelID1,
			subPanelID1 = @subPanelID1
		WHERE clientID = @clientID;

		SELECT @searchID = searchID
		FROM searchMC.dbo.tblSearchReferralHistory
		WHERE clientID = @clientID;
	END
	ELSE BEGIN
		INSERT INTO searchMC.dbo.tblSearchReferralHistory (clientID, statsSessionID, dateEntered, searchXML, panelID1, subPanelID1)
		VALUES (@clientID, NULLIF(@statsSessionID,0), GETDATE(), @xmlSearch, @panelID1, @subPanelID1);

		SELECT @searchID = SCOPE_IDENTITY();
	END
	
	-- audit logging
	SELECT @primaryPanel = p1.[name], @primarySubPanelList = NULLIF(tmpSP1.nameList,''),
		@secondaryPanel = p2.[name], @secondarySubPanelList=NULLIF(tmpSP2.nameList,''),
		@tertiaryPanel = p3.[name], @tertiarySubPanelList=NULLIF(tmpSP3.nameList,'')
	FROM @xmlSearch.nodes('/search') AS T(X)
	LEFT OUTER JOIN dbo.ref_panels AS p1 ON p1.panelID = X.value('(field[@name="panelid1"]/text())[1]', 'INT')
	LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = X.value('(field[@name="panelid2"]/text())[1]', 'INT')
	LEFT OUTER JOIN dbo.ref_panels AS p3 ON p3.panelID = X.value('(field[@name="panelid3"]/text())[1]', 'INT')
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid1"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP1
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid2"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP2
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid3"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP3;

	IF @currentSearchID IS NOT NULL BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;

		CREATE TABLE #tmpAuditLogData (
			[rowCode] varchar(20) PRIMARY KEY, [Primary Panel] varchar(max), [Primary Sub-Panels] varchar(max),
			[Secondary Panel] varchar(max), [Secondary Sub-Panels] varchar(max), [Tertiary Panel] varchar(max),
			[Tertiary Sub-Panels] varchar(max)
		);

		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		SELECT 
			'OLDVAL', p1.[name] AS primaryPanel, NULLIF(tmpSP1.nameList,'') AS primarySubPanelList,
			p2.[name] AS secondaryPanel, NULLIF(tmpSP2.nameList,'') AS secondarySubPanelList,
			p3.[name] AS tertiaryPanel, NULLIF(tmpSP3.nameList,'') AS tertiarySubPanelList
		FROM @existingSearchXML.nodes('/search') AS T(X)
		LEFT OUTER JOIN dbo.ref_panels AS p1 ON p1.panelID = X.value('(field[@name="panelid1"]/text())[1]', 'INT')
		LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = X.value('(field[@name="panelid2"]/text())[1]', 'INT')
		LEFT OUTER JOIN dbo.ref_panels AS p3 ON p3.panelID = X.value('(field[@name="panelid3"]/text())[1]', 'INT')
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid1"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP1
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid2"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP2
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid3"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP3;

		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING');
		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		VALUES (
			'NEWVAL', @primaryPanel, @primarySubPanelList, @secondaryPanel,
			@secondarySubPanelList, @tertiaryPanel, @tertiarySubPanelList
		);

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @outputAsChangesArray=1, @msg=@msgjson OUTPUT;

		IF @msgjson <> '' BEGIN
			SET @msgjson = '[' + @msgjson + ']';
			EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
				@message='Referral Filters Updated', @changesArray=@msgjson, @enteredByMemberID=@enteredByMemberID;
		END

		IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;
	END
	ELSE BEGIN
		SELECT @message = 'Referral Filters Added'
			+ CASE WHEN @primaryPanel IS NOT NULL THEN @crlf + 'Primary Panel: ' + @primaryPanel ELSE '' END
			+ CASE WHEN @primarySubPanelList IS NOT NULL THEN @crlf + 'Primary Sub-Panels: ' + @primarySubPanelList ELSE '' END
			+ CASE WHEN @secondaryPanel IS NOT NULL THEN @crlf + 'Secondary Panel: ' + @secondaryPanel ELSE '' END
			+ CASE WHEN @secondarySubPanelList IS NOT NULL THEN @crlf + 'Secondary Sub-Panels: ' + @secondarySubPanelList ELSE '' END
			+ CASE WHEN @tertiaryPanel IS NOT NULL THEN @crlf + 'Tertiary Panel: ' + @tertiaryPanel ELSE '' END
			+ CASE WHEN @tertiarySubPanelList IS NOT NULL THEN @crlf + 'Tertiary Sub-Panels: ' + @tertiarySubPanelList ELSE '' END;

		SET @message = STRING_ESCAPE(@message,'json');

		EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
			@message=@message, @enteredByMemberID=@enteredByMemberID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_updateClientReferral
@referralID int,
@clientReferralID int,
@clientID int,
@repID int,
@firstName varchar(75),
@middleName varchar(25),
@lastName varchar(75),
@businessName varchar(100),
@address1 varchar(100),
@address2 varchar(100),
@city varchar(100),
@state int,
@postalCode varchar(25),
@countryID int,
@email varchar(255),
@homePhone varchar(40),
@cellPhone varchar(40),
@alternatePhone varchar(40),
@homePhoneE164 varchar(40),
@cellPhoneE164 varchar(40),
@alternatePhoneE164	varchar(40),
@clientParentID int,
@repFirstName varchar(75),
@repLastName varchar(75),
@repAddress1 varchar(100),
@repAddress2 varchar(100),
@repCity varchar(100),
@repState int,
@repPostalCode varchar(25),
@repEmail varchar(255),
@repHomePhone varchar(40),
@repCellPhone varchar(40),
@repAlternatePhone varchar(40),
@repHomePhoneE164 varchar(40),
@repCellPhoneE164 varchar(40),
@repAlternatePhoneE164	varchar(40),
@relationToClient varchar(100),
@repParentID int,
@sourceID int,
@otherSource varchar(100),
@communicateLanguageID int,
@issueDesc varchar(max),
@agencyID int,
@caseFeeTypeID int,
@callTypeID int,
@sendSurvey	bit,
@sendNewsBlog bit,
@statusID int,
@panelID int,
@counselorMemberID int,
@updateMode varchar(5),
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	CREATE TABLE #tmpAuditLogData (
		[rowCode] varchar(20) PRIMARY KEY, [First Name] varchar(max), [Middle Name] varchar(max), [Last Name] varchar(max),
		[Business] varchar(max), [Address 1] varchar(max), [Address 2] varchar(max), [City] varchar(max), [State] varchar(max),
		[Zip Code] varchar(max), [E-mail] varchar(max), [Home Phone] varchar(max), [Cell Phone] varchar(max), [Alternate Phone] varchar(max),
		[Home Phone E164] varchar(max), [Cell Phone E164] varchar(max), [Alternate Phone E164] varchar(max),
		[Rep First Name] varchar(max), [Rep Last Name] varchar(max), [Rep Relationship to Client] varchar(max), [Rep Address 1] varchar(max),
		[Rep Address 2] varchar(max), [Rep City] varchar(max), [Rep State] varchar(max), [Rep Zip Code] varchar(max), [Rep E-mail] varchar(max),
		[Rep Home Phone] varchar(max), [Rep Cell Phone] varchar(max), [Rep Alternate Phone] varchar(max),
		[Rep Home Phone E164] varchar(max), [Rep Cell Phone E164] varchar(max), [Rep Alternate Phone E164] varchar(max),
		[Source] varchar(max), [Language] varchar(max), [Issue Description] varchar(max), [Counselor] varchar(max), [Call Type] varchar(max),
		[Agency] varchar(max), [Case Fee Type] varchar(max), [Send Survey] varchar(max), [Send News Blog] varchar(max)
	);

	DECLARE @orgID int, @siteID int, @crlf varchar(10), @mainMessage varchar(max), @msgjson varchar(max), @nowDate datetime = getdate(),
		@newClientRecord bit = 0, @newRepRecord bit = 0;

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Source], [Language], [Issue Description], [Counselor], [Call Type],
		[Agency], [Case Fee Type], [Send Survey], [Send News Blog]
	)
	SELECT 'OLDVAL', src.clientReferralSource, rl.languageName, r.issueDesc, counselor.firstName + ' ' + counselor.lastName,
		rt.clientReferralType, a.[name], ft.feeTypeName, ISNULL(r.sendSurvey,0), ISNULL(r.sendNewsBlog,0)
	FROM dbo.ref_clientReferrals AS r
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS src ON src.clientReferralSourceID = r.sourceID
	LEFT OUTER JOIN dbo.ref_languages AS rl ON rl.languageID = r.communicateLanguageID
	LEFT OUTER JOIN dbo.ams_members AS counselor ON counselor.memberID = r.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS rt ON rt.referralID = @referralID
		AND rt.clientReferralTypeID = r.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.referralID = @referralID
		AND a.agencyID = r.agencyID
	LEFT OUTER JOIN dbo.ref_clientReferralFeeTypes AS ft ON ft.feeTypeID = r.feeTypeID
	WHERE r.clientReferralID = @clientReferralID;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [First Name], [Middle Name], [Last Name], [Business], [Address 1], [Address 2], [City],
		[State], [Zip Code], [E-mail], [Home Phone], [Cell Phone], [Alternate Phone],[Home Phone E164], [Cell Phone E164], [Alternate Phone E164],
		[Rep First Name], [Rep Last Name], [Rep Relationship to Client], [Rep Address 1], [Rep Address 2],
		[Rep City], [Rep State], [Rep Zip Code], [Rep E-mail], [Rep Home Phone], [Rep Cell Phone], [Rep Alternate Phone],
		[Rep Home Phone E164], [Rep Cell Phone E164], [Rep Alternate Phone E164],
		[Source], [Language], [Issue Description], [Counselor], [Call Type], [Agency], [Case Fee Type], [Send Survey],
		[Send News Blog]
	)
	VALUES (
		'DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING',
		'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING','STRING', 'STRING', 'STRING','STRING',
		'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING','STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT'
	);

	-- compare client info only if existing record is updated
	IF ISNULL(@clientID,0) > 0 BEGIN
		UPDATE tmp
		SET tmp.[First Name] = c.firstName, tmp.[Middle Name] = c.middleName,  tmp.[Last Name] = c.lastName, tmp.[Business] = c.businessName,
			tmp.[Address 1] = c.address1, tmp.[Address 2] = c.address2, tmp.[City] = c.city, tmp.[State] = cs.[Name],
			tmp.[Zip Code] = c.postalCode, tmp.[E-mail] = c.email, tmp.[Home Phone] = c.homePhone, tmp.[Cell Phone] = c.cellPhone,
			tmp.[Alternate Phone] = c.alternatePhone, tmp.[Home Phone E164] = c.homePhoneE164, tmp.[Cell Phone E164] = c.cellPhoneE164,
			tmp.[Alternate Phone E164] = c.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS c ON c.clientID = @clientID
			AND c.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = c.[state]
		WHERE tmp.[rowCode] = 'OLDVAL';
	END

	-- compare rep info only if existing record is updated
	IF ISNULL(@repID,0) > 0 BEGIN
		UPDATE tmp
		SET tmp.[Rep First Name] = rep.firstName, tmp.[Rep Last Name] = rep.lastName, tmp.[Rep Relationship to Client] = rep.relationToClient,
			tmp.[Rep Address 1] = rep.address1, tmp.[Rep Address 2] = rep.address2, tmp.[Rep City] = rep.city, tmp.[Rep State] = cs.[Name],
			tmp.[Rep Zip Code] = rep.postalCode, tmp.[Rep E-mail] = rep.email, tmp.[Rep Home Phone] = rep.homePhone, tmp.[Rep Cell Phone] = rep.cellPhone,
			tmp.[Rep Alternate Phone] = rep.alternatePhone, tmp.[Rep Home Phone E164] = rep.homePhoneE164, tmp.[Rep Cell Phone E164] = rep.cellPhoneE164,
			tmp.[Rep Alternate Phone E164] = rep.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS rep ON rep.clientID = @repID
			AND rep.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = rep.[state]
		WHERE tmp.[rowCode] = 'OLDVAL';
	END

	BEGIN TRAN;

		IF ISNULL(@clientID,0) > 0 BEGIN
			UPDATE dbo.ref_clients
			SET firstName = @firstName,
				middleName = @middleName,
				lastName = @lastName,
				businessName = @businessName,
				address1 = @address1,
				address2 = @address2,
				city = @city,
				[state] = @state,
				postalCode = @postalCode,
				email = @email,
				homePhone = @homePhone,
				cellPhone = @cellPhone,
				alternatePhone = @alternatePhone,
				homePhoneE164 = @homePhoneE164,
				cellPhoneE164 = @cellPhoneE164,
				alternatePhoneE164 = @alternatePhoneE164,
				clientParentID = @clientParentID
			WHERE clientID = @clientID;
		END
		ELSE BEGIN
			EXEC dbo.ref_createClient @referralID=@referralID, @firstName=@firstName, @middleName=@middleName,
				@lastName=@lastName, @businessName=@businessName, @address1=@address1, @address2=@address2,
				@address3=NULL, @city=@city, @state=@state, @postalCode=@postalCode, @countryID=@countryID,
				@email=@email, @homePhone=@homePhone, @cellPhone=@cellPhone, @alternatePhone=@alternatePhone,
				@homePhoneE164 =@homePhoneE164,@cellPhoneE164=@cellPhoneE164,@alternatePhoneE164=@alternatePhoneE164,
				@typeID=1, @createdBy=@enteredByMemberID, @clientParentID=@clientParentID,
				@relationToClient=NULL, @clientID=@clientID OUTPUT;

			SET @newClientRecord = 1;
		END

		IF ISNULL(@repID,0) = 0 AND LEN(@repFirstName) > 0 AND LEN(@repLastName) > 0 AND (
			LEN(@repHomePhone) > 0 OR LEN(@repCellPhone) > 0 OR LEN(@repAlternatePhone) > 0 OR LEN(@repEmail) > 0
		) BEGIN
			EXEC dbo.ref_createClient @referralID	= @referralID, @firstName = @repFirstName,
				@middleName	= NULL, @lastName = @repLastName, @businessName = NULL, @address1 = @repAddress1,
				@address2 = @repAddress2, @address3 = NULL, @city = @repCity, @state = @repState,
				@postalCode = @repPostalCode, @countryID = NULL, @email = @repEmail, @homePhone = @repHomePhone,
				@cellPhone = @repCellPhone, @alternatePhone = @repAlternatePhone, 
				@homePhoneE164 =@repHomePhoneE164,@cellPhoneE164=@repCellPhoneE164,@alternatePhoneE164=@repAlternatePhoneE164,
				@typeID = 2, @createdBy = @enteredByMemberID,
				@clientParentID = @repParentID, @relationToClient = @relationToClient, @clientID = @repID OUTPUT;

			SET @newRepRecord = 1;
		END
		ELSE IF ISNULL(@repID,0) > 0 BEGIN
			UPDATE dbo.ref_clients
			SET firstName = @repFirstName,
				lastName = @repLastName,
				businessName = @businessName,
				address1 = @repAddress1,
				address2 = @repAddress2,
				city = @repCity,
				[state] = @repState,
				postalCode = @repPostalCode,
				email = @repEmail,
				homePhone = @repHomePhone,
				cellPhone = @repCellPhone,
				alternatePhone = @repAlternatePhone,
				homePhoneE164 = @repHomePhoneE164,
				cellPhoneE164 = @repCellPhoneE164,
				alternatePhoneE164 = @repAlternatePhoneE164,
				relationToClient = @relationToClient,
				clientParentID = @repParentID
			WHERE clientID = @repID;
		END

		IF @updateMode IN ('CP','API')
			UPDATE dbo.ref_clientReferrals
			SET representativeID = NULLIF(@repID,0),
				sourceID = @sourceID,
				otherSource = @otherSource,
				communicateLanguageID = @communicateLanguageID,
				issueDesc = @issueDesc,
				agencyID = @agencyID,
				feeTypeID = @caseFeeTypeID,
				sendSurvey = @sendSurvey,
				statusID = CASE WHEN @statusID IS NOT NULL THEN @statusID ELSE statusID END,
				typeID = @callTypeID,
				sendNewsBlog = @sendNewsBlog,
				panelID = CASE WHEN @panelID IS NOT NULL THEN @panelID ELSE panelID END,
				enteredByMemberID = CASE WHEN @counselorMemberID IS NOT NULL THEN @counselorMemberID ELSE enteredByMemberID END,
				lastUpdatedBy = @enteredByMemberID,
				dateLastUpdated = @nowDate
			WHERE clientReferralID = @clientReferralID;
		ELSE
			UPDATE dbo.ref_clientReferrals
			SET clientID = @clientID,
				representativeID = NULLIF(@repID,0),
				sourceID = @sourceID,
				otherSource = @otherSource,
				communicateLanguageID = @communicateLanguageID,
				issueDesc = @issueDesc,
				feeTypeID = CASE WHEN ISNULL(@caseFeeTypeID,0) > 0 THEN @caseFeeTypeID ELSE feeTypeID END,
				sendSurvey = @sendSurvey,
				statusID = CASE WHEN @statusID IS NOT NULL THEN @statusID ELSE statusID END,
				sendNewsBlog = @sendNewsBlog,
				lastUpdatedBy = @enteredByMemberID,
				dateLastUpdated = @nowDate
			WHERE clientReferralID = @clientReferralID;

	COMMIT TRAN

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Source], [Language], [Issue Description], [Counselor], [Call Type],
		[Agency], [Case Fee Type], [Send Survey], [Send News Blog]
	)
	SELECT 'NEWVAL', src.clientReferralSource, rl.languageName, r.issueDesc, counselor.firstName + ' ' + counselor.lastName,
		rt.clientReferralType, a.[name], ft.feeTypeName, ISNULL(r.sendSurvey,0), ISNULL(r.sendNewsBlog,0)
	FROM dbo.ref_clientReferrals AS r
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS src ON src.clientReferralSourceID = r.sourceID
	LEFT OUTER JOIN dbo.ref_languages AS rl ON rl.languageID = r.communicateLanguageID
	LEFT OUTER JOIN dbo.ams_members AS counselor ON counselor.memberID = r.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS rt ON rt.referralID = @referralID
		AND rt.clientReferralTypeID = r.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.referralID = @referralID
		AND a.agencyID = r.agencyID
	LEFT OUTER JOIN dbo.ref_clientReferralFeeTypes AS ft ON ft.feeTypeID = r.feeTypeID
	WHERE r.clientReferralID = @clientReferralID;

	IF ISNULL(@clientID,0) > 0 AND @newClientRecord = 0 BEGIN
		UPDATE tmp
		SET tmp.[First Name] = c.firstName, tmp.[Middle Name] = c.middleName,  tmp.[Last Name] = c.lastName, tmp.[Business] = c.businessName,
			tmp.[Address 1] = c.address1, tmp.[Address 2] = c.address2, tmp.[City] = c.city, tmp.[State] = cs.[Name],
			tmp.[Zip Code] = c.postalCode, tmp.[E-mail] = c.email, tmp.[Home Phone] = c.homePhone, tmp.[Cell Phone] = c.cellPhone,
			tmp.[Alternate Phone] = c.alternatePhone, tmp.[Home Phone E164] = c.homePhoneE164, tmp.[Cell Phone E164] = c.cellPhoneE164,
			tmp.[Alternate Phone E164] = c.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS c ON c.clientID = @clientID
			AND c.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = c.[state]
		WHERE tmp.[rowCode] = 'NEWVAL';
	END
	
	IF ISNULL(@repID,0) > 0 AND @newRepRecord = 0 BEGIN
		UPDATE tmp
		SET tmp.[Rep First Name] = rep.firstName, tmp.[Rep Last Name] = rep.lastName, tmp.[Rep Relationship to Client] = rep.relationToClient,
			tmp.[Rep Address 1] = rep.address1, tmp.[Rep Address 2] = rep.address2, tmp.[Rep City] = rep.city, tmp.[Rep State] = cs.[Name],
			tmp.[Rep Zip Code] = rep.postalCode, tmp.[Rep E-mail] = rep.email, tmp.[Rep Home Phone] = rep.homePhone, tmp.[Rep Cell Phone] = rep.cellPhone,
			tmp.[Rep Alternate Phone] = rep.alternatePhone, tmp.[Rep Home Phone E164] = rep.homePhoneE164, tmp.[Rep Cell Phone E164] = rep.cellPhoneE164,
			tmp.[Rep Alternate Phone E164] = rep.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS rep ON rep.clientID = @repID
			AND rep.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = rep.[state]
		WHERE tmp.[rowCode] = 'NEWVAL';
	END

	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @outputAsChangesArray=1, @msg=@msgjson OUTPUT;

	
	IF @newClientRecord = 1
		SET @mainMessage = COALESCE(@mainMessage + @crlf, '') + 'Added New Client Record: ' + @firstName + ' ' + @lastName;
	IF @newRepRecord = 1
		SET @mainMessage = COALESCE(@mainMessage + @crlf, '') + 'Added Representative: ' + @repFirstName + ' ' + @repLastName;

	-- audit log
	IF @msgjson <> '' OR ISNULL(@mainMessage,'') <> '' BEGIN
		SET @mainMessage = 'Referral Updated (' + CASE @updateMode WHEN 'CP' THEN 'Admin' WHEN 'API' THEN 'API' WHEN 'FE' THEN 'Member' ELSE '' END + ')'
			+ COALESCE(@crlf + @mainMessage, '');
		SET @mainMessage = STRING_ESCAPE(@mainMessage,'json');
		SET @msgjson = '[' + @msgjson + ']';
		EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
			@message=@mainMessage, @changesArray=@msgjson, @enteredByMemberID=@enteredByMemberID;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.ref_updatePanelsByIds
@orgID INT,
@siteID INT,
@panelIDs VARCHAR(MAX),
@defaultLanguageID INT,
@recordedByMemberID INT,
@panelStatusID VARCHAR(10),
@feDspClientReferral VARCHAR(2),
@panelID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpReferralsToUpdate') IS NOT NULL
		DROP TABLE #tmpReferralsToUpdate;

	CREATE TABLE #tmpReferralsToUpdate (panelID INT);
	
	DECLARE  @Delimiter VARCHAR(2) = ',', @panelCount INT, @panelIdx INT = 0;
	
	DECLARE @Xml XML = CAST('<d>' + REPLACE(@panelIDs, @Delimiter, '</d><d>') + '</d>' AS xml);
	
	INSERT INTO #tmpReferralsToUpdate (panelID)
	SELECT  a.split.value('.', 'VARCHAR(MAX)') as panelID
	from    @Xml.nodes('/d') a(split);
	
	SELECT @panelCount = COUNT(trp.panelID) FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;

	IF @panelCount = 0
		SET @panelID = 0;
	ELSE BEGIN
		DECLARE @msg VARCHAR(MAX), @crlf VARCHAR(2), @oldPanelName VARCHAR(255);
		SET @crlf = char(13) + char(10);
		
		SELECT TOP 1 @panelIdx = rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;
		SET @panelID = 0;
		WHILE EXISTS(SELECT TOP 1 rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID)
		BEGIN
			
			IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;			
		
			CREATE TABLE #tmpAuditLogData ([rowCode] VARCHAR(20) PRIMARY KEY, [Status] VARCHAR(MAX),[Display Panel in Front-End?] VARCHAR(MAX));
			
			-- datatypecode
			INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
			VALUES ('DATATYPECODE', 'STRING','BIT');

			-- existing data
			INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
			SELECT 'OLDVAL', ps.statusName, p.feDspClientReferral
			FROM dbo.ref_panels AS P
			INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
			LEFT OUTER JOIN dbo.ref_panels AS pp ON pp.panelID = p.panelParentID
			LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pg on pg.GLAccountID = p.GLAccountID
			LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pc on pc.GLAccountID = p.clientFeeGLAccountID
			WHERE p.panelID = @panelIdx;		
			
			
			SELECT @oldPanelName = p.name FROM dbo.ref_panels AS P WHERE p.panelID = @panelIdx;			
			
			BEGIN TRAN;
			
				UPDATE p    
					SET  p.statusID = CASE @panelStatusID
										WHEN ''   THEN statusID
										ELSE COALESCE(@panelStatusID, statusID)
									  END,								  
						 p.feDspClientReferral = CASE @feDspClientReferral
										WHEN ''  THEN feDspClientReferral
										ELSE COALESCE(@feDspClientReferral, feDspClientReferral)
									  END  
									  
					FROM dbo.ref_panels AS P INNER JOIN #tmpReferralsToUpdate trp
						ON p.panelID = trp.panelID
				
				
				-- new data
				INSERT INTO #tmpAuditLogData ([rowCode], [Status],[Display Panel in Front-End?])
				SELECT 'NEWVAL', ps.statusName, p.feDspClientReferral
				FROM dbo.ref_panels AS P
				INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
				LEFT OUTER JOIN dbo.ref_panels AS pp ON pp.panelID = p.panelParentID
				LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pg on pg.GLAccountID = p.GLAccountID
				LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as pc on pc.GLAccountID = p.clientFeeGLAccountID
				WHERE p.panelID = @panelIdx;

				EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
				
				IF (@panelStatusID is not null AND @panelStatusID <> '' )
				BEGIN
					declare @parentPanelStatusID int, @statusName varchar(255), @parentPanelID int;

					SELECT @parentPanelStatusID = panelStatusID, @statusName = statusName 
					FROM dbo.ref_panelStatus 
					WHERE panelStatusID = @panelStatusID;
					
					IF @statusName = 'Active' BEGIN
						SELECT @msg = COALESCE(@msg + @crlf,'') + 'Sub-Panel [' + STRING_ESCAPE(p.[name],'json') + '] activated.'
						FROM dbo.ref_panels AS p
						INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
							AND ps.statusName = 'Inactive'
						WHERE p.panelParentID = @panelIdx;

						UPDATE p 
						SET p.statusID = @panelStatusID 
						FROM dbo.ref_panels AS p
						INNER JOIN dbo.ref_panelStatus AS ps ON ps.panelStatusID = p.statusID
							AND ps.statusName = 'Inactive'
						WHERE p.panelParentID = @panelIdx;
					END

					IF @statusName = 'Deleted' BEGIN
						SELECT @msg = COALESCE(@msg + @crlf,'') + 'Sub-Panel [' + STRING_ESCAPE([name],'json') + '] deleted.'
						FROM dbo.ref_panels
						WHERE panelParentID = @panelIdx 
						AND isActive = 1;

						UPDATE dbo.ref_panels 
						SET statusID = @panelStatusID 
						WHERE panelParentID = @panelIdx 
						AND isActive = 1;
					END		
				END
			COMMIT TRAN;
			-- audit log
			IF ISNULL(@msg,'') <> '' BEGIN
				SET @msg = 'Panel [' + STRING_ESCAPE(@oldPanelName,'json') + '] has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

				EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msg, @enteredByMemberID=@recordedByMemberID;
			END
			SET @panelID = @panelIdx;
			DELETE FROM #tmpReferralsToUpdate WHERE panelID = @panelIdx;			
			SELECT TOP 1 @panelIdx = rp.panelID FROM ref_panels rp INNER JOIN #tmpReferralsToUpdate trp ON rp.panelID = trp.panelID;
			
		END
		
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_copySubscription
@orgID INT,
@siteid INT,
@typeID INT,
@subID INT,
@newSubName VARCHAR(300),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @scheduleID INT, @rateTermDateFlag varchar(1), @autoExpire BIT, @status varchar(1), 
		@soldSeparately BIT, @paymentOrder INT, @GLAccountID INT, @allowRateGLOverride BIT, 
		@activationOptionCode varchar(1), @alternateActivationOptionCode varchar(1), @feRawContent varchar(max), 
		@feCompletedRawContent varchar(max), @feParentSubRawContent varchar(max), @emailTemplateID INT, 
		@renewEmailTemplateID INT, @newSubID INT, @addonID INT, @newAddonID INT, @childSetID INT, @orderNum INT,
		@minAllowed INT, @maxAllowed INT, @useAcctCodeInSet BIT, @useTermEndDateInSet BIT, @PCnum INT, 
		@PCPctOffEach INT, @frontEndAllowSelect BIT, @frontEndAllowChangePrice BIT, @frontEndContentID INT, 
		@frontEndAddAdditional BIT, @feContentRawContent VARCHAR(MAX), @subAdminResourceID INT, 
		@appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT, 
		@subName VARCHAR(300), @msgjson VARCHAR(MAX);

	SELECT @scheduleID=subs.scheduleID, @autoExpire=subs.autoExpire, @status=subs.status, @soldSeparately=subs.soldSeparately,  
		@rateTermDateFlag=subs.rateTermDateFlag, @GLAccountID=subs.GLAccountID, 
		@paymentOrder=subs.paymentOrder, @allowRateGLOverride=subs.allowRateGLAccountOverride, 
		@feRawContent=frontEndContent.rawContent, @feCompletedRawContent=frontEndCompletedContent.rawContent, 
		@feParentSubRawContent=frontEndParentSubContent.rawContent, @emailTemplateID=subs.emailTemplateID, 
		@renewEmailTemplateID=subs.renewEmailTemplateID, @activationOptionCode=o.subActivationCode, 
		@alternateActivationOptionCode=o2.subActivationCode, @subName=subs.subscriptionName
	FROM dbo.sub_subscriptions subs
	INNER JOIN dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteid
	INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = subs.GLAccountID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
	INNER JOIN dbo.sub_activationOptions o on o.subActivationID = subs.subActivationID
	INNER JOIN dbo.sub_activationOptions o2 on o2.subActivationID = subs.subAlternateActivationID
	CROSS APPLY dbo.fn_getContent(subs.frontEndContentID,1) as frontEndContent
	CROSS APPLY dbo.fn_getContent(subs.frontEndCompletedContentID,1) as frontEndCompletedContent
	CROSS APPLY dbo.fn_getContent(subs.frontEndParentSubContentID,1) as frontEndParentSubContent
	LEFT OUTER JOIN dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID
	LEFT OUTER JOIN dbo.et_emailTemplates ret on ret.templateID = subs.renewEmailTemplateID
	WHERE subs.subscriptionID = @subID;

	BEGIN TRAN;
		-- create subscription
		EXEC dbo.sub_createSubscription @orgID=@orgID, @siteid=@siteid, @typeID=@typeID, @subName=@newSubName,
			@reportCode=NULL, @scheduleID=@scheduleID, @rateTermDateFlag=@rateTermDateFlag,
			@autoExpire=@autoExpire, @status=@status, @soldSeparately=@soldSeparately, @paymentOrder=@paymentOrder,
			@GLAccountID=@GLAccountID, @allowRateGLOverride=@allowRateGLOverride, @activationOptionCode=@activationOptionCode,
			@alternateActivationOptionCode=@alternateActivationOptionCode, @feRawContent=@feRawContent, 
			@feCompletedRawContent=@feCompletedRawContent, @feParentSubRawContent=@feParentSubRawContent, 
			@recordedByMemberID=@recordedByMemberID, @subID=@newSubID OUTPUT;

		UPDATE dbo.sub_subscriptions
		SET emailTemplateID = @emailTemplateID,
			renewEmailTemplateID = @renewEmailTemplateID
		WHERE subscriptionID = @newSubID;

		-- copy subscription_sets
		INSERT INTO dbo.sub_subscriptionSets (subscriptionID, setID, orderNum)
		SELECT @newSubID as subscriptionID, setID, (SELECT IsNull(MAX(orderNum),0)+1 FROM sub_subscriptionSets WHERE setID=sets.setID)
		FROM sub_subscriptionSets sets
		where subscriptionID=@subID;

		-- copy add_ons
		SELECT @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
		FROM dbo.sites
		WHERE siteID = @siteID;

		SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

		DECLARE add_on_cursor CURSOR FOR 
			SELECT addonID, childSetID, orderNum 
			FROM dbo.sub_addons
			WHERE subscriptionID=@subID;
		OPEN add_on_cursor;
		FETCH NEXT FROM add_on_cursor INTO @addonID, @childSetID, @orderNum;
		WHILE @@FETCH_STATUS = 0
		BEGIN

			SELECT @minAllowed=ao.minAllowed, @maxAllowed=ao.maxAllowed, @useAcctCodeInSet=ao.useAcctCodeInSet,
				@useTermEndDateInSet=ao.useTermEndDateInSet, @PCnum=IsNull(ao.PCnum, 0), @PCPctOffEach=IsNull(PCPctOffEach, 0),
				@frontEndAllowSelect=ao.frontEndAllowSelect, @frontEndAddAdditional=ao.frontEndAddAdditional,
				@frontEndAllowChangePrice=ao.frontEndAllowChangePrice, @feContentRawContent=frontEndContent.rawContent
			FROM dbo.sub_addons ao
			INNER JOIN dbo.sub_sets sets on sets.setID = ao.childSetID and sets.siteID=@siteid
			CROSS APPLY dbo.fn_getContent(ao.frontEndContentID,1) as frontEndContent
			WHERE ao.addonID = @addonID AND childSetID=@childSetID;

			INSERT INTO dbo.sub_addons(subscriptionID, childSetID, orderNum, minAllowed, maxAllowed, useAcctCodeInSet,
				useTermEndDateInSet, PCnum, PCPctOffEach, frontEndAllowSelect, frontEndAllowChangePrice,frontEndAddAdditional)
			VALUES (@newSubID, @childSetID, (@orderNum+1), @minAllowed, @maxAllowed, @useAcctCodeInSet, @useTermEndDateInSet,
				@PCnum, @PCPctOffEach, @frontEndAllowSelect, @frontEndAllowChangePrice, @frontEndAddAdditional);

			SELECT @newAddonID = SCOPE_IDENTITY();

			exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
				@rawContent=@feContentRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

			UPDATE dbo.sub_addons
			SET frontEndContentID = @contentID
			WHERE addonID = @newAddonID;

			FETCH NEXT FROM add_on_cursor INTO @addonID, @childSetID, @orderNum;
		END 
		CLOSE add_on_cursor;
		DEALLOCATE add_on_cursor;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@newSubID AS varchar(10))+' }';
		SET @msgjson = STRING_ESCAPE('Subscription ' + QUOTENAME(@newSubName) + ' has been copied from Subscription '+ QUOTENAME(@subName) +'.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_createFrequency 
@frequencyName varchar (50), 
@frequency int, 
@frequencyShortName varchar (10), 
@rateRequired bit, 
@hasInstallments bit = 0, 
@monthlyInterval int = null, 
@isSystemRate bit = 0,
@siteID int, 
@status char(1),
@recordedByMemberID int,
@frequencyID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @frequencyID = null;
	DECLARE @crlf varchar(10), @msgjson varchar(max), @orgID int;

	SET @crlf = char(13) + char(10);
	select @orgID = orgID from sites where siteID = @siteID;

	IF NOT EXISTS (select frequencyID from dbo.sub_frequencies where siteID = @siteID and [status] <> 'D' and (frequencyName = @frequencyName or frequencyShortName = @frequencyShortName)) BEGIN
		INSERT into dbo.sub_frequencies (frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, 
			monthlyInterval, isSystemRate, siteID, [status]) 
		VALUES (@frequencyName, @frequency, @frequencyShortName, @rateRequired, @hasInstallments, @monthlyInterval, 
			@isSystemRate, @siteID, @status);
	
		select @frequencyID = SCOPE_IDENTITY();

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
		SET @msgjson = 'New Frequency [' + @frequencyName + '] has been created.'
			+ CASE WHEN @frequency IS NOT NULL THEN @crlf + 'Number of Installments: ' + CAST(@frequency AS VARCHAR(10)) ELSE '' END
			+ CASE WHEN NULLIF(@frequencyShortName,'') IS NOT NULL THEN @crlf + 'Short Name: ' + @frequencyShortName ELSE '' END
			+ CASE WHEN @rateRequired IS NOT NULL THEN @crlf + 'Rate Required: ' + CASE WHEN @rateRequired = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frequency IS NOT NULL THEN @crlf + 'Has Installments: ' + CASE WHEN @frequency > 0 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @monthlyInterval IS NOT NULL THEN @crlf + 'Number of Months Between Payments: ' + CAST(@monthlyInterval AS VARCHAR(10)) ELSE '' END;

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_createRate
@scheduleID int,
@rateName varchar(100),
@reportCode varchar(15),
@status char(1),
@rateStartDate datetime,
@rateEndDate datetime,
@rateStartAFID INT,
@rateEndAFID INT,
@termStartDate datetime,
@termEndDate datetime,
@termStartAFID INT,
@termEndAFID INT,
@graceEndDate datetime,
@graceEndAFID INT,
@recogStartDate datetime,
@recogEndDate datetime,
@recogStartAFID INT,
@recogEndAFID INT,
@rateAdvanceOnTermEnd INT,
@isRenewalRate bit,
@forceUpfront bit,
@accountID INT,
@frontEndAllowChangePrice bit,
@linkedNonRenewalRateID INT,
@fallbackRenewalRateID INT,
@keepChangedPriceOnRenewal bit,
@frontEndChangePriceMin decimal(18,2),
@frontEndChangePriceMax decimal(18,2),
@isImport bit = 0,
@recordedByMemberID INT,
@rateID int OUTPUT,
@siteResourceID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- null output variables
	select @rateID = null, @siteResourceID = null;

	declare @siteID int, @orgID int, @siteResourceTypeID int, @rateOrder int, 
		@linkedNonRenewalRateName varchar(100), @fallbackRenewalRateName varchar(100), @scheduleName varchar(200),
		@crlf varchar(10), @msgjson varchar(max);

	select @siteID = siteid from dbo.sub_rateSchedules where scheduleID = @scheduleID;
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @siteResourceTypeID = dbo.fn_getResourceTypeID('SubscriptionRate');
	
	SET @crlf = char(13) + char(10);

	if (@frontEndChangePriceMin is not null) and (@frontEndChangePriceMax is not null) and (@frontEndChangePriceMin > @frontEndChangePriceMax) BEGIN
		set @frontEndChangePriceMin = null;
		set @frontEndChangePriceMax = null;
	END

	-- get new rate's sort order
	select @rateOrder = isNull(max(r.rateOrder),0)+1 
	from dbo.sub_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
	where r.scheduleID = @scheduleID
	and sr.siteResourceStatusID = 1;

	IF @linkedNonRenewalRateID IS NOT NULL
		SELECT @linkedNonRenewalRateName = rateName
		FROM dbo.sub_rates
		WHERE rateID = @linkedNonRenewalRateID;

	IF @fallbackRenewalRateID IS NOT NULL
		SELECT @fallbackRenewalRateName = rateName
		FROM dbo.sub_rates
		WHERE rateID = @fallbackRenewalRateID;

	SELECT @scheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE scheduleID = @scheduleID;

	BEGIN TRAN;
		-- create a resourceID for the rate
		exec dbo.cms_createSiteResource @resourceTypeID=@siteResourceTypeID, @siteResourceStatusID=1,
			@siteID=@siteid, @isVisible=1, @parentSiteResourceID=null, @siteResourceID=@siteResourceID OUTPUT;

		-- add rate
		INSERT INTO dbo.sub_rates (scheduleID, siteResourceID, [status], 
			rateStartDate, rateEndDate, rateStartDateAFID, rateEndDateAFID, rateAFStartDate, rateAFEndDate, 
			termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, 
			graceEndDate, graceAFID, 
			recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, recogAFStartDate, recogAFEndDate, 
			rateName, rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, reportCode, GLAccountID, 
			frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID, keepChangedPriceOnRenewal, 
			frontEndChangePriceMin, frontEndChangePriceMax, rateOrder)
		VALUES (@scheduleID, @siteResourceID, @status, 
			@rateStartDate, @rateEndDate, @rateStartAFID, @rateEndAFID, @rateStartDate, @rateEndDate, 
			@termStartDate, @termEndDate, @termStartAFID, @termEndAFID, @termStartDate, @termEndDate, 
			@graceEndDate, @graceEndAFID, 
			@recogStartDate, @recogEndDate, @recogStartAFID, @recogEndAFID, @recogStartDate, @recogEndDate, 
			@rateName, @rateAdvanceOnTermEnd, @isRenewalRate, @forceUpfront, @reportCode, @accountID,
			@frontEndAllowChangePrice, @linkedNonRenewalRateID, @fallbackRenewalRateID, @keepChangedPriceOnRenewal,
			@frontEndChangePriceMin, @frontEndChangePriceMax, @rateOrder);

		select @rateID = SCOPE_IDENTITY();

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msgjson = 'New Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] has been created.'
			+ CASE WHEN NULLIF(@reportCode,'') IS NOT NULL THEN @crlf + 'Report Code: ' + @reportCode ELSE '' END
			+ @crlf + 'Rate Start Date: ' + CONVERT(varchar(20), @rateStartDate, 120)
			+ @crlf + 'Rate End Date: ' + CONVERT(varchar(20), @rateEndDate, 120)
			+ CASE WHEN @termStartDate IS NOT NULL THEN @crlf + 'Term Start Date: ' + CONVERT(varchar(20), @termStartDate, 120) ELSE '' END
			+ CASE WHEN @termEndDate IS NOT NULL THEN @crlf + 'Term End Date: ' + CONVERT(varchar(20), @termEndDate, 120) ELSE '' END
			+ CASE WHEN @graceEndDate IS NOT NULL THEN @crlf + 'Grace End Date: ' + CONVERT(varchar(20), @graceEndDate, 120) ELSE '' END
			+ CASE WHEN @recogStartDate IS NOT NULL THEN @crlf + 'Recognition Start Date: ' + CONVERT(varchar(20), @recogStartDate, 120) ELSE '' END
			+ CASE WHEN @recogEndDate IS NOT NULL THEN @crlf + 'Recognition End Date: ' + CONVERT(varchar(20), @recogEndDate, 120) ELSE '' END
			+ CASE WHEN @rateAdvanceOnTermEnd IS NOT NULL THEN @crlf + 'Rate Advance On Term End: ' + CAST(@rateAdvanceOnTermEnd AS VARCHAR(10)) ELSE '' END
			+ CASE WHEN @isRenewalRate IS NOT NULL THEN @crlf + 'Is Renewal Rate: ' + CASE WHEN @isRenewalRate = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @forceUpfront IS NOT NULL THEN @crlf + 'Force Upfront: ' + CASE WHEN @forceUpfront = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frontEndAllowChangePrice IS NOT NULL THEN @crlf + 'Front End Allow Change Price: ' + CASE WHEN @frontEndAllowChangePrice = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @linkedNonRenewalRateID IS NOT NULL THEN @crlf + 'Linked Non Renewal Rate: ' + @linkedNonRenewalRateName ELSE '' END
			+ CASE WHEN @fallbackRenewalRateID IS NOT NULL THEN @crlf + 'Fallback Renewal Rate: ' + @fallbackRenewalRateName ELSE '' END
			+ CASE WHEN @keepChangedPriceOnRenewal IS NOT NULL THEN @crlf + 'Keep Changed Price On Renewal: ' + CASE WHEN @keepChangedPriceOnRenewal = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frontEndChangePriceMin IS NOT NULL THEN @crlf + 'Front End Change Price Min: ' + CAST(@frontEndChangePriceMin AS VARCHAR(20)) ELSE '' END
			+ CASE WHEN @frontEndChangePriceMax IS NOT NULL THEN @crlf + 'Front End Change Price Max: ' + CAST(@frontEndChangePriceMax AS VARCHAR(20)) ELSE '' END;

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msgjson, 
			@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_createRateSchedule
@siteID int,
@scheduleName varchar(200),
@recordedByMemberID int,
@scheduleID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @scheduleID = null;
	DECLARE @orgID int, @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);
	select @orgID = orgID from dbo.sites where siteID = @siteID;

	IF len(@scheduleName) = 0
		RAISERROR('Schedule name invalid.',16,1);
	
	-- check to see if valid name
	SELECT TOP 1 @scheduleID = scheduleID
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleName = @scheduleName
	AND [status] <> 'D';

	IF @scheduleID is not null
		RAISERROR('Schedule name already in use.',16,1);

	INSERT INTO dbo.sub_rateSchedules (scheduleName, status, siteID)
	VALUES (@scheduleName, 'A', @siteID);

	select @scheduleID = SCOPE_IDENTITY();

	-- audit log
	DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID": '+ CAST(@scheduleID AS varchar(10)) +' }';
	SET @msgjson = 'New Subscription Rate Schedule [' + STRING_ESCAPE(@scheduleName,'json') + '] has been created.'

	EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msgjson,
			@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_createSubscription
@orgID int,
@siteid INT,
@typeID INT,
@subName varchar(300),
@reportCode varchar(15),
@scheduleID INT,
@rateTermDateFlag varchar(1),
@autoExpire BIT,
@status varchar(1),
@soldSeparately BIT,
@paymentOrder INT,
@GLAccountID INT,
@allowRateGLOverride BIT,
@activationOptionCode varchar(1),
@alternateActivationOptionCode varchar(1),
@feRawContent varchar(max),
@feCompletedRawContent varchar(max),
@feParentSubRawContent varchar(max),
@isImport bit = 0,
@recordedByMemberID int,
@subID INT OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;
	CREATE TABLE #tblMCQSubCond (conditionID int);

	DECLARE @activationOptionID int, @alternateActivationOptionID int, @subAdminResourceID INT, 
		@appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT, @completedContentID INT, 
		@completedContentSRID INT, @parentSubContentID INT, @parentSubContentSRID INT, @conditionKeyID INT, 
		@typeName varchar(100), @crlf varchar(10), @msgjson varchar(max), @GLAccountName varchar(max), @scheduleName varchar(200),
		@subActivationName varchar(100), @alternateSubActivationName varchar(100);

	SET @crlf = char(13) + char(10);

	SELECT @typeName = typeName
	from dbo.sub_Types
	where typeID = @typeID
	and siteID = @siteid;
	
	IF @typeName IS NULL
		RAISERROR('Invalid Subscription Type',16,1);

	SELECT @activationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @activationOptionCode;

	SELECT @alternateActivationOptionID = subActivationID
	from dbo.sub_activationOptions
	where subActivationCode = @alternateActivationOptionCode;

	select @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
	from dbo.sites
	where siteID = @siteID;

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	SELECT @conditionKeyID = conditionKeyID 
	FROM dbo.ams_virtualGroupConditionKeys 
	WHERE conditionKey = 'subSubscription';

	-- get conditions that target all subscriptions for this subtype
	INSERT INTO #tblMCQSubCond (conditionID)
	SELECT DISTINCT c.conditionID
	FROM dbo.ams_virtualGroupConditions AS c
	INNER JOIN dbo.cache_members_conditions_subTypes AS t 
		ON t.orgID = @orgID
		AND t.conditionID = c.conditionID
		AND t.typeID = @typeID
	INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
		AND cv.conditionKeyID = @conditionKeyID
		AND cv.conditionValue = ''
	WHERE c.orgID = @orgID
	AND c.fieldCode ='sub_entry';

	select @GLAccountName = thePathExpanded
	from dbo.fn_getRecursiveGLAccounts(@orgID)
	where GLAccountID = @GLAccountID;

	select @scheduleName = scheduleName
	from dbo.sub_rateSchedules
	where scheduleID = @scheduleID
	and siteID = @siteID;

	select @subActivationName = subActivationName
	from dbo.sub_activationOptions
	where subActivationID = @activationOptionID;

	select @alternateSubActivationName = subActivationName
	from dbo.sub_activationOptions
	where subActivationID = @alternateActivationOptionID;

	BEGIN TRAN;	
		insert into dbo.sub_subscriptions (typeID, subscriptionName, reportCode, scheduleID, autoExpire, [status], 
			soldSeparately, paymentOrder, GLAccountID, rateTermDateFlag, subActivationID, 
			subAlternateActivationID, allowRateGLAccountOverride, orgID)
		values(@typeID, @subName, @reportCode, @scheduleID, @autoExpire, @status,
			@soldSeparately, @paymentOrder, @GLAccountID, @rateTermDateFlag, @activationOptionID, 
			@alternateActivationOptionID, @allowRateGLOverride, @orgID);
			SELECT @subID = SCOPE_IDENTITY();

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feCompletedRawContent, @memberID=null, @contentID=@completedContentID OUTPUT, @siteResourceID=@completedContentSRID OUTPUT;

		exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feParentSubRawContent, @memberID=null, @contentID=@parentSubContentID OUTPUT, @siteResourceID=@parentSubContentSRID OUTPUT;

		update dbo.sub_subscriptions 
		set frontEndContentID = @contentID, 
			frontEndCompletedContentID = @completedContentID,
			frontEndParentSubContentID = @parentSubContentID
		where subscriptionID = @subID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';

		SET @msgjson = 'New Subscription ' + QUOTENAME(@subName) + ' has been created under the Subscription Type ' + QUOTENAME(@typeName) + '.' +
			CASE WHEN NULLIF(@reportCode,'') IS NOT NULL THEN @crlf + 'Report Code: ' + @reportCode ELSE '' END +
			CASE WHEN NULLIF(@scheduleName,'') IS NOT NULL THEN @crlf + 'Rate Schedule: ' + @scheduleName ELSE '' END +
			CASE 
				WHEN @rateTermDateFlag IS NOT NULL THEN @crlf + 'Term Dates: ' + 
					CASE 
						WHEN @rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
						WHEN @rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
						WHEN @rateTermDateFlag = 'C' THEN 'Calculate term end date for term length (term start date is current day)'
						ELSE ''
					END
				ELSE ''
			END +
			CASE WHEN @autoExpire IS NOT NULL THEN @crlf + 'Auto Expire Subscribers: ' + CASE WHEN @autoExpire = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN NULLIF(@status,'') IS NOT NULL THEN @crlf + 'Status: ' + CASE WHEN @status = 'A' THEN 'Active' ELSE 'Inactive' END ELSE '' END +
			CASE WHEN @soldSeparately IS NOT NULL THEN @crlf + 'Sold Separately: ' + CASE WHEN @soldSeparately = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN @paymentOrder IS NOT NULL THEN @crlf + 'Payment Order: ' + CAST(@paymentOrder AS VARCHAR(10)) ELSE '' END +
			CASE WHEN NULLIF(@GLAccountName,'') IS NOT NULL THEN @crlf + 'GL Account Name: ' + @GLAccountName ELSE '' END +
			CASE WHEN @allowRateGLOverride IS NOT NULL THEN @crlf + 'Allow Rate GL Override: ' + CASE WHEN @allowRateGLOverride = 1 THEN 'Yes' ELSE 'No' END ELSE '' END +
			CASE WHEN @subActivationName IS NOT NULL THEN @crlf + 'Activation Option: ' + @subActivationName ELSE '' END +
			CASE WHEN @alternateSubActivationName IS NOT NULL THEN @crlf + 'Alternate Activation Option: ' + @alternateSubActivationName ELSE '' END;
		
		SET @msgjson = STRING_ESCAPE(@msgjson,'json');
		
		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	IF EXISTS (SELECT 1 FROM #tblMCQSubCond)
		EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_createSubscriptionType 
@orgID int, 
@siteid int,
@typeName varchar(100),
@feRawContent varchar(max),
@feCompletedRawContent varchar(max),
@feEmailNotification varchar(max),
@isImport bit = 0,
@recordedByMemberID int,
@subTypeID INT OUTPUT

AS 

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @subAdminResourceID INT, @appCreatedContentResourceTypeID INT, @languageID INT, 
		@contentID INT, @contentSRID INT,  @completedContentID INT, @completedContentSRID INT, @subTypeResourceTypeID INT, 
		@subTypeSRID INT, @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);

	select @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
	from dbo.sites
	where siteID = @siteID;

	select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');
	select @subTypeResourceTypeID = dbo.fn_getResourceTypeId('subscriptionType');

	BEGIN TRAN;
		EXEC dbo.cms_createSiteResource @resourceTypeID=@subTypeResourceTypeID, @siteResourceStatusID=1,  
			@siteID=@siteID, @isVisible=1, @parentSiteResourceID=NULL, @siteResourceID=@subTypeSRID OUTPUT;

		INSERT INTO dbo.sub_Types (typeName, siteID, feEmailNotification, siteResourceID)
		VALUES(@typeName, @siteid, @feEmailNotification, @subTypeSRID);
			SELECT @subTypeID = SCOPE_IDENTITY();

		EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		EXEC dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@feCompletedRawContent, @memberID=null, @contentID=@completedContentID OUTPUT, @siteResourceID=@completedContentSRID OUTPUT;

		update dbo.sub_Types 
		set frontEndContentID = @contentID, 
			frontEndCompletedContentID = @completedContentID
		where typeID = @subTypeID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "TYPEID":'+CAST(@subTypeID AS varchar(10))+' }';
		SET @msgjson = 'New Subscription Type [' + @typeName + '] has been created.'
			+ CASE WHEN NULLIF(@feEmailNotification,'') IS NOT NULL THEN @crlf + 'Renewal Notification Email(s): ' + @feEmailNotification ELSE '' END;
		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBTYPE', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_deleteFrequency
@siteID int, 
@frequencyID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @frequencyName varchar(50), @msgjson varchar(max), @crlf varchar(2), @subKeyMapJSON varchar(100);

	SELECT @orgID = orgID FROM dbo.sites WHERE siteID = @siteID;
	SET @crlf = char(13) + char(10);
	
	SELECT @frequencyName = frequencyName
	FROM dbo.sub_frequencies
	WHERE frequencyID = @frequencyID;
	
	SET @subKeyMapJSON = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
	SET @msgjson = 'Frequency [' + STRING_ESCAPE(@frequencyName,'json') + '] has been Deleted.';
	
	BEGIN TRAN;
		UPDATE dbo.sub_frequencies
		SET [status] = 'D'
		WHERE frequencyID = @frequencyID
		AND siteID = @siteID
		AND [status] = 'A'
		AND isSystemRate = 0;

		IF @@ROWCOUNT > 0
			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_deleteRate
@orgID int, 
@rateID int,
@isImport bit = 0,
@recordedByMemberID int

AS 

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @depCIDList varchar(max), @conditionID int, @expression varchar(20), @datepart varchar(8), 
		@dateExpression varchar(20), @valueXML xml, @minRuleVersionID int, @ruleID int, 
		@ruleSQL varchar(max), @rateName varchar(200), @scheduleID int, 
		@scheduleName varchar(200), @msgjson varchar(max), @siteID int;
	DECLARE @subRateConditions TABLE (conditionID int);
	DECLARE @subRateDependentConditions TABLE (conditionID int);
	DECLARE @subRateNonDependentConditions TABLE (conditionID int);
	DECLARE @tblRules TABLE (ruleID int, ruleVersionID int);

	SELECT @siteID = srs.siteID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, @scheduleName = srs.scheduleName
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	WHERE rateID = @rateID;

	-- get conditions that use this rate
	INSERT INTO @subRateConditions (conditionID)
	select distinct c.conditionID
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@rateID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
		and k.conditionKey = 'subRate'
	where c.orgID = @orgID;

	-- get conditions that depend on other rates also
	INSERT INTO @subRateNonDependentConditions (conditionID)
	select distinct cv.conditionID
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
	inner join @subRateConditions as tmp on tmp.conditionID = cv.conditionID
	where ck.conditionKey = 'subRate'
	and cv.conditionValue <> cast(@rateID as varchar(10))
	group by cv.conditionID;
	
	-- get conditions that depend on this rate only
	INSERT INTO @subRateDependentConditions (conditionID)
	select conditionID 
	from @subRateConditions
		except
	select conditionID 
	from @subRateNonDependentConditions;

	select @depCIDList = COALESCE(@depCIDList + ',', '') + cast(conditionID as varchar(10)) 
	from @subRateDependentConditions;

	BEGIN TRAN;
		IF EXISTS (select 1 from @subRateNonDependentConditions) BEGIN
			-- delete this subRateID from cv
			DELETE cv
			FROM dbo.ams_virtualGroupConditionValues as cv
			INNER JOIN dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
			INNER JOIN @subRateNonDependentConditions as tmp on tmp.conditionID = cv.conditionID
			WHERE ck.conditionKey = 'subRate'
			AND cv.conditionValue = cast(@rateID as varchar(10));

			select @conditionID = min(conditionID) from @subRateNonDependentConditions;
			WHILE @conditionID IS NOT NULL BEGIN
				select @expression = null, @datepart = null, @dateExpression = null, @valueXML = null;

				select @expression = ge1.expression, @datepart = gc.[datePart], @dateExpression = ge2.expression
				from dbo.ams_virtualGroupConditions as gc
				inner join dbo.ams_virtualGroupExpressions as ge1 on ge1.expressionID = gc.expressionID
				left outer join dbo.ams_virtualGroupExpressions as ge2 on ge2.expressionID = gc.dateExpressionID
				where gc.orgID = @orgID
				and gc.conditionID = @conditionID;

				SELECT @valueXML = ISNULL((
										SELECT DISTINCT 
											k.conditionKey AS [key],
											(SELECT STUFF(( SELECT ',' + tmp.conditionValue
												FROM ams_virtualGroupConditionValues tmp
												WHERE tmp.conditionID = v.conditionID
												AND tmp.conditionKeyID = v.conditionKeyID
												ORDER BY tmp.conditionValue
												FOR XML PATH(''), TYPE
											).value('.', 'VARCHAR(MAX)'), 1, 1, '' )
											) AS [value],  
											NULLIF(v.AFID,0) AS [afid]
										FROM dbo.ams_virtualGroupConditionValues AS v
										INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = v.conditionKeyID
										WHERE v.conditionID = @conditionID
										ORDER BY 1, 2
									FOR XML RAW('value'), ROOT('values'), TYPE),'<values/>');

				EXEC dbo.ams_updateVirtualGroupCondition @orgID=@orgID, @conditionID=@conditionID, @expression=@expression, 
					@datepart=@datepart, @dateExpression=@dateExpression, @value=@valueXML, @bypassQueue=0;

				select @conditionID = min(conditionID) from @subRateNonDependentConditions where conditionID > @conditionID;
			END

			-- get rules these conditions are in
			INSERT INTO @tblRules (ruleID, ruleVersionID)
			select distinct r.ruleID, rv.ruleVersionID
			from dbo.ams_virtualGroupRules as r
			inner join dbo.ams_virtualGroupRuleVersions as rv on rv.orgID = @orgID
				and rv.ruleID = r.ruleID
			inner join dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = r.ruleID
				and rcs.ruleVersionID = rv.ruleVersionID
			inner join dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
			inner join @subRateNonDependentConditions as tmp on tmp.conditionID = rc.conditionID
			where r.orgID = @orgID;

			-- regenerate SQL for all updated rules
			select @minRuleVersionID = min(ruleVersionID) from @tblRules;
			while @minRuleVersionID is not null BEGIN
				SET @ruleID = NULL;
				
				SELECT @ruleID = ruleID
				FROM @tblRules
				WHERE ruleVersionID = @minRuleVersionID;

				EXEC dbo.ams_updateVirtualGroupRuleSQL @ruleID=@ruleID, @ruleVersionID=@minRuleVersionID;

				select @minRuleVersionID = min(ruleVersionID) from @tblRules where ruleVersionID > @minRuleVersionID;
			END
		END

		IF @depCIDList IS NOT NULL
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@depCIDList, @recordedByMemberID=@recordedByMemberID, @bypassQueue=0;

		UPDATE dbo.sub_rateFrequenciesMerchantProfiles
		SET [status] = 'D'
		WHERE rfid IN (
			SELECT DISTINCT rfid
			FROM dbo.sub_rateFrequencies rf
			WHERE rf.rateID = @rateID
		);

		UPDATE dbo.sub_rateFrequencies
		SET [status] = 'D'
		WHERE rfid IN (
			SELECT DISTINCT rfid
			FROM dbo.sub_rateFrequencies rf
			WHERE rf.rateID = @rateID
		);

		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM dbo.cms_siteResources as sr
		INNER JOIN dbo.sub_rates as r on r.siteResourceID = sr.siteResourceID
		WHERE r.rateID = @rateID;

		UPDATE dbo.sub_rates
		set fallbackRenewalRateID = null
		where fallbackRenewalRateID = @rateID;

		UPDATE dbo.sub_rates
		set [status] = 'D',
			rateOrder = NULL
		where rateID = @rateID;

		EXEC dbo.sub_reorderRates @rateID=@rateID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msgjson = STRING_ESCAPE('Rate [' + @rateName + '] under Rate Schedule [' + @scheduleName + '] has been deleted.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msgjson,
			@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_deleteRateSchedule
@siteID int,
@scheduleID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tblRates TABLE (rateID int);
	DECLARE @orgID int, @rateID int, @checkScheduleID int, @subscriptionID int, @scheduleName varchar(200), 
		@crlf varchar(10) = char(13) + char(10), @msgjson varchar(max);
	
	SELECT @orgID = orgID 
	FROM dbo.sites
	WHERE siteID = @siteID;
	
	SELECT @checkScheduleID = scheduleID, @scheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID = @scheduleID;

	IF @checkScheduleID is null
		RAISERROR('Invalid Rate Schedule.',16,1);

	SELECT TOP 1 @subscriptionID = subscriptionID
	FROM dbo.sub_subscriptions
	WHERE orgID = @orgID
	AND scheduleID = @scheduleID;

	IF @subscriptionID is not null
		RAISERROR('Rate Schedule in use by a subscription.',16,1);

	INSERT INTO @tblRates (rateID)
	select r.rateID
	from dbo.sub_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
		and sr.siteResourceID = r.siteResourceID
		and sr.siteResourceStatusID = 1
	where r.scheduleID = @scheduleID;
	
	BEGIN TRAN;
		select @rateID = min(rateID) from @tblRates;
		while @rateID is not null begin
			EXEC dbo.sub_deleteRate @orgID=@orgID, @rateID=@rateID, @recordedByMemberID=@enteredByMemberID;
			select @rateID = min(rateID) from @tblRates where rateID > @rateID;
		end

		UPDATE dbo.sub_rateSchedules
		set [status] = 'D'
		where scheduleID = @scheduleID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID":' + CAST(@checkScheduleID AS varchar(10)) +' }';
		SET @msgjson = 'Subscription Rate Schedule [' + STRING_ESCAPE(@scheduleName,'json') + '] and its rates have been deleted.'

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_deleteSubscription 
@orgID INT, 
@siteID INT,
@subscriptionID INT,
@isImport BIT = 0,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @minConditionID int, @CIDList varchar(max), @contentSRID int, @subscriptionName varchar(300), 
		@typeID int, @subscriptionType varchar(100), @msgjson varchar(max);

	SELECT @subscriptionName = ss.subscriptionName, @typeID = st.typeID, @subscriptionType = st.typeName
	FROM dbo.sub_subscriptions as ss
	INNER JOIN dbo.sub_types as st ON st.siteID = @siteID
		AND ss.typeID = st.typeID
	WHERE ss.subscriptionID = @subscriptionID;

	-- is sub not deleted?
	IF EXISTS (
		SELECT s.subscriptionID 
		FROM dbo.sub_subscribers s
		inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode <> 'D'
		WHERE s.subscriptionID = @subscriptionID)
		GOTO on_done;

	-- delete any conditions that depend on this subscription
	select @CIDList = COALESCE(@CIDList + ',', '') + cast(c.conditionID as varchar(10)) 
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@subscriptionID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubscription'
	where c.orgID = @orgID

	BEGIN TRAN;
		IF @CIDList is not null
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList, 
				@recordedByMemberID=@recordedByMemberID, @byPassQueue=0;

		-- mark sub as deleted
		UPDATE dbo.sub_subscriptions
		set [status] = 'D'
		where subscriptionID = @subscriptionID;

		-- delete content object
		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_subscriptions subs
		inner join dbo.cms_content cc on subs.frontEndContentID = cc.contentID
			and subs.subscriptionID = @subscriptionID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_subscriptions subs
		inner join dbo.cms_content cc on subs.frontEndCompletedContentID = cc.contentID
			and subs.subscriptionID = @subscriptionID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		DELETE FROM dbo.hooks_webhooks_subscriptionStatusChange
		WHERE subscriptionID = @subscriptionID;

		IF @@ROWCOUNT > 0 BEGIN
			DELETE FROM dbo.hooks_webhooks_subscriptionStatusChangeRules
			WHERE ruleID NOT IN (
				SELECT DISTINCT ruleID FROM dbo.hooks_webhooks_subscriptionStatusChange
			);
		END

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';
		SET @msgjson = STRING_ESCAPE('Subscription ' + QUOTENAME(@subscriptionName) + ' under the Subscription Type ' + QUOTENAME(@subscriptionType) + ' has been deleted.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_deleteSubscriptionType
@orgID INT, 
@siteid INT,
@subTypeID INT,
@isImport BIT = 0,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @minConditionID int, @contentSRID int, @CIDList varchar(max), @msg varchar(max),
		@subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);

	IF EXISTS (
		SELECT subs.subscriptionID
		FROM dbo.sub_Types st
		INNER join dbo.sub_subscriptions subs on subs.typeID = st.typeID AND subs.status ='A'
		WHERE siteID = @siteid    
		and st.typeID = @subTypeID)
		GOTO on_done;

	SELECT @msg = 'Subscription Type [' + STRING_ESCAPE(typeName,'json') + '] deleted.'
	FROM dbo.sub_Types
	WHERE typeID = @subTypeID
	AND siteID = @siteID;
	
	SET @subKeyMapJSON = '{ "TYPEID":'+CAST(@subTypeID AS varchar(10))+' }';

	select @CIDList = COALESCE(@CIDList + ',', '') + cast(c.conditionID as varchar(10)) 
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@subTypeID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubType';
	
	BEGIN TRAN;
		-- delete any conditions that depend on this subscription type
		IF @CIDList is not null
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList, 
				@recordedByMemberID=@recordedByMemberID, @bypassQueue=0;

		-- mark type as deleted	
		UPDATE dbo.sub_Types
		set [status] = 'D'
		where typeID = @subTypeID
		and siteID = @siteid;

		-- delete content object
		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t
		inner join dbo.cms_content cc on t.frontEndContentID = cc.contentID
			and t.typeID = @subTypeID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t
		inner join dbo.cms_content cc on t.frontEndCompletedContentID = cc.contentID
			and t.typeID = @subTypeID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t			
		inner join dbo.cms_siteResources sr on t.siteResourceID = sr.siteResourceID
			and t.typeID = @subTypeID;

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBTYPE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_importSubscriptionStructure
@siteID int,
@recordedByMemberID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubFrequencies') IS NOT NULL
		DROP TABLE #tmpSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpSets') IS NOT NULL
		DROP TABLE #tmpSets;
	IF OBJECT_ID('tempdb..#tmpSubTypes') IS NOT NULL
		DROP TABLE #tmpSubTypes;
	IF OBJECT_ID('tempdb..#tmpSubscriptions') IS NOT NULL
		DROP TABLE #tmpSubscriptions;
	IF OBJECT_ID('tempdb..#tmpSubAddOns') IS NOT NULL
		DROP TABLE #tmpSubAddOns;
	IF OBJECT_ID('tempdb..#tmpSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpSubRates') IS NOT NULL
		DROP TABLE #tmpSubRates;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequencies') IS NOT NULL
		DROP TABLE #tmpSubRateFrequencies;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequenciesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpSubRateFrequenciesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpSiteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpNewSubRateGroups') IS NOT NULL
		DROP TABLE #tmpNewSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpNewSubContentObjs') IS NOT NULL
		DROP TABLE #tmpNewSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpUpdateSubContentObjs') IS NOT NULL
		DROP TABLE #tmpUpdateSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubFrequencies') IS NOT NULL
		DROP TABLE #tmpDeleteSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpDeleteSets') IS NOT NULL
		DROP TABLE #tmpDeleteSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubAddOns') IS NOT NULL
		DROP TABLE #tmpDeleteSubAddOns;
	IF OBJECT_ID('tempdb..#tmpDeleteSubTypes') IS NOT NULL
		DROP TABLE #tmpDeleteSubTypes;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRates') IS NOT NULL
		DROP TABLE #tmpDeleteSubRates;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqs') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptions') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptions;
	IF OBJECT_ID('tempdb..#tmpLogLabelsType') IS NOT NULL
		DROP TABLE #tmpLogLabelsType;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSub') IS NOT NULL
		DROP TABLE #tmpLogLabelsSub;
	IF OBJECT_ID('tempdb..#tmpLogLabelsAddOn') IS NOT NULL
		DROP TABLE #tmpLogLabelsAddOn;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSet') IS NOT NULL
		DROP TABLE #tmpLogLabelsSet;
	IF OBJECT_ID('tempdb..#tmpLogLabelsFreq') IS NOT NULL
		DROP TABLE #tmpLogLabelsFreq;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSched') IS NOT NULL
		DROP TABLE #tmpLogLabelsSched;
	IF OBJECT_ID('tempdb..#tmpLogLabelsRate') IS NOT NULL
		DROP TABLE #tmpLogLabelsRate;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
		DROP TABLE #tmpAuditLogMessages;

	CREATE TABLE #tmpSubFrequencies (syncFrequencyID int, frequencyID int, frequencyName varchar(50), frequency int, frequencyShortName varchar(10), 
		[uid] uniqueidentifier, rateRequired bit, hasInstallments bit, monthlyInterval int, isSystemRate bit, [status] char(1), finalAction char(1));
	CREATE TABLE #tmpSets (syncSetID int, setID int, setName varchar(100), [status] char(1), [uid] uniqueidentifier, finalAction char(1));
	CREATE TABLE #tmpSubRateSchedules (syncScheduleID int, scheduleID int, scheduleName varchar(200), [status] char(1), [uid] uniqueidentifier, finalAction char(1));
	CREATE TABLE #tmpSubTypes (syncTypeID int, typeID int, typeName varchar(100), [status] varchar(1), [uid] uniqueidentifier, typeCode varchar(30), 
		frontEndContent varchar(max), frontEndCompletedContent varchar(max), feEmailNotification varchar(400), frontEndContentID int, 
		frontEndCompletedContentID int, finalAction char(1));
	CREATE TABLE #tmpSubscriptions (syncSubscriptionID int, syncTypeID int, subscriptionID int, typeID int, [uid] uniqueidentifier, syncTypeUID uniqueidentifier,
		typeUID uniqueidentifier, subscriptionName varchar(300), oldSubscriptionName varchar(300), syncScheduleID int, scheduleUID uniqueidentifier, autoExpire bit, [status] char(1), 
		soldSeparately bit, GLAccountID int, rateTermDateFlag char(1), paymentOrder int, reportCode varchar(15), subActivationID int, 
		subAlternateActivationID int, allowRateGLAccountOverride bit, frontEndContent varchar(max), frontEndCompletedContent varchar(max), frontEndParentSubContent varchar(max),
		frontEndContentID int, frontEndCompletedContentID int, frontEndParentSubContentID int, finalAction char(1));
	CREATE TABLE #tmpSubRates (syncRateID int, syncScheduleID int, rateID int, scheduleID int, scheduleUID uniqueidentifier, [status] char(1), 
		rateStartDate datetime, rateEndDate datetime, rateStartDateAFID int, rateAFStartDate datetime, rateAFEndDate datetime, 
		termStartDate datetime, termEndDate datetime, termStartDateAFID int, termEndDateAFID int, termAFStartDate datetime, 
		termAFEndDate datetime, graceEndDate datetime, graceAFID int, rateName varchar(200), rateEndDateAFID int, 
		rateAdvanceOnTermEnd int, [uid] uniqueidentifier, isRenewalRate bit, forceUpfront bit, reportCode varchar(15),
		GLAccountID int, frontEndAllowChangePrice bit, linkedNonRenewalRateUID uniqueidentifier, fallbackRenewalRateUID uniqueidentifier, 
		keepChangedPriceOnRenewal bit, frontEndChangePriceMin decimal(18,2), frontEndChangePriceMax decimal(18,2),
		recogStartDate datetime, recogEndDate datetime, recogStartDateAFID int, recogEndDateAFID int, 
		recogAFStartDate datetime, recogAFEndDate datetime, rateOrder int, finalAction char(1));
	CREATE TABLE #tmpSubRateFrequencies (syncRFID int, rfid int, rateUID uniqueidentifier, frequencyUID uniqueidentifier, rateAmt decimal(18,2), 
		[status] varchar(1), numInstallments int, allowFrontEnd bit, finalAction char(1));
	CREATE TABLE #tmpSubRateFrequenciesMerchantProfiles (syncRFMPID int, rfmpid int, syncRFID int, profileID int, [status] char(1), finalAction char(1));
	CREATE TABLE #tmpSiteSubRateGroups (resourceRightsID int, rateID int, rateUID uniqueidentifier, groupID int);
	CREATE TABLE #tmpNewSubRateGroups (autoID int IDENTITY(1,1), syncRateID int, rateUID uniqueidentifier, groupID int, include bit);
	CREATE TABLE #tmpSubAddOns (syncAddonID int, addonID int, syncSubscriptionID int, subscriptionUID uniqueidentifier, syncChildSetID int, 
		syncChildSetUID uniqueidentifier, orderNum smallint, minAllowed smallint, maxAllowed smallint, useAcctCodeInSet bit, 
		useTermEndDateInSet bit, PCnum smallint, PCPctOffEach smallint, 
		frontEndAllowSelect bit, frontEndAllowChangePrice bit, frontEndContent varchar(max), frontEndContentID int, 
		frontEndAddAdditional bit, finalAction char(1));
	CREATE TABLE #tmpSubscriptionSets (syncSubscriptionSetID int, subscriptionSetID int, subscriptionUID uniqueidentifier, setUID uniqueidentifier, orderNum int, finalAction char(1));
	CREATE TABLE #tmpNewSubContentObjs (autoID int IDENTITY(1,1), rawContent varchar(max), itemID int, itemType varchar(10));
	CREATE TABLE #tmpUpdateSubContentObjs (contentID int, rawContent varchar(max), areaCode varchar(100), fieldName varchar(100), refID int);
	CREATE TABLE #tmpDeleteSubFrequencies (frequencyID int);
	CREATE TABLE #tmpDeleteSets (setID int);
	CREATE TABLE #tmpDeleteSubscriptionSets (subscriptionSetID int);
	CREATE TABLE #tmpDeleteSubAddOns (addonID int);
	CREATE TABLE #tmpDeleteSubTypes (typeID int);
	CREATE TABLE #tmpDeleteSubscriptions (subscriptionID int);
	CREATE TABLE #tmpDeleteSubRateSchedules (scheduleID int);
	CREATE TABLE #tmpDeleteSubRates (rateID int);
	CREATE TABLE #tmpDeleteSubRateFreqs (rfid int);
	CREATE TABLE #tmpDeleteSubRateFreqMerchantProfiles (rfmpid int);
	CREATE TABLE #tmpDeleteSubRateGroups (resourceRightsID int, siteResourceID int);

	CREATE TABLE #tmpLogLabelsType (typeID int PRIMARY KEY, typeName varchar(100));
	CREATE TABLE #tmpLogLabelsSub (subscriptionID int PRIMARY KEY, subscriptionName varchar(300), typeID int, typeName varchar(100));
	CREATE TABLE #tmpLogLabelsAddOn (addonID int PRIMARY KEY, subscriptionID varchar(300), childSetID int);
	CREATE TABLE #tmpLogLabelsSet (setID int PRIMARY KEY, setName varchar(100));
	CREATE TABLE #tmpLogLabelsFreq (frequencyID int PRIMARY KEY, frequencyName varchar(50));
	CREATE TABLE #tmpLogLabelsSched (scheduleID int PRIMARY KEY, scheduleName varchar(200));
	CREATE TABLE #tmpLogLabelsRate (rateID int PRIMARY KEY, rateName varchar(200), scheduleID int, scheduleName varchar(200));
	CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), areaCode varchar(100), msg VARCHAR(max), refID int, actionType varchar(10));
	CREATE TABLE #tmpAuditLogMessages (rowID INT IDENTITY(1,1), refID int, msg varchar(max), col varchar(max));

	DECLARE @orgID int, @orgcode varchar(10), @sitecode varchar(10), @defaultLanguageID int, @minTypeID int, @newSubTypeID int,
		@typeName varchar(100), @typeCode varchar(30), @feRawContent varchar(max), @feCompletedRawContent varchar(max), 
		@feEmailNotification varchar(max), @uid uniqueidentifier, @minContentID int, @rawContent varchar(max), @minRateID int, 
		@scheduleID int, @rateName varchar(100), @reportCode varchar(15), @status char(1), @rateStartDate datetime, 
		@rateEndDate datetime, @rateStartAFID int, @rateEndAFID int, @termStartDate datetime, @termEndDate datetime, 
		@termStartAFID int, @termEndAFID int, @graceEndDate datetime, @graceEndAFID int, @recogStartDate datetime, 
		@recogEndDate datetime, @recogStartAFID int, @recogEndAFID int, @rateAdvanceOnTermEnd int, @isRenewalRate int, 
		@forceUpfront int, @accountID int, @frontEndAllowChangePrice int, @linkedNonRenewalRateID int, @fallbackRenewalRateID int, 
		@keepChangedPriceOnRenewal int, @frontEndChangePriceMin decimal(18,2), @frontEndChangePriceMax decimal(18,2), @newRateID int, 
		@newSiteResourceID int, @rateAFStartDate datetime, @rateAFEndDate datetime, @termAFStartDate datetime, @termAFEndDate datetime, 
		@recogAFStartDate datetime, @recogAFEndDate datetime, @minSubID int, @minSetID int,
		@typeID int, @subName varchar(300), @newSubsID int, @rateTermDateFlag varchar(1), @autoExpire bit,
		@soldSeparately bit, @paymentOrder int, @GLAccountID int, @allowRateGLOverride bit, @activationOptionCode varchar(1),
		@alternateActivationOptionCode varchar(1), @subAdminResourceID int, @appCreatedContentResourceTypeID int,
		@contentID int, @contentSRID int, @minAutoID int, @itemID int, @itemType varchar(10), @minResourceRightsID int,
		@include bit, @groupID int, @siteResourceID int, @activeTypeGroupID int, @waitingTypeGroupID int, @expiredTypeGroupID int, @renewableTypeGroupID int, 
		@pendingTypeGroupID int, @activeGroupID int, @waitingGroupID int, @expiredGroupID int, @renewableGroupID int, @pendingGroupID int, @feParentSubRawContent varchar(max),
		@crlf varchar(10), @thisAreaCode varchar(100), @minRefID int, @msgjson varchar(max), @subKeyMapJSON varchar(100), @actionCount int, @changesCount int, @prefixMsg varchar(max),
		@actionType varchar(10), @groupPathExpanded varchar(max), @rateID int;

	DECLARE @insertedEntries TABLE (refID INT);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = o.orgID, @orgcode = o.orgcode, @sitecode = s.sitecode,
		@defaultLanguageID = s.defaultLanguageID, @subAdminResourceID = s.subscriptionAdminSiteResourceID
	FROM dbo.sites as s
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

	-- frequencies
	INSERT INTO #tmpSubFrequencies (syncFrequencyID, frequencyID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, 
		hasInstallments, monthlyInterval, isSystemRate, [status], finalAction)
	select distinct ssf.frequencyID, sf.frequencyID, ssf.frequencyName, ssf.frequency, ssf.frequencyShortName, ssf.[uid], 
		ssf.rateRequired, ssf.hasInstallments, ssf.monthlyInterval, ssf.isSystemRate, ssf.[status], ssf.finalAction
	from datatransfer.dbo.sync_sub_frequencies as ssf
	left outer join dbo.sub_frequencies as sf on sf.siteID = @siteID and sf.[uid] = ssf.[uid]
	where ssf.siteID = @siteID
	and ssf.finalAction in ('A','C');

	-- sets
	INSERT INTO #tmpSets (syncSetID, setID, setName, [status], [uid], finalAction)
	select distinct ss.setID, s.setID, ss.setName, ss.[status], ss.uid, ss.finalAction
	from datatransfer.dbo.sync_sub_sets as ss
	left outer join dbo.sub_sets as s on s.siteID = @siteID and s.[uid] = ss.[uid] and s.status <> 'D'
	where ss.siteID = @siteID
	and ss.finalAction in ('A','C');

	-- subTypes
	INSERT INTO #tmpSubTypes (syncTypeID, typeID, typeName, [status], [uid], typeCode, frontEndContent, frontEndCompletedContent, 
		feEmailNotification, frontEndContentID, frontEndCompletedContentID, finalAction)
	select distinct st.typeID, t.typeID, st.typeName, st.[status], st.[uid], st.typeCode, st.frontEndContent, st.frontEndCompletedContent,
		st.feEmailNotification, t.frontEndContentID, t.frontEndCompletedContentID, st.finalAction
	from datatransfer.dbo.sync_sub_types as st
	left outer join dbo.sub_types as t on t.siteID = @siteID and t.status <> 'D' and t.[uid] = st.[uid]
	where st.siteID = @siteID
	and st.finalAction in ('A','C');

	-- rate schedules
	INSERT INTO #tmpSubRateSchedules (syncScheduleID, scheduleID, scheduleName, [status], [uid], finalAction)
	select distinct srs.scheduleID, rs.scheduleID, srs.scheduleName, srs.[status], srs.[uid], srs.finalAction
	from datatransfer.dbo.sync_sub_rateSchedules as srs
	left outer join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = srs.[uid] and rs.status <> 'D'
	where srs.siteID = @siteID
	and srs.finalAction in ('A','C');

	-- rates
	INSERT INTO #tmpSubRates (syncRateID, syncScheduleID, rateID, scheduleID, scheduleUID, [status], 
		rateStartDate, rateEndDate, rateStartDateAFID, rateAFStartDate, rateAFEndDate, 
		termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, 
		termAFEndDate, graceEndDate, graceAFID, rateName, rateEndDateAFID, 
		rateAdvanceOnTermEnd, [uid], isRenewalRate, forceUpfront, reportCode,
		GLAccountID, frontEndAllowChangePrice, linkedNonRenewalRateUID, fallbackRenewalRateUID, 
		keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax,
		recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, 
		recogAFStartDate, recogAFEndDate, rateOrder, finalAction)
	select distinct sync_sr.rateID, sync_sr.scheduleID, r.rateID, r.scheduleID, sync_srs.[uid], sync_sr.[status],
		sync_sr.rateStartDate, sync_sr.rateEndDate, sync_rateStartDateAFID.useID, 
		sync_sr.rateAFStartDate, sync_sr.rateAFEndDate, sync_sr.termStartDate, sync_sr.termEndDate, 
		sync_termStartDateAFID.useID, sync_termEndDateAFID.useID, sync_sr.termAFStartDate, 
		sync_sr.termAFEndDate, sync_sr.graceEndDate, sync_graceAFID.useID, sync_sr.rateName, 
		sync_rateEndDateAFID.useID, sync_sr.rateAdvanceOnTermEnd, sync_sr.[uid], 
		sync_sr.isRenewalRate, sync_sr.forceUpfront, sync_sr.reportCode, sync_GLAccountID.useID, 
		sync_sr.frontEndAllowChangePrice, sync_linkedNonRenewalRateID.[uid], sync_fallbackRenewalRateID.[uid], 
		sync_sr.keepChangedPriceOnRenewal, sync_sr.frontEndChangePriceMin, sync_sr.frontEndChangePriceMax, sync_sr.recogStartDate, 
		sync_sr.recogEndDate, sync_recogStartDateAFID.useID, sync_recogEndDateAFID.useID, sync_sr.recogAFStartDate, 
		sync_sr.recogAFEndDate, sync_sr.rateOrder, sync_sr.finalAction
	from dataTransfer.dbo.sync_sub_rates as sync_sr
	inner join datatransfer.dbo.sync_sub_rateSchedules as sync_srs on sync_srs.siteID = @siteID and sync_srs.scheduleID = sync_sr.scheduleID
	left outer join dbo.sub_rates as r on r.[uid] = sync_sr.[uid] and r.[status] <> 'D'
	left outer join datatransfer.dbo.sync_sub_supporting as sync_rateStartDateAFID on sync_rateStartDateAFID.siteID = @siteID 
		and sync_rateStartDateAFID.cat = 'af' 
		and sync_rateStartDateAFID.itemID = sync_sr.rateStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_termStartDateAFID on sync_termStartDateAFID.siteID = @siteID 
		and sync_termStartDateAFID.cat = 'af' 
		and sync_termStartDateAFID.itemID = sync_sr.termStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_termEndDateAFID on sync_termEndDateAFID.siteID = @siteID 
		and sync_termEndDateAFID.cat = 'af' 
		and sync_termEndDateAFID.itemID = sync_sr.termEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_graceAFID on sync_graceAFID.siteID = @siteID 
		and sync_graceAFID.cat = 'af' 
		and sync_graceAFID.itemID = sync_sr.graceAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_rateEndDateAFID on sync_rateEndDateAFID.siteID = @siteID 
		and sync_rateEndDateAFID.cat = 'af' 
		and sync_rateEndDateAFID.itemID = sync_sr.rateEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_recogStartDateAFID on sync_recogStartDateAFID.siteID = @siteID 
		and sync_recogStartDateAFID.cat = 'af' 
		and sync_recogStartDateAFID.itemID = sync_sr.recogStartDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_recogEndDateAFID on sync_recogEndDateAFID.siteID = @siteID 
		and sync_recogEndDateAFID.cat = 'af' 
		and sync_recogEndDateAFID.itemID = sync_sr.recogEndDateAFID
	left outer join datatransfer.dbo.sync_sub_supporting as sync_GLAccountID on sync_GLAccountID.siteID = @siteID 
		and sync_GLAccountID.cat = 'gl' 
		and sync_GLAccountID.itemID = sync_sr.GLAccountID
	left outer join dataTransfer.dbo.sync_sub_rates as sync_linkedNonRenewalRateID on sync_linkedNonRenewalRateID.siteID = @siteID 
		and sync_linkedNonRenewalRateID.rateID = sync_sr.linkedNonRenewalRateID
	left outer join dataTransfer.dbo.sync_sub_rates as sync_fallbackRenewalRateID on sync_fallbackRenewalRateID.siteID = @siteID 
		and sync_fallbackRenewalRateID.rateID = sync_sr.fallbackRenewalRateID
	where sync_sr.siteID = @siteID
	and sync_sr.finalAction in ('A','C');

	-- subscriptions
	INSERT INTO #tmpSubscriptions (syncSubscriptionID, syncTypeID, subscriptionID, typeID, subscriptionName,
		[uid], syncTypeUID, typeUID, syncScheduleID, scheduleUID, autoExpire, [status], soldSeparately, 
		GLAccountID, rateTermDateFlag, paymentOrder, reportCode, subActivationID, 
		subAlternateActivationID, allowRateGLAccountOverride, frontEndContent, frontEndCompletedContent, 
		frontEndParentSubContent, oldSubscriptionName, frontEndContentID, frontEndCompletedContentID, 
		frontEndParentSubContentID, finalAction)
	select distinct s.subscriptionID, s.typeID, sub.subscriptionID, sub.typeID, s.subscriptionName,
		s.[uid], t.[uid], st.[uid], s.scheduleID, rs.[uid], s.autoExpire, s.[status], s.soldSeparately, 
		ss.useID, s.rateTermDateFlag, s.paymentOrder, s.reportCode, s.subActivationID, 
		s.subAlternateActivationID, s.allowRateGLAccountOverride, s.frontEndContent, s.frontEndCompletedContent,
		s.frontEndParentSubContent, sub.subscriptionName, sub.frontEndContentID, sub.frontEndCompletedContentID, 
		sub.frontEndParentSubContentID, s.finalAction
	from datatransfer.dbo.sync_sub_subscriptions as s
	inner join datatransfer.dbo.sync_sub_types as t on t.typeID = s.typeID and t.siteID = @siteID
	inner join datatransfer.dbo.sync_sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = s.scheduleID
	left outer join datatransfer.dbo.sync_sub_supporting as ss on ss.siteID = @siteID and ss.cat = 'gl' and ss.itemID = s.GLAccountID
	left outer join dbo.sub_subscriptions as sub
		inner join dbo.sub_types as st on st.typeID = sub.typeID and st.siteID = @siteID
		on sub.[uid] = s.[uid]
	where s.siteID = @siteID
	and s.finalAction in ('A','C');

	-- sub addons
	INSERT INTO #tmpSubAddOns (syncAddonID, addonID, syncSubscriptionID, subscriptionUID, syncChildSetID, syncChildSetUID, 
		orderNum, minAllowed, maxAllowed, useAcctCodeInSet, useTermEndDateInSet, PCnum, PCPctOffEach, frontEndAllowSelect, 
		frontEndAllowChangePrice, frontEndContent, frontEndContentID, frontEndAddAdditional, finalAction)
	select distinct sao.addonID, ao.addonID, sao.subscriptionID, subs.[uid], sao.childSetID, cs.[uid], sao.orderNum, 
		sao.minAllowed, sao.maxAllowed, sao.useAcctCodeInSet, sao.useTermEndDateInSet, sao.PCnum, sao.PCPctOffEach, 
		sao.frontEndAllowSelect, sao.frontEndAllowChangePrice, sao.frontEndContent, ao.frontEndContentID, 
		sao.frontEndAddAdditional, sao.finalAction
	from datatransfer.dbo.sync_sub_addons as sao
	inner join datatransfer.dbo.sync_sub_sets as cs on cs.setID = sao.childSetID 
		and cs.siteID = @siteID
		and cs.status <> 'D'
	inner join datatransfer.dbo.sync_sub_subscriptions as subs on subs.subscriptionID = sao.subscriptionID 
		and subs.siteID = @siteID
		and subs.status <> 'D'
	left outer join dbo.sub_addons as ao on ao.addonID = sao.useID
	where sao.siteID = @siteID
	and sao.finalAction in ('A','C');

	-- new rate frequencies
	INSERT INTO #tmpSubRateFrequencies (syncRFID, rfid, rateUID, frequencyUID, rateAmt, [status], numInstallments, allowFrontEnd, finalAction)
	select distinct rf.rfid, srf.rfid, r.[uid], f.[uid], rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd, rf.finalAction
	from datatransfer.dbo.sync_sub_rateFrequencies as rf
	inner join datatransfer.dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rf.rateID and r.status <> 'D'
	inner join datatransfer.dbo.sync_sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID 
		and f.status <> 'D'
	left outer join dbo.sub_rateFrequencies as srf on srf.rfid = rf.useID
	where rf.siteID = @siteID
	and rf.status <> 'D'
	and rf.finalAction in ('A','C');

	-- new rate frequency merchant profiles
	INSERT INTO #tmpSubRateFrequenciesMerchantProfiles (syncRFMPID, rfmpid, syncRFID, profileID, [status], finalAction)
	select distinct srfmp.rfmpid, rfmp.rfmpid, srfmp.rfid, ss.useID, srfmp.[status], srfmp.finalAction
	from datatransfer.dbo.sync_sub_rateFrequenciesMerchantProfiles as srfmp
	inner join datatransfer.dbo.sync_sub_rateFrequencies as srf on srf.siteID = @siteID and srf.rfid = srfmp.rfid
	inner join datatransfer.dbo.sync_sub_supporting as ss on ss.siteID = @siteID and ss.cat = 'mp' and ss.itemID = srfmp.profileID
	left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = srfmp.useID and rfmp.[status] <> 'D'
	where srfmp.siteID = @siteID
	and srfmp.finalAction = 'A';

	-- site rate groups
	INSERT INTO #tmpSiteSubRateGroups (resourceRightsID, rateID, rateUID, groupID)
	select srr.resourceRightsID, r.rateID, r.[uid], g.groupID
	from dbo.sub_rates as r
	inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID and rs.siteID = @siteID
	inner join dbo.cms_siteResourceRights as srr on srr.resourceID = r.siteResourceID and srr.siteID = @siteID and srr.functionID = @qualifyFID
	inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = srr.groupID and g.status <> 'D'
	where r.status <> 'D';

	-- sync rate groups
	INSERT INTO #tmpNewSubRateGroups (syncRateID, rateUID, groupID, include)
	select rateID, rateUID, useGroupID, include
	from datatransfer.dbo.sync_sub_rateGroups
	where siteID = @siteID
	and useGroupID is not null
	and finalAction = 'A';

	-- subscription sets
	INSERT INTO #tmpSubscriptionSets (syncSubscriptionSetID, subscriptionSetID, subscriptionUID, setUID, orderNum, finalAction)
	select distinct ss.subscriptionSetID, ss.useID, subs.[uid], s.[uid], ss.orderNum, ss.finalAction
	from datatransfer.dbo.sync_sub_subscriptionSets as ss
	inner join datatransfer.dbo.sync_sub_sets as s on s.setID = ss.setID and s.siteID = @siteID
	inner join datatransfer.dbo.sync_sub_subscriptions as subs on subs.siteID = @siteID and subs.subscriptionID = ss.subscriptionID
	where ss.siteID = @siteID
	and ss.finalAction in ('A','C');
	
	-- delete frequencies
	INSERT INTO #tmpDeleteSubFrequencies (frequencyID)
	select distinct sf.frequencyID
	from dbo.sub_frequencies as sf
	left outer join dataTransfer.dbo.sync_sub_frequencies as ssf on ssf.siteID = @siteID and ssf.uid = sf.uid
	where sf.siteID = @siteID
	and sf.[status] <> 'D'
	and ssf.frequencyID is null;

	-- delete sets
	INSERT INTO #tmpDeleteSets (setID)
	select distinct s.setID
	from dbo.sub_sets as s
	left outer join dataTransfer.dbo.sync_sub_sets as ss on ss.siteID = @siteID and ss.uid = s.uid
	where s.siteID = @siteID
	and ss.setID is null;

	IF EXISTS (select 1 from #tmpDeleteSets) BEGIN
		-- delete sub sets
		INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
		select ss.subscriptionSetID
		from dbo.sub_subscriptionSets as ss
		inner join #tmpDeleteSets as tmp on tmp.setID = ss.setID;

		-- delete sub addons
		INSERT INTO #tmpDeleteSubAddOns (addonID)
		select sa.addonID
		from dbo.sub_addons as sa
		inner join #tmpDeleteSets as tmp on tmp.setID = sa.childSetID;
	END

	-- delete subTypes
	INSERT INTO #tmpDeleteSubTypes (typeID)
	select distinct t.typeID
	from dbo.sub_types as t
	left outer join dataTransfer.dbo.sync_sub_types as st on st.siteID = @siteID and st.uid = t.uid
	where t.siteID = @siteID
	and t.[status] <> 'D'
	and st.typeID is null;

	-- delete subscriptions
	INSERT INTO #tmpDeleteSubscriptions (subscriptionID)
	select s.subscriptionID
	from dbo.sub_subscriptions as s
	inner join #tmpDeleteSubTypes as tmp on tmp.typeID = s.typeID
	where s.[status] <> 'D';

	INSERT INTO #tmpDeleteSubscriptions (subscriptionID)
	select distinct s.subscriptionID
	from dbo.sub_subscriptions as s
	inner join dbo.sub_types as st on st.siteID = @siteID and st.typeID = s.typeID and st.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_subscriptions as ss on ss.siteID = @siteID and ss.uid = s.uid
	where ss.subscriptionID is null
	and s.[status] <> 'D'
		except
	select subscriptionID
	from #tmpDeleteSubscriptions;

	-- delete sub sets
	INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
	select s.subscriptionSetID
	from dbo.sub_subscriptionSets as s
	inner join dbo.sub_sets as st on st.setID = s.setID and st.siteID = @siteID
	left outer join dataTransfer.dbo.sync_sub_subscriptionSets as ss on ss.siteID = @siteID and ss.useID = s.subscriptionSetID
	where ss.subscriptionSetID is null
		except
	select subscriptionSetID
	from #tmpDeleteSubscriptionSets;

	INSERT INTO #tmpDeleteSubscriptionSets (subscriptionSetID)
	select ss.subscriptionSetID
	from dbo.sub_subscriptionSets as ss
	inner join #tmpDeleteSubscriptions as tmp on tmp.subscriptionID = ss.subscriptionID
		except
	select subscriptionSetID
	from #tmpDeleteSubscriptionSets;

	-- delete sub addons
	INSERT INTO #tmpDeleteSubAddOns (addonID)
	select sa.addonID
	from dbo.sub_addons as sa
	inner join #tmpDeleteSubscriptions as tmp on tmp.subscriptionID = sa.subscriptionID
		except
	select addonID
	from #tmpDeleteSubAddOns;

	INSERT INTO #tmpDeleteSubAddOns (addonID)
	select distinct ao.addonID
	from dbo.sub_addons as ao
	inner join dbo.sub_sets as s on s.setID = ao.childSetID 
		and s.siteID = @siteID
		and s.status <> 'D'
	inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ao.subscriptionID and subs.status <> 'D'
	left outer join datatransfer.dbo.sync_sub_addons as sao on sao.siteID = @siteID and sao.useID = ao.addonID
	where sao.addonID is null
		except
	select addonID
	from #tmpDeleteSubAddOns;

	-- delete rate schedules
	INSERT INTO #tmpDeleteSubRateSchedules (scheduleID)
	select distinct rs.scheduleID
	from dbo.sub_rateSchedules as rs
	left outer join dataTransfer.dbo.sync_sub_rateSchedules as srs on srs.siteID = @siteID and srs.uid = rs.uid
	where rs.siteID = @siteID
	and srs.scheduleID is null
	and rs.[status] <> 'D';

	-- delete rates
	INSERT INTO #tmpDeleteSubRates (rateID)
	select distinct r.rateID
	from dbo.sub_rates as r
	inner join #tmpDeleteSubRateSchedules as rs on rs.scheduleID = r.scheduleID
	where r.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRates (rateID)
	select distinct r.rateID
	from dbo.sub_rates as r
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rates as sr on sr.siteID = @siteID and sr.uid = r.uid
	where sr.rateID is null
	and r.[status] <> 'D'
		except
	select rateID
	from #tmpDeleteSubRates;

	-- delete rate freqs
	INSERT INTO #tmpDeleteSubRateFreqs (rfid)
	select distinct rf.rfid
	from dbo.sub_rateFrequencies as rf
	inner join #tmpDeleteSubRates as tmp on tmp.rateID = rf.rateID
	where rf.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRateFreqs (rfid)
	select distinct rf.rfid
	from dbo.sub_rateFrequencies as rf
	inner join dbo.sub_rates as r on r.rateID = rf.rateID
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rateFrequencies as srf on srf.siteID = @siteID and srf.useID = rf.rfid
	where srf.rfid is null
	and rf.[status] <> 'D'
		except
	select rfid
	from #tmpDeleteSubRateFreqs;

	-- delete rate freq merchant profiles
	INSERT INTO #tmpDeleteSubRateFreqMerchantProfiles (rfmpid)
	select distinct rfmp.rfmpid
	from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
	inner join #tmpDeleteSubRateFreqs as tmp on tmp.rfid = rfmp.rfid
	where rfmp.[status] <> 'D';

	INSERT INTO #tmpDeleteSubRateFreqMerchantProfiles (rfmpid)
	select distinct rfmp.rfmpid
	from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
	inner join dbo.sub_rateFrequencies as rf on rf.rfid = rfmp.rfid and rf.[status] <> 'D'
	inner join dbo.sub_rates as r on r.rateID = rf.rateID
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID and rs.[status] <> 'D'
	left outer join dataTransfer.dbo.sync_sub_rateFrequenciesMerchantProfiles as srfmp on srfmp.siteID = @siteID and srfmp.useID = rfmp.rfmpid
	where srfmp.rfmpid is null
	and rfmp.[status] <> 'D'
		except
	select rfmpid
	from #tmpDeleteSubRateFreqMerchantProfiles;

	-- remove rate groups
	INSERT INTO #tmpDeleteSubRateGroups (resourceRightsID, siteResourceID)
	select distinct tmp.resourceRightsID, r.siteResourceID
	from #tmpSiteSubRateGroups as tmp
	inner join dbo.sub_rates as r on r.rateID = tmp.rateID
	left outer join dataTransfer.dbo.sync_sub_rateGroups as srg on srg.siteID = @siteID and srg.useGroupID = tmp.groupID and srg.rateUID = tmp.rateUID
	where srg.groupID is null;

	-- store labels to be used in logging - before updating entries
	INSERT INTO #tmpLogLabelsType (typeID, typeName)
	SELECT typeID, typeName
	FROM dbo.sub_types
	WHERE siteID = @siteID
	AND [status] <> 'D';

	INSERT INTO #tmpLogLabelsSub (subscriptionID, subscriptionName, typeID, typeName)
	SELECT s.subscriptionID, s.subscriptionName, t.typeID, t.typeName
	FROM dbo.sub_subscriptions AS s
	INNER JOIN dbo.sub_types AS t ON t.typeID = s.typeID
	WHERE s.orgID = @orgID
	AND s.[status] <> 'D';

	INSERT INTO #tmpLogLabelsAddOn (addonID, subscriptionID, childSetID)
	SELECT ao.addonID, ao.subscriptionID, ao.childSetID
	FROM dbo.sub_addons AS ao
	INNER JOIN dbo.sub_subscriptions AS s ON s.subscriptionID = ao.subscriptionID
		AND s.orgID = @orgID
		AND s.[status] <> 'D';

	INSERT INTO #tmpLogLabelsSet (setID, setName)
	SELECT setID, setName
	FROM dbo.sub_sets
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsFreq (frequencyID, frequencyName)
	SELECT frequencyID, frequencyName
	FROM dbo.sub_frequencies
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsSched (scheduleID, scheduleName)
	SELECT scheduleID, scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID;

	INSERT INTO #tmpLogLabelsRate (rateID, rateName, scheduleID, scheduleName)
	SELECT r.rateID, r.rateName, sch.scheduleID, sch.scheduleName
	FROM dbo.sub_rates AS r
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
		AND sr.siteID = @siteID
	INNER JOIN dbo.sub_rateSchedules AS sch ON sch.scheduleID = r.scheduleID
	WHERE r.[status] <> 'D';

	-- new content objects
	INSERT INTO #tmpNewSubContentObjs (rawContent, itemID, itemType)
	select frontEndContent, syncAddonID, 'ao'
	from #tmpSubAddOns
	where finalAction = 'A';

	select @minAutoID = min(autoID) from #tmpNewSubContentObjs;
	while @minAutoID is not null BEGIN
		select @rawContent = null, @itemID = null, @itemType = null, @contentID = null, @contentSRID = null;

		select @rawContent = rawContent, @itemID = itemID, @itemType = itemType
		from #tmpNewSubContentObjs
		where autoID = @minAutoID;

		EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
			@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, @isActive=1, @contentTitle='', @contentDesc='', 
			@rawContent=@rawContent, @memberID=@recordedByMemberID,	@contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

		IF @itemType = 'ao'
			UPDATE #tmpSubAddOns
			SET frontEndContentID = @contentID
			WHERE syncAddonID = @itemID;

		select @minAutoID = min(autoID) from #tmpNewSubContentObjs where autoID > @minAutoID;
	END

	-- update content objects
	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBTYPE', 'Front End Content', tmp.typeID
	from #tmpSubTypes as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndCompletedContentID, tmp.frontEndCompletedContent, 'SUBTYPE', 'Front End Completed Content', tmp.typeID
	from #tmpSubTypes as tmp
	cross apply dbo.fn_getContent(tmp.frontEndCompletedContentID,@defaultLanguageID) as frontEndCompletedContent
	where tmp.finalAction = 'C'
	and frontEndCompletedContent.rawContent <> tmp.frontEndCompletedContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBSCRIPTION', 'Front End Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndParentSubContentID, tmp.frontEndParentSubContent, 'SUBSCRIPTION', 'Front End Parent Subscription Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndParentSubContentID,@defaultLanguageID) as frontEndParentSubContent
	where tmp.finalAction = 'C'
	and frontEndParentSubContent.rawContent <> tmp.frontEndParentSubContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndCompletedContentID, tmp.frontEndCompletedContent, 'SUBSCRIPTION', 'Front End Completed Content', tmp.subscriptionID
	from #tmpSubscriptions as tmp
	cross apply dbo.fn_getContent(tmp.frontEndCompletedContentID,@defaultLanguageID) as frontEndCompletedContent
	where tmp.finalAction = 'C'
	and frontEndCompletedContent.rawContent <> tmp.frontEndCompletedContent;

	INSERT INTO #tmpUpdateSubContentObjs (contentID, rawContent, areaCode, fieldName, refID)
	select tmp.frontEndContentID, tmp.frontEndContent, 'SUBADDON', 'Front End Content', tmp.addonID
	from #tmpSubAddOns as tmp
	cross apply dbo.fn_getContent(tmp.frontEndContentID,@defaultLanguageID) as frontEndContent
	where tmp.finalAction = 'C'
	and frontEndContent.rawContent <> tmp.frontEndContent;

	INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
	SELECT areaCode, fieldName + ' updated.', refID, 'UPDATE'
	FROM #tmpUpdateSubContentObjs;

	select @minContentID = min(contentID) from #tmpUpdateSubContentObjs;
	while @minContentID is not null BEGIN
		select @rawContent = null;

		select @rawContent = rawContent
		from #tmpUpdateSubContentObjs
		where contentID = @minContentID;

		EXEC dbo.cms_updateContent @contentID=@minContentID, @languageID=@defaultLanguageID, @isHTML=1, @contentTitle='', 
			@contentDesc='', @rawContent=@rawContent, @memberID=@recordedByMemberID;

		select @minContentID = min(contentID) from #tmpUpdateSubContentObjs where contentID > @minContentID;
	END


	EXEC dbo.cache_perms_setStatus @orgID=@orgID, @status='disabled-needsRebuild';

	-- delete sub sets
	IF EXISTS (select 1 from #tmpDeleteSubscriptionSets) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', 'Subscription(s) ' + STRING_AGG(QUOTENAME(sub.subscriptionName),', ') + ' removed from set.', ss.setID, 'UPDATE'
		FROM #tmpDeleteSubscriptionSets AS tmp
		INNER JOIN dbo.sub_subscriptionSets as ss on ss.subscriptionSetID = tmp.subscriptionSetID
		INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
		LEFT OUTER JOIN #tmpDeleteSets as tmpDel on tmpDel.setID = ss.setID
		WHERE tmpDel.setID IS NULL
		GROUP BY ss.setID;

		DELETE ss
		FROM dbo.sub_subscriptionSets as ss
		INNER JOIN #tmpDeleteSubscriptionSets as tmp on tmp.subscriptionSetID = ss.subscriptionSetID;
	END

	-- delete sub addons
	IF EXISTS (select 1 from #tmpDeleteSubAddOns) BEGIN
		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_content AS cc on cc.siteResourceID = sr.siteResourceID
		INNER JOIN dbo.sub_addons AS ao on ao.frontEndContentID = cc.contentID
		INNER JOIN #tmpDeleteSubAddOns AS tmp on tmp.addonID = ao.addonID
		WHERE sr.siteID = @siteID;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBADDON', 'Addon ' + QUOTENAME(s.setName) + ' was removed from subscription ' + QUOTENAME(sub.subscriptionName) + '.', sa.addonID, 'DELETE'
		FROM #tmpDeleteSubAddOns AS tmp
		INNER JOIN dbo.sub_addons as sa on sa.addonID = tmp.addonID
		INNER JOIN dbo.sub_sets as s on s.setID = sa.childSetID
		INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = sa.subscriptionID;
		
		DELETE sa
		FROM dbo.sub_addons as sa
		INNER JOIN #tmpDeleteSubAddOns as tmp on tmp.addonID = sa.addonID;
	END

	-- delete subscriptions
	IF EXISTS (select 1 from #tmpDeleteSubscriptions) BEGIN
		select @minSubID = min(subscriptionID) from #tmpDeleteSubscriptions;
		WHILE @minSubID is not null BEGIN
			EXEC dbo.sub_deleteSubscription @orgID=@orgID, @siteID=@siteID, @subscriptionID=@minSubID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minSubID = min(subscriptionID) from #tmpDeleteSubscriptions where subscriptionID > @minSubID;
		END
	END

	-- delete subTypes
	IF EXISTS (select 1 from #tmpDeleteSubTypes) BEGIN
		select @minTypeID = min(typeID) from #tmpDeleteSubTypes;
		WHILE @minTypeID is not null BEGIN
			EXEC dbo.sub_deleteSubscriptionType @orgID=@orgID, @siteID=@siteID, @subTypeID=@minTypeID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minTypeID = min(typeID) from #tmpDeleteSubTypes where typeID > @minTypeID;
		END
	END

	-- delete rate freqs merchant profiles
	IF EXISTS (select 1 from #tmpDeleteSubRateFreqMerchantProfiles) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Payment Method(s) ' + STRING_AGG(QUOTENAME(p.profileName),', ') + ' were removed from Rate Frequency ' + QUOTENAME(f.frequencyName) + '.', rf.rateID, 'UPDATE'
		FROM #tmpDeleteSubRateFreqMerchantProfiles as tmp
		INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = tmp.rfmpid
		inner join dbo.sub_rateFrequencies as rf on rf.RFID = rfmp.rfid
		inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		inner join dbo.mp_profiles as p on p.profileID = rfmp.profileID
		left outer join #tmpDeleteSubRates as tmpDel on tmpDel.rateID = rf.rateID
		where tmpDel.rateID IS NULL
		group by rf.rateID, f.frequencyName;

		UPDATE rfmp
		SET rfmp.[status] = 'D'
		FROM dbo.sub_rateFrequenciesMerchantProfiles as rfmp 
		INNER JOIN #tmpDeleteSubRateFreqMerchantProfiles as tmp on tmp.rfmpid = rfmp.rfmpid;
	END

	-- delete rate freqs
	IF EXISTS (select 1 from #tmpDeleteSubRateFreqs) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' has been deleted.', rf.rateID, 'UPDATE'
		FROM #tmpDeleteSubRateFreqs as tmp
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.RFID = tmp.rfid
		INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		left outer join #tmpDeleteSubRates as tmpDel on tmpDel.rateID = rf.rateID
		where tmpDel.rateID IS NULL;

		UPDATE rf
		SET rf.[status] = 'D'
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpDeleteSubRateFreqs as tmp on tmp.rfid = rf.rfid;
	END

	-- delete rates
	IF EXISTS (select 1 from #tmpDeleteSubRates) BEGIN
		select @minRateID = min(rateID) from #tmpDeleteSubRates;
		WHILE @minRateID is not null BEGIN
			EXEC dbo.sub_deleteRate @orgID=@orgID, @rateID=@minRateID, @isImport=1, @recordedByMemberID=@recordedByMemberID;
			select @minRateID = min(rateID) from #tmpDeleteSubRates where rateID > @minRateID;
		END
	END

	-- delete rate schedules
	IF EXISTS (select 1 from #tmpDeleteSubRateSchedules) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'RATESCH', 'Rate Schedule ' + QUOTENAME(rs.scheduleName) + ' has been deleted.', tmp.scheduleID, 'DELETE'
		FROM #tmpDeleteSubRateSchedules as tmp
		INNER JOIN dbo.sub_rateSchedules as rs on rs.scheduleID = tmp.scheduleID
		WHERE rs.siteID = @siteID;

		UPDATE rs
		SET rs.[status] = 'D'
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpDeleteSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID;
	END
	
	-- delete frequencies
	IF EXISTS (select 1 from #tmpDeleteSubFrequencies) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'FREQ', 'Frequency ' + QUOTENAME(sf.frequencyName) + ' has been deleted.', tmp.frequencyID, 'DELETE'
		FROM #tmpDeleteSubFrequencies as tmp
		INNER JOIN dbo.sub_frequencies as sf on sf.frequencyID = tmp.frequencyID
		WHERE sf.siteID = @siteID;

		UPDATE sf
		SET sf.[status] = 'D'
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpDeleteSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
		WHERE sf.siteID = @siteID;
	END

	-- delete sets
	IF EXISTS (select 1 from #tmpDeleteSets) BEGIN
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', 'Subscription Set ' + QUOTENAME(ss.setName) + ' has been deleted.', tmp.setID, 'DELETE'
		FROM #tmpDeleteSets as tmp
		INNER JOIN dbo.sub_sets as ss on ss.setID = tmp.setID
		WHERE ss.siteID = @siteID;

		DELETE ss
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpDeleteSets as tmp on tmp.setID = ss.setID
		WHERE ss.siteID = @siteID;
	END

	-- delete rate groups
	IF EXISTS (select 1 from #tmpDeleteSubRateGroups) BEGIN
		select @minResourceRightsID = min(resourceRightsID) from #tmpDeleteSubRateGroups;
		WHILE @minResourceRightsID is not null BEGIN
			set @siteResourceID = null;

			select @siteResourceID = tmp.siteResourceID, @groupPathExpanded = g.groupPathExpanded, @rateID = r.rateID
			from #tmpDeleteSubRateGroups as tmp
			inner join #tmpSiteSubRateGroups as tmp2 on tmp2.resourceRightsID = tmp.resourceRightsID
			inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = tmp2.groupID
			inner join dbo.sub_rates as r on r.rateID = tmp2.rateID
			where tmp.resourceRightsID = @minResourceRightsID;

			IF NOT EXISTS (SELECT 1 FROM #tmpDeleteSubRates WHERE rateID = @rateID)
				INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
				SELECT 'SUBRATE', 'Group ' + QUOTENAME(@groupPathExpanded) + ' removed from Rate.', @rateID, 'UPDATE';

			EXEC dbo.cms_deleteSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @siteResourceRightID=@minResourceRightsID;

			select @minResourceRightsID = min(resourceRightsID) from #tmpDeleteSubRateGroups where resourceRightsID > @minResourceRightsID;
		END
	END

	-- new frequencies
	IF EXISTS (select 1 from #tmpSubFrequencies where finalAction = 'A') BEGIN
		-- update uids of deleted freqs having same UID
		UPDATE sf
		SET sf.[uid] = NEWID()
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.[uid] = sf.[uid]
		WHERE sf.[status] = 'D'
		AND tmp.finalAction = 'A';

		DELETE FROM @insertedEntries;

		-- insert freqs
		INSERT INTO dbo.sub_frequencies (siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status])
		OUTPUT inserted.frequencyID
		INTO @insertedEntries
		select @siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status]
		from #tmpSubFrequencies 
		where finalAction = 'A'
			except
		select siteID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, monthlyInterval, isSystemRate, [status]
		from dbo.sub_frequencies
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'FREQ', 'New Frequency ' + QUOTENAME(f.frequencyName) + ' has been created.'
				+ CASE WHEN f.frequency IS NOT NULL THEN @crlf + 'Number of Installments: ' + CAST(f.frequency AS VARCHAR(10)) ELSE '' END
				+ CASE WHEN NULLIF(f.frequencyShortName,'') IS NOT NULL THEN @crlf + 'Short Name: ' + f.frequencyShortName ELSE '' END
				+ CASE WHEN f.rateRequired IS NOT NULL THEN @crlf + 'Rate Required: ' + CASE WHEN f.rateRequired = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
				+ CASE WHEN f.frequency IS NOT NULL THEN @crlf + 'Has Installments: ' + CASE WHEN f.frequency > 0 THEN 'Yes' ELSE 'No' END ELSE '' END
				+ CASE WHEN f.monthlyInterval IS NOT NULL THEN @crlf + 'Number of Months Between Payments: ' + CAST(f.monthlyInterval AS VARCHAR(10)) ELSE '' END,
				f.frequencyID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_frequencies AS f ON f.frequencyID = tmp.refID;
		END
	END

	-- new sets
	IF EXISTS (select 1 from #tmpSets where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_sets (siteID, setName, [uid], [status])
		OUTPUT inserted.setID
		INTO @insertedEntries
		select @siteID, setName, [uid], [status]
		from #tmpSets 
		where finalAction = 'A'
			except
		select siteID, setName, [uid], [status]
		from dbo.sub_sets
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBSET', 'New Subscription Set ' + QUOTENAME(s.setName) + ' has been created.', s.setID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_sets AS s ON s.setID = tmp.refID;
		END
	END

	-- new subTypes
	IF EXISTS (select 1 from #tmpSubTypes where finalAction = 'A') BEGIN
		-- update uids of deleted subTypes having same UID
		UPDATE st
		SET st.[uid] = NEWID()
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.[uid] = st.[uid]
		WHERE st.[status] = 'D'
		AND tmp.finalAction = 'A';

		select @minTypeID = min(syncTypeID) from #tmpSubTypes where finalAction = 'A';

		WHILE @minTypeID is not null BEGIN
			select @uid = null, @newSubTypeID = null, @typeName = null, @typeCode = null, @feRawContent = null, @feCompletedRawContent = null, @feEmailNotification = null;

			select @uid = [uid], @typeName = typeName, @typeCode = typeCode, @feRawContent = frontEndContent, 
				@feCompletedRawContent = frontEndCompletedContent, @feEmailNotification = feEmailNotification
			from #tmpSubTypes
			where finalAction = 'A' 
			and syncTypeID = @minTypeID;

			EXEC dbo.sub_createSubscriptionType @orgID=@orgID, @siteID=@siteID, @typeName=@typeName, @feRawContent=@feRawContent, 
				@feCompletedRawContent=@feCompletedRawContent, @feEmailNotification=@feEmailNotification, @isImport=1, 
				@recordedByMemberID=@recordedByMemberID, @subTypeID=@newSubTypeID OUTPUT;

			UPDATE dbo.sub_types
			SET [uid] = @uid,
				typeCode = @typeCode
			WHERE typeID = @newSubTypeID;
			
			select @minTypeID = min(syncTypeID) from #tmpSubTypes where finalAction = 'A' and syncTypeID > @minTypeID;
		END
	END

	-- new rate schedules
	IF EXISTS (select 1 from #tmpSubRateSchedules where finalAction = 'A') BEGIN
		-- update uids of deleted schedules having same UID
		UPDATE rs
		SET rs.[uid] = NEWID()
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.[uid] = rs.[uid]
		WHERE rs.[status] = 'D'
		AND tmp.finalAction = 'A';

		DELETE FROM @insertedEntries;

		-- insert schedules
		INSERT INTO dbo.sub_rateSchedules (siteID, scheduleName, [uid], [status])
		OUTPUT inserted.scheduleID
		INTO @insertedEntries
		select @siteID, scheduleName, [uid], [status]
		from #tmpSubRateSchedules 
		where finalAction = 'A'
			except
		select siteID, scheduleName, [uid], [status]
		from dbo.sub_rateSchedules
		where siteID = @siteID
		and [status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'RATESCH', 'New Subscription Rate Schedule ' + QUOTENAME(s.scheduleName) + ' has been created.', s.scheduleID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateSchedules AS s ON s.scheduleID = tmp.refID;
		END
	END

	-- new subscriptions
	IF EXISTS (select 1 from #tmpSubscriptions where finalAction = 'A') BEGIN
		-- update uids of deleted subscriptions having same UID
		UPDATE s
		SET s.[uid] = NEWID()
		FROM dbo.sub_subscriptions as s
		INNER JOIN dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		INNER JOIN #tmpSubscriptions as tmp on tmp.[uid] = s.[uid]
		WHERE s.[status] = 'D'
		AND tmp.finalAction = 'A';

		select @minSubID = min(syncSubscriptionID) from #tmpSubscriptions where finalAction = 'A';

		WHILE @minSubID is not null BEGIN
			select @uid = null, @newSubsID = null, @subName = null, @feRawContent = null, @feCompletedRawContent = null, @feParentSubRawContent = null,
				@reportCode = null, @scheduleID = null, @rateTermDateFlag = null, @autoExpire = null, @status = null,
				@soldSeparately = null, @paymentOrder = null, @GLAccountID = null, @allowRateGLOverride = null, @activationOptionCode = null,
				@alternateActivationOptionCode = null, @typeID = null;

			select @uid = s.[uid], @subName = s.subscriptionName, @feRawContent = s.frontEndContent, @feCompletedRawContent = s.frontEndCompletedContent, 
				@feParentSubRawContent = s.frontEndParentSubContent, @reportCode = s.reportCode, @scheduleID = rs.scheduleID, @rateTermDateFlag = s.rateTermDateFlag, 
				@autoExpire = s.autoExpire, @status = s.[status], @soldSeparately = s.soldSeparately, @paymentOrder = s.paymentOrder, @GLAccountID = s.GLAccountID, 
				@allowRateGLOverride = s.allowRateGLAccountOverride, @activationOptionCode = ao.subActivationCode,
				@alternateActivationOptionCode = ao_alt.subActivationCode, @typeID = t.typeID
			from #tmpSubscriptions as s
			inner join dbo.sub_types as t on t.siteID = @siteID and t.[uid] = s.syncTypeUID
			inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = s.scheduleUID
			inner join dbo.sub_activationOptions as ao on ao.subActivationID = s.subActivationID
			inner join dbo.sub_activationOptions as ao_alt on ao_alt.subActivationID = s.subAlternateActivationID
			where s.finalAction = 'A' 
			and s.syncSubscriptionID = @minSubID;

			EXEC dbo.sub_createSubscription @orgID=@orgID, @siteID=@siteID, @typeID=@typeID, @subName=@subName, 
				@reportCode=@reportCode, @scheduleID=@scheduleID, @rateTermDateFlag=@rateTermDateFlag,
				@autoExpire=@autoExpire, @status=@status,
				@soldSeparately=@soldSeparately, @paymentOrder=@paymentOrder, @GLAccountID=@GLAccountID,
				@allowRateGLOverride=@allowRateGLOverride, @activationOptionCode=@activationOptionCode,
				@alternateActivationOptionCode=@alternateActivationOptionCode, @feRawContent=@feRawContent,
				@feCompletedRawContent=@feCompletedRawContent, @feParentSubRawContent=@feParentSubRawContent,
				@isImport=1, @recordedByMemberID=@recordedByMemberID, @subID=@newSubsID OUTPUT;

			UPDATE dbo.sub_subscriptions
			SET [uid] = @uid
			WHERE subscriptionID = @newSubsID;
			
			select @minSubID = min(syncSubscriptionID) from #tmpSubscriptions where finalAction = 'A' and syncSubscriptionID > @minSubID;
		END
	END

	-- new rates
	IF EXISTS (select 1 from #tmpSubRates where finalAction = 'A') BEGIN
		-- update uids of deleted rates having same UID
		UPDATE r
		SET r.[uid] = NEWID()
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.[uid] = r.[uid]
		WHERE r.[status] = 'D'
		AND tmp.finalAction = 'A';

		-- insert rates
		select @minRateID = min(syncRateID) from #tmpSubRates where finalAction = 'A';
		WHILE @minRateID is not null BEGIN
			select @scheduleID = null, @rateName = null, @reportCode = null, @status = null, @rateStartDate = null, @rateEndDate = null, 
				@rateStartAFID = null, @rateEndAFID = null, @termStartDate = null, @termEndDate = null, @termStartAFID = null, @termEndAFID = null,
				@graceEndDate = null, @graceEndAFID = null, @recogStartDate = null, @recogEndDate = null, @recogStartAFID = null,
				@recogEndAFID = null, @rateAdvanceOnTermEnd = null, @isRenewalRate = null, @forceUpfront = null, @accountID = null,
				@frontEndAllowChangePrice = null, @keepChangedPriceOnRenewal = null, @frontEndChangePriceMin = null, @frontEndChangePriceMax = null, 
				@newRateID = null, @newSiteResourceID = null, @uid = null, @rateAFStartDate = null, @rateAFEndDate = null, @termAFStartDate = null, 
				@termAFEndDate = null, @recogAFStartDate = null, @recogAFEndDate = null;

			select @scheduleID = rs.scheduleID, @rateName = r.rateName, @reportCode = r.reportCode, @status = r.[status], @rateStartDate = r.rateStartDate, 
				@rateEndDate = r.rateEndDate, @rateStartAFID = r.rateStartDateAFID, @rateEndAFID = r.rateEndDateAFID, @termStartDate = r.termStartDate, 
				@termEndDate = r.termEndDate, @termStartAFID = r.termStartDateAFID, @termEndAFID = r.termEndDateAFID,
				@graceEndDate = r.graceEndDate, @graceEndAFID = r.graceAFID, @recogStartDate = r.recogStartDate, @recogEndDate = r.recogEndDate, 
				@recogStartAFID = r.recogStartDateAFID, @recogEndAFID = r.recogEndDateAFID, @rateAdvanceOnTermEnd = r.rateAdvanceOnTermEnd, 
				@isRenewalRate = r.isRenewalRate, @forceUpfront = r.forceUpfront, @accountID = r.GLAccountID,
				@frontEndAllowChangePrice = r.frontEndAllowChangePrice, @keepChangedPriceOnRenewal = r.keepChangedPriceOnRenewal,
				@frontEndChangePriceMin = r.frontEndChangePriceMin, @frontEndChangePriceMax = r.frontEndChangePriceMax, @uid = r.[uid],
				@rateAFStartDate = r.rateAFStartDate, @rateAFEndDate = r.rateAFEndDate, @termAFStartDate = r.termAFStartDate, 
				@termAFEndDate = r.termAFEndDate, @recogAFStartDate = r.recogAFStartDate, @recogAFEndDate = r.recogAFEndDate
			from #tmpSubRates as r
			inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = r.scheduleUID
			where r.finalAction = 'A'
			and r.syncRateID = @minRateID;

			-- create rate
			EXEC dbo.sub_createRate @scheduleID=@scheduleID, @rateName=@rateName, @reportCode=@reportCode, @status=@status,
				@rateStartDate=@rateStartDate, @rateEndDate=@rateEndDate, @rateStartAFID=@rateStartAFID,
				@rateEndAFID=@rateEndAFID, @termStartDate=@termStartDate, @termEndDate=@termEndDate,
				@termStartAFID=@termStartAFID, @termEndAFID=@termEndAFID, @graceEndDate=@graceEndDate,
				@graceEndAFID=@graceEndAFID, @recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate,
				@recogStartAFID=@recogStartAFID, @recogEndAFID=@recogEndAFID, @rateAdvanceOnTermEnd=@rateAdvanceOnTermEnd,
				@isRenewalRate=@isRenewalRate, @forceUpfront=@forceUpfront, @accountID=@accountID,
				@frontEndAllowChangePrice=@frontEndAllowChangePrice, @linkedNonRenewalRateID=NULL, @fallbackRenewalRateID=NULL,
				@keepChangedPriceOnRenewal=@keepChangedPriceOnRenewal, @frontEndChangePriceMin=@frontEndChangePriceMin,
				@frontEndChangePriceMax=@frontEndChangePriceMax, @isImport=1, @recordedByMemberID=@recordedByMemberID, 
				@rateID=@newRateID OUTPUT, @siteResourceID=@newSiteResourceID OUTPUT;

			UPDATE dbo.sub_rates 
			SET [uid] = @uid,
				rateAFStartDate = @rateAFStartDate,
				rateAFEndDate = @rateAFEndDate,
				termAFStartDate = @termAFStartDate,
				termAFEndDate = @termAFEndDate,
				recogAFStartDate = @recogAFStartDate,
				recogAFEndDate = @recogAFEndDate
			WHERE rateID = @newRateID;

			select @minRateID = min(syncRateID) from #tmpSubRates where finalAction = 'A' and syncRateID > @minRateID;
		END
	END

	-- new sub addons
	IF EXISTS (select 1 from #tmpSubAddOns where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_addons (subscriptionID, childSetID, orderNum, minAllowed, maxAllowed, useAcctCodeInSet, useTermEndDateInSet,
			PCnum, PCPctOffEach, frontEndAllowSelect, frontEndAllowChangePrice, frontEndContentID, frontEndAddAdditional)
		OUTPUT inserted.addonID
		INTO @insertedEntries
		select sub.subscriptionID, s.setID, ao.orderNum, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet,
			ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, ao.frontEndAddAdditional
		from #tmpSubAddOns as ao
		inner join dbo.sub_sets as s on s.[uid] = ao.syncChildSetUID
		inner join dbo.sub_subscriptions as sub on sub.[uid] = ao.subscriptionUID
		where ao.finalAction = 'A'
			except
		select subs.subscriptionID, s.setID, ao.orderNum, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet,
			ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, ao.frontEndAddAdditional
		from dbo.sub_addons as ao
		inner join dbo.sub_sets as s on s.setID = ao.childSetID 
			and s.siteID = @siteID
			and s.status <> 'D'
		inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ao.subscriptionID and subs.status <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBADDON', 'New AddOn created for Subscription ' + QUOTENAME(sub.subscriptionName) + '.' +
					@crlf + 'Subscription Set: ' + QUOTENAME(ss.setName) +
					CASE WHEN ao.orderNum IS NOT NULL THEN @crlf + 'Order Number: ' + CAST(ao.orderNum AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.minAllowed IS NOT NULL THEN @crlf + 'Min Allowed: ' + CAST(ao.minAllowed AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.maxAllowed IS NOT NULL THEN @crlf + 'Max Allowed: ' + CAST(ao.maxAllowed AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.useAcctCodeInSet IS NOT NULL THEN @crlf + 'Use Set Acct Code: ' + CASE WHEN ao.useAcctCodeInSet = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.useTermEndDateInSet IS NOT NULL THEN @crlf + 'Use Term End Date In Set: ' + CASE WHEN ao.useTermEndDateInSet = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.PCnum IS NOT NULL THEN @crlf + 'Items Free in Set: ' + CAST(ao.PCnum AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.PCPctOffEach IS NOT NULL THEN @crlf + 'PC Percent Off Each: ' + CAST(ao.PCPctOffEach AS VARCHAR(10)) ELSE '' END +
					CASE WHEN ao.frontEndAllowSelect IS NOT NULL THEN @crlf + 'Should this Addon appear in front end: ' + CASE WHEN ao.frontEndAllowSelect = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.frontEndAddAdditional IS NOT NULL THEN @crlf + 'Allow User to add additional selections in front end wizard: ' + CASE WHEN ao.frontEndAddAdditional = 0 THEN 'No' ELSE 'Yes' END ELSE '' END +
					CASE WHEN ao.frontEndAllowChangePrice IS NOT NULL THEN @crlf + 'Front End Change Price Select: ' + CASE WHEN ao.frontEndAllowChangePrice = 0 THEN 'No' ELSE 'Yes' END ELSE '' END,
				ao.addonID, 'INSERT'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_addons AS ao ON ao.addonID = tmp.refID
			INNER JOIN dbo.sub_sets AS ss ON ss.setID = ao.childSetID
			INNER JOIN dbo.sub_subscriptions AS sub ON sub.subscriptionID = ao.subscriptionID;
		END
	END

	-- new rate freqs
	IF EXISTS (select 1 from #tmpSubRateFrequencies where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_rateFrequencies (rateID, frequencyID, rateAmt, status, numInstallments, allowFrontEnd)
		OUTPUT inserted.rfid
		INTO @insertedEntries
		select r.rateID, f.frequencyID, rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd
		from #tmpSubRateFrequencies as rf
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		where rf.finalAction = 'A'
			except
		select r.rateID, f.frequencyID, rf.rateAmt, rf.[status], rf.numInstallments, rf.allowFrontEnd
		from dbo.sub_rateFrequencies as rf
		inner join dbo.sub_rates as r on r.rateID = rf.rateID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID;

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' is added to Rate.'+
					@crlf + 'Amount: ' + CAST(rf.rateAmt AS VARCHAR(20)) +
					CASE WHEN rf.allowFrontEnd IS NOT NULL THEN @crlf + 'Allow Front End: ' + CASE WHEN rf.allowFrontEnd = 0 THEN 'No' ELSE 'Yes' END ELSE '' END,
				r.rateID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rfid = tmp.refID
			INNER JOIN dbo.sub_rates as r on r.rateID = rf.rateID
			INNER JOIN dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID;
		END
	END

	-- new rate freq merchant profiles
	IF EXISTS (select 1 from #tmpSubRateFrequenciesMerchantProfiles where finalAction = 'A') BEGIN
		-- update matching rate freq merchant profiles from deleted status to active status
		UPDATE rfmp
		SET rfmp.[status] = 'A'
		from #tmpSubRateFrequenciesMerchantProfiles as tmpRFMP
		inner join #tmpSubRateFrequencies as rf on rf.syncRFID = tmpRFMP.syncRFID
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		inner join dbo.sub_rateFrequencies as srf on srf.rateID = r.rateID and srf.frequencyID = f.frequencyID
		inner join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = srf.rfid
			and rfmp.profileID = tmpRFMP.profileID
			and rfmp.[status] = 'D'
		where tmpRFMP.finalAction = 'A';

		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_rateFrequenciesMerchantProfiles (rfid, profileID, [status])
		OUTPUT inserted.rfmpid
		INTO @insertedEntries
		select distinct srf.rfid, rfmp.profileID, rfmp.[status]
		from #tmpSubRateFrequenciesMerchantProfiles as rfmp
		inner join #tmpSubRateFrequencies as rf on rf.syncRFID = rfmp.syncRFID
		inner join dbo.sub_rates as r on r.[uid] = rf.rateUID
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.[uid] = rf.frequencyUID
		inner join dbo.sub_rateFrequencies as srf on srf.rateID = r.rateID and srf.frequencyID = f.frequencyID
		where rfmp.finalAction = 'A'
			except
		select srf.rfid, rfmp.profileID, rfmp.[status]
		from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
		inner join dbo.sub_rateFrequencies as srf on srf.rfid = rfmp.rfid
		inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = srf.frequencyID
		where rfmp.[status] <> 'D';

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBRATE', 'Payment Method(s) ' + STRING_AGG(QUOTENAME(p.profileName),', ') + ' were added to Rate Frequency ' + QUOTENAME(f.frequencyName) + '.',
				rf.rateID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfmpid = tmp.refID
			INNER JOIN dbo.sub_rateFrequencies as rf on rf.RFID = rfmp.rfid
			INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
			INNER JOIN dbo.mp_profiles as p on p.profileID = rfmp.profileID
			WHERE rfmp.[status] = 'A'
			GROUP BY rf.rateID, f.frequencyName;
		END
	END

	-- new rate groups
	IF EXISTS (select 1 from #tmpNewSubRateGroups) BEGIN
		select @minAutoID = min(autoID) from #tmpNewSubRateGroups;
		WHILE @minAutoID IS NOT NULL BEGIN
			select @siteResourceID = null, @include = null, @groupID = null;

			select @siteResourceID = r.siteResourceID, @include = rg.include, @groupID = rg.groupID
			from #tmpNewSubRateGroups as rg
			inner join dbo.sub_rates as r on r.[uid] = rg.rateUID
			where rg.autoID = @minAutoID;

			EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=@include,
				@functionIDList=@qualifyFID, @roleID=null, @groupID=@groupID, @inheritedRightsResourceID=null,
				@inheritedRightsFunctionID=null;

			select @minAutoID = min(autoID) from #tmpNewSubRateGroups where autoID > @minAutoID;
		END

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Group(s) ' + STRING_AGG(QUOTENAME(g.groupPathExpanded) + ' (' + CASE rg.[include] WHEN 1 THEN 'Allow' ELSE 'Deny' END + ')',', ') + ' added to Rate.', r.rateID, 'UPDATE'
		FROM #tmpNewSubRateGroups as rg
		inner join dbo.sub_rates as r on r.[uid] = rg.rateUID
		inner join dbo.ams_groups as g on g.orgID = @orgID and g.groupID = rg.groupID
		group by r.rateID;
	END

	-- new subscription sets
	IF EXISTS (select 1 from #tmpSubscriptionSets where finalAction = 'A') BEGIN
		DELETE FROM @insertedEntries;

		INSERT INTO dbo.sub_subscriptionSets (subscriptionID, setID, orderNum)
		OUTPUT inserted.subscriptionSetID
		INTO @insertedEntries
		select subs.subscriptionID, ss.setID, s.orderNum
		from #tmpSubscriptionSets as s
		inner join dbo.sub_subscriptions as subs on subs.[uid] = s.subscriptionUID
		inner join dbo.sub_sets as ss on ss.siteID = @siteID and ss.[uid] = s.setUID
		where s.finalAction = 'A'
			except
		select s.subscriptionID, s.setID, s.orderNum
		from dbo.sub_subscriptionSets as s
		inner join dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
		inner join dbo.sub_sets as ss on ss.siteID = @siteID and ss.setID = s.setID;

		IF EXISTS (SELECT 1 FROM @insertedEntries) BEGIN
			INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
			SELECT 'SUBSET', 'Subscription(s) ' + STRING_AGG(QUOTENAME(sub.subscriptionName),', ') + ' has been added to set.', ss.setID, 'UPDATE'
			FROM @insertedEntries AS tmp
			INNER JOIN dbo.sub_subscriptionSets AS ss ON ss.subscriptionSetID = tmp.refID
			INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
			LEFT OUTER JOIN #tmpDeleteSets as tmpDel on tmpDel.setID = ss.setID
			WHERE tmpDel.setID IS NULL
			GROUP BY ss.setID;
		END
	END

	-- update frequencies
	IF EXISTS (select 1 from #tmpSubFrequencies where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataFreq;
		CREATE TABLE #tmpAuditLogDataFreq (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Frequency Name] varchar(max), [Number of Installments] varchar(max), 
			[Frequency Short Name] varchar(max), [Rate Required] varchar(max), [Has Installments] varchar(max), [Monthly Interval] varchar(max));

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		VALUES ('DATATYPECODE', 'STRING', 'INTEGER', 'STRING', 'BIT', 'BIT', 'INTEGER');

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [refID], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		SELECT 'OLDVAL', sf.frequencyID, sf.frequencyName, sf.frequency, sf.frequencyShortName, sf.rateRequired, sf.hasInstallments, sf.monthlyInterval
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
			AND tmp.finalAction = 'C'
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D';

		UPDATE sf
		SET sf.frequencyName = tmp.frequencyName,
			sf.frequency = tmp.frequency,
			sf.frequencyShortName = tmp.frequencyShortName,
			sf.rateRequired = tmp.rateRequired,
			sf.hasInstallments = tmp.hasInstallments,
			sf.monthlyInterval = tmp.monthlyInterval,
			sf.isSystemRate = tmp.isSystemRate,
			sf.[status] = tmp.[status]
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataFreq ([rowCode], [refID], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
		SELECT 'NEWVAL', sf.frequencyID, sf.frequencyName, sf.frequency, sf.frequencyShortName, sf.rateRequired, sf.hasInstallments, sf.monthlyInterval
		FROM dbo.sub_frequencies as sf
		INNER JOIN #tmpSubFrequencies as tmp on tmp.frequencyID = sf.frequencyID
			AND tmp.finalAction = 'C'
		WHERE sf.siteID = @siteID
		AND sf.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataFreq', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'FREQ', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataFreq;
	END

	-- update sets
	IF EXISTS (select 1 from #tmpSets where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSet') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSet;
		CREATE TABLE #tmpAuditLogDataSet (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Name] varchar(max), [Status] varchar(max));

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [Name], [Status])
		VALUES ('DATATYPECODE', 'STRING', 'BIT');

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [refID], [Name], [Status])
		SELECT 'OLDVAL', ss.setID, ss.setName, CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
			AND tmp.finalAction = 'C'
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D';

		UPDATE ss
		SET ss.setName = tmp.setName,
			ss.[status] = tmp.[status]
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSet ([rowCode], [refID], [Name], [Status])
		SELECT 'NEWVAL', ss.setID, ss.setName, CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_sets as ss
		INNER JOIN #tmpSets as tmp on tmp.setID = ss.setID
			AND tmp.finalAction = 'C'
		WHERE ss.siteID = @siteID
		AND ss.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSet', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSET', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSet') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSet;
	END

	-- update subTypes
	IF EXISTS (select 1 from #tmpSubTypes where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSubType') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSubType;
		CREATE TABLE #tmpAuditLogDataSubType (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Type Name] varchar(max),
			[Type Code] varchar(max), [Renewal Notification Email(s)] varchar(max));

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [Type Name], [Type Code], [Renewal Notification Email(s)])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [refID], [Type Name], [Type Code], [Renewal Notification Email(s)])
		SELECT 'OLDVAL', st.typeID, st.typeName, st.typeCode, st.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';
		
		UPDATE st
		SET st.typeName = tmp.typeName,
			st.typeCode = tmp.typeCode,
			st.feEmailNotification = tmp.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSubType ([rowCode], [refID], [Type Name], [Type Code], [Renewal Notification Email(s)])
		SELECT 'NEWVAL', st.typeID, st.typeName, st.typeCode, st.feEmailNotification
		FROM dbo.sub_types as st
		INNER JOIN #tmpSubTypes as tmp on tmp.typeID = st.typeID
		WHERE st.siteID = @siteID
		AND st.[status] <> 'D'
		AND tmp.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSubType', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBTYPE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSubType') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSubType;
	END

	-- update rate schedules
	IF EXISTS (select 1 from #tmpSubRateSchedules where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSched') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSched;
		CREATE TABLE #tmpAuditLogDataSched (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Rate Schedule Name] varchar(max), [Status] varchar(max));

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [Rate Schedule Name], [Status])
		VALUES ('DATATYPECODE', 'STRING', 'BIT');

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [refID], [Rate Schedule Name], [Status])
		SELECT 'OLDVAL', rs.scheduleID, rs.scheduleName, CASE WHEN rs.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		UPDATE rs
		SET rs.scheduleName = tmp.scheduleName,
			rs.[status] = tmp.[status]
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSched ([rowCode], [refID], [Rate Schedule Name], [Status])
		SELECT 'NEWVAL', rs.scheduleID, rs.scheduleName, CASE WHEN rs.[status] = 'A' THEN 'Active' ELSE 'Inactive' END
		FROM dbo.sub_rateSchedules as rs
		INNER JOIN #tmpSubRateSchedules as tmp on tmp.scheduleID = rs.scheduleID
		WHERE rs.siteID = @siteID
		AND rs.[status] <> 'D'
		AND tmp.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSched', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'RATESCH', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSched') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSched;
	END

	-- update subscriptions
	IF EXISTS (select 1 from #tmpSubscriptions where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataSub') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSub;
		CREATE TABLE #tmpAuditLogDataSub (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Subscription Name] varchar(max), [Report Code] varchar(max), 
			[Subscription Type] varchar(max), [Rate Schedule] varchar(max), [Term Dates] varchar(max), 
			[Auto Expire Subscribers] varchar(max), [Sold Seperately] varchar(max), [Status] varchar(max), [Payment Order] varchar(max), 
			[Subscription GL Account] varchar(max), [Allow Rate GL Account Override] varchar(max), [Activation Option] varchar(max),
			[Alternate Activation Option] varchar(max));

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates], [Auto Expire Subscribers], 
			[Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [refID], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates],
			[Auto Expire Subscribers], [Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		SELECT 'OLDVAL', ss.subscriptionID, ss.subscriptionName, ss.reportCode, t.typeName, srs.scheduleName, 
			CASE 
				WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
				WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
				ELSE 'Calculate term end date for term length (term start date is current day)'
			END,
			CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END, 
			CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
			glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END,
			sao.subActivationName, alt_sao.subActivationName
		FROM dbo.sub_subscriptions as ss
		INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
		INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
		INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
		INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
		LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
		LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID;

		UPDATE s
		SET s.typeID = st.typeID,
			s.subscriptionName = tmp.subscriptionName,
			s.scheduleID = rs.scheduleID,
			s.autoExpire = tmp.autoExpire,
			s.[status] = tmp.[status],
			s.soldSeparately = tmp.soldSeparately,
			s.GLAccountID = tmp.GLAccountID,
			s.rateTermDateFlag = tmp.rateTermDateFlag,
			s.paymentOrder = tmp.paymentOrder,
			s.reportCode = tmp.reportCode,
			s.subActivationID = tmp.subActivationID,
			s.subAlternateActivationID = tmp.subAlternateActivationID,
			s.allowRateGLAccountOverride = tmp.allowRateGLAccountOverride
		FROM dbo.sub_subscriptions as s
		INNER JOIN dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		INNER JOIN #tmpSubscriptions as tmp on tmp.subscriptionID = s.subscriptionID
		INNER JOIN dbo.sub_types as st on st.siteID = @siteID and st.[uid] = tmp.syncTypeUID
		INNER JOIN dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.[uid] = tmp.scheduleUID
		WHERE s.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataSub ([rowCode], [refID], [Subscription Name], [Report Code], [Subscription Type], [Rate Schedule], [Term Dates],
			[Auto Expire Subscribers], [Sold Seperately], [Status], [Payment Order], [Subscription GL Account], [Allow Rate GL Account Override],
			[Activation Option], [Alternate Activation Option])
		SELECT 'NEWVAL', ss.subscriptionID, ss.subscriptionName, ss.reportCode, t.typeName, srs.scheduleName,
			CASE
				WHEN ss.rateTermDateFlag = 'A' THEN 'Adhere to term dates in rate'
				WHEN ss.rateTermDateFlag = 'S' THEN 'Use current day as term start date (adhere to term end date in rate)'
				ELSE 'Calculate term end date for term length (term start date is current day)'
			END,
			CASE WHEN ss.autoExpire = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN soldSeparately = 0 THEN 'No' ELSE 'Yes' END,
			CASE WHEN ss.[status] = 'A' THEN 'Active' ELSE 'Inactive' END, ss.paymentOrder,
			glPath.thePathExpanded, CASE WHEN ss.allowRateGLAccountOverride = 0 THEN 'No' ELSE 'Yes' END,
			sao.subActivationName, alt_sao.subActivationName
		FROM dbo.sub_subscriptions as ss
		INNER JOIN dbo.sub_Types AS t ON ss.typeID = t.typeID AND t.siteID = @siteID
		INNER JOIN dbo.sub_rateSchedules as srs ON ss.scheduleID = srs.scheduleID AND srs.siteID = @siteID
		INNER JOIN dbo.sub_activationOptions as sao ON sao.subActivationID = ss.subActivationID
		INNER JOIN dbo.sub_activationOptions as alt_sao ON alt_sao.subActivationID = ss.subAlternateActivationID
		LEFT OUTER JOIN dbo.tr_GLAccounts as gl ON gl.GLAccountID = ss.GLAccountID
		LEFT OUTER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) AS glPath ON glPath.GLAccountID = GL.GLAccountID;

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataSub', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBSCRIPTION', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataSub') IS NOT NULL
			DROP TABLE #tmpAuditLogDataSub;
	END

	-- update rates
	IF EXISTS (select 1 from #tmpSubRates where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRate') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRate;
		CREATE TABLE #tmpAuditLogDataRate (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Status] varchar(max), [Rate Start Date] varchar(max), [Rate End Date] varchar(max),
			[Rate Start Date Advance Formula] varchar(max), [Rate End Date Advance Formula] varchar(max), [Rate Start Date Advance Date] varchar(max), [Rate End Date Advance Date] varchar(max), 
			[Term Start Date] varchar(max), [Term End Date] varchar(max), [Term Start Date Advance Formula] varchar(max), [Term End Date Advance Formula] varchar(max), 
			[Term Start Date Advance Date] varchar(max), [Term End Date Advance Date] varchar(max), [Grace End Date] varchar(max), [Grace End Date Advance Formula] varchar(max), 
			[Rate Name] varchar(max), [When to Advance Rates] varchar(max), [Is Renewal Rate] varchar(max), [Force Up front] varchar(max),
			[Report Code] varchar(max), [GL Account] varchar(max), [Front End Allow Change Price] varchar(max), [Front End Change Price Min] varchar(max), [Front End Change Price Max] varchar(max),
			[Recog Start Date] varchar(max), [Recog End Date] varchar(max), [Recog Start Date Advance Formula] varchar(max), [Recog End Date Advance Formula] varchar(max), 
			[Recog Start Date Advance Date] varchar(max), [Recog End Date Advance Date] varchar(max), [Renewal Keeps Changed Price] varchar(max));

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date],
			[Recog End Date], [Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		VALUES ('DATATYPECODE','STRING', 'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'DATE', 'DATE', 'STRING', 'STRING',
			'DATE', 'DATE', 'DATE', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'DECIMAL2', 'DECIMAL2',
			'DATE', 'DATE', 'STRING', 'STRING', 'DATE', 'DATE', 'BIT');

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [refID], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date], [Recog End Date],
			[Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		SELECT 'OLDVAL', r.rateID, CASE WHEN r.[status] = 'A' THEN 'Active' WHEN r.[status] = 'D' THEN 'Deleted' ELSE 'Inactive' END,
			r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
			r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
			af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
			af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
			r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax,
			r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
			af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
		LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
		WHERE r.[status] <> 'D';

		UPDATE r
		SET r.[status] = tmp.[status],
			r.rateStartDate = tmp.rateStartDate,
			r.rateEndDate = tmp.rateEndDate,
			r.rateStartDateAFID = tmp.rateStartDateAFID,
			r.rateEndDateAFID = tmp.rateEndDateAFID,
			r.rateAFStartDate = tmp.rateAFStartDate,
			r.rateAFEndDate = tmp.rateAFEndDate,
			r.termStartDate = tmp.termStartDate,
			r.termEndDate = tmp.termEndDate,
			r.termStartDateAFID = tmp.termStartDateAFID,
			r.termEndDateAFID = tmp.termEndDateAFID,
			r.termAFStartDate = tmp.termAFStartDate,
			r.termAFEndDate = tmp.termAFEndDate,
			r.graceEndDate = tmp.graceEndDate,
			r.graceAFID = tmp.graceAFID,
			r.rateName = tmp.rateName,
			r.rateAdvanceOnTermEnd = tmp.rateAdvanceOnTermEnd,
			r.isRenewalRate = tmp.isRenewalRate,
			r.forceUpfront = tmp.forceUpfront,
			r.reportCode = tmp.reportCode,
			r.GLAccountID = tmp.GLAccountID,
			r.frontEndAllowChangePrice = tmp.frontEndAllowChangePrice,
			r.keepChangedPriceOnRenewal = tmp.keepChangedPriceOnRenewal,
			r.frontEndChangePriceMin = tmp.frontEndChangePriceMin,
			r.frontEndChangePriceMax = tmp.frontEndChangePriceMax,
			r.recogStartDate = tmp.recogStartDate,
			r.recogEndDate = tmp.recogEndDate,
			r.recogStartDateAFID = tmp.recogStartDateAFID,
			r.recogEndDateAFID = tmp.recogEndDateAFID,
			r.recogAFStartDate = tmp.recogAFStartDate,
			r.recogAFEndDate = tmp.recogAFEndDate,
			r.rateOrder = tmp.rateOrder
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
		WHERE r.[status] <> 'D'
		AND tmp.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataRate ([rowCode], [refID], [Status], [Rate Start Date], [Rate End Date], [Rate Start Date Advance Formula], [Rate End Date Advance Formula],
			[Rate Start Date Advance Date], [Rate End Date Advance Date], [Term Start Date], [Term End Date], [Term Start Date Advance Formula],
			[Term End Date Advance Formula],  [Term Start Date Advance Date], [Term End Date Advance Date], [Grace End Date], [Grace End Date Advance Formula],
			[Rate Name], [When to Advance Rates], [Is Renewal Rate], [Force Up front], [Report Code], [GL Account], [Front End Allow Change Price],
			[Front End Change Price Min], [Front End Change Price Max], [Recog Start Date], [Recog End Date],
			[Recog Start Date Advance Formula], [Recog End Date Advance Formula], [Recog Start Date Advance Date], [Recog End Date Advance Date],
			[Renewal Keeps Changed Price])
		SELECT 'NEWVAL', r.rateID, CASE WHEN r.[status] = 'A' THEN 'Active' WHEN r.[status] = 'D' THEN 'Deleted' ELSE 'Inactive' END,
			r.rateStartDate, r.rateEndDate, af_rsd.afName AS [Rate Start Date Advance Formula], af_red.afName AS [Rate End Date Advance Formula],
			r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, af_tsd.afName AS [Term Start Date Advance Formula],
			af_ted.afName AS [Term End Date Advance Formula], r.termAFStartDate, r.termAFEndDate, r.graceEndDate,
			af_g.afName AS [Grace Date Advance Formula], r.rateName, CASE WHEN r.rateAdvanceOnTermEnd = 1 THEN 'After Term End Date' ELSE 'After Rate End Date' END, 
			r.isRenewalRate, r.forceUpfront, r.reportCode, gl.AccountName, r.frontEndAllowChangePrice, r.frontEndChangePriceMin, r.frontEndChangePriceMax,
			r.recogStartDate, r.recogEndDate, af_recogsd.afName AS [Recog Start Date Advance Formula], 
			af_recoged.afName AS [Recog End Date Advance Formula], r.recogAFStartDate, r.recogAFEndDate, r.keepChangedPriceOnRenewal
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_rsd ON af_rsd.AFID = r.rateStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_red ON af_red.AFID = r.rateEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_tsd ON af_tsd.AFID = r.termStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_ted ON af_ted.AFID = r.termEndDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_g ON af_g.AFID = r.graceAFID
		LEFT OUTER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = r.GLAccountID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recogsd ON af_recogsd.AFID = r.recogStartDateAFID
		LEFT OUTER JOIN dbo.af_advanceFormulas AS af_recoged ON af_recoged.AFID = r.recogEndDateAFID
		WHERE r.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRate', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRate') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRate;
	END

	-- update rates linkedNonRenewalRateID and fallbackRenewalRateID
	IF EXISTS (select 1 from #tmpSubRates where linkedNonRenewalRateUID is not null or fallbackRenewalRateUID is not null) BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateRenew') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateRenew;
		CREATE TABLE #tmpAuditLogDataRateRenew (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Linked Non Renewal Rate] varchar(max), [Fallback Renewal Rate] varchar(max));

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		VALUES ('DATATYPECODE','STRING', 'STRING');

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [refID], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		SELECT 'OLDVAL', r.rateID, lr.rateName AS [Linked Non Renewal Rate], fr.rateName AS [Fallback Renewal Rate]
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.sub_rates AS lr ON lr.rateID = r.linkedNonRenewalRateID
		LEFT OUTER JOIN dbo.sub_rates AS fr ON fr.rateID = r.fallbackRenewalRateID
		WHERE r.[status] <> 'D';

		UPDATE r
		SET r.linkedNonRenewalRateID = lr.rateID,
			r.fallbackRenewalRateID = fr.rateID
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.[uid] = r.[uid]
		LEFT OUTER JOIN dbo.sub_rates as lr on lr.[uid] = tmp.linkedNonRenewalRateUID
		LEFT OUTER JOIN dbo.sub_rates as fr on fr.[uid] = tmp.fallbackRenewalRateUID
		WHERE r.[status] <> 'D';

		INSERT INTO #tmpAuditLogDataRateRenew ([rowCode], [refID], [Linked Non Renewal Rate], [Fallback Renewal Rate])
		SELECT 'OLDVAL', r.rateID, lr.rateName AS [Linked Non Renewal Rate], fr.rateName AS [Fallback Renewal Rate]
		FROM dbo.sub_rates as r
		INNER JOIN #tmpSubRates as tmp on tmp.rateID = r.rateID
			AND tmp.finalAction = 'C'
		LEFT OUTER JOIN dbo.sub_rates AS lr ON lr.rateID = r.linkedNonRenewalRateID
		LEFT OUTER JOIN dbo.sub_rates AS fr ON fr.rateID = r.fallbackRenewalRateID
		WHERE r.[status] <> 'D';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRateRenew', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateRenew') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateRenew;
	END

	-- update sub addons
	IF EXISTS (select 1 from #tmpSubAddOns where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataAO') IS NOT NULL
			DROP TABLE #tmpAuditLogDataAO;
		CREATE TABLE #tmpAuditLogDataAO (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Min Allowed] varchar(max), [Max Allowed] varchar(max),
			[Use Account Code In Set] varchar(max), [Use Term End Date In Set] varchar(max), [PC Number] varchar(max), 
			[PC Percent Off Each] varchar(max), [Front End Change Select] varchar(max), [Front End Add Additional] varchar(max),
			[Front End Change Price Select] varchar(max));

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'BIT', 'BIT', 'STRING', 'STRING', 'BIT', 'BIT', 'BIT');

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [refID], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		SELECT 'OLDVAL', ao.addonID, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
			ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		UPDATE ao
		SET ao.minAllowed = sao.minAllowed, 
			ao.maxAllowed = sao.maxAllowed, 
			ao.useAcctCodeInSet = sao.useAcctCodeInSet, 
			ao.useTermEndDateInSet = sao.useTermEndDateInSet,
			ao.PCnum = sao.PCnum,
			ao.PCPctOffEach = sao.PCPctOffEach,
			ao.frontEndAllowSelect = sao.frontEndAllowSelect, 
			ao.frontEndAllowChangePrice = sao.frontEndAllowChangePrice, 
			ao.frontEndAddAdditional = sao.frontEndAddAdditional
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataAO ([rowCode], [refID], [Min Allowed], [Max Allowed], [Use Account Code In Set], [Use Term End Date In Set], 
			[PC Number], [PC Percent Off Each], [Front End Change Select], [Front End Add Additional], [Front End Change Price Select])
		SELECT 'NEWVAL', ao.addonID, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
			ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice
		FROM dbo.sub_addons as ao
		INNER JOIN #tmpSubAddOns as sao on sao.addonID = ao.addonID
		WHERE sao.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataAO', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBADDON', msg, refID, 'UPDATE'
		FROM #tmpAuditLogMessages;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataAO') IS NOT NULL
			DROP TABLE #tmpAuditLogDataAO;
	END

	-- update rate freqs
	IF EXISTS (select 1 from #tmpSubRateFrequencies where finalAction = 'C') BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateFreq;
		CREATE TABLE #tmpAuditLogDataRateFreq (autoID INT IDENTITY(1,1), [rowCode] varchar(20), [refID] int, [Status] varchar(max), [Amount] varchar(max), [Installments] varchar(max), [Allow Front End] varchar(max));

		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [Status], [Amount], [Installments], [Allow Front End])
		VALUES ('DATATYPECODE', 'STRING', 'DECIMAL2', 'INTEGER', 'BIT');
		
		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [refID], [Amount], [Status], [Installments], [Allow Front End])
		SELECT 'OLDVAL', rf.rfid, rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, CAST(rf.numInstallments AS VARCHAR(10)), rf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		UPDATE rf
		SET rf.rateAmt = srf.rateAmt, 
			rf.[status] = srf.[status], 
			rf.numInstallments = srf.numInstallments, 
			rf.allowFrontEnd = srf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		INSERT INTO #tmpAuditLogDataRateFreq ([rowCode], [refID], [Amount], [Status], [Installments], [Allow Front End])
		SELECT 'NEWVAL', rf.rfid, rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, CAST(rf.numInstallments AS VARCHAR(10)), rf.allowFrontEnd
		FROM dbo.sub_rateFrequencies as rf
		INNER JOIN #tmpSubRateFrequencies as srf on srf.rfid = rf.rfid
		WHERE srf.finalAction = 'C';

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataRateFreq', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

		-- grouping differences for a single rate frequency changes into one entry, so it falls under the changes for SUBRATE areacode
		INSERT INTO #tmpLogMessages (areaCode, msg, refID, actionType)
		SELECT 'SUBRATE', 'Rate Frequency ' + QUOTENAME(f.frequencyName) + ' is updated. ' + STRING_AGG(tmp.msg,' '), rf.rateID, 'UPDATE'
		FROM #tmpAuditLogMessages as tmp
		INNER JOIN dbo.sub_rateFrequencies as rf on rf.rfid = tmp.refID
		INNER JOIN dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		GROUP BY rf.rateID, rf.frequencyID, f.frequencyName;

		IF OBJECT_ID('tempdb..#tmpAuditLogDataRateFreq') IS NOT NULL
			DROP TABLE #tmpAuditLogDataRateFreq;
	END

	-- update subscription sets
	IF EXISTS (select 1 from #tmpSubscriptionSets where finalAction = 'C')
		UPDATE s
		SET s.orderNum = tmp.orderNum
		FROM dbo.sub_subscriptionSets as s
		INNER JOIN #tmpSubscriptionSets as tmp on tmp.subscriptionSetID = s.subscriptionSetID
		WHERE tmp.finalAction = 'C';

	-- repopulate sub split cache
	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;
	CREATE TABLE #tblMCQSubCond (conditionID int);
	
	INSERT INTO #tblMCQSubCond (conditionID)
	SELECT conditionID
	FROM dbo.ams_virtualGroupConditions
	WHERE orgID = @orgID
	AND fieldCode ='sub_entry';

	EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;

	EXEC dbo.cache_perms_setStatus @orgID=@orgID, @status='enabled';

	-- re-order addons & sub sets
	select @minSubID = min(s.subscriptionID)
	from dbo.sub_subscriptions as s 
	inner join dbo.sub_types as t on t.typeID = s.typeID
	where t.siteID = @siteID;
	
	WHILE @minSubID is not null BEGIN
		EXEC dbo.subs_reorderAddons @subscriptionID=@minSubID;

		select @minSubID = min(s.subscriptionID)
		from dbo.sub_subscriptions as s 
		inner join dbo.sub_types as t on t.typeID = s.typeID
			and t.siteID = @siteID
		where s.subscriptionID > @minSubID;
	END
	
	select @minSetID = min(setID)
	from dbo.sub_sets
	where siteID = @siteID;
	
	WHILE @minSetID is not null BEGIN
		EXEC dbo.subs_reorderSubscriptionSets @setID=@minSetID;
	
		select @minSetID = min(setID)
		from dbo.sub_sets
		where siteID = @siteID
		and setID > @minSetID;
	END

	-- labels from newly added entries to be used in logging
	INSERT INTO #tmpLogLabelsType (typeID, typeName)
	SELECT t.typeID, t.typeName
	FROM dbo.sub_types AS t
	LEFT OUTER JOIN #tmpLogLabelsType AS tmp ON tmp.typeID = t.typeID
	WHERE t.siteID = @siteID
	AND t.[status] <> 'D'
	AND tmp.typeID IS NULL;

	INSERT INTO #tmpLogLabelsSub (subscriptionID, subscriptionName, typeID, typeName)
	SELECT s.subscriptionID, s.subscriptionName, t.typeID, t.typeName
	FROM dbo.sub_subscriptions AS s
	INNER JOIN dbo.sub_types AS t ON t.typeID = s.typeID
	LEFT OUTER JOIN #tmpLogLabelsSub AS tmp ON tmp.subscriptionID = s.subscriptionID
	WHERE s.orgID = @orgID
	AND s.[status] <> 'D'
	AND tmp.subscriptionID IS NULL;

	INSERT INTO #tmpLogLabelsAddOn (addonID, subscriptionID, childSetID)
	SELECT ao.addonID, ao.subscriptionID, ao.childSetID
	FROM dbo.sub_addons AS ao
	INNER JOIN dbo.sub_subscriptions AS s ON s.subscriptionID = ao.subscriptionID
		AND s.orgID = @orgID
		AND s.[status] <> 'D'
	LEFT OUTER JOIN #tmpLogLabelsAddOn AS tmp ON tmp.addonID = ao.addonID
	WHERE tmp.addonID IS NULL;

	INSERT INTO #tmpLogLabelsSet (setID, setName)
	SELECT s.setID, s.setName
	FROM dbo.sub_sets AS s
	LEFT OUTER JOIN #tmpLogLabelsSet AS tmp ON tmp.setID = s.setID
	WHERE s.siteID = @siteID
	AND tmp.setID IS NULL;

	INSERT INTO #tmpLogLabelsFreq (frequencyID, frequencyName)
	SELECT f.frequencyID, f.frequencyName
	FROM dbo.sub_frequencies AS f
	LEFT OUTER JOIN #tmpLogLabelsFreq AS tmp ON tmp.frequencyID = f.frequencyID
	WHERE f.siteID = @siteID
	AND tmp.frequencyID IS NULL;

	INSERT INTO #tmpLogLabelsSched (scheduleID, scheduleName)
	SELECT s.scheduleID, s.scheduleName
	FROM dbo.sub_rateSchedules AS s
	LEFT OUTER JOIN #tmpLogLabelsSched AS tmp ON tmp.scheduleID = s.scheduleID
	WHERE s.siteID = @siteID
	AND tmp.scheduleID IS NULL;

	INSERT INTO #tmpLogLabelsRate (rateID, rateName, scheduleID, scheduleName)
	SELECT r.rateID, r.rateName, sch.scheduleID, sch.scheduleName
	FROM dbo.sub_rates AS r
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
		AND sr.siteID = @siteID
	INNER JOIN dbo.sub_rateSchedules AS sch ON sch.scheduleID = r.scheduleID
	LEFT OUTER JOIN #tmpLogLabelsRate AS tmp ON tmp.rateID = r.rateID
	WHERE r.[status] <> 'D'
	AND tmp.rateID IS NULL;

	-- audit logging
	SELECT @thisAreaCode = MIN(areaCode) FROM #tmpLogMessages
	WHILE @thisAreaCode IS NOT NULL BEGIN
		SELECT @minRefID = MIN(refID) FROM #tmpLogMessages WHERE areaCode = @thisAreaCode;

		WHILE @minRefID IS NOT NULL BEGIN
			SET @actionCount = 0;
			SET @changesCount = 0;
			SET @msgjson = NULL;
			SET @subKeyMapJSON = NULL;

			-- there is case where create & update entries are present for same entity
			SELECT @actionCount = COUNT(DISTINCT actionType)
			FROM #tmpLogMessages
			WHERE areaCode = @thisAreaCode
			AND refID = @minRefID;

			SELECT @changesCount = COUNT(rowID), @actionType = MAX(actionType)
			FROM #tmpLogMessages
			WHERE areaCode = @thisAreaCode
			AND refID = @minRefID;

			IF @thisAreaCode = 'SUBTYPE' BEGIN
				SET @subKeyMapJSON = '{ "TYPEID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBSCRIPTION' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "SUBSCRIPTIONID":' + CAST(s.subscriptionID AS varchar(10)) + ', "SUBSCRIPTIONTYPEID":' + CAST(s.typeID AS varchar(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END
			ELSE IF @thisAreaCode = 'SUBADDON' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "SUBSCRIPTIONID":' + CAST(a.subscriptionID AS varchar(10)) + ', "ADDONID":' + CAST(a.addonID AS VARCHAR(10)) + ', "CHILDSETID":' + CAST(a.childSetID AS VARCHAR(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsAddOn AS a ON a.addonID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END
			ELSE IF @thisAreaCode = 'FREQ' BEGIN
				SET @subKeyMapJSON = '{ "FREQUENCYID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBSET' BEGIN
				SET @subKeyMapJSON = '{ "SETID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'RATESCH' BEGIN
				SET @subKeyMapJSON = '{ "SCHEDULEID":' + CAST(@minRefID AS varchar(10)) + ' }';
			END
			ELSE IF @thisAreaCode = 'SUBRATE' BEGIN
				SELECT TOP 1 @subKeyMapJSON = '{ "RATEID":' + CAST(r.rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(r.scheduleID AS varchar(10)) + ' }'
				FROM #tmpLogMessages AS tmp
				INNER JOIN #tmpLogLabelsRate AS r ON r.rateID = tmp.refID
				WHERE tmp.areaCode = @thisAreaCode
				AND tmp.refID = @minRefID;
			END

			IF @actionType = 'UPDATE' AND @actionCount = 1 BEGIN
				SET @prefixMsg = '';

				SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID;

				IF @thisAreaCode = 'SUBTYPE' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription Type ' + QUOTENAME(t.typeName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsType AS t ON t.typeID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBSCRIPTION' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription ' + QUOTENAME(s.subscriptionName) + ' under the Subscription Type ' + QUOTENAME(s.typeName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBADDON' BEGIN
					SELECT TOP 1 @prefixMsg = 'Addon ' + QUOTENAME(st.setName) + ' for Subscription ' + QUOTENAME(s.subscriptionName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsAddOn AS a ON a.addonID = tmp.refID
					INNER JOIN #tmpLogLabelsSet AS st ON st.setID = a.childSetID
					INNER JOIN #tmpLogLabelsSub AS s ON s.subscriptionID = a.subscriptionID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'FREQ' BEGIN
					SELECT TOP 1 @prefixMsg = 'Frequency ' + QUOTENAME(f.frequencyName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsFreq AS f ON f.frequencyID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBSET' BEGIN
					SELECT TOP 1 @prefixMsg = 'Subscription Set ' + QUOTENAME(s.setName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSet AS s ON s.setID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'RATESCH' BEGIN
					SELECT TOP 1 @prefixMsg = 'Rate Schedule ' + QUOTENAME(s.scheduleName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsSched AS s ON s.scheduleID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;
				END
				ELSE IF @thisAreaCode = 'SUBRATE' BEGIN
					SELECT TOP 1 @prefixMsg = 'Rate ' + QUOTENAME(r.rateName) + ' under Rate Schedule ' + QUOTENAME(r.scheduleName) + ' has been updated.'
					FROM #tmpLogMessages AS tmp
					INNER JOIN #tmpLogLabelsRate AS r ON r.rateID = tmp.refID
					WHERE tmp.areaCode = @thisAreaCode
					AND tmp.refID = @minRefID;

					-- if this rate was just created from this import
					IF EXISTS(
						SELECT 1
						FROM sub_rates AS r
						INNER JOIN #tmpSubRates AS tmp ON tmp.[uid] = r.[uid]
							AND tmp.finalAction = 'A'
						WHERE r.rateID = @minRefID
					)
						SET @prefixMsg = 'Newly added ' + @prefixMsg;
				END

				IF @actionCount = 1 AND @changesCount > 1
					SET @prefixMsg = @prefixMsg + ' The following changes have been made:'

				SET @msgjson = @prefixMsg + @crlf + @msgjson;
			END
			ELSE IF @actionType = 'UPDATE' AND @actionCount > 1 BEGIN
				SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID
				ORDER BY actionType;
			END
			ELSE BEGIN
				SELECT TOP 1 @msgjson = msg
				FROM #tmpLogMessages
				WHERE areaCode = @thisAreaCode
				AND refID = @minRefID;
			END

			SET @subKeyMapJSON = ISNULL(@subKeyMapJSON,'{}');

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode=@thisAreaCode, @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=1, @enteredByMemberID=@recordedByMemberID;
			
			SELECT @minRefID = MIN(refID) FROM #tmpLogMessages WHERE areaCode = @thisAreaCode and refID > @minRefID;
		END

		SELECT @thisAreaCode = MIN(areaCode) FROM #tmpLogMessages WHERE areaCode > @thisAreaCode;
	END

	on_done:
	IF OBJECT_ID('tempdb..#tmpSubFrequencies') IS NOT NULL
		DROP TABLE #tmpSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpSets') IS NOT NULL
		DROP TABLE #tmpSets;
	IF OBJECT_ID('tempdb..#tmpSubTypes') IS NOT NULL
		DROP TABLE #tmpSubTypes;
	IF OBJECT_ID('tempdb..#tmpSubscriptions') IS NOT NULL
		DROP TABLE #tmpSubscriptions;
	IF OBJECT_ID('tempdb..#tmpSubAddOns') IS NOT NULL
		DROP TABLE #tmpSubAddOns;
	IF OBJECT_ID('tempdb..#tmpSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpSubRates') IS NOT NULL
		DROP TABLE #tmpSubRates;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequencies') IS NOT NULL
		DROP TABLE #tmpSubRateFrequencies;
	IF OBJECT_ID('tempdb..#tmpSubRateFrequenciesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpSubRateFrequenciesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpSiteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpNewSubRateGroups') IS NOT NULL
		DROP TABLE #tmpNewSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateGroups') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateGroups;
	IF OBJECT_ID('tempdb..#tmpSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpNewSubContentObjs') IS NOT NULL
		DROP TABLE #tmpNewSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpUpdateSubContentObjs') IS NOT NULL
		DROP TABLE #tmpUpdateSubContentObjs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubFrequencies') IS NOT NULL
		DROP TABLE #tmpDeleteSubFrequencies;
	IF OBJECT_ID('tempdb..#tmpDeleteSets') IS NOT NULL
		DROP TABLE #tmpDeleteSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptionSets') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptionSets;
	IF OBJECT_ID('tempdb..#tmpDeleteSubAddOns') IS NOT NULL
		DROP TABLE #tmpDeleteSubAddOns;
	IF OBJECT_ID('tempdb..#tmpDeleteSubTypes') IS NOT NULL
		DROP TABLE #tmpDeleteSubTypes;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateSchedules') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateSchedules;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRates') IS NOT NULL
		DROP TABLE #tmpDeleteSubRates;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqs') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqs;
	IF OBJECT_ID('tempdb..#tmpDeleteSubRateFreqMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpDeleteSubRateFreqMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteSubscriptions') IS NOT NULL
		DROP TABLE #tmpDeleteSubscriptions;
	IF OBJECT_ID('tempdb..#tmpLogLabelsType') IS NOT NULL
		DROP TABLE #tmpLogLabelsType;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSub') IS NOT NULL
		DROP TABLE #tmpLogLabelsSub;
	IF OBJECT_ID('tempdb..#tmpLogLabelsAddOn') IS NOT NULL
		DROP TABLE #tmpLogLabelsAddOn;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSet') IS NOT NULL
		DROP TABLE #tmpLogLabelsSet;
	IF OBJECT_ID('tempdb..#tmpLogLabelsFreq') IS NOT NULL
		DROP TABLE #tmpLogLabelsFreq;
	IF OBJECT_ID('tempdb..#tmpLogLabelsSched') IS NOT NULL
		DROP TABLE #tmpLogLabelsSched;
	IF OBJECT_ID('tempdb..#tmpLogLabelsRate') IS NOT NULL
		DROP TABLE #tmpLogLabelsRate;
	IF OBJECT_ID('tempdb..#tmpLogMessages') IS NOT NULL
		DROP TABLE #tmpLogMessages;
	IF OBJECT_ID('tempdb..#tmpAuditLogMessages') IS NOT NULL
		DROP TABLE #tmpAuditLogMessages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@msgjson varchar(max),
@subKeyMapJSON varchar(max),
@isImport bit = 0,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF @isImport = 1
		SET @msgjson = '[Import] ' + @msgjson;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog_SUBSETUP", "d": {
		"AUDITCODE":"SUBSETUP",
		"AREACODE":"' + @areaCode + '",
		"ORGID":' + cast(@orgID as varchar(10)) + ',
		"SITEID":' + cast(@siteID as varchar(10)) + ',
		"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
		"ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
		"MESSAGE":"' + @msgjson + '",
		"SUBKEYMAP":' + @subKeyMapJSON + ' } }');

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_moveRate
@scheduleID int,
@rateID int,
@dir varchar(4),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @rateOrder int, @rateName varchar(200), @scheduleName varchar(200), @newPos int,
		@msg varchar(max), @subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);
	
	SELECT @siteID = srs.siteID, @orgID = s.orgID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, 
		@scheduleName = srs.scheduleName, @rateOrder = sr.rateOrder
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	INNER JOIN dbo.sites AS s ON s.siteID = srs.siteID
	WHERE rateID = @rateID;

	IF @rateOrder IS NULL
		RAISERROR('invalid rate',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			UPDATE r
			SET r.rateOrder = r.rateOrder + 1
			FROM dbo.sub_rates as r
			WHERE r.scheduleID = @scheduleID
			AND r.[status] = 'A'
			AND r.rateOrder >= @rateOrder - 1;

			UPDATE dbo.sub_rates
			SET rateOrder = rateOrder  - 2
			WHERE rateID = @rateID;
		END
		ELSE BEGIN
			UPDATE r
			SET r.rateOrder = r.rateOrder - 1
			FROM dbo.sub_rates as r
			WHERE r.scheduleID = @scheduleID
			AND r.[status] = 'A'
			AND r.rateOrder <= @rateOrder + 1;
			
			UPDATE dbo.sub_rates
			SET rateOrder = rateOrder  + 2
			WHERE rateID = @rateID;
		END
	
		EXEC dbo.sub_reorderRates @rateID=@rateID;

		SELECT @newPos = rateOrder
		FROM dbo.sub_rates
		WHERE rateID = @rateID;

		IF @newPos <> @rateOrder BEGIN
			SET @subKeyMapJSON = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

			SET @msg =  'Rate ' + QUOTENAME(@rateName) + ' under the Rate Schedule ' + QUOTENAME(@scheduleName) + ' moved to position ' + CAST(@newPos AS varchar(10));
			SET @msg = STRING_ESCAPE(@msg,'json');

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_moveRateToPosition
@rateID int,
@pos int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @orgID int, @rateOrder int, @scheduleID int, @rateName varchar(200), @scheduleName varchar(200), 
		@msg varchar(max), @subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);
	declare @tmp table (newOrder int NOT NULL, rateID int NOT NULL, rateOrder int NOT NULL);

	SELECT @siteID = srs.siteID, @orgID = s.orgID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, 
		@scheduleName = srs.scheduleName, @rateOrder = sr.rateOrder
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	INNER JOIN dbo.sites AS s ON s.siteID = srs.siteID
	WHERE rateID = @rateID;

	IF @rateOrder <> @pos BEGIN
		SET @subKeyMapJSON = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msg =  'Rate ' + QUOTENAME(@rateName) + ' under the Rate Schedule ' + QUOTENAME(@scheduleName) + ' moved to position ' + CAST(@pos AS varchar(10));
	END

	BEGIN TRAN;
		if @rateOrder > @pos begin
			insert into @tmp (rateID, rateOrder, newOrder)
			select rateID, rateOrder + 1, ROW_NUMBER() OVER (order by rateOrder)
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.scheduleID = @scheduleID
			and (r.rateOrder >= @pos and r.rateOrder <= @rateOrder);

			update rates
			set rates.rateOrder = t.rateOrder
			from dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
			where rates.scheduleID = @scheduleID
			and rates.status = 'A';
		end else begin
			insert into @tmp (rateID, rateOrder, newOrder)
			select rateID, rateOrder, ROW_NUMBER() OVER (order by rateOrder)
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.scheduleID = @scheduleID
			and rateOrder <= @pos
			and r.rateID <> @rateID;

			update rates
			set rates.rateOrder = t.neworder
			from dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
			where rates.scheduleID = @scheduleID
			and rates.status = 'A'
		end

		update dbo.sub_rates
		set rateOrder = @pos
		where rateID = @rateID;

		IF ISNULL(@msg,'') <> '' BEGIN
			SET @msg = STRING_ESCAPE(@msg,'json');
			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_sortSubInSet
@siteID int,
@setID int,
@dir char(4),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (newOrder int IDENTITY(1,1) NOT NULL, subscriptionID int NOT NULL);
	DECLARE @setName VARCHAR(200), @orgID int, @msg varchar(max);

	SELECT @setName = ss.setName, @orgID = s.orgID
	FROM dbo.sub_sets AS ss
	INNER JOIN dbo.sites AS s ON s.siteID = ss.siteID
	WHERE ss.setID = @setID;

	IF @dir = 'asc'
		INSERT INTO @tmp (subscriptionID)
		SELECT ss.subscriptionID
		FROM dbo.sub_subscriptionSets ss 
		INNER JOIN dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
		INNER JOIN dbo.sub_Types st on st.typeID = subs.typeID 
			and st.siteID = @siteID
		INNER JOIN dbo.sub_sets sets on sets.setID = ss.setID 
			and sets.siteID = @siteID
		WHERE ss.setID = @setID
		ORDER BY st.typeName ASC, subs.subscriptionName ASC;
	ELSE
		INSERT INTO @tmp (subscriptionID)
		SELECT ss.subscriptionID
		FROM dbo.sub_subscriptionSets ss 
		INNER JOIN dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
		INNER JOIN dbo.sub_Types st on st.typeID = subs.typeID 
			and st.siteID = @siteID
		INNER JOIN dbo.sub_sets sets on sets.setID = ss.setID 
			and sets.siteID = @siteID
		WHERE ss.setID = @setID
		ORDER BY st.typeName DESC, subs.subscriptionName DESC;

	BEGIN TRAN;
		UPDATE ss
		SET ss.orderNum = tmp.newOrder
		FROM dbo.sub_subscriptionSets ss
		INNER JOIN @tmp AS tmp ON tmp.subscriptionID = ss.subscriptionID
		WHERE ss.setID = @setID;

		EXEC dbo.subs_reorderSubscriptionSets @setID=@setID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS VARCHAR(10))+' }';

		IF @dir = 'asc'
			SET @msg = 'All Subscriptions in the Set ' + QUOTENAME(@setName) + ' have been arranged in ascending order.';
		ELSE
			SET @msg = 'All Subscriptions in the Set ' + QUOTENAME(@setName) + ' have been arranged in descending order.';
		
		SET @msg = STRING_ESCAPE(@msg,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
			@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;
			
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.sub_updateFrequency 
@frequencyID int, 
@frequencyName varchar(50), 
@frequency int, 
@frequencyShortName varchar (10), 
@rateRequired bit, 
@hasInstallments bit = 0, 
@monthlyInterval int = null, 
@isSystemRate bit = 0,
@siteID int, 
@status char(1),
@recordedByMemberID int,
@successInd bit OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @prevFrequencyName varchar(50),  @msg varchar(max), @crlf varchar(2);
	SET @crlf = char(13) + char(10);
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	set @successInd = 0;

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;
	CREATE TABLE #tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Frequency Name] varchar(max), [Number of Installments] varchar(max), 
		[Frequency Short Name] varchar(max), [Rate Required] varchar(max), [Has Installments] varchar(max), [Monthly Interval] varchar(max));

	INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
	VALUES ('DATATYPECODE', 'STRING', 'INTEGER', 'STRING', 'BIT', 'BIT', 'INTEGER');

	INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
	SELECT 'OLDVAL', frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, monthlyInterval
	FROM dbo.sub_frequencies
	WHERE frequencyID = @frequencyID;

	SELECT @prevFrequencyName = [Frequency Name]
	FROM #tmpAuditLogData
	WHERE rowCode = 'OLDVAL';

	IF NOT EXISTS (select frequencyID from dbo.sub_frequencies where siteID = @siteID and frequencyID <> @frequencyID and [status] <> 'D' and (frequencyName = @frequencyName or frequencyShortName = @frequencyShortName)) BEGIN
		BEGIN TRAN;
			UPDATE dbo.sub_frequencies
			SET	frequencyName = @frequencyName, 
				frequency = @frequency, 
				frequencyShortName = @frequencyShortName, 
				rateRequired = @rateRequired, 
				hasInstallments = @hasInstallments, 
				monthlyInterval = @monthlyInterval, 
				[status] =  @status
			WHERE frequencyID = @frequencyID;
	
			-- update any virtual group conditions
			-- yes, the frequencyID here should be varchar
			UPDATE dbo.ams_virtualGroupConditions
			SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
			WHERE orgID = @orgID
			and left(fieldCode,4) = 'sub_'	
			and conditionID in (
				select c.conditionID 
				from dbo.ams_virtualGroupConditions as c
				inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
					and cv.conditionValue = cast(@frequencyID as varchar(10))
				inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
					and k.conditionKey = 'subFrequency'
				where c.orgID = @orgID
			);
			
			set @successInd = 1;

			INSERT INTO #tmpAuditLogData ([rowCode], [Frequency Name], [Number of Installments], [Frequency Short Name], [Rate Required], [Has Installments], [Monthly Interval])
			SELECT 'NEWVAL', frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, monthlyInterval
			FROM dbo.sub_frequencies
			WHERE frequencyID = @frequencyID;

			EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
		
			IF ISNULL(@msg,'') <> '' BEGIN
				DECLARE @subKeyMapJSON varchar(100) = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
				SET @msg = 'Frequency [' + STRING_ESCAPE(@prevFrequencyName,'json') + '] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

				EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msg,
					@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
			END
		COMMIT TRAN;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROCEDURE dbo.sub_updateRateSchedule
@siteID int,
@scheduleID int,
@scheduleName varchar(200),
@scheduleUID uniqueidentifier,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;
	CREATE TABLE #tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Rate Schedule Name] varchar(max), [API ID] varchar(max));

	declare @checkScheduleID int, @orgID int, @prevScheduleName varchar(200), @msg varchar(max), @crlf varchar(2);

	IF len(@scheduleName) = 0
		RAISERROR('Schedule name invalid.',16,1);
	
	-- check to see if valid name
	SELECT TOP 1 @checkScheduleID = scheduleID
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID <> @scheduleID
	AND scheduleName = @scheduleName
	AND [status] <> 'D';

	IF @checkScheduleID is not null
		RAISERROR('Schedule name already in use.',16,1);

	select @checkScheduleID = scheduleID, @prevScheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID = @scheduleID;

	IF @checkScheduleID is null
		RAISERROR('Schedule name invalid.',16,1);

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	SET @crlf = char(13) + char(10);

	INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
	VALUES ('DATATYPECODE', 'STRING', 'STRING');

	INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
	SELECT 'OLDVAL', scheduleName, [uid]
	FROM dbo.sub_rateSchedules
	WHERE scheduleID = @checkScheduleID;

	BEGIN TRAN
		update dbo.sub_rateSchedules
		set scheduleName = @scheduleName
		where scheduleID = @checkScheduleID;

		IF @scheduleUID is not null
			update dbo.sub_rateSchedules
			set [uid] = @scheduleUID
			where scheduleID = @checkScheduleID;

		UPDATE dbo.ams_virtualGroupConditions
		SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
		WHERE orgID = @orgID
		and left(fieldCode,4) = 'sub_'
		and conditionID in (
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
				and k.conditionKey = 'subRate'
			inner join dbo.sub_rates as r on r.rateID = cast(cv.conditionValue as int)
				and cv.conditionValue > 0
				and r.scheduleID = @checkScheduleID
			where c.orgID = @orgID
		);

		INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
		SELECT 'NEWVAL', scheduleName, [uid]
		FROM dbo.sub_rateSchedules
		WHERE scheduleID = @checkScheduleID;

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
		
		IF ISNULL(@msg,'') <> '' BEGIN
			DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID":'+CAST(@checkScheduleID AS varchar(10))+' }';
			SET @msg = 'Rate Schedule [' + STRING_ESCAPE(@prevScheduleName,'json') + '] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO