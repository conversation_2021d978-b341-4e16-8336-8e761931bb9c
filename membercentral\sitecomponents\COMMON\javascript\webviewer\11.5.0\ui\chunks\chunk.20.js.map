{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageManipulationOverlay/PageAdditionalControls/PageAdditionalControls.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageAdditionalControls/PageAdditionalControlsContainer.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageAdditionalControls/index.js"], "names": ["PageAdditionalControls", "props", "t", "useTranslation", "moveToTop", "moveToBottom", "DataElementWrapper", "dataElement", "className", "aria-label", "onClick", "<PERSON><PERSON>", "title", "img", "role", "propTypes", "pageIndexes", "PropTypes", "arrayOf", "number", "warn", "bool", "PageAdditionalControlsContainer", "dispatch", "useDispatch", "pageNumbers", "noPagesSelectedWarning", "movePagesToTop", "isMobile", "actions", "closeElement", "DataElements", "PAGE_MANIPULATION_OVERLAY", "movePagesToBottom"], "mappings": "yIAgDeA,MA3Cf,SAAgCC,GAC9B,IAAQC,EAAMC,cAAND,EACAE,EAA4BH,EAA5BG,UAAWC,EAAiBJ,EAAjBI,aAEnB,OACE,oCACE,kBAACC,EAAA,EAAkB,CACjBC,YAAY,+BACZC,UAAU,OACVC,aAAW,gBAEVP,EAAE,yCAEL,kBAACI,EAAA,EAAkB,CACjBE,UAAU,MACVD,YAAY,YACZG,QAASN,GAET,kBAACO,EAAA,EAAM,CACLC,MAAM,uBACNC,IAAI,oBACJC,KAAK,SACLJ,QAASN,IAEX,yBAAKI,UAAU,SAAUN,EAAE,sBAE7B,kBAACI,EAAA,EAAkB,CACjBE,UAAU,MACVD,YAAY,eACZG,QAASL,GAET,kBAACM,EAAA,EAAM,CACLC,MAAM,0BACNC,IAAI,sBACJC,KAAK,SACLJ,QAASL,IAEX,yBAAKG,UAAU,SAASN,EAAE,2B,sDCjC5Ba,EAAY,CAChBC,YAAaC,IAAUC,QAAQD,IAAUE,QACzCC,KAAMH,IAAUI,MAGlB,SAASC,EAAgCrB,GACvC,IAAMsB,EAAWC,cACTC,EAAsBxB,EAAtBwB,YAAaL,EAASnB,EAATmB,KAkBrB,OACE,kBAAC,EAAsB,CACrBhB,UAlBc,WACZgB,GACDM,YAAuBD,EAAaF,IAAaI,YAAeF,GAEjEE,YAAeF,GAEjBG,eAAcL,EAASM,IAAQC,aAAaC,IAAaC,6BAavD3B,aAXiB,WACfe,GACDM,YAAuBD,EAAaF,IAAaU,YAAkBR,GAEpEQ,YAAkBR,GAEpBG,eAAcL,EAASM,IAAQC,aAAaC,IAAaC,+BAU7DV,EAAgCP,UAAYA,EAE7BO,QC1CAA", "file": "chunks/chunk.20.js", "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport Button from 'components/Button';\n\nfunction PageAdditionalControls(props) {\n  const { t } = useTranslation();\n  const { moveToTop, moveToBottom } = props;\n\n  return (\n    <>\n      <DataElementWrapper\n        dataElement=\"pageAdditionalControlsHeader\"\n        className=\"type\"\n        aria-label=\"presentation\"\n      >\n        {t('option.thumbnailsControlOverlay.move')}\n      </DataElementWrapper>\n      <DataElementWrapper\n        className=\"row\"\n        dataElement=\"moveToTop\"\n        onClick={moveToTop}\n      >\n        <Button\n          title=\"action.movePageToTop\"\n          img=\"icon-page-move-up\"\n          role=\"option\"\n          onClick={moveToTop}\n        />\n        <div className=\"title\" >{t('action.moveToTop')}</div>\n      </DataElementWrapper>\n      <DataElementWrapper\n        className=\"row\"\n        dataElement=\"moveToBottom\"\n        onClick={moveToBottom}\n      >\n        <Button\n          title=\"action.movePageToBottom\"\n          img=\"icon-page-move-down\"\n          role=\"option\"\n          onClick={moveToBottom}\n        />\n        <div className=\"title\">{t('action.moveToBottom')}</div>\n      </DataElementWrapper>\n    </>\n  );\n}\n\nexport default PageAdditionalControls;", "import React from 'react';\nimport PageAdditionalControls from './PageAdditionalControls';\nimport { movePagesToBottom, movePagesToTop, noPagesSelectedWarning } from 'helpers/pageManipulationFunctions';\nimport { useDispatch } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport { isMobile } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\n\nconst propTypes = {\n  pageIndexes: PropTypes.arrayOf(PropTypes.number),\n  warn: PropTypes.bool,\n};\n\nfunction PageAdditionalControlsContainer(props) {\n  const dispatch = useDispatch();\n  const { pageNumbers, warn } = props;\n\n  const moveToTop = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToTop(pageNumbers);\n    } else {\n      movePagesToTop(pageNumbers);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  const moveToBottom = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && movePagesToBottom(pageNumbers);\n    } else {\n      movePagesToBottom(pageNumbers);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  return (\n    <PageAdditionalControls\n      moveToTop={moveToTop}\n      moveToBottom={moveToBottom}\n    />\n  );\n}\n\nPageAdditionalControlsContainer.propTypes = propTypes;\n\nexport default PageAdditionalControlsContainer;", "import PageAdditionalControlsContainer from './PageAdditionalControlsContainer';\n\nexport default PageAdditionalControlsContainer;"], "sourceRoot": ""}