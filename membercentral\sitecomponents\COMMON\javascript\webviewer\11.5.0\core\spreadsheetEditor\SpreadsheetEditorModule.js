!(function (t) {
	var e = {};
	function r(n) {
		if (e[n]) return e[n].exports;
		var o = (e[n] = { i: n, l: !1, exports: {} });
		return t[n].call(o.exports, o, o.exports, r), (o.l = !0), o.exports;
	}
	(r.m = t),
		(r.c = e),
		(r.d = function (t, e, n) {
			r.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: n });
		}),
		(r.r = function (t) {
			"undefined" != typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }),
				Object.defineProperty(t, "__esModule", { value: !0 });
		}),
		(r.t = function (t, e) {
			if ((1 & e && (t = r(t)), 8 & e)) return t;
			if (4 & e && "object" == typeof t && t && t.__esModule) return t;
			var n = Object.create(null);
			if (
				(r.r(n),
				Object.defineProperty(n, "default", { enumerable: !0, value: t }),
				2 & e && "string" != typeof t)
			)
				for (var o in t)
					r.d(
						n,
						o,
						function (e) {
							return t[e];
						}.bind(null, o),
					);
			return n;
		}),
		(r.n = function (t) {
			var e =
				t && t.__esModule
					? function () {
							return t.default;
						}
					: function () {
							return t;
						};
			return r.d(e, "a", e), e;
		}),
		(r.o = function (t, e) {
			return Object.prototype.hasOwnProperty.call(t, e);
		}),
		(r.p = "/core/spreadsheetEditor"),
		r((r.s = 0));
})([
	function (t, e, r) {
		t.exports = r(1);
	},
	function (t, e) {
		function r(t) {
			return (r =
				"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
					? function (t) {
							return typeof t;
						}
					: function (t) {
							return t &&
								"function" == typeof Symbol &&
								t.constructor === Symbol &&
								t !== Symbol.prototype
								? "symbol"
								: typeof t;
						})(t);
		}
		function n() {
			"use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
			n = function () {
				return t;
			};
			var t = {},
				e = Object.prototype,
				o = e.hasOwnProperty,
				a =
					Object.defineProperty ||
					function (t, e, r) {
						t[e] = r.value;
					},
				i = "function" == typeof Symbol ? Symbol : {},
				c = i.iterator || "@@iterator",
				u = i.asyncIterator || "@@asyncIterator",
				s = i.toStringTag || "@@toStringTag";
			function l(t, e, r) {
				return (
					Object.defineProperty(t, e, {
						value: r,
						enumerable: !0,
						configurable: !0,
						writable: !0,
					}),
					t[e]
				);
			}
			try {
				l({}, "");
			} catch (t) {
				l = function (t, e, r) {
					return (t[e] = r);
				};
			}
			function f(t, e, r, n) {
				var o = e && e.prototype instanceof y ? e : y,
					i = Object.create(o.prototype),
					c = new O(n || []);
				return a(i, "_invoke", { value: _(t, r, c) }), i;
			}
			function p(t, e, r) {
				try {
					return { type: "normal", arg: t.call(e, r) };
				} catch (t) {
					return { type: "throw", arg: t };
				}
			}
			t.wrap = f;
			var h = {};
			function y() {}
			function d() {}
			function v() {}
			var g = {};
			l(g, c, function () {
				return this;
			});
			var m = Object.getPrototypeOf,
				b = m && m(m(M([])));
			b && b !== e && o.call(b, c) && (g = b);
			var w = (v.prototype = y.prototype = Object.create(g));
			function x(t) {
				["next", "throw", "return"].forEach(function (e) {
					l(t, e, function (t) {
						return this._invoke(e, t);
					});
				});
			}
			function E(t, e) {
				var n;
				a(this, "_invoke", {
					value: function (a, i) {
						function c() {
							return new e(function (n, c) {
								!(function n(a, i, c, u) {
									var s = p(t[a], t, i);
									if ("throw" !== s.type) {
										var l = s.arg,
											f = l.value;
										return f && "object" == r(f) && o.call(f, "__await")
											? e.resolve(f.__await).then(
													function (t) {
														n("next", t, c, u);
													},
													function (t) {
														n("throw", t, c, u);
													},
												)
											: e.resolve(f).then(
													function (t) {
														(l.value = t), c(l);
													},
													function (t) {
														return n("throw", t, c, u);
													},
												);
									}
									u(s.arg);
								})(a, i, n, c);
							});
						}
						return (n = n ? n.then(c, c) : c());
					},
				});
			}
			function _(t, e, r) {
				var n = "suspendedStart";
				return function (o, a) {
					if ("executing" === n)
						throw new Error("Generator is already running");
					if ("completed" === n) {
						if ("throw" === o) throw a;
						return P();
					}
					for (r.method = o, r.arg = a; ; ) {
						var i = r.delegate;
						if (i) {
							var c = L(i, r);
							if (c) {
								if (c === h) continue;
								return c;
							}
						}
						if ("next" === r.method) r.sent = r._sent = r.arg;
						else if ("throw" === r.method) {
							if ("suspendedStart" === n) throw ((n = "completed"), r.arg);
							r.dispatchException(r.arg);
						} else "return" === r.method && r.abrupt("return", r.arg);
						n = "executing";
						var u = p(t, e, r);
						if ("normal" === u.type) {
							if (((n = r.done ? "completed" : "suspendedYield"), u.arg === h))
								continue;
							return { value: u.arg, done: r.done };
						}
						"throw" === u.type &&
							((n = "completed"), (r.method = "throw"), (r.arg = u.arg));
					}
				};
			}
			function L(t, e) {
				var r = e.method,
					n = t.iterator[r];
				if (void 0 === n)
					return (
						(e.delegate = null),
						("throw" === r &&
							t.iterator.return &&
							((e.method = "return"),
							(e.arg = void 0),
							L(t, e),
							"throw" === e.method)) ||
							("return" !== r &&
								((e.method = "throw"),
								(e.arg = new TypeError(
									"The iterator does not provide a '" + r + "' method",
								)))),
						h
					);
				var o = p(n, t.iterator, e.arg);
				if ("throw" === o.type)
					return (e.method = "throw"), (e.arg = o.arg), (e.delegate = null), h;
				var a = o.arg;
				return a
					? a.done
						? ((e[t.resultName] = a.value),
							(e.next = t.nextLoc),
							"return" !== e.method && ((e.method = "next"), (e.arg = void 0)),
							(e.delegate = null),
							h)
						: a
					: ((e.method = "throw"),
						(e.arg = new TypeError("iterator result is not an object")),
						(e.delegate = null),
						h);
			}
			function S(t) {
				var e = { tryLoc: t[0] };
				1 in t && (e.catchLoc = t[1]),
					2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
					this.tryEntries.push(e);
			}
			function j(t) {
				var e = t.completion || {};
				(e.type = "normal"), delete e.arg, (t.completion = e);
			}
			function O(t) {
				(this.tryEntries = [{ tryLoc: "root" }]),
					t.forEach(S, this),
					this.reset(!0);
			}
			function M(t) {
				if (t) {
					var e = t[c];
					if (e) return e.call(t);
					if ("function" == typeof t.next) return t;
					if (!isNaN(t.length)) {
						var r = -1,
							n = function e() {
								for (; ++r < t.length; )
									if (o.call(t, r)) return (e.value = t[r]), (e.done = !1), e;
								return (e.value = void 0), (e.done = !0), e;
							};
						return (n.next = n);
					}
				}
				return { next: P };
			}
			function P() {
				return { value: void 0, done: !0 };
			}
			return (
				(d.prototype = v),
				a(w, "constructor", { value: v, configurable: !0 }),
				a(v, "constructor", { value: d, configurable: !0 }),
				(d.displayName = l(v, s, "GeneratorFunction")),
				(t.isGeneratorFunction = function (t) {
					var e = "function" == typeof t && t.constructor;
					return (
						!!e &&
						(e === d || "GeneratorFunction" === (e.displayName || e.name))
					);
				}),
				(t.mark = function (t) {
					return (
						Object.setPrototypeOf
							? Object.setPrototypeOf(t, v)
							: ((t.__proto__ = v), l(t, s, "GeneratorFunction")),
						(t.prototype = Object.create(w)),
						t
					);
				}),
				(t.awrap = function (t) {
					return { __await: t };
				}),
				x(E.prototype),
				l(E.prototype, u, function () {
					return this;
				}),
				(t.AsyncIterator = E),
				(t.async = function (e, r, n, o, a) {
					void 0 === a && (a = Promise);
					var i = new E(f(e, r, n, o), a);
					return t.isGeneratorFunction(r)
						? i
						: i.next().then(function (t) {
								return t.done ? t.value : i.next();
							});
				}),
				x(w),
				l(w, s, "Generator"),
				l(w, c, function () {
					return this;
				}),
				l(w, "toString", function () {
					return "[object Generator]";
				}),
				(t.keys = function (t) {
					var e = Object(t),
						r = [];
					for (var n in e) r.push(n);
					return (
						r.reverse(),
						function t() {
							for (; r.length; ) {
								var n = r.pop();
								if (n in e) return (t.value = n), (t.done = !1), t;
							}
							return (t.done = !0), t;
						}
					);
				}),
				(t.values = M),
				(O.prototype = {
					constructor: O,
					reset: function (t) {
						if (
							((this.prev = 0),
							(this.next = 0),
							(this.sent = this._sent = void 0),
							(this.done = !1),
							(this.delegate = null),
							(this.method = "next"),
							(this.arg = void 0),
							this.tryEntries.forEach(j),
							!t)
						)
							for (var e in this)
								"t" === e.charAt(0) &&
									o.call(this, e) &&
									!isNaN(+e.slice(1)) &&
									(this[e] = void 0);
					},
					stop: function () {
						this.done = !0;
						var t = this.tryEntries[0].completion;
						if ("throw" === t.type) throw t.arg;
						return this.rval;
					},
					dispatchException: function (t) {
						if (this.done) throw t;
						var e = this;
						function r(r, n) {
							return (
								(i.type = "throw"),
								(i.arg = t),
								(e.next = r),
								n && ((e.method = "next"), (e.arg = void 0)),
								!!n
							);
						}
						for (var n = this.tryEntries.length - 1; n >= 0; --n) {
							var a = this.tryEntries[n],
								i = a.completion;
							if ("root" === a.tryLoc) return r("end");
							if (a.tryLoc <= this.prev) {
								var c = o.call(a, "catchLoc"),
									u = o.call(a, "finallyLoc");
								if (c && u) {
									if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
									if (this.prev < a.finallyLoc) return r(a.finallyLoc);
								} else if (c) {
									if (this.prev < a.catchLoc) return r(a.catchLoc, !0);
								} else {
									if (!u)
										throw new Error("try statement without catch or finally");
									if (this.prev < a.finallyLoc) return r(a.finallyLoc);
								}
							}
						}
					},
					abrupt: function (t, e) {
						for (var r = this.tryEntries.length - 1; r >= 0; --r) {
							var n = this.tryEntries[r];
							if (
								n.tryLoc <= this.prev &&
								o.call(n, "finallyLoc") &&
								this.prev < n.finallyLoc
							) {
								var a = n;
								break;
							}
						}
						a &&
							("break" === t || "continue" === t) &&
							a.tryLoc <= e &&
							e <= a.finallyLoc &&
							(a = null);
						var i = a ? a.completion : {};
						return (
							(i.type = t),
							(i.arg = e),
							a
								? ((this.method = "next"), (this.next = a.finallyLoc), h)
								: this.complete(i)
						);
					},
					complete: function (t, e) {
						if ("throw" === t.type) throw t.arg;
						return (
							"break" === t.type || "continue" === t.type
								? (this.next = t.arg)
								: "return" === t.type
									? ((this.rval = this.arg = t.arg),
										(this.method = "return"),
										(this.next = "end"))
									: "normal" === t.type && e && (this.next = e),
							h
						);
					},
					finish: function (t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var r = this.tryEntries[e];
							if (r.finallyLoc === t)
								return this.complete(r.completion, r.afterLoc), j(r), h;
						}
					},
					catch: function (t) {
						for (var e = this.tryEntries.length - 1; e >= 0; --e) {
							var r = this.tryEntries[e];
							if (r.tryLoc === t) {
								var n = r.completion;
								if ("throw" === n.type) {
									var o = n.arg;
									j(r);
								}
								return o;
							}
						}
						throw new Error("illegal catch attempt");
					},
					delegateYield: function (t, e, r) {
						return (
							(this.delegate = { iterator: M(t), resultName: e, nextLoc: r }),
							"next" === this.method && (this.arg = void 0),
							h
						);
					},
				}),
				t
			);
		}
		function o(t, e, r, n, o, a, i) {
			try {
				var c = t[a](i),
					u = c.value;
			} catch (t) {
				return void r(t);
			}
			c.done ? e(u) : Promise.resolve(u).then(n, o);
		}
		function a(t) {
			return function () {
				var e = this,
					r = arguments;
				return new Promise(function (n, a) {
					var i = t.apply(e, r);
					function c(t) {
						o(i, n, a, c, u, "next", t);
					}
					function u(t) {
						o(i, n, a, c, u, "throw", t);
					}
					c(void 0);
				});
			};
		}
		var i, c;
		self.importScripts("SpreadsheetEditorEngine.js");
		var u = "startup",
			s = "initialize",
			l = "jsonToXlsx",
			f = "excelToJson",
			p = "engineLoaded",
			h = "engineLoadedError";
		function y() {
			return d.apply(this, arguments);
		}
		function d() {
			return (d = a(
				n().mark(function t() {
					return n().wrap(
						function (t) {
							for (;;)
								switch ((t.prev = t.next)) {
									case 0:
										return (t.prev = 0), (t.next = 3), LtExcelEngineModule();
									case 3:
										(i = t.sent), self.postMessage({ type: p }), (t.next = 10);
										break;
									case 7:
										(t.prev = 7),
											(t.t0 = t.catch(0)),
											self.postMessage({
												type: h,
												message:
													"Error loading Leadtools.ExcelEngine.wasm, ".concat(
														t.t0.message,
													),
											});
									case 10:
									case "end":
										return t.stop();
								}
						},
						t,
						null,
						[[0, 7]],
					);
				}),
			)).apply(this, arguments);
		}
		function v(t) {
			var e = null;
			try {
				var r = t.data.value;
				if (r) {
					var n = i.lengthBytesUTF8(r) + 1;
					(e = i._malloc(n)), i.stringToUTF8(r, e, n);
				}
				var o = i._L_ExcelEngine_WASM_Initialize(e);
				self.postMessage({ type: t.data.action, message: "success", data: o });
			} catch (e) {
				self.postMessage({
					type: t.data.action,
					errorMessage: "Error Initializing Engine, ".concat(e),
				});
			} finally {
				e && i._free(e);
			}
		}
		function g(t) {
			var e = t.data.value,
				r = i._malloc(4),
				n = i.lengthBytesUTF8(e) + 1,
				o = i._malloc(n);
			i.stringToUTF8(e, o, n);
			var a = i._L_ExcelEngine_WASM_JsonToXlsx(o, r),
				c = i.HEAP32[r >> 2],
				u = new Uint8Array(i.HEAPU8.buffer, a, c);
			self.postMessage({ type: l, message: u }),
				i._free(o),
				i._free(a),
				i._free(r);
		}
		function m(t) {
			var e, r, n;
			try {
				var o = t.data.value;
				n = i._malloc(4);
				var a = new Uint8Array(o);
				(r = i._malloc(a.length)),
					i.HEAPU8.set(a, r),
					(e = i._L_ExcelEngine_WASM_ExcelToJson(r, a.length, n));
				var c = i.HEAP32[n >> 2],
					u = new Uint8Array(i.HEAPU8).subarray(e, e + c),
					s = new TextDecoder("utf-8").decode(u);
				self.postMessage({ type: t.data.action, message: s });
			} catch (e) {
				self.postMessage({
					type: t.data.action,
					errorMessage: 'Error converting XLSX to supported format: "'.concat(
						e,
						'"',
					),
				});
			} finally {
				void 0 !== e && i._free(e),
					void 0 !== r && i._free(r),
					void 0 !== n && i._free(n);
			}
		}
		(self.onmessage = (function () {
			var t = a(
				n().mark(function t(e) {
					return n().wrap(function (t) {
						for (;;)
							switch ((t.prev = t.next)) {
								case 0:
									(c = e.data.action),
										(t.t0 = e.data.action),
										(t.next =
											t.t0 === u
												? 4
												: t.t0 === s
													? 7
													: t.t0 === l
														? 9
														: t.t0 === f
															? 11
															: 13);
									break;
								case 4:
									return (t.next = 6), y();
								case 6:
									return t.abrupt("break", 14);
								case 7:
									return v(e), t.abrupt("break", 14);
								case 9:
									return g(e), t.abrupt("break", 14);
								case 11:
									return m(e), t.abrupt("break", 14);
								case 13:
									console.error(
										"Unknown SpreadsheetEditor worker message: ".concat(
											e.data.action,
											".",
										),
									);
								case 14:
								case "end":
									return t.stop();
							}
					}, t);
				}),
			);
			return function (e) {
				return t.apply(this, arguments);
			};
		})()),
			(self.onerror = function (t) {
				this.postMessage({
					type: c,
					errorMessage: "Error in action [".concat(c, "]."),
					error: t,
				}),
					console.error(t);
			});
	},
]);
