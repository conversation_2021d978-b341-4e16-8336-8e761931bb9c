(window.webpackJsonp = window.webpackJsonp || []).push([
	[94],
	{
		2061: function (e, t, a) {
			"use strict";
			a.r(t);
			a(97), a(58), a(44);
			var n = a(0),
				s = a.n(n),
				i = a(82),
				o = a(3),
				r = a.n(o),
				c = a(68),
				l = a(70),
				p = a(100),
				u =
					(a(16),
					function (e) {
						return "string" != typeof e
							? ""
							: e.charAt(0).toUpperCase() + e.slice(1);
					});
			function y() {
				return (y = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var a = arguments[t];
								for (var n in a)
									Object.prototype.hasOwnProperty.call(a, n) && (e[n] = a[n]);
							}
							return e;
						}).apply(this, arguments);
			}
			var f = {
					actionType: r.a.oneOf(Object.values(p.b)).isRequired,
					isFlyoutItem: r.a.bool,
					style: r.a.object,
					className: r.a.string,
				},
				b = Object(n.forwardRef)(function (e, t) {
					var a = e.isFlyoutItem,
						n = e.actionType,
						o = e.style,
						r = e.className,
						p = "cell".concat(u(n)),
						f = l.b[p],
						b = f.dataElement,
						d = f.icon,
						m = f.title,
						v = function () {};
					return a
						? s.a.createElement(
								c.a,
								y({}, e, { ref: t, onClick: v, additionalClass: "" }),
							)
						: s.a.createElement(i.a, {
								key: n,
								isActive: !1,
								onClick: v,
								dataElement: b,
								title: m,
								img: d,
								ariaPressed: !1,
								style: o,
								className: r,
							});
				});
			(b.propTypes = f), (b.displayName = "CopyPasteCutButton");
			t.default = b;
		},
	},
]);
//# sourceMappingURL=chunk.94.js.map
