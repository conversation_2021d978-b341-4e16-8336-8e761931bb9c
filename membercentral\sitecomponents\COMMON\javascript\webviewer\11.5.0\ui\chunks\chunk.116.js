(window.webpackJsonp = window.webpackJsonp || []).push([
	[116],
	{
		1374: function (a, e, _) {
			a.exports = (function (a) {
				"use strict";
				var e = (function (a) {
						return a && "object" == typeof a && "default" in a
							? a
							: { default: a };
					})(a),
					_ = {
						name: "bi",
						weekdays: "Sand<PERSON>_Mande_Tusde_Wenesde_Tosde_Fraede_Sarade".split(
							"_",
						),
						months:
							"<PERSON><PERSON><PERSON>_Februari_Maj_Eprel_Mei_Jun_Julae_Okis_Septemba_Oktoba_Novemba_Disemba".split(
								"_",
							),
						weekStart: 1,
						weekdaysShort: "San_Man_Tus_Wen_Tos_Frae_Sar".split("_"),
						monthsShort:
							"Jan_Feb_Maj_Epr_Mai_Jun_Jul_Oki_Sep_Okt_Nov_Dis".split("_"),
						weekdaysMin: "San_Ma_Tu_We_To_Fr_Sar".split("_"),
						ordinal: function (a) {
							return a;
						},
						formats: {
							LT: "h:mm A",
							LTS: "h:mm:ss A",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY h:mm A",
							LLLL: "dddd, D MMMM YYYY h:mm A",
						},
						relativeTime: {
							future: "lo %s",
							past: "%s bifo",
							s: "sam seken",
							m: "wan minit",
							mm: "%d minit",
							h: "wan haoa",
							hh: "%d haoa",
							d: "wan dei",
							dd: "%d dei",
							M: "wan manis",
							MM: "%d manis",
							y: "wan yia",
							yy: "%d yia",
						},
					};
				return e.default.locale(_, null, !0), _;
			})(_(105));
		},
	},
]);
//# sourceMappingURL=chunk.116.js.map
