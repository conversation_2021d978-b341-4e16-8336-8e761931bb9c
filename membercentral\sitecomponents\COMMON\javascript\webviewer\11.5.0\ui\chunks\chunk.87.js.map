{"version": 3, "sources": ["webpack:///./src/ui/src/components/LeftPanelOverlay/ThumbnailMoreOptionsPopupSmall.js"], "names": ["ThumbnailMoreOptionsPopupSmall", "selectedPageIndexes", "useSelector", "state", "selectors", "getSelectedThumbnailPageIndexes", "menu", "DataElements", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL", "trigger", "THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL_TRIGGER", "pageNumbers", "map", "i", "warn"], "mappings": "+KA4BeA,UApBwB,WACrC,IAAMC,EAAsBC,aAAY,SAACC,GAAK,OAAKC,IAAUC,gCAAgCF,MAE7F,OACE,kBAAC,IAAU,CACTG,KAAMC,IAAaC,0CACnBC,QAASF,IAAaG,mDAEtB,kBAAC,IAAsB,CACrBC,YAAaV,EAAoBW,KAAI,SAACC,GAAC,OAAKA,EAAI,KAChDC,MAAI,IAEN,kBAAC,IAAwB,CACvBH,YAAaV,EAAoBW,KAAI,SAACC,GAAC,OAAKA,EAAI,KAChDC,MAAI", "file": "chunks/chunk.87.js", "sourcesContent": ["import React from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport FlyoutMenu from 'components/FlyoutMenu/FlyoutMenu';\nimport PageAdditionalControls from 'components/PageManipulationOverlay/PageAdditionalControls';\nimport PageManipulationControls from '../PageManipulationOverlay/PageManipulationControls';\nimport DataElements from 'constants/dataElement';\n\nconst ThumbnailMoreOptionsPopupSmall = () => {\n  const selectedPageIndexes = useSelector((state) => selectors.getSelectedThumbnailPageIndexes(state));\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL}\n      trigger={DataElements.THUMBNAILS_CONTROL_MANIPULATE_POPUP_SMALL_TRIGGER}\n    >\n      <PageAdditionalControls\n        pageNumbers={selectedPageIndexes.map((i) => i + 1)}\n        warn\n      />\n      <PageManipulationControls\n        pageNumbers={selectedPageIndexes.map((i) => i + 1)}\n        warn\n      />\n    </FlyoutMenu>\n  );\n};\n\nexport default ThumbnailMoreOptionsPopupSmall;\n"], "sourceRoot": ""}