<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getSubscriberChangeLogList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 3)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.qryChangeLogs = CreateObject("component","subscriptions").getSubscriptionsChangeLogFromFilters(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
			mode='grid', posStart=arguments.event.getValue('posStart'), count=arguments.event.getValue('count'), 
			direct=arguments.event.getValue('orderDir'), orderby=arguments.event.getValue('orderBy'))>

		<cfset local.arrData = []>
		<cfloop query="local.qryChangeLogs">
			<cfset local.arrData.append({
				"memberid": local.qryChangeLogs.memberID,
				"membername": local.qryChangeLogs.memberName,
				"membercompany": local.qryChangeLogs.memberCompany,
				"description": local.qryChangeLogs.description,
				"rootsubscriberid": local.qryChangeLogs.rootSubscriberID,
				"substartdate": DateFormat(local.qryChangeLogs.subStartDate, "m/d/yy"),
				"subenddate": DateFormat(local.qryChangeLogs.subEndDate, "m/d/yy"),
				"previousstatus": local.qryChangeLogs.previousStatus,
				"updatedate": DateTimeFormat(local.qryChangeLogs.updateDate, "m/d/yy h:nn tt"),
				"actormembername": local.qryChangeLogs.actorMemberName,
				"actormembernameForJS": EncodeForJavaScript(local.qryChangeLogs.actorMemberName),
				"actormemberid": local.qryChangeLogs.actorMemberID,
				"actormembernumber": local.qryChangeLogs.actorMemberNumber,
				"daterecorded": DateTimeFormat(local.qryChangeLogs.dateRecorded, "m/d/yy h:nn tt"),
				"DT_RowId": "changeLogRow_#local.qryChangeLogs.row#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryChangeLogs.totalCount),
			"recordsFiltered":  val(local.qryChangeLogs.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAllSubscribersScheduleList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.SubReportFilter = CreateObject("component","subscriptions").getSubReportFilter()>

		<cfset local.startFromDate = ''>
		<cfset local.startToDate = ''>
		<cfset local.endFromDate = ''>
		<cfset local.endToDate = ''>
		<cfif len(local.SubReportFilter.scheduleFilter.fTermStartFrom)>
			<cfset local.startFromDate = DateFormat(local.SubReportFilter.scheduleFilter.fTermStartFrom,'m/d/yyyy')>
		</cfif>
		<cfif len(local.SubReportFilter.scheduleFilter.fTermStartTo)>
			<cfset local.startToDate = DateFormat(local.SubReportFilter.scheduleFilter.fTermStartTo,'m/d/yyyy') & " 23:59:59.997">
		</cfif>
		<cfif len(local.SubReportFilter.scheduleFilter.fTermEndFrom)>
			<cfset local.endFromDate = DateFormat(local.SubReportFilter.scheduleFilter.fTermEndFrom,'m/d/yyyy')>
		</cfif>
		<cfif len(local.SubReportFilter.scheduleFilter.fTermEndTo)>
			<cfset local.endToDate = DateFormat(local.SubReportFilter.scheduleFilter.fTermEndTo,'m/d/yyyy') & " 23:59:59.997">
		</cfif>
		
		<cfset local.subStatus = local.SubReportFilter.scheduleFilter.fSubStatus>
		<cfset local.subType = local.SubReportFilter.scheduleFilter.fSubType>
		<cfset local.subID = local.SubReportFilter.scheduleFilter.fSubscription>
		<cfset local.subPaymentStatus = local.SubReportFilter.scheduleFilter.fSubPaymentStatus>
		<cfset local.rateID = local.SubReportFilter.scheduleFilter.fRate>
		<cfset local.freqID = local.SubReportFilter.scheduleFilter.fFreq>
		<cfset local.hasCard = local.SubReportFilter.scheduleFilter.fHasCardOnFile>
		<cfset local.revenueGLAccountID = local.SubReportFilter.scheduleFilter.fRevGL>
		<cfset local.associatedMemberID = local.SubReportFilter.scheduleFilter.associatedMemberID>
		<cfset local.associatedGroupID = local.SubReportFilter.scheduleFilter.associatedGroupID>
		
		<cfset local.rfidList = ''>
		<cfif (val(local.rateID) gt 0) OR (val(local.freqID) gt 0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
				select rf.rfid
				from dbo.sub_rateFrequencies rf
				inner join dbo.sub_rates r 
					on r.rateID = rf.rateID 
				<cfif val(local.rateID) gt 0>
					and r.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.rateID#">)
				</cfif>
				<cfif val(local.freqID) gt 0>
					where rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.freqID#">
				</cfif>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
			<cfif len(local.rfidList) eq 0>
				<cfset local.rfidList = 0>
			</cfif>
		</cfif>
	
		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')'")>
		<cfset arrayAppend(local.arrCols,"sub.subscriptionName")>
		<cfset arrayAppend(local.arrCols,"s.subStartDate")>
		<cfset arrayAppend(local.arrCols,"s.subEndDate")>
		<cfset arrayAppend(local.arrCols,"s.recogStartDate")>
		<cfset arrayAppend(local.arrCols,"s.recogEndDate")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<!--- get subscribers --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalCount int;

			IF OBJECT_ID('tempdb..##allSubscribers') IS NOT NULL
				DROP TABLE ##allSubscribers;
			IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
				DROP TABLE ##tmpSubscribers;

			select s.subscriberID
			INTO ##allSubscribers
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on sub.typeiD = t.typeID and t.siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
				<cfif (len(local.subType) gt 0) AND (local.subType neq "0")>
					AND t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subType#">
				</cfif>
			inner join dbo.sub_statuses st on st.statusID = s.statusID 
				<cfif local.subStatus neq 0>and st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatus#"></cfif>
			inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				<cfif local.subPaymentStatus neq 0>
					and pst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subPaymentStatus#">
				</cfif>
			inner join dbo.tr_GLAccounts gl on gl.GLAccountID = s.GLAccountID and gl.deferredGLAccountID is not null
			WHERE 1 = 1
			<cfif len(local.startFromDate) gt 0>
				AND s.subStartDate >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startFromDate#">
			</cfif>
			<cfif len(local.startToDate) gt 0>
				AND s.subStartDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startToDate#">
			</cfif>
			<cfif len(local.endFromDate) gt 0>
				AND s.subEndDate >= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endFromDate#">
			</cfif>
			<cfif len(local.endToDate) gt 0>
				AND s.subEndDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endToDate#">
			</cfif>
			<cfif local.hasCard eq 'Y'>
				AND s.payProfileID is not null
			<cfelseif local.hasCard eq 'N'>
				AND s.payProfileID is null
			</cfif>
			<cfif local.subID neq "0">
				AND s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			</cfif>
			<cfif len(local.rfidList) gt 0>
				AND s.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
			</cfif>
			<cfif local.revenueGLAccountID gt 0>
				AND s.GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.revenueGLAccountID#">
			</cfif>;
			
			select s.subscriberID, s.memberID, m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' AS memberName, m2.company, 
				s.subscriptionID, sub.subscriptionName, st.statusCode, st.statusName, pst.statusCode as paymentStatusCode, s.subStartDate, s.subEndDate, 
				s.recogStartDate, s.recogEndDate, r.rateName, 
				ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#) as row
			into ##tmpSubscribers
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID
			inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID 
			inner join ##allSubscribers as allSubscribers on allSubscribers.subscriberID = s.subscriberID
			inner join dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
			inner join dbo.sub_rates as r on r.rateID = rf.rateID
			inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on sub.typeiD = t.typeID 
			inner join dbo.ams_members as m on s.memberID = m.memberID
			inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID and m2.status <> 'D'
				<cfif local.associatedMemberID gt 0>
					AND m2.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedMemberID#">
				</cfif>
			<cfif local.associatedGroupID gt 0>
				inner join dbo.cache_members_groups mg ON mg.memberID = m2.memberID AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedGroupID#">
			</cfif>;

			set @totalCount = @@ROWCOUNT;

			SELECT tmp.subscriberID, tmp.memberID, tmp.memberName, tmp.company, tmp.subscriptionID, tmp.subscriptionName, tmp.statusCode, tmp.statusName,
				tmp.paymentStatusCode, tmp.subStartDate, tmp.subEndDate, tmp.recogStartDate, tmp.recogEndDate, tmp.rateName, tmp.row, @totalCount as totalCount
			FROM ##tmpSubscribers AS tmp
			WHERE tmp.row > #arguments.event.getValue('posStart')# AND tmp.row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			ORDER by tmp.row;

			IF OBJECT_ID('tempdb..##allSubscribers') IS NOT NULL
				DROP TABLE ##allSubscribers;
			IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
				DROP TABLE ##tmpSubscribers;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qrySubs">
			<cfset local.arrData.append({
				"subscriberid": local.qrySubs.subscriberID,
				"statuscode": local.qrySubs.statusCode,
				"memberid": local.qrySubs.memberid,
				"membername": local.qrySubs.membername,
				"company": local.qrySubs.company,
				"subscriptionname": local.qrySubs.subscriptionName,
				"statusname": local.qrySubs.statusName,
				"ratename": local.qrySubs.rateName,
				"company": local.qrySubs.company,
				"substartdate": DateFormat(local.qrySubs.subStartDate,'m/d/yy'),
				"subenddate": DateFormat(local.qrySubs.subEndDate,'m/d/yy'),
				"recogstartdate": DateFormat(local.qrySubs.recogStartDate,'m/d/yy'),
				"recogenddate": DateFormat(local.qrySubs.recogEndDate,'m/d/yy'),
				"DT_RowId": "scheduleRow_#local.qrySubs.row#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qrySubs.totalCount),
			"recordsFiltered":  val(local.qrySubs.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionTypes" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.types = arguments.event.getValue('t','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			declare @siteID int, @selectedTypes varchar(max);
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
			set @selectedTypes = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#local.types#">;
			
			select t.typeid, t.typeName, t.status, dg.listitem as selected
			from dbo.sub_types as t
			left outer join dbo.fn_intListToTable(@selectedTypes,',') dg on dg.listitem = t.typeID
			where siteID = @siteID
			order by t.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"typeid": local.qryData.typeid,
				"typename": local.qryData.typename,
				"status": local.qryData.status,
				"isselected": local.qryData.selected,
				"DT_RowId": "row_#local.qryData.typeid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionsOfSubType" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.types = arguments.event.getValue('t','');
			local.subs = arguments.event.getValue('s','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select s.subscriptionID, s.subscriptionName, s.status, dg.listitem as selected
			from dbo.sub_subscriptions as s
			inner join dbo.sub_Types t on t.typeID = s.typeID 
				and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
				and t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.types#">
			left outer join dbo.fn_intListToTable('#local.subs#',',') dg on dg.listitem = s.subscriptionID
			ORDER BY s.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"subscriptionid": local.qryData.subscriptionid,
				"subscriptionname": local.qryData.subscriptionname,
				"status": local.qryData.status,
				"isselected": local.qryData.selected,
				"DT_RowId": "row_#local.qryData.subscriptionid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionRates" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.subs = arguments.event.getValue('s','');
			local.rates = arguments.event.getValue('r','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			with currRates as (
				select r.rateID, rs.scheduleID, rs.scheduleName, r.rateName, 
					case when rs.status = 'A' and r.status = 'A' then 'A' else 'I' end as status
				from dbo.sub_rates as r
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 	
				inner join dbo.sub_subscriptions as sub on sub.scheduleID = r.scheduleID
				inner join dbo.fn_intListToTable('#local.subs#',',') as dg on dg.listitem = sub.subscriptionID
				where rs.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
			)
			SELECT rateID, dgr.listitem as selected, status, scheduleName, rateName
			FROM (
				select *
				from (
					select * from currRates
						union all
					select distinct r.rateID, rs.scheduleID, rs.scheduleName, r.rateName, 'I' as status
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
					inner join dbo.fn_intListToTable('#local.subs#',',') as dg on dg.listitem = sub.subscriptionID
					inner join dbo.sub_types as subtype on subtype.typeID = sub.typeID
					inner join dbo.sub_rateFrequencies as rf on rf.RFID = s.RFID
					inner join dbo.sub_rates as r on r.rateID = rf.rateID
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 	
					where subtype.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
					and NOT EXISTS (select rateID from currRates where rateID = r.rateID)
				) as tmp
			) as tmp2
			left outer join dbo.fn_intListToTable('#local.rates#',',') dgr on dgr.listitem = tmp2.rateID
			ORDER BY scheduleName, rateName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"rateid": local.qryData.rateid,
				"ratename": htmleditformat(local.qryData.scheduleName) & "/" & htmleditformat(local.qryData.rateName),
				"status": local.qryData.status,
				"isselected": local.qryData.selected,
				"DT_RowId": "row_#local.qryData.rateid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionStatuses" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.statuses = arguments.event.getValue('ss','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select s.statusID, s.statusName, dg.listitem as selected
			from dbo.sub_statuses as s
			left outer join dbo.fn_intListToTable('#local.statuses#',',') dg on dg.listitem = s.statusID
			ORDER BY s.statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"statusid": local.qryData.statusid,
				"statusname": htmleditformat(local.qryData.statusName),
				"isselected": local.qryData.selected,
				"DT_RowId": "row_#local.qryData.statusid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionPayStatuses" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.paystatuses = arguments.event.getValue('ps','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select s.statusID, s.statusName, dg.listitem as selected
			from dbo.sub_paymentStatuses as s
			left outer join dbo.fn_intListToTable('#local.paystatuses#',',') dg on dg.listitem = s.statusID
			ORDER BY s.statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"statusid": local.qryData.statusid,
				"statusname": htmleditformat(local.qryData.statusName),
				"isselected": local.qryData.selected,
				"DT_RowId": "row_#local.qryData.statusid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAllSchedules" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		</cfscript>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"rs.scheduleName")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;

			select rs.scheduleID, rs.scheduleName, rs.siteID, rs.status, count(sr.rateID) as numRates, rs.UID
			from dbo.sub_rateSchedules rs
			left outer join dbo.sub_rates sr on sr.scheduleID = rs.scheduleID and sr.status = 'A'
			where rs.siteID = @siteID
			and rs.status = 'A'
			group by rs.scheduleID, rs.scheduleName, rs.siteID, rs.status, rs.UID
			ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('orderDir')#;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubCount">
				select count(s.subscriptionID) as subCount
				from dbo.sub_subscriptions s WITH(NOLOCK)
				inner join dbo.sub_rateSchedules rs WITH(NOLOCK) on rs.scheduleID = s.scheduleID and rs.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				where s.scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryData.scheduleID#">
			</cfquery>
			<cfset arrayAppend(local.data, {
				"scheduleid": local.qryData.scheduleID,
				"uid": local.qryData.UID,
				"schedulename": local.qryData.scheduleName,
				"numrates": local.qryData.numRates,
				"status": local.qryData.status,
				"subcount" : val(local.qrySubCount.subCount),
				"DT_RowId": "row_#local.qryData.scheduleID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>
	
	<cffunction name="memberDatesGridJSON" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfquery name="local.arrMemberDates" datasource="#application.dsn.customApps.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT udid, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, renewalDateFieldName,
				CASE WHEN isActive = 1 THEN 'Yes' ELSE 'No' END AS isActive
			FROM dbo.schedTask_memberJoinDates
			WHERE siteCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.siteCode')#">
			ORDER BY udid;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrMemberDates),
			"recordsFiltered": arrayLen(local.arrMemberDates),
			"data": local.arrMemberDates
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionSets" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"sets.setName")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.arrSubSets" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpSubSets') IS NOT NULL
				DROP TABLE ##tmpSubSets;
			CREATE TABLE ##tmpSubSets (setID int, setName varchar(100), [status] char(1), numSubscriptions int, row int);

			DECLARE @siteID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			INSERT INTO ##tmpSubSets (setID, setName, [status], numSubscriptions, row)
			SELECT sets.setID, sets.setName, sets.[status], COUNT(subscriptionSetID) as numSubscriptions,
				ROW_NUMBER() OVER (ORDER BY #local.orderby#)
			FROM dbo.sub_sets AS sets 
			LEFT OUTER JOIN dbo.sub_subscriptionSets AS ss ON ss.setID = sets.setID
			WHERE sets.siteID  = @siteID
			<cfif len(local.searchValue)>
				and (sets.setName LIKE @searchValue)
			</cfif>
			GROUP BY sets.setID, sets.setName, sets.[status];

			SET @totalCount = @@ROWCOUNT;

			SELECT setID, setName, [status], numSubscriptions, @totalCount AS totalCount
			FROM ##tmpSubSets
			WHERE row > @posStart 
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpSubSets') IS NOT NULL
				DROP TABLE ##tmpSubSets;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrSubSets) GT 0 ? val(local.arrSubSets[1].totalCount) : 0,
			"recordsFiltered": arrayLen(local.arrSubSets) GT 0 ? val(local.arrSubSets[1].totalCount) : 0,
			"data": local.arrSubSets
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRateFrequencies" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select frequencyID, frequencyName, [uid], ISNULL(isSystemRate,0) as isSystemRate
			from dbo.sub_frequencies
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
			and [status] = 'A'
			order by frequencyName
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"frequencyID": local.qryData.frequencyID,
				"frequencyName": local.qryData.frequencyName,
				"uid": local.qryData.uid,
				"isSystemRate": local.qryData.isSystemRate,
				"DT_RowId": "subRateFreq_#local.qryData.frequencyID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionsForSet" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateData">
					SET NOCOUNT ON;
					
					DECLARE @siteID int, @orgID int, @recordedByMemberID int, @msg varchar(100), @currentOrderNum int, 
						@newOrderNum int, @subscriptionID int, @setID int, @setName varchar(100), 
						@subscriptionName varchar(200);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">;
					SET @setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('setID',0)#">;
					SET @newOrderNum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder+1#">;

					SELECT @setName = s.setName, @subscriptionName = subs.subscriptionName, @currentOrderNum = ss.orderNum
					FROM dbo.sub_subscriptionSets AS ss
					INNER JOIN dbo.sub_sets AS s on s.siteID = @siteID
						AND s.setID = ss.setID
					INNER JOIN dbo.sub_subscriptions AS subs on subs.subscriptionID = ss.subscriptionID
					WHERE ss.subscriptionID = @subscriptionID
					AND ss.setID = @setID;

					IF @newOrderNum <> @currentOrderNum BEGIN
						UPDATE dbo.sub_subscriptionSets 
						SET orderNum = @newOrderNum
						where subscriptionID = @subscriptionID
						AND setID = @setID;
					
						DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "SETID":'+CAST(@setID AS VARCHAR(10))+', "SITEID":'+CAST(@siteID AS VARCHAR(10))+'}';

						SET @msg = STRING_ESCAPE('Subscription ' + QUOTENAME(@subscriptionName) + ' in Set ' + QUOTENAME(@setName) + ' moved to position ' + CAST(@newOrderNum AS varchar(10)),'json');
						
						EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
							@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
					END
				</cfquery>
			</cfloop>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			select ss.subscriptionID, subs.subscriptionName, subs.status, ss.orderNum, st.typeName
			from dbo.sub_subscriptionSets ss 
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
			inner join dbo.sub_Types st on st.typeID = subs.typeID 
				and st.siteID = @siteID
			inner join dbo.sub_sets sets on sets.setID = ss.setID 
				and sets.siteID = @siteID
			where ss.setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('setID',0)#">
			order by ss.orderNum, subs.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"subscriptionID": local.qryData.subscriptionID,
				"subscriptionName": local.qryData.subscriptionName,
				"typeName": local.qryData.typeName,
				"orderNum": local.qryData.orderNum,
				"status": local.qryData.status,
				"canmoveup":local.qryData.currentRow NEQ 1,
				"canmovedown": local.qryData.currentRow NEQ local.qryData.recordCount,
				"DT_RowId": "subForSet_#local.qryData.subscriptionID#"
			})>
		</cfloop>
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>
		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscriptionsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>
		<cfset local.subscriptionObj = CreateObject('component', 'subscriptions')>

		<cfset local.memberID = arguments.event.getValue('mid',0)>
		<cfset local.checkActivations = arguments.event.getValue('checkActivations',0)>
		<cfset local.subTypeID = int(val(arguments.event.getTrimValue('fsubtype',0)))>
		<cfset local.subscriptionID = int(val(arguments.event.getTrimValue('fsub',0)))>
		<cfset local.rateIDList = arguments.event.getTrimValue('frate','')>
		<cfset local.frequencyID = int(val(arguments.event.getTrimValue('ffreq',0)))>
		<cfset local.paymentMethod = arguments.event.getTrimValue('fcof','')>
		<cfset local.statusCodeList = arguments.event.getTrimValue('fstatus','')>
		<cfset local.paymentStatusCode = arguments.event.getTrimValue('fpaystatus','')>
		<cfset local.startFromDate = arguments.event.getTrimValue('ftermstartfrom','')>
		<cfset local.startToDate = arguments.event.getTrimValue('ftermstartto','')>
		<cfset local.endFromDate = arguments.event.getTrimValue('ftermendfrom','')>
		<cfset local.endToDate = arguments.event.getTrimValue('ftermendto','')>
		<cfset local.loggedInMemberID = val(session.cfcuser.memberdata.memberid)>

		<cfset local.isHistory = int(val(arguments.event.getTrimValue('hist',0)))>
		<cfset local.isOnlyRenewal = int(val(arguments.event.getTrimValue('isRenew',0)))>
		
		<cfset local.defaultStatusCodeList = 'P,A,O,R,I'>
		<cfset local.historyStatusCodeList = 'D,E,X'>
		<cfset local.renewStatusCodeList = 'A,E,O,R'>
		<cfif NOT len(local.statusCodeList)>
			<cfif local.isHistory eq 1>
				<cfset local.statusCodeList = local.historyStatusCodeList>
			<cfelseif local.isOnlyRenewal>
				<cfset local.statusCodeList = local.renewStatusCodeList>	
			<cfelse>
				<cfset local.statusCodeList = local.defaultStatusCodeList>
			</cfif>
		<cfelse>
			<cfif local.isHistory eq 1>
				<cfset local.defaultArray = listToArray(local.historyStatusCodeList)>
			<cfelseif local.isOnlyRenewal>
				<cfset local.defaultArray = listToArray(local.renewStatusCodeList)>
			<cfelse>
				<cfset local.defaultArray = listToArray(local.defaultStatusCodeList)>
			</cfif>
			<cfset local.statusArray= listToArray(local.statusCodeList)>
			<cfset local.defaultArray.retainAll(local.statusArray) />
			<cfset local.statusCodeList = arrayToList(local.defaultArray)>
		</cfif>
		
		<cfif len(local.startFromDate)>
			<cfset local.startFromDate = DateFormat(local.startFromDate, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.startToDate)>
			<cfset local.startToDate = DateFormat(local.startToDate, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.endFromDate)>
			<cfset local.endFromDate = DateFormat(local.endFromDate, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.endToDate)>
			<cfset local.endToDate = DateFormat(local.endToDate, "mm/dd/yyyy")>
		</cfif>
		
		<cfif local.checkActivations eq 1 and local.isHistory eq 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_checkActivationsByMember">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
		</cfif>

		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="Qualify")>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_getMemberSubscriptionsForXML">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.subTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.subscriptionID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.rateIDList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.frequencyID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.paymentMethod#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.statusCodeList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.paymentStatusCode#">
			<cfif len(local.startFromDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.startFromDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
			</cfif>
			<cfif len(local.startToDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.startToDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
			</cfif>
			<cfif len(local.endFromDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.endFromDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
			</cfif>
			<cfif len(local.endToDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.endToDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.loggedInMemberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
			<cfprocresult name="local.qrySubs">
		</cfstoredproc>

		<cfif local.isOnlyRenewal>
			<cfset local.qrySubs = local.qrySubs.filter(
					function(thisRow) {
						return arguments.thisRow.anycanRenew eq 1 AND arguments.thisRow.canRenew eq true AND XMLSearch(arguments.thisRow.subTypePerms,"string(/rights/right[@functionName='GenerateSingleRenewals']/@allowed)") EQ 1;
					}
				)>
		</cfif>	

		<cfset local.hasSubFilters = val(local.qrySubs.parentSubscriberID) EQ 0 ? true : false>
		<cfset local.subscriptionStatusList = "">
		
		<cfset local.arrSubs = []>
		
		<cfloop query="local.qrySubs">
			<cfset local.allowModifySubscription = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='ModifySubscription']/@allowed)")>
			<cfset local.allowAcceptSubscription = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='AcceptSubscription']/@allowed)")>
			<cfset local.allowMarkBilled = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='MarkBilled']/@allowed)")>
			<cfset local.allowEmailOffer = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='EmailOffer']/@allowed)")>
			<cfset local.allowGenerateSingleRenewals = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='GenerateSingleRenewals']/@allowed)")>
			<cfset local.allowExpireSubscription = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='ExpireSubscription']/@allowed)")>
			<cfset local.allowDeleteSubscription = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='DeleteSubscription']/@allowed)")>
			<cfset local.allowManageActivation = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='ManageActivation']/@allowed)")>
			<cfset local.allowAssocPayMethod = XMLSearch(local.qrySubs.subTypePerms,"string(/rights/right[@functionName='AssocPayMethod']/@allowed)")>
			
			<cfset local.payMethod = 0>
			<cfif local.qrySubs.anyshowCCIcon and local.allowAssocPayMethod and len(local.qrySubs.parentSubscriberID) eq 0>
				<cfif local.qrySubs.showCCIcon and NOT len(local.qrySubs.payProfileID)>
					<cfset local.payMethod = 1>
				<cfelseif local.qrySubs.showCCIcon>
					<cfset local.payMethod = 2>
				</cfif>
			</cfif>

			<cfset local.addPayment = 0>
			<cfset local.addPaymentEncString = ''>
			<cfif local.allowAssocPayMethod AND local.qrySubs.canPay AND local.qrySubs.dueAmt gt 0 AND len(local.qrySubs.invoiceList) and len(local.qrySubs.parentSubscriberID) eq 0>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.memberID, t=local.qrySubs.subscriptionName, ta=local.qrySubs.dueAmt, tmid=local.memberID, ad="v|#local.qrySubs.invoiceList#")>
				<cfset local.addPayment = 1>
			</cfif>

			<cfset local.canEditSub = 0>
			<cfif local.allowModifySubscription AND local.qrySubs.anycanEdit AND local.qrySubs.canEdit and len(local.qrySubs.parentSubscriberID) eq 0>
				<cfset local.canEditSub = 1>
			</cfif>

			<cfset local.markActive = 0>
			<cfif local.allowManageActivation AND local.qrySubs.anyAI>
				<cfif local.qrySubs.status eq 'A'>
					<cfset local.markActive = 1>
				<cfelseif local.qrySubs.status eq 'I'>
					<cfset local.markActive = 2>
				</cfif>
			</cfif>

			<cfset local.subCanRenew = 0>
			<cfif local.allowGenerateSingleRenewals AND local.qrySubs.anycanRenew>
				<cfif (local.qrySubs.canRenew eq true)>
					<cfset local.subCanRenew = 1>
				</cfif>
			</cfif>

			<cfset local.acceptSub = 0>
			<cfif local.allowAcceptSubscription AND local.qrySubs.billedSubsCount gt 0>
				<cfif local.qrySubs.statusName eq 'Billed'>
					<cfset local.acceptSub = 1>
				</cfif>
			</cfif>

			<cfset local.emailOfferSub = 0>
			<cfif local.allowEmailOffer AND local.qrySubs.anycanEmail>
				<cfif local.qrySubs.canEmail>
					<cfset local.emailOfferSub = 1>
				</cfif>
			</cfif>

			<cfset local.cleanUpInvoice = 0>
			<cfif local.allowModifySubscription AND local.qrySubs.anycanCleanupInvoices>
				<cfif local.qrySubs.canCleanupInvoices>
					<cfset local.cleanUpInvoice = 1>
				</cfif>
			</cfif>

			<cfset local.markBilledExpired = 0>
			<cfif (local.allowMarkBilled OR local.allowExpireSubscription) AND local.qrySubs.anyCanBillExpire>
				<cfif local.allowMarkBilled AND local.qrySubs.canMarkAsBilled>
					<cfset local.markBilledExpired = 1>
				<cfelseif local.allowExpireSubscription AND local.qrySubs.canExpire>
					<cfset local.markBilledExpired = 2>
				</cfif>
			</cfif>

			<cfset local.markOverrideSub = 0>
			<cfif local.allowManageActivation AND local.qrySubs.anycanActivate>
				<cfif local.qrySubs.canActivate>
					<cfset local.markOverrideSub = 1>
				</cfif>
			</cfif>

			<cfset local.strRemoveSub = 0>
			<cfif local.allowDeleteSubscription AND local.qrySubs.anycanRemove>
				<cfif local.qrySubs.canRemove>
					<cfset local.strRemoveSub = 1>
				</cfif>
			</cfif>

			<cfset local.hasChildSubs = false>
			<cfset local.classlist = "childSubOf#val(local.qrySubs.parentSubscriberID)#">
			
			<!--- has child groups for a filtered parent --->
			<cfif not local.hasChildSubs>
				<cfquery name="local.qryChildSub" dbtype="query" maxrows="1">
					SELECT subscriberID
					FROM [local].qrySubs
					WHERE parentSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubs.subscriberID#">
				</cfquery>
				<cfset local.hasChildSubs = (local.qryChildSub.recordCount gt 0)? true : false>
			</cfif>

			<cfif local.isHistory eq 0 && (val(local.qrySubs.parentSubscriberID) neq local.qrySubs.rootSubscriberID) AND val(local.qrySubs.parentSubscriberID) gt 0>
				<cfset local.classlist = "#local.classlist# d-none">
			<cfelseif local.isHistory eq 1 && (val(local.qrySubs.parentSubscriberID) neq local.qrySubs.rootSubscriberID) AND len(local.qrySubs.parentSubscriberID) neq 0>
				<cfset local.classlist = "#local.classlist# d-none">
			</cfif>
			
			<cfif local.qrySubs.status == 'D'>
				<cfset local.subscriptionStatusList = ListAppend(local.subscriptionStatusList,"deleted")>
			<cfelseif local.qrySubs.paymentStatus == 'N' AND (local.qrySubs.status == 'A' OR local.qrySubs.status == 'P')>
				<cfset local.subscriptionStatusList = ListAppend(local.subscriptionStatusList,"nonactivated")>
			</cfif>
			
			
			<cfset local.arrSubs.append({
				"subscriberID": local.qrySubs.subscriberID,
				"subscriptionID": local.qrySubs.subscriptionID,
				"typeName": local.qrySubs.typeName,
				"statusName": local.qrySubs.statusName,
				"status": local.qrySubs.status,
				"paymentStatus": local.qrySubs.paymentStatus,
				"subscriptionName": local.qrySubs.subscriptionName,
				"rateName": local.qrySubs.ratename,
				"frequencyName": local.qrySubs.frequencyName,
				"parentSubscriberID": val(local.qrySubs.parentSubscriberID),
				"thePath": local.qrySubs.thePath,
				"hasChildSubs": local.hasChildSubs,
				"isHistory": local.isHistory,
				"expandSub": local.hasChildSubs AND (len(local.qrySubs.parentSubscriberID) eq 0),
				"subStartDate":dateformat(local.qrySubs.subStartDate,'m/d/yy'),
				"subEndDate":dateformat(local.qrySubs.subEndDate,'m/d/yy'),
				"billedAmt": (len(local.qrySubs.billedAmt) and len(local.qrySubs.parentSubscriberID) eq 0)?"$#numberformat(local.qrySubs.billedAmt,"9,999.99")#":"",
				"dueAmt": (len(local.qrySubs.dueAmt) and len(local.qrySubs.parentSubscriberID) eq 0)?"$#numberformat(local.qrySubs.dueAmt,"9,999.99")#":"",
				"anyshowCCIcon": local.qrySubs.anyshowCCIcon,
				"showCCIcon": local.qrySubs.showCCIcon,
				"allowAssocPayMethod": local.allowAssocPayMethod,
				"payMethod": local.payMethod,
				"payProfileID": local.qrySubs.payProfileID,
				"addPaymentEncString": local.addPaymentEncString,
				"addPayment": local.addPayment,
				"canEditSub": local.canEditSub,
				"markActive": local.markActive,
				"subCanRenew": local.subCanRenew,
				"acceptSub": local.acceptSub,
				"emailOfferSub": local.emailOfferSub,
				"cleanUpInvoice": local.cleanUpInvoice,
				"markBilledExpired": local.markBilledExpired,
				"markOverrideSub": local.markOverrideSub,
				"strRemoveSub": local.strRemoveSub,				
				"parentSubscriberIDLen": len(local.qrySubs.parentSubscriberID),				
				"DT_RowId": "grpRow_#local.qrySubs.subscriberID#",
				"DT_RowClass": local.classlist
			})>
		</cfloop>
		
		<cfset local.subscriptionStatusLegendHtml = local.subscriptionObj.showSubscriptionStatusLegend(listRemoveDuplicates(local.subscriptionStatusList))>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrSubs),
			"recordsFiltered": arrayLen(local.arrSubs),
			"data": local.arrSubs,
			"hasSubFilters": local.hasSubFilters,
			"subscriptionStatusLegendHtml":local.subscriptionStatusLegendHtml
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMemberSubscriptions" access="public" output="false" returntype="string" hint="mainly used for Member Main Tab Active/Accepted Subs Datatable">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSubs = CreateObject('component', 'subscriptions')>
		<cfset local.qrySubs = local.objSubs.getMemberSubscriptions(orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=arguments.event.getValue('mid',0), statusCodeList=arguments.event.getTrimValue('fstatus',''))>
		
		<cfset local.subscriptionStatusList = "">
		<cfset local.arrSubs = []>

		<cfloop query="local.qrySubs">
			<cfset local.hasChildSubs = false>
			<cfset local.classlist = "childSubOf#val(local.qrySubs.parentSubscriberID)#">
			
			<!--- has child groups for a filtered parent --->
			<cfif not local.hasChildSubs>
				<cfquery name="local.qryChildSub" dbtype="query" maxrows="1">
					SELECT subscriberID
					FROM [local].qrySubs
					WHERE parentSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubs.subscriberID#">
				</cfquery>
				<cfset local.hasChildSubs = (local.qryChildSub.recordCount gt 0)? true : false>
			</cfif>

			<cfif val(local.qrySubs.parentSubscriberID) neq local.qrySubs.rootSubscriberID AND val(local.qrySubs.parentSubscriberID) gt 0>
				<cfset local.classlist = "#local.classlist# d-none">
			</cfif>
			
			<cfif local.qrySubs.status EQ 'D'>
				<cfset local.subscriptionStatusList = ListAppend(local.subscriptionStatusList,"deleted")>
			<cfelseif local.qrySubs.paymentStatus EQ 'N' AND (local.qrySubs.status EQ 'A' OR local.qrySubs.status EQ 'P')>
				<cfset local.subscriptionStatusList = ListAppend(local.subscriptionStatusList,"nonactivated")>
			</cfif>
			
			<cfset local.arrSubs.append({
				"subscriberID": local.qrySubs.subscriberID,
				"subscriptionID": local.qrySubs.subscriptionID,
				"statusName": local.qrySubs.statusName,
				"status": local.qrySubs.status,
				"paymentStatus": local.qrySubs.paymentStatus,
				"subscriptionName": local.qrySubs.subscriptionName,
				"typeName": local.qrySubs.typeName,
				"rateName": local.qrySubs.ratename,
				"frequencyName": local.qrySubs.frequencyName,
				"parentSubscriberID": val(local.qrySubs.parentSubscriberID),
				"thePath": local.qrySubs.thePath,
				"hasChildSubs": local.hasChildSubs,
				"expandSub": local.hasChildSubs AND (len(local.qrySubs.parentSubscriberID) eq 0),
				"subStartDate":dateformat(local.qrySubs.subStartDate,'m/d/yy'),
				"subEndDate":dateformat(local.qrySubs.subEndDate,'m/d/yy'),
				"parentSubscriberIDLen": len(local.qrySubs.parentSubscriberID),				
				"DT_RowId": "grpRow_#local.qrySubs.subscriberID#",
				"DT_RowClass": local.classlist
			})>
		</cfloop>
		
		<cfset local.subscriptionStatusLegendHtml = local.objSubs.showSubscriptionStatusLegend(statusList=listRemoveDuplicates(local.subscriptionStatusList))>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrSubs),
			"recordsFiltered": arrayLen(local.arrSubs),
			"data": local.arrSubs,
			"subscriptionStatusLegendHtml": local.subscriptionStatusLegendHtml
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubscribersForGenerateRenewals" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.subStatus = arguments.event.getValue('fSubStatus','0')>
		<cfset local.subPaymentStatus = arguments.event.getValue('fSubPaymentStatus','0')>
		<cfset local.subStartFromDate = arguments.event.getValue('fTermStartFrom','')>
		<cfset local.subStartToDate = arguments.event.getValue('fTermStartTo','')>
		<cfset local.subEndFromDate = arguments.event.getValue('fTermEndFrom','')>
		<cfset local.subEndToDate = arguments.event.getValue('fTermEndTo','')>
		<cfset local.subType = arguments.event.getValue('fSubType','0')>
		<cfset local.subID = arguments.event.getValue('fSubscription','0')>
		<cfset local.freqID = arguments.event.getValue('fFreq','0')>
		<cfset local.rateID = arguments.event.getValue('fRate','0')>
		<cfset local.hasCard = arguments.event.getValue('fCard','0')>
		<cfset local.chkAllState = arguments.event.getValue('chkAll','0')>
		<cfset local.associatedMemberID = arguments.event.getValue('associatedMemberID',0)>
		<cfset local.associatedGroupID = arguments.event.getValue('associatedGroupID',0)>
		<cfset local.linkedRecords = arguments.event.getValue('linkedRecords','all')>

		<cfset arguments.event.setValue('direct',form['order[0][dir]'] ?: 'asc')>
		<cfset arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 1)))>
		<cfset arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))))>
		<cfset arguments.event.setValue('count',int(val(arguments.event.getValue('length',100))))>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfif len(local.subStartFromDate) eq 0 AND len(local.subStartToDate) gt 0>
			<cfset local.subStartFromDate = local.subStartToDate>
			<cfset local.subStartToDate = ''>
		</cfif>
		<cfif len(local.subStartToDate)>
			<cfset local.subStartToDate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subStartToDate), 0)))>
		</cfif>
		<cfif len(local.subEndFromDate) gt 0 AND len(local.subEndToDate) eq 0>
			<cfset local.subEndToDate = local.subEndFromDate>
			<cfset local.subEndFromDate = ''>
		</cfif>
		<cfif len(local.subEndToDate)>
			<cfset local.subEndToDateate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subEndToDate), 0)))>
		</cfif>
			
		<cfset local.subList = CreateObject("component","subscriptions").getSubRenewalsQuery(subStatus=local.subStatus, 
			subPaymentStatus=local.subPaymentStatus, subStartFromDate=local.subStartFromDate, subStartToDate=local.subStartToDate,
			subEndFromDate=local.subEndFromDate, subEndToDate=local.subEndToDate, subType=local.subType, subID=local.subID, 
			freqID=local.freqID, rateID=local.rateID, hasCard=local.hasCard, siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			posStart=arguments.event.getValue('posStart'), rowLimit=arguments.event.getValue('count'), 
			orderBy=arguments.event.getValue('orderBy'), direct=arguments.event.getValue('direct'), associatedMemberID=local.associatedMemberID, 
			associatedGroupID=local.associatedGroupID, linkedRecords=local.linkedRecords)>
		
		<cfset local.qrySubs = local.subList.qry>
		
		<cfset local.data = []>
		<cfif local.qrySubs.recordCount>
			<cfloop query="local.qrySubs">
				<cfset local.tmpStr = {
					"subscriberID": local.qrySubs.subscriberID,
					"memberID": local.qrySubs.memberID,
					"memberName": local.qrySubs.memberName,
					"company": local.qrySubs.company,
					"subscriptionName": local.qrySubs.subscriptionName,
					"typeName": local.qrySubs.typeName,
					"statusName": local.qrySubs.statusName,
					"subStartDate": DateFormat(local.qrySubs.subStartDate, "m/d/yyyy"),
					"subEndDate": DateFormat(local.qrySubs.subEndDate, "m/d/yyyy"),
					"graceEndDate": DateFormat(local.qrySubs.graceEndDate, "m/d/yyyy"),
					"DT_RowId": "subscriber_#local.qrySubs.subscriberID#"
				}>

				<cfset arrayAppend(local.data, local.tmpStr)>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": val(local.qrySubs.totalCount),
			"recordsFiltered": val(local.qrySubs.totalCount),
			"data": local.data
		}>
		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRatesForSchedule" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGroups">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT groupID, groupPathExpanded AS thePathexpanded
			FROM dbo.ams_groups 
			WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
			AND status <> 'D'
			AND hideOnGroupLists = 0
			ORDER BY groupPathSortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.qryRates = createObject("component","subscriptions").getRatesByScheduleIDFromFilters(event=arguments.event)>
		
		<cfquery name="local.thisActiveScheduleCount" dbtype="query">
			SELECT DISTINCT rateID
			FROM [local].qryRates
			WHERE status = 'A'
		</cfquery>
		<cfquery name="local.thisRateCount" dbtype="query">
			SELECT DISTINCT rateID
			FROM [local].qryRates
		</cfquery>
		<cfset local.arrRates = []>
		<cfset local.rateCount = 0>
		
		<cfoutput query="local.qryRates" group="rateID">
			<cfif val(local.qryRates.rateID) gt 0>
				<cfset local.rateCount++>										
				<cfset local.rateRowID = "rg#local.qryRates.rateID#">

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateFreq">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @rateFreqs varchar(100);
					
					select @rateFreqs = COALESCE(@rateFreqs,'') + f.frequencyShortName + ':' + convert(varchar, rf.rateAmt) + ' '
					from dbo.sub_rateFrequencies rf
					inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
						and f.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
						and f.status <> 'D'
					where rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRates.rateID#">
					and rf.status <> 'D';
					
					select @rateFreqs as rateFreqs;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfscript>
					local.rateStartDate = "";
					local.rateEndDate = "";
					local.termStartDate = "";
					local.termEndDate = "";
					local.graceEndDate = "";
					local.recogStartDate = "";
					local.recogEndDate = "";
					if (len(local.qryRates.rateAFStartDate)) local.rateStartDate = local.qryRates.rateAFStartDate;
					if (len(local.qryRates.rateAFEndDate)) local.rateEndDate = local.qryRates.rateAFEndDate;
					if (len(local.qryRates.termAFStartDate)) local.termStartDate = local.qryRates.termAFStartDate;
					if (len(local.qryRates.termAFEndDate)) local.termEndDate = local.qryRates.termAFEndDate;
					if (len(local.qryRates.graceEndDate)) local.graceEndDate = local.qryRates.graceEndDate;
					if (len(local.qryRates.recogAFStartDate)) local.recogStartDate = local.qryRates.recogAFStartDate;
					if (len(local.qryRates.recogAFEndDate)) local.recogEndDate = local.qryRates.recogAFEndDate;
				</cfscript>
				
				<cfset local.objRate = {
					"level":  1,
					"rowType": "rate",
					"displayName": local.qryRates.rateName,
					"displayNameEncoded": encodeForJavascript(local.qryRates.rateName),
					"rateid": val(local.qryRates.rateID),
					"rateOrder": local.qryRates.rateOrder,
					"forceUpfront": local.qryRates.forceUpfront,
					"rateFreqs": replace(local.qryRateFreq.rateFreqs,' ','&nbsp;&nbsp;','ALL'),
					"status": local.qryRates.status,
					"rateStartDate": DateFormat(local.rateStartDate, "m/d/yy"),
					"rateEndDate": DateFormat(local.rateEndDate, "m/d/yy"),
					"termStartDate": DateFormat(local.termStartDate, "m/d/yy"),
					"termEndDate": DateFormat(local.termEndDate, "m/d/yy"),
					"graceEndDate": DateFormat(local.graceEndDate, "m/d/yy"),
					"recogStartDate": DateFormat(local.recogStartDate, "m/d/yy"),
					"recogEndDate": DateFormat(local.recogEndDate, "m/d/yy"),
					"isRenewalRate": local.qryRates.isRenewalRate,
					"siteResourceID": local.qryRates.siteResourceID,
					"isRenewalRate": local.qryRates.isRenewalRate,
					"groupID": 0,
					"hasChildren": 0,
					"canmoveup": local.rateCount gt 1 and local.qryRates.status is "A",
					"canmovedown": local.rateCount NEQ local.thisActiveScheduleCount.recordCount and local.qryRates.status is "A",
					"canmoveratetoposition": local.thisActiveScheduleCount.recordCount gt 2 and local.qryRates.status is "A",
					"canrestorerate": local.qryRates.status is not 'A',
					"parentRowID": "gridRoot",
					"DT_RowId": local.rateRowID,
					"DT_RowClass": "child-of-gridRoot"
				}>
				<cfset local.arrRates.append(local.objRate)>

				<cfset local.groupIndex = 0>
				<cfoutput group="groupid">
					<cfif local.qryRates.groupID gt 0>
						<cfset local.groupIndex++>

						<cfquery dbtype="query" name="local.qryGroupFullPath">
							select thePathExpanded
							from [local].qryGroups
							where groupID = #local.qryRates.groupID#
						</cfquery>
						<cfif len(local.qryGroupFullPath.thePathExpanded)>
							<cfset local.displayGroupAs = local.qryGroupFullPath.thePathExpanded>
						<cfelse>
							<cfset local.displayGroupAs = local.qryRates.groupName>
						</cfif>

						<cfset local.arrRates.append({
							"level": 2,
							"rowType": "group",
							"displayName": (local.qryRates.include?'':'Denied: ') & local.displayGroupAs,
							"displayNameEncoded": encodeForJavascript(local.displayGroupAs),
							"rateid": val(local.qryRates.rateID),
							"status": local.qryRates.status,
							"groupID": local.qryRates.groupID,
							"include": local.qryRates.include,
							"hasChildren": 0,
							"canMoveUp": 0,
							"canMoveDown": 0,
							"parentRowID": "#local.rateRowID#",
							"DT_RowId": "rg#local.qryRates.rateID#-#local.qryRates.groupid#",
							"DT_RowClass": "child-of-#local.rateRowID#",
							"DT_btnID": "rg#local.qryRates.rateID##local.qryRates.groupid#"
						})>
					</cfif>
				</cfoutput>

				<cfset local.objRate.hasChildren = local.groupIndex gt 0>
			</cfif>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.rateCount,
			"recordsFiltered": local.rateCount,
			"data": local.arrRates
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
	
	<cffunction name="getManageSetSubscriptions" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.setID = arguments.event.getValue('setID', 0);
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select s.subscriptionID, s.subscriptionName, s.status, s.soldSeparately, st.typeID, st.typeName, case when IsNull(ss.subscriptionID,0) = 0 then 0 else 1 end as isAdded
			from dbo.sub_subscriptions s 
			inner join dbo.sub_Types st on st.typeID = s.typeID 
				and st.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			left outer join dbo.sub_subscriptionSets ss ON ss.subscriptionID = s.subscriptionID 
				and ss.setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('setID', 0)#">
			order by st.typeName, s.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"subscriptionID": local.qryData.subscriptionID,
				"subscriptionName": local.qryData.subscriptionName,
				"status": local.qryData.status,
				"typeID": local.qryData.typeID,
				"typeName": local.qryData.typeName,
				"soldSeparately": local.qryData.soldSeparately,
				"isAdded": local.qryData.isAdded,
				"DT_RowId": "row_#local.qryData.typeID#_#local.qryData.subscriptionID#"
			})>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getSubSetupAuditLogs" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getSubSetupAuditLogs(siteID=arguments.event.getValue('mc_siteinfo.siteID'),
									area=arguments.event.getValue('fArea',''), keywords=arguments.event.getValue('fDescription',''), 
									dateFrom=arguments.event.getValue('fDateFrom',''), dateTo=arguments.event.getValue('fDateTo',''), 
									limit=50).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
					AND m2.orgID IN (1,@orgID)
				WHERE m.memberID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberIDList#" list="true">)
				AND m.orgID IN (1,@orgID);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>

			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAvailableSubscriptionsList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strAvailableSubs = CreateObject("component","subscriptionReg").showSubscriptions(siteid=arguments.event.getValue('mc_siteinfo.siteid'), memberID=arguments.event.getValue('mid', '0'), filterTypeID=arguments.event.getValue('filterTypeID',0))>
		
		<cfset local.arrSubLists = []>
		<cfoutput query="local.strAvailableSubs.qrySubscriptions" group="typeID">
			<cfset local.subListTypeRowID = "clt#val(local.strAvailableSubs.qrySubscriptions.typeID)#">
			<cfset local.arrSubLists.append({
				"level": 1,
				"rowType": "subListType",
				"subscriptionID": val(local.strAvailableSubs.qrySubscriptions.subscriptionID),
				"typeID": local.strAvailableSubs.qrySubscriptions.typeID,
				"displayName": local.strAvailableSubs.qrySubscriptions.typeName,
				"hasChildren": val(local.strAvailableSubs.qrySubscriptions.subscriptionID) gt 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.subListTypeRowID,
				"DT_RowClass": "child-of-gridRoot"
			})>
			<cfoutput>
				<cfif val(local.strAvailableSubs.qrySubscriptions.subscriptionID) gt 0>
					<cfset local.arrSubLists.append({
						"level": 2,
						"rowType": "subList",
						"subscriptionID": val(local.strAvailableSubs.qrySubscriptions.subscriptionID),
						"typeID": local.strAvailableSubs.qrySubscriptions.typeID,
						"displayName": local.strAvailableSubs.qrySubscriptions.subscriptionName,
						"parentRowID": local.subListTypeRowID,
						"DT_RowId": "clt#local.strAvailableSubs.qrySubscriptions.typeID#-#local.strAvailableSubs.qrySubscriptions.subscriptionID#",
						"DT_RowClass": "child-of-#local.subListTypeRowID# d-none"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"recordsTotal": arrayLen(local.arrSubLists),
			"recordsFiltered": arrayLen(local.arrSubLists),
			"data": local.arrSubLists
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAvailableSubscriptionsListForForceAdd" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			declare @allowedTypes TABLE (typeID int PRIMARY KEY);

			insert into @allowedTypes (typeID)
			select st.typeID
			from dbo.sub_Types as st 
			inner join dbo.sub_subscriptions as subs on subs.typeID = st.typeID 
				and subs.soldSeparately = 0
				and subs.status <> 'D'
			inner join dbo.sub_subscriptionSets as ss on ss.subscriptionID = subs.subscriptionID
			inner join dbo.sub_addons as sa on ss.setID = sa.childSetID
			inner join dbo.sub_subscriptions as subs2 on subs2.subscriptionID = sa.subscriptionID
			where st.siteID = @siteID
			and st.status = 'A'
			group by st.typeID, st.typeName
			order by st.typeName;

			select subs.subscriptionID, subs.subscriptionName,st.typeID, st.typeName
			from @allowedTypes at
			inner join dbo.sub_Types as st on st.typeID = at.typeID
			inner join dbo.sub_subscriptions as subs on st.typeID = subs.typeID and st.siteID = @siteID			
			inner join dbo.sub_subscriptionSets as ss on ss.subscriptionID = subs.subscriptionID
			inner join dbo.sub_addons as sa on ss.setID = sa.childSetID
			inner join dbo.sub_subscriptions as subs2 on subs2.subscriptionID = sa.subscriptionID
			where subs.soldSeparately = 0
			and subs.status = 'A'
			group by subs.subscriptionID,st.typeID, subs.subscriptionName, st.typeName
			order by st.typeName,subs.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrSubLists = []>
		<cfoutput query="local.qryData" group="typeID">
			<cfset local.subListTypeRowID = "clt#val(local.qryData.typeID)#">
			<cfset local.arrSubLists.append({
				"level": 1,
				"rowType": "subListType",
				"subscriptionID": val(local.qryData.subscriptionID),
				"typeID": local.qryData.typeID,
				"displayName": local.qryData.typeName,
				"hasChildren": val(local.qryData.subscriptionID) gt 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.subListTypeRowID,
				"DT_RowClass": "child-of-gridRoot"
			})>
			<cfoutput>
				<cfif val(local.qryData.subscriptionID) gt 0>
					<cfset local.arrSubLists.append({
						"level": 2,
						"rowType": "subList",
						"subscriptionID": val(local.qryData.subscriptionID),
						"typeID": local.qryData.typeID,
						"displayName": local.qryData.subscriptionName,
						"parentRowID": local.subListTypeRowID,
						"DT_RowId": "clt#local.qryData.typeID#-#local.qryData.subscriptionID#",
						"DT_RowClass": "child-of-#local.subListTypeRowID# d-none"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"recordsTotal": arrayLen(local.arrSubLists),
			"recordsFiltered": arrayLen(local.arrSubLists),
			"data": local.arrSubLists
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAvailableSubscriptionsListForGenerate" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			declare @allowedTypes TABLE (typeID int PRIMARY KEY);

			insert into @allowedTypes (typeID)
			select st.typeID
			from dbo.sub_Types as st 
			inner join dbo.sub_subscriptions as subs on subs.typeID = st.typeID 
				and subs.soldSeparately = 1
				and subs.status <> 'D'
			where st.siteID = @siteID
			and st.status = 'A'
			group by st.typeID, st.typeName
			order by st.typeName;

			select subs.subscriptionID, subs.subscriptionName,st.typeID, st.typeName
			from @allowedTypes at
			inner join dbo.sub_Types as st on st.typeID = at.typeID
			inner join dbo.sub_subscriptions as subs on st.typeID = subs.typeID and st.siteID = @siteID			
			where subs.soldSeparately = 1
			and subs.status <> 'D'
			group by subs.subscriptionID,st.typeID, subs.subscriptionName, st.typeName
			order by st.typeName,subs.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrSubLists = []>
		<cfoutput query="local.qryData" group="typeID">
			<cfset local.subListTypeRowID = "clt#val(local.qryData.typeID)#">
			<cfset local.arrSubLists.append({
				"level": 1,
				"rowType": "subListType",
				"subscriptionID": val(local.qryData.subscriptionID),
				"typeID": local.qryData.typeID,
				"displayName": local.qryData.typeName,
				"hasChildren": val(local.qryData.subscriptionID) gt 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.subListTypeRowID,
				"DT_RowClass": "child-of-gridRoot"
			})>
			<cfoutput>
				<cfif val(local.qryData.subscriptionID) gt 0>
					<cfset local.arrSubLists.append({
						"level": 2,
						"rowType": "subList",
						"subscriptionID": val(local.qryData.subscriptionID),
						"typeID": local.qryData.typeID,
						"displayName": local.qryData.subscriptionName,
						"parentRowID": local.subListTypeRowID,
						"DT_RowId": "clt#local.qryData.typeID#-#local.qryData.subscriptionID#",
						"DT_RowClass": "child-of-#local.subListTypeRowID# d-none"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"recordsTotal": arrayLen(local.arrSubLists),
			"recordsFiltered": arrayLen(local.arrSubLists),
			"data": local.arrSubLists
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAddOnSubsForMassAction" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		
		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;
			CREATE TABLE ##tmpRootSubscribers (rootsubscriberID int PRIMARY KEY);

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@rootsubscriberIDList varchar(max) = '#local.subscriberIDList#';

			INSERT INTO ##tmpRootSubscribers (rootsubscriberID)
			SELECT DISTINCT listitem
			FROM dbo.fn_intListToTable(@rootsubscriberIDList, ',');

			select st.typeID, st.typeName, subs.subscriptionID, subs.subscriptionName
			from ##tmpRootSubscribers as tmp
			inner join dbo.sub_subscribers as s on s.orgID = @orgID
				and s.rootSubscriberID = tmp.rootsubscriberID
				and s.subscriberID <> s.rootSubscriberID
			inner join dbo.sub_statuses as subst on subst.statusID = s.statusID
				and subst.statusCode <> 'D'
			inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID
				and subs.subscriptionID = s.subscriptionID
				and subs.status = 'A'
			inner join dbo.sub_types as st on st.siteID = @siteID
				and st.typeID = subs.typeID
			group by st.typeID, st.typeName, subs.subscriptionID, subs.subscriptionName
			order by st.typeName, subs.subscriptionName;

			IF OBJECT_ID('tempdb..##tmpRootSubscribers') IS NOT NULL
				DROP TABLE ##tmpRootSubscribers;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrSubLists = []>
		<cfoutput query="local.qryData" group="typeID">
			<cfset local.subTypeRowID = "subTypeRow#local.qryData.typeID#">
			<cfset local.arrSubLists.append({
				"level": 1,
				"rowType": "subType",
				"subscriptionID": val(local.qryData.subscriptionID),
				"typeID": local.qryData.typeID,
				"displayName": local.qryData.typeName,
				"hasChildren": val(local.qryData.subscriptionID) gt 0,
				"parentRowID": "gridRoot",
				"DT_RowId": local.subTypeRowID,
				"DT_RowClass": "child-of-gridRoot"
			})>
			<cfoutput>
				<cfif val(local.qryData.subscriptionID) gt 0>
					<cfset local.arrSubLists.append({
						"level": 2,
						"rowType": "subList",
						"subscriptionID": local.qryData.subscriptionID,
						"typeID": local.qryData.typeID,
						"displayName": local.qryData.subscriptionName,
						"parentRowID": local.subTypeRowID,
						"DT_RowId": "clt#local.qryData.typeID#-#local.qryData.subscriptionID#",
						"DT_RowClass": "child-of-#local.subTypeRowID# d-none"
					})>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfset local.returnStruct = {
			"success": true,
			"recordsTotal": arrayLen(local.arrSubLists),
			"recordsFiltered": arrayLen(local.arrSubLists),
			"data": local.arrSubLists
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAddonsForSub" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.reorderData = DeserializeJSON(arguments.event.getValue('reorderData',''))>

		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfif isArray(local.reorderData) and arrayLen(local.reorderData) GT 1>
			<cfloop array="#local.reorderData#" index="local.thisData">
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateData">
					UPDATE ao
					SET ao.orderNum = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
					FROM dbo.sub_addons ao 
					INNER JOIN dbo.sub_sets sets ON sets.setID = ao.childSetID 
							AND sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					WHERE ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('subID', 0)#">
					AND addonID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
				</cfquery>
			</cfloop>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddons">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ao.addonID, ao.subscriptionID, ao.childSetID, ao.orderNum, 
			ao.minAllowed , 
			ao.maxAllowed ,
			CASE WHEN ao.useAcctCodeInSet = 1 THEN 'Yes' ELSE 'No' END as useAcctCodeInSet,
			ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
			CASE WHEN ao.frontEndAllowSelect = 1 THEN 'Yes' ELSE 'No' END as frontEndAllowSelect, 
			CASE WHEN ao.frontEndAllowCHangePrice = 1 THEN 'Yes' ELSE 'No' END as frontEndAllowCHangePrice,
			sets.setName, sets.status, sets.setID
			from dbo.sub_addons ao 
			inner join dbo.sub_sets sets on sets.setID = ao.childSetID 
				and sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			where ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('subID', 0)#">
			order by ao.orderNum, sets.setName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qryAddons">
			<cfif local.qryAddons.minAllowed GT 0 >
				<cfset local.min = local.qryAddons.minAllowed>
			<cfelse>
				<cfset local.min = 'No Min'>
			</cfif>
			<cfif local.qryAddons.maxAllowed GT 0 >
				<cfset local.max = local.qryAddons.maxAllowed>
			<cfelse>
				<cfset local.max = 'No Max'>
			</cfif>
			<cfset local.arrData.append({
				"addonid": local.qryAddons.addonID,
				"subscriptionID": local.qryAddons.subscriptionID,
				"setname": htmleditformat(local.qryAddons.setName),
				"setID": local.qryAddons.setID,
				"min": local.min,
				"max": local.max,
				"useAcctCodeInSet": local.qryAddons.useAcctCodeInSet,
				"frontEndAllowSelect": local.qryAddons.frontEndAllowSelect,
				"frontEndAllowCHangePrice": local.qryAddons.frontEndAllowCHangePrice,
				"DT_RowId": "row_#local.qryAddons.addonID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryAddons.recordCount,
			"recordsFiltered": local.qryAddons.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getSetsForSub" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySets">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.setID, s.setName
			FROM dbo.sub_sets s 
			INNER JOIN dbo.sub_subscriptionSets ss on ss.setID = s.setID 
				AND ss.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('subID', 0)#">
			WHERE s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			AND s.status = 'A'
			ORDER BY s.setName;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qrySets">
			<cfset local.arrData.append({
				"setid": local.qrySets.setID,
				"setname": htmleditformat(local.qrySets.setName),
				"DT_RowId": "row_#local.qrySets.setID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qrySets.recordCount,
			"recordsFiltered": local.qrySets.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>