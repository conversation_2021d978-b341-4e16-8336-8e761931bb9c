(window.webpackJsonp = window.webpackJsonp || []).push([
	[240],
	{
		1498: function (_, t, r) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					r = {
						name: "x-pseudo",
						weekdays:
							"S~úñdá~ý_Mó~ñdáý~_Túé~sdáý~_Wéd~ñésd~áý_T~húrs~dáý_~Fríd~áý_S~átúr~dáý".split(
								"_",
							),
						months:
							"J~áñúá~rý_F~ébrú~árý_~Márc~h_Áp~ríl_~Máý_~Júñé~_Júl~ý_Áú~gúst~_Sép~témb~ér_Ó~ctób~ér_Ñ~óvém~bér_~Décé~mbér".split(
								"_",
							),
						weekStart: 1,
						weekdaysShort: "S~úñ_~Móñ_~Túé_~Wéd_~Thú_~Frí_~Sát".split("_"),
						monthsShort:
							"J~áñ_~Féb_~Már_~Ápr_~Máý_~Júñ_~Júl_~Áúg_~Sép_~Óct_~Ñóv_~Déc".split(
								"_",
							),
						weekdaysMin: "S~ú_Mó~_Tú_~Wé_T~h_Fr~_Sá".split("_"),
						ordinal: function (_) {
							return _;
						},
						formats: {
							LT: "HH:mm",
							LTS: "HH:mm:ss",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY HH:mm",
							LLLL: "dddd, D MMMM YYYY HH:mm",
						},
						relativeTime: {
							future: "í~ñ %s",
							past: "%s á~gó",
							s: "á ~féw ~sécó~ñds",
							m: "á ~míñ~úté",
							mm: "%d m~íñú~tés",
							h: "á~ñ hó~úr",
							hh: "%d h~óúrs",
							d: "á ~dáý",
							dd: "%d d~áýs",
							M: "á ~móñ~th",
							MM: "%d m~óñt~hs",
							y: "á ~ýéár",
							yy: "%d ý~éárs",
						},
					};
				return t.default.locale(r, null, !0), r;
			})(r(105));
		},
	},
]);
//# sourceMappingURL=chunk.240.js.map
