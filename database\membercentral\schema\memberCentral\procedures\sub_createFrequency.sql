ALTER PROC dbo.sub_createFrequency 
@frequencyName varchar (50), 
@frequency int, 
@frequencyShortName varchar (10), 
@rateRequired bit, 
@hasInstallments bit = 0, 
@monthlyInterval int = null, 
@isSystemRate bit = 0,
@siteID int, 
@status char(1),
@recordedByMemberID int,
@frequencyID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @frequencyID = null;
	DECLARE @crlf varchar(10), @msgjson varchar(max), @orgID int;

	SET @crlf = char(13) + char(10);
	select @orgID = orgID from sites where siteID = @siteID;

	IF NOT EXISTS (select frequencyID from dbo.sub_frequencies where siteID = @siteID and [status] <> 'D' and (frequencyName = @frequencyName or frequencyShortName = @frequencyShortName)) BEGIN
		INSERT into dbo.sub_frequencies (frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, 
			monthlyInterval, isSystemRate, siteID, [status]) 
		VALUES (@frequencyName, @frequency, @frequencyShortName, @rateRequired, @hasInstallments, @monthlyInterval, 
			@isSystemRate, @siteID, @status);
	
		select @frequencyID = SCOPE_IDENTITY();

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "FREQUENCYID":'+CAST(@frequencyID AS varchar(10))+' }';
		SET @msgjson = 'New Frequency [' + @frequencyName + '] has been created.'
			+ CASE WHEN @frequency IS NOT NULL THEN @crlf + 'Number of Installments: ' + CAST(@frequency AS VARCHAR(10)) ELSE '' END
			+ CASE WHEN NULLIF(@frequencyShortName,'') IS NOT NULL THEN @crlf + 'Short Name: ' + @frequencyShortName ELSE '' END
			+ CASE WHEN @rateRequired IS NOT NULL THEN @crlf + 'Rate Required: ' + CASE WHEN @rateRequired = 1 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @frequency IS NOT NULL THEN @crlf + 'Has Installments: ' + CASE WHEN @frequency > 0 THEN 'Yes' ELSE 'No' END ELSE '' END
			+ CASE WHEN @monthlyInterval IS NOT NULL THEN @crlf + 'Number of Months Between Payments: ' + CAST(@monthlyInterval AS VARCHAR(10)) ELSE '' END;

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='FREQ', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
