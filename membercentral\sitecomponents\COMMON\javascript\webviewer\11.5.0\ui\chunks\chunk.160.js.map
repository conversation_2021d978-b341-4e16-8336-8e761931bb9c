{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/gl.js"], "names": ["module", "exports", "e", "s", "default", "o", "d", "name", "weekdays", "split", "months", "weekStart", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,mDAAmDC,MAAM,KAAKC,OAAO,yFAAyFD,MAAM,KAAKE,UAAU,EAAEC,cAAc,qCAAqCH,MAAM,KAAKI,YAAY,8DAA8DJ,MAAM,KAAKK,YAAY,uBAAuBL,MAAM,KAAKM,QAAQ,SAASb,GAAG,OAAOA,EAAE,KAAKc,QAAQ,CAACC,GAAG,OAAOC,IAAI,UAAUC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,6BAA6BC,KAAK,oCAAoCC,aAAa,CAACC,OAAO,QAAQC,KAAK,SAAStB,EAAE,eAAeuB,EAAE,YAAYC,GAAG,aAAaC,EAAE,YAAYC,GAAG,WAAWvB,EAAE,SAASwB,GAAG,UAAUC,EAAE,SAASC,GAAG,WAAWC,EAAE,SAASC,GAAG,YAAY,OAAO/B,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAAzjCD,CAAE,EAAQ", "file": "chunks/chunk.160.js", "sourcesContent": ["!function(e,o){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=o(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],o):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_gl=o(e.dayjs)}(this,(function(e){\"use strict\";function o(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var s=o(e),d={name:\"gl\",weekdays:\"domingo_luns_martes_mércores_xoves_venres_sábado\".split(\"_\"),months:\"xaneiro_febreiro_marzo_abril_maio_xuño_xullo_agosto_setembro_outubro_novembro_decembro\".split(\"_\"),weekStart:1,weekdaysShort:\"dom._lun._mar._mér._xov._ven._sáb.\".split(\"_\"),monthsShort:\"xan._feb._mar._abr._mai._xuñ._xul._ago._set._out._nov._dec.\".split(\"_\"),weekdaysMin:\"do_lu_ma_mé_xo_ve_sá\".split(\"_\"),ordinal:function(e){return e+\"º\"},formats:{LT:\"H:mm\",LTS:\"H:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D [de] MMMM [de] YYYY\",LLL:\"D [de] MMMM [de] YYYY H:mm\",LLLL:\"dddd, D [de] MMMM [de] YYYY H:mm\"},relativeTime:{future:\"en %s\",past:\"fai %s\",s:\"uns segundos\",m:\"un minuto\",mm:\"%d minutos\",h:\"unha hora\",hh:\"%d horas\",d:\"un día\",dd:\"%d días\",M:\"un mes\",MM:\"%d meses\",y:\"un ano\",yy:\"%d anos\"}};return s.default.locale(d,null,!0),d}));"], "sourceRoot": ""}