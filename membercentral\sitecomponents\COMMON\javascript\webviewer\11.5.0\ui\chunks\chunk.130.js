(window.webpackJsonp = window.webpackJsonp || []).push([
	[130],
	{
		1388: function (e, n, t) {
			e.exports = (function (e) {
				"use strict";
				var n = (function (e) {
						return e && "object" == typeof e && "default" in e
							? e
							: { default: e };
					})(e),
					t = {
						s: "ein paar Sekunden",
						m: ["eine Minute", "einer Minute"],
						mm: "%d Minuten",
						h: ["eine Stunde", "einer Stunde"],
						hh: "%d Stunden",
						d: ["ein Tag", "einem Tag"],
						dd: ["%d Tage", "%d Tagen"],
						M: ["ein Monat", "einem Monat"],
						MM: ["%d <PERSON><PERSON>", "%d <PERSON>ten"],
						y: ["ein Jahr", "einem Jahr"],
						yy: ["%d Jahre", "%d Jahren"],
					};
				function a(e, n, a) {
					var r = t[a];
					return Array.isArray(r) && (r = r[n ? 0 : 1]), r.replace("%d", e);
				}
				var r = {
					name: "de",
					weekdays:
						"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split(
							"_",
						),
					weekdaysShort: "So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),
					weekdaysMin: "So_Mo_Di_Mi_Do_Fr_Sa".split("_"),
					months:
						"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split(
							"_",
						),
					monthsShort:
						"Jan._Feb._März_Apr._Mai_Juni_Juli_Aug._Sept._Okt._Nov._Dez.".split(
							"_",
						),
					ordinal: function (e) {
						return e + ".";
					},
					weekStart: 1,
					yearStart: 4,
					formats: {
						LTS: "HH:mm:ss",
						LT: "HH:mm",
						L: "DD.MM.YYYY",
						LL: "D. MMMM YYYY",
						LLL: "D. MMMM YYYY HH:mm",
						LLLL: "dddd, D. MMMM YYYY HH:mm",
					},
					relativeTime: {
						future: "in %s",
						past: "vor %s",
						s: a,
						m: a,
						mm: a,
						h: a,
						hh: a,
						d: a,
						dd: a,
						M: a,
						MM: a,
						y: a,
						yy: a,
					},
				};
				return n.default.locale(r, null, !0), r;
			})(t(105));
		},
	},
]);
//# sourceMappingURL=chunk.130.js.map
