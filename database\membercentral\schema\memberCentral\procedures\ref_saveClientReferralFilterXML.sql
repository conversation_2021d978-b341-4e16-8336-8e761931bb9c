ALTER PROC dbo.ref_saveClientReferralFilterXML
@siteID int,
@clientID int,
@panelID1 int,
@subPanelID1 varchar(500),
@xmlSearch xml,
@statsSessionID int,
@enteredByMemberID int,
@searchID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @clientReferralID int, @currentSearchID int, @existingSearchXML xml, @primaryPanel varchar(255),
		@primarySubPanelList varchar(max), @secondaryPanel varchar(255), @secondarySubPanelList varchar(max), @tertiaryPanel varchar(255),
		@tertiarySubPanelList varchar(max), @crlf varchar(10), @msgjson varchar(max), @message varchar(max);

	SET @crlf = char(13) + char(10);
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT TOP 1 @clientReferralID = clientReferralID
	FROM dbo.ref_clientReferrals
	WHERE clientID = @clientID;
			
	SELECT TOP 1 @currentSearchID = searchID, @existingSearchXML = searchXML
	FROM searchMC.dbo.tblSearchReferralHistory
	WHERE clientID = @clientID
	ORDER BY dateEntered DESC;

	IF @currentSearchID IS NOT NULL BEGIN
		UPDATE searchMC.dbo.tblSearchReferralHistory 
		SET statsSessionID = NULLIF(@statsSessionID,0), 
			searchXML = @xmlSearch, 
			panelID1 = @panelID1,
			subPanelID1 = @subPanelID1
		WHERE clientID = @clientID;

		SELECT @searchID = searchID
		FROM searchMC.dbo.tblSearchReferralHistory
		WHERE clientID = @clientID;
	END
	ELSE BEGIN
		INSERT INTO searchMC.dbo.tblSearchReferralHistory (clientID, statsSessionID, dateEntered, searchXML, panelID1, subPanelID1)
		VALUES (@clientID, NULLIF(@statsSessionID,0), GETDATE(), @xmlSearch, @panelID1, @subPanelID1);

		SELECT @searchID = SCOPE_IDENTITY();
	END
	
	-- audit logging
	SELECT @primaryPanel = p1.[name], @primarySubPanelList = NULLIF(tmpSP1.nameList,''),
		@secondaryPanel = p2.[name], @secondarySubPanelList=NULLIF(tmpSP2.nameList,''),
		@tertiaryPanel = p3.[name], @tertiarySubPanelList=NULLIF(tmpSP3.nameList,'')
	FROM @xmlSearch.nodes('/search') AS T(X)
	LEFT OUTER JOIN dbo.ref_panels AS p1 ON p1.panelID = X.value('(field[@name="panelid1"]/text())[1]', 'INT')
	LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = X.value('(field[@name="panelid2"]/text())[1]', 'INT')
	LEFT OUTER JOIN dbo.ref_panels AS p3 ON p3.panelID = X.value('(field[@name="panelid3"]/text())[1]', 'INT')
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid1"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP1
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid2"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP2
	OUTER APPLY (
		SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
		FROM dbo.ref_panels AS sp
		INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid3"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
	) AS tmpSP3;

	IF @currentSearchID IS NOT NULL BEGIN
		IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;

		CREATE TABLE #tmpAuditLogData (
			[rowCode] varchar(20) PRIMARY KEY, [Primary Panel] varchar(max), [Primary Sub-Panels] varchar(max),
			[Secondary Panel] varchar(max), [Secondary Sub-Panels] varchar(max), [Tertiary Panel] varchar(max),
			[Tertiary Sub-Panels] varchar(max)
		);

		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		SELECT 
			'OLDVAL', p1.[name] AS primaryPanel, NULLIF(tmpSP1.nameList,'') AS primarySubPanelList,
			p2.[name] AS secondaryPanel, NULLIF(tmpSP2.nameList,'') AS secondarySubPanelList,
			p3.[name] AS tertiaryPanel, NULLIF(tmpSP3.nameList,'') AS tertiarySubPanelList
		FROM @existingSearchXML.nodes('/search') AS T(X)
		LEFT OUTER JOIN dbo.ref_panels AS p1 ON p1.panelID = X.value('(field[@name="panelid1"]/text())[1]', 'INT')
		LEFT OUTER JOIN dbo.ref_panels AS p2 ON p2.panelID = X.value('(field[@name="panelid2"]/text())[1]', 'INT')
		LEFT OUTER JOIN dbo.ref_panels AS p3 ON p3.panelID = X.value('(field[@name="panelid3"]/text())[1]', 'INT')
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid1"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP1
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid2"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP2
		OUTER APPLY (
			SELECT STRING_AGG(sp.[name],', ') WITHIN GROUP (ORDER BY sp.[name] ASC) as nameList
			FROM dbo.ref_panels AS sp
			INNER JOIN dbo.fn_intListToTable(ISNULL(X.value('(field[@name="subpanelid3"]/text())[1]', 'VARCHAR(255)'),''),',') tmp ON tmp.listitem = sp.panelID
		) AS tmpSP3;

		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING');
		INSERT INTO #tmpAuditLogData (
			[rowCode], [Primary Panel], [Primary Sub-Panels], [Secondary Panel],
			[Secondary Sub-Panels], [Tertiary Panel], [Tertiary Sub-Panels]
		)
		VALUES (
			'NEWVAL', @primaryPanel, @primarySubPanelList, @secondaryPanel,
			@secondarySubPanelList, @tertiaryPanel, @tertiarySubPanelList
		);

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @outputAsChangesArray=1, @msg=@msgjson OUTPUT;

		IF @msgjson <> '' BEGIN
			SET @msgjson = '[' + @msgjson + ']';
			EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
				@message='Referral Filters Updated', @changesArray=@msgjson, @enteredByMemberID=@enteredByMemberID;
		END

		IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
			DROP TABLE #tmpAuditLogData;
	END
	ELSE BEGIN
		SELECT @message = 'Referral Filters Added'
			+ CASE WHEN @primaryPanel IS NOT NULL THEN @crlf + 'Primary Panel: ' + @primaryPanel ELSE '' END
			+ CASE WHEN @primarySubPanelList IS NOT NULL THEN @crlf + 'Primary Sub-Panels: ' + @primarySubPanelList ELSE '' END
			+ CASE WHEN @secondaryPanel IS NOT NULL THEN @crlf + 'Secondary Panel: ' + @secondaryPanel ELSE '' END
			+ CASE WHEN @secondarySubPanelList IS NOT NULL THEN @crlf + 'Secondary Sub-Panels: ' + @secondarySubPanelList ELSE '' END
			+ CASE WHEN @tertiaryPanel IS NOT NULL THEN @crlf + 'Tertiary Panel: ' + @tertiaryPanel ELSE '' END
			+ CASE WHEN @tertiarySubPanelList IS NOT NULL THEN @crlf + 'Tertiary Sub-Panels: ' + @tertiarySubPanelList ELSE '' END;

		SET @message = STRING_ESCAPE(@message,'json');

		EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
			@message=@message, @enteredByMemberID=@enteredByMemberID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
