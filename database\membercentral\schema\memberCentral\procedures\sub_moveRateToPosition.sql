ALTER PROCEDURE dbo.sub_moveRateToPosition
@rateID int,
@pos int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @orgID int, @rateOrder int, @scheduleID int, @rateName varchar(200), @scheduleName varchar(200), 
		@msg varchar(max), @subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);
	declare @tmp table (newOrder int NOT NULL, rateID int NOT NULL, rateOrder int NOT NULL);

	SELECT @siteID = srs.siteID, @orgID = s.orgID, @rateName = sr.rateName, @scheduleID = srs.scheduleID, 
		@scheduleName = srs.scheduleName, @rateOrder = sr.rateOrder
	FROM dbo.sub_rates AS sr
	INNER JOIN dbo.sub_rateSchedules AS srs ON srs.scheduleID = sr.scheduleID
	INNER JOIN dbo.sites AS s ON s.siteID = srs.siteID
	WHERE rateID = @rateID;

	IF @rateOrder <> @pos BEGIN
		SET @subKeyMapJSON = '{ "RATEID":' + CAST(@rateID AS varchar(10)) + ', "RATESCHEDULEID":' + CAST(@scheduleID AS varchar(10)) + ' }';

		SET @msg =  'Rate ' + QUOTENAME(@rateName) + ' under the Rate Schedule ' + QUOTENAME(@scheduleName) + ' moved to position ' + CAST(@pos AS varchar(10));
	END

	BEGIN TRAN;
		if @rateOrder > @pos begin
			insert into @tmp (rateID, rateOrder, newOrder)
			select rateID, rateOrder + 1, ROW_NUMBER() OVER (order by rateOrder)
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.scheduleID = @scheduleID
			and (r.rateOrder >= @pos and r.rateOrder <= @rateOrder);

			update rates
			set rates.rateOrder = t.rateOrder
			from dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
			where rates.scheduleID = @scheduleID
			and rates.status = 'A';
		end else begin
			insert into @tmp (rateID, rateOrder, newOrder)
			select rateID, rateOrder, ROW_NUMBER() OVER (order by rateOrder)
			from dbo.sub_rates as r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where r.scheduleID = @scheduleID
			and rateOrder <= @pos
			and r.rateID <> @rateID;

			update rates
			set rates.rateOrder = t.neworder
			from dbo.sub_rates as rates 
			inner join @tmp as t on t.rateID = rates.rateID 
			where rates.scheduleID = @scheduleID
			and rates.status = 'A'
		end

		update dbo.sub_rates
		set rateOrder = @pos
		where rateID = @rateID;

		IF ISNULL(@msg,'') <> '' BEGIN
			SET @msg = STRING_ESCAPE(@msg,'json');
			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
