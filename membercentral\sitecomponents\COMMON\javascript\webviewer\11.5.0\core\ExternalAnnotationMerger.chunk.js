/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[4],
		{
			641: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(663),
					ma = r(664),
					ja;
				(function (ia) {
					ia[(ia.EXTERNAL_XFDF_NOT_REQUESTED = 0)] =
						"EXTERNAL_XFDF_NOT_REQUESTED";
					ia[(ia.EXTERNAL_XFDF_NOT_AVAILABLE = 1)] =
						"EXTERNAL_XFDF_NOT_AVAILABLE";
					ia[(ia.EXTERNAL_XFDF_AVAILABLE = 2)] = "EXTERNAL_XFDF_AVAILABLE";
				})(ja || (ja = {}));
				ya = (function () {
					function ia(ca) {
						this.ba = ca;
						this.state = ja.EXTERNAL_XFDF_NOT_REQUESTED;
					}
					ia.prototype.OEa = function () {
						var ca = this;
						return function (y, x, n) {
							return Object(la.b)(ca, void 0, void 0, function () {
								var h,
									b,
									e,
									f,
									a,
									w,
									z,
									aa = this,
									ea;
								return Object(la.d)(this, function (ba) {
									switch (ba.label) {
										case 0:
											if (this.state !== ja.EXTERNAL_XFDF_NOT_REQUESTED)
												return [3, 2];
											h = this.ba.getDocument().Iz();
											return [4, this.fCa(h)];
										case 1:
											(b = ba.aa()),
												(e = this.wva(b)),
												(this.WU =
													null !==
														(ea =
															null === e || void 0 === e
																? void 0
																: e.parse()) && void 0 !== ea
														? ea
														: null),
												(this.state =
													null === this.WU
														? ja.EXTERNAL_XFDF_NOT_AVAILABLE
														: ja.EXTERNAL_XFDF_AVAILABLE),
												(ba.label = 2);
										case 2:
											if (this.state === ja.EXTERNAL_XFDF_NOT_AVAILABLE)
												return n(y), [2];
											f = new DOMParser();
											a = f.parseFromString(y, "text/xml");
											x.forEach(function (fa) {
												aa.merge(a, aa.WU, fa - 1);
											});
											w = new XMLSerializer();
											z = w.serializeToString(a);
											n(z);
											return [2];
									}
								});
							});
						};
					};
					ia.prototype.b0 = function (ca) {
						this.fCa = ca;
					};
					ia.prototype.Tf = function () {
						this.WU = void 0;
						this.state = ja.EXTERNAL_XFDF_NOT_REQUESTED;
					};
					ia.prototype.wva = function (ca) {
						return ca
							? Array.isArray(ca)
								? new na.a(ca)
								: "string" !== typeof ca
									? null
									: new DOMParser()
												.parseFromString(ca, "text/xml")
												.querySelector("xfdf > add")
										? new na.a(ca)
										: new ma.a(ca)
							: null;
					};
					ia.prototype.merge = function (ca, y, x) {
						var n = this;
						0 === x && (this.yJa(ca, y.hv), this.AJa(ca, y.AU));
						var h = y.ea[x];
						h &&
							(this.BJa(ca, h.Zu),
							this.DJa(ca, h.ika, y.aE),
							this.CJa(ca, h.page, x),
							this.zJa(ca, h.z8));
						h = this.ba.xb();
						if (x === h - 1) {
							var b = y.aE;
							Object.keys(b).forEach(function (e) {
								b[e].XW || n.mda(ca, e, b[e]);
							});
						}
					};
					ia.prototype.yJa = function (ca, y) {
						null !== y &&
							((ca = this.dD(ca)), this.Uw(ca, "calculation-order", y));
					};
					ia.prototype.AJa = function (ca, y) {
						null !== y &&
							((ca = this.dD(ca)), this.Uw(ca, "document-actions", y));
					};
					ia.prototype.BJa = function (ca, y) {
						var x = this,
							n = this.cD(ca.querySelector("xfdf"), "annots");
						Object.keys(y).forEach(function (h) {
							x.Uw(n, '[name="'.concat(h, '"]'), y[h]);
						});
					};
					ia.prototype.DJa = function (ca, y, x) {
						var n = this;
						if (0 !== y.length) {
							var h = this.dD(ca);
							y.forEach(function (b) {
								var e = b.getAttribute("field"),
									f = x[e];
								f && (n.mda(ca, e, f), n.Uw(h, "null", b));
							});
						}
					};
					ia.prototype.mda = function (ca, y, x) {
						var n = this.dD(ca),
							h = n.querySelector('ffield[name="'.concat(y, '"]'));
						null !== x.DL &&
							null === h &&
							this.Uw(n, 'ffield[name="'.concat(y, '"]'), x.DL);
						ca = this.cD(ca.querySelector("xfdf"), "xfdf > fields", "fields");
						y = y.split(".");
						this.SZ(ca, y, 0, x.value);
						x.XW = !0;
					};
					ia.prototype.CJa = function (ca, y, x) {
						null !== y &&
							((ca = this.dD(ca)),
							(ca = this.cD(ca, "pages")),
							this.Uw(ca, '[number="'.concat(x + 1, '"]'), y));
					};
					ia.prototype.zJa = function (ca, y) {
						Object.keys(y).forEach(function (x) {
							(x = ca.querySelector('annots [name="'.concat(x, '"]'))) &&
								x.parentElement.removeChild(x);
						});
					};
					ia.prototype.SZ = function (ca, y, x, n) {
						if (x === y.length)
							(y = document.createElementNS("", "value")),
								(y.textContent = n),
								this.Uw(ca, "value", y);
						else {
							var h = y[x];
							this.cD(ca, '[name="'.concat(h, '"]'), "field").setAttribute(
								"name",
								h,
							);
							ca = ca.querySelectorAll('[name="'.concat(h, '"]'));
							1 === ca.length
								? this.SZ(ca[0], y, x + 1, n)
								: ((h = this.jAa(ca)),
									this.SZ(
										x === y.length - 1 ? h : this.xUa(ca, h),
										y,
										x + 1,
										n,
									));
						}
					};
					ia.prototype.jAa = function (ca) {
						for (var y = null, x = 0; x < ca.length; x++) {
							var n = ca[x];
							if (
								0 === n.childElementCount ||
								(1 === n.childElementCount && "value" === n.children[0].tagName)
							) {
								y = n;
								break;
							}
						}
						return y;
					};
					ia.prototype.xUa = function (ca, y) {
						for (var x = 0; x < ca.length; x++) if (ca[x] !== y) return ca[x];
						return null;
					};
					ia.prototype.Uw = function (ca, y, x) {
						y = ca.querySelector(y);
						null !== y && ca.removeChild(y);
						ca.appendChild(x);
					};
					ia.prototype.dD = function (ca) {
						var y = ca.querySelector("pdf-info");
						if (null !== y) return y;
						y = this.cD(ca.querySelector("xfdf"), "pdf-info");
						y.setAttribute("xmlns", "http://www.pdftron.com/pdfinfo");
						y.setAttribute("version", "2");
						y.setAttribute("import-version", "4");
						return y;
					};
					ia.prototype.cD = function (ca, y, x) {
						var n = ca.querySelector(y);
						if (null !== n) return n;
						n = document.createElementNS("", x || y);
						ca.appendChild(n);
						return n;
					};
					return ia;
				})();
				va["default"] = ya;
			},
			652: function (ya, va) {
				ya = (function () {
					function r() {}
					r.prototype.HJ = function (la) {
						var na = { hv: null, AU: null, aE: {}, ea: {} };
						la = new DOMParser().parseFromString(la, "text/xml");
						na.hv = la.querySelector("pdf-info calculation-order");
						na.AU = la.querySelector("pdf-info document-actions");
						na.aE = this.qLa(la);
						na.ea = this.ELa(la);
						return na;
					};
					r.prototype.qLa = function (la) {
						var na = la.querySelector("fields");
						la = la.querySelectorAll("pdf-info > ffield");
						if (null === na && null === la) return {};
						var ma = {};
						this.ura(ma, na);
						this.sra(ma, la);
						return ma;
					};
					r.prototype.ura = function (la, na) {
						if (null !== na && na.children) {
							for (var ma = [], ja = 0; ja < na.children.length; ja++) {
								var ia = na.children[ja];
								ma.push({ name: ia.getAttribute("name"), element: ia });
							}
							for (; 0 !== ma.length; )
								for (
									na = ma.shift(), ja = 0;
									ja < na.element.children.length;
									ja++
								)
									(ia = na.element.children[ja]),
										"value" === ia.tagName
											? (la[na.name] = {
													value: ia.textContent,
													DL: null,
													XW: !1,
												})
											: ia.children &&
												ma.push({
													name: ""
														.concat(na.name, ".")
														.concat(ia.getAttribute("name")),
													element: ia,
												});
						}
					};
					r.prototype.sra = function (la, na) {
						na.forEach(function (ma) {
							var ja = ma.getAttribute("name");
							la[ja]
								? (la[ja].DL = ma)
								: (la[ja] = { value: null, DL: ma, XW: !1 });
						});
					};
					r.prototype.ELa = function (la) {
						var na = this,
							ma = {};
						la.querySelectorAll("pdf-info widget").forEach(function (ja) {
							var ia = parseInt(ja.getAttribute("page"), 10) - 1;
							na.bN(ma, ia);
							ma[ia].ika.push(ja);
						});
						la.querySelectorAll("pdf-info page").forEach(function (ja) {
							var ia = parseInt(ja.getAttribute("number"), 10) - 1;
							na.bN(ma, ia);
							ma[ia].page = ja;
						});
						this.U$(la).forEach(function (ja) {
							var ia = parseInt(ja.getAttribute("page"), 10),
								ca = ja.getAttribute("name");
							na.bN(ma, ia);
							ma[ia].Zu[ca] = ja;
						});
						this.y$(la).forEach(function (ja) {
							var ia = parseInt(ja.getAttribute("page"), 10);
							ja = ja.textContent;
							na.bN(ma, ia);
							ma[ia].z8[ja] = !0;
						});
						return ma;
					};
					r.prototype.bN = function (la, na) {
						la[na] || (la[na] = { Zu: {}, z8: {}, ika: [], page: null });
					};
					return r;
				})();
				va.a = ya;
			},
			663: function (ya, va, r) {
				var la = r(0),
					na = r(1);
				r.n(na);
				ya = (function (ma) {
					function ja(ia) {
						var ca = ma.call(this) || this;
						ca.Rza = Array.isArray(ia) ? ia : [ia];
						return ca;
					}
					Object(la.c)(ja, ma);
					ja.prototype.parse = function () {
						var ia = this,
							ca = { hv: null, AU: null, aE: {}, ea: {} };
						this.Rza.forEach(function (y) {
							ca = Object(na.merge)(ca, ia.HJ(y));
						});
						return ca;
					};
					ja.prototype.U$ = function (ia) {
						var ca = [];
						ia.querySelectorAll("add > *").forEach(function (y) {
							ca.push(y);
						});
						ia.querySelectorAll("modify > *").forEach(function (y) {
							ca.push(y);
						});
						return ca;
					};
					ja.prototype.y$ = function (ia) {
						return ia.querySelectorAll("delete > *");
					};
					return ja;
				})(r(652).a);
				va.a = ya;
			},
			664: function (ya, va, r) {
				var la = r(0);
				ya = (function (na) {
					function ma(ja) {
						var ia = na.call(this) || this;
						ia.Sza = ja;
						return ia;
					}
					Object(la.c)(ma, na);
					ma.prototype.parse = function () {
						return this.HJ(this.Sza);
					};
					ma.prototype.U$ = function (ja) {
						return ja.querySelectorAll("annots > *");
					};
					ma.prototype.y$ = function () {
						return [];
					};
					return ma;
				})(r(652).a);
				va.a = ya;
			},
		},
	]);
}).call(this || window);
