(window.webpackJsonp = window.webpackJsonp || []).push([
	[80],
	{
		1864: function (t, e, n) {
			var o = n(30),
				i = n(1865);
			"string" == typeof (i = i.__esModule ? i.default : i) &&
				(i = [[t.i, i, ""]]);
			var a = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, n = document) {
								const o = [];
								return (
									n.querySelectorAll(e).forEach((t) => o.push(t)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...t(e, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < e.length; o++) {
						const i = e[o];
						if (0 === o)
							i.shadowRoot.appendChild(t),
								(t.onload = function () {
									n.length > 0 &&
										n.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							i.shadowRoot.appendChild(e), n.push(e);
						}
					}
				},
				singleton: !1,
			};
			o(i, a);
			t.exports = i.locals || {};
		},
		1865: function (t, e, n) {
			(e = t.exports = n(31)(!1)).push([
				t.i,
				".open.TextPopup{visibility:visible}.closed.TextPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.TextPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.TextPopup:empty{padding:0}.TextPopup .buttons{display:flex}.TextPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .TextPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .TextPopup .Button{width:42px;height:42px}}.TextPopup .Button:hover{background:var(--popup-button-hover)}.TextPopup .Button:hover:disabled{background:none}.TextPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .TextPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .TextPopup .Button .Icon{width:24px;height:24px}}.is-vertical.TextPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.TextPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.TextPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.TextPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.TextPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.TextPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.TextPopup{box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);border-radius:4px}.TextPopup.is-horizontal .container{display:inherit}.TextPopup.is-vertical{flex-direction:column;align-items:flex-start}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2049: function (t, e, n) {
			"use strict";
			n.r(e);
			n(19),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22),
				n(32),
				n(29),
				n(47),
				n(23),
				n(24),
				n(49),
				n(48);
			var o = n(0),
				i = n.n(o),
				a = n(6),
				r = n(1506),
				l = n(302),
				p = n(177),
				c = n.n(p),
				u = n(17),
				s = n.n(u),
				d = n(82),
				m = n(1778),
				b = n(1),
				h = n(291),
				f = n(283),
				x = n(513),
				y = n(140),
				w = n(2),
				g = n(4),
				P = n(38),
				T = n(5);
			n(1864);
			function v(t) {
				return (v =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function E(t, e) {
				var n = Object.keys(t);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(t);
					e &&
						(o = o.filter(function (e) {
							return Object.getOwnPropertyDescriptor(t, e).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function O(t) {
				for (var e = 1; e < arguments.length; e++) {
					var n = null != arguments[e] ? arguments[e] : {};
					e % 2
						? E(Object(n), !0).forEach(function (e) {
								k(t, e, n[e]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n))
							: E(Object(n)).forEach(function (e) {
									Object.defineProperty(
										t,
										e,
										Object.getOwnPropertyDescriptor(n, e),
									);
								});
				}
				return t;
			}
			function k(t, e, n) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== v(t) || null === t) return t;
							var n = t[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(t, e || "default");
								if ("object" !== v(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === v(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = n),
					t
				);
			}
			function A(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var n =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != n) {
							var o,
								i,
								a,
								r,
								l = [],
								p = !0,
								c = !1;
							try {
								if (((a = (n = n.call(t)).next), 0 === e)) {
									if (Object(n) !== n) return;
									p = !1;
								} else
									for (
										;
										!(p = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== e);
										p = !0
									);
							} catch (t) {
								(c = !0), (i = t);
							} finally {
								try {
									if (
										!p &&
										null != n.return &&
										((r = n.return()), Object(r) !== r)
									)
										return;
								} finally {
									if (c) throw i;
								}
							}
							return l;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return j(t, e);
						var n = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === n && t.constructor && (n = t.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(t);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return j(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function j(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var n = 0, o = new Array(e); n < e; n++) o[n] = t[n];
				return o;
			}
			var C = i.a.memo(
				Object(r.a)()(function (t) {
					var e = t.t,
						n = t.selectedTextQuads,
						r = A(
							Object(a.e)(function (t) {
								return [
									g.a.isElementDisabled(t, T.a.TEXT_POPUP),
									g.a.isElementOpen(t, T.a.TEXT_POPUP),
									g.a.getPopupItems(t, T.a.TEXT_POPUP),
									g.a.isRightClickAnnotationPopupEnabled(t),
									g.a.getActiveDocumentViewerKey(t),
									g.a.isMultiViewerMode(t),
								];
							}, a.c),
							6,
						),
						p = r[0],
						u = r[1],
						v = r[2],
						E = r[3],
						k = r[4],
						j = r[5],
						C = Object(a.d)(),
						B = A(Object(o.useState)({ left: 0, top: 0 }), 2),
						N = B[0],
						S = B[1],
						_ = Object(o.useRef)();
					Object(y.a)(_, function () {
						C(w.a.closeElement(T.a.TEXT_POPUP));
					});
					Object(o.useEffect)(
						function () {
							u &&
								C(
									w.a.closeElements([
										T.a.ANNOTATION_POPUP,
										T.a.CONTEXT_MENU_POPUP,
										T.a.INLINE_COMMENT_POPUP,
									]),
								);
						},
						[C, u],
					),
						Object(o.useEffect)(
							function () {
								_.current && v.length > 0 && n && S(Object(h.f)(n, _, k));
							},
							[n, k, j],
						);
					if (p) return null;
					var I = i.a.createElement(
						"div",
						{
							className: s()({
								Popup: !0,
								TextPopup: !0,
								open: u,
								closed: !u,
								"is-vertical": E,
								"is-horizontal": !E,
							}),
							"data-element": T.a.TEXT_POPUP,
							ref: _,
							style: O({}, N),
							onClick: function () {
								return C(w.a.closeElement(T.a.TEXT_POPUP));
							},
							"aria-label": e("component.textPopup"),
						},
						i.a.createElement(
							l.a,
							{ locked: u && 0 !== N.top && 0 !== N.left },
							i.a.createElement(
								"div",
								{ className: "container" },
								i.a.createElement(
									m.a,
									{
										dataElement: T.a.TEXT_POPUP,
										childrenClassName: "main-menu-button",
									},
									i.a.createElement(d.a, {
										className: "main-menu-button",
										dataElement: "copyTextButton",
										label: E ? "action.copy" : "",
										title: E ? "" : "action.copy",
										img: "ic_copy_black_24px",
										onClick: function () {
											return Object(x.a)(k);
										},
									}),
									i.a.createElement(d.a, {
										className: "main-menu-button",
										dataElement: "textHighlightToolButton",
										label: E ? "annotation.highlight" : "",
										title: E ? "" : "annotation.highlight",
										img: "icon-tool-highlight",
										onClick: function () {
											return Object(f.a)(
												C,
												window.Core.Annotations.TextHighlightAnnotation,
												k,
											);
										},
									}),
									i.a.createElement(d.a, {
										className: "main-menu-button",
										dataElement: "textUnderlineToolButton",
										label: E ? "annotation.underline" : "",
										title: E ? "" : "annotation.underline",
										img: "icon-tool-text-manipulation-underline",
										onClick: function () {
											return Object(f.a)(
												C,
												window.Core.Annotations.TextUnderlineAnnotation,
												k,
											);
										},
									}),
									i.a.createElement(d.a, {
										className: "main-menu-button",
										dataElement: "textSquigglyToolButton",
										label: E ? "annotation.squiggly" : "",
										title: E ? "" : "annotation.squiggly",
										img: "icon-tool-text-manipulation-squiggly",
										onClick: function () {
											return Object(f.a)(
												C,
												window.Core.Annotations.TextSquigglyAnnotation,
												k,
											);
										},
									}),
									i.a.createElement(d.a, {
										className: "main-menu-button",
										label: E ? "annotation.strikeout" : "",
										title: E ? "" : "annotation.strikeout",
										img: "icon-tool-text-manipulation-strikethrough",
										onClick: function () {
											return Object(f.a)(
												C,
												window.Core.Annotations.TextStrikeoutAnnotation,
												k,
											);
										},
										dataElement: "textStrikeoutToolButton",
									}),
									i.a.createElement(d.a, {
										className: "main-menu-button",
										label: E ? "tool.Link" : "",
										title: E ? "" : "tool.Link",
										img: "icon-tool-link",
										onClick: function () {
											return C(w.a.openElement(T.a.LINK_MODAL));
										},
										dataElement: "linkButton",
									}),
									b.a.isCreateRedactionEnabled() &&
										i.a.createElement(d.a, {
											className: "main-menu-button",
											dataElement: "textRedactToolButton",
											label: E ? "option.redaction.markForRedaction" : "",
											title: E ? "" : "option.redaction.markForRedaction",
											fillColor: "868E96",
											img: "icon-tool-select-area-redaction",
											onClick: function () {
												return Object(f.a)(
													C,
													window.Core.Annotations.RedactionAnnotation,
													k,
												);
											},
										}),
								),
							),
						),
					);
					return P.e || Object(P.k)()
						? I
						: i.a.createElement(
								c.a,
								{
									cancel:
										".Button, .cell, .sliders-container svg, select, button, input",
								},
								I,
							);
				}),
			);
			e.default = C;
		},
	},
]);
//# sourceMappingURL=chunk.80.js.map
