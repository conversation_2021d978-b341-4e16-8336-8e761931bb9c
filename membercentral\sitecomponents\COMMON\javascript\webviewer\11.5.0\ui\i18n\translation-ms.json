{"action": {"addParagraph": "Tambah Perenggan", "add": "Tambah", "addSheet": "<PERSON><PERSON>", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> semua", "calendar": "<PERSON><PERSON><PERSON>", "calibrate": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "confirm": "sahkan", "currentPageIs": "<PERSON><PERSON> semasa i<PERSON>h", "clear": "<PERSON><PERSON>", "clearAll": "Kosongkan semua", "close": "tutup", "undo": "<PERSON><PERSON><PERSON> asal", "redo": "sedia", "comment": "Komen", "reply": "<PERSON><PERSON> balasan", "copy": "Salinan", "cut": "potong", "paste": "tampal", "pasteWithoutFormatting": "Tampal tanpa memformat", "delete": "Padam", "deleted": "<PERSON><PERSON><PERSON><PERSON>", "group": "Ku<PERSON><PERSON><PERSON>", "ungroup": "Nyahkumpulan", "download": "<PERSON>at turun", "edit": "Sunting", "collapse": "<PERSON><PERSON><PERSON>", "expand": "Kembangkan", "extract": "Ekstrak", "extractPage": "Ekstrak Halaman", "enterFullscreen": "<PERSON><PERSON><PERSON>", "exitFullscreen": "<PERSON><PERSON><PERSON> dari skrin penuh", "fit": "<PERSON><PERSON><PERSON>", "fitToPage": "<PERSON><PERSON> m<PERSON>uh", "fitToWidth": "<PERSON><PERSON><PERSON> dengan lebar", "more": "<PERSON><PERSON>", "openFile": "Buka fail", "showMoreFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON>", "of": "<PERSON><PERSON><PERSON>", "pagePrev": "Halaman sebelumnya", "pageNext": "Muka surat seterusnya", "pageSet": "Tetapkan halaman", "print": "Cetak", "proceed": "Teruskan", "name": "nama", "rename": "<PERSON><PERSON><PERSON> semula", "remove": "<PERSON><PERSON> k<PERSON>", "ok": "okey", "rotate": "<PERSON><PERSON>", "rotate3D": "Putar", "rotateClockwise": "Putar mengikut arah jam", "rotateCounterClockwise": "<PERSON>ar lawan jam", "rotatedClockwise": "diputar mengikut arah jam", "rotatedCounterClockwise": "diputar lawan jam", "rotationIs": "putaran halaman semasa ialah", "movedToBottomOfDocument": "dial<PERSON><PERSON> ke bahagian bawah dokumen", "movedToTopofDocument": "dialih<PERSON> ke bahagian atas dokumen", "extracted": "diekstrak", "save": "Jimat", "post": "Pos", "create": "Buat", "update": "<PERSON><PERSON> kini", "showMoreResults": "<PERSON><PERSON><PERSON><PERSON><PERSON> lebih banyak hasil", "sign": "<PERSON><PERSON>", "style": "<PERSON><PERSON>", "submit": "Hantar", "zoom": "<PERSON><PERSON>", "zoomIn": "Mengezum masuk", "zoomOut": "<PERSON><PERSON> keluar", "zoomSet": "Tetapkan zum", "zoomChanged": "<PERSON>um semasa i<PERSON>h", "zoomControls": "<PERSON><PERSON><PERSON>", "draw": "<PERSON><PERSON>", "type": "taip", "upload": "<PERSON>at naik", "link": "<PERSON><PERSON><PERSON>", "unlink": "<PERSON><PERSON><PERSON>", "fileAttachmentDownload": "Muat turun fail yang di<PERSON>an", "prevResult": "Keputusan sebelumnya", "nextResult": "Keputusan seterusnya", "prev": "Sebelumnya", "next": "Seterusnya", "startFormEditing": "<PERSON><PERSON><PERSON>", "exitFormEditing": "<PERSON><PERSON><PERSON> dari <PERSON>", "exit": "<PERSON><PERSON><PERSON>", "addOption": "Tambah Pilihan", "formFieldEdit": "<PERSON> <PERSON><PERSON>", "formFieldEditMode": "<PERSON> <PERSON><PERSON>", "contentEditMode": "<PERSON>", "viewShortCutKeysFor3D": "<PERSON><PERSON>", "markAllRead": "Tandai semua sebagai dibaca", "pageInsertion": "<PERSON><PERSON><PERSON>", "insertPage": "<PERSON><PERSON><PERSON>", "insert": "<PERSON>sip<PERSON>", "pageManipulation": "<PERSON><PERSON><PERSON><PERSON>", "replace": "Gantikan", "replacePage": "Gantikan <PERSON>", "modal": "modal", "isOpen": "ia terbuka", "setDestination": "Tetapkan Destinasi", "showLess": "kurang <PERSON>", "showMore": "... lagi", "chooseFile": "<PERSON><PERSON><PERSON> fail", "changeDate": "<PERSON><PERSON> ta<PERSON>h", "browse": "<PERSON><PERSON>k im<PERSON>", "selectYourOption": "<PERSON><PERSON><PERSON> pilihan anda", "open": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "moveToTop": "Bergerak ke atas", "moveToBottom": "<PERSON><PERSON> ke bawah", "movePageToTop": "<PERSON><PERSON><PERSON> ke Atas", "movePageToBottom": "<PERSON><PERSON><PERSON> ke Bawah", "moveUp": "Pindah ke atas", "moveDown": "Pindah ke bawah", "moveLeft": "Bergerak ke Kiri", "moveRight": "<PERSON><PERSON>", "backToMenu": "Ke<PERSON><PERSON> ke Menu", "redactPages": "Sunting halaman", "playAudio": "Mainkan audio", "pauseAudio": "jeda audio", "selectAll": "<PERSON><PERSON><PERSON> se<PERSON>a", "unselect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addMark": "Tambah Mark", "viewFile": "Lihat fail", "multiReplyAnnotations": "<PERSON>las kepada anotasi yang dipilih ({{count}})", "comparePages": "<PERSON><PERSON><PERSON> halaman", "startComparison": "<PERSON><PERSON><PERSON>", "showComparison": "<PERSON>n<PERSON><PERSON><PERSON>", "highlightChanges": "<PERSON><PERSON><PERSON>", "back": "belakang", "clearSignature": "Tandatangan yang jelas", "clearInitial": "Kosong<PERSON> awalan", "readOnlySignature": "Tandatangan baca sahaja tidak boleh dipadamkan", "newDocument": "<PERSON><PERSON><PERSON>", "sideBySideView": "Pandangan Sebelah", "pageNumberInput": "Input nombor halaman", "addNewColor": "Tambah Warna Baru", "deleteColor": "<PERSON><PERSON><PERSON> ya<PERSON>", "copySelectedColor": "<PERSON><PERSON> ya<PERSON>", "showMoreColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showLessColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fromCustomColorPicker": "<PERSON><PERSON><PERSON>", "newSpreadsheetDocument": "<PERSON><PERSON><PERSON>", "resetDefault": "Tetapkan semula kepada lalai"}, "annotation": {"areaMeasurement": "<PERSON><PERSON>an", "arc": "Ark<PERSON>", "arcMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "arrow": "anak panah", "callout": "<PERSON><PERSON><PERSON> ciri", "crop": "<PERSON><PERSON>", "caret": "kekurangan", "dateFreeText": "<PERSON><PERSON><PERSON>", "formFillCheckmark": "<PERSON><PERSON><PERSON>", "formFillCross": "Menyeberang", "distanceMeasurement": "Jarak", "rectangularAreaMeasurement": "<PERSON><PERSON><PERSON>", "ellipseMeasurement": "<PERSON><PERSON><PERSON>", "countMeasurement": "<PERSON>", "ellipse": "Ellipse", "eraser": "Pemadam", "fileattachment": "Lam<PERSON><PERSON>", "freehand": "<PERSON><PERSON>", "freeHandHighlight": "<PERSON><PERSON><PERSON>", "freetext": "<PERSON><PERSON>", "markInsertText": "Sisipkan Teks", "markReplaceText": "Gantikan Teks", "highlight": "Serlahkan", "image": "<PERSON><PERSON><PERSON>", "line": "<PERSON><PERSON>", "perimeterMeasurement": "Perimeter", "polygon": "Poligon", "polygonCloud": "awan", "polyline": "Polyline", "rectangle": "segi empat tepat", "redact": "sunting", "formFillDot": "titik", "signature": "Tandatangan", "snipping": "Alat Pemotongan", "squiggly": "Dengan be<PERSON>kuk-lekuk", "stamp": "setem", "stickyNote": "Catatan", "strikeout": "Strikeout", "underline": "<PERSON><PERSON> bawah", "custom": "Ada<PERSON>", "rubberStamp": "<PERSON><PERSON>ah", "note": "Catatan", "textField": "<PERSON><PERSON>", "signatureFormField": "Medan <PERSON>", "checkBoxFormField": "Medan Kotak semak", "radioButtonFormField": "Medan Butang Radio", "listBoxFormField": "Medan Kotak Senarai", "comboBoxFormField": "Padang Kotak Kombo", "link": "<PERSON><PERSON><PERSON>", "other": "Lain-lain", "3D": "3D", "sound": "<PERSON><PERSON><PERSON>", "changeView": "<PERSON><PERSON>", "newImage": "<PERSON><PERSON><PERSON>", "defaultCustomStampTitle": "<PERSON><PERSON>"}, "rubberStamp": {"Approved": "Dilulus<PERSON>", "AsIs": "As Is", "Completed": "Se<PERSON><PERSON>", "Confidential": "Sulit", "Departmental": "Jabatan", "Draft": "Draf", "Experimental": "<PERSON><PERSON><PERSON><PERSON>", "Expired": "<PERSON><PERSON> tempoh", "Final": "<PERSON><PERSON><PERSON>", "ForComment": "Untuk Komen", "ForPublicRelease": "Untuk <PERSON>", "InformationOnly": "<PERSON><PERSON><PERSON><PERSON>", "NotApproved": "Tidak diterima", "NotForPublicRelease": "<PERSON><PERSON>n <PERSON>", "PreliminaryResults": "Keputusan <PERSON>", "Sold": "<PERSON><PERSON><PERSON><PERSON>", "TopSecret": "<PERSON><PERSON><PERSON> su<PERSON>", "Void": "batal", "SHSignHere": "Tandatangan di sini", "SHWitness": "saksi", "SHInitialHere": "Inisial Di Sini", "SHAccepted": "Diterima", "SBRejected": "<PERSON><PERSON><PERSON>"}, "component": {"attachmentPanel": "Lam<PERSON>ran", "leftPanel": "<PERSON> Kiri", "toolsHeader": "Alatan", "searchOverlay": "<PERSON><PERSON>", "searchPanel": "<PERSON><PERSON>", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "Komen", "indexPanel": "Panel Indeks", "outlinePanel": "<PERSON><PERSON> besar", "outlinesPanel": "<PERSON><PERSON> besar", "newOutlineTitle": "Tajuk Rangka Baharu", "outlineTitle": "<PERSON><PERSON>k <PERSON>", "destination": "<PERSON><PERSON><PERSON>", "bookmarkPanel": "<PERSON><PERSON> buku", "bookmarksPanel": "<PERSON><PERSON> buku", "bookmarkTitle": "<PERSON><PERSON><PERSON>", "bookmarkPage": "<PERSON><PERSON>", "signaturePanel": "Tandatangan", "layersPanel": "<PERSON><PERSON><PERSON>", "thumbnailsPanel": "Gambar kecil", "toolsButton": "Alatan", "redaction": "<PERSON><PERSON><PERSON>", "viewControls": "<PERSON><PERSON>", "pageControls": "<PERSON><PERSON><PERSON>", "calibration": "<PERSON><PERSON><PERSON><PERSON>", "zoomOverlay": "<PERSON><PERSON><PERSON>", "textPopup": "Teks Pop Timbul", "createStampButton": "<PERSON><PERSON><PERSON>", "filter": "Penap<PERSON>", "multiSelectButton": "Pelbagai Pilihan", "pageReplaceModalTitle": "Gantikan <PERSON>", "files": "Fail", "file": "Fail", "editText": "<PERSON>", "redactionPanel": "Panel Redaksi", "tabLabel": "Tab", "noteGroupSection": {"open": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "comparePanel": "Bandingkan Panel", "watermarkPanel": "Panel Tera Air", "mainMenu": "<PERSON><PERSON>"}, "message": {"showMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showLess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toolsOverlayNoPresets": "Tiada Pratetap", "badDocument": "Gagal memuatkan dokumen. Dokumen itu sama ada rosak atau tidak sah.", "customPrintPlaceholder": "cth. 3, 4-10", "encryptedAttemptsExceeded": "Gagal memuatkan dokumen yang disulitkan. Terlalu banyak percubaan.", "encryptedUserCancelled": "Gagal memuatkan dokumen yang disulitkan. Kemasukan kata laluan dibatalkan.", "enterPassword": "Dokumen ini dilindungi kata laluan. <PERSON>la masukkan kata laluan", "incorrectPassword": "<PERSON><PERSON>an salah, percu<PERSON>an yang tinggal: {{ remainingAttempts }}", "noAnnotations": "<PERSON>la membuat anotasi untuk meninggalkan ulasan.", "noAnnotationsReadOnly": "Dokumen ini tidak mempunyai anotasi.", "noAnnotationsFilter": "<PERSON>la membuat anotasi dan penapis akan dipaparkan di sini.", "noBookmarks": "<PERSON><PERSON><PERSON> penanda halaman tersedia", "noOutlines": "Dokumen ini tidak mempunyai garis besar.", "noAttachments": "Dokumen ini tidak mempunyai lampiran.", "noResults": "Tiada keputusan dijumpai.", "numResultsFound": "keputusan dijumpai", "loadError": "<PERSON><PERSON>", "notSupported": "Jenis fail itu tidak disokong.", "passwordRequired": "kata laluan diperlukan", "enterPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kata laluan", "preparingToPrint": "Bersedia untuk mencetak...", "annotationReplyCount": "{{count}} <PERSON><PERSON>", "annotationReplyCount_plural": "{{count}} <PERSON><PERSON><PERSON>", "printTotalPageCount": "<PERSON><PERSON><PERSON>: {{count}} halaman", "printTotalPageCount_plural": "<PERSON><PERSON><PERSON>: {{count}} halaman", "processing": "Memproses...", "searching": "<PERSON><PERSON><PERSON>...", "searchCommentsPlaceholder": "<PERSON><PERSON> komen", "searchDocumentPlaceholder": "Cari dokumen", "clearSearchResults": "Kosong<PERSON> hasil carian", "searchResultsCleared": "<PERSON><PERSON><PERSON>", "searchSettingsPlaceholder": "Tetapan carian", "searchSuggestionsPlaceholder": "Cadangan carian", "signHere": "Tandatangan di sini", "insertTextHere": "Sisipkan teks di sini", "imageSignatureAcceptedFileTypes": "<PERSON>ya {{acceptedFileTypes}} diterima", "signatureRequired": "Tandatangan dan parap diperlukan untuk meneruskan", "enterMeasurement": "Masukkan ukuran antara dua titik", "errorEnterMeasurement": "Nombor yang anda masukkan tidak sah, anda boleh memasukkan nilai seperti 7.5 atau 7 1/2", "linkURLorPage": "Pautan URL atau Halaman", "warning": "<PERSON><PERSON>", "svgMalicious": "Skrip SVG diabaikan untuk keselamatan", "doNotShowAgain": "<PERSON><PERSON> tun<PERSON>kkan ini kepada saya lagi", "doNotAskAgain": "<PERSON>an tanya lagi", "enterReplacementText": "Masukkan teks yang ingin anda gantikan", "sort": "<PERSON><PERSON>", "sortBy": "<PERSON><PERSON>", "emptyCustomStampInput": "Teks setem tidak boleh kosong", "unpostedComment": "Komen Tidak Disiarkan", "lockedLayer": "<PERSON><PERSON><PERSON>", "layerVisibililtyNoChange": "Ke<PERSON>lihatan lapisan tidak boleh diubah", "noLayers": "Dokumen ini tidak mempunyai lapisan.", "noSignatureFields": "Dokumen ini tidak mempunyai medan tandatangan.", "untitled": "Tidak bertajuk", "selectHowToLoadFile": "<PERSON><PERSON>h cara memuatkan dokumen anda", "openFileByUrl": "Buka fail mengikut URL:", "enterUrlHere": "Masukkan URL di sini", "openLocalFile": "Buka fail tempatan:", "selectFile": "<PERSON><PERSON><PERSON> fail", "selectPageToReplace": "<PERSON><PERSON><PERSON> halaman dalam dokumen yang anda ingin gantikan.", "embeddedFiles": "<PERSON><PERSON>", "pageNum": "<PERSON><PERSON>", "viewBookmark": "<PERSON><PERSON> pada <PERSON>", "error": "ralat", "errorPageNumber": "Nombor halaman tidak sah. <PERSON> ialah", "errorBlankPageNumber": "Sila nyatakan nombor halaman", "errorLoadingDocument": "Terdapat masalah membaca dokumen ini dan beberapa halaman mungkin tidak dipaparkan. Ini menunjukkan dokumen itu mungkin rosak. <PERSON><PERSON><PERSON> kiraan halaman ialah {{totalPageCount}} dan nombor halaman yang dipaparkan ialah {{displayedPageCount}}.", "noRevisions": "Dokumen ini tidak mempunyai semakan."}, "option": {"type": {"caret": "kekurangan", "custom": "Ada<PERSON>", "ellipse": "Ellipse", "fileattachment": "Lam<PERSON><PERSON>", "freehand": "<PERSON><PERSON>", "callout": "<PERSON><PERSON><PERSON> ciri", "freetext": "<PERSON><PERSON>", "line": "<PERSON><PERSON>", "polygon": "Poligon", "polyline": "Polyline", "rectangle": "segi empat tepat", "redact": "sunting", "signature": "Tandatangan", "stamp": "setem", "stickyNote": "<PERSON><PERSON>", "highlight": "Serlahkan", "strikeout": "Strikeout", "underline": "<PERSON><PERSON> bawah", "squiggly": "Dengan be<PERSON>kuk-lekuk", "3D": "3D", "other": "Lain-lain", "initials": "Inisial", "saved": "Disimpan"}, "notesOrder": {"dropdownLabel": "<PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON>", "status": "Status", "author": "Pengarang", "type": "taip", "color": "<PERSON><PERSON>", "createdDate": "<PERSON><PERSON><PERSON>", "modifiedDate": "<PERSON><PERSON><PERSON>"}, "toolbarGroup": {"dropdownLabel": "Kumpulan Bar Alat", "flyoutLabel": "reben", "toolbarGroup-View": "Lihat", "toolbarGroup-Annotate": "<PERSON><PERSON><PERSON>", "toolbarGroup-Shapes": "<PERSON><PERSON>", "toolbarGroup-Insert": "<PERSON>sip<PERSON>", "toolbarGroup-Measure": "ukur", "toolbarGroup-Edit": "Sunting", "toolbarGroup-EditText": "<PERSON>", "toolbarGroup-FillAndSign": "<PERSON><PERSON> dan <PERSON>", "toolbarGroup-Forms": "<PERSON><PERSON>", "toolbarGroup-Redact": "sunting", "toolbarGroup-oe-Home": "<PERSON><PERSON><PERSON>", "toolbarGroup-oe-Insert": "<PERSON>sip<PERSON>", "toolbarGroup-oe-Layout": "<PERSON><PERSON>", "toolbarGroup-oe-Review": "Semakan"}, "annotationColor": {"StrokeColor": "Strok", "FillColor": "isi", "TextColor": "Teks"}, "colorPalette": {"colorLabel": "<PERSON><PERSON>"}, "colorPalettePicker": {"addColor": "Tambah warna baru", "selectColor": "<PERSON><PERSON><PERSON> warna"}, "displayMode": {"layout": "<PERSON><PERSON>", "pageTransition": "<PERSON><PERSON><PERSON>"}, "documentControls": {"selectTooltip": "<PERSON><PERSON><PERSON> berbilang halaman", "closeTooltip": "<PERSON><PERSON><PERSON> pilihan berbilang"}, "bookmarkOutlineControls": {"edit": "Sunting", "done": "Se<PERSON><PERSON>", "reorder": "<PERSON><PERSON> semula"}, "layout": {"cover": "<PERSON><PERSON>", "double": "<PERSON><PERSON>", "single": "<PERSON><PERSON>"}, "mathSymbols": "simbol matematik", "notesPanel": {"separator": {"today": "<PERSON> ini", "yesterday": "Semalam", "unknown": "Tidak diketahui"}, "noteContent": {"noName": "(tiada nama)", "noDate": "(tiada tarikh)"}, "toggleMultiSelect": "Togol Berbilang Pilih untuk Anotasi"}, "pageTransition": {"continuous": "<PERSON><PERSON>", "default": "Halaman demi <PERSON>", "reader": "Pembaca"}, "print": {"all": "<PERSON><PERSON><PERSON>", "current": "Muka surat ini", "pages": "Halaman untuk dicetak", "specifyPages": "<PERSON><PERSON><PERSON>", "view": "Pandangan Semasa", "pageQuality": "<PERSON><PERSON><PERSON>", "qualityNormal": "<PERSON><PERSON><PERSON><PERSON>", "qualityHigh": "tinggi", "includeAnnotations": "Sertakan anota<PERSON>", "includeComments": "<PERSON><PERSON><PERSON>", "printSettings": "Tetapan <PERSON>", "printGrayscale": "Cetak Skala Kelabu", "printCurrentDisabled": "<PERSON><PERSON> semasa hanya tersedia apabila melihat satu halaman."}, "printInfo": {"author": "Pengarang", "subject": "Subjek", "date": "<PERSON><PERSON><PERSON>"}, "redaction": {"markForRedaction": "<PERSON>dakan untuk redaksi"}, "searchPanel": {"caseSensitive": "<PERSON><PERSON><PERSON><PERSON> huruf besar", "wholeWordOnly": "<PERSON><PERSON><PERSON><PERSON>", "wildcard": "<PERSON><PERSON> <PERSON>", "replace": "Gantikan", "replaceAll": "Gantikan Semua", "replaceText": "Gantikan Teks", "confirmMessageReplaceAll": "<PERSON><PERSON>h anda pasti mahu menggantikan semua teks?", "confirmMessageReplaceOne": "<PERSON><PERSON>h anda pasti mahu menggantikan teks ini?", "moreOptions": "<PERSON><PERSON>h banyak pilihan", "lessOptions": "<PERSON><PERSON> p<PERSON>han", "confirm": "sahkan"}, "toolsOverlay": {"currentStamp": "<PERSON><PERSON>", "currentSignature": "Tandatangan Se<PERSON>a", "signatureAltText": "Tandatangan"}, "stampOverlay": {"addStamp": "Tambah Setem Baharu"}, "signatureOverlay": {"addSignatureOrInitials": "Tandatangan/Inisial Baharu", "addSignature": "Tandatangan Baru"}, "signatureModal": {"modalName": "<PERSON><PERSON><PERSON> Baharu", "dragAndDrop": "Seret & Lepaskan imej anda di sini", "or": "Ataupun", "pickImage": "<PERSON><PERSON><PERSON>", "selectImage": "<PERSON><PERSON><PERSON> imej anda di sini", "typeSignature": "<PERSON><PERSON>*", "typeInitial": "<PERSON><PERSON>*", "drawSignature": "<PERSON><PERSON>*", "drawInitial": "<PERSON><PERSON>*", "imageSignature": "Tandatangan Imej", "imageInitial": "<PERSON><PERSON><PERSON>", "pickInitialsFile": "<PERSON><PERSON><PERSON>", "noSignatures": "Pada masa ini tiada tandatangan yang disimpan.", "fontStyle": "gaya fon", "textSignature": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "pageReplacementModal": {"yourFiles": "<PERSON><PERSON>", "chooseFile": "<PERSON><PERSON><PERSON> fail", "localFile": "<PERSON><PERSON>", "pageReplaceInputLabel": "Gantikan <PERSON>", "pageReplaceInputFromSource": "<PERSON><PERSON> halaman", "warning": {"title": "<PERSON><PERSON><PERSON> dari halaman ganti?", "message": "<PERSON><PERSON><PERSON> akan membatalkan semua pilihan yang telah anda buat setakat ini. <PERSON><PERSON>h anda pasti anda masih mahu keluar?"}}, "filterAnnotModal": {"color": "<PERSON><PERSON>", "includeReplies": "<PERSON><PERSON><PERSON>", "filters": "Penap<PERSON>", "user": "pengguna", "type": "taip", "status": "Status", "filterSettings": "Tetapan <PERSON>", "filterDocument": "<PERSON><PERSON> dokumen dan panel ulasan"}, "state": {"accepted": "Diterima", "rejected": "<PERSON><PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "cancelled": "Di<PERSON><PERSON><PERSON>", "set": "Tetapkan status:", "setBy": "ditetapkan oleh", "none": "tiada", "marked": "<PERSON><PERSON><PERSON>", "unmarked": "Tidak bertanda"}, "measurementOverlay": {"scale": "Nisbah Skala", "angle": "sudut", "distance": "Jarak", "perimeter": "Perimeter", "area": "<PERSON><PERSON>an", "distanceMeasurement": "Pengukuran Jarak", "perimeterMeasurement": "Pengukuran Perimeter", "arcMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "areaMeasurement": "<PERSON><PERSON><PERSON><PERSON>", "countMeasurement": "<PERSON>", "radius": "<PERSON><PERSON><PERSON>", "count": "<PERSON>", "length": "Panjang", "xAxis": "Paksi X", "yAxis": "<PERSON><PERSON>"}, "freeTextOption": {"autoSizeFont": "Skalakan saiz fon secara dinamik"}, "measurementOption": {"scale": "<PERSON><PERSON><PERSON>", "selectScale": "<PERSON><PERSON><PERSON>", "selectScaleDropdown": "Pilih Dropdown Skala"}, "measurement": {"scaleModal": {"calibrate": "<PERSON><PERSON><PERSON>", "custom": "Ada<PERSON>", "fractionalUnits": "Unit Pecahan", "precision": "Ketepatan", "preset": "Pratetap", "paperUnits": "Unit Kertas", "displayUnits": "Unit Paparan", "fractionUnitsTooltip": "Unit pecahan hanya digunakan untuk in dan ft-in", "incorrectSyntax": "Sintaks salah", "units": "Unit"}, "scaleOverlay": {"addNewScale": "Tambah Skala Baharu", "selectTwoPoints": "<PERSON><PERSON>h dua titik dimensi yang diketahui untuk ditentukur", "inputKnowDimension": "Masukkan dimensi dan unit yang diketahui untuk ditentukur", "multipleScales": "Berbilang Skala"}, "deleteScaleModal": {"deleteScale": "Pa<PERSON>", "scaleIsOn-delete-info": "Skala ini sedang digunakan pada", "page-delete-info": "muka surat", "appliedTo-delete-info": "dan digunakan untuk", "measurement": "<PERSON><PERSON><PERSON><PERSON>", "measurements": "ukuran", "deletionIs": "<PERSON><PERSON><PERSON><PERSON>", "irreversible": "tak boleh balik", "willDeleteMeasurement": "dan akan memadamkan ukuran yang berkaitan.", "confirmDelete": "<PERSON><PERSON>h anda pasti mahu memadamkan skala ini?", "thisCantBeUndone": " Ini tidak boleh dibuat asal.", "ifChangeScale": "<PERSON><PERSON> anda menukar skala untuk ukuran atau alat yang dipilih, skala", "notUsedWillDelete": " tidak akan digunakan lagi oleh sebarang ukuran atau alat dan akan dipadamkan. Pemadaman tidak dapat dipulihkan.", "ifToContinue": "<PERSON><PERSON>h anda pasti mahu men<PERSON>?"}}, "contentEdit": {"deletionModal": {"title": "<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON>h anda pasti mahu memadamkan kandungan yang dipilih? Ini tidak boleh dibuat asal."}, "digitalSign": {"title": "Dokumen Termeterai Amaran", "message": "Dokumen ini telah ditandatangani dan tidak boleh diubah atau diubah."}}, "stylePopup": {"textStyle": "<PERSON><PERSON>", "colors": "<PERSON><PERSON>", "invalidFontSize": "<PERSON>z fon mestilah dalam julat berikut", "labelText": "Teks Label", "labelTextPlaceholder": "Tambahkan teks label"}, "styleOption": {"style": "<PERSON><PERSON>", "solid": "Pa<PERSON>t", "cloudy": "<PERSON><PERSON><PERSON>"}, "slider": {"opacity": "Kelegapan", "thickness": "Strok", "text": "Saiz Teks"}, "shared": {"page": "<PERSON><PERSON>", "precision": "Ketepatan", "enableSnapping": "<PERSON><PERSON><PERSON> menjentik untuk alat ukuran"}, "watermark": {"title": "Tera air", "addWatermark": "Tambah Tera Air", "size": "Saiz", "location": "Pilih lokasi untuk mengedit tera air", "text": "Teks", "style": "<PERSON><PERSON>", "resetAllSettings": "Tetapkan <PERSON>", "font": "buat", "addNew": "Tambah baru", "locations": {"center": "Pusat", "topLeft": "<PERSON><PERSON>", "topRight": "<PERSON><PERSON>", "topCenter": "Pusat Atas", "bottomLeft": "<PERSON><PERSON><PERSON><PERSON> bawah kiri", "bottomRight": "<PERSON><PERSON><PERSON>", "bottomCenter": "P<PERSON><PERSON>"}}, "thumbnailPanel": {"delete": "Padam", "rotateClockwise": "ikut arah jam", "rotateCounterClockwise": "lawan jam", "rotatePageClockwise": "Putar Halaman Mengikut Jam", "rotatePageCounterClockwise": "<PERSON><PERSON> lawan jam", "moreOptions": "<PERSON><PERSON>h banyak pilihan", "moreOptionsMenu": "<PERSON><PERSON>", "enterPageNumbers": "Masukkan nombor halaman untuk dipilih", "multiSelectPages": "Halaman Berbilang <PERSON>", "multiSelectPagesExample": "cth. 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "Pi<PERSON><PERSON><PERSON> halaman"}, "richText": {"bold": "berani", "italic": "Italic", "underline": "<PERSON><PERSON> bawah", "strikeout": "Strikeout", "alignLeft": "Teks dijajarkan ke kiri", "alignRight": "Teks dijajarkan ke kanan", "alignCenter": "Pusat pen<PERSON>jaran teks", "justifyCenter": "Pusat justify teks", "alignTop": "Jajarkan atas", "alignMiddle": "Jajarkan tengah", "alignBottom": "<PERSON><PERSON><PERSON><PERSON> bah<PERSON>an bawah"}, "customStampModal": {"modalName": "<PERSON><PERSON><PERSON>", "stampText": "<PERSON><PERSON> setem", "timestampText": "Teks cap masa", "Username": "<PERSON><PERSON>", "Date": "<PERSON><PERSON><PERSON>", "Time": "<PERSON><PERSON>", "fontStyle": "gaya fon", "dateFormat": "Format tarikh", "month": "bulan", "day": "<PERSON>", "year": "tahun", "hour": "Jam", "minute": "minit", "second": "Kedua", "textColor": "Warna teks", "backgroundColor": "<PERSON><PERSON> latar belakang", "dateToolTipLabel": "Maklumat lanjut tentang format tarikh", "previewCustomStamp": "Pratonton bagi"}, "pageRedactModal": {"addMark": "Tambah Mark", "pageSelection": "<PERSON><PERSON><PERSON><PERSON>", "current": "Muka surat ini", "specify": "<PERSON><PERSON><PERSON>", "odd": "Muka surat ganjil sahaja", "even": "Muka surat genap sahaja"}, "lineStyleOptions": {"title": "<PERSON><PERSON>"}, "settings": {"settings": "tetapan", "searchSettings": "Tetapan <PERSON>", "general": "<PERSON><PERSON>", "language": "Bahasa", "theme": "<PERSON><PERSON>", "darkMode": "Mod gelap", "lightMode": "<PERSON><PERSON> ca<PERSON>a", "advancedSetting": "Tetapan <PERSON>", "viewing": "Melihat", "disableFadePageNavigationComponent": "Lumpuhkan Komponen Navigasi Halaman <PERSON>uda<PERSON>", "disableFadePageNavigationComponentDesc": "<PERSON><PERSON><PERSON> pastikan Kompo<PERSON> Navigasi <PERSON>aman pada skrin. Tingkah laku lalai adalah untuk memudarkannya selepas tempoh tertentu tidak aktif.", "enabledFormFieldHighlighting": "<PERSON><PERSON><PERSON>", "enabledFormFieldHighlightingDesc": "<PERSON><PERSON><PERSON>an medan borang dalam dokumen PDF. <PERSON><PERSON><PERSON> secara lalai.", "disableNativeScrolling": "Lumpuhkan Tatal As<PERSON>", "disableNativeScrollingDesc": "Lumpuhkan tingkah laku menatal peranti mudah alih asli jika ia telah didayakan sebelum ini. Ambil perhatian bahawa tingkah laku menatal peranti mudah alih asli dimatikan secara lalai.", "annotations": "<PERSON><PERSON><PERSON>", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Lumpuhkan Kemas Kini Gaya Lalai Alat Daripada Pop Timbul Anotasi", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Melumpuhkan penyegerakan kemas kini gaya anotasi kepada alat berkaitan yang mencipta anotasi. <PERSON><PERSON> jika gaya anotasi ditukar, gaya lalai alat tidak akan dikemas kini.", "notesPanel": "Panel Nota", "disableNoteSubmissionWithEnter": "<PERSON><PERSON><PERSON><PERSON> Nota Dengan Enter", "disableNoteSubmissionWithEnterDesc": "Lumpuhkan keupayaan untuk menyerahkan nota dengan hanya menekan Enter jika ia telah didayakan sebelum ini. Ini akan mengembalikan penyerahan nota kepada lalai iaitu Ctrl/Cmd + Enter.", "disableAutoExpandCommentThread": "Lumpuhkan Auto Kembangkan Benang Ulasan", "disableAutoExpandCommentThreadDesc": "Melumpuhkan pengembangan automatik semua urutan ulasan dalam Panel Nota.", "disableReplyCollapse": "Lumpuh<PERSON>", "disableReplyCollapseDesc": "<PERSON><PERSON><PERSON><PERSON> keruntuhan balasan dalam <PERSON> Nota.", "disableTextCollapse": "Lumpuhkan Runtuhkan Teks", "disableTextCollapseDesc": "Melumpuhkan keruntuhan teks anotasi dalam Panel Nota.", "search": "<PERSON><PERSON>", "disableClearSearchOnPanelClose": "Lumpuhkan Carian Kosong Pada Tutup Panel", "disableClearSearchOnPanelCloseDesc": "Lumpuhkan mengosongkan hasil carian apabila pengguna menutup panel carian. <PERSON><PERSON><PERSON><PERSON> dilump<PERSON>, hasil carian disimpan walaupun pengguna menutup dan membuka semula panel carian. <PERSON><PERSON>, peranti mudah alih tidak pernah mengosongkan hasil carian walaupun tetapan ini didayakan. Ini kerana panel perlu ditutup untuk melihat hasil carian pada dokumen.", "pageManipulation": "<PERSON><PERSON><PERSON><PERSON>", "disablePageDeletionConfirmationModal": "Lumpuhkan Modal Pengesahan <PERSON>", "disablePageDeletionConfirmationModalDesc": "Lumpuhkan modal pengesahan apabila memadam halaman daripada paparan lakaran kecil", "disableMultiselect": "Lumpuhkan Multiselect", "disableMultiselectDesc": "Lumpuhkan berbilang pilihan dalam panel lakaran kecil kiri", "miscellaneous": "<PERSON>am-macam", "keyboardShortcut": "Pintasan papan kekunci", "command": "<PERSON><PERSON><PERSON>", "description": "Penerangan", "action": "<PERSON><PERSON><PERSON>", "rotateDocumentClockwise": "Putar dokumen mengikut arah jam", "rotateDocumentCounterclockwise": "Putar dokumen mengikut lawan jam", "copyText": "<PERSON>in teks atau anotasi yang dipilih", "pasteText": "Tampal teks atau anotasi", "undoChange": "<PERSON><PERSON><PERSON> asal per<PERSON>han anotasi", "redoChange": "<PERSON><PERSON>t semula per<PERSON>han anotasi", "openFile": "<PERSON><PERSON> pemilih fail", "openSearch": "<PERSON><PERSON> tin<PERSON> carian", "zoomOptions": "<PERSON><PERSON><PERSON>", "zoomIn": "Mengezum masuk", "zoomOut": "<PERSON><PERSON> keluar", "setHeaderFocus": "Menetapkan fokus kepada pengepala", "fitScreenWidth": "Muatkan dokumen dengan lebar skrin dalam skrin kecil (< 640px), jika tidak muatkannya dengan saiz asalnya", "print": "Cetak", "bookmarkOpenPanel": "Tandai halaman dengan cepat dan buka panel penanda halaman", "goToPreviousPage": "<PERSON>gi ke halaman sebelumnya", "goToNextPage": "<PERSON>gi ke halaman set<PERSON>", "goToPreviousPageArrowUp": "<PERSON>gi ke halaman sebelumnya dalam mod susun atur tunggal (ArrowUp)", "goToNextPageArrowDown": "<PERSON>gi ke halaman seterusnya dalam mod susun atur tunggal (ArrowDown)", "holdSwitchPan": "<PERSON>han untuk beralih ke mod Pan dan lepaskan untuk kembali ke alat sebelumnya", "selectAnnotationEdit": "Pilih alat AnnotationEdit", "selectPan": "<PERSON><PERSON><PERSON> al<PERSON>", "selectCreateArrowTool": "Pilih alat AnnotationCreateArrow", "selectCreateCalloutTool": "Pilih alat AnnotationCreateCallout", "selectEraserTool": "<PERSON><PERSON>h alat AnnotationEraser", "selectCreateFreeHandTool": "Pilih alat AnnotationCreateFreeHand", "selectCreateStampTool": "Pilih alat AnnotationCreateStamp", "selectCreateLineTool": "Pilih alat AnnotationCreateLine", "selectCreateStickyTool": "Pilih alat AnnotationCreateSticky", "selectCreateEllipseTool": "Pilih alat AnnotationCreateEllipse", "selectCreateRectangleTool": "Pilih alat AnnotationCreateRectangle", "selectCreateRubberStampTool": "Pilih alat AnnotationCreateRubberStamp", "selectCreateFreeTextTool": "Pilih alat AnnotationCreateFreeText", "openSignatureModal": "Buka modal tandatangan atau tindanan", "selectCreateTextSquigglyTool": "Pilih alat AnnotationCreateTextSquiggly", "selectCreateTextHighlightTool": "Pilih alat AnnotationCreateTextHighlight", "selectCreateTextStrikeoutTool": "Pilih alat AnnotationCreateTextStrikeout", "selectCreateTextUnderlineTool": "Pilih alat AnnotationCreateTextUnderline", "editKeyboardShorcut": "<PERSON> <PERSON><PERSON><PERSON>", "setShortcut": "Tetapkan Pintasan", "editShortcut": "<PERSON>", "shortcutAlreadyExists": "Pintasan papan kekunci di atas sudah wujud.", "close": "Tutup petua alat"}, "cellBorderStyle": {"none": "tiada", "solid": "Pa<PERSON>t", "dashed": "putus-putus", "dotted": "<PERSON><PERSON><PERSON>"}}, "warning": {"deletePage": {"deleteTitle": "<PERSON><PERSON>", "deleteMessage": "<PERSON><PERSON><PERSON> anda pasti mahu memadamkan halaman yang dipilih. Ini tidak boleh dibuat asal.", "deleteLastPageMessage": "<PERSON>a tidak boleh memadam semua halaman dalam dokumen."}, "extractPage": {"title": "Ekstrak Halaman", "message": "<PERSON><PERSON>h anda pasti mahu mengekstrak halaman yang dipilih?", "confirmBtn": "Ekstrak", "secondaryBtn": "Ekstrak dan <PERSON>"}, "redaction": {"applyTile": "<PERSON><PERSON><PERSON>", "applyMessage": "Tindakan ini akan mengalih keluar semua item yang dipilih untuk redaksi secara kekal. Ia tidak boleh dibuat asal."}, "deleteBookmark": {"title": "<PERSON><PERSON><PERSON>?", "message": "<PERSON><PERSON>h anda pasti mahu memadamkan penanda halaman ini? Anda tidak boleh membuat asal tindakan ini."}, "deleteOutline": {"title": "<PERSON><PERSON><PERSON>?", "message": "<PERSON><PERSON><PERSON> anda pasti mahu memadamkan garis besar ini?\n\nMemadamkan garis besar yang mempunyai garis besar bersarang akan menyebabkan keseluruhan struktur dalaman dipadamkan dan, jika perlu, perlu dibuat semula."}, "selectPage": {"selectTitle": "<PERSON><PERSON><PERSON>", "selectMessage": "<PERSON>la pilih halaman dan cuba lagi."}, "colorPicker": {"deleteTitle": "Padam<PERSON> warna tersuai", "deleteMessage": "Padamkan warna tersuai yang dipilih? Ia akan dialih keluar daripada palet warna anda."}, "colorPalettePicker": {"deleteTitle": "Padam<PERSON> warna tersuai"}, "multiDeleteAnnotation": {"title": "<PERSON><PERSON><PERSON>?", "message": "<PERSON><PERSON><PERSON><PERSON> akan mengalih keluar semua ul<PERSON>n, balasan dan pengumpulan dan tidak boleh dibuat asal.\n\n <PERSON>kah anda pasti mahu memadamkan anotasi ini?"}, "closeFile": {"title": "Tutup tanpa memuat turun?", "message": "Terdapat perubahan yang dibuat pada dokumen ini, adakah anda pasti mahu menutupnya tanpa memuat turun kerja anda? Anda tidak boleh membuat asal tindakan ini.", "rejectDownloadButton": "<PERSON><PERSON><PERSON> tanpa muat turun"}, "connectToURL": {"title": "<PERSON><PERSON>", "message": "Dokumen ini cuba disambungkan ke:\n\n{{- uri}}\n\n <PERSON><PERSON> anda mempercayai dokumen ini, klik Sahkan untuk membukanya."}, "sheetTabRenameIssueOne": {"title": "<PERSON>", "message": "<PERSON>a he<PERSON>an ini sudah wujud. <PERSON>la masukkan nama lain."}, "sheetTabRenameIssueTwo": {"title": "<PERSON>", "message": "<PERSON>a he<PERSON>an ini tidak boleh kosong."}, "sheetTabDeleteMessage": {"title": "<PERSON>", "message": "<PERSON><PERSON>h anda pasti mahu memadamkan helaian ini?"}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(The)", "ellipse": "(O)", "eraser": "(DAN)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(saya)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(Esc)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(KEPADA)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Shift + Seret", "zoom3D": "Shift + Tatal"}, "tool": {"pan": "kuali", "select": "<PERSON><PERSON><PERSON>", "selectAnOption": "<PERSON><PERSON>h satu pilihan", "Marquee": "Zum Marquee", "Link": "Pautkan URL atau Halaman", "Standard": "Standard", "Custom": "Ada<PERSON>"}, "link": {"url": "URL", "page": "<PERSON><PERSON>", "enterurl": "Masukkan URL yang anda ingin pautkan", "enterUrlAlt": "Masukkan URL", "insertLink": "<PERSON><PERSON><PERSON><PERSON>", "insertLinkOrPage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "enterpage": "Masukkan nombor halaman yang anda ingin pautkan", "urlLink": "p<PERSON>an <PERSON>"}, "Model3D": {"add3D": "Tambah objek 3D", "enterurl": "Masukkan URL objek 3D dalam format glTF", "enterurlOrLocalFile": "Masukkan URL atau muat naik objek 3D dalam format glTF", "formatError": "Hanya format glTF (.glb) disokong"}, "OpenFile": {"enterUrlOrChooseFile": "Masukkan URL atau pilih fail untuk dimuatkan ke dalam WebViewer", "enterUrl": "Masukkan URL fail", "extension": "Sambungan fail", "existingFile": "<PERSON>ail sudah dibuka", "addTab": "Tambah Tab", "newTab": "Tab baru"}, "datePicker": {"previousMonth": "<PERSON><PERSON><PERSON> lepas", "nextMonth": "<PERSON><PERSON><PERSON> depan", "months": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "<PERSON>", "3": "April", "4": "<PERSON><PERSON><PERSON>", "5": "Jun", "6": "Jul<PERSON>", "7": "Ogos", "8": "September", "9": "Oktober", "10": "November", "11": "Disember"}, "monthsShort": {"0": "Jan", "1": "Feb", "2": "<PERSON><PERSON>", "3": "Apr", "4": "<PERSON><PERSON><PERSON>", "5": "Jun", "6": "Jul", "7": "Ogos", "8": "tujuh", "9": "Okt", "10": "Nov", "11": "Dis"}, "weekdays": {"0": "<PERSON><PERSON>", "1": "<PERSON>in", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "Juma<PERSON>", "6": "Sabtu"}, "weekdaysShort": {"0": "<PERSON><PERSON><PERSON>", "1": "saya", "2": "milik awak", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "<PERSON>b"}, "today": "<PERSON> ini", "invalidDateTime": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>: Input hendaklah sepadan dengan format"}, "formField": {"indexPanel": {"formFieldList": "<PERSON><PERSON><PERSON>", "notFields": "Dokumen ini tidak mempunyai medan borang"}, "formFieldPopup": {"fieldName": "<PERSON><PERSON>", "fieldValue": "<PERSON><PERSON>", "readOnly": "Baca sahaja", "multiSelect": "Pelbagai Pilihan", "required": "<PERSON><PERSON><PERSON><PERSON>", "multiLine": "Berbilang talian", "apply": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "flags": "<PERSON><PERSON>", "fieldSize": "<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>", "properties": "<PERSON><PERSON><PERSON>", "radioGroups": "Butang radio dengan Nama Medan yang sama akan tergolong dalam kumpulan yang sama.", "nameRequired": "<PERSON><PERSON> medan diperlukan", "fieldIndicator": "Penunjuk Medan", "documentFieldIndicator": "Penunjuk Medan Dokumen", "includeFieldIndicator": "Sertakan <PERSON>", "indicatorPlaceHolders": {"TextFormField": "Sisipkan Teks Di Sini", "CheckBoxFormField": "Semak", "RadioButtonFormField": "isi", "ListBoxFormField": "<PERSON><PERSON><PERSON>", "ComboBoxFormField": "<PERSON><PERSON><PERSON>", "SignatureFormField": {"fullSignature": "Tandatangan di sini", "initialsSignature": "<PERSON><PERSON><PERSON><PERSON>"}}, "size": "Saiz", "width": "<PERSON><PERSON>", "height": "Ketinggian", "invalidField": {"duplicate": "<PERSON><PERSON> sudah wujud", "empty": "<PERSON><PERSON> tidak boleh kosong"}}, "formFieldPanel": {"SignatureFormField": "<PERSON><PERSON><PERSON>", "CheckBoxFormField": "Anotasi Medan Kotak Semak", "RadioButtonFormField": "Anotasi Medan Butang Radio", "ListBoxFormField": "<PERSON><PERSON><PERSON> Medan Kotak Senarai", "ComboBoxFormField": "Anotasi Medan Kotak Kombo", "TextFormField": "<PERSON><PERSON><PERSON>"}, "apply": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "types": {"text": "Teks", "signature": "Tandatangan", "checkbox": "Kotak semak", "radio": "Butang radio", "listbox": "Kotak senarai", "combobox": "Kotak kombo", "button": "<PERSON><PERSON>"}}, "alignmentPopup": {"alignment": "Selar<PERSON><PERSON>", "alignLeft": "<PERSON><PERSON><PERSON>", "alignHorizontalCenter": "Jajarkan <PERSON>", "alignVerticalCenter": "Jajarkan P<PERSON> Menegak", "alignRight": "<PERSON><PERSON><PERSON>", "alignTop": "<PERSON><PERSON><PERSON>", "alignBottom": "<PERSON><PERSON><PERSON>", "distribute": "<PERSON><PERSON><PERSON>", "distributeHorizontal": "<PERSON><PERSON><PERSON>", "distributeVertical": "<PERSON><PERSON><PERSON>"}, "digitalSignatureModal": {"certification": "<PERSON><PERSON><PERSON><PERSON>", "Certification": "<PERSON><PERSON><PERSON><PERSON>", "signature": "tandatangan", "Signature": "Tandatangan", "valid": "sah", "invalid": "tidak sah", "unknown": "tidak <PERSON>i", "title": "{{type}} Sifat", "header": {"documentIntegrity": "Integriti Dokumen", "identitiesTrust": "Identiti & Amanah", "generalErrors": "<PERSON><PERSON><PERSON><PERSON>", "digestStatus": "Status Digest"}, "documentPermission": {"noChangesAllowed": "{{editor}} telah menya<PERSON>kan bahawa tiada perubahan dibenarkan untuk dokumen ini", "formfillingSigningAllowed": "{{editor}} telah men<PERSON> bahawa <PERSON> dan <PERSON> dibenarkan untuk dokumen ini. <PERSON><PERSON>da per<PERSON>han lain dibenarkan.", "annotatingFormfillingSigningAllowed": "{{editor}} te<PERSON> men<PERSON> bah<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON> diben<PERSON>an untuk dokumen ini. <PERSON><PERSON>da per<PERSON>han lain dibenarkan.", "unrestricted": "{{editor}} telah menyatakan bahawa tiada sekatan untuk dokumen ini."}, "digestAlgorithm": {"preamble": "Algoritma ringkasan yang digunakan untuk menandatangani tandatangan:", "unknown": "Algoritma ringkasan yang digunakan untuk menandatangani tandatangan tidak diketahui."}, "trustVerification": {"none": "Tiada keputusan pengesahan amanah terperinci tersedia.", "current": "<PERSON><PERSON><PERSON> amanah dicuba berkenaan dengan masa semasa", "signing": "<PERSON><PERSON><PERSON> amanah dicuba berkenaan dengan masa menandatangani: {{trustVerificationTime}}", "timestamp": "Pengesahan amanah dicuba berkenaan dengan cap waktu terbenam yang selamat: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "Identiti penandatangan ialah", "valid": "sah.", "unknown": "tidak diket<PERSON>."}, "summaryBox": {"summary": "{{jenis}} digital ialah {{status}}", "signedBy": ", ditandatangani oleh {{name}}"}}, "digitalSignatureVerification": {"certifier": "<PERSON><PERSON><PERSON><PERSON>", "certified": "di<PERSON><PERSON><PERSON>", "Certified": "<PERSON><PERSON><PERSON><PERSON>", "Certification": "<PERSON><PERSON><PERSON><PERSON>", "signer": "tanda", "signed": "ditandatangani", "Signed": "Ditandatangani", "Signature": "Tandatangan", "by": "oleh", "on": "pada", "disallowedChange": "<PERSON><PERSON><PERSON>: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Medan tandatangan yang tidak ditandatangani: {{fieldName}}", "signatureProperties": "<PERSON><PERSON><PERSON>", "trustVerification": {"current": "<PERSON>sa pengesahan yang digunakan ialah masa semasa", "signing": "<PERSON><PERSON> pen<PERSON> adalah dari jam pada komputer penandatangan", "timestamp": "<PERSON><PERSON> pen<PERSON>ahan adalah daripada cap waktu selamat yang dibenamkan dalam dokumen", "verifiedTrust": "Keputusan pengesahan amanah: <PERSON><PERSON><PERSON><PERSON>", "noTrustVerification": "Tiada keputusan pengesahan amanah terperinci tersedia."}, "permissionStatus": {"noPermissionsStatus": "Tiada status kebenaran untuk melaporkan.", "permissionsVerificationDisabled": "Pengesahan kebenaran telah di<PERSON>.", "hasAllowedChanges": "Dokumen mempunyai perubahan yang diben<PERSON>an oleh tetapan kebenaran tandatangan.", "invalidatedByDisallowedChanges": "Dokumen mempunyai perubahan yang tidak dibenarkan oleh tetapan kebenaran tandatangan.", "unmodified": "Dokumen itu tidak diubah suai sejak ia dibuat", "unsupportedPermissionsFeatures": "Menghadapi ciri kebenaran yang tidak disokong."}, "trustStatus": {"trustVerified": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> dalam {{verificationType}} ber<PERSON><PERSON>.", "untrusted": "Kepercayaan tidak dapat diwujudkan.", "trustVerificationDisabled": "<PERSON><PERSON><PERSON> amanah telah di<PERSON>.", "noTrustStatus": "Tiada status amanah untuk dilaporkan."}, "digestStatus": {"digestInvalid": "Pencernaan tidak betul.", "digestVerified": "<PERSON><PERSON> betul.", "digestVerificationDisabled": "Pengesahan ringkasan telah di<PERSON>.", "weakDigestAlgorithmButDigestVerifiable": "Digest adalah betul, tetapi algoritma digest lemah dan tidak selamat.", "noDigestStatus": "Tiada status ringkasan untuk dilaporkan.", "unsupportedEncoding": "Tiada SignatureHandler yang dipasang dapat mengecam pengekodan tandatangan", "documentHasBeenAltered": "Dokumen telah diubah atau rosak sejak ia ditandatangani."}, "documentStatus": {"noError": "Tiada ralat umum untuk dilaporkan.", "corruptFile": "SignatureHandler melaporkan rasuah fail.", "unsigned": "Tandatangan itu belum lagi ditandatangani secara kriptografi.", "badByteRanges": "SignatureHandler melaporkan rasuah dalam ByteRanges dalam tandatangan digital.", "corruptCryptographicContents": "SignatureHandler melaporkan rasuah dalam Kandungan tandatangan digital."}, "signatureDetails": {"signatureDetails": "<PERSON><PERSON><PERSON>", "contactInformation": "Maklumat per<PERSON>bungan", "location": "<PERSON><PERSON>", "reason": "Sebab", "signingTime": "<PERSON><PERSON>", "noContactInformation": "Tiada maklumat hubungan diberikan", "noLocation": "Tiada lokasi disediakan", "noReason": "<PERSON><PERSON><PERSON> alasan di<PERSON>n", "noSigningTime": "<PERSON>iada masa tandatangan di<PERSON>ui"}, "panelMessages": {"noSignatureFields": "Dokumen ini tidak mempunyai medan tandatangan", "certificateDownloadError": "<PERSON><PERSON> di<PERSON>ui semasa cuba memuat turun sijil yang dipercayai", "localCertificateError": "Terdapat beberapa isu dengan membaca sijil tempatan"}, "verificationStatus": {"valid": "{{verificationType}} adalah sah.", "failed": "{{verificationType}} gagal."}}, "cropPopUp": {"title": "Halaman untuk <PERSON>kas", "allPages": "<PERSON><PERSON><PERSON>", "singlePage": "Muka surat ini", "multiPage": "<PERSON><PERSON><PERSON><PERSON>", "cropDimensions": "<PERSON><PERSON><PERSON>", "dimensionInput": {"unitOfMeasurement": "Unit", "autoTrim": "Auto-trim", "autoTrimCustom": "Ada<PERSON>"}, "cropModal": {"applyTitle": "<PERSON><PERSON><PERSON> tanaman?", "applyMessage": "Tindakan ini akan memangkas semua halaman yang dipilih yang dipilih secara kekal. Ia tidak boleh dibuat asal.", "cancelTitle": "Batalkan pemang<PERSON>an?", "cancelMessage": "<PERSON><PERSON><PERSON> anda pasti mahu membatalkan pemangkasan semua halaman yang dipilih?"}}, "snippingPopUp": {"title": "Alat Pemotongan", "clipboard": "<PERSON><PERSON> ke <PERSON>", "download": "<PERSON>at turun", "cropAndRemove": "<PERSON><PERSON><PERSON> dan <PERSON>", "snippingModal": {"applyTitle": "Terapkan pemotongan?", "applyMessage": "Tindakan ini akan memotong kawasan yang ditentukan secara kekal dan mengeluarkan halaman lain. Ia tidak boleh dikembalikan.", "cancelTitle": "Batal pemotongan?", "cancelMessage": "<PERSON><PERSON><PERSON> anda pasti mahu menghentikan pemotongan?"}}, "textEditingPanel": {"paragraph": "<PERSON><PERSON><PERSON>", "text": "Teks"}, "redactionPanel": {"noMarkedRedactions": "<PERSON><PERSON> menyunting dengan menandakan teks, kawasan, halaman atau membuat carian.", "redactionSearchPlaceholder": "Redaksi mengikut papan kekunci atau corak", "redactionCounter": "Ditanda untuk Redaksi", "clearMarked": "<PERSON><PERSON>", "redactAllMarked": "<PERSON><PERSON><PERSON>", "redactionItems": "<PERSON><PERSON>", "redactionItem": {"regionRedaction": "Penyuntingan wilayah", "fullPageRedaction": "<PERSON><PERSON><PERSON><PERSON> halaman penuh", "fullVideoFrameRedaction": "Penyuntingan Video", "audioRedaction": "Penyuntingan Audio", "fullVideoFrameAndAudioRedaction": "Penyuntingan Video dan Audio", "image": "<PERSON><PERSON><PERSON>"}, "expand": "Kembangkan", "collapse": "<PERSON><PERSON><PERSON>", "searchResults": "Keputusan Carian", "search": {"creditCards": "<PERSON><PERSON> kredit", "phoneNumbers": "Nombor telefon", "images": "<PERSON><PERSON><PERSON>", "emails": "E-mel", "pattern": "Corak", "start": "<PERSON><PERSON> membuat carian anda"}}, "wv3dPropertiesPanel": {"propertiesHeader": "Properties", "miscValuesHeader": "<PERSON><PERSON><PERSON>", "emptyPanelMessage": "<PERSON>lih elemen untuk melihat sifatnya."}, "watermarkPanel": {"textWatermark": "Tanda Air Teks", "uploadImage": "Muat Na<PERSON>", "browse": "<PERSON><PERSON><PERSON> imbas", "watermarkOptions": "<PERSON><PERSON>han <PERSON>ra Air", "watermarks": "Tera air"}, "portfolio": {"createPDFPortfolio": "Cipta Portfolio PDF", "uploadFiles": "<PERSON><PERSON>at naik fail", "uploadFolder": "Muat naik Folder", "addFiles": "Menambah fail", "addFile": "Tambah fail", "addFolder": "Tambah folder", "createFolder": "Buat Folder", "portfolioPanelTitle": "Portfolio PDF", "portfolioNewFolder": "<PERSON><PERSON> baharu", "portfolioDocumentTitle": "Tajuk dokumen", "portfolioFolderPlaceholder": "Nama folder", "portfolioFilePlaceholder": "<PERSON>a fail", "folderNameAlreadyExists": "Nama folder sudah wujud", "fileNameAlreadyExists": "<PERSON>a fail sudah wujud", "openFile": "<PERSON><PERSON><PERSON>", "fileAlreadyExists": "<PERSON><PERSON>", "fileAlreadyExistsMessage": "Fail \"{{fileName}}\" sudah wujud da<PERSON> portfolio.", "deletePortfolio": "<PERSON><PERSON><PERSON> anda pasti mahu memadamkan \"{{fileName}}\"?", "reselect": "<PERSON><PERSON><PERSON> semula"}, "languageModal": {"selectLanguage": "<PERSON><PERSON><PERSON>"}, "officeEditor": {"bold": "berani", "italic": "Italic", "underline": "<PERSON><PERSON> bawah", "textColor": "Warna teks", "leftAlign": "Jaja<PERSON> ke kiri", "centerAlign": "<PERSON><PERSON><PERSON> tengah", "rightAlign": "<PERSON><PERSON><PERSON> ke kanan", "justify": "Mewajarkan", "lineSpacing": "Jarak baris & perenggan", "lineSpacingMenu": "<PERSON><PERSON><PERSON>", "bulletList": "<PERSON><PERSON><PERSON> bertitik tumpu mata", "numberList": "<PERSON><PERSON><PERSON>", "decreaseIndent": "Kurangkan inden", "increaseIndent": "Tingkatkan inden", "nonPrintingCharacters": "<PERSON>ks<PERSON> bukan cetakan", "insertLink": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>an", "insertImage": "<PERSON><PERSON><PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON>", "table": "Jadual", "insertRowAbove": "Sisipkan Baris Di Atas", "insertRowBelow": "Sisipkan Baris Di Bawah", "insertColumnRight": "<PERSON><PERSON><PERSON><PERSON>", "insertColumnLeft": "<PERSON><PERSON><PERSON><PERSON>", "deleteRow": "Padam Baris", "deleteColumn": "<PERSON><PERSON>", "deleteTable": "<PERSON><PERSON><PERSON>", "deleted": "Di<PERSON>am<PERSON>:", "added": "Ditambah:", "editing": "<PERSON><PERSON><PERSON>", "editingDescription": "<PERSON> dokumen", "reviewing": "<PERSON><PERSON><PERSON><PERSON>", "reviewingDescription": "Membuat cadangan", "viewOnly": "<PERSON><PERSON>", "viewOnlyDescription": "<PERSON><PERSON> tanpa cadangan", "notSupportedOnMobile": "Penyuntingan pejabat tidak disokong pada peranti mudah alih.", "previewAllChanges": "<PERSON><PERSON><PERSON> semua per<PERSON>han", "accept": "Terima", "reject": "<PERSON><PERSON>", "pastingTitle": "Menampal tidak tersedia", "pastingMessage": "Menampal tidak disokong dalam penyemak imbas anda. <PERSON><PERSON><PERSON><PERSON>, anda boleh menggunakan pintasan papan kekunci", "pastingWithoutFormatTitle": "Menampal tanpa pemformatan tidak tersedia", "pastingWithoutFormatMessage": "Menampal tanpa pemformatan tidak disokong dalam penyemak imbas anda. Sebalik<PERSON>, anda boleh menggunakan pintasan papan kekunci", "breaks": "<PERSON><PERSON>", "pageBreak": "<PERSON><PERSON><PERSON><PERSON>", "pageBreakDescription": "<PERSON><PERSON> akhir dan mulakan pada halaman baharu", "sectionBreakNextPage": "<PERSON><PERSON>h <PERSON> - <PERSON><PERSON>", "sectionBreakNextPageDescription": "<PERSON><PERSON><PERSON><PERSON> pemisah bahagian dan mulakan pada halaman set<PERSON>nya", "sectionBreakContinuous": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "sectionBreakContinuousDescription": "<PERSON><PERSON><PERSON><PERSON> pemisah bahagian dan teruskan pada halaman yang sama ", "section": "Bahagian", "margins": "<PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON><PERSON>", "narrow": "Sempit", "moderate": "<PERSON><PERSON><PERSON>", "wide": "lebar", "top": "Atas", "bottom": "<PERSON>wa<PERSON>", "left": "kiri", "right": "Betul", "customMargins": "<PERSON><PERSON>", "customMarginsDescription": "Tentukan margin tersuai", "header": {"0": "Pen<PERSON>pal<PERSON>", "1": "<PERSON><PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "-1": "Pengepala tidak sah"}, "footer": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "-1": "Pengaki Tidak Sah"}, "options": "<PERSON><PERSON><PERSON>", "pageOptions": "<PERSON><PERSON><PERSON>", "removeHeader": "<PERSON><PERSON>", "removeFooter": "<PERSON><PERSON>", "headerFooterOptionsModal": {"title": "Format pengepala & pengaki", "headerFromTop": "Tajuk dari <PERSON>", "footerFromBottom": "Penga<PERSON> dari <PERSON>", "layouts": {"layout": "<PERSON><PERSON>", "noSelection": "<PERSON><PERSON><PERSON>", "differentFirstPage": "<PERSON><PERSON> yang berbeza", "differentEvenOddPages": "Halaman Genap & Ganjil <PERSON>a", "differentFirstEvenOddPages": "<PERSON><PERSON>, <PERSON><PERSON> dan <PERSON> yang berbeza"}}, "marginsModal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "leftMargin": "<PERSON><PERSON>", "rightMargin": "<PERSON><PERSON>", "topMargin": "<PERSON><PERSON>", "bottomMargin": "<PERSON><PERSON>"}, "lineSpacingOptions": {"15": "1.5", "115": "1.15", "single": "<PERSON><PERSON><PERSON><PERSON>", "double": "<PERSON><PERSON><PERSON>", "custom": "Ada<PERSON>"}, "numberDropdown": {"6": "Nombor Roman Latin 1", "7": "Perpuluhan Nombor", "8": "Nombor Roman Latin 2", "10": "Rom Latin", "11": "Nombor Latin Rom", "dropdownLabel": "Pratetap <PERSON>"}, "bulletDropdown": {"0": "peluru", "1": "Dataran <PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON>", "4": "Semak", "5": "anak panah", "dropdownLabel": "Pratetap Peluru"}, "fontSize": {"dropdownLabel": "<PERSON><PERSON>"}, "fontStyles": {"dropdownLabel": "<PERSON><PERSON>"}, "fontFamily": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}}, "spreadsheetEditor": {"editing": "<PERSON><PERSON><PERSON>", "viewOnly": "Melihat", "editingDescription": "<PERSON> dokumen", "viewOnlyDescription": "<PERSON><PERSON> sahaja", "bold": "berani", "italic": "Italic", "underline": "<PERSON><PERSON> bawah", "strikethrough": "Strikethrough", "strikeout": "Strikeout", "cellBorderStyle": "<PERSON><PERSON>", "merge": "Bercantum", "unmerge": "Nyahcantum", "cellFormat": "Format Sel", "automaticFormat": "Automatik", "plainTextFormat": "<PERSON><PERSON> <PERSON>", "increaseDecimalFormat": "Tingkatkan perpuluhan", "decreaseDecimalFormat": "Kurangkan perpuluhan", "numberFormat": "Nombor", "percentFormat": "<PERSON><PERSON>", "accountingFormat": "<PERSON><PERSON><PERSON><PERSON>", "financialFormat": "<PERSON><PERSON><PERSON>", "currencyFormat": "mata wang", "currencyRoundedFormat": "<PERSON> wang dibulatkan", "calendarFormat": "tarikh", "clockHourFormat": "<PERSON><PERSON>", "calendarTimeFormat": "<PERSON><PERSON><PERSON>", "formatAsCurrency": "Format sebagai mata wang", "formatAsPercent": "Format sebagai peratus", "formatAsDecDecimal": "Kurangkan titik perpuluhan", "formatAsIncDecimal": "Tambah titik perpuluhan", "textColor": "<PERSON><PERSON>", "cellBackgroundColor": "<PERSON><PERSON>", "cellBorderColor": "<PERSON><PERSON>", "textLabel": "Teks", "backgroundLabel": "<PERSON><PERSON> be<PERSON>", "borderLabel": "Sempadan", "textAlignment": "<PERSON><PERSON><PERSON><PERSON>", "topAlign": "<PERSON><PERSON><PERSON> atas", "middleAlign": "<PERSON><PERSON><PERSON> tengah", "bottomAlign": "<PERSON><PERSON><PERSON> bawah", "cellAdjustment": "<PERSON><PERSON><PERSON><PERSON>", "cellFormatMoreOptions": "Lebih banyak pilihan format sel", "insertColumnLeft": "Sisipkan lajur kiri", "insertColumnRight": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>r kanan", "insertRowTop": "Sisipkan baris di atas", "insertRowBottom": "Sisipkan baris di bawah", "insertColumnShiftDown": "<PERSON><PERSON><PERSON><PERSON> sel dan beralih ke bawah", "insertColumnShiftRight": "<PERSON><PERSON><PERSON>n sel dan alih ke kanan", "deleteColumn": "<PERSON><PERSON> la<PERSON>r", "deleteRow": "Pa<PERSON><PERSON> baris", "deleteColumnShiftUp": "Padamkan sel dan alihkan ke atas", "deleteColumnShiftLeft": "Padam<PERSON> sel dan alih ke kiri", "clearBorder": "Kosongkan Sempadan", "cellBorderAll": "<PERSON><PERSON><PERSON>", "cellBorderInside": "<PERSON><PERSON>", "cellBorderOutside": "<PERSON><PERSON>", "cellBorderTop": "Sempadan Atas", "cellBorderBottom": "Sempadan <PERSON>", "cellBorderLeft": "<PERSON><PERSON><PERSON><PERSON>", "cellBorderRight": "<PERSON><PERSON><PERSON><PERSON>", "cellBorderVertical": "Sempadan Menegak", "cellBorderHorizontal": "Sempadan Mendatar", "cellBorderNone": "Tiada Sempadan", "borders": "Sempadan", "fontSize": {"dropdownLabel": "<PERSON><PERSON>"}, "fontFamily": {"dropdownLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "blankSheet": "<PERSON><PERSON><PERSON>"}, "insertPageModal": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tabs": {"blank": "kosong", "upload": "<PERSON>at naik"}, "pagePlacements": {"header": "Peletakan <PERSON>", "above": "<PERSON><PERSON>", "below": "<PERSON><PERSON>"}, "pageLocations": {"header": "<PERSON><PERSON>", "specify": "<PERSON><PERSON><PERSON><PERSON>", "specifyLocation": "Tentukan <PERSON>", "amount": "<PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "pages": "muka surat"}, "pageDimensions": {"header": "<PERSON><PERSON><PERSON>", "subHeader": "Pratetap", "presets": {"letter": "surat", "halfLetter": "<PERSON><PERSON><PERSON> huruf", "juniorLegal": "Undang-undang muda", "custom": "Ada<PERSON>"}, "units": "Unit"}, "browse": "<PERSON><PERSON>k im<PERSON>", "fileSelected": {"title": "<PERSON><PERSON><PERSON> untuk Ditambah"}, "button": "<PERSON><PERSON> Halaman", "selectPages": "<PERSON><PERSON><PERSON> untuk Ditambah", "page": "<PERSON><PERSON>", "warning": {"title": "<PERSON><PERSON><PERSON> dari sisipan halaman baharu?", "message": "<PERSON><PERSON><PERSON> akan membatalkan semua pilihan yang telah anda buat setakat ini. <PERSON><PERSON>h anda pasti anda masih mahu keluar?"}}, "multiViewer": {"dragAndDrop": "<PERSON>et dan <PERSON> fail anda di sini untuk membandingkan", "or": "Ataupun", "browse": "<PERSON><PERSON>k im<PERSON>", "startSync": "<PERSON><PERSON><PERSON>", "stopSync": "Hentikan Pen<PERSON>gerakan", "closeDocument": "Tutup Dokumen", "save": "Simpan Dokumen", "comparePanel": {"Find": "<PERSON>i dalam dokumen", "changesList": "<PERSON><PERSON>", "change": "Ubah", "old": "<PERSON><PERSON>", "new": "<PERSON><PERSON>", "page": "<PERSON><PERSON>", "textContent": "Kandungan Teks", "delete": "padam", "insert": "ma<PERSON><PERSON>n", "edit": "edit"}}, "saveModal": {"close": "tutup", "saveAs": "Simpan sebagai", "general": "<PERSON><PERSON>", "fileName": "<PERSON>a fail", "fileType": "<PERSON><PERSON> fail", "fileLocation": "Lok<PERSON>", "browse": "<PERSON><PERSON><PERSON> imbas", "pageRange": "<PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON>", "currentView": "Pandangan Semasa", "currentPage": "Muka surat ini", "specifyPage": "<PERSON><PERSON><PERSON><PERSON>", "properties": "<PERSON><PERSON><PERSON>", "includeAnnotation": "Sertakan <PERSON>", "includeComments": "Sertakan Komen", "watermark": "Tera air", "addWatermark": "Tambah Tera Air", "save": "Simpan fail", "pageError": "<PERSON><PERSON> masukkan julat antara 1 - ", "fileNameCannotBeEmpty": "<PERSON><PERSON> tidak boleh kosong"}, "filePicker": {"dragAndDrop": "Seret & Lepaskan fail anda di sini", "selectFile": "<PERSON><PERSON><PERSON> fail anda di sini", "or": "Ataupun"}, "stylePanel": {"headings": {"styles": "<PERSON><PERSON>", "annotation": "<PERSON><PERSON><PERSON>", "annotations": "<PERSON><PERSON><PERSON>", "tool": "alat", "textStyles": "<PERSON><PERSON>", "currentColor": "<PERSON><PERSON>", "customColors": "<PERSON><PERSON>", "redactionTextLabel": "Label Teks", "redactionMarkOutline": "<PERSON><PERSON><PERSON>", "redactionFill": "<PERSON><PERSON>", "redactionTextPlaceholder": "Sisipkan label teks", "contentEdit": "<PERSON><PERSON><PERSON>"}, "lineStyles": {"startLineStyleLabel": "<PERSON><PERSON>", "middleLineStyleLabel": "<PERSON><PERSON>", "endLineStyleLabel": "<PERSON><PERSON>"}, "addColorToCustom": "Tambahkan pada warna tersuai", "noToolSelected": "<PERSON>lih alat untuk melihat sifat alat", "noToolStyle": "Alat tidak mengandungi sebarang sifat penggayaan.", "noSharedToolStyle": "<PERSON><PERSON><PERSON> yang dipilih tidak berkongsi sebarang sifat penggayaan.", "lineEnding": {"start": {"dropdownLabel": "<PERSON><PERSON>"}, "end": {"dropdownLabel": "Talian Tamat"}, "middle": {"dropdownLabel": "<PERSON><PERSON>"}}, "borderStyle": {"dropdownLabel": "Sempadan"}}, "signatureListPanel": {"header": "<PERSON><PERSON><PERSON>", "newSignature": "Tandatangan Baru", "newSignatureAndInitial": "Tandatangan & Permulaan Baharu", "signatureList": {"signature": "Tandatangan", "initials": "Inisial"}}, "rubberStampPanel": {"header": "setem", "standard": "Setem Standard"}, "colorPickerModal": {"modalTitle": "<PERSON><PERSON><PERSON><PERSON>"}, "accessibility": {"landmarks": {"topHeader": "Ta<PERSON>k <PERSON>", "leftHeader": "<PERSON><PERSON><PERSON>", "rightHeader": "<PERSON><PERSON><PERSON>", "bottomHeader": "<PERSON><PERSON><PERSON>", "documentContent": "Kandungan Dokumen"}, "label": "Kebolehcapaian", "accessibilityMode": "<PERSON><PERSON>", "skipTo": "Lang<PERSON><PERSON> ke", "document": "Dokumen", "notes": "<PERSON>a"}, "formulaBar": {"label": "Formula Bar", "range": "Julat", "formulas": "Formula", "sumif": "<PERSON><PERSON><PERSON> be<PERSON> mere<PERSON> j<PERSON>t", "sumsq": "Men<PERSON><PERSON><PERSON><PERSON> hasil tambah kuasa dua julat", "sum": "Menambah semua nombor dalam julat", "asinh": "Mengembalikan sinus hiperbolik songsang bagi suatu nombor", "acos": "Mengembalikan a<PERSON><PERSON> no<PERSON>, dalam radian", "cosh": "Mengembalikan kosinus hiperbolik suatu nombor", "iseven": "Menyemak sama ada nombor genap", "isodd": "Menyemak sama ada nombor itu ganjil"}}