(window.webpackJsonp = window.webpackJsonp || []).push([
	[62],
	{
		1894: function (o, e, t) {
			var n = t(30),
				r = t(1895);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[o.i, r, ""]]);
			var a = {
				insert: function (o) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(o);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function o(e, t = document) {
								const n = [];
								return (
									t.querySelectorAll(e).forEach((o) => n.push(o)),
									t.querySelectorAll("*").forEach((t) => {
										t.shadowRoot && n.push(...o(e, t.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const t = [];
					for (let n = 0; n < e.length; n++) {
						const r = e[n];
						if (0 === n)
							r.shadowRoot.appendChild(o),
								(o.onload = function () {
									t.length > 0 &&
										t.forEach((e) => {
											e.innerHTML = o.innerHTML;
										});
								});
						else {
							const e = o.cloneNode(!0);
							r.shadowRoot.appendChild(e), t.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(r, a);
			o.exports = r.locals || {};
		},
		1895: function (o, e, t) {
			(e = o.exports = t(31)(!1)).push([
				o.i,
				".open.ErrorModal{visibility:visible}.closed.ErrorModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ErrorModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.ErrorModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.ErrorModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.ErrorModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.ErrorModal .footer .modal-button.cancel:hover,.ErrorModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.ErrorModal .footer .modal-button.cancel,.ErrorModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.ErrorModal .footer .modal-button.cancel.disabled,.ErrorModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.ErrorModal .footer .modal-button.cancel.disabled span,.ErrorModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.ErrorModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.ErrorModal .modal-container .wrapper .modal-content{padding:10px}.ErrorModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.ErrorModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.ErrorModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.ErrorModal .footer .modal-button.confirm{margin-left:4px}.ErrorModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .footer .modal-button{padding:23px 8px}}.ErrorModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .swipe-indicator{width:32px}}.ErrorModal .modal-container{width:300px;word-break:break-word;white-space:pre-wrap}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2051: function (o, e, t) {
			"use strict";
			t.r(e);
			t(46),
				t(52),
				t(223),
				t(19),
				t(12),
				t(13),
				t(8),
				t(14),
				t(10),
				t(9),
				t(11),
				t(16),
				t(15),
				t(20),
				t(18);
			var n = t(0),
				r = t.n(n),
				a = t(17),
				i = t.n(a),
				d = t(6),
				l = t(347),
				p = t(2),
				s = t(4),
				c = t(42),
				m = t(93),
				u = t(351),
				b = t(5);
			t(1894);
			function f(o, e) {
				return (
					(function (o) {
						if (Array.isArray(o)) return o;
					})(o) ||
					(function (o, e) {
						var t =
							null == o
								? null
								: ("undefined" != typeof Symbol && o[Symbol.iterator]) ||
									o["@@iterator"];
						if (null != t) {
							var n,
								r,
								a,
								i,
								d = [],
								l = !0,
								p = !1;
							try {
								if (((a = (t = t.call(o)).next), 0 === e)) {
									if (Object(t) !== t) return;
									l = !1;
								} else
									for (
										;
										!(l = (n = a.call(t)).done) &&
										(d.push(n.value), d.length !== e);
										l = !0
									);
							} catch (o) {
								(p = !0), (r = o);
							} finally {
								try {
									if (
										!l &&
										null != t.return &&
										((i = t.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (p) throw r;
								}
							}
							return d;
						}
					})(o, e) ||
					(function (o, e) {
						if (!o) return;
						if ("string" == typeof o) return h(o, e);
						var t = Object.prototype.toString.call(o).slice(8, -1);
						"Object" === t && o.constructor && (t = o.constructor.name);
						if ("Map" === t || "Set" === t) return Array.from(o);
						if (
							"Arguments" === t ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)
						)
							return h(o, e);
					})(o, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function h(o, e) {
				(null == e || e > o.length) && (e = o.length);
				for (var t = 0, n = new Array(e); t < e; t++) n[t] = o[t];
				return n;
			}
			var w = function () {
				var o = f(
						Object(d.e)(function (o) {
							return [
								s.a.getErrorMessage(o),
								s.a.getErrorTitle(o),
								s.a.isElementDisabled(o, b.a.ERROR_MODAL),
								s.a.isElementOpen(o, b.a.ERROR_MODAL),
							];
						}, d.c),
						4,
					),
					e = o[0],
					t = o[1],
					a = o[2],
					h = o[3],
					w = Object(d.d)(),
					x = f(Object(l.a)(), 1)[0],
					y = function (o) {
						return null == o ? void 0 : o.includes("dev.apryse.com");
					};
				Object(n.useEffect)(
					function () {
						if (h)
							return (
								w(
									p.a.closeElements([
										b.a.SIGNATURE_MODAL,
										b.a.PRINT_MODAL,
										b.a.LOADING_MODAL,
										b.a.PROGRESS_MODAL,
										b.a.PASSWORD_MODAL,
										b.a.FILTER_MODAL,
									]),
								),
								window.addEventListener("keydown", function (o) {
									return Object(m.b)(o, v);
								}),
								function () {
									return window.removeEventListener("keydown", m.b);
								}
							);
					},
					[w, h],
				);
				var E = e.startsWith("message."),
					v = function () {
						w(p.a.closeElement(b.a.ERROR_MODAL)),
							y(e) && window.open("https://dev.apryse.com", "_blank");
					},
					g = x("action.ok");
				return (
					y(e) && (g = "Get trial key"),
					a
						? null
						: r.a.createElement(
								"div",
								{
									className: i()({
										Modal: !0,
										ErrorModal: !0,
										open: h,
										closed: !h,
									}),
									"data-element": b.a.ERROR_MODAL,
								},
								r.a.createElement(
									u.a,
									{
										isOpen: h,
										title: t || "message.error",
										closeButtonDataElement: "errorModalCloseButton",
										onCloseClick: v,
									},
									r.a.createElement(
										"div",
										{ className: "modal-content error-modal-content" },
										r.a.createElement("p", null, E ? x(e) : e),
									),
									r.a.createElement(
										"div",
										{ className: "modal-footer footer" },
										r.a.createElement(c.a, {
											className: "confirm modal-button",
											dataElement: "closeErrorModalButton",
											label: g,
											onClick: v,
										}),
									),
								),
							)
				);
			};
			e.default = w;
		},
	},
]);
//# sourceMappingURL=chunk.62.js.map
