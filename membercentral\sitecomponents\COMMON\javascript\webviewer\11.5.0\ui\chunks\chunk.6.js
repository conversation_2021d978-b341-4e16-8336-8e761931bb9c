(window.webpackJsonp = window.webpackJsonp || []).push([
	[6],
	{
		1542: function (e, t, n) {
			"use strict";
			var o = n(1);
			t.a = function (e) {
				var t = window.Core.Annotations.FreeTextAnnotation;
				if (e instanceof t && e.getAutoSizeType() !== t.AutoSizeTypes.NONE) {
					var n = o.a.getDocument(),
						a = e.PageNumber,
						i = n.getPageInfo(a),
						g = n.getPageMatrix(a),
						r = n.getPageRotation(a);
					e.fitText(i, g, r);
				}
			};
		},
	},
]);
//# sourceMappingURL=chunk.6.js.map
