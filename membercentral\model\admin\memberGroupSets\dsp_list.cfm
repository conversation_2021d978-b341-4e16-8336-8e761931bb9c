<cfset local.selectedTab = event.getTrimValue("tab","GroupSet")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>
<cfsavecontent variable="local.GSListJS">
	<cfoutput>
		<script language="JavaScript">
			let gridInitArray = [{'importExportTab':false}], groupSetTable;

			function initializeGroupSetTable() {
				groupSetTable = $('##groupSetTable').DataTable({
					"processing": true,
					"serverSide": true,
					"pageLength": 10,
					"lengthMenu": [ 10, 25, 50, 100 ],
					"language": {
						"lengthMenu": "_MENU_"
					},
					"ajax": { 
						"url": "#local.groupSetLink#",
						"type": "post",
						"data": function(d) {
							$.each($('##frmFilter').serializeArray(),function() {
								d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
							});
						}
					},
					"autoWidth": false,
					"columns": [
						{ "data": "GroupSetName" },
						{ "data": "groupCount",
						  "width": "15%", 
						  "className": "text-center"},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<a href="javascript:editGroupSet('+data.GroupsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Edit Group Set"><i class="fa-solid fa-pencil"></i></a>';
									
									if (data.GroupSetBeingUsed == 0) {
										renderData += '<a href="javascript:deleteGroupSet('+data.GroupsetID+');" id="btnDel'+data.GroupsetID+'" class="btn btn-xs text-danger p-1 m-1" title="Delete Group Set"><i class="fa-solid fa-trash-can"></i></a>';
									} else {
										renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-can"></i></a>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"width": "15%",
							"className": "text-center",
							"orderable": false
						}
					],
					"order": [[0, 'asc']],
					"searching": false
				});
			}
			function hideToolBarContent() {
				$('##groupSetGrid').show();
				$('##divItemForm').hide();
			}
			function addGroupSet() {
				$('##groupSetGrid').hide();
				$('##divItemForm').show();
				$('##groupSetName').focus();
			}
			function editGroupSet(gsid) {
				MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: gsid > 0 ? 'Edit Member Group Set' : 'Create New Member Group Set',
				iframe: true,
				contenturl: '#this.link.edit#&gsID=' + gsid,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frm_createGroupSet").submit',
					extrabuttonlabel: 'Save',
				}
				});
			}
			function deleteGroupSet(gsid) {
				var removeItem = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						groupSetTable.draw(false);
					} else {
						delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						if(r.msg) alert(r.msg);
						else alert('We were unable to delete this group set. Try again.');
					}
				};

				let delElement = $('##btnDel'+gsid);
				mca_initConfirmButton(delElement, function(){
					var objParams = { gsid:gsid };
					TS_AJX('MEMBERGROUPSETS','doRemoveGroupSet',objParams,removeItem,removeItem,10000,removeItem);
				});
			}
			function validateForm(){
				mca_hideAlert('err_ad');
				var msg = '';
				if($('##groupSetName').val().trim().length == 0){
					msg = 'Enter a name for the new Member Group Set.';
				}
				if(msg.length == 0){					
					var addGroupToGroupSetResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true'){
							$('##frm_createGroupSet')[0].reset();
							$('##groupSetGrid').show();
							$('##divItemForm').hide();
							reloadGroupSetsTable();
							editGroupSet(r.gsid);
							}
							else{
								$('##groupSetGrid').hide();
								$('##divItemForm').show();
								mca_showAlert('err_ad', 'Unable to add  group set');
							}
						};	
					var objParams = { groupSetName:$('##groupSetName').val().trim()};
					TS_AJX('MEMBERGROUPSETS','doCreateMemberGroupSet',objParams,addGroupToGroupSetResult,addGroupToGroupSetResult,10000,addGroupToGroupSetResult);		
				}
				else {
					mca_showAlert('err_ad',msg);
					return false;
				}			
			}
			
			function gotoList() {
				top.location.href = '#this.link.list#';
			}
			function onTabChangeHandler(ActiveTab) {
				if (!gridInitArray[ActiveTab.id]) {
					gridInitArray[ActiveTab.id] = true;
					switch(ActiveTab.id) {
						case "groupSetTab":
							initializeGroupSetTable(); break;
					}
				}
			}	
			function reloadGroupSetsTable() {
				groupSetTable.draw();
			}
			function doFilterGroupSets() {
				reloadGroupSetsTable();
				$('##divFilterForm').hide('slow');
			}
			function clearFilterGroupSets() {
				$('##frmFilter')[0].reset();
				doFilterGroupSets();
			}
			function filterGroupSets() {
				if (!$('##divFilterForm').is(':visible')) {
					$('div.grpToolBarItem').hide();
					$('##divFilterForm').show();
				}
			}

			$(function() {
				mca_initNavPills('GroupSetPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
				$('##groupSetName').on('keydown', function (e) {
					if (e.key === 'Enter' || e.keyCode === 13) {
					e.preventDefault(); 
					$('##btnAddGroupSet').click();
					}
				});
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.GSListJS)#">

<cfoutput>
<h4>Member Group Sets</h4>
<ul class="nav nav-pills nav-pills-dotted" id="GroupSetPills">
	<cfset local.thisTabID = "groupSetTab">
	<cfset local.thisTabName = "groupSet">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Group Sets</a>
	</li>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.thisTabID = "importExportTab">
		<cfset local.thisTabName = "ex">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import / Export</a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-groupSetTab" role="tabpanel" aria-labelledby="groupSetTab">
		<!--- button bar --->
		<div class="toolButtonBar">
			<div><a href="javascript:addGroupSet();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new group set."><i class="fa-regular fa-circle-plus"></i> Add Group Set</a></div>
			<div><a href="javascript:filterGroupSets();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter group sets."><i class="fa-regular fa-filter"></i> Filter Group Sets</a></div>
		</div>

		<div id="divFilterForm" class="my-3 grpToolBarItem" style="display:none;">
			<form name="frmFilter" id="frmFilter" onsubmit="doFilterGroupSets();return false;">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-lg">
							Filter Group Sets
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-row">
							<div class="col-sm-6">
								<div class="form-label-group mb-0">
									<input type="text" name="fGroupSetName" id="fGroupSetName" class="form-control" value="">
									<label for="fGroupSetName">Group Set Name Contains...</label>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-label-group mb-0">
									<input type="text" name="fGroupSetUID" id="fGroupSetUID" class="form-control" maxlength="60" value="">
									<label for="fGroupSetUID">API ID</label>
								</div>
							</div>
						</div>			
					</div>
					<div class="card-footer p-2 text-right">
						<button type="button" name="btnResetFilterGroupSets" class="btn btn-sm btn-secondary" onclick="clearFilterGroupSets();">Clear Filters</button>
						<button type="submit" name="btnFilterGroupSets" class="btn btn-sm btn-primary">Show Group Sets</button>
					</div>
				</div>
			</form>
		</div>
		
		<div id="divItemForm" style="display:none;">
			<div id="divItemFormArea" class="row my-3">
				<div class="col-xl-12">
					<div class="card card-box mb-1">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								Create New Member Group Set
							</div>
							<button type="button" class="close" aria-label="Close" onclick="hideToolBarContent();">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>
						<div class="card-body pb-3">
							<div id="err_ad" class="alert alert-danger mb-2 d-none"></div>
							<form method="post" name="frm_createGroupSet" id="frm_createGroupSet" >
								<input type="hidden" name="gsID"  id="gsID" value="0">
								<div class="form-group">
									<div class="form-label-group">
										<input type="text" autocomplete="off" message="Please enter a group set name." size="44" maxlength="200" name="groupSetName" id="groupSetName" value="" class="form-control ">
										<label for="groupSetName">Group Set Name</label>
									</div>
								</div>
								<div class="mt-2">
									<button type="button" id="btnAddGroupSet" name="btnAddGroupSet" class="btn btn-sm btn-primary" onclick="return validateForm();">
										<span class="btn-wrapper--icon">
											<i class="fa-light fa-right"></i>
										</span>
										<span class="btn-wrapper--label">Continue</span>
									</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div id="groupSetGrid">
			<table id="groupSetTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Group Set</th>
						<th>Group Count</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div class="tab-pane fade" id="pills-importExportTab" role="tabpanel" aria-labelledby="importExportTab">
			<cfinclude template="dsp_importexport.cfm">
		</div>
	</cfif>
</div>
</cfoutput>