ALTER PROC dbo.sub_copySubscription
@orgID INT,
@siteid INT,
@typeID INT,
@subID INT,
@newSubName VARCHAR(300),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @scheduleID INT, @rateTermDateFlag varchar(1), @autoExpire BIT, @status varchar(1), 
		@soldSeparately BIT, @paymentOrder INT, @GLAccountID INT, @allowRateGLOverride BIT, 
		@activationOptionCode varchar(1), @alternateActivationOptionCode varchar(1), @feRawContent varchar(max), 
		@feCompletedRawContent varchar(max), @feParentSubRawContent varchar(max), @emailTemplateID INT, 
		@renewEmailTemplateID INT, @newSubID INT, @addonID INT, @newAddonID INT, @childSetID INT, @orderNum INT,
		@minAllowed INT, @maxAllowed INT, @useAcctCodeInSet BIT, @useTermEndDateInSet BIT, @PCnum INT, 
		@PCPctOffEach INT, @frontEndAllowSelect BIT, @frontEndAllowChangePrice BIT, @frontEndContentID INT, 
		@frontEndAddAdditional BIT, @feContentRawContent VARCHAR(MAX), @subAdminResourceID INT, 
		@appCreatedContentResourceTypeID INT, @languageID INT, @contentID INT, @contentSRID INT, 
		@subName VARCHAR(300), @msgjson VARCHAR(MAX);

	SELECT @scheduleID=subs.scheduleID, @autoExpire=subs.autoExpire, @status=subs.status, @soldSeparately=subs.soldSeparately,  
		@rateTermDateFlag=subs.rateTermDateFlag, @GLAccountID=subs.GLAccountID, 
		@paymentOrder=subs.paymentOrder, @allowRateGLOverride=subs.allowRateGLAccountOverride, 
		@feRawContent=frontEndContent.rawContent, @feCompletedRawContent=frontEndCompletedContent.rawContent, 
		@feParentSubRawContent=frontEndParentSubContent.rawContent, @emailTemplateID=subs.emailTemplateID, 
		@renewEmailTemplateID=subs.renewEmailTemplateID, @activationOptionCode=o.subActivationCode, 
		@alternateActivationOptionCode=o2.subActivationCode, @subName=subs.subscriptionName
	FROM dbo.sub_subscriptions subs
	INNER JOIN dbo.sub_types t on t.typeID = subs.typeID and t.siteID = @siteid
	INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = subs.GLAccountID
	INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
	INNER JOIN dbo.sub_activationOptions o on o.subActivationID = subs.subActivationID
	INNER JOIN dbo.sub_activationOptions o2 on o2.subActivationID = subs.subAlternateActivationID
	CROSS APPLY dbo.fn_getContent(subs.frontEndContentID,1) as frontEndContent
	CROSS APPLY dbo.fn_getContent(subs.frontEndCompletedContentID,1) as frontEndCompletedContent
	CROSS APPLY dbo.fn_getContent(subs.frontEndParentSubContentID,1) as frontEndParentSubContent
	LEFT OUTER JOIN dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID
	LEFT OUTER JOIN dbo.et_emailTemplates ret on ret.templateID = subs.renewEmailTemplateID
	WHERE subs.subscriptionID = @subID;

	BEGIN TRAN;
		-- create subscription
		EXEC dbo.sub_createSubscription @orgID=@orgID, @siteid=@siteid, @typeID=@typeID, @subName=@newSubName,
			@reportCode=NULL, @scheduleID=@scheduleID, @rateTermDateFlag=@rateTermDateFlag,
			@autoExpire=@autoExpire, @status=@status, @soldSeparately=@soldSeparately, @paymentOrder=@paymentOrder,
			@GLAccountID=@GLAccountID, @allowRateGLOverride=@allowRateGLOverride, @activationOptionCode=@activationOptionCode,
			@alternateActivationOptionCode=@alternateActivationOptionCode, @feRawContent=@feRawContent, 
			@feCompletedRawContent=@feCompletedRawContent, @feParentSubRawContent=@feParentSubRawContent, 
			@recordedByMemberID=@recordedByMemberID, @subID=@newSubID OUTPUT;

		UPDATE dbo.sub_subscriptions
		SET emailTemplateID = @emailTemplateID,
			renewEmailTemplateID = @renewEmailTemplateID
		WHERE subscriptionID = @newSubID;

		-- copy subscription_sets
		INSERT INTO dbo.sub_subscriptionSets (subscriptionID, setID, orderNum)
		SELECT @newSubID as subscriptionID, setID, (SELECT IsNull(MAX(orderNum),0)+1 FROM sub_subscriptionSets WHERE setID=sets.setID)
		FROM sub_subscriptionSets sets
		where subscriptionID=@subID;

		-- copy add_ons
		SELECT @languageID = defaultLanguageID, @subAdminResourceID = subscriptionAdminSiteResourceID
		FROM dbo.sites
		WHERE siteID = @siteID;

		SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

		DECLARE add_on_cursor CURSOR FOR 
			SELECT addonID, childSetID, orderNum 
			FROM dbo.sub_addons
			WHERE subscriptionID=@subID;
		OPEN add_on_cursor;
		FETCH NEXT FROM add_on_cursor INTO @addonID, @childSetID, @orderNum;
		WHILE @@FETCH_STATUS = 0
		BEGIN

			SELECT @minAllowed=ao.minAllowed, @maxAllowed=ao.maxAllowed, @useAcctCodeInSet=ao.useAcctCodeInSet,
				@useTermEndDateInSet=ao.useTermEndDateInSet, @PCnum=IsNull(ao.PCnum, 0), @PCPctOffEach=IsNull(PCPctOffEach, 0),
				@frontEndAllowSelect=ao.frontEndAllowSelect, @frontEndAddAdditional=ao.frontEndAddAdditional,
				@frontEndAllowChangePrice=ao.frontEndAllowChangePrice, @feContentRawContent=frontEndContent.rawContent
			FROM dbo.sub_addons ao
			INNER JOIN dbo.sub_sets sets on sets.setID = ao.childSetID and sets.siteID=@siteid
			CROSS APPLY dbo.fn_getContent(ao.frontEndContentID,1) as frontEndContent
			WHERE ao.addonID = @addonID AND childSetID=@childSetID;

			INSERT INTO dbo.sub_addons(subscriptionID, childSetID, orderNum, minAllowed, maxAllowed, useAcctCodeInSet,
				useTermEndDateInSet, PCnum, PCPctOffEach, frontEndAllowSelect, frontEndAllowChangePrice,frontEndAddAdditional)
			VALUES (@newSubID, @childSetID, (@orderNum+1), @minAllowed, @maxAllowed, @useAcctCodeInSet, @useTermEndDateInSet,
				@PCnum, @PCPctOffEach, @frontEndAllowSelect, @frontEndAllowChangePrice, @frontEndAddAdditional);

			SELECT @newAddonID = SCOPE_IDENTITY();

			exec dbo.cms_createContentObject @siteID=@siteid, @resourceTypeID=@appCreatedContentResourceTypeID, @parentSiteResourceID=@subAdminResourceID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle='', @contentDesc='', 
				@rawContent=@feContentRawContent, @memberID=null, @contentID=@contentID OUTPUT, @siteResourceID=@contentSRID OUTPUT;

			UPDATE dbo.sub_addons
			SET frontEndContentID = @contentID
			WHERE addonID = @newAddonID;

			FETCH NEXT FROM add_on_cursor INTO @addonID, @childSetID, @orderNum;
		END 
		CLOSE add_on_cursor;
		DEALLOCATE add_on_cursor;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@newSubID AS varchar(10))+' }';
		SET @msgjson = STRING_ESCAPE('Subscription ' + QUOTENAME(@newSubName) + ' has been copied from Subscription '+ QUOTENAME(@subName) +'.','json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
