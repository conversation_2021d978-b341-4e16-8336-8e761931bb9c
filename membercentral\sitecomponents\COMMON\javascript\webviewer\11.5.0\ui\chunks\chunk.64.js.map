{"version": 3, "sources": ["webpack:///./src/ui/src/components/FormulaBar/FormulaBar.scss?c368", "webpack:///./src/ui/src/components/FormulaBar/FormulaBar.scss", "webpack:///./src/ui/src/components/FormulaBar/FormulaBar.js", "webpack:///./src/ui/src/components/FormulaBar/FormulaBarContainer.js", "webpack:///./src/ui/src/components/FormulaBar/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "FormulaBar", "props", "isReadOnly", "activeCellRange", "cellFormula", "stringCellValue", "onRangeInputChange", "onRangeInputKeyDown", "t", "useTranslation", "formulaBarValue", "DataElementWrapper", "className", "dataElement", "DataElements", "FORMULA_BAR", "type", "value", "onChange", "e", "target", "onKeyDown", "aria-label", "classNames", "readOnly", "Icon", "glyph", "propTypes", "PropTypes", "bool", "string", "oneOfType", "number", "func", "formulaOptions", "label", "description", "EDIT_MODE", "Core", "SpreadsheetEditor", "SpreadsheetEditorEditMode", "FormulaBarContainer", "useSelector", "selectors", "getActiveCellRange", "getCellFormula", "getStringCellValue", "isReadOnlyMode", "getSpreadsheetEditorEditMode", "VIEW_ONLY", "useState", "rangeInputValue", "setRangeInputValue", "useEffect", "event", "key", "spreadsheetEditorDocument", "core", "getDocumentViewer", "getDocument", "getSpreadsheetEditorDocument", "cellRange", "getCellRange", "selectCellRange", "console", "error"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,sgCAAugC,M,4NCI1hC4B,EAAa,SAACC,GAClB,IAAQC,EAAuGD,EAAvGC,WAAYC,EAA2FF,EAA3FE,gBAAiBC,EAA0EH,EAA1EG,YAAaC,EAA6DJ,EAA7DI,gBAAiBC,EAA4CL,EAA5CK,mBAAoBC,EAAwBN,EAAxBM,oBAE/EC,EAAMC,cAAND,EAEFE,EAAkBN,GAAeC,GAAmB,GAE1D,OACE,kBAACM,EAAA,EAAkB,CAACC,UAAU,aAAaC,YAAaC,IAAaC,aACnE,2BACEC,KAAK,OACLJ,UAAU,aACVK,MAAOd,EACPe,SAAU,SAACC,GAAC,OAAKb,EAAmBa,EAAEC,OAAOH,QAC7CI,UAAWd,EACXe,aAAYd,EAAE,sBAEhB,yBAAKI,UAAWW,IAAW,UAAW,CAAEC,SAAUtB,KAChD,kBAACuB,EAAA,EAAI,CAACC,MAAM,WAAWd,UAAWW,IAAW,cAAe,CAAEC,SAAUtB,MACxE,2BAAOc,KAAK,OACVJ,UAAWW,IAAW,eAAgB,CAAEC,SAAUtB,IAClDgB,SAAU,aACVD,MAAOP,EACPc,SAAUtB,EACVoB,aAAYd,EAAE,yBAOxBR,EAAW2B,UAAY,CACrBzB,WAAY0B,IAAUC,KACtB1B,gBAAiByB,IAAUE,OAC3B1B,YAAawB,IAAUG,UAAU,CAACH,IAAUE,OAASF,IAAUI,SAC/D3B,gBAAiBuB,IAAUE,OAC3BxB,mBAAoBsB,IAAUK,KAC9B1B,oBAAqBqB,IAAUK,MAGlBjC,Q,siCC3Cf,IAAMkC,EAAiB,CACrB,CAAEjB,MAAO,SAAUkB,MAAO,QAASC,YAAa,oBAChD,CAAEnB,MAAO,SAAUkB,MAAO,QAASC,YAAa,oBAChD,CAAEnB,MAAO,OAAQkB,MAAO,MAAOC,YAAa,kBAC5C,CAAEnB,MAAO,SAAUkB,MAAO,QAASC,YAAa,oBAChD,CAAEnB,MAAO,QAASkB,MAAO,OAAQC,YAAa,mBAC9C,CAAEnB,MAAO,QAASkB,MAAO,OAAQC,YAAa,mBAC9C,CAAEnB,MAAO,UAAWkB,MAAO,SAAUC,YAAa,qBAClD,CAAEnB,MAAO,SAAUkB,MAAO,QAASC,YAAa,qBAG5CC,EAAY9D,OAAO+D,KAAKC,kBAAkBC,0BAgDjCC,EA9CoB,WAEjC,IAAMtC,EAAkBuC,YAAYC,IAAUC,oBACxCxC,EAAcsC,YAAYC,IAAUE,gBACpCxC,EAAkBqC,YAAYC,IAAUG,oBAExCC,EAD4BL,YAAYC,IAAUK,gCACHX,EAAUY,UAEQ,IAAzBC,mBAAS/C,GAAgB,GAAhEgD,EAAe,KAAEC,EAAkB,KAyB1C,OAJAC,qBAAU,WACRD,EAAmBjD,KAClB,CAACA,IAGF,kBAAC,EAAU,CACTD,WAAY6C,EACZb,eAAgBA,EAChB/B,gBAAiBgD,EACjB/C,YAAaA,EACbC,gBAAiBA,EACjBC,mBA9BuB,SAACW,GAC1BmC,EAAmBnC,IA8BjBV,oBA3BwB,SAAC+C,GAC3B,GAAkB,UAAdA,EAAMC,IACR,IACE,IACMC,EADiBC,IAAKC,oBACqBC,cAAcC,+BACzDC,EAAYJ,IAAKK,aAAaR,EAAMlC,OAAOH,OACjDuC,EAA0BO,gBAAgBF,GAC1C,MAAO1C,GAGP,OAFAiC,EAAmBjD,QACnB6D,QAAQC,MAAM9C,QCzCPsB", "file": "chunks/chunk.64.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./FormulaBar.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".FormulaBar{display:flex;border:1px solid var(--gray-5);background-color:var(--gray-2);padding:8px;grid-gap:8px;gap:8px;border-bottom:1px solid #e0e0e0}.FormulaBar .RangeInput{padding:6px 8px}.FormulaBar .RangeInput.focus-visible,.FormulaBar .RangeInput:focus-visible{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula{display:flex;align-items:center;flex-grow:1;height:32px;position:relative;border:1px solid var(--border);border-radius:4px;background-color:var(--component-background);padding-left:30px}.FormulaBar .Formula.readOnly{border-color:var(--lighter-border)}.FormulaBar .Formula:not(.readOnly)[focus-within]{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula:not(.readOnly):focus-within{outline:var(--focus-visible-outline)!important}.FormulaBar .FormulaIcon{position:absolute;left:8px}.FormulaBar .FormulaIcon.readOnly{color:var(--disabled-icon)}.FormulaBar .FormulaInput{flex-grow:1;width:100%;padding:6px 8px;border:none;outline:none;font-size:14px;background-color:transparent}\", \"\"]);\n\n// exports\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Icon from 'components/Icon';\nimport './FormulaBar.scss';\nimport PropTypes from 'prop-types';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport classNames from 'classnames';\n\nconst FormulaBar = (props) => {\n  const { isReadOnly, activeCellRange, cellFormula, stringCellValue, onRangeInputChange, onRangeInputKeyDown } = props;\n\n  const { t } = useTranslation();\n\n  const formulaBarValue = cellFormula || stringCellValue || '';\n\n  return (\n    <DataElementWrapper className='FormulaBar' dataElement={DataElements.FORMULA_BAR}>\n      <input\n        type=\"text\"\n        className='RangeInput'\n        value={activeCellRange}\n        onChange={(e) => onRangeInputChange(e.target.value)}\n        onKeyDown={onRangeInputKeyDown}\n        aria-label={t('formulaBar.range')}\n      />\n      <div className={classNames('Formula', { readOnly: isReadOnly })}>\n        <Icon glyph=\"function\" className={classNames('FormulaIcon', { readOnly: isReadOnly })}/>\n        <input type=\"text\"\n          className={classNames('FormulaInput', { readOnly: isReadOnly })}\n          onChange={()=>{}}\n          value={formulaBarValue}\n          readOnly={isReadOnly}\n          aria-label={t('formulaBar.label')}\n        />\n      </div>\n    </DataElementWrapper>\n  );\n};\n\nFormulaBar.propTypes = {\n  isReadOnly: PropTypes.bool,\n  activeCellRange: PropTypes.string,\n  cellFormula: PropTypes.oneOfType([PropTypes.string , PropTypes.number]),\n  stringCellValue: PropTypes.string,\n  onRangeInputChange: PropTypes.func,\n  onRangeInputKeyDown: PropTypes.func,\n};\n\nexport default FormulaBar;", "import React, { useEffect, useState } from 'react';\nimport { useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport FormulaBar from './FormulaBar';\nimport core from 'core';\n\nconst formulaOptions = [\n  { value: '=SUMIF', label: 'SUMIF', description: 'formulaBar.sumif' },\n  { value: '=SUMSQ', label: 'SUMSQ', description: 'formulaBar.sumsq' },\n  { value: '=SUM', label: 'SUM', description: 'formulaBar.sum' },\n  { value: '=ASINH', label: 'ASINH', description: 'formulaBar.asinh' },\n  { value: '=ACOS', label: 'ACOS', description: 'formulaBar.acos' },\n  { value: '=COSH', label: 'COSH', description: 'formulaBar.cosh' },\n  { value: '=ISEVEN', label: 'ISEVEN', description: 'formulaBar.iseven' },\n  { value: '=ISODD', label: 'ISODD', description: 'formulaBar.isodd' },\n];\n\nconst EDIT_MODE = window.Core.SpreadsheetEditor.SpreadsheetEditorEditMode;\n\nexport const FormulaBarContainer = () => {\n  // This component can pull all Redux state and call any core methods\n  const activeCellRange = useSelector(selectors.getActiveCellRange);\n  const cellFormula = useSelector(selectors.getCellFormula);\n  const stringCellValue = useSelector(selectors.getStringCellValue);\n  const spreadsheetEditorEditMode = useSelector(selectors.getSpreadsheetEditorEditMode);\n  const isReadOnlyMode = spreadsheetEditorEditMode === EDIT_MODE.VIEW_ONLY;\n\n  const [rangeInputValue, setRangeInputValue] = useState(activeCellRange);\n\n  const onRangeInputChange = (value) => {\n    setRangeInputValue(value);\n  };\n\n  const onRangeInputKeyDown = (event) => {\n    if (event.key === 'Enter') {\n      try {\n        const documentViewer = core.getDocumentViewer();\n        const spreadsheetEditorDocument = documentViewer.getDocument().getSpreadsheetEditorDocument();\n        const cellRange = core.getCellRange(event.target.value);\n        spreadsheetEditorDocument.selectCellRange(cellRange);\n      } catch (e) {\n        setRangeInputValue(activeCellRange);\n        console.error(e);\n        return;\n      }\n    }\n  };\n\n  useEffect(() => {\n    setRangeInputValue(activeCellRange);\n  }, [activeCellRange]);\n\n  return (\n    <FormulaBar\n      isReadOnly={isReadOnlyMode}\n      formulaOptions={formulaOptions}\n      activeCellRange={rangeInputValue}\n      cellFormula={cellFormula}\n      stringCellValue={stringCellValue}\n      onRangeInputChange={onRangeInputChange}\n      onRangeInputKeyDown={onRangeInputKeyDown}\n    />\n  );\n};\n\nexport default FormulaBarContainer;", "import FormulaBarContainer from './FormulaBarContainer';\nexport default FormulaBarContainer;"], "sourceRoot": ""}