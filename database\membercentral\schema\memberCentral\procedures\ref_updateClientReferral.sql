ALTER PROCEDURE dbo.ref_updateClientReferral
@referralID int,
@clientReferralID int,
@clientID int,
@repID int,
@firstName varchar(75),
@middleName varchar(25),
@lastName varchar(75),
@businessName varchar(100),
@address1 varchar(100),
@address2 varchar(100),
@city varchar(100),
@state int,
@postalCode varchar(25),
@countryID int,
@email varchar(255),
@homePhone varchar(40),
@cellPhone varchar(40),
@alternatePhone varchar(40),
@homePhoneE164 varchar(40),
@cellPhoneE164 varchar(40),
@alternatePhoneE164	varchar(40),
@clientParentID int,
@repFirstName varchar(75),
@repLastName varchar(75),
@repAddress1 varchar(100),
@repAddress2 varchar(100),
@repCity varchar(100),
@repState int,
@repPostalCode varchar(25),
@repEmail varchar(255),
@repHomePhone varchar(40),
@repCellPhone varchar(40),
@repAlternatePhone varchar(40),
@repHomePhoneE164 varchar(40),
@repCellPhoneE164 varchar(40),
@repAlternatePhoneE164	varchar(40),
@relationToClient varchar(100),
@repParentID int,
@sourceID int,
@otherSource varchar(100),
@communicateLanguageID int,
@issueDesc varchar(max),
@agencyID int,
@caseFeeTypeID int,
@callTypeID int,
@sendSurvey	bit,
@sendNewsBlog bit,
@statusID int,
@panelID int,
@counselorMemberID int,
@updateMode varchar(5),
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	CREATE TABLE #tmpAuditLogData (
		[rowCode] varchar(20) PRIMARY KEY, [First Name] varchar(max), [Middle Name] varchar(max), [Last Name] varchar(max),
		[Business] varchar(max), [Address 1] varchar(max), [Address 2] varchar(max), [City] varchar(max), [State] varchar(max),
		[Zip Code] varchar(max), [E-mail] varchar(max), [Home Phone] varchar(max), [Cell Phone] varchar(max), [Alternate Phone] varchar(max),
		[Home Phone E164] varchar(max), [Cell Phone E164] varchar(max), [Alternate Phone E164] varchar(max),
		[Rep First Name] varchar(max), [Rep Last Name] varchar(max), [Rep Relationship to Client] varchar(max), [Rep Address 1] varchar(max),
		[Rep Address 2] varchar(max), [Rep City] varchar(max), [Rep State] varchar(max), [Rep Zip Code] varchar(max), [Rep E-mail] varchar(max),
		[Rep Home Phone] varchar(max), [Rep Cell Phone] varchar(max), [Rep Alternate Phone] varchar(max),
		[Rep Home Phone E164] varchar(max), [Rep Cell Phone E164] varchar(max), [Rep Alternate Phone E164] varchar(max),
		[Source] varchar(max), [Language] varchar(max), [Issue Description] varchar(max), [Counselor] varchar(max), [Call Type] varchar(max),
		[Agency] varchar(max), [Case Fee Type] varchar(max), [Send Survey] varchar(max), [Send News Blog] varchar(max)
	);

	DECLARE @orgID int, @siteID int, @crlf varchar(10), @mainMessage varchar(max), @msgjson varchar(max), @nowDate datetime = getdate(),
		@newClientRecord bit = 0, @newRepRecord bit = 0;

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Source], [Language], [Issue Description], [Counselor], [Call Type],
		[Agency], [Case Fee Type], [Send Survey], [Send News Blog]
	)
	SELECT 'OLDVAL', src.clientReferralSource, rl.languageName, r.issueDesc, counselor.firstName + ' ' + counselor.lastName,
		rt.clientReferralType, a.[name], ft.feeTypeName, ISNULL(r.sendSurvey,0), ISNULL(r.sendNewsBlog,0)
	FROM dbo.ref_clientReferrals AS r
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS src ON src.clientReferralSourceID = r.sourceID
	LEFT OUTER JOIN dbo.ref_languages AS rl ON rl.languageID = r.communicateLanguageID
	LEFT OUTER JOIN dbo.ams_members AS counselor ON counselor.memberID = r.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS rt ON rt.referralID = @referralID
		AND rt.clientReferralTypeID = r.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.referralID = @referralID
		AND a.agencyID = r.agencyID
	LEFT OUTER JOIN dbo.ref_clientReferralFeeTypes AS ft ON ft.feeTypeID = r.feeTypeID
	WHERE r.clientReferralID = @clientReferralID;

	INSERT INTO #tmpAuditLogData (
		[rowCode], [First Name], [Middle Name], [Last Name], [Business], [Address 1], [Address 2], [City],
		[State], [Zip Code], [E-mail], [Home Phone], [Cell Phone], [Alternate Phone],[Home Phone E164], [Cell Phone E164], [Alternate Phone E164],
		[Rep First Name], [Rep Last Name], [Rep Relationship to Client], [Rep Address 1], [Rep Address 2],
		[Rep City], [Rep State], [Rep Zip Code], [Rep E-mail], [Rep Home Phone], [Rep Cell Phone], [Rep Alternate Phone],
		[Rep Home Phone E164], [Rep Cell Phone E164], [Rep Alternate Phone E164],
		[Source], [Language], [Issue Description], [Counselor], [Call Type], [Agency], [Case Fee Type], [Send Survey],
		[Send News Blog]
	)
	VALUES (
		'DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING',
		'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING','STRING', 'STRING', 'STRING','STRING',
		'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING','STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'BIT', 'BIT'
	);

	-- compare client info only if existing record is updated
	IF ISNULL(@clientID,0) > 0 BEGIN
		UPDATE tmp
		SET tmp.[First Name] = c.firstName, tmp.[Middle Name] = c.middleName,  tmp.[Last Name] = c.lastName, tmp.[Business] = c.businessName,
			tmp.[Address 1] = c.address1, tmp.[Address 2] = c.address2, tmp.[City] = c.city, tmp.[State] = cs.[Name],
			tmp.[Zip Code] = c.postalCode, tmp.[E-mail] = c.email, tmp.[Home Phone] = c.homePhone, tmp.[Cell Phone] = c.cellPhone,
			tmp.[Alternate Phone] = c.alternatePhone, tmp.[Home Phone E164] = c.homePhoneE164, tmp.[Cell Phone E164] = c.cellPhoneE164,
			tmp.[Alternate Phone E164] = c.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS c ON c.clientID = @clientID
			AND c.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = c.[state]
		WHERE tmp.[rowCode] = 'OLDVAL';
	END

	-- compare rep info only if existing record is updated
	IF ISNULL(@repID,0) > 0 BEGIN
		UPDATE tmp
		SET tmp.[Rep First Name] = rep.firstName, tmp.[Rep Last Name] = rep.lastName, tmp.[Rep Relationship to Client] = rep.relationToClient,
			tmp.[Rep Address 1] = rep.address1, tmp.[Rep Address 2] = rep.address2, tmp.[Rep City] = rep.city, tmp.[Rep State] = cs.[Name],
			tmp.[Rep Zip Code] = rep.postalCode, tmp.[Rep E-mail] = rep.email, tmp.[Rep Home Phone] = rep.homePhone, tmp.[Rep Cell Phone] = rep.cellPhone,
			tmp.[Rep Alternate Phone] = rep.alternatePhone, tmp.[Rep Home Phone E164] = rep.homePhoneE164, tmp.[Rep Cell Phone E164] = rep.cellPhoneE164,
			tmp.[Rep Alternate Phone E164] = rep.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS rep ON rep.clientID = @repID
			AND rep.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = rep.[state]
		WHERE tmp.[rowCode] = 'OLDVAL';
	END

	BEGIN TRAN;

		IF ISNULL(@clientID,0) > 0 BEGIN
			UPDATE dbo.ref_clients
			SET firstName = @firstName,
				middleName = @middleName,
				lastName = @lastName,
				businessName = @businessName,
				address1 = @address1,
				address2 = @address2,
				city = @city,
				[state] = @state,
				postalCode = @postalCode,
				email = @email,
				homePhone = @homePhone,
				cellPhone = @cellPhone,
				alternatePhone = @alternatePhone,
				homePhoneE164 = @homePhoneE164,
				cellPhoneE164 = @cellPhoneE164,
				alternatePhoneE164 = @alternatePhoneE164,
				clientParentID = @clientParentID
			WHERE clientID = @clientID;
		END
		ELSE BEGIN
			EXEC dbo.ref_createClient @referralID=@referralID, @firstName=@firstName, @middleName=@middleName,
				@lastName=@lastName, @businessName=@businessName, @address1=@address1, @address2=@address2,
				@address3=NULL, @city=@city, @state=@state, @postalCode=@postalCode, @countryID=@countryID,
				@email=@email, @homePhone=@homePhone, @cellPhone=@cellPhone, @alternatePhone=@alternatePhone,
				@homePhoneE164 =@homePhoneE164,@cellPhoneE164=@cellPhoneE164,@alternatePhoneE164=@alternatePhoneE164,
				@typeID=1, @createdBy=@enteredByMemberID, @clientParentID=@clientParentID,
				@relationToClient=NULL, @clientID=@clientID OUTPUT;

			SET @newClientRecord = 1;
		END

		IF ISNULL(@repID,0) = 0 AND LEN(@repFirstName) > 0 AND LEN(@repLastName) > 0 AND (
			LEN(@repHomePhone) > 0 OR LEN(@repCellPhone) > 0 OR LEN(@repAlternatePhone) > 0 OR LEN(@repEmail) > 0
		) BEGIN
			EXEC dbo.ref_createClient @referralID	= @referralID, @firstName = @repFirstName,
				@middleName	= NULL, @lastName = @repLastName, @businessName = NULL, @address1 = @repAddress1,
				@address2 = @repAddress2, @address3 = NULL, @city = @repCity, @state = @repState,
				@postalCode = @repPostalCode, @countryID = NULL, @email = @repEmail, @homePhone = @repHomePhone,
				@cellPhone = @repCellPhone, @alternatePhone = @repAlternatePhone, 
				@homePhoneE164 =@repHomePhoneE164,@cellPhoneE164=@repCellPhoneE164,@alternatePhoneE164=@repAlternatePhoneE164,
				@typeID = 2, @createdBy = @enteredByMemberID,
				@clientParentID = @repParentID, @relationToClient = @relationToClient, @clientID = @repID OUTPUT;

			SET @newRepRecord = 1;
		END
		ELSE IF ISNULL(@repID,0) > 0 BEGIN
			UPDATE dbo.ref_clients
			SET firstName = @repFirstName,
				lastName = @repLastName,
				businessName = @businessName,
				address1 = @repAddress1,
				address2 = @repAddress2,
				city = @repCity,
				[state] = @repState,
				postalCode = @repPostalCode,
				email = @repEmail,
				homePhone = @repHomePhone,
				cellPhone = @repCellPhone,
				alternatePhone = @repAlternatePhone,
				homePhoneE164 = @repHomePhoneE164,
				cellPhoneE164 = @repCellPhoneE164,
				alternatePhoneE164 = @repAlternatePhoneE164,
				relationToClient = @relationToClient,
				clientParentID = @repParentID
			WHERE clientID = @repID;
		END

		IF @updateMode IN ('CP','API')
			UPDATE dbo.ref_clientReferrals
			SET representativeID = NULLIF(@repID,0),
				sourceID = @sourceID,
				otherSource = @otherSource,
				communicateLanguageID = @communicateLanguageID,
				issueDesc = @issueDesc,
				agencyID = @agencyID,
				feeTypeID = @caseFeeTypeID,
				sendSurvey = @sendSurvey,
				statusID = CASE WHEN @statusID IS NOT NULL THEN @statusID ELSE statusID END,
				typeID = @callTypeID,
				sendNewsBlog = @sendNewsBlog,
				panelID = CASE WHEN @panelID IS NOT NULL THEN @panelID ELSE panelID END,
				enteredByMemberID = CASE WHEN @counselorMemberID IS NOT NULL THEN @counselorMemberID ELSE enteredByMemberID END,
				lastUpdatedBy = @enteredByMemberID,
				dateLastUpdated = @nowDate
			WHERE clientReferralID = @clientReferralID;
		ELSE
			UPDATE dbo.ref_clientReferrals
			SET clientID = @clientID,
				representativeID = NULLIF(@repID,0),
				sourceID = @sourceID,
				otherSource = @otherSource,
				communicateLanguageID = @communicateLanguageID,
				issueDesc = @issueDesc,
				feeTypeID = CASE WHEN ISNULL(@caseFeeTypeID,0) > 0 THEN @caseFeeTypeID ELSE feeTypeID END,
				sendSurvey = @sendSurvey,
				statusID = CASE WHEN @statusID IS NOT NULL THEN @statusID ELSE statusID END,
				sendNewsBlog = @sendNewsBlog,
				lastUpdatedBy = @enteredByMemberID,
				dateLastUpdated = @nowDate
			WHERE clientReferralID = @clientReferralID;

	COMMIT TRAN

	INSERT INTO #tmpAuditLogData (
		[rowCode], [Source], [Language], [Issue Description], [Counselor], [Call Type],
		[Agency], [Case Fee Type], [Send Survey], [Send News Blog]
	)
	SELECT 'NEWVAL', src.clientReferralSource, rl.languageName, r.issueDesc, counselor.firstName + ' ' + counselor.lastName,
		rt.clientReferralType, a.[name], ft.feeTypeName, ISNULL(r.sendSurvey,0), ISNULL(r.sendNewsBlog,0)
	FROM dbo.ref_clientReferrals AS r
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS src ON src.clientReferralSourceID = r.sourceID
	LEFT OUTER JOIN dbo.ref_languages AS rl ON rl.languageID = r.communicateLanguageID
	LEFT OUTER JOIN dbo.ams_members AS counselor ON counselor.memberID = r.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS rt ON rt.referralID = @referralID
		AND rt.clientReferralTypeID = r.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.referralID = @referralID
		AND a.agencyID = r.agencyID
	LEFT OUTER JOIN dbo.ref_clientReferralFeeTypes AS ft ON ft.feeTypeID = r.feeTypeID
	WHERE r.clientReferralID = @clientReferralID;

	IF ISNULL(@clientID,0) > 0 AND @newClientRecord = 0 BEGIN
		UPDATE tmp
		SET tmp.[First Name] = c.firstName, tmp.[Middle Name] = c.middleName,  tmp.[Last Name] = c.lastName, tmp.[Business] = c.businessName,
			tmp.[Address 1] = c.address1, tmp.[Address 2] = c.address2, tmp.[City] = c.city, tmp.[State] = cs.[Name],
			tmp.[Zip Code] = c.postalCode, tmp.[E-mail] = c.email, tmp.[Home Phone] = c.homePhone, tmp.[Cell Phone] = c.cellPhone,
			tmp.[Alternate Phone] = c.alternatePhone, tmp.[Home Phone E164] = c.homePhoneE164, tmp.[Cell Phone E164] = c.cellPhoneE164,
			tmp.[Alternate Phone E164] = c.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS c ON c.clientID = @clientID
			AND c.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = c.[state]
		WHERE tmp.[rowCode] = 'NEWVAL';
	END
	
	IF ISNULL(@repID,0) > 0 AND @newRepRecord = 0 BEGIN
		UPDATE tmp
		SET tmp.[Rep First Name] = rep.firstName, tmp.[Rep Last Name] = rep.lastName, tmp.[Rep Relationship to Client] = rep.relationToClient,
			tmp.[Rep Address 1] = rep.address1, tmp.[Rep Address 2] = rep.address2, tmp.[Rep City] = rep.city, tmp.[Rep State] = cs.[Name],
			tmp.[Rep Zip Code] = rep.postalCode, tmp.[Rep E-mail] = rep.email, tmp.[Rep Home Phone] = rep.homePhone, tmp.[Rep Cell Phone] = rep.cellPhone,
			tmp.[Rep Alternate Phone] = rep.alternatePhone, tmp.[Rep Home Phone E164] = rep.homePhoneE164, tmp.[Rep Cell Phone E164] = rep.cellPhoneE164,
			tmp.[Rep Alternate Phone E164] = rep.alternatePhoneE164
		FROM #tmpAuditLogData AS tmp
		INNER JOIN dbo.ref_clients AS rep ON rep.clientID = @repID
			AND rep.referralID = @referralID
		LEFT OUTER JOIN dbo.ams_states AS cs ON cs.stateID = rep.[state]
		WHERE tmp.[rowCode] = 'NEWVAL';
	END

	EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @outputAsChangesArray=1, @msg=@msgjson OUTPUT;

	
	IF @newClientRecord = 1
		SET @mainMessage = COALESCE(@mainMessage + @crlf, '') + 'Added New Client Record: ' + @firstName + ' ' + @lastName;
	IF @newRepRecord = 1
		SET @mainMessage = COALESCE(@mainMessage + @crlf, '') + 'Added Representative: ' + @repFirstName + ' ' + @repLastName;

	-- audit log
	IF @msgjson <> '' OR ISNULL(@mainMessage,'') <> '' BEGIN
		SET @mainMessage = 'Referral Updated (' + CASE @updateMode WHEN 'CP' THEN 'Admin' WHEN 'API' THEN 'API' WHEN 'FE' THEN 'Member' ELSE '' END + ')'
			+ COALESCE(@crlf + @mainMessage, '');
		SET @mainMessage = STRING_ESCAPE(@mainMessage,'json');
		SET @msgjson = '[' + @msgjson + ']';
		EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
			@message=@mainMessage, @changesArray=@msgjson, @enteredByMemberID=@enteredByMemberID;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
