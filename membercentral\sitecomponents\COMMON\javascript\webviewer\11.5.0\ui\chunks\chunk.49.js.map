{"version": 3, "sources": ["webpack:///./src/ui/src/components/CreatePortfolioModal/PortfolioItemGrid.scss?9701", "webpack:///./src/ui/src/components/CreatePortfolioModal/PortfolioItemGrid.scss", "webpack:///./src/ui/src/components/CreatePortfolioModal/CreatePortfolioModal.scss?95eb", "webpack:///./src/ui/src/components/CreatePortfolioModal/CreatePortfolioModal.scss", "webpack:///./src/ui/src/components/CreatePortfolioModal/PortfolioItemPreview.js", "webpack:///./src/ui/src/components/CreatePortfolioModal/PortfolioItemGrid.js", "webpack:///./src/ui/src/components/CreatePortfolioModal/CreatePortfolioModal.js", "webpack:///./src/ui/src/components/CreatePortfolioModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "item", "PropTypes", "object", "loadAsPDF", "PortfolioItemPreview", "canvasContainer", "useRef", "useState", "showIcon", "setShowIcon", "t", "useTranslation", "useEffect", "requestId", "core", "createDocument", "getPageCount", "loadThumbnail", "canvas", "canvasContainer<PERSON><PERSON><PERSON>", "current", "clientWidth", "canvasContainerHeight", "clientHeight", "canvasWidth", "width", "canvasHeight", "height", "ratio", "Math", "min", "style", "setAttribute", "name", "fn", "cancelLoadThumbnail", "className", "ref", "Icon", "glyph", "memo", "PortfolioItemGrid", "items", "onDeleteItem", "onDropItems", "onDragEnter", "e", "preventDefault", "stopPropagation", "onDragOver", "onDrop", "map", "index", "key", "title", "<PERSON><PERSON>", "img", "onClick", "CreatePortfolioModal", "dispatch", "useDispatch", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "CREATE_PORTFOLIO_MODAL", "isElementOpen", "getIsMultiTab", "getTabManager", "shallowEqual", "isDisabled", "isOpen", "isMultiTab", "tabManager", "setItems", "fileInputRef", "closeModal", "actions", "closeElement", "closeCreatePortfolioModalAfterCreate", "useFocusOnClose", "onFilesSelected", "files", "create", "useCallback", "createPortfolio", "pdfDoc", "Blob", "saveM<PERSON>ory<PERSON>uffer", "type", "blob", "addTab", "setActive", "extension", "loadDocument", "addFiles", "Array", "from", "conflictItem", "find", "file", "warning", "message", "fileName", "confirmBtnText", "onConfirm", "openFilePicker", "showWarningMessage", "concat", "click", "modalClass", "classNames", "data-element", "ModalWrapper", "closehandler", "onCloseClick", "swipeToClose", "FilePicker", "onChange", "allowMultiple", "splice", "dataTransfer", "label", "PORTFOLIO_MODAL_ADD_ITEM_TRIGGER", "disabled", "multiple", "display", "event", "target", "value"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEzB5B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,qjCAAsjC,M,qBCL/kC,IAAIL,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,ovUAAqvU,KAG9wU0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,ysBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4YAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAKA,IAAM4B,EAAY,CAChBC,K,OAAMC,EAAUC,QAEZ9B,EAAU,CAAE+B,WAAW,GAEvBC,EAAuB,SAAH,GAAiB,IAAXJ,EAAI,EAAJA,KACxBK,EAAkBC,mBACuB,IAAfC,oBAAS,GAAM,GAAxCC,EAAQ,KAAEC,EAAW,KACpBC,EAAMC,cAAND,EAwCR,OAtCAE,qBAAU,WACR,IAAIpC,EACAqC,EA+BJ,OA7BQ,iBAnBZ,iMAmBY,WAAG,uGAEUC,IAAKC,eAAef,EAAM5B,GAAQ,OACV,GADzCI,EAAW,EAAH,OACUA,EAASwC,eACX,GAAC,gBACG,OAAlBP,GAAY,GAAM,2CAGFjC,EAASyC,cAAc,GAAG,SAACC,GAAW,MAChDC,EAAuBd,EAAgBe,QAAQC,YAC/CC,EAAwBjB,EAAgBe,QAAQG,aAChDC,EAAcN,EAAOO,MACrBC,EAAeR,EAAOS,OAC5B,GAAIR,EAAuBK,GAAeF,EAAwBI,EAAc,CAC9E,IAAME,EAAQC,KAAKC,IAAIX,EAAuBK,EAAaF,EAAwBI,GACnFR,EAAOa,MAAMN,MAAQ,GAAH,OAAMD,EAAcI,EAAK,MAC3CV,EAAOa,MAAMJ,OAAS,GAAH,OAAMD,EAAeE,EAAK,MAE/CV,EAAOc,aAAa,OAAQ,OAC5Bd,EAAOc,aAAa,aAAc,GAAF,OAAKtB,EAAE,iCAAgC,YAAIV,EAAKiC,OACzD,QAAvB,EAAA5B,EAAgBe,eAAO,OAAvB,EAAyB1C,YAAYwC,MACrC,QAbFL,EAAY,EAAH,uDAeTJ,GAAY,GAAM,0DAErB,kBAzBO,kCA2BRyB,GAEO,WACLrB,GAAarC,EAAS2D,oBAAoBtB,MAE3C,IAGD,yBAAKuB,UAAU,oBAAoBC,IAAKhC,GACrCG,GACC,kBAAC8B,EAAA,EAAI,CAACC,MAAM,0BAMpBnC,EAAqBL,UAAYA,EAElByC,qBAAKpC,GCtBLqC,G,QArCW,SAAH,GAA6C,IAAvCC,EAAK,EAALA,MAAOC,EAAY,EAAZA,aAAcC,EAAW,EAAXA,YAWhD,OACE,yBACER,UAAU,kBACVS,YAboB,SAACC,GACvBA,EAAEC,iBACFD,EAAEE,mBAYAC,WATmB,SAACH,GACtBA,EAAEC,iBACFD,EAAEE,mBAQAE,OAAQN,GAEPF,EAAMS,KAAI,SAACnD,EAAMoD,GAAK,OACrB,yBAAKhB,UAAU,iBAAiBiB,IAAKrD,EAAKiC,MACxC,yBAAKG,UAAU,0BACb,kBAAC,EAAoB,CAACpC,KAAMA,KAE9B,yBAAKoC,UAAU,sBAAsBkB,MAAOtD,EAAKiC,MAC9CjC,EAAKiC,MAER,kBAACsB,EAAA,EAAM,CACLC,IAAI,YACJC,QAAS,kBAAMd,EAAaS,IAC5BE,MAAM,yB,6xBClClB,8lGAAAnF,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,IAAAA,IAAA,ygBAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAgBA,IAgJeuF,EAhJc,WAC3B,IAAOhD,EAAqB,EAAhBC,cAAgB,GAApB,GACFgD,EAAWC,cAYD,IALZC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,kBAAkBF,EAAOG,IAAaC,wBAChDH,IAAUI,cAAcL,EAAOG,IAAaC,wBAC5CH,IAAUK,cAAcN,GACxBC,IAAUM,cAAcP,MACvBQ,KAAa,GATdC,EAAU,KACVC,EAAM,KACNC,EAAU,KACVC,EAAU,KAQ0B,IAAZnE,mBAAS,IAAG,GAA/BmC,EAAK,KAAEiC,EAAQ,KAEhBC,EAAetE,iBAAO,MAEtBuE,EAAa,WACjBlB,EAASmB,IAAQC,aAAad,IAAaC,0BAGvCc,EAAuCC,YAAgBJ,GAEvDK,EAAkB,SAACC,GACvBR,EAASQ,IAGLC,EAASC,sBAAW,YAAC,sGACJC,YAAgB5C,GAAM,OAA/B,GAAN6C,EAAS,EAAH,MACRd,EAAY,CAAF,gBACS,OADT,KACKe,KAAI,SAAQD,EAAOE,iBAAiB,GAAE,OAA7C,OAA6C,6BAAG,CAAEC,KAAM,mBAA5DC,EAAO,IAAI,EAAP,wBACJjB,EAAWkB,OAAOD,EAAM,CAC5BE,WAAW,EACXC,UAAW,QACX,gCAEFC,YAAapC,EAAU4B,GAAQ,QAEjCP,IAAuC,4CACtC,CAACtC,EAAO+B,EAAYC,IAEjBsB,EAAW,SAACb,GAChB,GAAIA,EAAMtG,OAAS,EAAG,CACpBsG,EAAQc,MAAMC,KAAKf,GACnB,IAAMgB,EAAezD,EAAM0D,MAAK,SAACpG,GAAI,OAAKmF,EAAMiB,MAAK,SAACC,GAAI,OAAKA,EAAKpE,OAASjC,EAAKiC,WAClF,GAAIkE,EAAc,CAChB,IAGMG,EAAU,CACdC,QAJc7F,EAAE,qCAAsC,CAAE8F,SAAUL,EAAalE,OAK/EqB,MAJY5C,EAAE,+BAKd+F,eAJqB/F,EAAE,sBAKvBgG,UAAW,kBAAMC,MAEnBhD,EAASmB,IAAQ8B,mBAAmBN,SAEpC3B,EAASjC,EAAMmE,OAAO1B,MAKtBwB,EAAiB,WAAM,MAC3B/B,SAAqB,QAAT,EAAZA,EAAcxD,eAAO,OAArB,EAAuB0F,SAanBC,EAAaC,IAAW,CAC5B,sBAAwB,EACxB,aAActE,EAAM7D,OAAS,IAG/B,OAAQ0F,IAAeC,EAAU,KAC/B,yBAAKpC,UAAW2E,EAAYE,eAAchD,IAAaC,wBACrD,kBAACgD,EAAA,EAAY,CACX1C,OAAQA,EACRlB,MAAO5C,EAAE,gCACTyG,aAActC,EACduC,aAAcvC,EACdwC,cAAY,GAEZ,yBAAKjF,UAAU,qBACM,IAAjBM,EAAM7D,QACN,yBAAKuD,UAAU,yBACb,kBAACkF,EAAA,EAAU,CACTC,SAAUrC,EACVhC,OAAQgC,EACRsC,eAAe,KAInB9E,EAAM7D,OAAS,GACf,kBAAC,EAAiB,CAChB6D,MAAOA,EACPC,aArCO,SAACS,GAClBV,EAAM+E,OAAOrE,EAAO,GACpBuB,EAAS,EAAIjC,KAoCHE,YAjCQ,SAACE,GACnBA,EAAEC,iBACFiD,EAASlD,EAAE4E,aAAavC,UAkClB,yBAAK/C,UAAU,YACf,yBAAKA,UAAU,UACb,6BACE,kBAACmB,EAAA,EAAM,CACLnB,UAAU,kBACVoB,IAAI,sBACJC,QAASkD,EACTgB,MAAOjH,EAAE,wBAEX,yBAAK0B,UAAU,mBAAmB6E,eAAchD,IAAa2D,oCAE/D,kBAACrE,EAAA,EAAM,CACLnB,UAAU,mBACVyF,SAA2B,IAAjBnF,EAAM7D,OAChB4E,QAAS2B,EACTuC,MAAOjH,EAAE,oBAGb,2BACE2B,IAAKuC,EACLkD,UAAQ,EACR/F,MAAO,CAAEgG,QAAS,QAClBrC,KAAK,OACL6B,SAAU,SAACS,GACThC,EAASgC,EAAMC,OAAO9C,OACtB6C,EAAMC,OAAOC,MAAQ,YCtJpBxE", "file": "chunks/chunk.49.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PortfolioItemGrid.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".portfolio-items{height:100%;overflow-y:auto;margin:16px;padding:16px;background-color:var(--faded-component-background);display:flex;flex-wrap:wrap;grid-gap:16px;gap:16px}.portfolio-items .portfolio-item{width:130px;height:185px;padding:12px 24px 8px;background-color:var(--component-background);border:1px solid var(--lighter-border);display:flex;flex-direction:column;align-items:center;border-radius:6px}.portfolio-items .portfolio-item .portfolio-item-preview{width:100%;height:104px;border:1px solid var(--gray-3)}.portfolio-items .portfolio-item .portfolio-item-preview .preview-container{width:100%;height:100%;display:flex;align-items:center;justify-content:center}.portfolio-items .portfolio-item .portfolio-item-preview .preview-container .Icon{color:var(--gray-6)}.portfolio-items .portfolio-item .portfolio-item-name{margin:8px 0;height:15px;font-size:13px;max-width:90px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;color:var(--gray-9)}.portfolio-items .portfolio-item .Button,.portfolio-items .portfolio-item .Button .Icon{width:28px;height:28px}\", \"\"]);\n\n// exports\n", "var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./CreatePortfolioModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.CreatePortfolioModal{visibility:visible}.closed.CreatePortfolioModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CreatePortfolioModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CreatePortfolioModal .content-container .footer .create-portfolio,.CreatePortfolioModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CreatePortfolioModal .content-container .footer .create-portfolio:disabled,.CreatePortfolioModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CreatePortfolioModal .content-container .footer .create-portfolio:disabled span,.CreatePortfolioModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CreatePortfolioModal .footer .modal-button.cancel:hover,.CreatePortfolioModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CreatePortfolioModal .footer .modal-button.cancel,.CreatePortfolioModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CreatePortfolioModal .footer .modal-button.cancel.disabled,.CreatePortfolioModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CreatePortfolioModal .footer .modal-button.cancel.disabled span,.CreatePortfolioModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CreatePortfolioModal .content-container .footer .add-item-option:hover{border:none;background:none;color:var(--blue-6)}.CreatePortfolioModal .content-container .footer .add-item-option:hover .Icon{color:var(--blue-6)}.CreatePortfolioModal .content-container .footer .add-item-option{border:none;background:none;color:var(--blue-5)}.CreatePortfolioModal .content-container .footer .add-item-option .Icon{color:var(--blue-5)}.CreatePortfolioModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CreatePortfolioModal .modal-container .wrapper .modal-content{padding:10px}.CreatePortfolioModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CreatePortfolioModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CreatePortfolioModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CreatePortfolioModal .footer .modal-button.confirm{margin-left:4px}.CreatePortfolioModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CreatePortfolioModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CreatePortfolioModal .footer .modal-button{padding:23px 8px}}.CreatePortfolioModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CreatePortfolioModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CreatePortfolioModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CreatePortfolioModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CreatePortfolioModal .swipe-indicator{width:32px}}.CreatePortfolioModal .content-container .tab-list{width:100%;height:28px;display:flex;border-radius:4px;color:var(--text-color)}.CreatePortfolioModal .content-container .tab-list .tab-options-button{background-color:transparent;text-align:center;vertical-align:middle;line-height:24px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;flex:1;border-radius:0;cursor:pointer}.CreatePortfolioModal .content-container .tab-list .tab-options-button:first-child{border-bottom-left-radius:4px;border-top-left-radius:4px}.CreatePortfolioModal .content-container .tab-list .tab-options-button:last-child{border-bottom-right-radius:4px;border-top-right-radius:4px}.CreatePortfolioModal .content-container .tab-list .tab-options-button:hover{background:var(--popup-button-hover)}.CreatePortfolioModal .content-container .tab-list .tab-options-button.selected{cursor:default}.CreatePortfolioModal .content-container .tab-list .tab-options-button.focus-visible,.CreatePortfolioModal .content-container .tab-list .tab-options-button:focus-visible{outline:var(--focus-visible-outline)}.CreatePortfolioModal .content-container .tab-panel{width:100%;display:flex;flex-direction:column;align-items:center}.CreatePortfolioModal .content-container .tab-panel.focus-visible,.CreatePortfolioModal .content-container .tab-panel:focus-visible{outline:var(--focus-visible-outline)!important}.CreatePortfolioModal .content-container{display:flex;flex-direction:column;justify-content:space-between;width:786px;height:604px;padding:0;border-radius:4px;overflow-y:visible;background:var(--component-background)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CreatePortfolioModal .content-container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CreatePortfolioModal .content-container{width:100%}}.CreatePortfolioModal .content-container .header{display:flex;justify-content:space-between;margin:16px;font-size:16px;font-weight:700;align-items:center;height:24px}.CreatePortfolioModal .content-container .header .Button{height:32px;width:32px}.CreatePortfolioModal .content-container .divider{height:1px;width:100%;background:var(--divider)}.CreatePortfolioModal .content-container .file-picker-container{width:100%;height:100%;padding:16px}.CreatePortfolioModal .content-container .footer{display:flex;padding:16px;align-items:center;justify-content:space-between;width:100%;margin:0}.CreatePortfolioModal .content-container .footer .add-item-option{visibility:hidden;display:flex;align-items:center;position:relative}.CreatePortfolioModal .content-container .footer .add-item-option.show-popup,.CreatePortfolioModal .content-container .footer .add-item-option.show-popup .add-item-icon,.CreatePortfolioModal .content-container .footer .add-item-option.show-popup .Button .Icon{color:var(--secondary-button-hover)}.CreatePortfolioModal .content-container .footer .add-item-option .add-item-icon{color:var(--secondary-button-text)}.CreatePortfolioModal .content-container .footer .add-item-option .add-item-trigger{width:1px;height:1px;visibility:hidden;position:absolute;left:100px;top:30px}.CreatePortfolioModal .content-container .footer .create-portfolio{min-width:72px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CreatePortfolioModal .content-container .footer .create-portfolio{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CreatePortfolioModal .content-container .footer .create-portfolio{font-size:13px}}.CreatePortfolioModal .content-container .footer .create-portfolio:enabled:hover{background:var(--primary-button-hover)}.CreatePortfolioModal .content-container .tab-list .tab-options-divider+.tab-options-button{border-left:none!important}.CreatePortfolioModal .content-container .tab-list .tab-options-button{border-top:1px solid var(--tab-border-color);border-bottom:1px solid var(--tab-border-color)}.CreatePortfolioModal .content-container .tab-list .tab-options-button:first-child{border-left:1px solid var(--tab-border-color)}.CreatePortfolioModal .content-container .tab-list .tab-options-button:last-child{border-right:1px solid var(--tab-border-color)}.CreatePortfolioModal .content-container .tab-list .tab-options-button:hover{background:var(--tab-background-color-hover);border-top:1px solid var(--tab-border-color-hover);border-bottom:1px solid var(--tab-border-color-hover);border-right:1px solid var(--tab-border-color-hover)}.CreatePortfolioModal .content-container .tab-list .tab-options-button:hover+button,.CreatePortfolioModal .content-container .tab-list .tab-options-button:hover+div{border-left:none}.CreatePortfolioModal .content-container .tab-list .tab-options-button.selected{background:var(--tab-color-selected);border:1px solid var(--tab-color-selected);color:var(--tab-text-color-selected)}.CreatePortfolioModal .content-container .tab-list .tab-options-button.selected+button,.CreatePortfolioModal .content-container .tab-list .tab-options-button.selected+div{border-left:none!important}.CreatePortfolioModal .content-container .tab-list .tab-options-button:not(.selected){border-right:1px solid var(--tab-border-color)}.CreatePortfolioModal.is-editing .content-container .header{margin-bottom:20px}.CreatePortfolioModal.is-editing .content-container .footer .add-item-option{visibility:visible;min-width:115px;padding:8px 16px}.CreatePortfolioModal.is-editing .content-container .footer .add-item-option .Icon{width:16px;height:16px;margin-right:4px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useRef, useEffect, memo } from 'react';\nimport core from 'core';\nimport Icon from 'components/Icon';\nimport { useTranslation } from 'react-i18next';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  item: PropTypes.object,\n};\nconst options = { loadAsPDF: true };\n\nconst PortfolioItemPreview = ({ item }) => {\n  const canvasContainer = useRef();\n  const [showIcon, setShowIcon] = useState(false);\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    let document;\n    let requestId;\n\n    const fn = async () => {\n      try {\n        document = await core.createDocument(item, options);\n        const pageCount = document.getPageCount();\n        if (pageCount < 1) {\n          setShowIcon(true);\n          return;\n        }\n        requestId = await document.loadThumbnail(1, (canvas) => {\n          const canvasContainerWidth = canvasContainer.current.clientWidth;\n          const canvasContainerHeight = canvasContainer.current.clientHeight;\n          const canvasWidth = canvas.width;\n          const canvasHeight = canvas.height;\n          if (canvasContainerWidth < canvasWidth || canvasContainerHeight < canvasHeight) {\n            const ratio = Math.min(canvasContainerWidth / canvasWidth, canvasContainerHeight / canvasHeight);\n            canvas.style.width = `${canvasWidth * ratio}px`;\n            canvas.style.height = `${canvasHeight * ratio}px`;\n          }\n          canvas.setAttribute('role', 'img');\n          canvas.setAttribute('aria-label', `${t('portfolio.portfolioPanelTitle')} ${item.name}`);\n          canvasContainer.current?.appendChild(canvas);\n        });\n      } catch (e) {\n        setShowIcon(true);\n      }\n    };\n\n    fn();\n\n    return () => {\n      requestId && document.cancelLoadThumbnail(requestId);\n    };\n  }, []);\n\n  return (\n    <div className=\"preview-container\" ref={canvasContainer}>\n      {showIcon && (\n        <Icon glyph=\"icon-portfolio-file\" />\n      )}\n    </div>\n  );\n};\n\nPortfolioItemPreview.propTypes = propTypes;\n\nexport default memo(PortfolioItemPreview);\n", "import React from 'react';\nimport Button from 'components/Button';\nimport PortfolioItemPreview from './PortfolioItemPreview';\n\nimport './PortfolioItemGrid.scss';\n\nconst PortfolioItemGrid = ({ items, onDeleteItem, onDropItems }) => {\n  const handleDragEnter = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  const handleDragOver = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n  };\n\n  return (\n    <div\n      className=\"portfolio-items\"\n      onDragEnter={handleDragEnter}\n      onDragOver={handleDragOver}\n      onDrop={onDropItems}\n    >\n      {items.map((item, index) => (\n        <div className=\"portfolio-item\" key={item.name}>\n          <div className=\"portfolio-item-preview\">\n            <PortfolioItemPreview item={item} />\n          </div>\n          <div className=\"portfolio-item-name\" title={item.name}>\n            {item.name}\n          </div>\n          <Button\n            img=\"ic-delete\"\n            onClick={() => onDeleteItem(index)}\n            title=\"action.delete\"\n          />\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default PortfolioItemGrid;\n", "import React, { useState, useRef, useCallback } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport FilePicker from 'components/FilePicker';\nimport PortfolioItemGrid from './PortfolioItemGrid';\nimport { createPortfolio } from 'helpers/portfolio';\nimport loadDocument from 'helpers/loadDocument';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './CreatePortfolioModal.scss';\n\nconst CreatePortfolioModal = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [\n    isDisabled,\n    isOpen,\n    isMultiTab,\n    tabManager,\n  ] = useSelector((state) => [\n    selectors.isElementDisabled(state, DataElements.CREATE_PORTFOLIO_MODAL),\n    selectors.isElementOpen(state, DataElements.CREATE_PORTFOLIO_MODAL),\n    selectors.getIsMultiTab(state),\n    selectors.getTabManager(state),\n  ], shallowEqual);\n\n  const [items, setItems] = useState([]);\n\n  const fileInputRef = useRef(null);\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.CREATE_PORTFOLIO_MODAL));\n  };\n\n  const closeCreatePortfolioModalAfterCreate = useFocusOnClose(closeModal);\n\n  const onFilesSelected = (files) => {\n    setItems(files);\n  };\n\n  const create = useCallback(async () => {\n    const pdfDoc = await createPortfolio(items);\n    if (isMultiTab) {\n      const blob = new Blob([await pdfDoc.saveMemoryBuffer(0)], { type: 'application/pdf' });\n      await tabManager.addTab(blob, {\n        setActive: true,\n        extension: 'pdf',\n      });\n    } else {\n      loadDocument(dispatch, pdfDoc);\n    }\n    closeCreatePortfolioModalAfterCreate();\n  }, [items, isMultiTab, tabManager]);\n\n  const addFiles = (files) => {\n    if (files.length > 0) {\n      files = Array.from(files);\n      const conflictItem = items.find((item) => files.find((file) => file.name === item.name));\n      if (conflictItem) {\n        const message = t('portfolio.fileAlreadyExistsMessage', { fileName: conflictItem.name });\n        const title = t('portfolio.fileAlreadyExists');\n        const confirmBtnText = t('portfolio.reselect');\n        const warning = {\n          message,\n          title,\n          confirmBtnText,\n          onConfirm: () => openFilePicker(),\n        };\n        dispatch(actions.showWarningMessage(warning));\n      } else {\n        setItems(items.concat(files));\n      }\n    }\n  };\n\n  const openFilePicker = () => {\n    fileInputRef?.current?.click();\n  };\n\n  const deleteItem = (index) => {\n    items.splice(index, 1);\n    setItems([...items]);\n  };\n\n  const onDropItems = (e) => {\n    e.preventDefault();\n    addFiles(e.dataTransfer.files);\n  };\n\n  const modalClass = classNames({\n    'CreatePortfolioModal': true,\n    'is-editing': items.length > 0\n  });\n\n  return (isDisabled || !isOpen) ? null : (\n    <div className={modalClass} data-element={DataElements.CREATE_PORTFOLIO_MODAL}>\n      <ModalWrapper\n        isOpen={isOpen}\n        title={t('portfolio.createPDFPortfolio')}\n        closehandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n      >\n        <div className=\"content-container\">\n          {(items.length === 0) && (\n            <div className='file-picker-container'>\n              <FilePicker\n                onChange={onFilesSelected}\n                onDrop={onFilesSelected}\n                allowMultiple={true}\n              />\n            </div>\n          )}\n          {(items.length > 0) && (\n            <PortfolioItemGrid\n              items={items}\n              onDeleteItem={deleteItem}\n              onDropItems={onDropItems}\n            />\n          )}\n          <div className=\"divider\"></div>\n          <div className=\"footer\">\n            <div>\n              <Button\n                className='add-item-option'\n                img='icon-portfolio-file'\n                onClick={openFilePicker}\n                label={t('portfolio.addFiles')}\n              />\n              <div className=\"add-item-trigger\" data-element={DataElements.PORTFOLIO_MODAL_ADD_ITEM_TRIGGER}></div>\n            </div>\n            <Button\n              className='create-portfolio'\n              disabled={items.length === 0}\n              onClick={create}\n              label={t('action.create')}\n            />\n          </div>\n          <input\n            ref={fileInputRef}\n            multiple\n            style={{ display: 'none' }}\n            type=\"file\"\n            onChange={(event) => {\n              addFiles(event.target.files);\n              event.target.value = null;\n            }}\n          />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default CreatePortfolioModal;\n", "import CreatePortfolioModal from './CreatePortfolioModal';\n\nexport default CreatePortfolioModal;\n"], "sourceRoot": ""}