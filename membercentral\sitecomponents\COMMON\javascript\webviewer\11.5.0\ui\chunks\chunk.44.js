(window.webpackJsonp = window.webpackJsonp || []).push([
	[44, 55],
	{
		1574: function (e, t, n) {
			"use strict";
			var o = n(454);
			t.a = o.a;
		},
		1647: function (e, t, n) {
			var o = n(30),
				r = n(1648);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, i);
			e.exports = r.locals || {};
		},
		1648: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.RichTextStyleEditor{margin-bottom:16px}.RichTextStyleEditor .menu-items{margin-bottom:8px!important}.RichTextStyleEditor .menu-items .icon-grid{padding-top:12px;grid-row-gap:12px;row-gap:12px}.RichTextStyleEditor .menu-items .icon-grid .row{padding-top:0}.RichTextStyleEditor .menu-items .icon-grid .row.isRedaction{padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox{padding-top:4px;padding-bottom:8px}.RichTextStyleEditor .menu-items .icon-grid .auto-size-checkbox .ui__choice__input__check--focus{outline:var(--focus-visible-outline)}.RichTextStyleEditor .Dropdown__wrapper{width:100%}.RichTextStyleEditor .Dropdown__wrapper .Dropdown{width:100%!important}.RichTextStyleEditor .Dropdown__wrapper .Dropdown__items{right:unset;width:100%!important}.RichTextStyleEditor .FontSizeDropdown{width:100%!important}.RichTextStyleEditor .ColorPalette{padding-bottom:8px}.RichTextStyleEditor .text-size-slider{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .RichTextStyleEditor .icon-grid{display:flex;flex-direction:column}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1707: function (e, t, n) {
			"use strict";
			n.r(t);
			n(36),
				n(46),
				n(52),
				n(19),
				n(90),
				n(290),
				n(353),
				n(446),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(15),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22),
				n(32),
				n(29),
				n(47),
				n(23),
				n(24),
				n(49),
				n(48),
				n(58),
				n(44);
			var o = n(0),
				r = n.n(o),
				i = n(6),
				l = n(3),
				a = n.n(l),
				c = n(1574),
				s = n(1),
				u = n(2),
				d = n(4),
				p = (n(1647), n(5)),
				y = n(300),
				m = n(1542),
				f = n(347);
			function S(e) {
				return (S =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function h() {
				return (h = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function g(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function b(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? g(Object(n), !0).forEach(function (t) {
								v(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: g(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function v(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== S(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== S(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === S(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function T(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return C(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return C(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function C(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var w = {
					annotation: a.a.object,
					editor: a.a.object,
					style: a.a.shape({
						TextColor: a.a.oneOfType([a.a.string, a.a.object]),
						RichTextStyle: a.a.any,
					}),
					isFreeTextAutoSize: a.a.bool,
					onFreeTextSizeToggle: a.a.func,
					onPropertyChange: a.a.func,
					onRichTextStyleChange: a.a.func,
					isRedaction: a.a.bool,
					isRichTextEditMode: a.a.bool,
					setIsRichTextEditMode: a.a.func,
					isWidget: a.a.bool,
				},
				x = function (e) {
					var t,
						n,
						l,
						a,
						S,
						g,
						C,
						w,
						x,
						E,
						O,
						A,
						P,
						k = e.annotation,
						j = e.editor,
						F = e.style,
						L = e.isFreeTextAutoSize,
						R = e.onFreeTextSizeToggle,
						N = e.onPropertyChange,
						I = e.onRichTextStyleChange,
						_ = e.isRichTextEditMode,
						D = e.setIsRichTextEditMode,
						M = e.isRedaction,
						z = e.isWidget,
						H = e.activeTool,
						W = T(
							Object(i.e)(function (e) {
								return [d.a.getFonts(e)];
							}, i.c),
							1,
						)[0],
						B = T(Object(o.useState)({}), 2),
						U = B[0],
						Y = B[1],
						V = Object(o.useRef)(null),
						q = Object(o.useRef)(null),
						K = Object(o.useRef)({}),
						$ = Object(i.d)(),
						X = Object(o.useRef)(),
						G = Object(o.useRef)();
					G.current = _;
					var J = T(Object(f.a)(), 1)[0];
					Object(o.useEffect)(function () {
						var e = function (e, t) {
								!e &&
									t &&
									V.current &&
									V.current.setSelection(t.index, t.length),
									e && V.current && Y(Q(e));
							},
							t = function () {
								var e;
								Y(
									Q(
										null === (e = V.current) || void 0 === e
											? void 0
											: e.getSelection(),
									),
								);
							};
						return (
							s.a.addEventListener("editorSelectionChanged", e),
							s.a.addEventListener("editorTextChanged", t),
							$(u.a.disableElements([p.a.ANNOTATION_STYLE_POPUP])),
							function () {
								s.a.removeEventListener("editorSelectionChanged", e),
									s.a.removeEventListener("editorTextChanged", t),
									$(u.a.enableElements([p.a.ANNOTATION_STYLE_POPUP]));
							}
						);
					}, []),
						Object(o.useEffect)(
							function () {
								var e;
								if (((V.current = j), (q.current = k), _ && k)) {
									var t,
										n,
										o,
										r,
										i,
										l,
										a = "solid";
									try {
										a =
											"dash" === k.Style
												? "".concat(k.Style, ",").concat(k.Dashes)
												: k.Style;
									} catch (e) {
										console.error(e);
									}
									var c = k.getRichTextStyle()[0];
									K.current = {
										Font: k.Font,
										FontSize: k.FontSize,
										TextAlign: k.TextAlign,
										TextVerticalAlign: k.TextVerticalAlign,
										bold:
											null !==
												(t =
													"bold" === (null == c ? void 0 : c["font-weight"])) &&
											void 0 !== t &&
											t,
										italic:
											null !==
												(n =
													"italic" ===
													(null == c ? void 0 : c["font-style"])) &&
											void 0 !== n &&
											n,
										underline:
											(null == c ||
											null === (o = c["text-decoration"]) ||
											void 0 === o
												? void 0
												: o.includes("underline")) ||
											(null == c ||
											null === (r = c["text-decoration"]) ||
											void 0 === r
												? void 0
												: r.includes("word")),
										strikeout:
											null !==
												(i =
													null == c ||
													null === (l = c["text-decoration"]) ||
													void 0 === l
														? void 0
														: l.includes("line-through")) &&
											void 0 !== i &&
											i,
										size: null == c ? void 0 : c["font-size"],
										font: null == c ? void 0 : c["font-family"],
										StrokeStyle: a,
										calculatedFontSize: k.getCalculatedFontSize(),
									};
								}
								Y(
									Q(
										null === (e = V.current) || void 0 === e
											? void 0
											: e.getSelection(),
									),
								),
									X.current &&
										(V.current.setSelection(X.current), (X.current = null));
							},
							[k, j, _],
						),
						Object(o.useEffect)(
							function () {
								var e = function () {
										(V.current = null), (q.current = null), D(!1);
									},
									t = function () {
										D(!0);
									};
								return (
									s.a.addEventListener("editorBlur", e),
									s.a.addEventListener("editorFocus", t),
									function () {
										s.a.removeEventListener("editorBlur", e),
											s.a.removeEventListener("editorFocus", t);
									}
								);
							},
							[$],
						);
					var Z,
						Q = function (e) {
							if (!e) return {};
							var t = V.current.getFormat(e.index, e.length);
							if ("string" == typeof t.color)
								t.color = new window.Core.Annotations.Color(t.color);
							else if (Array.isArray(t.color)) {
								var n = new window.Core.Annotations.Color(
									t.color[t.color.length - 1],
								);
								t.color = n;
							} else t.color || (t.color = q.current.TextColor);
							for (
								var o = 0, r = ["font", "size", "originalSize"];
								o < r.length;
								o++
							) {
								var i = r[o];
								t[i] && Array.isArray(t[i]) && (t[i] = void 0);
							}
							return t;
						},
						ee = function (e, t) {
							var n, o;
							"size" === e
								? null === (n = V.current) ||
									void 0 === n ||
									n.format("applyCustomFontSize", t)
								: null === (o = V.current) || void 0 === o || o.format(e, t);
							"color" === e && (t = new window.Core.Annotations.Color(t)),
								Y(b(b({}, U), {}, v({}, e, t)));
						},
						te = function (e, t) {
							if (G.current) {
								var n = V.current.getSelection(),
									o = n.index,
									r = n.length,
									i = q.current;
								(i[e] = t),
									V.current.blur(),
									("FontSize" !== e && "Font" !== e) || Object(m.a)(i),
									setTimeout(function () {
										(X.current = { index: o, length: r }),
											s.a
												.getAnnotationManager()
												.getEditBoxManager()
												.focusBox(i);
									}, 0);
							} else N(e, t);
						},
						ne = F.RichTextStyle,
						oe = {
							bold:
								null !==
									(t =
										"bold" ===
										(null == ne || null === (n = ne[0]) || void 0 === n
											? void 0
											: n["font-weight"])) &&
								void 0 !== t &&
								t,
							italic:
								null !==
									(l =
										"italic" ===
										(null == ne || null === (a = ne[0]) || void 0 === a
											? void 0
											: a["font-style"])) &&
								void 0 !== l &&
								l,
							underline:
								(null == ne ||
								null === (S = ne[0]) ||
								void 0 === S ||
								null === (g = S["text-decoration"]) ||
								void 0 === g
									? void 0
									: g.includes("underline")) ||
								(null == ne ||
								null === (C = ne[0]) ||
								void 0 === C ||
								null === (w = C["text-decoration"]) ||
								void 0 === w
									? void 0
									: w.includes("word")),
							strikeout:
								null !==
									(x =
										null == ne ||
										null === (E = ne[0]) ||
										void 0 === E ||
										null === (O = E["text-decoration"]) ||
										void 0 === O
											? void 0
											: O.includes("line-through")) &&
								void 0 !== x &&
								x,
							font:
								null == ne || null === (A = ne[0]) || void 0 === A
									? void 0
									: A["font-family"],
							size:
								null == ne || null === (P = ne[0]) || void 0 === P
									? void 0
									: P["font-size"],
							StrokeStyle: "solid",
						};
					(Z = b(b({}, F), oe)),
						_ &&
							k &&
							((K.current.bold = U.bold),
							(K.current.italic = U.italic),
							(K.current.underline = U.underline),
							(K.current.strikeout = U.strike),
							(K.current.quillFont = U.font || K.current.Font),
							(K.current.quillFontSize = U.originalSize || K.current.FontSize));
					var re = {
							fonts: W,
							onPropertyChange: te,
							properties: Z,
							stateless: !0,
							isFreeText: !M,
						},
						ie = {
							onRichTextStyleChange: function (e, t) {
								if (G.current) {
									var n = {
										"font-weight": "bold",
										"font-style": "italic",
										underline: "underline",
										"line-through": "strike",
										"font-family": "font",
										"font-size": "size",
									};
									if ("font-family" === e || "font-size" === e) {
										ee(n[e], t);
										var o = q.current;
										if (o.isAutoSized())
											s.a
												.getAnnotationManager()
												.getEditBoxManager()
												.resizeAnnotation(o);
									} else
										!(function (e) {
											return function () {
												var t = V.current.getSelection(),
													n = t.index,
													o = t.length;
												if (0 === o) {
													X.current = { index: n, length: o };
													var r = V.current.getSelection();
													(n = r.index), (o = r.length);
												}
												var i = V.current.getFormat(n, o);
												ee(e, !i[e]);
											};
										})(n[e])();
								} else I(e, t);
							},
							properties: _ ? K.current : Z,
							isFreeTextAutoSize: L,
							isRichTextEditMode: _,
							isRedaction: M,
							onFreeTextSizeToggle: R,
						},
						le = {
							onRichTextStyleChange: te,
							isFreeTextAutoSize: !1,
							isRichTextEditMode: !1,
							isRedaction: !1,
							isWidget: z,
						};
					return r.a.createElement(
						"div",
						{
							className: "RichTextStyleEditor",
							onMouseDown: function (e) {
								"touchstart" !== e.type && _ && e.preventDefault();
							},
						},
						r.a.createElement(
							"div",
							{ className: "menu-items" },
							r.a.createElement(y.a, h({}, re, z ? le : ie)),
						),
						r.a.createElement(c.a, {
							onColorChange: function (e) {
								!(function (e, t) {
									G.current ? ee("color", t.toHexString()) : N(e, t);
								})("TextColor", new window.Core.Annotations.Color(e));
							},
							color: _ ? U.color : F.TextColor,
							activeTool: H,
							type: "Text",
							ariaTypeLabel: J("option.stylePopup.textStyle"),
						}),
					);
				};
			x.propTypes = w;
			var E = r.a.memo(x);
			t.default = E;
		},
		1727: function (e, t, n) {
			var o = n(30),
				r = n(1728);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, i);
			e.exports = r.locals || {};
		},
		1728: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePicker{display:flex;flex-direction:column}.StylePicker .slider-property{font-size:14px;font-weight:700;margin-bottom:8px!important}.StylePicker .StyleOption{margin-bottom:16px}.StylePicker .StyleOption .styles-container .styles-title{margin:0 0 8px;font-size:14px;font-weight:700}.StylePicker .StyleOption .styles-container [data-element=borderStylePicker]{margin-top:8px}.StylePicker .StyleOption .slider:only-child{margin-bottom:0}.StylePicker .StyleOption .slider .slider-element-container{margin-left:-3px}.StylePicker .StyleOption:last-child{margin-bottom:0}.StylePicker .PanelSection~.PanelSection .CollapsibleSection>.collapsible-page-group-header{margin-top:16px}.StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px;padding:0;font-weight:700;height:31.5px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection>.collapsible-page-group-header>button{font-size:16px}}.StylePicker .PanelSection .CollapsibleSection:first-of-type{margin-bottom:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker .PanelSection .CollapsibleSection{border-bottom:none}}.StylePicker .PanelSection .panel-section-wrapper.Opacity{margin-top:16px}.StylePicker .PanelSection:first-child .panel-section-wrapper.Opacity{margin-top:0}.StylePicker .PanelSection .collapsible-page-group-header+.collapsible-content{margin-top:16px}.StylePicker .PanelSection .PanelSubsection{margin-bottom:12px}.StylePicker .PanelSection .PanelSubsection .menu-subtitle{font-size:14px;font-weight:700;margin-bottom:12px}.StylePicker .PanelSection .divider{background-color:var(--divider);width:100%;height:1px}.StylePicker .PanelSection .menu-items{margin-bottom:16px}.StylePicker .PanelSection .menu-items:only-child{margin-bottom:0}.StylePicker .PanelSection .menu-items .ColorPalette{margin-bottom:8px}.StylePicker .PanelSection .slider{margin-bottom:16px}.StylePicker .PanelSection .slider:last-child,.StylePicker .PanelSection:empty,.StylePicker .PanelSection:last-child{margin-bottom:0}.StylePicker .PanelSection .snapping-option{margin-top:16px}.StylePicker .spacer{width:100%}.StylePicker .Dropdown,.StylePicker .FontSizeDropdown,.StylePicker .overlay-text-input{height:32px}.StylePicker .overlay-text-input:focus{border-color:var(--blue-5)}.StylePicker .lineStyleContainer{margin-bottom:0!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle{display:flex;flex-direction:row;grid-column-gap:8px;-moz-column-gap:8px;column-gap:8px;justify-content:space-between}.StylePicker .lineStyleContainer .StylePicker-LineStyle div.Dropdown{width:100%!important}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items,.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__wrapper{width:100%}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image svg{width:35px;margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .linestyle-image.shift-alignment svg{margin-top:8px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .Dropdown__items .linestyle-image svg{width:45px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment{padding-top:0}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .linestyle-image.shift-alignment svg{margin-top:11px}.StylePicker .lineStyleContainer .StylePicker-LineStyle [data-element=middleLineStyleDropdown] .Dropdown__items{top:-197px}.StylePicker .lineStyleContainer .StylePicker-LineStyle .StyleOptions{max-width:80px}.StylePicker .lineStyleContainer .StylePicker-LineStyle>*{flex-grow:1;flex-basis:0}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePicker{padding:0 16px 16px;overflow:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePicker{padding:0 16px 16px;overflow:auto}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1729: function (e, t, n) {
			var o = n(30),
				r = n(1730);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, i);
			e.exports = r.locals || {};
		},
		1730: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.StylePanel{display:flex;flex-direction:column;background-color:var(--panel-background);padding-bottom:75px}.StylePanel .style-panel-header{font-size:16px;font-weight:700;margin-top:0;margin-bottom:16px}.StylePanel .label{padding-top:16px;font-size:14px;font-weight:700}.StylePanel .no-tool-selected{padding-top:36px;display:flex;flex-direction:column;align-items:center;flex:1 1 auto}.StylePanel .no-tool-selected .msg{padding-top:24px;font-size:13px;text-align:center}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .no-tool-selected .msg{line-height:15px;width:146px}}.StylePanel .no-tool-selected .empty-icon,.StylePanel .no-tool-selected .empty-icon svg{width:55px;height:56px}.StylePanel .no-tool-selected .empty-icon *{fill:var(--gray-6);color:var(--gray-6)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .StylePanel .style-panel-header{margin:0 16px 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel{width:100%;height:100%;padding-bottom:16px}.App.is-web-component:not(.is-in-desktop-only-mode) .StylePanel .style-panel-header{margin:0 16px 16px}}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1804: function (e, t, n) {
			"use strict";
			n.r(t);
			n(10),
				n(8),
				n(145),
				n(9),
				n(11),
				n(23),
				n(24),
				n(20),
				n(46),
				n(52),
				n(15),
				n(19),
				n(12),
				n(13),
				n(14),
				n(16),
				n(18);
			var o = n(0),
				r = n.n(o),
				i = n(6),
				l = n(4),
				a = n(1),
				c = (n(102), n(198), n(58), n(44), n(3)),
				s = n.n(c),
				u = (n(116), n(289), n(139), n(347)),
				d = (n(1727), n(1574)),
				p = n(449),
				y = n(5),
				m = n(417),
				f = n(57),
				S = n(33),
				h = n(293),
				g = n(241),
				b = n(2),
				v = function (e) {
					var t,
						n,
						o = e.Scale,
						l = e.Precision,
						c = e.isSnapModeEnabled,
						s = Object(i.d)(),
						u =
							(null === (t = a.a.getDocument()) || void 0 === t
								? void 0
								: t.getType()) === f.a.WEBVIEWER_SERVER &&
							a.a.getDocument().isWebViewerServerDocument(),
						d =
							(null === (n = a.a.getDocument()) || void 0 === n
								? void 0
								: n.getType().toLowerCase()) === f.a.PDF || u,
						p = o && l && d && a.a.isFullPDFEnabled();
					return r.a.createElement(
						r.a.Fragment,
						null,
						p &&
							r.a.createElement(
								"div",
								{ className: "snapping-option" },
								r.a.createElement(h.a, {
									dataElement: "measurementSnappingOption",
									id: "measurement-snapping",
									type: "checkbox",
									label: S.a.t("option.shared.enableSnapping"),
									checked: c,
									onChange: function (e) {
										if (a.a.isFullPDFEnabled()) {
											var t = e.target.checked,
												n = t
													? a.a.getDocumentViewer().SnapMode.e_DefaultSnapMode |
														a.a.getDocumentViewer().SnapMode.POINT_ON_LINE
													: null;
											Object(g.a)().forEach(function (e) {
												var o;
												null === (o = e.setSnapMode) ||
													void 0 === o ||
													o.call(e, n),
													s(
														b.a.setEnableSnapMode({
															toolName: e.name,
															isEnabled: t,
														}),
													);
											});
										}
									},
								}),
							),
					);
				};
			n(154), n(41), n(27), n(28), n(25), n(22);
			function T(e) {
				return (T =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function C(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== T(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== T(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === T(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			var w = window.Core,
				x = w.Tools,
				E = w.Annotations,
				O = x.ToolNames,
				A = function (e) {
					return [
						x.CountMeasurementCreateTool,
						x.FreeHandCreateTool,
						x.FreeHandHighlightCreateTool,
						x.ArcCreateTool,
						x.ArcMeasurementCreateTool,
						x.TextAnnotationCreateTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				P = function (e) {
					return [
						x.RectangleCreateTool,
						x.EllipseCreateTool,
						x.PolygonCreateTool,
						x.PolygonCloudCreateTool,
						x.EllipseMeasurementCreateTool,
						x.AreaMeasurementCreateTool,
						x.FreeTextCreateTool,
						x.CalloutCreateTool,
						x.RedactionCreateTool,
						x.TextFormFieldCreateTool,
						x.RadioButtonFormFieldCreateTool,
						x.CheckBoxFormFieldCreateTool,
						x.ListBoxFormFieldCreateTool,
						x.ComboBoxFormFieldCreateTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				k = function (e) {
					return [
						x.RubberStampCreateTool,
						x.StampCreateTool,
						x.EraserTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				j = function (e) {
					return [
						x.TextUnderlineCreateTool,
						x.TextHighlightCreateTool,
						x.TextSquigglyCreateTool,
						x.TextStrikeoutCreateTool,
						x.CountMeasurementCreateTool,
						x.RubberStampCreateTool,
						x.FileAttachmentCreateTool,
						x.StampCreateTool,
						x.StickyCreateTool,
						x.MarkInsertTextCreateTool,
						x.MarkReplaceTextCreateTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				F = function (e) {
					return [
						x.RubberStampCreateTool,
						x.StampCreateTool,
						x.EraserTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				L = function (e) {
					return [x.EllipseCreateTool, x.LineCreateTool].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				R = function (e) {
					return [
						x.RedactionCreateTool,
						x.EraserTool,
						x.TextFormFieldCreateTool,
						x.ListBoxFormFieldCreateTool,
						x.ComboBoxFormFieldCreateTool,
						x.SignatureFormFieldCreateTool,
						x.CheckBoxFormFieldCreateTool,
						x.RadioButtonFormFieldCreateTool,
					].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				N = function (e) {
					return [x.RedactionCreateTool].some(function (t) {
						return a.a.getTool(e) instanceof t;
					});
				},
				I = function (e, t) {
					var n = {
						AnnotationCreateRedaction: {
							Title: "component.redaction",
							StrokeColor: "stylePanel.headings.redactionMarkOutline",
							FillColor: "stylePanel.headings.redactionFill",
						},
					};
					return n[e] && n[e][t];
				},
				_ = function (e) {
					return e.length >= 1
						? Array.from(
								new Set(
									e.map(function (e) {
										return e.ToolName;
									}),
								),
							)
						: null;
				},
				D = function (e, t) {
					var n = _(e),
						o = (null == n ? void 0 : n.length) > 0 ? n[0] : t.name;
					return {
						toolName: o,
						isRedaction:
							(1 === (null == n ? void 0 : n.length) && n[0] === O.REDACTION) ||
							o === O.REDACTION,
						isStamp:
							(null == n ? void 0 : n.includes(O.STAMP)) || o === O.STAMP,
						isWidget:
							e.some(function (e) {
								return e instanceof E.WidgetAnnotation;
							}) ||
							(function (e) {
								return [
									O.TEXT_FORM_FIELD,
									O.LIST_BOX_FIELD,
									O.COMBO_BOX_FIELD,
								].includes(e);
							})(t.name),
						isInFormFieldCreationMode: a.a
							.getFormFieldCreationManager()
							.isInFormFieldCreationMode(),
						isFreeText: o === O.FREE_TEXT,
						activeTool: o,
						annotationTypes: n,
					};
				},
				M = function () {
					var e,
						t = Object(i.d)(),
						n = Object(i.e)(l.a.isSnapModeEnabled),
						o = Object(i.e)(function (e) {
							return l.a.isElementDisabled(e, y.a.STYLE_OPTION);
						}),
						r = Object(i.e)(function (e) {
							return l.a.isElementOpen(e, y.a.STROKE_STYLE_CONTAINER);
						}),
						a = Object(i.e)(function (e) {
							return l.a.isElementOpen(e, y.a.FILL_COLOR_CONTAINER);
						}),
						c = Object(i.e)(function (e) {
							return l.a.isElementOpen(e, y.a.OPACITY_CONTAINER);
						}),
						s = Object(i.e)(function (e) {
							return l.a.isElementOpen(e, y.a.RICH_TEXT_STYLE_CONTAINER);
						}),
						u =
							(C((e = {}), y.a.STROKE_STYLE_CONTAINER, r),
							C(e, y.a.FILL_COLOR_CONTAINER, a),
							C(e, y.a.OPACITY_CONTAINER, c),
							C(e, y.a.RICH_TEXT_STYLE_CONTAINER, s),
							e),
						d = function (e) {
							t(u[e] ? b.a.closeElement(e) : b.a.openElement(e));
						};
					return {
						isSnapModeEnabled: n,
						isStyleOptionDisabled: o,
						isStrokeStyleContainerActive: r,
						isFillColorContainerActive: a,
						isOpacityContainerActive: c,
						isTextStyleContainerActive: s,
						openTextStyleContainer: function () {
							t(b.a.openElements(y.a.RICH_TEXT_EDITOR)),
								d(y.a.RICH_TEXT_STYLE_CONTAINER);
						},
						openStrokeStyleContainer: function () {
							return d(y.a.STROKE_STYLE_CONTAINER);
						},
						openFillColorContainer: function () {
							return d(y.a.FILL_COLOR_CONTAINER);
						},
						openOpacityContainer: function () {
							return d(y.a.OPACITY_CONTAINER);
						},
					};
				},
				z = n(197),
				H = (n(36), n(78)),
				W = n(187);
			function B(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return U(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return U(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function U(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Y = W.d.concat(W.a),
				V = function (e) {
					var t = e.showFillColorAndCollapsablePanelSections,
						n = e.isStamp,
						o = e.onStrokeColorChange,
						i = e.onStyleChange,
						l = e.strokeColor,
						a = e.activeTool,
						c = e.hideStrokeDropdowns,
						s = e.hideStrokeSlider,
						p = e.strokethicknessComponent,
						y = e.showLineStyleOptions,
						m = e.renderSlider,
						f = e.strokeStyle,
						S = e.isInFormFieldCreationMode,
						h = e.isFreeText,
						g = e.onStartLineStyleChange,
						b = e.startingLineStyle,
						v = e.isStyleOptionDisabled,
						T = e.onStrokeStyleChange,
						C = e.strokeLineStyle,
						w = e.onEndLineStyleChange,
						x = e.endingLineStyle,
						E = e.openStrokeStyleContainer,
						O = e.isStrokeStyleContainerActive,
						A = e.hideCloudyLineStyle,
						P = B(Object(u.a)(), 1)[0],
						k = y ? "stylePanel.lineEnding.middle" : "stylePanel.borderStyle",
						j = r.a.createElement(
							"div",
							{ className: "panel-section-wrapper" },
							!n &&
								r.a.createElement(
									r.a.Fragment,
									null,
									r.a.createElement(
										"div",
										{ className: "menu-items" },
										r.a.createElement(d.a, {
											onColorChange: o,
											onStyleChange: i,
											color: l,
											activeTool: a,
											type: "Stroke",
											ariaTypeLabel: P("option.annotationColor.StrokeColor"),
										}),
									),
									!s && p && p,
									y &&
										r.a.createElement(
											"div",
											{ className: "StyleOption" },
											m("opacity"),
										),
									!!f &&
										!(S && !h) &&
										!c &&
										r.a.createElement(
											"div",
											{ className: "StyleOption" },
											r.a.createElement(
												"div",
												{ className: "styles-container lineStyleContainer" },
												r.a.createElement(
													"div",
													{ className: "styles-title" },
													P("option.styleOption.style"),
												),
												r.a.createElement(
													"div",
													{ className: "StylePicker-LineStyle" },
													y &&
														r.a.createElement(H.a, {
															id: "startLineStyleDropdown",
															translationPrefix: "stylePanel.lineEnding.start",
															className: "StylePicker-StartLineStyleDropdown",
															dataElement: "startLineStyleDropdown",
															images: W.c,
															onClickItem: g,
															currentSelectionKey: b,
															showLabelInList: !0,
														}),
													!v &&
														r.a.createElement(H.a, {
															id: "middleLineStyleDropdown",
															translationPrefix: k,
															className:
																"StylePicker-StrokeLineStyleDropdown".concat(
																	f && !y ? " StyleOptions" : "",
																),
															dataElement: "middleLineStyleDropdown",
															images: y || A ? W.d : Y,
															onClickItem: T,
															currentSelectionKey: C,
															showLabelInList: !0,
														}),
													y &&
														r.a.createElement(H.a, {
															id: "endLineStyleDropdown",
															translationPrefix: "stylePanel.lineEnding.end",
															className: "StylePicker-EndLineStyleDropdown",
															dataElement: "endLineStyleDropdown",
															images: W.b,
															onClickItem: w,
															currentSelectionKey: x,
															showLabelInList: !0,
														}),
												),
											),
										),
								),
						);
					return t
						? r.a.createElement(
								z.a,
								{
									header: P(
										I(a, "StrokeColor") || "option.annotationColor.StrokeColor",
									),
									headingLevel: 2,
									isInitiallyExpanded: !1,
									onToggle: E,
									shouldShowHeading: t,
									isExpanded: O || !t,
								},
								j,
							)
						: j;
				},
				q = V;
			function K(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return $(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return $(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function $(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			V.propTypes = {
				showFillColorAndCollapsablePanelSections: s.a.bool,
				isStamp: s.a.bool,
				onStrokeColorChange: s.a.func,
				onStyleChange: s.a.func,
				strokeColor: s.a.oneOfType([s.a.string, s.a.object]),
				activeTool: s.a.string,
				hideStrokeDropdowns: s.a.bool,
				hideStrokeSlider: s.a.bool,
				strokethicknessComponent: s.a.node,
				showLineStyleOptions: s.a.bool,
				renderSlider: s.a.func,
				strokeStyle: s.a.string,
				isInFormFieldCreationMode: s.a.bool,
				isFreeText: s.a.bool,
				onStartLineStyleChange: s.a.func,
				startingLineStyle: s.a.string,
				isStyleOptionDisabled: s.a.bool,
				onStrokeStyleChange: s.a.func,
				strokeLineStyle: s.a.string,
				onEndLineStyleChange: s.a.func,
				endingLineStyle: s.a.string,
				openStrokeStyleContainer: s.a.func,
				isStrokeStyleContainerActive: s.a.bool,
				hideCloudyLineStyle: s.a.bool,
			};
			var X = function e(t) {
					var n = t.showFillColorAndCollapsablePanelSections,
						o = t.shouldHideOpacitySlider,
						i = t.showLineStyleOptions,
						l = t.renderSlider,
						a = t.isOpacityContainerActive,
						c = t.openOpacityContainer,
						d = K(Object(u.a)(), 1)[0];
					e.propTypes = {
						showFillColorAndCollapsablePanelSections: s.a.bool,
						shouldHideOpacitySlider: s.a.bool,
						showLineStyleOptions: s.a.bool,
						renderSlider: s.a.func,
						isOpacityContainerActive: s.a.bool,
						openOpacityContainer: s.a.func,
					};
					var p = r.a.createElement(
						"div",
						{ className: "panel-section-wrapper Opacity" },
						!i &&
							!o &&
							r.a.createElement(
								"div",
								{ className: "StyleOption" },
								l("opacity", n),
							),
					);
					return !n || o
						? p
						: r.a.createElement(
								z.a,
								{
									header: d("option.slider.opacity"),
									headingLevel: 2,
									isInitiallyExpanded: !1,
									isExpanded: a || !n,
									onToggle: c,
								},
								p,
							);
				},
				G = (n(32), n(29), n(47), n(49), n(48), n(50)),
				J = n(159),
				Z = n(200),
				Q = n(1542),
				ee = n(1564),
				te = n(458),
				ne = n(199),
				oe = n(73);
			function re(e) {
				return (re =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function ie(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function le(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? ie(Object(n), !0).forEach(function (t) {
								ae(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: ie(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function ae(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== re(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== re(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === re(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function ce(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return se(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return se(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function se(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var ue = window.Core.Annotations,
				de = function (e) {
					var t = e.selectedAnnotations,
						n = e.currentTool,
						r = ce(Object(u.a)(), 1)[0],
						c = ce(
							Object(o.useState)({
								StrokeColor: null,
								StrokeThickness: null,
								Opacity: null,
								FillColor: null,
							}),
							2,
						),
						s = c[0],
						d = c[1],
						p = ce(Object(o.useState)(""), 2),
						y = p[0],
						m = p[1],
						f = ce(Object(o.useState)(""), 2),
						S = f[0],
						h = f[1],
						g = ce(Object(o.useState)(""), 2),
						v = g[0],
						T = g[1],
						C = ce(Object(o.useState)(""), 2),
						w = C[0],
						x = C[1],
						E = ce(Object(o.useState)(!1), 2),
						O = E[0],
						A = E[1],
						P = ce(Object(o.useState)(null), 2),
						k = P[0],
						j = P[1],
						F = null == n ? void 0 : n.name,
						L = ce(
							Object(o.useState)(
								F !== oe.a && Object(G.e)(Object(G.j)(F)).hasLineEndings,
							),
							2,
						),
						R = L[0],
						N = L[1],
						_ = ["StrokeColor", "FillColor"],
						D = Object(i.d)(),
						M = ce(
							Object(i.e)(function (e) {
								return [
									l.a.getToolButtonObjects(e),
									l.a.isAnnotationToolStyleSyncingEnabled(e),
									l.a.getActiveDocumentViewerKey(e),
								];
							}),
							3,
						),
						z = M[0],
						H = M[1],
						W = M[2],
						B = null == t ? void 0 : t[0];
					Object(o.useEffect)(
						function () {
							U(n);
						},
						[n],
					);
					var U = function (e) {
							if (a.a.isFullPDFEnabled() && e && e.getSnapMode) {
								var t = !!e.getSnapMode();
								D(b.a.setEnableSnapMode({ toolName: e.name, isEnabled: t }));
							}
						},
						Y = function (e) {
							var t = {},
								n = "solid";
							try {
								n =
									"dash" === e.Style
										? "".concat(e.Style, ",").concat(e.Dashes)
										: e.Style;
							} catch (e) {
								console.error(e);
							}
							(t.TextColor = e.TextColor),
								(t.RichTextStyle = e.getRichTextStyle()),
								(t.Font = e.Font),
								(t.FontSize = e.FontSize),
								(t.TextAlign = e.TextAlign),
								(t.TextVerticalAlign = e.TextVerticalAlign),
								(t.calculatedFontSize = e.getCalculatedFontSize()),
								(t.StrokeStyle = n),
								(t.isAutoSizeFont = e.isAutoSizeFont()),
								A(e.isAutoSizeFont());
							var o = (function (e, t) {
									var n = new Set(),
										o = new Set();
									for (var r in e)
										if (e.hasOwnProperty(r)) {
											var i = parseInt(r, 10);
											!isNaN(i) &&
												" " !== t[i] &&
												e[r]["font-family"] &&
												n.add(e[r]["font-family"].trim()),
												!isNaN(i) &&
													" " !== t[i] &&
													e[r]["font-size"] &&
													o.add(e[r]["font-size"].trim());
										}
									return { fonts: Array.from(n), sizes: Array.from(o) };
								})(t.RichTextStyle, e.getContents()),
								r = o.fonts,
								i = o.sizes;
							return (
								(r.length >= 2 || (1 === r.length && r[0] !== t.Font)) &&
									(t.Font = void 0),
								(i.length >= 2 || (1 === i.length && i[0] !== t.FontSize)) &&
									(t.FontSize = void 0),
								t
							);
						},
						V = function (e) {
							var t,
								n = e.name,
								o = Object(J.a)(n);
							(n.includes("FreeText") || n.includes("Callout")) &&
								((o.isAutoSizeFont =
									null === (t = e.defaults) || void 0 === t
										? void 0
										: t.isAutoSizeFont),
								A(o.isAutoSizeFont));
							d(o || {}),
								T((null == o ? void 0 : o.StartLineStyle) || ""),
								x((null == o ? void 0 : o.EndLineStyle) || ""),
								h((null == o ? void 0 : o.StrokeStyle) || ""),
								(function (e) {
									var t,
										n = e.name,
										o = null === (t = z[n]) || void 0 === t ? void 0 : t.title;
									m(
										""
											.concat(r(I(n, "Title") || o), " ")
											.concat(r("stylePanel.headings.tool")),
									);
								})(e);
						};
					Object(o.useEffect)(
						function () {
							if (B) {
								t.length > 1
									? ((l = t),
										m(
											""
												.concat(r("stylePanel.headings.annotations"), " (")
												.concat(l.length, ")"),
										))
									: (o = B).isContentEditPlaceholder()
										? m(
												""
													.concat(r("stylePanel.headings.contentEdit"), " ")
													.concat(r("stylePanel.headings.annotation")),
											)
										: m(
												""
													.concat(
														r(
															I(o.ToolName, "Title") ||
																(null === (i = z[o.ToolName]) || void 0 === i
																	? void 0
																	: i.title),
														),
														" ",
													)
													.concat(r("stylePanel.headings.annotation")),
											),
									(function (e) {
										var t,
											n,
											o,
											r,
											i = {};
										e instanceof ue.FreeTextAnnotation && (i = Y(e)),
											e instanceof ue.RedactionAnnotation &&
												((i.OverlayText = e.OverlayText),
												(i.Font = e.Font),
												(i.FontSize = e.FontSize),
												(i.TextAlign = e.TextAlign)),
											e instanceof ue.WidgetAnnotation &&
												void 0 !== e.FontSize &&
												(i.FontSize = e.FontSize),
											d(
												le(
													le({}, s),
													{},
													{
														StrokeColor:
															null !== (t = e.StrokeColor) && void 0 !== t
																? t
																: null,
														StrokeThickness:
															null !== (n = e.StrokeThickness) && void 0 !== n
																? n
																: null,
														Opacity:
															null !== (o = e.Opacity) && void 0 !== o
																? o
																: null,
														FillColor:
															null !== (r = e.FillColor) && void 0 !== r
																? r
																: null,
													},
													i,
												),
											),
											T(e.getStartStyle ? e.getStartStyle() : "None"),
											x(e.getEndStyle ? e.getEndStyle() : "None"),
											h(
												(function (e) {
													var t = e.Style,
														n = e.Dashes;
													return "dash" !== t ? t : "".concat(t, ",").concat(n);
												})(e),
											);
									})(B);
								var e = t.every(function (e) {
									return Object(G.e)(Object(G.j)(e.ToolName)).hasLineEndings;
								});
								N(e);
							} else n && (V(n), N(Object(G.e)(Object(G.j)(F)).hasLineEndings));
							var o, i, l;
						},
						[B, n, t],
					);
					return {
						panelTitle: y,
						style: s,
						strokeStyle: S,
						startLineStyle: v,
						endLineStyle: w,
						onStyleChange: function (e, n) {
							var o = le(le({}, s), {}, ae({}, e, n));
							if ((d(o), 0 === t.length && k && "FillColor" === e)) {
								var r = k[0];
								if (null != r && r.hasFocus()) {
									var i = k[1];
									return (
										r.setStyle({ background: n }),
										void (i.FillColor = new ue.Color(n))
									);
								}
							}
							if (t.length > 0)
								t.forEach(function (t) {
									if (_.includes(e)) {
										var o = Object(ne.b)(n),
											r = new ue.Color(o.r, o.g, o.b, o.a);
										a.a.setAnnotationStyles(t, ae({}, e, r), W),
											H && Object(Z.a)(t.ToolName, e, r);
									} else
										a.a.setAnnotationStyles(t, ae({}, e, n), W),
											t instanceof ue.FreeTextAnnotation &&
												["FontSize", "Font", "StrokeThickness"].includes(e) &&
												Object(Q.a)(t),
											H && Object(Z.a)(t.ToolName, e, n);
									a.a.getAnnotationManager().redrawAnnotation(t),
										t instanceof ue.WidgetAnnotation && t.refresh();
								});
							else {
								var l = a.a.getToolMode();
								if (l) {
									if (_.includes(e)) {
										var c = Object(ne.b)(n),
											u = new ue.Color(c.r, c.g, c.b, c.a);
										Object(Z.a)(l.name, e, u);
									} else Object(Z.a)(l.name, e, n);
									l instanceof window.Core.Tools.RubberStampCreateTool &&
										l.showPreview();
								}
							}
						},
						onLineStyleChange: function (e, n) {
							var o = {
								start: "StartLineStyle",
								middle: "StrokeStyle",
								end: "EndLineStyle",
							};
							if (
								("start" === e && T(n),
								"middle" === e && h(n),
								"end" === e && x(n),
								t.length > 0)
							)
								t.forEach(function (t) {
									if (("start" === e && t.setStartStyle(n), "middle" === e)) {
										var r = n.split(",");
										(t.Style = r.shift()), (t.Dashes = r);
									}
									"end" === e && t.setEndStyle(n),
										a.a.getAnnotationManager(W).redrawAnnotation(t),
										H && Object(Z.a)(t.ToolName, o[e], n);
								}),
									a.a
										.getAnnotationManager(W)
										.trigger("annotationChanged", [t, "modify", {}]);
							else {
								var r = a.a.getToolMode();
								r && Object(Z.a)(r.name, o[e], n);
							}
						},
						isAutoSizeFont: O,
						handleAutoSize: function () {
							t.length > 0
								? t.forEach(function (e) {
										Object(ee.a)(e, A, O);
									})
								: n && (Object(Z.a)(n.name, "isAutoSizeFont", !O), A(!O));
						},
						handleRichTextStyleChange: function (e, o) {
							var r,
								i =
									(null === (r = s.RichTextStyle) || void 0 === r
										? void 0
										: r[0]) || {},
								l = ["underline", "line-through"].includes(e)
									? Object(te.a)(ae({}, e, o), i)
									: o,
								c = {
									0: le(
										le({}, i),
										{},
										ae(
											{},
											"underline" === e || "line-through" === e
												? "text-decoration"
												: e,
											l,
										),
									),
								};
							t.length > 0
								? t.forEach(function (t) {
										a.a.updateAnnotationRichTextStyle(t, ae({}, e, o), W);
									})
								: n && Object(Z.a)(n.name, "RichTextStyle", c),
								d(le(le({}, s), {}, { RichTextStyle: c }));
						},
						saveEditorInstance: j,
						activeTool: null == n ? void 0 : n.name,
						showLineStyleOptions: R,
					};
				},
				pe = n(43);
			function ye(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return me(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return me(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function me(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var fe = function (e) {
				var t = e.currentTool,
					n = e.selectedAnnotations,
					o = ye(Object(u.a)(), 1)[0],
					i = de({ currentTool: t, selectedAnnotations: n }).panelTitle;
				return r.a.createElement(
					r.a.Fragment,
					null,
					i && r.a.createElement("h2", { className: "style-panel-header" }, i),
					r.a.createElement(
						"div",
						{ className: "no-tool-selected" },
						r.a.createElement(
							"div",
							null,
							r.a.createElement(pe.a, {
								className: "empty-icon",
								glyph: "style-panel-no-tool-selected",
							}),
						),
						r.a.createElement(
							"div",
							{ className: "msg" },
							o("stylePanel.noSharedToolStyle"),
						),
					),
				);
			};
			fe.propTypes = {
				selectedAnnotations: s.a.arrayOf(s.a.object),
				currentTool: s.a.object,
			};
			var Se = fe;
			function he() {
				return (he = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function ge(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return be(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return be(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function be(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var ve = {
					activeType: s.a.string,
					endLineStyle: s.a.string,
					isFreeText: s.a.bool,
					isInFormFieldCreationMode: s.a.bool,
					hasParentPicker: s.a.bool,
					isRedaction: s.a.bool,
					isStamp: s.a.bool,
					isTextStylePickerHidden: s.a.bool,
					onLineStyleChange: s.a.func,
					onStyleChange: s.a.func.isRequired,
					redactionLabelProperties: s.a.object,
					showLineStyleOptions: s.a.bool,
					sliderProperties: s.a.arrayOf(s.a.string),
					startLineStyle: s.a.string,
					strokeStyle: s.a.string,
					style: s.a.object.isRequired,
					toolName: s.a.string,
					annotationTypes: s.a.arrayOf(s.a.string),
				},
				Te = function (e) {
					var t,
						n,
						l = e.onStyleChange,
						c = e.style,
						s = e.isFreeText,
						f = e.isRedaction,
						S = e.showLineStyleOptions,
						h = e.isStamp,
						g = e.isInFormFieldCreationMode,
						T = e.startLineStyle,
						C = e.endLineStyle,
						w = e.strokeStyle,
						E = e.onLineStyleChange,
						O = e.activeTool,
						_ = e.hasParentPicker,
						D = e.annotationTypes,
						H = ge(Object(u.a)(), 1)[0],
						W = Object(i.d)(),
						B = ge(Object(o.useState)(c.StrokeColor), 2),
						U = B[0],
						Y = B[1],
						V = ge(Object(o.useState)(T), 2),
						K = V[0],
						$ = V[1],
						G = ge(Object(o.useState)(C), 2),
						J = G[0],
						Z = G[1],
						Q = ge(Object(o.useState)(w), 2),
						ee = Q[0],
						te = Q[1],
						ne = ge(Object(o.useState)(c.FillColor), 2),
						oe = ne[0],
						re = ne[1],
						ie = function (e) {
							return D && D.length > 0 ? D.some(e) : e(O);
						},
						le = ie(R),
						ae = ie(F),
						ce = ((t = P), D && D.length > 0 ? D.every(t) : t(O)),
						se = ie(k),
						ue = ie(A),
						de = ie(j),
						pe =
							((n = O),
							[
								x.DistanceMeasurementCreateTool,
								x.ArcMeasurementCreateTool,
								x.PerimeterMeasurementCreateTool,
								x.AreaMeasurementCreateTool,
								x.RectangularAreaMeasurementCreateTool,
								x.CloudyRectangularAreaMeasurementCreateTool,
							].some(function (e) {
								return a.a.getTool(n) instanceof e;
							}) && !D),
						ye = ie(L);
					Object(o.useEffect)(
						function () {
							ce && W(b.a.openElement(y.a.STROKE_STYLE_CONTAINER));
						},
						[O],
					),
						Object(o.useEffect)(
							function () {
								Y(c.StrokeColor), re(c.FillColor);
							},
							[U, oe, c],
						),
						Object(o.useEffect)(
							function () {
								Y(c.StrokeColor), $(T), te(w), Z(C);
							},
							[T, C, w],
						);
					var me = function (e, t) {
						null == l || l(e, t);
					};
					f && ((c.Opacity = null), (c.StrokeThickness = null));
					var fe = M(),
						be = fe.isSnapModeEnabled,
						ve = fe.isStyleOptionDisabled,
						Te = fe.isStrokeStyleContainerActive,
						we = fe.isFillColorContainerActive,
						xe = fe.isOpacityContainerActive,
						Ee = fe.openStrokeStyleContainer,
						Oe = fe.openFillColorContainer,
						Ae = fe.openOpacityContainer,
						Pe = function (e, t) {
							var n = (function (e) {
								var t = c.Opacity,
									n = c.StrokeThickness,
									o = c.FontSize;
								switch (e.toLowerCase()) {
									case "opacity":
										return null === t
											? null
											: {
													property: "Opacity",
													displayProperty: "opacity",
													value: 100 * t,
													getDisplayValue: function (e) {
														return "".concat(Math.round(e), "%");
													},
													dataElement: y.a.OPACITY_SLIDER,
													withInputField: !0,
													inputFieldType: "number",
													min: 0,
													max: 100,
													step: 1,
													getLocalValue: function (e) {
														return parseInt(e) / 100;
													},
												};
									case "strokethickness":
										return null === n
											? null
											: {
													property: "StrokeThickness",
													displayProperty: "thickness",
													value: n,
													getDisplayValue: m.a,
													dataElement: y.a.STROKE_THICKNESS_SLIDER,
													withInputField: !0,
													inputFieldType: "number",
													min: 0,
													max: 23,
													step: 1,
													steps: Object(m.b)(s),
												};
									case "fontsize":
										return null === o
											? null
											: {
													property: "FontSize",
													displayProperty: "text",
													value: o,
													getDisplayValue: function (e) {
														return "".concat(Math.round(parseInt(e, 10)), "pt");
													},
													dataElement: y.a.FONT_SIZE_SLIDER,
													min: 5,
													max: 45,
													step: 1,
													withInputField: !0,
													inputFieldType: "number",
													getLocalValue: function (e) {
														return "".concat(parseFloat(e).toFixed(2), "pt");
													},
												};
								}
							})(e);
							return n
								? r.a.createElement(
										p.a,
										he({ key: e }, n, {
											onStyleChange: me,
											onSliderChange: me,
											shouldHideSliderTitle: t,
											customCircleRadius: 8,
											customLineStrokeWidth: 5,
										}),
									)
								: null;
						},
						ke = function () {
							if (ce) return r.a.createElement("div", { className: "divider" });
						},
						je = Pe("strokethickness"),
						Fe = _
							? {}
							: {
									className: "StylePicker",
									onMouseDown: function (e) {
										"touchstart" !== e.type &&
											"INPUT" !== e.target.tagName.toUpperCase() &&
											e.preventDefault();
									},
								};
					return ae && se && le && (null == D ? void 0 : D.length) > 1
						? r.a.createElement(Se, null)
						: r.a.createElement(
								Ce,
								{ parentProps: Fe, hasParentPicker: _ },
								!ae &&
									r.a.createElement(
										"div",
										{ className: "PanelSection" },
										r.a.createElement(q, {
											showFillColorAndCollapsablePanelSections: ce,
											isStamp: h,
											onStrokeColorChange: function (e) {
												null == l || l("StrokeColor", e), Y(e);
											},
											onStyleChange: l,
											strokeColor: U,
											activeTool: O,
											hideStrokeDropdowns: ue,
											hideStrokeSlider: de,
											strokethicknessComponent: je,
											showLineStyleOptions: S,
											renderSlider: Pe,
											strokeStyle: ee,
											isInFormFieldCreationMode: g,
											isFreeText: s,
											onStartLineStyleChange: function (e) {
												null == E || E("start", e), $(e);
											},
											startingLineStyle: K,
											isStyleOptionDisabled: ve,
											onStrokeStyleChange: function (e) {
												null == E || E("middle", e), te(e);
											},
											strokeLineStyle: ee,
											onEndLineStyleChange: function (e) {
												null == E || E("end", e), Z(e);
											},
											endingLineStyle: J,
											openStrokeStyleContainer: Ee,
											isStrokeStyleContainerActive: Te,
											hideCloudyLineStyle: ye,
										}),
										ke(),
									),
								ae && !de && je && je,
								ce &&
									!se &&
									r.a.createElement(
										"div",
										{ className: "PanelSection" },
										r.a.createElement(
											z.a,
											{
												header: H(
													I(O, "FillColor") ||
														"option.annotationColor.FillColor",
												),
												headingLevel: 2,
												isInitiallyExpanded: !1,
												isExpanded: we,
												onToggle: Oe,
											},
											r.a.createElement(
												"div",
												{ className: "panel-section-wrapper" },
												r.a.createElement(
													"div",
													{ className: "menu-items" },
													r.a.createElement(d.a, {
														onColorChange: function (e) {
															null == l || l("FillColor", e), re(e);
														},
														onStyleChange: l,
														color: oe,
														hasTransparentColor: !N(O),
														activeTool: O,
														type: "Fill",
														ariaTypeLabel: H(
															"option.annotationColor.FillColor",
														),
													}),
												),
											),
										),
										!le && ke(),
									),
								r.a.createElement(
									"div",
									{ className: "PanelSection" },
									r.a.createElement(X, {
										showFillColorAndCollapsablePanelSections: ce,
										shouldHideOpacitySlider: le,
										activeTool: O,
										showLineStyleOptions: S,
										renderSlider: Pe,
										isOpacityContainerActive: xe,
										openOpacityContainer: Ae,
									}),
									pe && ke(),
								),
								pe &&
									r.a.createElement(
										r.a.Fragment,
										null,
										!ce && r.a.createElement("div", { className: "spacer" }),
										r.a.createElement(
											"div",
											{ className: "PanelSection" },
											r.a.createElement(v, {
												Scale: c.Scale,
												Precision: c.Precision,
												isSnapModeEnabled: be,
											}),
										),
									),
							);
				},
				Ce = function (e) {
					var t = e.hasParentPicker,
						n = e.children,
						o = e.parentProps;
					return t
						? r.a.createElement(r.a.Fragment, null, n)
						: r.a.createElement("div", o, n);
				};
			(Ce.propTypes = {
				hasParentPicker: s.a.bool,
				children: s.a.any,
				parentProps: s.a.object,
			}),
				(Te.propTypes = ve);
			var we = Te,
				xe = n(467),
				Ee = n(1707),
				Oe = n(466);
			function Ae() {
				return (Ae = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function Pe(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return ke(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return ke(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function ke(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var je = function (e) {
				var t = e.selectedAnnotations,
					n = e.currentTool,
					l = Object(i.d)(),
					a = Object(u.a)().t,
					c = Pe(Object(o.useState)(!1), 2),
					s = c[0],
					d = c[1];
				Object(o.useEffect)(
					function () {
						s && l(b.a.closeElement(y.a.ANNOTATION_POPUP));
					},
					[s],
				);
				var p = de({ selectedAnnotations: t, currentTool: n }),
					m = p.panelTitle,
					f = p.style,
					S = p.strokeStyle,
					h = p.onStyleChange,
					g = p.onLineStyleChange,
					v = p.isAutoSizeFont,
					T = p.handleAutoSize,
					C = p.handleRichTextStyleChange,
					w = p.saveEditorInstance,
					x = p.showLineStyleOptions,
					E = p.startLineStyle,
					O = p.endLineStyle,
					A = D(t, n),
					k = A.isRedaction,
					j = A.isWidget,
					F = A.activeTool,
					L = M(),
					R = L.openTextStyleContainer,
					N = L.isTextStyleContainerActive,
					_ = P(F);
				Object(o.useEffect)(
					function () {
						_ && l(b.a.openElement(y.a.RICH_TEXT_STYLE_CONTAINER));
					},
					[F],
				);
				var H = Object(Oe.a)(w);
				return r.a.createElement(
					r.a.Fragment,
					null,
					r.a.createElement("h2", { className: "style-panel-header" }, m),
					r.a.createElement(
						"div",
						{
							className: "StylePicker",
							onMouseDown: function (e) {
								"touchstart" !== e.type &&
									"INPUT" !== e.target.tagName.toUpperCase() &&
									e.preventDefault();
							},
						},
						r.a.createElement(
							"div",
							{ className: "PanelSection TextStyle" },
							r.a.createElement(
								z.a,
								{
									header: a(
										I(F, "OverlayText") || "option.stylePopup.textStyle",
									),
									headingLevel: 2,
									isInitiallyExpanded: !1,
									isExpanded: N,
									onToggle: R,
								},
								r.a.createElement(
									"div",
									{ className: "panel-section-wrapper" },
									k &&
										r.a.createElement(
											"div",
											{ className: "PanelSubsection RedactionTextLabel" },
											r.a.createElement(
												"div",
												{ className: "menu-subtitle" },
												a("stylePanel.headings.redactionTextLabel"),
											),
											r.a.createElement(xe.a, {
												properties: f,
												onPropertyChange: h,
												placeholderText: " ",
											}),
										),
									r.a.createElement(
										Ee.default,
										Ae({ style: f }, H, {
											property: "TextColor",
											colorMapKey: "freeText",
											isFreeTextAutoSize: v,
											onFreeTextSizeToggle: T,
											onPropertyChange: h,
											onRichTextStyleChange: C,
											isRichTextEditMode: s,
											setIsRichTextEditMode: d,
											isRedaction: k,
											activeTool: F,
											isWidget: j,
										}),
									),
								),
							),
							r.a.createElement("div", { className: "divider" }),
						),
						r.a.createElement(
							we,
							Ae({}, A, {
								hasParentPicker: !0,
								sliderProperties: ["Opacity", "StrokeThickness"],
								style: f,
								onStyleChange: h,
								strokeStyle: S,
								onLineStyleChange: g,
								activeTool: F,
								showLineStyleOptions: x,
								startLineStyle: E,
								endLineStyle: O,
							}),
						),
					),
				);
			};
			je.propTypes = {
				selectedAnnotations: s.a.arrayOf(s.a.object),
				currentTool: s.a.object,
			};
			var Fe = je;
			function Le() {
				return (Le = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function Re(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return Ne(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return Ne(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function Ne(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Ie = function (e) {
				var t = e.currentTool,
					n = e.selectedAnnotations,
					o = Re(Object(u.a)(), 1)[0],
					i = de({ currentTool: t, selectedAnnotations: n }),
					l = i.style,
					c = i.panelTitle,
					s = i.strokeStyle,
					d = i.startLineStyle,
					p = i.endLineStyle,
					y = i.onStyleChange,
					m = i.onLineStyleChange,
					f = i.showLineStyleOptions,
					S = D(n, t),
					h = S.toolName;
				return r.a.createElement(
					r.a.Fragment,
					null,
					r.a.createElement("h2", { className: "style-panel-header" }, c),
					(function (e) {
						return [
							x.AddParagraphTool,
							x.AddImageContentTool,
							x.CropCreateTool,
							x.SnippingCreateTool,
						].some(function (t) {
							return a.a.getTool(e) instanceof t;
						});
					})(h)
						? r.a.createElement(
								"div",
								{ className: "no-tool-selected" },
								r.a.createElement(
									"div",
									null,
									r.a.createElement(pe.a, {
										className: "empty-icon",
										glyph: "style-panel-no-tool-selected",
									}),
								),
								r.a.createElement(
									"div",
									{ className: "msg" },
									o("stylePanel.noToolStyle"),
								),
							)
						: r.a.createElement(
								we,
								Le({}, S, {
									sliderProperties: ["Opacity", "StrokeThickness"],
									style: l,
									onStyleChange: y,
									showLineStyleOptions: f,
									startLineStyle: d,
									endLineStyle: p,
									strokeStyle: s,
									onLineStyleChange: m,
								}),
							),
				);
			};
			Ie.propTypes = {
				selectedAnnotations: s.a.arrayOf(s.a.object),
				currentTool: s.a.object,
			};
			var _e = Ie,
				De = function (e) {
					return e.length > 1 &&
						e.some(function (e) {
							return (function (e) {
								return [x.RedactionCreateTool].some(function (t) {
									return a.a.getTool(e) instanceof t;
								});
							})(e);
						})
						? Se
						: e.every(function (e) {
									return (function (e) {
										return [
											x.FreeTextCreateTool,
											x.CalloutCreateTool,
											x.RedactionCreateTool,
											x.TextFormFieldCreateTool,
											x.ListBoxFormFieldCreateTool,
											x.ComboBoxFormFieldCreateTool,
										].some(function (t) {
											return a.a.getTool(e) instanceof t;
										});
									})(e);
								})
							? Fe
							: _e;
				},
				Me = n(75);
			function ze(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return He(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return He(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function He(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var We = function () {
					var e = ze(Object(u.a)(), 1)[0];
					return r.a.createElement(
						r.a.Fragment,
						null,
						r.a.createElement(
							"h2",
							{ className: "style-panel-header" },
							e("stylePanel.headings.styles"),
						),
						r.a.createElement(
							"div",
							{ className: "no-tool-selected" },
							r.a.createElement(
								"div",
								null,
								r.a.createElement(pe.a, {
									className: "empty-icon",
									glyph: "style-panel-no-tool-selected",
								}),
							),
							r.a.createElement(
								"div",
								{ className: "msg" },
								e("stylePanel.noToolSelected"),
							),
						),
					);
				},
				Be = n(461),
				Ue = n(111),
				Ye = n.n(Ue);
			n(1729);
			function Ve(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								i,
								l,
								a = [],
								c = !0,
								s = !1;
							try {
								if (((i = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = i.call(n)).done) &&
										(a.push(o.value), a.length !== t);
										c = !0
									);
							} catch (e) {
								(s = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((l = n.return()), Object(l) !== l)
									)
										return;
								} finally {
									if (s) throw r;
								}
							}
							return a;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return qe(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return qe(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function qe(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Ke = window.Core.Annotations,
				$e = function () {
					var e = Object(i.e)(function (e) {
							return l.a.isElementOpen(e, "stylePanel");
						}),
						t = Object(Be.a)(),
						n = Ve(Object(o.useState)(a.a.getSelectedAnnotations()), 2),
						c = n[0],
						s = n[1],
						u = Ve(Object(o.useState)(a.a.getToolMode()), 2),
						d = u[0],
						p = u[1],
						y = [Ke.PushButtonWidgetAnnotation],
						m = Ye()(
							function () {
								var e = a.a.getAnnotationManager(),
									t = a.a.getToolMode(),
									n = a.a.getSelectedAnnotations(),
									o = new Set(n);
								n.forEach(function (t) {
									t.isGrouped()
										? e.getGroupAnnotations(t).forEach(function (e) {
												return o.add(e);
											})
										: t.getGroupedChildren().length > 1 &&
											t.getGroupedChildren().forEach(function (e) {
												return o.add(e);
											});
								});
								var r = Array.from(o);
								s(r), p(t);
							},
							150,
							{ leading: !1, trailing: !0 },
						);
					Object(o.useEffect)(
						function () {
							e && m();
						},
						[e],
					),
						Object(o.useEffect)(function () {
							return (
								a.a.addEventListener("annotationSelected", m),
								a.a.addEventListener("toolModeUpdated", m),
								function () {
									a.a.removeEventListener("annotationSelected", m),
										a.a.removeEventListener("toolModeUpdated", m);
								}
							);
						}, []);
					if (!e) return null;
					var f = (
						c.length > 0
							? (function (e, t) {
									return (
										1 === e.length &&
										(function (e, t) {
											return t.some(function (t) {
												return e instanceof t;
											});
										})(e[0], t)
									);
								})(c, y)
							: !t.includes(null == d ? void 0 : d.name)
					)
						? We
						: (function (e, t) {
								var n = _(t),
									o = (null == n ? void 0 : n.length) >= 1 ? n : [e.name];
								return De(o);
							})(d, c);
					return r.a.createElement(
						Me.a,
						{ dataElement: "stylePanel", className: "Panel StylePanel" },
						r.a.createElement(f, { currentTool: d, selectedAnnotations: c }),
					);
				};
			t.default = $e;
		},
	},
]);
//# sourceMappingURL=chunk.44.js.map
