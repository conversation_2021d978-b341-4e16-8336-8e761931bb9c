/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[13],
		{
			635: function (ya, va, r) {
				function la() {
					return !1;
				}
				function na(z, aa, ea) {
					if (!(aa in w)) return !0;
					aa = w[aa];
					for (var ba = 0; ba < aa.length; ba++) {
						var fa = z;
						var ha = aa[ba];
						var pa = ea;
						if (ha.name in fa) {
							var oa = "",
								ka = !1;
							fa = fa[ha.name];
							switch (ha.type) {
								case "s":
									oa = "String";
									ka = Object(ia.isString)(fa);
									break;
								case "a":
									oa = "Array";
									ka = Object(ia.isArray)(fa);
									break;
								case "n":
									oa = "Number";
									ka = Object(ia.isNumber)(fa) && Object(ia.isFinite)(fa);
									break;
								case "o":
									(oa = "Object"),
										(ka = Object(ia.isObject)(fa) && !Object(ia.isArray)(fa));
							}
							ka ||
								pa.reject(
									'Expected response field "'
										.concat(ha.name, '" to have type ')
										.concat(oa),
								);
							ha = ka;
						} else
							pa.reject('Response missing field "'.concat(ha.name, '"')),
								(ha = !1);
						if (!ha) return !1;
					}
					return !0;
				}
				function ma(z) {
					for (
						var aa = 0,
							ea = [
								"locale",
								"excelMaxAllowedCellCount",
								"applyPageBreaksToSheet",
								"excelDefaultCellBorderWidth",
								"displayChangeTracking",
							];
						aa < ea.length;
						aa++
					) {
						var ba = ea[aa],
							fa = ba;
						ba = ba.charAt(0).toUpperCase() + ba.slice(1);
						z[fa] &&
							(Object.defineProperty(
								z,
								ba,
								Object.getOwnPropertyDescriptor(z, fa),
							),
							delete z[fa]);
					}
					return z;
				}
				r.r(va);
				var ja = r(0),
					ia = r(1);
				r.n(ia);
				var ca = r(2);
				ya = r(53);
				var y = r(36),
					x = r(654),
					n = r(129),
					h = r(547),
					b = r(52),
					e = r(246),
					f = (function () {
						function z() {
							this.request = this.result = null;
							this.state = 0;
							var aa = this;
							aa.promise = new Promise(function (ea, ba) {
								aa.resolve = function () {
									if (0 === aa.state || 4 === aa.state)
										(aa.state = 1),
											(aa.result = arguments[0]),
											ea.apply(null, arguments);
								};
								aa.reject = function () {
									if (0 === aa.state || 4 === aa.state)
										(aa.state = 2), ba.apply(null, arguments);
								};
							});
						}
						z.prototype.qA = function () {
							return 1 === (this.state & 1);
						};
						z.prototype.$Ha = function () {
							return 2 === (this.state & 2);
						};
						z.prototype.ym = function () {
							return !this.$Ha() && !this.qA();
						};
						z.prototype.oHa = function () {
							return 4 === (this.state & 4);
						};
						z.prototype.c0 = function () {
							this.state |= 4;
						};
						return z;
					})(),
					a = (function () {
						function z() {
							this.cA = {};
							this.Oc = [];
						}
						z.prototype.pop = function () {
							var aa = this.Oc.pop();
							this.cA[aa.key] = void 0;
							return aa;
						};
						z.prototype.push = function (aa, ea) {
							ea = { key: aa, data: ea };
							this.Oc.push(ea);
							this.cA[aa] = ea.data;
						};
						z.prototype.contains = function (aa) {
							return !!this.cA[aa];
						};
						z.prototype.get = function (aa) {
							return this.cA[aa];
						};
						z.prototype.set = function (aa, ea) {
							var ba = this;
							this.cA[aa] = ea;
							this.Oc.forEach(function (fa, ha) {
								fa.key === aa && (ba.Oc[ha] = fa);
							});
						};
						z.prototype.remove = function (aa) {
							var ea = this;
							this.cA[aa] = void 0;
							this.Oc.forEach(function (ba, fa) {
								ba.key === aa && ea.Oc.splice(fa, 1);
							});
						};
						z.prototype.length = function () {
							return this.Oc.length;
						};
						return z;
					})(),
					w = {
						pages: [{ name: "pages", type: "a" }],
						pdf: [{ name: "url", type: "s" }],
						docmod: [
							{ name: "url", type: "s" },
							{ name: "rID", type: "s" },
						],
						health: [],
						tiles: [
							{ name: "z", type: "n" },
							{ name: "rID", type: "n" },
							{ name: "tiles", type: "a" },
							{ name: "size", type: "n" },
						],
						cAnnots: [{ name: "annots", type: "a" }],
						annots: [
							{ name: "url", type: "s" },
							{ name: "name", type: "s" },
						],
						image: [
							{ name: "url", type: "s" },
							{ name: "name", type: "s" },
							{ name: "p", type: "n" },
						],
						text: [
							{ name: "url", type: "s" },
							{ name: "name", type: "s" },
							{ name: "p", type: "n" },
						],
						ApString2Xod: [
							{ name: "url", type: "s" },
							{ name: "rID", type: "s" },
						],
					};
				r = (function () {
					function z(aa, ea, ba) {
						var fa = this;
						this.G0 = this.H8 = !1;
						this.Tj =
							this.jQ =
							this.UB =
							this.vg =
							this.oD =
							this.Ht =
							this.nD =
							this.Cr =
								null;
						this.Qq = new f();
						this.pv = new f();
						this.OK = !1;
						this.Qh = this.Ag = this.Bg = this.Dh = null;
						this.li = [];
						this.JL = [];
						this.cache = {};
						this.timeStamp = 0;
						this.Nj = [];
						this.Jm = [];
						this.$U = null;
						this.t8 = !1;
						this.mha = this.id = null;
						this.pY = this.rba = la;
						this.ei = 0;
						this.SW = !1;
						this.Ida = 1;
						this.Em = {};
						this.Ay = 0;
						this.bB = new a();
						ea.endsWith("/") || (ea += "/");
						ba = ba || {};
						this.H8 = ba.disableWebsockets || !1;
						this.G0 = ba.singleServerMode || !1;
						null != ba.customQueryParameters &&
							Object(b.b)("wvsQueryParameters", ba.customQueryParameters);
						ea.endsWith("blackbox/") || (ea += "blackbox/");
						this.Cr = ba.uploadData || null;
						this.UB = ba.uriData || null;
						this.nD = ba.cacheKey || null;
						if ((this.Ht = ba.officeOptions || null)) this.Ht = ma(this.Ht);
						this.vg = ba.rasterizerOptions || null;
						this.oD = ba.cadOptions || null;
						this.Ug = ea;
						this.zU = aa;
						this.au(!0);
						this.Wy = new x.a(ea, null, this.Qk()).NAa(
							!this.H8,
							function (ha) {
								fa.EKa(ha);
							},
							function () {
								return null;
							},
							function () {
								fa.OK = !1;
							},
							function () {
								fa.hPa();
							},
						);
					}
					z.prototype.xwa = function () {
						var aa = this;
						return new Promise(function (ea, ba) {
							var fa = new XMLHttpRequest(),
								ha = "".concat(aa.Ug, "ck");
							fa.open("GET", ha);
							fa.withCredentials = aa.Qk();
							fa.onreadystatechange = function () {
								fa.readyState === XMLHttpRequest.DONE &&
									(200 === fa.status ? ea() : ba());
							};
							fa.send();
						});
					};
					z.prototype.wRa = function (aa) {
						this.rba = aa || la;
						this.pY = la;
					};
					z.prototype.oua = function () {
						this.Aga();
						return this.Wy.Iv();
					};
					z.prototype.Aga = function () {
						Object(ja.b)(this, void 0, void 0, function () {
							return Object(ja.d)(this, function (aa) {
								switch (aa.label) {
									case 0:
										return (
											(this.pv = new f()),
											(this.Qq = new f()),
											(this.OK = !1),
											(this.id = null),
											(this.t8 = !1),
											[4, this.xwa().catch(function () {})]
										);
									case 1:
										return aa.aa(), [2];
								}
							});
						});
					};
					z.prototype.hPa = function () {
						this.rba();
						this.Aga();
						this.Dh &&
							(this.Dh.ym()
								? this.Yj(this.Dh.request)
								: this.Dh.qA() &&
									this.pY(this.Dh.result.url, "pdf") &&
									((this.Dh = null), this.xga()));
						this.Qh && this.Qh.ym() && this.Yj(this.Qh.request);
						this.Bg && this.Bg.ym()
							? this.Yj(this.Bg.request)
							: this.Ag && this.Ag.ym() && this.Taa();
						var aa;
						for (aa = 0; aa < this.Nj.length; aa++)
							this.Nj[aa] &&
								(this.Nj[aa].ym()
									? this.Yj(this.Nj[aa].request)
									: this.Nj[aa].qA() &&
										this.pY(this.Nj[aa].result.url, "image") &&
										((this.Nj[aa] = null), this.OO(Object(ia.uniqueId)(), aa)));
						for (aa = 0; aa < this.Jm.length; aa++)
							this.Jm[aa] &&
								this.Jm[aa].ym() &&
								!this.Jm[aa].oHa() &&
								this.Yj(this.Jm[aa].request);
						for (aa = 0; aa < this.li.length; aa++)
							this.li[aa] && this.li[aa].ym() && this.Yj(this.li[aa].request);
					};
					z.prototype.sGa = function () {
						return this.OK
							? Promise.resolve()
							: ((this.OK = !0), (this.timeStamp = Date.now()), this.Wy.cN());
					};
					z.prototype.LVa = function () {
						var aa = this,
							ea,
							ba,
							fa,
							ha,
							pa;
						return new Promise(function (oa, ka) {
							if (aa.Cr)
								(ea = new FormData()),
									ea.append("file", aa.Cr.fileHandle, aa.Cr.fileHandle.name),
									aa.Ht && ea.append("officeOptions", JSON.stringify(aa.Ht)),
									aa.vg &&
										ea.append("rasterizerOptions", JSON.stringify(aa.vg)),
									aa.oD && ea.append("cadOptions", aa.oD.getJsonString()),
									aa.nD && ea.append("cacheKey", aa.nD),
									(ba = aa.Cr.loadCallback),
									(ha = "upload"),
									(fa = aa.Cr.extension);
							else if (aa.UB)
								(ea = { uri: aa.UB.uri, h0a: aa.UB.shareId }),
									(ea = Object.keys(ea)
										.map(function (sa) {
											return ""
												.concat(sa, "=")
												.concat(ea[sa] ? encodeURIComponent(ea[sa]) : "");
										})
										.join("&")),
									(pa = "application/x-www-form-urlencoded; charset=UTF-8"),
									(ba = aa.UB.loadCallback),
									(ha = "url"),
									(fa = aa.UB.extension);
							else {
								oa();
								return;
							}
							var ra = new XMLHttpRequest(),
								qa = Object(y.l)(aa.Ug, "AuxUpload");
							qa = Object(e.a)(qa, { type: ha, ext: fa });
							ra.open("POST", qa);
							ra.withCredentials = aa.Qk();
							pa && ra.setRequestHeader("Content-Type", pa);
							ra.addEventListener("load", function () {
								if (ra.readyState === ra.DONE && 200 === ra.status) {
									var sa = JSON.parse(ra.response);
									aa.zU = sa.uri;
									ba(sa);
									oa(sa);
								}
							});
							ra.addEventListener("error", function () {
								ka("".concat(ra.statusText, " ").concat(JSON.stringify(ra)));
							});
							aa.Cr &&
								null != aa.Cr.onProgress &&
								(ra.upload.onprogress = function (sa) {
									aa.Cr.onProgress(sa);
								});
							ra.send(ea);
						});
					};
					z.prototype.UOa = function (aa) {
						this.password = aa || null;
						this.Qq.qA() || ((this.Qq = new f()), this.Yj({ t: "pages" }));
						return this.Qq.promise;
					};
					z.prototype.NG = function (aa) {
						this.$U = aa || null;
						this.Qq.qA() || this.Yj({ t: "pages" });
						return this.Qq.promise;
					};
					z.prototype.zD = function (aa) {
						aa = Object.assign(aa, { uri: encodeURIComponent(this.zU) });
						this.$U && (aa.ext = this.$U);
						this.Tj && (aa.c = this.Tj);
						this.password && (aa.pswd = this.password);
						this.nD && (aa.cacheKey = this.nD);
						this.Ht && (aa.officeOptions = this.Ht);
						this.vg && (aa.rastOptions = this.vg);
						this.oD && (aa.cadOptions = this.oD.mImpl);
						return aa;
					};
					z.prototype.$Pa = function () {
						0 < this.bB.length() &&
							10 >= this.Ay &&
							this.aQa(this.bB.pop().data);
					};
					z.prototype.xta = function (aa) {
						0 < this.bB.length() && this.bB.contains(aa) && this.bB.remove(aa);
					};
					z.prototype.Yj = function (aa) {
						aa = this.zD(aa);
						this.Wy.send(aa);
					};
					z.prototype.hha = function (aa, ea) {
						10 < this.Ay
							? this.bB.push(aa, ea)
							: (this.Ay++, (aa = this.zD(ea)), this.Wy.send(aa));
					};
					z.prototype.aQa = function (aa) {
						this.Ay++;
						aa = this.zD(aa);
						this.Wy.send(aa);
					};
					z.prototype.Pp = function (aa) {
						return aa;
					};
					z.prototype.qba = function (aa) {
						this.G0 && aa
							? Object(ca.i)(
									"Server failed health check. Single server mode ignoring check.",
								)
							: !this.oZa && aa && 3 >= this.ei
								? ((this.SW = !0), this.Wy.Iv())
								: 3 < this.ei && (this.G0 = !0);
					};
					z.prototype.EKa = function (aa) {
						var ea = this,
							ba = aa.data,
							fa = aa.err,
							ha = aa.t;
						switch (ha) {
							case "upload":
								fa ? this.MVa.reject(fa) : this.MVa.resolve("Success");
								break;
							case "pages":
								fa
									? this.Qq.reject(fa)
									: na(ba, ha, this.Qq) && this.Qq.resolve(ba);
								break;
							case "config":
								if (fa) this.pv.reject(fa);
								else if (na(ba, ha, this.pv)) {
									this.qba(ba.unhealthy);
									ba.id && (this.id = ba.id);
									if (ba.auth) {
										var pa = Object(b.a)("wvsQueryParameters");
										pa.auth = ba.auth;
										Object(b.b)("wvsQueryParameters", pa);
									}
									ba.serverVersion &&
										((this.jQ = ba.serverVersion),
										Object(ca.g)(
											"[WebViewer Server] server version: ".concat(this.jQ),
										));
									ba.serverID
										? ((this.ei =
												ba.serverID === this.mha && this.SW ? this.ei + 1 : 0),
											(this.mha = ba.serverID))
										: (this.ei = 0);
									this.SW = !1;
									this.pv.resolve(ba);
								}
								break;
							case "health":
								fa
									? this.pv.reject(fa)
									: na(ba, ha, this.pv) && this.qba(ba.unhealthy);
								break;
							case "pdf":
								ba.url = Object(e.a)(
									"".concat(this.Ug, "../").concat(encodeURI(ba.url)),
								);
								fa
									? this.Dh.reject(fa)
									: na(ba, ha, this.Dh) && this.Dh.resolve(ba);
								break;
							case "ApString2Xod":
								ba.url = Object(e.a)(
									"".concat(this.Ug, "../data/").concat(encodeURI(ba.url)),
								);
								fa
									? this.Em[ba.rID].reject(fa)
									: na(ba, ha, this.Em[ba.rID]) && this.Em[ba.rID].resolve(ba);
								break;
							case "docmod":
								ba.url = Object(e.a)(
									"".concat(this.Ug, "../").concat(encodeURI(ba.url)),
								);
								fa
									? this.Em[ba.rID].reject(fa)
									: na(ba, ha, this.Dh) && this.Em[ba.rID].resolve(ba);
								break;
							case "xod":
								if (fa)
									this.Bg && this.Bg.ym() && this.Bg.reject(fa),
										this.Ag && this.Ag.ym() && this.Ag.reject(fa);
								else if (ba.notFound)
									ba.noCreate ||
										(this.Bg && this.Bg.ym() && this.Bg.resolve(ba)),
										this.Ag && this.Ag.ym() && this.Ag.resolve(ba);
								else {
									ba.url &&
										(ba.url = Object(e.a)(
											"".concat(this.Ug, "../").concat(encodeURI(ba.url)),
										));
									if (!this.Ag || this.Ag.qA())
										(this.Ag = new f()),
											(this.Ag.request = { t: "xod", noCreate: !0 });
									this.Bg ||
										((this.Bg = new f()), (this.Bg.request = { t: "xod" }));
									this.Ag.resolve(ba);
									this.Bg.resolve(ba);
								}
								break;
							case "cAnnots":
								pa = this.Qh;
								if (fa) pa.reject(fa);
								else if (na(ba, ha, pa)) {
									pa.c0();
									var oa = [],
										ka = ba.annots;
									ba = function (za) {
										var Aa = ka[za].s,
											Ea = ka[za].e,
											Ga = ""
												.concat(ra.Ug, "../")
												.concat(encodeURI(ka[za].xfdf)),
											Ca =
												"true" === ka[za].hasAppearance
													? Object(e.a)("".concat(Ga, ".xodapp"))
													: null,
											La = Object(ia.range)(Aa, Ea);
										oa[za] = {
											range: La,
											promise: new Promise(function (Ka, Ia) {
												var Qa = new XMLHttpRequest();
												Qa.open("GET", Object(e.a)(Ga));
												Qa.responseType = "text";
												Qa.withCredentials = ea.Qk();
												Qa.addEventListener("load", function () {
													Qa.readyState === Qa.DONE &&
														200 === Qa.status &&
														Ka({ Gr: Qa.response, Ep: Ca, range: La });
												});
												Qa.addEventListener("error", function () {
													Ia(
														""
															.concat(Qa.statusText, " ")
															.concat(JSON.stringify(Qa)),
													);
												});
												Qa.send();
											}),
										};
									};
									var ra = this;
									for (fa = 0; fa < ka.length; fa++) ba(fa);
									pa.resolve(oa);
								}
								break;
							case "annots":
								if (fa) this.Qh.reject(fa);
								else if (na(ba, ha, this.Qh)) {
									this.Qh.c0();
									var qa = new XMLHttpRequest();
									pa = "".concat(this.Ug, "../").concat(encodeURI(ba.url));
									var sa = ba.hasAppearance
										? Object(e.a)("".concat(pa, ".xodapp"))
										: null;
									qa.open("GET", Object(e.a)(pa));
									qa.responseType = "text";
									qa.withCredentials = this.Qk();
									qa.addEventListener("load", function () {
										qa.readyState === qa.DONE &&
											200 === qa.status &&
											ea.Qh.resolve({ Gr: qa.response, Ep: sa });
									});
									qa.addEventListener("error", function () {
										ea.Qh.reject(
											"".concat(qa.statusText, " ").concat(JSON.stringify(qa)),
										);
									});
									qa.send();
								}
								break;
							case "image":
								this.Ay--;
								var ta = this.Nj[ba.p];
								fa
									? ta.promise.reject(fa)
									: na(ba, ha, ta) &&
										((ta.result = ba),
										(ta.result.url = Object(e.a)(
											""
												.concat(this.Ug, "../")
												.concat(encodeURI(ta.result.url)),
										)),
										ta.resolve(ta.result));
								break;
							case "tiles":
								this.Ay--;
								ta = ba.rID;
								pa = this.li[ta];
								this.li[ta] = null;
								this.JL.push(ta);
								if (fa) pa.reject(fa);
								else if (na(ba, ha, pa)) {
									for (fa = 0; fa < ba.tiles.length; fa++)
										ba.tiles[fa] = Object(e.a)(
											"".concat(this.Ug, "../").concat(encodeURI(ba.tiles[fa])),
										);
									pa.resolve(ba);
								}
								break;
							case "text":
								ta = this.Jm[ba.p];
								if (fa) ta.reject(fa);
								else if (na(ba, ha, ta)) {
									ta.c0();
									var wa = new XMLHttpRequest();
									ba = Object(e.a)(
										"".concat(this.Ug, "../").concat(encodeURI(ba.url)),
									);
									wa.open("GET", ba);
									wa.withCredentials = this.Qk();
									wa.addEventListener("load", function () {
										wa.readyState === wa.DONE &&
											200 === wa.status &&
											((ta.result = JSON.parse(wa.response)),
											ta.resolve(ta.result));
									});
									wa.addEventListener("error", function (za) {
										ta.reject(
											"".concat(wa.statusText, " ").concat(JSON.stringify(za)),
										);
									});
									wa.send();
								}
								break;
							case "progress":
								"loading" === ba.t &&
									this.trigger(n.a.Events.DOCUMENT_LOADING_PROGRESS, [
										ba.bytes,
										ba.total,
									]);
						}
						this.$Pa();
						!ha &&
							aa.echo &&
							aa &&
							"apstring2xod" === aa.echo.t &&
							(aa = aa.echo.reqID) &&
							(2 <= parseInt(this.jQ, 10)
								? this.Em[aa].reject("Message unhandled by server")
								: this.Em[aa].reject());
					};
					z.prototype.BBa = function () {
						return Object(ja.b)(this, void 0, void 0, function () {
							return Object(ja.d)(this, function (aa) {
								switch (aa.label) {
									case 0:
										return [4, this.sGa()];
									case 1:
										return aa.aa(), [2, this.pv.promise];
								}
							});
						});
					};
					z.prototype.bBa = function (aa) {
						for (
							var ea = this,
								ba = new XMLHttpRequest(),
								fa = Object(e.a)("".concat(this.Ug, "aul"), { id: this.id }),
								ha = new FormData(),
								pa = {},
								oa = 0;
							oa < aa.body.length;
							oa++
						) {
							var ka = aa.body[oa];
							pa[ka.id] = "".concat(ka.NS.w, ";").concat(ka.NS.h);
							ha.append(ka.id, ka.NS.dataString);
						}
						aa = { t: "apstring2xod", reqID: this.Ida++, parts: pa };
						var ra = this.zD(aa);
						ha.append("msg", JSON.stringify(ra));
						this.Em[ra.reqID] = new f();
						ba.open("POST", fa);
						ba.withCredentials = this.Qk;
						fa = new Promise(function (qa, sa) {
							ba.onreadystatechange = function () {
								4 === ba.readyState &&
									(200 === ba.status
										? qa()
										: sa(
												"An error occurred while sending down appearance strings to the server",
											));
							};
						});
						ba.send(ha);
						return fa.then(function () {
							return ea.Em[ra.reqID].promise;
						});
					};
					z.prototype.tua = function () {
						var aa = this.jQ.split("-")[0].split("."),
							ea = ["1", "5", "9"];
						if (3 !== aa.length) throw Error("Invalid WVS version length.");
						if (3 !== ea.length) throw Error("Invalid version length.");
						for (var ba = 0; ba < aa.length; ++ba) {
							if (ea.length === ba || aa[ba] > ea[ba]) return -1;
							if (aa[ba] !== ea[ba]) return 1;
						}
						return 0;
					};
					z.prototype.mv = function () {
						return 0 >= this.tua();
					};
					z.prototype.qV = function () {
						this.Qh ||
							((this.Qh = new f()),
							this.mv()
								? (this.Qh.request = { t: "cAnnots" })
								: (this.Qh.request = { t: "annots" }),
							this.Yj(this.Qh.request));
						return this.Qh.promise;
					};
					z.prototype.OO = function (aa, ea) {
						this.Nj[ea] ||
							((this.Nj[ea] = new f()),
							(this.Nj[ea].request = { t: "image", p: ea }),
							this.hha(aa, this.Nj[ea].request));
						return this.Nj[ea].promise;
					};
					z.prototype.VOa = function (aa) {
						this.Jm[aa] ||
							((this.Jm[aa] = new f()),
							(this.Jm[aa].request = { t: "text", p: aa }),
							this.Yj(this.Jm[aa].request));
						return this.Jm[aa].promise;
					};
					z.prototype.WOa = function (aa, ea, ba, fa, ha) {
						var pa = this.li.length;
						this.JL.length && (pa = this.JL.pop());
						this.li[pa] = new f();
						this.li[pa].request = {
							t: "tiles",
							p: ea,
							z: ba,
							r: fa,
							size: ha,
							rID: pa,
						};
						this.hha(aa, this.li[pa].request);
						return this.li[pa].promise;
					};
					z.prototype.xga = function () {
						this.Dh ||
							((this.Dh = new f()),
							(this.Dh.request = { t: "pdf" }),
							this.t8
								? this.Dh.resolve({ url: this.zU })
								: this.Yj(this.Dh.request));
						return this.Dh.promise;
					};
					z.prototype.W$ = function (aa) {
						var ea = this,
							ba = new XMLHttpRequest(),
							fa = Object(e.a)("".concat(this.Ug, "aul"), { id: this.id }),
							ha = new FormData(),
							pa = {};
						aa.annots && (pa.annots = "xfdf");
						aa.watermark && (pa.watermark = "png");
						aa.redactions && (pa.redactions = "redact");
						pa = { t: "docmod", reqID: this.Ida++, parts: pa };
						aa.print && (pa.print = !0);
						var oa = this.zD(pa);
						ha.append("msg", JSON.stringify(oa));
						return Promise.all(
							[aa.annots, aa.watermark, aa.redactions].map(function (ka) {
								return Promise.resolve(ka);
							}),
						).then(function (ka) {
							var ra = ka[0],
								qa = ka[1];
							ka = ka[2];
							ra && ha.append("annots", ra);
							qa && ha.append("watermark", qa);
							ka && ha.append("redactions", ka);
							ea.Em[oa.reqID] = new f();
							ba.open("POST", fa);
							ba.withCredentials = ea.Qk;
							ra = new Promise(function (sa, ta) {
								ba.onreadystatechange = function () {
									4 === ba.readyState &&
										(200 === ba.status
											? sa()
											: ta(
													"An error occurred while sending down annotation data to the server",
												));
								};
							});
							ba.send(ha);
							return ra.then(function () {
								return ea.Em[oa.reqID].promise;
							});
						});
					};
					z.prototype.Taa = function () {
						this.Ag ||
							((this.Ag = new f()),
							(this.Ag.request = { t: "xod", noCreate: !0 }),
							this.Yj(this.Ag.request));
						return this.Ag.promise;
					};
					z.prototype.XOa = function () {
						this.Bg ||
							((this.Bg = new f()),
							(this.Bg.request = { t: "xod" }),
							this.Yj(this.Bg.request));
						return this.Bg.promise;
					};
					z.prototype.Tt = function () {
						return !0;
					};
					z.prototype.request = function () {};
					z.prototype.Mfa = function () {};
					z.prototype.abort = function () {
						for (var aa = 0; aa < this.li.length; aa++)
							this.li[aa] &&
								(this.li[aa].resolve(null),
								(this.li[aa] = null),
								this.JL.push(aa));
						this.close();
					};
					z.prototype.hP = function (aa) {
						this.Tj = this.Tj || {};
						this.Tj.headers = aa;
					};
					z.prototype.au = function (aa) {
						this.Tj = this.Tj || {};
						this.Tj.internal = this.Tj.internal || {};
						this.Tj.internal.withCredentials = aa;
					};
					z.prototype.Qk = function () {
						return this.Tj && this.Tj.internal
							? this.Tj.internal.withCredentials
							: null;
					};
					z.prototype.getFileData = function () {
						return Promise.reject();
					};
					return z;
				})();
				Object(ya.a)(r);
				Object(h.a)(r);
				Object(h.b)(r);
				va["default"] = r;
			},
			654: function (ya, va, r) {
				var la = r(0),
					na = r(2),
					ma = r(36),
					ja = r(52),
					ia = r(246),
					ca = r(109),
					y = (function () {
						function n(h, b, e, f, a, w) {
							void 0 === e && (e = null);
							void 0 === f && (f = null);
							void 0 === a && (a = null);
							void 0 === w && (w = null);
							this.VW = !1;
							this.ei = 0;
							this.LF = 8;
							this.Nga = 3e3;
							this.EP = !1;
							this.K6 = this.nWa(h);
							this.url = b
								? "".concat(this.K6, "/").concat(b)
								: "".concat(this.K6, "/ws");
							this.hU = e;
							this.eG = f;
							this.xD = a;
							this.yga = w;
						}
						n.prototype.nWa = function (h) {
							var b = h.indexOf("://"),
								e = "ws://";
							0 > b ? (b = 0) : (5 === b && (e = "wss://"), (b += 3));
							var f = h.lastIndexOf("/");
							0 > f && (f = h.length);
							return e + h.slice(b, f);
						};
						n.prototype.send = function (h) {
							this.pu.readyState === WebSocket.CLOSED ||
								this.VW ||
								this.pu.send(JSON.stringify(h));
						};
						n.prototype.cN = function () {
							return Object(la.b)(this, void 0, void 0, function () {
								var h,
									b = this;
								return Object(la.d)(this, function () {
									h = Object(ja.a)("wvsQueryParameters");
									h.bcid = Object(ma.m)(8);
									Object(ja.b)("wvsQueryParameters", h);
									return [
										2,
										new Promise(function (e, f) {
											var a = Object(ia.a)(b.url);
											b.EP = !1;
											b.pu = new WebSocket(a);
											b.pu.onopen = function () {
												b.VW = !1;
												b.ei = 0;
												b.eG && b.eG();
												e();
											};
											b.pu.onerror = function () {
												b.VW = !0;
											};
											b.pu.onclose = function (w) {
												w = w.code;
												b.xD && b.xD();
												1e3 !== w && 3e3 !== w && b.CFa(w, e, f);
											};
											b.pu.onmessage = function (w) {
												w &&
													w.data &&
													((w = JSON.parse(w.data)),
													w.hb
														? b.send({ hb: !0 })
														: w.end
															? close()
															: b.hU(w));
											};
										}),
									];
								});
							});
						};
						n.prototype.CFa = function (h, b, e) {
							Object(la.b)(this, void 0, void 0, function () {
								var f = this;
								return Object(la.d)(this, function () {
									if (this.EP) return b(), [2];
									this.ei < this.LF
										? setTimeout(function () {
												f.EP
													? b()
													: (f.ei++,
														Object(na.i)(
															"Failed to connect to server with WebSocket close code "
																.concat(
																	h,
																	". Reconnecting to WebViewer Server, attempt ",
																)
																.concat(f.ei, " of ")
																.concat(f.LF, " ..."),
														),
														f.cN().then(b).catch(e));
											}, this.Nga)
										: e(ca.a);
									return [2];
								});
							});
						};
						n.prototype.Iv = function () {
							var h;
							void 0 === h && (h = !1);
							this.ei = 0;
							this.EP = !0;
							h ? this.pu.close(3e3) : this.pu.close();
							return Promise.resolve();
						};
						return n;
					})(),
					x = (function () {
						function n(h, b, e, f, a, w, z) {
							void 0 === f && (f = null);
							void 0 === a && (a = null);
							void 0 === w && (w = null);
							void 0 === z && (z = null);
							this.ei = this.NO = this.id = 0;
							this.YE = !1;
							this.request = null;
							this.LF = 8;
							this.Nga = 3e3;
							h = this.BMa(h);
							this.url = b
								? "".concat(h, "/").concat(b, "pf")
								: "".concat(h, "/pf");
							this.cQ = e;
							this.hU = f;
							this.eG = a;
							this.xD = w;
							this.yga = z;
						}
						n.prototype.BMa = function (h) {
							var b = h.lastIndexOf("/");
							0 > b && (b = h.length);
							return h.slice(0, b);
						};
						n.prototype.Wva = function (h) {
							h = h.split("\n");
							for (
								h[h.length - 1] && h.pop();
								0 < h.length && 3 > h[h.length - 1].length;
							)
								"]" === h.pop() && (this.id = 0);
							0 < h.length && 3 > h[0].length && h.shift();
							for (var b = 0; b < h.length; ++b)
								h[b].endsWith(",") && (h[b] = h[b].substr(0, h[b].length - 1));
							return h;
						};
						n.prototype.Mga = function () {
							return Object(la.b)(this, void 0, void 0, function () {
								var h = this;
								return Object(la.d)(this, function (b) {
									switch (b.label) {
										case 0:
											return this.ei++ < this.LF
												? [
														4,
														new Promise(function (e) {
															return setTimeout(function () {
																h.yga();
																h.cN();
																e();
															}, 3e3);
														}),
													]
												: [3, 2];
										case 1:
											b.aa(), (b.label = 2);
										case 2:
											return [2];
									}
								});
							});
						};
						n.prototype.GMa = function (h) {
							Object(la.b)(this, void 0, void 0, function () {
								var b, e;
								return Object(la.d)(this, function (f) {
									switch (f.label) {
										case 0:
											(b = null), (e = 0), (f.label = 1);
										case 1:
											if (!(e < h.length)) return [3, 6];
											b = JSON.parse(h[e]);
											if (!b) return [3, 5];
											if (!b.end) return [3, 2];
											close();
											return [3, 5];
										case 2:
											if (!b.id || Number(b.id) === this.id) return [3, 4];
											Object(na.i)("Reconnecting, new server detected");
											this.Iv();
											return [4, this.Mga()];
										case 3:
											return f.aa(), [3, 5];
										case 4:
											b.hb && Number(b.id) === this.id
												? this.send({ hb: !0 })
												: this.YE || this.hU(b),
												(f.label = 5);
										case 5:
											return ++e, [3, 1];
										case 6:
											return [2];
									}
								});
							});
						};
						n.prototype.zKa = function (h) {
							Object(la.b)(this, void 0, void 0, function () {
								var b, e, f;
								return Object(la.d)(this, function (a) {
									switch (a.label) {
										case 0:
											if (!(3 <= h.readyState)) return [3, 2];
											try {
												b = h.responseText.length;
											} catch (w) {
												return Object(na.g)("caught exception"), [2];
											}
											if (0 < b)
												try {
													(e = this.Wva(h.responseText)),
														0 === this.id &&
															0 < e.length &&
															((f = JSON.parse(e.shift())),
															(this.id = f.id),
															(this.ei = 0)),
														this.GMa(e);
												} catch (w) {}
											return this.YE ? [3, 2] : [4, this.A9()];
										case 1:
											a.aa(), (a.label = 2);
										case 2:
											return [2];
									}
								});
							});
						};
						n.prototype.A9 = function () {
							return Object(la.b)(this, void 0, void 0, function () {
								var h = this;
								return Object(la.d)(this, function () {
									return [
										2,
										new Promise(function (b, e) {
											function f() {
												return Object(la.b)(h, void 0, void 0, function () {
													return Object(la.d)(this, function (w) {
														switch (w.label) {
															case 0:
																e(), this.Iv(), (w.label = 1);
															case 1:
																return this.YE && this.ei < this.LF
																	? [4, this.Mga()]
																	: [3, 3];
															case 2:
																return w.aa(), [3, 1];
															case 3:
																return [2];
														}
													});
												});
											}
											h.request = new XMLHttpRequest();
											h.request.withCredentials = h.cQ;
											var a = Object(ia.a)(
												h.url,
												0 !== h.id
													? { id: String(h.id), uc: String(h.NO) }
													: { uc: String(h.NO) },
											);
											h.NO++;
											h.request.open("GET", a, !0);
											h.request.setRequestHeader("Cache-Control", "no-cache");
											h.request.setRequestHeader(
												"X-Requested-With",
												"XMLHttpRequest",
											);
											h.request.onreadystatechange = function () {
												h.zKa(h.request);
											};
											h.request.addEventListener("error", f);
											h.request.addEventListener("timeout", f);
											h.request.addEventListener("load", function () {
												h.eG && h.eG();
												b();
											});
											h.request.send();
										}),
									];
								});
							});
						};
						n.prototype.cN = function () {
							var h = Object(ja.a)("wvsQueryParameters");
							h.bcid = Object(ma.m)(8);
							Object(ja.b)("wvsQueryParameters", h);
							this.NO = this.id = 0;
							this.YE = !1;
							return this.A9();
						};
						n.prototype.send = function (h) {
							var b = this,
								e = new XMLHttpRequest();
							e.withCredentials = this.cQ;
							var f = Object(ia.a)(this.url, { id: String(this.id) }),
								a = new FormData();
							a.append("data", JSON.stringify(h));
							e.addEventListener("error", function () {
								b.Iv();
							});
							e.open("POST", f);
							e.setRequestHeader("X-Requested-With", "XMLHttpRequest");
							e.send(a);
						};
						n.prototype.Iv = function () {
							this.id = 0;
							this.YE = !0;
							this.xD && this.xD();
							this.request.abort();
							return Promise.resolve();
						};
						return n;
					})();
				ya = (function () {
					function n(h, b, e) {
						this.F7 = h;
						this.target = b;
						this.cQ = e;
					}
					n.prototype.NAa = function (h, b, e, f, a) {
						void 0 === h && (h = !0);
						void 0 === b && (b = null);
						void 0 === e && (e = null);
						void 0 === f && (f = null);
						void 0 === a && (a = null);
						return h
							? new y(this.F7, this.target, b, e, f, a)
							: new x(this.F7, this.target, this.cQ, b, e, f, a);
					};
					return n;
				})();
				va.a = ya;
			},
		},
	]);
}).call(this || window);
