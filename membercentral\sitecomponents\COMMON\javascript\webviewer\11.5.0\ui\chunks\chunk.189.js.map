{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ml.js"], "names": ["module", "exports", "_", "t", "default", "e", "d", "name", "weekdays", "split", "months", "weekdaysShort", "monthsShort", "weekdaysMin", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,CAACC,KAAK,KAAKC,SAAS,wEAAwEC,MAAM,KAAKC,OAAO,yFAAyFD,MAAM,KAAKE,cAAc,2CAA2CF,MAAM,KAAKG,YAAY,yEAAyEH,MAAM,KAAKI,YAAY,wBAAwBJ,MAAM,KAAKK,QAAQ,SAASZ,GAAG,OAAOA,GAAGa,QAAQ,CAACC,GAAG,aAAaC,IAAI,gBAAgBC,EAAE,aAAaC,GAAG,cAAcC,IAAI,0BAA0BC,KAAK,iCAAiCC,aAAa,CAACC,OAAO,aAAaC,KAAK,WAAWC,EAAE,gBAAgBC,EAAE,eAAeC,GAAG,cAAcC,EAAE,eAAeC,GAAG,cAAcvB,EAAE,YAAYwB,GAAG,WAAWC,EAAE,WAAWC,GAAG,UAAUC,EAAE,WAAWC,GAAG,YAAY,OAAO/B,EAAEC,QAAQ+B,OAAO7B,EAAE,MAAK,GAAIA,EAArmCD,CAAE,EAAQ", "file": "chunks/chunk.189.js", "sourcesContent": ["!function(_,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=e(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],e):(_=\"undefined\"!=typeof globalThis?globalThis:_||self).dayjs_locale_ml=e(_.dayjs)}(this,(function(_){\"use strict\";function e(_){return _&&\"object\"==typeof _&&\"default\"in _?_:{default:_}}var t=e(_),d={name:\"ml\",weekdays:\"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച\".split(\"_\"),months:\"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ\".split(\"_\"),weekdaysShort:\"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി\".split(\"_\"),monthsShort:\"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.\".split(\"_\"),weekdaysMin:\"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ\".split(\"_\"),ordinal:function(_){return _},formats:{LT:\"A h:mm -നു\",LTS:\"A h:mm:ss -നു\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY, A h:mm -നു\",LLLL:\"dddd, D MMMM YYYY, A h:mm -നു\"},relativeTime:{future:\"%s കഴിഞ്ഞ്\",past:\"%s മുൻപ്\",s:\"അൽപ നിമിഷങ്ങൾ\",m:\"ഒരു മിനിറ്റ്\",mm:\"%d മിനിറ്റ്\",h:\"ഒരു മണിക്കൂർ\",hh:\"%d മണിക്കൂർ\",d:\"ഒരു ദിവസം\",dd:\"%d ദിവസം\",M:\"ഒരു മാസം\",MM:\"%d മാസം\",y:\"ഒരു വർഷം\",yy:\"%d വർഷം\"}};return t.default.locale(d,null,!0),d}));"], "sourceRoot": ""}