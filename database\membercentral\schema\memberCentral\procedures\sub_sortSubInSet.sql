ALTER PROC dbo.sub_sortSubInSet
@siteID int,
@setID int,
@dir char(4),
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tmp TABLE (newOrder int IDENTITY(1,1) NOT NULL, subscriptionID int NOT NULL);
	DECLARE @setName VARCHAR(200), @orgID int, @msg varchar(max);

	SELECT @setName = ss.setName, @orgID = s.orgID
	FROM dbo.sub_sets AS ss
	INNER JOIN dbo.sites AS s ON s.siteID = ss.siteID
	WHERE ss.setID = @setID;

	IF @dir = 'asc'
		INSERT INTO @tmp (subscriptionID)
		SELECT ss.subscriptionID
		FROM dbo.sub_subscriptionSets ss 
		INNER JOIN dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
		INNER JOIN dbo.sub_Types st on st.typeID = subs.typeID 
			and st.siteID = @siteID
		INNER JOIN dbo.sub_sets sets on sets.setID = ss.setID 
			and sets.siteID = @siteID
		WHERE ss.setID = @setID
		ORDER BY st.typeName ASC, subs.subscriptionName ASC;
	ELSE
		INSERT INTO @tmp (subscriptionID)
		SELECT ss.subscriptionID
		FROM dbo.sub_subscriptionSets ss 
		INNER JOIN dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID
		INNER JOIN dbo.sub_Types st on st.typeID = subs.typeID 
			and st.siteID = @siteID
		INNER JOIN dbo.sub_sets sets on sets.setID = ss.setID 
			and sets.siteID = @siteID
		WHERE ss.setID = @setID
		ORDER BY st.typeName DESC, subs.subscriptionName DESC;

	BEGIN TRAN;
		UPDATE ss
		SET ss.orderNum = tmp.newOrder
		FROM dbo.sub_subscriptionSets ss
		INNER JOIN @tmp AS tmp ON tmp.subscriptionID = ss.subscriptionID
		WHERE ss.setID = @setID;

		EXEC dbo.subs_reorderSubscriptionSets @setID=@setID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SETID":'+CAST(@setID AS VARCHAR(10))+' }';

		IF @dir = 'asc'
			SET @msg = 'All Subscriptions in the Set ' + QUOTENAME(@setName) + ' have been arranged in ascending order.';
		ELSE
			SET @msg = 'All Subscriptions in the Set ' + QUOTENAME(@setName) + ' have been arranged in descending order.';
		
		SET @msg = STRING_ESCAPE(@msg,'json');

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSET', @msgjson=@msg,
			@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;
			
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
