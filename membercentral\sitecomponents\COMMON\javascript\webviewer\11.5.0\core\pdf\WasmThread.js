(function () {
	var $jscomp = $jscomp || {};
	$jscomp.scope = {};
	$jscomp.arrayIteratorImpl = function (l) {
		var h = 0;
		return function () {
			return h < l.length ? { done: !1, value: l[h++] } : { done: !0 };
		};
	};
	$jscomp.arrayIterator = function (l) {
		return { next: $jscomp.arrayIteratorImpl(l) };
	};
	$jscomp.ASSUME_ES5 = !1;
	$jscomp.ASSUME_NO_NATIVE_MAP = !1;
	$jscomp.ASSUME_NO_NATIVE_SET = !1;
	$jscomp.SIMPLE_FROUND_POLYFILL = !1;
	$jscomp.ISOLATE_POLYFILLS = !1;
	$jscomp.FORCE_POLYFILL_PROMISE = !1;
	$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION = !1;
	$jscomp.defineProperty =
		$jscomp.ASSUME_ES5 || "function" == typeof Object.defineProperties
			? Object.defineProperty
			: function (l, h, m) {
					if (l == Array.prototype || l == Object.prototype) return l;
					l[h] = m.value;
					return l;
				};
	$jscomp.getGlobal = function (l) {
		l = [
			"object" == typeof globalThis && globalThis,
			l,
			"object" == typeof window && window,
			"object" == typeof self && self,
			"object" == typeof global && global,
		];
		for (var h = 0; h < l.length; ++h) {
			var m = l[h];
			if (m && m.Math == Math) return m;
		}
		throw Error("Cannot find global object");
	};
	$jscomp.global = $jscomp.getGlobal(this);
	$jscomp.IS_SYMBOL_NATIVE =
		"function" === typeof Symbol && "symbol" === typeof Symbol("x");
	$jscomp.TRUST_ES6_POLYFILLS =
		!$jscomp.ISOLATE_POLYFILLS || $jscomp.IS_SYMBOL_NATIVE;
	$jscomp.polyfills = {};
	$jscomp.propertyToPolyfillSymbol = {};
	$jscomp.POLYFILL_PREFIX = "$jscp$";
	var $jscomp$lookupPolyfilledValue = function (l, h, m) {
		if (!m || null != l) {
			m = $jscomp.propertyToPolyfillSymbol[h];
			if (null == m) return l[h];
			m = l[m];
			return void 0 !== m ? m : l[h];
		}
	};
	$jscomp.polyfill = function (l, h, m, n) {
		h &&
			($jscomp.ISOLATE_POLYFILLS
				? $jscomp.polyfillIsolated(l, h, m, n)
				: $jscomp.polyfillUnisolated(l, h, m, n));
	};
	$jscomp.polyfillUnisolated = function (l, h, m, n) {
		m = $jscomp.global;
		l = l.split(".");
		for (n = 0; n < l.length - 1; n++) {
			var g = l[n];
			if (!(g in m)) return;
			m = m[g];
		}
		l = l[l.length - 1];
		n = m[l];
		h = h(n);
		h != n &&
			null != h &&
			$jscomp.defineProperty(m, l, {
				configurable: !0,
				writable: !0,
				value: h,
			});
	};
	$jscomp.polyfillIsolated = function (l, h, m, n) {
		var g = l.split(".");
		l = 1 === g.length;
		n = g[0];
		n = !l && n in $jscomp.polyfills ? $jscomp.polyfills : $jscomp.global;
		for (var r = 0; r < g.length - 1; r++) {
			var a = g[r];
			if (!(a in n)) return;
			n = n[a];
		}
		g = g[g.length - 1];
		m = $jscomp.IS_SYMBOL_NATIVE && "es6" === m ? n[g] : null;
		h = h(m);
		null != h &&
			(l
				? $jscomp.defineProperty($jscomp.polyfills, g, {
						configurable: !0,
						writable: !0,
						value: h,
					})
				: h !== m &&
					(void 0 === $jscomp.propertyToPolyfillSymbol[g] &&
						((m = (1e9 * Math.random()) >>> 0),
						($jscomp.propertyToPolyfillSymbol[g] = $jscomp.IS_SYMBOL_NATIVE
							? $jscomp.global.Symbol(g)
							: $jscomp.POLYFILL_PREFIX + m + "$" + g)),
					$jscomp.defineProperty(n, $jscomp.propertyToPolyfillSymbol[g], {
						configurable: !0,
						writable: !0,
						value: h,
					})));
	};
	$jscomp.initSymbol = function () {};
	$jscomp.polyfill(
		"Symbol",
		function (l) {
			if (l) return l;
			var h = function (r, a) {
				this.$jscomp$symbol$id_ = r;
				$jscomp.defineProperty(this, "description", {
					configurable: !0,
					writable: !0,
					value: a,
				});
			};
			h.prototype.toString = function () {
				return this.$jscomp$symbol$id_;
			};
			var m = "jscomp_symbol_" + ((1e9 * Math.random()) >>> 0) + "_",
				n = 0,
				g = function (r) {
					if (this instanceof g)
						throw new TypeError("Symbol is not a constructor");
					return new h(m + (r || "") + "_" + n++, r);
				};
			return g;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Symbol.iterator",
		function (l) {
			if (l) return l;
			l = Symbol("Symbol.iterator");
			for (
				var h =
						"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(
							" ",
						),
					m = 0;
				m < h.length;
				m++
			) {
				var n = $jscomp.global[h[m]];
				"function" === typeof n &&
					"function" != typeof n.prototype[l] &&
					$jscomp.defineProperty(n.prototype, l, {
						configurable: !0,
						writable: !0,
						value: function () {
							return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this));
						},
					});
			}
			return l;
		},
		"es6",
		"es3",
	);
	$jscomp.iteratorPrototype = function (l) {
		l = { next: l };
		l[Symbol.iterator] = function () {
			return this;
		};
		return l;
	};
	$jscomp.checkEs6ConformanceViaProxy = function () {
		try {
			var l = {},
				h = Object.create(
					new $jscomp.global.Proxy(l, {
						get: function (m, n, g) {
							return m == l && "q" == n && g == h;
						},
					}),
				);
			return !0 === h.q;
		} catch (m) {
			return !1;
		}
	};
	$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS = !1;
	$jscomp.ES6_CONFORMANCE =
		$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS &&
		$jscomp.checkEs6ConformanceViaProxy();
	$jscomp.makeIterator = function (l) {
		var h =
			"undefined" != typeof Symbol && Symbol.iterator && l[Symbol.iterator];
		if (h) return h.call(l);
		if ("number" == typeof l.length) return $jscomp.arrayIterator(l);
		throw Error(String(l) + " is not an iterable or ArrayLike");
	};
	$jscomp.owns = function (l, h) {
		return Object.prototype.hasOwnProperty.call(l, h);
	};
	$jscomp.MapEntry = function () {};
	$jscomp.polyfill(
		"Promise",
		function (l) {
			function h() {
				this.batch_ = null;
			}
			function m(a) {
				return a instanceof g
					? a
					: new g(function (p, t) {
							p(a);
						});
			}
			if (
				l &&
				(!(
					$jscomp.FORCE_POLYFILL_PROMISE ||
					($jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION &&
						"undefined" === typeof $jscomp.global.PromiseRejectionEvent)
				) ||
					!$jscomp.global.Promise ||
					-1 === $jscomp.global.Promise.toString().indexOf("[native code]"))
			)
				return l;
			h.prototype.asyncExecute = function (a) {
				if (null == this.batch_) {
					this.batch_ = [];
					var p = this;
					this.asyncExecuteFunction(function () {
						p.executeBatch_();
					});
				}
				this.batch_.push(a);
			};
			var n = $jscomp.global.setTimeout;
			h.prototype.asyncExecuteFunction = function (a) {
				n(a, 0);
			};
			h.prototype.executeBatch_ = function () {
				for (; this.batch_ && this.batch_.length; ) {
					var a = this.batch_;
					this.batch_ = [];
					for (var p = 0; p < a.length; ++p) {
						var t = a[p];
						a[p] = null;
						try {
							t();
						} catch (u) {
							this.asyncThrow_(u);
						}
					}
				}
				this.batch_ = null;
			};
			h.prototype.asyncThrow_ = function (a) {
				this.asyncExecuteFunction(function () {
					throw a;
				});
			};
			var g = function (a) {
				this.state_ = 0;
				this.result_ = void 0;
				this.onSettledCallbacks_ = [];
				this.isRejectionHandled_ = !1;
				var p = this.createResolveAndReject_();
				try {
					a(p.resolve, p.reject);
				} catch (t) {
					p.reject(t);
				}
			};
			g.prototype.createResolveAndReject_ = function () {
				function a(u) {
					return function (v) {
						t || ((t = !0), u.call(p, v));
					};
				}
				var p = this,
					t = !1;
				return { resolve: a(this.resolveTo_), reject: a(this.reject_) };
			};
			g.prototype.resolveTo_ = function (a) {
				if (a === this)
					this.reject_(new TypeError("A Promise cannot resolve to itself"));
				else if (a instanceof g) this.settleSameAsPromise_(a);
				else {
					a: switch (typeof a) {
						case "object":
							var p = null != a;
							break a;
						case "function":
							p = !0;
							break a;
						default:
							p = !1;
					}
					p ? this.resolveToNonPromiseObj_(a) : this.fulfill_(a);
				}
			};
			g.prototype.resolveToNonPromiseObj_ = function (a) {
				var p = void 0;
				try {
					p = a.then;
				} catch (t) {
					this.reject_(t);
					return;
				}
				"function" == typeof p
					? this.settleSameAsThenable_(p, a)
					: this.fulfill_(a);
			};
			g.prototype.reject_ = function (a) {
				this.settle_(2, a);
			};
			g.prototype.fulfill_ = function (a) {
				this.settle_(1, a);
			};
			g.prototype.settle_ = function (a, p) {
				if (0 != this.state_)
					throw Error(
						"Cannot settle(" +
							a +
							", " +
							p +
							"): Promise already settled in state" +
							this.state_,
					);
				this.state_ = a;
				this.result_ = p;
				2 === this.state_ && this.scheduleUnhandledRejectionCheck_();
				this.executeOnSettledCallbacks_();
			};
			g.prototype.scheduleUnhandledRejectionCheck_ = function () {
				var a = this;
				n(function () {
					if (a.notifyUnhandledRejection_()) {
						var p = $jscomp.global.console;
						"undefined" !== typeof p && p.error(a.result_);
					}
				}, 1);
			};
			g.prototype.notifyUnhandledRejection_ = function () {
				if (this.isRejectionHandled_) return !1;
				var a = $jscomp.global.CustomEvent,
					p = $jscomp.global.Event,
					t = $jscomp.global.dispatchEvent;
				if ("undefined" === typeof t) return !0;
				"function" === typeof a
					? (a = new a("unhandledrejection", { cancelable: !0 }))
					: "function" === typeof p
						? (a = new p("unhandledrejection", { cancelable: !0 }))
						: ((a = $jscomp.global.document.createEvent("CustomEvent")),
							a.initCustomEvent("unhandledrejection", !1, !0, a));
				a.promise = this;
				a.reason = this.result_;
				return t(a);
			};
			g.prototype.executeOnSettledCallbacks_ = function () {
				if (null != this.onSettledCallbacks_) {
					for (var a = 0; a < this.onSettledCallbacks_.length; ++a)
						r.asyncExecute(this.onSettledCallbacks_[a]);
					this.onSettledCallbacks_ = null;
				}
			};
			var r = new h();
			g.prototype.settleSameAsPromise_ = function (a) {
				var p = this.createResolveAndReject_();
				a.callWhenSettled_(p.resolve, p.reject);
			};
			g.prototype.settleSameAsThenable_ = function (a, p) {
				var t = this.createResolveAndReject_();
				try {
					a.call(p, t.resolve, t.reject);
				} catch (u) {
					t.reject(u);
				}
			};
			g.prototype.then = function (a, p) {
				function t(w, A) {
					return "function" == typeof w
						? function (B) {
								try {
									u(w(B));
								} catch (C) {
									v(C);
								}
							}
						: A;
				}
				var u,
					v,
					D = new g(function (w, A) {
						u = w;
						v = A;
					});
				this.callWhenSettled_(t(a, u), t(p, v));
				return D;
			};
			g.prototype.catch = function (a) {
				return this.then(void 0, a);
			};
			g.prototype.callWhenSettled_ = function (a, p) {
				function t() {
					switch (u.state_) {
						case 1:
							a(u.result_);
							break;
						case 2:
							p(u.result_);
							break;
						default:
							throw Error("Unexpected state: " + u.state_);
					}
				}
				var u = this;
				null == this.onSettledCallbacks_
					? r.asyncExecute(t)
					: this.onSettledCallbacks_.push(t);
				this.isRejectionHandled_ = !0;
			};
			g.resolve = m;
			g.reject = function (a) {
				return new g(function (p, t) {
					t(a);
				});
			};
			g.race = function (a) {
				return new g(function (p, t) {
					for (
						var u = $jscomp.makeIterator(a), v = u.next();
						!v.done;
						v = u.next()
					)
						m(v.value).callWhenSettled_(p, t);
				});
			};
			g.all = function (a) {
				var p = $jscomp.makeIterator(a),
					t = p.next();
				return t.done
					? m([])
					: new g(function (u, v) {
							function D(B) {
								return function (C) {
									w[B] = C;
									A--;
									0 == A && u(w);
								};
							}
							var w = [],
								A = 0;
							do
								w.push(void 0),
									A++,
									m(t.value).callWhenSettled_(D(w.length - 1), v),
									(t = p.next());
							while (!t.done);
						});
			};
			return g;
		},
		"es6",
		"es3",
	);
	$jscomp.checkStringArgs = function (l, h, m) {
		if (null == l)
			throw new TypeError(
				"The 'this' value for String.prototype." +
					m +
					" must not be null or undefined",
			);
		if (h instanceof RegExp)
			throw new TypeError(
				"First argument to String.prototype." +
					m +
					" must not be a regular expression",
			);
		return l + "";
	};
	(function (l) {
		function h(n) {
			if (m[n]) return m[n].exports;
			var g = (m[n] = { i: n, l: !1, exports: {} });
			l[n].call(g.exports, g, g.exports, h);
			g.l = !0;
			return g.exports;
		}
		var m = {};
		h.m = l;
		h.c = m;
		h.d = function (n, g, r) {
			h.o(n, g) || Object.defineProperty(n, g, { enumerable: !0, get: r });
		};
		h.r = function (n) {
			"undefined" !== typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(n, Symbol.toStringTag, { value: "Module" });
			Object.defineProperty(n, "__esModule", { value: !0 });
		};
		h.t = function (n, g) {
			g & 1 && (n = h(n));
			if (g & 8 || (g & 4 && "object" === typeof n && n && n.__esModule))
				return n;
			var r = Object.create(null);
			h.r(r);
			Object.defineProperty(r, "default", { enumerable: !0, value: n });
			if (g & 2 && "string" != typeof n)
				for (var a in n)
					h.d(
						r,
						a,
						function (p) {
							return n[p];
						}.bind(null, a),
					);
			return r;
		};
		h.n = function (n) {
			var g =
				n && n.__esModule
					? function () {
							return n["default"];
						}
					: function () {
							return n;
						};
			h.d(g, "a", g);
			return g;
		};
		h.o = function (n, g) {
			return Object.prototype.hasOwnProperty.call(n, g);
		};
		h.p = "/core";
		return h((h.s = 4));
	})([
		function (l, h, m) {
			m.d(h, "a", function () {
				return r;
			});
			m.d(h, "b", function () {
				return a;
			});
			var n = {},
				g = {
					flattenedResources: !1,
					CANVAS_CACHE_SIZE: void 0,
					maxPagesBefore: void 0,
					maxPagesAhead: void 0,
					disableLogs: !1,
					wvsQueryParameters: {},
					_trnDebugMode: !1,
					_logFiltersEnabled: null,
				},
				r = function (p) {
					return g[p];
				},
				a = function (p, t) {
					var u;
					g[p] = t;
					null === (u = n[p]) || void 0 === u
						? void 0
						: u.forEach(function (v) {
								v(t);
							});
				};
		},
		function (l, h, m) {
			(function (n) {
				m.d(h, "b", function () {
					return K;
				});
				m.d(h, "a", function () {
					return E;
				});
				m.d(h, "c", function () {
					return G;
				});
				m.d(h, "d", function () {
					return H;
				});
				var g = m(2),
					r = m(7);
				m.n(r);
				var a = window,
					p = (function () {
						return function (f, b) {
							this.fsQ = this.fs_read_counter = 0;
							this.needCallback = !1;
							this.fs_read_total = f;
							this.fsQ = b;
						};
					})();
				a.AsyncFSHack = { readingAsyncFS: 0, readingCounter: 0, readCalls: {} };
				var t = a.Module,
					u = 0,
					v = {},
					D = function (f) {
						a.AsyncFSHack.readingAsyncFS = 1;
						f in a.AsyncFSHack.readCalls ||
							(a.AsyncFSHack.readCalls[f] = new p(
								a.AsyncFSHack.read_total,
								a.AsyncFSHack.q,
							));
					},
					w = function (f) {
						var b = a.AsyncFSHack.readCalls[f];
						++b.fs_read_counter;
						b.fs_read_counter >= b.fs_read_total &&
							(b.needCallback
								? t._finish_do_call_operation(b.fsQ)
								: (a.AsyncFSHack.readingAsyncFS = 0),
							delete a.AsyncFSHack.readCalls[f]);
					},
					A = function (f, b, k, d, c, e, q) {
						this.lruList = [];
						this.chunkMap = {};
						this.chunkReader = {};
						this.chunkMap[b] = c;
						this.length = b;
						this.cacheDataSize = d;
						this.url = f;
						this.customHeaders = e;
						this.withCredentials = q;
						this.chunkSize = k;
					};
				A.prototype = {
					lruUpdate: function (f) {
						var b = this.lruList.lastIndexOf(f);
						0 <= b && this.lruList.splice(b, 1);
						this.lruList.push(f);
					},
					downloadChunk: function (f, b) {
						var k = !1;
						f in this.chunkReader || ((this.chunkReader[f] = []), (k = !0));
						b && this.chunkReader[f].push(b);
						if (k) {
							b = Math.min(f + this.chunkSize, this.length) - 1;
							var d = new XMLHttpRequest();
							d.open("GET", this.url, !0);
							d.responseType = "arraybuffer";
							d.setRequestHeader("Range", ["bytes=", f, "-", b].join(""));
							this.withCredentials &&
								(d.withCredentials = this.withCredentials);
							if (this.customHeaders)
								for (var c in this.customHeaders)
									d.setRequestHeader(c, this.customHeaders[c]);
							d.send();
							var e = this;
							d.onload = function () {
								if (200 === d.status || 206 === d.status) {
									var q = new Int8Array(d.response);
									e.writeChunk(q, f);
								} else
									window.parent.parentWarn(
										"Failed to load data from".concat(e.url),
									),
										(q = new Int8Array(0));
								for (var x = e.chunkReader[f], y = 0; y < x.length; y++)
									x[y](q);
								delete e.chunkReader[f];
							};
						}
					},
					hadChunk: function (f) {
						return f in this.chunkMap;
					},
					hasChunk: function (f) {
						return this.chunkMap[f];
					},
					getCacheData: function () {
						return this.chunkMap[this.length];
					},
					updateCache: function (f) {
						for (var b = 0; b < f.length; b++) {
							var k = f[b];
							this.hadChunk(k) &&
								(this.hasChunk(k) ? this.lruUpdate(k) : this.downloadChunk(k));
						}
					},
					wrapChunkRetrieval: function (f, b, k) {
						this.downloadChunk(f, function (d) {
							k(d, b);
						});
					},
					downloadChunks: function (f, b) {
						for (
							var k = f.length,
								d = Array(k),
								c = 0,
								e = function (x, y) {
									d[y] = x;
									++c;
									c === k && b(d);
								},
								q = 0;
							q < k;
							++q
						)
							this.wrapChunkRetrieval(f[q][0], q, e);
					},
					readFromCache: function (f, b, k) {
						var d = [],
							c = 0,
							e = Math.floor(b / this.chunkSize) * this.chunkSize,
							q = b % this.chunkSize;
						b = this.chunkSize - q;
						b = b > k ? k : b;
						this.chunkMap[e]
							? ((q = this.chunkMap[e].subarray(q, q + b)),
								f.set(q),
								this.lruUpdate(e))
							: this.hadChunk(e)
								? d.push([e, q, b, c])
								: ((q = new Int8Array(b)), f.set(q));
						for (k -= b; 0 < k; )
							(c += b),
								(e += this.chunkSize),
								(b = this.chunkSize > k ? k : this.chunkSize),
								this.chunkMap[e]
									? ((q = this.chunkMap[e].subarray(0, b)),
										f.set(q, c),
										this.lruUpdate(e))
									: this.hadChunk(e)
										? d.push([e, 0, b, c])
										: ((q = new Int8Array(b)), f.set(q, c)),
								(k -= b);
						return d;
					},
					writeChunk: function (f, b, k) {
						Object(g.a)(this.lruList, this.chunkMap, this.chunkSize, f, b, k);
						this.lruUpdate(b);
					},
				};
				var B = function (f) {
					this.chunkStorage = f;
					this.position = 0;
					this.length = this.chunkStorage.length;
				};
				B.prototype = {
					read: function (f, b, k, d) {
						var c = d + k <= this.length,
							e = c ? k : this.length - d,
							q = a.AsyncFSHack.readingCounter.toString();
						if (0 < e) {
							D(q);
							var x = f.subarray(b, b + e);
							var y = this.chunkStorage.readFromCache(x, d, e);
							y.length
								? ((a.AsyncFSHack.readCalls[q].needCallback = !0),
									this.chunkStorage.downloadChunks(y, function (M) {
										for (var I = 0; I < M.length; ++I) {
											var J = y[I],
												N = M[I].subarray(J[1], J[1] + J[2]);
											x.set(N, J[3]);
										}
										w(q);
									}))
								: c && w(q);
							d += e;
						} else e = 0;
						if (!c) {
							D(q);
							b += e;
							if ((k -= e)) {
								c = this.chunkStorage.getCacheData();
								k > c.length && (k = c.length);
								var L = d - this.length;
								k -= L;
								f = f.subarray(b, b + k);
								b = c.subarray(L, L + k);
								f.set(b);
							}
							(y && y.length) || w(q);
							d += k;
							e += k;
						}
						this.position = d;
						return e;
					},
					write: function (f, b, k, d) {
						var c = d + k <= this.length,
							e = d + k <= this.length ? k : this.length - d;
						if (0 < e) {
							var q = f.subarray(b, b + e),
								x = d % this.chunkStorage.chunkSize;
							this.chunkStorage.writeChunk(q, d - x, x);
							d += e;
						} else e = 0;
						if (!c) {
							q = b + e;
							if ((k -= e))
								(b = this.chunkStorage.getCacheData()),
									k > b.length && (k = b.length),
									(c = d - this.length),
									(k -= c),
									(q = f.subarray(q, q + k)),
									b.subarray(c, c + k).set(q);
							d += k;
							e += k;
						}
						this.position = d;
						return e;
					},
					seek: function (f) {
						this.position = f;
					},
					close: function () {
						this.chunkStorage = null;
					},
					getPos: function () {
						return this.position;
					},
					getTotalSize: function () {
						return this.length + this.chunkStorage.cacheDataSize;
					},
				};
				var C = function (f) {
					this.file = f;
					this.filePosition = 0;
					this.fileLength = f.size;
					this.readerPool = [];
				};
				C.prototype = {
					readFromFile: function (f, b, k) {
						var d = this.readerPool.length
							? this.readerPool.pop()
							: new FileReader();
						var c = this;
						d.onload = function () {
							var e = new Int8Array(d.result);
							c.readerPool.push(d);
							k(e);
						};
						d.readAsArrayBuffer(this.file.slice(b, b + f));
					},
					read: function (f, b, k, d) {
						k = d + k <= this.fileLength ? k : this.fileLength - d;
						if (0 < k) {
							var c = a.AsyncFSHack.readingCounter.toString();
							D(c);
							var e = f.subarray(b, b + k);
							a.AsyncFSHack.readCalls[c].needCallback = !0;
							this.readFromFile(k, d, function (q) {
								e.set(q);
								w(c);
							});
							this.filePosition = d + k;
						}
						return k;
					},
					seek: function (f) {
						this.filePosition = f;
					},
					close: function () {
						this.reader = this.file = null;
					},
					getPos: function () {
						return this.filePosition;
					},
					getTotalSize: function () {
						return this.fileLength;
					},
				};
				var F = {
						open: function (f) {
							var b = f.path.slice(1);
							f.provider = t.docs[b].chunkStorage
								? new B(t.docs[b].chunkStorage)
								: new C(t.docs[b]);
						},
						close: function (f) {
							f.provider.close();
						},
						read: function (f, b, k, d, c) {
							return f.provider.read(b, k, d, c);
						},
						llseek: function (f, b, k) {
							f = f.provider;
							1 === k
								? (b += f.getPos())
								: 2 === k && (b = f.getTotalSize() + b);
							if (0 > b) throw new a.FS.ErrnoError(n.ERRNO_CODES.EINVAL);
							f.seek(b);
							return b;
						},
						write: function (f, b, k, d, c) {
							return d ? f.provider.write(b, k, d, c) : 0;
						},
					},
					z = function (f) {
						if (!v[f]) {
							var b = a.FS.makedev(3, 5);
							a.FS.registerDevice(b, F);
							a.FS.mkdev(f, 511, b);
							v[f] = !0;
						}
					},
					K = function (f, b, k, d, c) {
						var e = "docdev".concat(++u);
						z(e);
						var q = Math.ceil((b + k - 1) / k / 8),
							x = new Int8Array(new ArrayBuffer(q));
						f = new A(f, b, k, q, x, d, c);
						t.docs[e] = { chunkStorage: f };
						return e;
					},
					E = function (f) {
						var b = "docdev".concat(++u);
						z(b);
						t.docs[b] = f;
						return b;
					},
					G = function (f) {
						a.FS.unlink(f);
						t.docs[f] && delete t.docs[f];
					},
					H = function (f) {
						var b = Object.prototype.toString.call(f);
						return (
							"object" === typeof f &&
							null !== f &&
							("[object File]" === b || "[object Blob]" === b)
						);
					};
			}).call(this, m(6));
		},
		function (l, h, m) {
			function n(g, r, a, p, t, u) {
				u = u || 0;
				var v = r[t],
					D = p.length,
					w = 100 <= g.length && !v;
				D !== a || p.buffer.byteLength !== D
					? (w
							? ((g = g.shift()),
								(v = r[g]),
								v.length < a && (v = new Int8Array(a)),
								(r[g] = null))
							: (v = v ? r[t] : new Int8Array(a)),
						v.subarray(u, u + D).set(p),
						(p = v))
					: w && ((g = g.shift()), (r[g] = null));
				r[t] = p;
			}
			m.d(h, "a", function () {
				return n;
			});
			m(3);
		},
		function (l, h, m) {
			m.d(h, "a", function () {
				return g;
			});
			var n = m(0),
				g = function (r, a) {
					Object(n.a)("disableLogs") ||
						(a ? console.warn("".concat(r, ": ").concat(a)) : console.warn(r));
				};
		},
		function (l, h, m) {
			l.exports = m(5);
		},
		function (l, h, m) {
			function n(b) {
				"@babel/helpers - typeof";
				return (
					(n =
						"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
							? function (k) {
									return typeof k;
								}
							: function (k) {
									return k &&
										"function" == typeof Symbol &&
										k.constructor === Symbol &&
										k !== Symbol.prototype
										? "symbol"
										: typeof k;
								}),
					n(b)
				);
			}
			m.r(h);
			var g = m(1),
				r = window,
				a;
			r.Module.onRuntimeInitialized = function () {
				p = !0;
				u || (E.handleMessage({ action: "workerLoaded", data: {} }), (u = !0));
				E.sendTestResponse();
				r.PThread.receiveObjectTransfer = function () {
					var b = r.Module.GetNextResponseMessage();
					r.getThreadedWasmWorker().handleMessage(b);
					r.Module.PopNextResponseMessage();
				};
			};
			r.Module.locateFile = function (b) {
				return a + b;
			};
			r.Module.INITIAL_MEMORY = 100663296;
			var p = !1,
				t = !1,
				u = !1,
				v = 1,
				D = function d(k) {
					if ("object" === n(k) && null !== k)
						if ("undefined" !== typeof k.byteLength) {
							var c = "mainThreadArrayBuffer".concat(v);
							v++;
							k = new Uint8Array(k);
							r.FS.writeFile(c, k);
							k = { handle: c, isArrayBufferRef: !0 };
						} else for (c in k) k.hasOwnProperty(c) && (k[c] = d(k[c]));
					return k;
				},
				w = function c(d) {
					if ("object" === n(d) && null !== d)
						if (d.isArrayBufferRef) {
							var e = r.FS.readFile(d.handle, { encoding: "binary" });
							r.FS.unlink(d.handle);
							d = e.buffer;
						} else if (d.constructor === Uint8Array)
							d = new Uint8Array(d).buffer;
						else for (e in d) d.hasOwnProperty(e) && (d[e] = c(d[e]));
					return d;
				},
				A = 0,
				B = {},
				C = {},
				F = {},
				z = {},
				K = function (d) {
					var c = d.action,
						e = d.data,
						q = d.callbackId;
					if ("NewDoc" === c && e)
						(c = e.value),
							"url" === e.type
								? ((c = Object(g.b)(
										c.url,
										c.size,
										e.chunkSize,
										c.customHeaders,
										c.withCredentials,
									)),
									(C[q] = c),
									(e.value.docDevId = c))
								: Object(g.d)(c) &&
									((c = Object(g.a)(c)), (C[q] = c), (e.value = c));
					else if (("SaveDoc" !== c && "SaveDocFromFixedElements" !== c) || !e)
						"GetCanvas" === c && e
							? ((q = e.docId),
								q in z &&
									((c = z[q]),
									c in r.Module.docs &&
										((c = r.Module.docs[c]),
										"chunkStorage" in c &&
											((e = r.Module.GetRequiredChunkOffsetArray(
												q,
												e.pageIndex + 1,
											)),
											e.length && c.chunkStorage.updateCache(e)))))
							: "DeleteDocument" === c &&
								e &&
								((e = e.docId), e in z && (F[q] = e));
					else {
						c = e.docId;
						var x = e.finishedWithDocument,
							y = "docFilePath".concat(A);
						B[q] = { filePath: y, docId: c, finishedWithDocument: x };
						A++;
						e.filePath = y;
					}
					r.Module.HandleMessage(D(d));
				},
				E;
			r.MainThreadLabel = !0;
			r.getThreadedWasmWorker = function () {
				return E;
			};
			var G = function (d) {
				window.parent.loadThreadedBackend(
					d,
					{ "Wasm.wasm": 1e7, "Wasm.js": 15e4 },
					window,
				);
				this.eventListeners = [];
			};
			G.prototype = {
				addEventListener: function (d, c) {
					if ("message" === d) this.eventListeners.push(c);
					else
						throw Error(
							"event type ".concat(d, " is not supported by WasmWorker."),
						);
				},
				removeEventListener: function (d, c) {
					"message" === d &&
						(this.eventListeners = this.eventListeners.filter(function (e) {
							return e !== c;
						}));
				},
				sendTestResponse: function () {
					p &&
						t &&
						(this.handleMessage({
							action: "test",
							data: { supportTypedArray: !0, supportTransfers: !0 },
						}),
						(this.postMessage = K));
				},
				postMessage: function (d) {
					if ("test" === d.action) (t = !0), this.sendTestResponse();
					else throw Error("Need to do handshake first!");
				},
				handleMessage: function (d) {
					var c = d.callbackId,
						e = d.data;
					if (c in C)
						e && e.docId ? (z[e.docId] = C[c]) : Object(g.c)(C[c]), delete C[c];
					else if (c in B) {
						if (!d.hasOwnProperty("error")) {
							var q = B[c].filePath,
								x = r.FS.readFile(q, { encoding: "binary" });
							e.fileData = x.buffer;
							e = B[c].docId;
							e in z && (Object(g.c)(z[e]), delete z[e]);
							e && !B[c].finishedWithDocument ? (z[e] = q) : r.FS.unlink(q);
						}
						delete B[c];
					} else
						c in F &&
							((q = F[c]),
							q in z && (Object(g.c)(z[q]), delete z[q]),
							delete F[c]);
					d = w(d);
					window.parent.postMessage(d);
				},
				reset: function () {
					E = null;
					u = t = p = !1;
				},
			};
			var H = (function () {
					var d = {},
						c = new Promise(function (e, q) {
							d.resolve = e;
							d.reject = q;
						});
					d.promise = c;
					return d;
				})(),
				f = function e(c) {
					"object" === n(c.data) &&
						"action" in c.data &&
						"workerLoaded" === c.data.action &&
						(H.resolve(E), E.removeEventListener("message", e));
				};
			window.addEventListener("message", function (c) {
				var e = c.data;
				c.origin === window.location.origin && "loadWasmWorker" === e.action
					? ((a = e.workerFolder),
						(E = new G("".concat(e.wasmSource, "PDFNetThreaded"))),
						u || E.addEventListener("message", f))
					: E.postMessage(e);
			});
			r.getWasmWorkerPromise = function () {
				return H.promise;
			};
		},
		function (l, h) {
			h = (function () {
				return this;
			})();
			try {
				h = h || new Function("return this")();
			} catch (m) {
				"object" === typeof window && (h = window);
			}
			l.exports = h;
		},
		function (l, h) {
			window.Module = { chunkMax: 100, docs: {} };
		},
	]);
}).call(this || window);
