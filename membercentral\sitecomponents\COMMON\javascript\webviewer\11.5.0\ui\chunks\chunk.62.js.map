{"version": 3, "sources": ["webpack:///./src/ui/src/components/ErrorModal/ErrorModal.scss?4561", "webpack:///./src/ui/src/components/ErrorModal/ErrorModal.scss", "webpack:///./src/ui/src/components/ErrorModal/ErrorModal.js", "webpack:///./src/ui/src/components/ErrorModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "ErrorModal", "useSelector", "state", "selectors", "getErrorMessage", "getErrorTitle", "isElementDisabled", "DataElements", "ERROR_MODAL", "isElementOpen", "shallowEqual", "message", "title", "isDisabled", "isOpen", "dispatch", "useDispatch", "t", "useTranslation", "isTrialError", "includes", "useEffect", "actions", "closeElements", "SIGNATURE_MODAL", "PRINT_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "PASSWORD_MODAL", "FILTER_MODAL", "addEventListener", "e", "escapePressListener", "closeErrorModal", "removeEventListener", "shouldTranslate", "startsWith", "closeElement", "open", "buttonLabel", "className", "classNames", "Modal", "closed", "data-element", "ModalWrapper", "closeButtonDataElement", "onCloseClick", "<PERSON><PERSON>", "dataElement", "label", "onClick"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,y/HAA0/H,KAGnhI0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,4wCCGvB,IAgFeC,EAhFI,WACjB,IAQC,IAR4CC,aAC3C,SAACC,GAAK,MAAK,CACTC,IAAUC,gBAAgBF,GAC1BC,IAAUE,cAAcH,GACxBC,IAAUG,kBAAkBJ,EAAOK,IAAaC,aAChDL,IAAUM,cAAcP,EAAOK,IAAaC,gBAE9CE,KACD,GARMC,EAAO,KAAEC,EAAK,KAAEC,EAAU,KAAEC,EAAM,KASnCC,EAAWC,cACVC,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAe,SAACR,GACpB,OAAOA,aAAO,EAAPA,EAASS,SAAS,mBAG3BC,qBAAU,WACR,GAAIP,EAaF,OAZAC,EACEO,IAAQC,cAAc,CACpBhB,IAAaiB,gBACbjB,IAAakB,YACblB,IAAamB,cACbnB,IAAaoB,eACbpB,IAAaqB,eACbrB,IAAasB,gBAIjBtD,OAAOuD,iBAAiB,WAAW,SAACC,GAAC,OAAKC,YAAoBD,EAAGE,MAC1D,kBAAM1D,OAAO2D,oBAAoB,UAAWF,QAEpD,CAACjB,EAAUD,IAEd,IAAMqB,EAAkBxB,EAAQyB,WAAW,YAErCH,EAAkB,WACtBlB,EAASO,IAAQe,aAAa9B,IAAaC,cACvCW,EAAaR,IACfpC,OAAO+D,KAAK,yBAA0B,WAItCC,EAActB,EAAE,aAMpB,OAJIE,EAAaR,KACf4B,EAAc,iBAGT1B,EAAa,KAClB,yBACE2B,UAAWC,IAAW,CACpBC,OAAO,EACP1C,YAAY,EACZsC,KAAMxB,EACN6B,QAAS7B,IAEX8B,eAAcrC,IAAaC,aAE3B,kBAACqC,EAAA,EAAY,CAAC/B,OAAQA,EAAQF,MAAOA,GAAS,gBAC5CkC,uBAAwB,wBACxBC,aAAcd,GAEd,yBAAKO,UAAU,qCACb,2BAAIL,EAAkBlB,EAAEN,GAAWA,IAErC,yBAAK6B,UAAU,uBACb,kBAACQ,EAAA,EAAM,CACLR,UAAU,uBACVS,YAAY,wBACZC,MAAOX,EACPY,QAASlB,QCnFNjC", "file": "chunks/chunk.62.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./ErrorModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.ErrorModal{visibility:visible}.closed.ErrorModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.ErrorModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.ErrorModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.ErrorModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.ErrorModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.ErrorModal .footer .modal-button.cancel:hover,.ErrorModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.ErrorModal .footer .modal-button.cancel,.ErrorModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.ErrorModal .footer .modal-button.cancel.disabled,.ErrorModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.ErrorModal .footer .modal-button.cancel.disabled span,.ErrorModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.ErrorModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.ErrorModal .modal-container .wrapper .modal-content{padding:10px}.ErrorModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.ErrorModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.ErrorModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.ErrorModal .footer .modal-button.confirm{margin-left:4px}.ErrorModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .footer .modal-button{padding:23px 8px}}.ErrorModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ErrorModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ErrorModal .swipe-indicator{width:32px}}.ErrorModal .modal-container{width:300px;word-break:break-word;white-space:pre-wrap}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect } from 'react';\nimport classNames from 'classnames';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport Button from 'components/Button';\nimport { escapePressListener } from 'helpers/accessibility';\nimport ModalWrapper from '../ModalWrapper';\nimport DataElements from 'constants/dataElement';\n\nimport './ErrorModal.scss';\n\nconst ErrorModal = () => {\n  const [message, title, isDisabled, isOpen] = useSelector(\n    (state) => [\n      selectors.getErrorMessage(state),\n      selectors.getErrorTitle(state),\n      selectors.isElementDisabled(state, DataElements.ERROR_MODAL),\n      selectors.isElementOpen(state, DataElements.ERROR_MODAL),\n    ],\n    shallowEqual\n  );\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n\n  const isTrialError = (message) => {\n    return message?.includes('dev.apryse.com');\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(\n        actions.closeElements([\n          DataElements.SIGNATURE_MODAL,\n          DataElements.PRINT_MODAL,\n          DataElements.LOADING_MODAL,\n          DataElements.PROGRESS_MODAL,\n          DataElements.PASSWORD_MODAL,\n          DataElements.FILTER_MODAL\n        ])\n      );\n\n      window.addEventListener('keydown', (e) => escapePressListener(e, closeErrorModal));\n      return () => window.removeEventListener('keydown', escapePressListener);\n    }\n  }, [dispatch, isOpen]);\n\n  const shouldTranslate = message.startsWith('message.');\n\n  const closeErrorModal = () => {\n    dispatch(actions.closeElement(DataElements.ERROR_MODAL));\n    if (isTrialError(message)) {\n      window.open('https://dev.apryse.com', '_blank');\n    }\n  };\n\n  let buttonLabel = t('action.ok');\n\n  if (isTrialError(message)) {\n    buttonLabel = 'Get trial key';\n  }\n\n  return isDisabled ? null : (\n    <div\n      className={classNames({\n        Modal: true,\n        ErrorModal: true,\n        open: isOpen,\n        closed: !isOpen,\n      })}\n      data-element={DataElements.ERROR_MODAL}\n    >\n      <ModalWrapper isOpen={isOpen} title={title || 'message.error'}\n        closeButtonDataElement={'errorModalCloseButton'}\n        onCloseClick={closeErrorModal}\n      >\n        <div className=\"modal-content error-modal-content\">\n          <p>{shouldTranslate ? t(message) : message}</p>\n        </div>\n        <div className=\"modal-footer footer\">\n          <Button\n            className=\"confirm modal-button\"\n            dataElement=\"closeErrorModalButton\"\n            label={buttonLabel}\n            onClick={closeErrorModal}\n          />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default ErrorModal;\n", "import ErrorModal from './ErrorModal';\n\nexport default ErrorModal;\n"], "sourceRoot": ""}