{"version": 3, "sources": ["webpack:///./src/ui/node_modules/core-js/modules/es.promise.finally.js"], "names": ["$", "IS_PURE", "NativePromiseConstructor", "fails", "getBuiltIn", "isCallable", "speciesConstructor", "promiseResolve", "defineBuiltIn", "NativePromisePrototype", "prototype", "target", "proto", "real", "forced", "call", "then", "onFinally", "C", "this", "isFunction", "x", "e", "method", "unsafe"], "mappings": "4FACA,IAAIA,EAAI,EAAQ,IACZC,EAAU,EAAQ,KAClBC,EAA2B,EAAQ,KACnCC,EAAQ,EAAQ,IAChBC,EAAa,EAAQ,KACrBC,EAAa,EAAQ,IACrBC,EAAqB,EAAQ,KAC7BC,EAAiB,EAAQ,KACzBC,EAAgB,EAAQ,KAExBC,EAAyBP,GAA4BA,EAAyBQ,UA0BlF,GAhBAV,EAAE,CAAEW,OAAQ,UAAWC,OAAO,EAAMC,MAAM,EAAMC,SAP5BZ,GAA4BC,GAAM,WAEpDM,EAAgC,QAAEM,KAAK,CAAEC,KAAM,eAA+B,mBAKT,CACrE,QAAW,SAAUC,GACnB,IAAIC,EAAIZ,EAAmBa,KAAMf,EAAW,YACxCgB,EAAaf,EAAWY,GAC5B,OAAOE,KAAKH,KACVI,EAAa,SAAUC,GACrB,OAAOd,EAAeW,EAAGD,KAAaD,MAAK,WAAc,OAAOK,MAC9DJ,EACJG,EAAa,SAAUE,GACrB,OAAOf,EAAeW,EAAGD,KAAaD,MAAK,WAAc,MAAMM,MAC7DL,OAMLhB,GAAWI,EAAWH,GAA2B,CACpD,IAAIqB,EAASnB,EAAW,WAAWM,UAAmB,QAClDD,EAAgC,UAAMc,GACxCf,EAAcC,EAAwB,UAAWc,EAAQ,CAAEC,QAAQ", "file": "chunks/chunk.12.js", "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar IS_PURE = require('../internals/is-pure');\nvar NativePromiseConstructor = require('../internals/promise-native-constructor');\nvar fails = require('../internals/fails');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar speciesConstructor = require('../internals/species-constructor');\nvar promiseResolve = require('../internals/promise-resolve');\nvar defineBuiltIn = require('../internals/define-built-in');\n\nvar NativePromisePrototype = NativePromiseConstructor && NativePromiseConstructor.prototype;\n\n// Safari bug https://bugs.webkit.org/show_bug.cgi?id=200829\nvar NON_GENERIC = !!NativePromiseConstructor && fails(function () {\n  // eslint-disable-next-line unicorn/no-thenable -- required for testing\n  NativePromisePrototype['finally'].call({ then: function () { /* empty */ } }, function () { /* empty */ });\n});\n\n// `Promise.prototype.finally` method\n// https://tc39.es/ecma262/#sec-promise.prototype.finally\n$({ target: 'Promise', proto: true, real: true, forced: NON_GENERIC }, {\n  'finally': function (onFinally) {\n    var C = speciesConstructor(this, getBuiltIn('Promise'));\n    var isFunction = isCallable(onFinally);\n    return this.then(\n      isFunction ? function (x) {\n        return promiseResolve(C, onFinally()).then(function () { return x; });\n      } : onFinally,\n      isFunction ? function (e) {\n        return promiseResolve(C, onFinally()).then(function () { throw e; });\n      } : onFinally\n    );\n  }\n});\n\n// makes sure that native promise-based APIs `Promise#finally` properly works with patched `Promise#then`\nif (!IS_PURE && isCallable(NativePromiseConstructor)) {\n  var method = getBuiltIn('Promise').prototype['finally'];\n  if (NativePromisePrototype['finally'] !== method) {\n    defineBuiltIn(NativePromisePrototype, 'finally', method, { unsafe: true });\n  }\n}\n"], "sourceRoot": ""}