{"version": 3, "sources": ["webpack:///./src/ui/src/components/Header/OfficeHeader.scss?7d4c", "webpack:///./src/ui/src/components/Header/OfficeHeader.scss", "webpack:///./src/ui/src/components/Header/OfficeEditorToolsHeader.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "officeEditorToggleableStyles", "Core", "Document", "OfficeEditor", "ToggleableStyles", "TextStyles", "activeStates", "Object", "values", "map", "style", "key", "isActive", "ariaPressed", "onClick", "core", "getOfficeEditor", "updateSelectionAndCursorStyle", "dataElement", "title", "img", "JustificationOptions", "justification", "JUSTIFICATION_OPTIONS", "Left", "updateParagraphStyle", "Center", "Right", "Both", "ListOptions", "listType", "bulletListObjects", "OFFICE_BULLET_OPTIONS", "className", "src", "numberListOptions", "OFFICE_NUMBER_OPTIONS", "LIST_OPTIONS", "Unordered", "toggleListSelection", "id", "images", "columns", "onClickItem", "val", "setListPreset", "Ordered", "decreaseIndent", "increaseIndent", "propTypes", "PropTypes", "string", "OfficeEditorToolsHeader", "dispatch", "useDispatch", "useSelector", "state", "selectors", "isElementOpen", "DataElement", "OFFICE_EDITOR_TOOLS_HEADER", "getOfficeEditorCursorProperties", "isCursorInTable", "getOfficeEditorSelectionProperties", "getAvailableFontFaces", "getActiveTheme", "getCSSFontValues", "isNonPrintingCharactersEnabled", "shallowEqual", "isOpen", "cursorProperties", "selectionProperties", "availableFontFaces", "activeTheme", "cssFontValues", "enableNonPrintingCharacters", "useState", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialHeader<PERSON>idth", "setInitialHeaderWidth", "visibleGroupCount", "setVisibleGroupCount", "showMoreTools", "setShowMoreTools", "useEffect", "onCursorPropertiesUpdated", "actions", "setOfficeEditorCursorProperties", "onSelectionPropertiesUpdated", "setOfficeEditorSelectionProperties", "getDocument", "addEventListener", "removeEventListener", "fontFace", "includes", "addOfficeEditorAvailableFontFace", "actualContainer<PERSON><PERSON>th", "properties", "isTextSelected", "isBold", "bold", "isItalic", "italic", "isUnderline", "underlineStyle", "pointSize", "pointSizeSelectionKey", "undefined", "toString", "paragraphProperties", "lineHeight", "calculateLineSpacing", "lineHeightMultiplier", "fontPointSize", "DEFAULT_POINT_SIZE", "isLightMode", "Theme", "LIGHT", "wvFontColor", "convertCoreColorToWebViewerColor", "color", "useColorIconBorder", "aria<PERSON><PERSON><PERSON>", "toHexString", "bounds", "onResize", "width", "measureRef", "ref", "items", "keys", "AVAILABLE_STYLE_PRESET_MAP", "onOpened", "item", "stylePreset", "parseInt", "fontSize", "fontColor", "Annotations", "Color", "parsedFontColor", "r", "R", "g", "G", "b", "B", "a", "newTextStyle", "underline", "updateParagraphStylePresets", "setMainCursorStyle", "getCustomItemStyle", "padding", "applyCustomStyleToButton", "currentSelectionKey", "convertCursorToStylePreset", "maxHeight", "customDataValidator", "font", "hasInput", "AVAILABLE_POINT_SIZES", "isNaN", "FONT_SIZE", "MAX", "MIN", "isSearchEnabled", "OFFICE_EDITOR_TEXT_COLOR_BUTTON", "element", "OFFICE_EDITOR_COLOR_PICKER_OVERLAY", "iconClassName", "onStyleChange", "_", "newColor", "closeElements", "LINE_SPACING_OPTIONS", "lineSpacingOption", "lineSpacing", "displayButton", "OFFICE_EDITOR_PAGE_BREAK", "disabled", "insertPageBreak", "OFFICE_EDITOR_TOOLS_HEADER_INSERT_TABLE", "glyph", "OFFICE_EDITOR_TOOLS_HEADER_INSERT_IMAGE", "openOfficeEditorFilePicker"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,2zGAA4zG,KAGr1G0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,k9ECTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,igCAkCA,IAGM4B,EAA+BzB,OAAO0B,KAAKC,SAASC,aAAaC,iBAEjEC,EAAa,SAAH,GAAyB,IAAnBC,EAAY,EAAZA,aACpB,OAAOC,OAAOC,OAAOR,GAA8BS,KAAI,SAACC,GAAK,OAC3D,kBAAC,IAAY,CACXC,IAAKD,EACLE,SAAUN,EAAaI,GACvBG,YAAaP,EAAaI,GAC1BI,QAAS,WACPC,IAAKC,kBAAkBC,8BAA8B,EAAD,GAAIP,GAAQ,KAElEQ,YAAW,wBAAmBR,GAC9BS,MAAK,uBAAkBT,GACvBU,IAAG,oBAAeV,SAKlBW,EAAuB,SAAH,GAA0B,IAApBC,EAAa,EAAbA,cAC9B,OACE,oCACE,kBAAC,IAAY,CACXV,SAAUU,IAAkBC,IAAsBC,KAClDN,YAAY,2BACZC,MAAM,yBACNC,IAAI,uBACJN,QAAS,WACPC,IAAKC,kBAAkBS,qBAAqB,CAC1CH,cAAe,YAIrB,kBAAC,IAAY,CACXV,SAAUU,IAAkBC,IAAsBG,OAClDR,YAAY,6BACZC,MAAM,2BACNC,IAAI,yBACJN,QAAS,WACPC,IAAKC,kBAAkBS,qBAAqB,CAC1CH,cAAe,cAIrB,kBAAC,IAAY,CACXV,SAAUU,IAAkBC,IAAsBI,MAClDT,YAAY,4BACZC,MAAM,0BACNC,IAAI,wBACJN,QAAS,WACPC,IAAKC,kBAAkBS,qBAAqB,CAC1CH,cAAe,aAIrB,kBAAC,IAAY,CACXV,SAAUU,IAAkBC,IAAsBK,KAClDV,YAAY,wBACZC,MAAM,uBACNC,IAAI,uBACJN,QAAS,WACPC,IAAKC,kBAAkBS,qBAAqB,CAC1CH,cAAe,cAQrBO,EAAc,SAAH,GAAqB,IAAfC,EAAQ,EAARA,SACfC,EAAoBC,IAAsBvB,KAAI,SAACpC,GAAO,MAAM,CAChE4D,UAAW,+BACXtB,IAAKtC,EAAO,KACZ6D,IAAK7D,EAAQ+C,QAGTe,EAAoBC,IAAsB3B,KAAI,SAACpC,GAAO,MAAM,CAChE4D,UAAW,+BACXtB,IAAKtC,EAAO,KACZ6D,IAAK7D,EAAQ+C,QAGf,OACE,oCACE,kBAAC,IAAY,CACXR,SAAUkB,IAAaO,IAAaC,UACpCpB,YAAY,0BACZC,MAAM,0BACNC,IAAI,iCACJa,UAAU,oBACVnB,QAAS,WACPC,IAAKC,kBAAkBuB,oBAAoBF,IAAaC,cAG5D,kBAAC,IAAQ,CACPE,GAAG,qCACHtB,YAAY,qCACZuB,OAAQV,EACRW,QAAS,EACTC,YAAa,SAACC,GACZ7B,IAAKC,kBAAkB6B,cAAcD,IAEvCX,UAAU,wBAEZ,kBAAC,IAAY,CACXrB,SAAUkB,IAAaO,IAAaS,QACpC5B,YAAY,4BACZC,MAAM,0BACNC,IAAI,iCACJa,UAAU,oBACVnB,QAAS,WACPC,IAAKC,kBAAkBuB,oBAAoBF,IAAaS,YAG5D,kBAAC,IAAQ,CACPN,GAAG,qCACHtB,YAAY,qCACZuB,OAAQN,EACRO,QAAS,EACTC,YAAa,SAACC,GACZ7B,IAAKC,kBAAkB6B,cAAcD,IAEvCX,UAAU,wBAEZ,kBAAC,IAAY,CACXf,YAAY,uBACZC,MAAM,8BACNC,IAAI,qBACJN,QAAO,YAAE,8FACDC,IAAKC,kBAAkB+B,iBAAgB,6CAGjD,kBAAC,IAAY,CACX7B,YAAY,uBACZC,MAAM,8BACNC,IAAI,qBACJN,QAAO,YAAE,8FACDC,IAAKC,kBAAkBgC,iBAAgB,+CAOvDnB,EAAYoB,UAAY,CACtBnB,SAAUoB,IAAUC,QAgXPC,UA7WiB,WAAM,MAC9BC,EAAWC,cAsBhB,IAZGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,cAAcF,EAAOG,IAAYC,4BAC3CH,IAAUI,gCAAgCL,GAC1CC,IAAUK,gBAAgBN,GAC1BC,IAAUM,mCAAmCP,GAC7CC,IAAUO,sBAAsBR,GAChCC,IAAUQ,eAAeT,GACzBC,IAAUS,iBAAiBV,GAC3BC,IAAUU,+BAA+BX,MAE3CY,KACD,GApBCC,EAAM,KACNC,EAAgB,KAChBR,EAAe,KACfS,EAAmB,KACnBC,EAAkB,KAClBC,EAAW,KACXC,EAAa,KACbC,EAA2B,KAe0B,IAAXC,mBAAS,GAAE,GAAhDC,EAAc,KAAEC,EAAiB,KACuB,IAAXF,mBAAS,GAAE,GAAxDG,EAAkB,KAAEC,EAAqB,KACa,IAAXJ,mBAAS,GAAE,GAAtDK,EAAiB,KAAEC,EAAoB,KACW,IAAfN,oBAAS,GAAM,GAAlDO,EAAa,KAAEC,EAAgB,KAEtCC,qBAAU,WAAM,MACRC,EAAyB,6BAAG,WAAOhB,GAAgB,iEACvDjB,EAASkC,IAAQC,gCAAgClB,IAAmB,2CACrE,gBAF8B,sCAGzBmB,EAA+B,SAAClB,GACpClB,EAASkC,IAAQG,mCAAmCnB,KAMtD,OAHAxD,IAAK4E,cAAcC,iBAAiB,0BAA2BN,GAC7C,QAAlB,EAAAvE,IAAK4E,qBAAa,OAAlB,EAAoBC,iBAAiB,6BAA8BH,GAE5D,WACL1E,IAAK4E,cAAcE,oBAAoB,6BAA8BJ,GACrE1E,IAAK4E,cAAcE,oBAAoB,0BAA2BP,MAEnE,IAEHD,qBAAU,WACJf,EAAiBwB,WAAatB,EAAmBuB,SAASzB,EAAiBwB,WAC7EzC,EAASkC,IAAQS,iCAAiC1B,EAAiBwB,aAEpE,CAACtB,EAAoBF,IAExBe,qBAAU,WACR,GAAuB,IAAnBR,GAA+C,IAAvBE,EAA5B,CAIA,IAAMkB,EAAuBpB,EAAiB,GAI5CK,EAFAe,GAAwBlB,EAEH,EAErBkB,EAAuBlB,GACvBkB,GAAyBlB,EAxNN,IAED,GAwNG,EAErBkB,EAAwBlB,EA5NL,IAED,IA2NlBkB,GAAyBlB,EA7NN,IACS,IACV,IA4NlBkB,GAAwB,IAEH,EAEA,MAEtB,CAACpB,EAAgBE,IAEpB,IACMmB,EADiBnF,IAAKC,kBAAkBmF,iBACV5B,EAAsBD,EACpD8B,EAASF,EAAWG,KACpBC,EAAWJ,EAAWK,OACtBC,EAA4C,WAA9BN,EAAWO,eACzBX,GAAWI,EAAWJ,UAAY,GAClCY,GAAYR,EAAWQ,UACvBC,QAAsCC,IAAdF,GAA0B,GAAKA,GAAUG,WACjEvF,GAAgB4E,EAAWY,oBAAoBxF,cAC/CyF,GAAaC,YACjBd,EAAWY,oBAAoBG,qBAC/Bf,EAAWY,oBAAoBC,WAC/BzC,EAAiBwC,oBAAoBI,eAAiBC,KAElDrF,GAAWoE,EAAWY,oBAAoBhF,SAE1CsF,GAAc3C,IAAgB4C,IAAMC,MACpCC,GAAcC,YAAiCtB,EAAWuB,OAC1DC,GAAqBN,GAAyC,wBAA3BG,GAAYV,WAAkE,kBAA3BU,GAAYV,WAClGc,GAAYJ,UAAwB,QAAb,EAAXA,GAAaK,mBAAW,WAAb,EAAX,OAAAL,IAElB,OAAOlD,EACL,kBAAC,IAAkB,CACjBnD,YAAayC,IAAYC,2BACzB3B,UAAU,wBAEV,kBAAC,IAAO,CACN4F,QAAM,EACNC,SAAU,YAAgB,IAAbD,EAAM,EAANA,OACX/C,EAAkB+C,EAAOE,UAG1B,gBAAGC,EAAU,EAAVA,WAAU,OACZ,yBACE/F,UAAU,qCACVgG,IAAKD,GAEL,kBAAC,IAAO,CACNH,QAAM,EACNC,SAAU,YAAgB,IAAbD,EAAM,EAANA,OACa,IAAvB9C,GAA6BC,EAAsB6C,EAAOE,UAG5D,gBAAGC,EAAU,EAAVA,WAAU,OACZ,yBACE/F,UAAU,cACVgG,IAAKD,GAEL,kBAAC,IAAQ,CACPxF,GAAG,4BACH0F,MAAO3H,OAAO4H,KAAKC,KAGnBC,SAAU,kBAAMjD,GAAiB,IACjCzC,YAAW,6BAAE,WAAO2F,GAAI,+EAiBrB,OAhBKC,EAAcH,IAA2BE,GACzCpB,EAAgBsB,SAASD,EAAYE,SAAU,IAC/CC,EAAY,IAAInK,OAAO0B,KAAK0I,YAAYC,MAAML,EAAYd,OAC1DoB,EAAkB,CACtBC,EAAGJ,EAAUK,EACbC,EAAGN,EAAUO,EACbC,EAAGR,EAAUS,EACbC,EAAG,KAGCC,EAAe,CACnBhD,MAAM,EACNE,QAAQ,EACR+C,WAAW,EACX5C,UAAWQ,EACXO,MAAOoB,GACR,SAEK9H,IAAKC,kBAAkBuI,4BAA4BF,GAAa,uBAChEtI,IAAKC,kBAAkBwI,mBAAmBH,GAAa,2CAC9D,mDArBU,GAsBXI,mBAAoB,SAACnB,GAAI,cAAWF,IAA2BE,IAAK,IAAEoB,QAAS,YAAajC,MAAO,mBACnGkC,0BAA0B,EAC1BC,oBAAqBC,YAA2B3D,GAChD6B,MAAO,IACP7G,YAAY,8BAEd,kBAAC,IAAQ,CACPsB,GAAG,qBACH0F,MAAO1D,EACP6D,SAAU,kBAAMjD,GAAiB,IACjCzC,YAAa,SAACmD,GACY,iBAAbA,GACT/E,IAAKC,kBAAkBC,8BAA8B,CAAE6E,cAG3D2D,mBAAoB,SAACnB,GAAI,YAAW5D,EAAc4D,KAClDwB,UAAW,IACXC,oBAAqB,SAACC,GAAI,OAAKxF,EAAmBuB,SAASiE,IAC3DjC,MAAO,IACP7G,YAAY,qBACZ0I,oBAAqB9D,GACrBmE,UAAQ,IAEV,kBAAC,IAAQ,CACPzH,GAAG,0BACH0F,MAAOgC,IACP7B,SAAU,kBAAMjD,GAAiB,IACjCzC,YAAa,SAAC+D,GACZ,IAAIQ,EAAgBsB,SAAS9B,EAAW,IAEpCyD,MAAMjD,KACRA,EAAgBC,KAGdD,EAAgBkD,IAAUC,IAC5BnD,EAAgBkD,IAAUC,IACjBnD,EAAgBkD,IAAUE,MACnCpD,EAAgBkD,IAAUE,KAE5BvJ,IAAKC,kBAAkBC,8BAA8B,CAAEyF,UAAWQ,KAEpE0C,oBAAqBjD,GACrBoB,MAAO,GACP7G,YAAY,0BACZ+I,UAAQ,EACRM,iBAAiB,IAEjBtF,GAAqB,GACrB,oCACE,yBAAKhD,UAAU,YACf,kBAAC5B,EAAU,CACTC,aAAc,CACZ+F,KAAMD,EACNG,OAAQD,EACRgD,UAAW9C,MAKnB,yBAAKvE,UAAU,YACf,kBAAC,IAAmB,CAClBnB,QAAS,kBAAMsE,GAAiB,IAChClE,YAAayC,IAAY6G,gCACzBrJ,MAAM,yBACNwG,UAAWA,GACXvG,IAAI,4BACJqJ,QAAS9G,IAAY+G,mCACrBjD,MAAOF,GAAYV,WACnB8D,cAAa,UAAKjD,GAAqB,cAAgB,GAAE,sBAE3D,kBAAC,IAAkB,CACjBkD,cAAe,SAACC,EAAGC,GACjB,IAAMrD,EAAQ,CACZqB,EAAGgC,EAAS/B,EACZC,EAAG8B,EAAS7B,EACZC,EAAG4B,EAAS3B,EACZC,EAAG,KAELrI,IAAKC,kBAAkBC,8BAA8B,CAAEwG,UACvDpE,EAASkC,IAAQwF,cAAc,CAACpH,IAAY+G,uCAE9CjD,MAAOF,KAEPtC,GAAqB,GACrB,oCACE,yBAAKhD,UAAU,YACf,kBAACZ,EAAoB,CAACC,cAAeA,MAGzC,yBAAKW,UAAU,YACf,kBAAC,IAAQ,CACPO,GAAG,6BACH0F,MAAO3H,OAAO4H,KAAK6C,KACnBrI,YAAa,SAACsI,GACZ,IAAMC,EAAcF,IAAqBC,GACzClK,IAAKC,kBAAkBS,qBAAqB,CAC1C,qBAAwByJ,KAG5BtB,oBAAqB7C,GACrBgB,MAAO,GACP7G,YAAY,6BACZiK,cAAe,SAAC9G,GAAM,OACpB,kBAAC,IAAY,CACXlD,MAAM,2BACNC,IAAI,kCACJR,SAAUyD,EACVvD,QAAS,kBAAMsE,GAAiB,SAItC,yBAAKnD,UAAU,YACf,kBAAC,IAAY,CACXd,MAAM,yBACNC,IAAI,gCACJF,YAAayC,IAAYyH,yBAEzBC,SAAUvH,EACVhD,QAAS,WACPC,IAAKC,kBAAkBsK,qBAG3B,kBAAC,IAAQ,CACP9I,GAAG,6BACHtB,YAAayC,IAAY4H,wCACzBxD,MAAO,IACP9F,UAAU,wBACVkJ,cAAe,SAAC9G,GAAM,OACpB,oCACE,kBAAC,IAAY,CACXlD,MAAM,qBACNC,IAAI,WACJR,SAAUyD,IAEZ,kBAAC,IAAI,CAACpC,UAAU,QAAQuJ,MAAK,uBAAkBnH,EAAS,KAAO,aAInE,kBAAC,IAA4B,OAE/B,oCACE,kBAAC,IAAY,CACXpC,UAAU,oBACVf,YAAayC,IAAY8H,wCACzBtK,MAAM,2BACNC,IAAI,uBACJN,QAAS,WACP4K,iBAGJ,kBAAC,IAAkC,OAEb,IAAtBzG,GACA,oCACE,yBAAKhD,UAAU,YACf,kBAACJ,EAAW,CAACC,SAAUA,GAAU6C,4BAA6BA,KAGhEM,EAAoB,GACpB,oCACE,yBAAKhD,UAAU,YACf,yBAAKA,UAAU,yBACb,kBAAC,IAAY,CACXA,UAAU,oBACVrB,SAAUuE,EACVjE,YAAY,2BACZC,MAAM,cACNC,IAAI,2BACJN,QAAS,kBAAMsE,GAAkBD,MAElCA,GACC,yBAAKlD,UAAU,iDACb,yBAAKA,UAAU,eACXgD,EAAoB,GACpB,oCACE,kBAAC5E,EAAU,CACTC,aAAc,CACZ+F,KAAMD,EACNG,OAAQD,EACRgD,UAAW9C,KAGf,yBAAKvE,UAAU,aAGjBgD,EAAoB,GACpB,oCACE,kBAAC5D,EAAoB,CAACC,cAAeA,KACrC,yBAAKW,UAAU,aAGjBgD,EAAoB,GACpB,kBAACpD,EAAW,CAACC,SAAUA,GAAU6C,4BAA6BA,iBAexF", "file": "chunks/chunk.65.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./OfficeHeader.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MainHeader.OfficeEditorTools{padding:0 8px;align-items:center;flex-direction:row}.MainHeader.OfficeEditorTools .HeaderItems{width:auto}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown{margin-left:8px}.MainHeader.OfficeEditorTools .HeaderItems .Dropdown .picked-option{text-align:left}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper{display:flex;align-items:center;position:relative}.MainHeader.OfficeEditorTools .HeaderItems .action-button-wrapper .more-tools{position:absolute;top:46px;right:6px;width:auto;padding:0}.MainHeader.OfficeEditorTools .Button .Icon svg{vertical-align:middle}.MainHeader.OfficeEditorTools .icon-text-color{display:flex;align-items:center;justify-content:center}.MainHeader.OfficeEditorTools .list-style-button{margin-right:0!important}.MainHeader.OfficeEditorTools .list-style-dropdown{background:transparent;border:none;width:12px!important;height:32px!important;margin-left:0!important;z-index:0}.MainHeader.OfficeEditorTools .list-style-dropdown.Dropdown__wrapper:hover{border-radius:4px;background:var(--tools-button-hover)}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown{padding:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown svg rect{stroke:none}.MainHeader.OfficeEditorTools .list-style-dropdown .picked-option-text{display:none}.MainHeader.OfficeEditorTools .list-style-dropdown .arrow{padding-left:0}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items{overflow:hidden;width:auto!important}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item{height:74px!important;padding:15px 0;margin:3px;cursor:pointer}.MainHeader.OfficeEditorTools .list-style-dropdown .Dropdown__items .Dropdown__item .officeEditor-list-style-icon{width:60px!important;height:74px!important}.MainHeader.OfficeEditorTools .insert-table-dropdown{margin:0 6px}.MainHeader.OfficeEditorTools .insert-table-dropdown.open{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button{display:flex;align-items:center;cursor:pointer}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button:hover{background-color:var(--tools-button-hover);border-radius:4px}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Button{background-color:transparent!important}.MainHeader.OfficeEditorTools .insert-table-dropdown .display-button .Icon.arrow{width:12px;height:12px;margin:0 2px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useState } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport Measure from 'react-measure';\nimport PropTypes from 'prop-types';\nimport core from 'core';\nimport Dropdown from 'components/Dropdown';\nimport ActionButton from 'components/ActionButton';\nimport ToggleElementButton from 'components/ToggleElementButton';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport OfficeEditorImageFilePickerHandler from 'components/OfficeEditorImageFilePickerHandler';\nimport ColorPickerOverlay from 'components/ColorPickerOverlay';\nimport Icon from 'components/Icon';\nimport OfficeEditorCreateTablePopup from 'components/OfficeEditorCreateTablePopup';\nimport DataElement from 'constants/dataElement';\nimport Theme from 'constants/theme';\nimport {\n  LINE_SPACING_OPTIONS,\n  JUSTIFICATION_OPTIONS,\n  LIST_OPTIONS,\n  DEFAULT_POINT_SIZE,\n  OFFICE_BULLET_OPTIONS,\n  OFFICE_NUMBER_OPTIONS,\n  FONT_SIZE,\n  AVAILABLE_STYLE_PRESET_MAP,\n  AVAILABLE_POINT_SIZES,\n} from 'constants/officeEditor';\nimport openOfficeEditorFilePicker from 'helpers/openOfficeEditorFilePicker';\nimport { calculateLineSpacing, convertCursorToStylePreset, convertCoreColorToWebViewerColor } from 'helpers/officeEditor';\n\nimport './Header.scss';\nimport './OfficeHeader.scss';\nimport '../HeaderItems/HeaderItems.scss';\n\nconst listOptionsWidth = 121;\nconst justificationOptionsWidth = 209;\nconst moreButtonWidth = 77;\nconst officeEditorToggleableStyles = window.Core.Document.OfficeEditor.ToggleableStyles;\n\nconst TextStyles = ({ activeStates }) => {\n  return Object.values(officeEditorToggleableStyles).map((style) => (\n    <ActionButton\n      key={style}\n      isActive={activeStates[style]}\n      ariaPressed={activeStates[style]}\n      onClick={() => {\n        core.getOfficeEditor().updateSelectionAndCursorStyle({ [style]: true });\n      }}\n      dataElement={`office-editor-${style}`}\n      title={`officeEditor.${style}`}\n      img={`icon-text-${style}`}\n    />\n  ));\n};\n\nconst JustificationOptions = ({ justification }) => {\n  return (\n    <>\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Left}\n        dataElement='office-editor-left-align'\n        title='officeEditor.leftAlign'\n        img='icon-menu-left-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'left'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Center}\n        dataElement='office-editor-center-align'\n        title='officeEditor.centerAlign'\n        img='icon-menu-center-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'center'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Right}\n        dataElement='office-editor-right-align'\n        title='officeEditor.rightAlign'\n        img='icon-menu-right-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'right'\n          });\n        }}\n      />\n      <ActionButton\n        isActive={justification === JUSTIFICATION_OPTIONS.Both}\n        dataElement='office-editor-justify'\n        title='officeEditor.justify'\n        img='icon-menu-both-align'\n        onClick={() => {\n          core.getOfficeEditor().updateParagraphStyle({\n            justification: 'both'\n          });\n        }}\n      />\n    </>\n  );\n};\n\nconst ListOptions = ({ listType }) => {\n  const bulletListObjects = OFFICE_BULLET_OPTIONS.map((options) => ({\n    className: 'officeEditor-list-style-icon',\n    key: options.enum,\n    src: options.img\n  }));\n\n  const numberListOptions = OFFICE_NUMBER_OPTIONS.map((options) => ({\n    className: 'officeEditor-list-style-icon',\n    key: options.enum,\n    src: options.img\n  }));\n\n  return (\n    <>\n      <ActionButton\n        isActive={listType === LIST_OPTIONS.Unordered}\n        dataElement='unorderedListDropButton'\n        title='officeEditor.bulletList'\n        img='icon-office-editor-bullet-list'\n        className='list-style-button'\n        onClick={() => {\n          core.getOfficeEditor().toggleListSelection(LIST_OPTIONS.Unordered);\n        }}\n      />\n      <Dropdown\n        id='office-editor-bullet-list-dropdown'\n        dataElement='office-editor-bullet-list-dropdown'\n        images={bulletListObjects}\n        columns={3}\n        onClickItem={(val) => {\n          core.getOfficeEditor().setListPreset(val);\n        }}\n        className='list-style-dropdown'\n      />\n      <ActionButton\n        isActive={listType === LIST_OPTIONS.Ordered}\n        dataElement='office-editor-number-list'\n        title='officeEditor.numberList'\n        img='icon-office-editor-number-list'\n        className='list-style-button'\n        onClick={() => {\n          core.getOfficeEditor().toggleListSelection(LIST_OPTIONS.Ordered);\n        }}\n      />\n      <Dropdown\n        id='office-editor-number-list-dropdown'\n        dataElement='office-editor-number-list-dropdown'\n        images={numberListOptions}\n        columns={3}\n        onClickItem={(val) => {\n          core.getOfficeEditor().setListPreset(val);\n        }}\n        className='list-style-dropdown'\n      />\n      <ActionButton\n        dataElement='decreaseIndentButton'\n        title='officeEditor.decreaseIndent'\n        img='ic-indent-decrease'\n        onClick={async () => {\n          await core.getOfficeEditor().decreaseIndent();\n        }}\n      />\n      <ActionButton\n        dataElement='increaseIndentButton'\n        title='officeEditor.increaseIndent'\n        img='ic-indent-increase'\n        onClick={async () => {\n          await core.getOfficeEditor().increaseIndent();\n        }}\n      />\n    </>\n  );\n};\n\nListOptions.propTypes = {\n  listType: PropTypes.string,\n};\n\nconst OfficeEditorToolsHeader = () => {\n  const dispatch = useDispatch();\n  const [\n    isOpen,\n    cursorProperties,\n    isCursorInTable,\n    selectionProperties,\n    availableFontFaces,\n    activeTheme,\n    cssFontValues,\n    enableNonPrintingCharacters\n  ] = useSelector(\n    (state) => [\n      selectors.isElementOpen(state, DataElement.OFFICE_EDITOR_TOOLS_HEADER),\n      selectors.getOfficeEditorCursorProperties(state),\n      selectors.isCursorInTable(state),\n      selectors.getOfficeEditorSelectionProperties(state),\n      selectors.getAvailableFontFaces(state),\n      selectors.getActiveTheme(state),\n      selectors.getCSSFontValues(state),\n      selectors.isNonPrintingCharactersEnabled(state),\n    ],\n    shallowEqual\n  );\n\n  const [containerWidth, setContainerWidth] = useState(0);\n  const [initialHeaderWidth, setInitialHeaderWidth] = useState(0);\n  const [visibleGroupCount, setVisibleGroupCount] = useState(6);\n  const [showMoreTools, setShowMoreTools] = useState(false);\n\n  useEffect(() => {\n    const onCursorPropertiesUpdated = async (cursorProperties) => {\n      dispatch(actions.setOfficeEditorCursorProperties(cursorProperties));\n    };\n    const onSelectionPropertiesUpdated = (selectionProperties) => {\n      dispatch(actions.setOfficeEditorSelectionProperties(selectionProperties));\n    };\n\n    core.getDocument().addEventListener('cursorPropertiesUpdated', onCursorPropertiesUpdated);\n    core.getDocument()?.addEventListener('selectionPropertiesUpdated', onSelectionPropertiesUpdated);\n\n    return () => {\n      core.getDocument().removeEventListener('selectionPropertiesUpdated', onSelectionPropertiesUpdated);\n      core.getDocument().removeEventListener('cursorPropertiesUpdated', onCursorPropertiesUpdated);\n    };\n  }, []);\n\n  useEffect(() => {\n    if (cursorProperties.fontFace && !availableFontFaces.includes(cursorProperties.fontFace)) {\n      dispatch(actions.addOfficeEditorAvailableFontFace(cursorProperties.fontFace));\n    }\n  }, [availableFontFaces, cursorProperties]);\n\n  useEffect(() => {\n    if (containerWidth === 0 || initialHeaderWidth === 0) {\n      return;\n    }\n\n    const actualContainerWidth = containerWidth - 16;\n    if (\n      actualContainerWidth >= initialHeaderWidth\n    ) {\n      setVisibleGroupCount(6);\n    } else if (\n      actualContainerWidth < initialHeaderWidth &&\n      actualContainerWidth >= (initialHeaderWidth - listOptionsWidth + moreButtonWidth)\n    ) {\n      setVisibleGroupCount(5);\n    } else if (\n      actualContainerWidth < (initialHeaderWidth - listOptionsWidth + moreButtonWidth) &&\n      actualContainerWidth >= (initialHeaderWidth - listOptionsWidth - justificationOptionsWidth + moreButtonWidth) &&\n      actualContainerWidth >= 858 // HeaderItems's width when on small devices (screen width < 900px)\n    ) {\n      setVisibleGroupCount(4);\n    } else {\n      setVisibleGroupCount(3);\n    }\n  }, [containerWidth, initialHeaderWidth]);\n\n  const isTextSelected = core.getOfficeEditor().isTextSelected();\n  const properties = isTextSelected ? selectionProperties : cursorProperties;\n  const isBold = properties.bold;\n  const isItalic = properties.italic;\n  const isUnderline = properties.underlineStyle === 'single';\n  const fontFace = properties.fontFace || '';\n  const pointSize = properties.pointSize;\n  const pointSizeSelectionKey = pointSize === undefined ? '' : pointSize.toString();\n  const justification = properties.paragraphProperties.justification;\n  const lineHeight = calculateLineSpacing(\n    properties.paragraphProperties.lineHeightMultiplier,\n    properties.paragraphProperties.lineHeight,\n    cursorProperties.paragraphProperties.fontPointSize || DEFAULT_POINT_SIZE,\n  );\n  const listType = properties.paragraphProperties.listType;\n\n  const isLightMode = activeTheme === Theme.LIGHT;\n  const wvFontColor = convertCoreColorToWebViewerColor(properties.color);\n  const useColorIconBorder = isLightMode ? wvFontColor.toString() === 'rgba(255,255,255,1)' : wvFontColor.toString() === 'rgba(0,0,0,1)';\n  const ariaLabel = wvFontColor?.toHexString?.();\n\n  return isOpen ? (\n    <DataElementWrapper\n      dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER}\n      className='HeaderToolsContainer'\n    >\n      <Measure\n        bounds\n        onResize={({ bounds }) => {\n          setContainerWidth(bounds.width);\n        }}\n      >\n        {({ measureRef }) => (\n          <div\n            className='MainHeader Tools OfficeEditorTools'\n            ref={measureRef}\n          >\n            <Measure\n              bounds\n              onResize={({ bounds }) => {\n                (initialHeaderWidth === 0) && setInitialHeaderWidth(bounds.width);\n              }}\n            >\n              {({ measureRef }) => (\n                <div\n                  className=\"HeaderItems\"\n                  ref={measureRef}\n                >\n                  <Dropdown\n                    id='office-editor-text-format'\n                    items={Object.keys(AVAILABLE_STYLE_PRESET_MAP)}\n                    // TODO: This shouldn't be closing more tools popup\n                    // It shouldn't know about the existence of it.\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={async (item) => {\n                      const stylePreset = AVAILABLE_STYLE_PRESET_MAP[item];\n                      const fontPointSize = parseInt(stylePreset.fontSize, 10);\n                      const fontColor = new window.Core.Annotations.Color(stylePreset.color);\n                      const parsedFontColor = {\n                        r: fontColor.R,\n                        g: fontColor.G,\n                        b: fontColor.B,\n                        a: 255,\n                      };\n\n                      const newTextStyle = {\n                        bold: false,\n                        italic: false,\n                        underline: false,\n                        pointSize: fontPointSize,\n                        color: parsedFontColor\n                      };\n\n                      await core.getOfficeEditor().updateParagraphStylePresets(newTextStyle);\n                      await core.getOfficeEditor().setMainCursorStyle(newTextStyle);\n                    }}\n                    getCustomItemStyle={(item) => ({ ...AVAILABLE_STYLE_PRESET_MAP[item], padding: '20px 10px', color: 'var(--gray-8)' })}\n                    applyCustomStyleToButton={false}\n                    currentSelectionKey={convertCursorToStylePreset(properties)}\n                    width={160}\n                    dataElement=\"office-editor-text-format\"\n                  />\n                  <Dropdown\n                    id='office-editor-font'\n                    items={availableFontFaces}\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={(fontFace) => {\n                      if (typeof fontFace === 'string') {\n                        core.getOfficeEditor().updateSelectionAndCursorStyle({ fontFace });\n                      }\n                    }}\n                    getCustomItemStyle={(item) => ({ ...cssFontValues[item] })}\n                    maxHeight={500}\n                    customDataValidator={(font) => availableFontFaces.includes(font)}\n                    width={160}\n                    dataElement=\"office-editor-font\"\n                    currentSelectionKey={fontFace}\n                    hasInput\n                  />\n                  <Dropdown\n                    id='office-editor-font-size'\n                    items={AVAILABLE_POINT_SIZES}\n                    onOpened={() => setShowMoreTools(false)}\n                    onClickItem={(pointSize) => {\n                      let fontPointSize = parseInt(pointSize, 10);\n\n                      if (isNaN(fontPointSize)) {\n                        fontPointSize = DEFAULT_POINT_SIZE;\n                      }\n\n                      if (fontPointSize > FONT_SIZE.MAX) {\n                        fontPointSize = FONT_SIZE.MAX;\n                      } else if (fontPointSize < FONT_SIZE.MIN) {\n                        fontPointSize = FONT_SIZE.MIN;\n                      }\n                      core.getOfficeEditor().updateSelectionAndCursorStyle({ pointSize: fontPointSize });\n                    }}\n                    currentSelectionKey={pointSizeSelectionKey}\n                    width={80}\n                    dataElement=\"office-editor-font-size\"\n                    hasInput\n                    isSearchEnabled={false}\n                  />\n                  {(visibleGroupCount >= 4) && (\n                    <>\n                      <div className=\"divider\" />\n                      <TextStyles\n                        activeStates={{\n                          bold: isBold,\n                          italic: isItalic,\n                          underline: isUnderline\n                        }}\n                      />\n                    </>\n                  )}\n                  <div className=\"divider\" />\n                  <ToggleElementButton\n                    onClick={() => setShowMoreTools(false)}\n                    dataElement={DataElement.OFFICE_EDITOR_TEXT_COLOR_BUTTON}\n                    title='officeEditor.textColor'\n                    ariaLabel={ariaLabel}\n                    img='icon-office-editor-circle'\n                    element={DataElement.OFFICE_EDITOR_COLOR_PICKER_OVERLAY}\n                    color={wvFontColor.toString()}\n                    iconClassName={`${useColorIconBorder ? 'icon-border' : ''} icon-text-color`}\n                  />\n                  <ColorPickerOverlay\n                    onStyleChange={(_, newColor) => {\n                      const color = {\n                        r: newColor.R,\n                        g: newColor.G,\n                        b: newColor.B,\n                        a: 255,\n                      };\n                      core.getOfficeEditor().updateSelectionAndCursorStyle({ color });\n                      dispatch(actions.closeElements([DataElement.OFFICE_EDITOR_COLOR_PICKER_OVERLAY]));\n                    }}\n                    color={wvFontColor}\n                  />\n                  {(visibleGroupCount >= 5) && (\n                    <>\n                      <div className=\"divider\" />\n                      <JustificationOptions justification={justification} />\n                    </>\n                  )}\n                  <div className=\"divider\" />\n                  <Dropdown\n                    id='office-editor-line-spacing'\n                    items={Object.keys(LINE_SPACING_OPTIONS)}\n                    onClickItem={(lineSpacingOption) => {\n                      const lineSpacing = LINE_SPACING_OPTIONS[lineSpacingOption];\n                      core.getOfficeEditor().updateParagraphStyle({\n                        'lineHeightMultiplier': lineSpacing\n                      });\n                    }}\n                    currentSelectionKey={lineHeight}\n                    width={80}\n                    dataElement=\"office-editor-line-spacing\"\n                    displayButton={(isOpen) => (\n                      <ActionButton\n                        title='officeEditor.lineSpacing'\n                        img='icon-office-editor-line-spacing'\n                        isActive={isOpen}\n                        onClick={() => setShowMoreTools(false)}\n                      />\n                    )}\n                  />\n                  <div className=\"divider\" />\n                  <ActionButton\n                    title='officeEditor.pageBreak'\n                    img='icon-office-editor-page-break'\n                    dataElement={DataElement.OFFICE_EDITOR_PAGE_BREAK}\n\n                    disabled={isCursorInTable}\n                    onClick={() => {\n                      core.getOfficeEditor().insertPageBreak();\n                    }}\n                  />\n                  <Dropdown\n                    id='office-editor-insert-table'\n                    dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER_INSERT_TABLE}\n                    width={136}\n                    className=\"insert-table-dropdown\"\n                    displayButton={(isOpen) => (\n                      <>\n                        <ActionButton\n                          title='officeEditor.table'\n                          img='ic-table'\n                          isActive={isOpen}\n                        />\n                        <Icon className=\"arrow\" glyph={`icon-chevron-${isOpen ? 'up' : 'down'}`} />\n                      </>\n                    )}\n                  >\n                    <OfficeEditorCreateTablePopup />\n                  </Dropdown>\n                  <>\n                    <ActionButton\n                      className=\"tool-group-button\"\n                      dataElement={DataElement.OFFICE_EDITOR_TOOLS_HEADER_INSERT_IMAGE}\n                      title='officeEditor.insertImage'\n                      img='icon-tool-image-line'\n                      onClick={() => {\n                        openOfficeEditorFilePicker();\n                      }}\n                    />\n                    <OfficeEditorImageFilePickerHandler />\n                  </>\n                  {(visibleGroupCount === 6) && (\n                    <>\n                      <div className=\"divider\" />\n                      <ListOptions listType={listType} enableNonPrintingCharacters={enableNonPrintingCharacters} />\n                    </>\n                  )}\n                  {(visibleGroupCount < 6) && (\n                    <>\n                      <div className=\"divider\" />\n                      <div className=\"action-button-wrapper\">\n                        <ActionButton\n                          className=\"tool-group-button\"\n                          isActive={showMoreTools}\n                          dataElement='office-editor-more-tools'\n                          title='action.more'\n                          img='icon-tools-more-vertical'\n                          onClick={() => setShowMoreTools(!showMoreTools)}\n                        />\n                        {showMoreTools && (\n                          <div className=\"more-tools MainHeader Tools OfficeEditorTools\">\n                            <div className=\"HeaderItems\">\n                              {(visibleGroupCount < 4) && (\n                                <>\n                                  <TextStyles\n                                    activeStates={{\n                                      bold: isBold,\n                                      italic: isItalic,\n                                      underline: isUnderline\n                                    }}\n                                  />\n                                  <div className=\"divider\" />\n                                </>\n                              )}\n                              {(visibleGroupCount < 5) && (\n                                <>\n                                  <JustificationOptions justification={justification} />\n                                  <div className=\"divider\" />\n                                </>\n                              )}\n                              {(visibleGroupCount < 6) && (\n                                <ListOptions listType={listType} enableNonPrintingCharacters={enableNonPrintingCharacters}/>\n                              )}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </>\n                  )}\n                </div>\n              )}\n            </Measure>\n          </div>\n        )}\n      </Measure>\n    </DataElementWrapper>\n  ) : null;\n};\n\nexport default OfficeEditorToolsHeader;\n"], "sourceRoot": ""}