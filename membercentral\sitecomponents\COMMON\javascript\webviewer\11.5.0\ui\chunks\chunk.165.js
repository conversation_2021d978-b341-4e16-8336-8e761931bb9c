(window.webpackJsonp = window.webpackJsonp || []).push([
	[165],
	{
		1423: function (a, t, _) {
			a.exports = (function (a) {
				"use strict";
				var t = (function (a) {
						return a && "object" == typeof a && "default" in a
							? a
							: { default: a };
					})(a),
					_ =
						"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split(
							"_",
						),
					e =
						"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split(
							"_",
						),
					n = /D[oD]?(\[[^[\]]*\]|\s)+MMMM?/,
					s = function (a, t) {
						return n.test(t) ? _[a.month()] : e[a.month()];
					};
				(s.s = e), (s.f = _);
				var o = {
					name: "hr",
					weekdays:
						"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split(
							"_",
						),
					weekdaysShort: "ned._pon._uto._sri._čet._pet._sub.".split("_"),
					weekdaysMin: "ne_po_ut_sr_če_pe_su".split("_"),
					months: s,
					monthsShort:
						"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split(
							"_",
						),
					weekStart: 1,
					formats: {
						LT: "H:mm",
						LTS: "H:mm:ss",
						L: "DD.MM.YYYY",
						LL: "D. MMMM YYYY",
						LLL: "D. MMMM YYYY H:mm",
						LLLL: "dddd, D. MMMM YYYY H:mm",
					},
					relativeTime: {
						future: "za %s",
						past: "prije %s",
						s: "sekunda",
						m: "minuta",
						mm: "%d minuta",
						h: "sat",
						hh: "%d sati",
						d: "dan",
						dd: "%d dana",
						M: "mjesec",
						MM: "%d mjeseci",
						y: "godina",
						yy: "%d godine",
					},
					ordinal: function (a) {
						return a + ".";
					},
				};
				return t.default.locale(o, null, !0), o;
			})(_(105));
		},
	},
]);
//# sourceMappingURL=chunk.165.js.map
