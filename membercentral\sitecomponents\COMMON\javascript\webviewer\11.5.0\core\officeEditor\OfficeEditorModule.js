(function () {
	/*
 @license DOMPurify 3.2.5 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE */
	var $jscomp = $jscomp || {};
	$jscomp.scope = {};
	$jscomp.arrayIteratorImpl = function (g) {
		var l = 0;
		return function () {
			return l < g.length ? { done: !1, value: g[l++] } : { done: !0 };
		};
	};
	$jscomp.arrayIterator = function (g) {
		return { next: $jscomp.arrayIteratorImpl(g) };
	};
	$jscomp.ASSUME_ES5 = !1;
	$jscomp.ASSUME_NO_NATIVE_MAP = !1;
	$jscomp.ASSUME_NO_NATIVE_SET = !1;
	$jscomp.SIMPLE_FROUND_POLYFILL = !1;
	$jscomp.ISOLATE_POLYFILLS = !1;
	$jscomp.FORCE_POLYFILL_PROMISE = !1;
	$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION = !1;
	$jscomp.defineProperty =
		$jscomp.ASSUME_ES5 || "function" == typeof Object.defineProperties
			? Object.defineProperty
			: function (g, l, k) {
					if (g == Array.prototype || g == Object.prototype) return g;
					g[l] = k.value;
					return g;
				};
	$jscomp.getGlobal = function (g) {
		g = [
			"object" == typeof globalThis && globalThis,
			g,
			"object" == typeof window && window,
			"object" == typeof self && self,
			"object" == typeof global && global,
		];
		for (var l = 0; l < g.length; ++l) {
			var k = g[l];
			if (k && k.Math == Math) return k;
		}
		throw Error("Cannot find global object");
	};
	$jscomp.global = $jscomp.getGlobal(this);
	$jscomp.IS_SYMBOL_NATIVE =
		"function" === typeof Symbol && "symbol" === typeof Symbol("x");
	$jscomp.TRUST_ES6_POLYFILLS =
		!$jscomp.ISOLATE_POLYFILLS || $jscomp.IS_SYMBOL_NATIVE;
	$jscomp.polyfills = {};
	$jscomp.propertyToPolyfillSymbol = {};
	$jscomp.POLYFILL_PREFIX = "$jscp$";
	var $jscomp$lookupPolyfilledValue = function (g, l, k) {
		if (!k || null != g) {
			k = $jscomp.propertyToPolyfillSymbol[l];
			if (null == k) return g[l];
			k = g[k];
			return void 0 !== k ? k : g[l];
		}
	};
	$jscomp.polyfill = function (g, l, k, f) {
		l &&
			($jscomp.ISOLATE_POLYFILLS
				? $jscomp.polyfillIsolated(g, l, k, f)
				: $jscomp.polyfillUnisolated(g, l, k, f));
	};
	$jscomp.polyfillUnisolated = function (g, l, k, f) {
		k = $jscomp.global;
		g = g.split(".");
		for (f = 0; f < g.length - 1; f++) {
			var b = g[f];
			if (!(b in k)) return;
			k = k[b];
		}
		g = g[g.length - 1];
		f = k[g];
		l = l(f);
		l != f &&
			null != l &&
			$jscomp.defineProperty(k, g, {
				configurable: !0,
				writable: !0,
				value: l,
			});
	};
	$jscomp.polyfillIsolated = function (g, l, k, f) {
		var b = g.split(".");
		g = 1 === b.length;
		f = b[0];
		f = !g && f in $jscomp.polyfills ? $jscomp.polyfills : $jscomp.global;
		for (var n = 0; n < b.length - 1; n++) {
			var d = b[n];
			if (!(d in f)) return;
			f = f[d];
		}
		b = b[b.length - 1];
		k = $jscomp.IS_SYMBOL_NATIVE && "es6" === k ? f[b] : null;
		l = l(k);
		null != l &&
			(g
				? $jscomp.defineProperty($jscomp.polyfills, b, {
						configurable: !0,
						writable: !0,
						value: l,
					})
				: l !== k &&
					(void 0 === $jscomp.propertyToPolyfillSymbol[b] &&
						((k = (1e9 * Math.random()) >>> 0),
						($jscomp.propertyToPolyfillSymbol[b] = $jscomp.IS_SYMBOL_NATIVE
							? $jscomp.global.Symbol(b)
							: $jscomp.POLYFILL_PREFIX + k + "$" + b)),
					$jscomp.defineProperty(f, $jscomp.propertyToPolyfillSymbol[b], {
						configurable: !0,
						writable: !0,
						value: l,
					})));
	};
	$jscomp.initSymbol = function () {};
	$jscomp.polyfill(
		"Symbol",
		function (g) {
			if (g) return g;
			var l = function (n, d) {
				this.$jscomp$symbol$id_ = n;
				$jscomp.defineProperty(this, "description", {
					configurable: !0,
					writable: !0,
					value: d,
				});
			};
			l.prototype.toString = function () {
				return this.$jscomp$symbol$id_;
			};
			var k = "jscomp_symbol_" + ((1e9 * Math.random()) >>> 0) + "_",
				f = 0,
				b = function (n) {
					if (this instanceof b)
						throw new TypeError("Symbol is not a constructor");
					return new l(k + (n || "") + "_" + f++, n);
				};
			return b;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Symbol.iterator",
		function (g) {
			if (g) return g;
			g = Symbol("Symbol.iterator");
			for (
				var l =
						"Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(
							" ",
						),
					k = 0;
				k < l.length;
				k++
			) {
				var f = $jscomp.global[l[k]];
				"function" === typeof f &&
					"function" != typeof f.prototype[g] &&
					$jscomp.defineProperty(f.prototype, g, {
						configurable: !0,
						writable: !0,
						value: function () {
							return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this));
						},
					});
			}
			return g;
		},
		"es6",
		"es3",
	);
	$jscomp.iteratorPrototype = function (g) {
		g = { next: g };
		g[Symbol.iterator] = function () {
			return this;
		};
		return g;
	};
	$jscomp.makeIterator = function (g) {
		var l =
			"undefined" != typeof Symbol && Symbol.iterator && g[Symbol.iterator];
		if (l) return l.call(g);
		if ("number" == typeof g.length) return $jscomp.arrayIterator(g);
		throw Error(String(g) + " is not an iterable or ArrayLike");
	};
	$jscomp.arrayFromIterator = function (g) {
		for (var l, k = []; !(l = g.next()).done; ) k.push(l.value);
		return k;
	};
	$jscomp.arrayFromIterable = function (g) {
		return g instanceof Array
			? g
			: $jscomp.arrayFromIterator($jscomp.makeIterator(g));
	};
	$jscomp.checkEs6ConformanceViaProxy = function () {
		try {
			var g = {},
				l = Object.create(
					new $jscomp.global.Proxy(g, {
						get: function (k, f, b) {
							return k == g && "q" == f && b == l;
						},
					}),
				);
			return !0 === l.q;
		} catch (k) {
			return !1;
		}
	};
	$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS = !1;
	$jscomp.ES6_CONFORMANCE =
		$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS &&
		$jscomp.checkEs6ConformanceViaProxy();
	$jscomp.owns = function (g, l) {
		return Object.prototype.hasOwnProperty.call(g, l);
	};
	$jscomp.polyfill(
		"WeakMap",
		function (g) {
			function l() {
				if (!g || !Object.seal) return !1;
				try {
					var e = Object.seal({}),
						h = Object.seal({}),
						q = new g([
							[e, 2],
							[h, 3],
						]);
					if (2 != q.get(e) || 3 != q.get(h)) return !1;
					q.delete(e);
					q.set(h, 4);
					return !q.has(e) && 4 == q.get(h);
				} catch (v) {
					return !1;
				}
			}
			function k() {}
			function f(e) {
				var h = typeof e;
				return ("object" === h && null !== e) || "function" === h;
			}
			function b(e) {
				if (!$jscomp.owns(e, d)) {
					var h = new k();
					$jscomp.defineProperty(e, d, { value: h });
				}
			}
			function n(e) {
				if (!$jscomp.ISOLATE_POLYFILLS) {
					var h = Object[e];
					h &&
						(Object[e] = function (q) {
							if (q instanceof k) return q;
							Object.isExtensible(q) && b(q);
							return h(q);
						});
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (g && $jscomp.ES6_CONFORMANCE) return g;
			} else if (l()) return g;
			var d = "$jscomp_hidden_" + Math.random();
			n("freeze");
			n("preventExtensions");
			n("seal");
			var m = 0,
				a = function (e) {
					this.id_ = (m += Math.random() + 1).toString();
					if (e) {
						e = $jscomp.makeIterator(e);
						for (var h; !(h = e.next()).done; )
							(h = h.value), this.set(h[0], h[1]);
					}
				};
			a.prototype.set = function (e, h) {
				if (!f(e)) throw Error("Invalid WeakMap key");
				b(e);
				if (!$jscomp.owns(e, d)) throw Error("WeakMap key fail: " + e);
				e[d][this.id_] = h;
				return this;
			};
			a.prototype.get = function (e) {
				return f(e) && $jscomp.owns(e, d) ? e[d][this.id_] : void 0;
			};
			a.prototype.has = function (e) {
				return f(e) && $jscomp.owns(e, d) && $jscomp.owns(e[d], this.id_);
			};
			a.prototype.delete = function (e) {
				return f(e) && $jscomp.owns(e, d) && $jscomp.owns(e[d], this.id_)
					? delete e[d][this.id_]
					: !1;
			};
			return a;
		},
		"es6",
		"es3",
	);
	$jscomp.MapEntry = function () {};
	$jscomp.polyfill(
		"Map",
		function (g) {
			function l() {
				if (
					$jscomp.ASSUME_NO_NATIVE_MAP ||
					!g ||
					"function" != typeof g ||
					!g.prototype.entries ||
					"function" != typeof Object.seal
				)
					return !1;
				try {
					var a = Object.seal({ x: 4 }),
						e = new g($jscomp.makeIterator([[a, "s"]]));
					if (
						"s" != e.get(a) ||
						1 != e.size ||
						e.get({ x: 4 }) ||
						e.set({ x: 4 }, "t") != e ||
						2 != e.size
					)
						return !1;
					var h = e.entries(),
						q = h.next();
					if (q.done || q.value[0] != a || "s" != q.value[1]) return !1;
					q = h.next();
					return q.done ||
						4 != q.value[0].x ||
						"t" != q.value[1] ||
						!h.next().done
						? !1
						: !0;
				} catch (v) {
					return !1;
				}
			}
			if ($jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS) {
				if (g && $jscomp.ES6_CONFORMANCE) return g;
			} else if (l()) return g;
			var k = new WeakMap(),
				f = function (a) {
					this.data_ = {};
					this.head_ = d();
					this.size = 0;
					if (a) {
						a = $jscomp.makeIterator(a);
						for (var e; !(e = a.next()).done; )
							(e = e.value), this.set(e[0], e[1]);
					}
				};
			f.prototype.set = function (a, e) {
				a = 0 === a ? 0 : a;
				var h = b(this, a);
				h.list || (h.list = this.data_[h.id] = []);
				h.entry
					? (h.entry.value = e)
					: ((h.entry = {
							next: this.head_,
							previous: this.head_.previous,
							head: this.head_,
							key: a,
							value: e,
						}),
						h.list.push(h.entry),
						(this.head_.previous.next = h.entry),
						(this.head_.previous = h.entry),
						this.size++);
				return this;
			};
			f.prototype.delete = function (a) {
				a = b(this, a);
				return a.entry && a.list
					? (a.list.splice(a.index, 1),
						a.list.length || delete this.data_[a.id],
						(a.entry.previous.next = a.entry.next),
						(a.entry.next.previous = a.entry.previous),
						(a.entry.head = null),
						this.size--,
						!0)
					: !1;
			};
			f.prototype.clear = function () {
				this.data_ = {};
				this.head_ = this.head_.previous = d();
				this.size = 0;
			};
			f.prototype.has = function (a) {
				return !!b(this, a).entry;
			};
			f.prototype.get = function (a) {
				return (a = b(this, a).entry) && a.value;
			};
			f.prototype.entries = function () {
				return n(this, function (a) {
					return [a.key, a.value];
				});
			};
			f.prototype.keys = function () {
				return n(this, function (a) {
					return a.key;
				});
			};
			f.prototype.values = function () {
				return n(this, function (a) {
					return a.value;
				});
			};
			f.prototype.forEach = function (a, e) {
				for (var h = this.entries(), q; !(q = h.next()).done; )
					(q = q.value), a.call(e, q[1], q[0], this);
			};
			f.prototype[Symbol.iterator] = f.prototype.entries;
			var b = function (a, e) {
					var h = e && typeof e;
					"object" == h || "function" == h
						? k.has(e)
							? (h = k.get(e))
							: ((h = "" + ++m), k.set(e, h))
						: (h = "p_" + e);
					var q = a.data_[h];
					if (q && $jscomp.owns(a.data_, h))
						for (a = 0; a < q.length; a++) {
							var v = q[a];
							if ((e !== e && v.key !== v.key) || e === v.key)
								return { id: h, list: q, index: a, entry: v };
						}
					return { id: h, list: q, index: -1, entry: void 0 };
				},
				n = function (a, e) {
					var h = a.head_;
					return $jscomp.iteratorPrototype(function () {
						if (h) {
							for (; h.head != a.head_; ) h = h.previous;
							for (; h.next != h.head; )
								return (h = h.next), { done: !1, value: e(h) };
							h = null;
						}
						return { done: !0, value: void 0 };
					});
				},
				d = function () {
					var a = {};
					return (a.previous = a.next = a.head = a);
				},
				m = 0;
			return f;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Promise",
		function (g) {
			function l() {
				this.batch_ = null;
			}
			function k(d) {
				return d instanceof b
					? d
					: new b(function (m, a) {
							m(d);
						});
			}
			if (
				g &&
				(!(
					$jscomp.FORCE_POLYFILL_PROMISE ||
					($jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION &&
						"undefined" === typeof $jscomp.global.PromiseRejectionEvent)
				) ||
					!$jscomp.global.Promise ||
					-1 === $jscomp.global.Promise.toString().indexOf("[native code]"))
			)
				return g;
			l.prototype.asyncExecute = function (d) {
				if (null == this.batch_) {
					this.batch_ = [];
					var m = this;
					this.asyncExecuteFunction(function () {
						m.executeBatch_();
					});
				}
				this.batch_.push(d);
			};
			var f = $jscomp.global.setTimeout;
			l.prototype.asyncExecuteFunction = function (d) {
				f(d, 0);
			};
			l.prototype.executeBatch_ = function () {
				for (; this.batch_ && this.batch_.length; ) {
					var d = this.batch_;
					this.batch_ = [];
					for (var m = 0; m < d.length; ++m) {
						var a = d[m];
						d[m] = null;
						try {
							a();
						} catch (e) {
							this.asyncThrow_(e);
						}
					}
				}
				this.batch_ = null;
			};
			l.prototype.asyncThrow_ = function (d) {
				this.asyncExecuteFunction(function () {
					throw d;
				});
			};
			var b = function (d) {
				this.state_ = 0;
				this.result_ = void 0;
				this.onSettledCallbacks_ = [];
				this.isRejectionHandled_ = !1;
				var m = this.createResolveAndReject_();
				try {
					d(m.resolve, m.reject);
				} catch (a) {
					m.reject(a);
				}
			};
			b.prototype.createResolveAndReject_ = function () {
				function d(e) {
					return function (h) {
						a || ((a = !0), e.call(m, h));
					};
				}
				var m = this,
					a = !1;
				return { resolve: d(this.resolveTo_), reject: d(this.reject_) };
			};
			b.prototype.resolveTo_ = function (d) {
				if (d === this)
					this.reject_(new TypeError("A Promise cannot resolve to itself"));
				else if (d instanceof b) this.settleSameAsPromise_(d);
				else {
					a: switch (typeof d) {
						case "object":
							var m = null != d;
							break a;
						case "function":
							m = !0;
							break a;
						default:
							m = !1;
					}
					m ? this.resolveToNonPromiseObj_(d) : this.fulfill_(d);
				}
			};
			b.prototype.resolveToNonPromiseObj_ = function (d) {
				var m = void 0;
				try {
					m = d.then;
				} catch (a) {
					this.reject_(a);
					return;
				}
				"function" == typeof m
					? this.settleSameAsThenable_(m, d)
					: this.fulfill_(d);
			};
			b.prototype.reject_ = function (d) {
				this.settle_(2, d);
			};
			b.prototype.fulfill_ = function (d) {
				this.settle_(1, d);
			};
			b.prototype.settle_ = function (d, m) {
				if (0 != this.state_)
					throw Error(
						"Cannot settle(" +
							d +
							", " +
							m +
							"): Promise already settled in state" +
							this.state_,
					);
				this.state_ = d;
				this.result_ = m;
				2 === this.state_ && this.scheduleUnhandledRejectionCheck_();
				this.executeOnSettledCallbacks_();
			};
			b.prototype.scheduleUnhandledRejectionCheck_ = function () {
				var d = this;
				f(function () {
					if (d.notifyUnhandledRejection_()) {
						var m = $jscomp.global.console;
						"undefined" !== typeof m && m.error(d.result_);
					}
				}, 1);
			};
			b.prototype.notifyUnhandledRejection_ = function () {
				if (this.isRejectionHandled_) return !1;
				var d = $jscomp.global.CustomEvent,
					m = $jscomp.global.Event,
					a = $jscomp.global.dispatchEvent;
				if ("undefined" === typeof a) return !0;
				"function" === typeof d
					? (d = new d("unhandledrejection", { cancelable: !0 }))
					: "function" === typeof m
						? (d = new m("unhandledrejection", { cancelable: !0 }))
						: ((d = $jscomp.global.document.createEvent("CustomEvent")),
							d.initCustomEvent("unhandledrejection", !1, !0, d));
				d.promise = this;
				d.reason = this.result_;
				return a(d);
			};
			b.prototype.executeOnSettledCallbacks_ = function () {
				if (null != this.onSettledCallbacks_) {
					for (var d = 0; d < this.onSettledCallbacks_.length; ++d)
						n.asyncExecute(this.onSettledCallbacks_[d]);
					this.onSettledCallbacks_ = null;
				}
			};
			var n = new l();
			b.prototype.settleSameAsPromise_ = function (d) {
				var m = this.createResolveAndReject_();
				d.callWhenSettled_(m.resolve, m.reject);
			};
			b.prototype.settleSameAsThenable_ = function (d, m) {
				var a = this.createResolveAndReject_();
				try {
					d.call(m, a.resolve, a.reject);
				} catch (e) {
					a.reject(e);
				}
			};
			b.prototype.then = function (d, m) {
				function a(v, t) {
					return "function" == typeof v
						? function (D) {
								try {
									e(v(D));
								} catch (p) {
									h(p);
								}
							}
						: t;
				}
				var e,
					h,
					q = new b(function (v, t) {
						e = v;
						h = t;
					});
				this.callWhenSettled_(a(d, e), a(m, h));
				return q;
			};
			b.prototype.catch = function (d) {
				return this.then(void 0, d);
			};
			b.prototype.callWhenSettled_ = function (d, m) {
				function a() {
					switch (e.state_) {
						case 1:
							d(e.result_);
							break;
						case 2:
							m(e.result_);
							break;
						default:
							throw Error("Unexpected state: " + e.state_);
					}
				}
				var e = this;
				null == this.onSettledCallbacks_
					? n.asyncExecute(a)
					: this.onSettledCallbacks_.push(a);
				this.isRejectionHandled_ = !0;
			};
			b.resolve = k;
			b.reject = function (d) {
				return new b(function (m, a) {
					a(d);
				});
			};
			b.race = function (d) {
				return new b(function (m, a) {
					for (
						var e = $jscomp.makeIterator(d), h = e.next();
						!h.done;
						h = e.next()
					)
						k(h.value).callWhenSettled_(m, a);
				});
			};
			b.all = function (d) {
				var m = $jscomp.makeIterator(d),
					a = m.next();
				return a.done
					? k([])
					: new b(function (e, h) {
							function q(D) {
								return function (p) {
									v[D] = p;
									t--;
									0 == t && e(v);
								};
							}
							var v = [],
								t = 0;
							do
								v.push(void 0),
									t++,
									k(a.value).callWhenSettled_(q(v.length - 1), h),
									(a = m.next());
							while (!a.done);
						});
			};
			return b;
		},
		"es6",
		"es3",
	);
	$jscomp.polyfill(
		"Array.from",
		function (g) {
			return g
				? g
				: function (l, k, f) {
						k =
							null != k
								? k
								: function (m) {
										return m;
									};
						var b = [],
							n =
								"undefined" != typeof Symbol &&
								Symbol.iterator &&
								l[Symbol.iterator];
						if ("function" == typeof n) {
							l = n.call(l);
							for (var d = 0; !(n = l.next()).done; )
								b.push(k.call(f, n.value, d++));
						} else
							for (n = l.length, d = 0; d < n; d++) b.push(k.call(f, l[d], d));
						return b;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.checkStringArgs = function (g, l, k) {
		if (null == g)
			throw new TypeError(
				"The 'this' value for String.prototype." +
					k +
					" must not be null or undefined",
			);
		if (l instanceof RegExp)
			throw new TypeError(
				"First argument to String.prototype." +
					k +
					" must not be a regular expression",
			);
		return g + "";
	};
	$jscomp.polyfill(
		"String.prototype.endsWith",
		function (g) {
			return g
				? g
				: function (l, k) {
						var f = $jscomp.checkStringArgs(this, l, "endsWith");
						l += "";
						void 0 === k && (k = f.length);
						k = Math.max(0, Math.min(k | 0, f.length));
						for (var b = l.length; 0 < b && 0 < k; )
							if (f[--k] != l[--b]) return !1;
						return 0 >= b;
					};
		},
		"es6",
		"es3",
	);
	$jscomp.underscoreProtoCanBeSet = function () {
		var g = { a: !0 },
			l = {};
		try {
			return (l.__proto__ = g), l.a;
		} catch (k) {}
		return !1;
	};
	$jscomp.setPrototypeOf =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.setPrototypeOf
			? Object.setPrototypeOf
			: $jscomp.underscoreProtoCanBeSet()
				? function (g, l) {
						g.__proto__ = l;
						if (g.__proto__ !== l)
							throw new TypeError(g + " is not extensible");
						return g;
					}
				: null;
	$jscomp.polyfill(
		"Object.setPrototypeOf",
		function (g) {
			return g || $jscomp.setPrototypeOf;
		},
		"es6",
		"es5",
	);
	$jscomp.assign =
		$jscomp.TRUST_ES6_POLYFILLS && "function" == typeof Object.assign
			? Object.assign
			: function (g, l) {
					for (var k = 1; k < arguments.length; k++) {
						var f = arguments[k];
						if (f) for (var b in f) $jscomp.owns(f, b) && (g[b] = f[b]);
					}
					return g;
				};
	$jscomp.polyfill(
		"Object.assign",
		function (g) {
			return g || $jscomp.assign;
		},
		"es6",
		"es3",
	);
	(function (g) {
		function l(f) {
			if (k[f]) return k[f].exports;
			var b = (k[f] = { i: f, l: !1, exports: {} });
			g[f].call(b.exports, b, b.exports, l);
			b.l = !0;
			return b.exports;
		}
		var k = {};
		l.m = g;
		l.c = k;
		l.d = function (f, b, n) {
			l.o(f, b) || Object.defineProperty(f, b, { enumerable: !0, get: n });
		};
		l.r = function (f) {
			"undefined" !== typeof Symbol &&
				Symbol.toStringTag &&
				Object.defineProperty(f, Symbol.toStringTag, { value: "Module" });
			Object.defineProperty(f, "__esModule", { value: !0 });
		};
		l.t = function (f, b) {
			b & 1 && (f = l(f));
			if (b & 8 || (b & 4 && "object" === typeof f && f && f.__esModule))
				return f;
			var n = Object.create(null);
			l.r(n);
			Object.defineProperty(n, "default", { enumerable: !0, value: f });
			if (b & 2 && "string" != typeof f)
				for (var d in f)
					l.d(
						n,
						d,
						function (m) {
							return f[m];
						}.bind(null, d),
					);
			return n;
		};
		l.n = function (f) {
			var b =
				f && f.__esModule
					? function () {
							return f["default"];
						}
					: function () {
							return f;
						};
			l.d(b, "a", b);
			return b;
		};
		l.o = function (f, b) {
			return Object.prototype.hasOwnProperty.call(f, b);
		};
		l.p = "/core/officeEditor";
		return l((l.s = 11));
	})([
		function (g, l, k) {
			k.d(l, "b", function () {
				return b;
			});
			k.d(l, "a", function () {
				return n;
			});
			var f = k(1),
				b = function (d, m) {
					Object(f.a)("disableLogs") ||
						(m ? console.warn("".concat(d, ": ").concat(m)) : console.warn(d));
				},
				n = function (d, m) {};
		},
		function (g, l, k) {
			k.d(l, "a", function () {
				return n;
			});
			k.d(l, "b", function () {
				return d;
			});
			var f = {},
				b = {
					flattenedResources: !1,
					CANVAS_CACHE_SIZE: void 0,
					maxPagesBefore: void 0,
					maxPagesAhead: void 0,
					disableLogs: !1,
					wvsQueryParameters: {},
					_trnDebugMode: !1,
					_logFiltersEnabled: null,
				},
				n = function (m) {
					return b[m];
				},
				d = function (m, a) {
					var e;
					b[m] = a;
					null === (e = f[m]) || void 0 === e
						? void 0
						: e.forEach(function (h) {
								h(a);
							});
				};
		},
		function (g, l, k) {
			k.d(l, "a", function () {
				return da;
			});
			k.d(l, "b", function () {
				return aa;
			});
			var f = k(3),
				b = k(0),
				n = k(6),
				d = k(4),
				m = k(5),
				a = "undefined" === typeof window ? self : window,
				e = a.importScripts,
				h = !1,
				q = function (x, w) {
					h ||
						(e(Object(m.a)("".concat(a.basePath, "decode.min.js"))), (h = !0));
					x = Object(d.b)(x);
					x = self.BrotliDecode(x);
					return w ? x : Object(d.a)(x);
				},
				v = function (x, w) {
					return Object(f.b)(void 0, void 0, Promise, function () {
						var y;
						return Object(f.c)(this, function (E) {
							switch (E.label) {
								case 0:
									return h
										? [3, 2]
										: [
												4,
												Object(n.a)(
													"".concat(
														self.Core.getWorkerPath(),
														"external/decode.min.js",
													),
													"Failed to download decode.min.js",
													window,
												),
											];
								case 1:
									E.sent(), (h = !0), (E.label = 2);
								case 2:
									return (
										(y = self.BrotliDecode(Object(d.b)(x))),
										[2, w ? y : Object(d.a)(y)]
									);
							}
						});
					});
				};
			(function () {
				function x() {
					this.remainingDataArrays = [];
				}
				x.prototype.processRaw = function (w) {
					return w;
				};
				x.prototype.processBrotli = function (w) {
					this.remainingDataArrays.push(w);
					return null;
				};
				x.prototype.GetNextChunk = function (w) {
					this.decodeFunction ||
						(this.decodeFunction =
							0 === w[0] && 97 === w[1] && 115 === w[2] && 109 === w[3]
								? this.processRaw
								: this.processBrotli);
					return this.decodeFunction(w);
				};
				x.prototype.End = function () {
					if (this.remainingDataArrays.length) {
						for (var w = this.arrays, y = 0, E = 0; E < w.length; ++E)
							y += w[E].length;
						y = new Uint8Array(y);
						var H = 0;
						for (E = 0; E < w.length; ++E) {
							var K = w[E];
							y.set(K, H);
							H += K.length;
						}
						return q(y, !0);
					}
					return null;
				};
				return x;
			})();
			var t = function (x, w, y) {
					void 0 === w && (w = !0);
					void 0 === y && (y = !1);
					var E = new XMLHttpRequest();
					E.open("GET", x, w);
					x = y && E.overrideMimeType;
					E.responseType = x ? "text" : "arraybuffer";
					x && E.overrideMimeType("text/plain; charset=x-user-defined");
					return E;
				},
				D = function (x, w, y) {
					return new Promise(function (E, H) {
						var K = t(x, w, y);
						K.send();
						K.onload = function () {
							200 === this.status || 0 === this.status
								? E(K.response)
								: H(Error("Download Failed ".concat(x)));
						};
						K.onerror = function () {
							H(Error("Network error occurred ".concat(x)));
						};
					});
				},
				p = function (x, w) {
					var y = w.decompressFunction,
						E = w.shouldOutputArray,
						H = w.compressedMaximum,
						K = "undefined" !== typeof e ? Date.now() : null;
					try {
						var W = E ? J(x) : x.join("");
						Object(b.a)("worker", "Result length is ".concat(W.length));
						if (W.length < H) {
							var U = y(W, E);
							Object(b.b)(
								"There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.",
							);
							e &&
								Object(b.a)(
									"worker",
									"Decompressed length is ".concat(U.length),
								);
							W = U;
						} else E || (W = Object(d.a)(W));
						if (e) {
							var Q = w.paths.join(", ");
							Object(b.a)(
								"worker",
								""
									.concat(Q, " Decompression took ")
									.concat(Date.now() - K, " ms"),
							);
						}
						return W;
					} catch (ba) {
						throw Error("Failed to decompress: ".concat(ba));
					}
				},
				J = function (x) {
					x = x.reduce(function (w, y) {
						y = new Uint8Array(y);
						return w.concat(Array.from(y));
					}, []);
					return new Uint8Array(x);
				},
				X = function (x) {
					var w = !x.shouldOutputArray,
						y = x.paths,
						E = x.isAsync;
					E
						? (y = Promise.all(
								y.map(function (H) {
									return D(H, E, w);
								}),
							)
								.then(function (H) {
									return p(H, x);
								})
								.catch(function (H) {
									throw Error(
										"Failed to fetch or decompress files: ".concat(H.message),
									);
								}))
						: ((y = y.map(function (H) {
								var K = t(H, E, w);
								K.send();
								if (200 === K.status || 0 === K.status) return K.response;
								throw Error("Failed to load ".concat(H));
							})),
							(y = p(y, x)));
					return y;
				},
				da = function (x) {
					var w = x.lastIndexOf("/");
					-1 === w && (w = 0);
					var y = x.slice(w).replace(".", ".br.");
					e ||
						(y.endsWith(".js.mem")
							? (y = y.replace(".js.mem", ".mem"))
							: y.endsWith(".js") && (y = y.concat(".mem")));
					return x.slice(0, w) + y;
				},
				ea = function (x) {
					return x.map(function (w) {
						return da(w);
					});
				},
				Ca = function (x, w) {
					w.decompressFunction = e ? q : v;
					w.paths = ea(x);
					return X(w);
				},
				ta = function (x, w, y, E) {
					return x.catch(function (H) {
						Object(b.b)(H);
						return E(w, y);
					});
				},
				aa = function (x, w, y, E) {
					x = Array.isArray(x) ? x : [x];
					a: {
						var H = [Ca];
						w = { compressedMaximum: w, isAsync: y, shouldOutputArray: E };
						if (w.isAsync) {
							var K = H[0](x, w);
							for (y = 1; y < H.length; ++y) K = ta(K, x, w, H[y]);
						} else {
							for (y = 0; y < H.length; y++) {
								E = H[y];
								try {
									K = E(x, w);
									break a;
								} catch (W) {
									Object(b.b)(W.message);
								}
							}
							throw Error("None of the worker files were able to load. ");
						}
					}
					return K;
				};
		},
		function (g, l, k) {
			function f(d, m, a, e) {
				function h(q) {
					return q instanceof a
						? q
						: new a(function (v) {
								v(q);
							});
				}
				return new (a || (a = Promise))(function (q, v) {
					function t(J) {
						try {
							p(e.next(J));
						} catch (X) {
							v(X);
						}
					}
					function D(J) {
						try {
							p(e["throw"](J));
						} catch (X) {
							v(X);
						}
					}
					function p(J) {
						J.done ? q(J.value) : h(J.value).then(t, D);
					}
					p((e = e.apply(d, m || [])).next());
				});
			}
			function b(d, m) {
				function a(p) {
					return function (J) {
						return e([p, J]);
					};
				}
				function e(p) {
					if (q) throw new TypeError("Generator is already executing.");
					for (; D && ((D = 0), p[0] && (h = 0)), h; )
						try {
							if (
								((q = 1),
								v &&
									(t =
										p[0] & 2
											? v["return"]
											: p[0]
												? v["throw"] || ((t = v["return"]) && t.call(v), 0)
												: v.next) &&
									!(t = t.call(v, p[1])).done)
							)
								return t;
							if (((v = 0), t)) p = [p[0] & 2, t.value];
							switch (p[0]) {
								case 0:
								case 1:
									t = p;
									break;
								case 4:
									return h.label++, { value: p[1], done: !1 };
								case 5:
									h.label++;
									v = p[1];
									p = [0];
									continue;
								case 7:
									p = h.ops.pop();
									h.trys.pop();
									continue;
								default:
									if (
										!((t = h.trys), (t = 0 < t.length && t[t.length - 1])) &&
										(6 === p[0] || 2 === p[0])
									) {
										h = 0;
										continue;
									}
									if (3 === p[0] && (!t || (p[1] > t[0] && p[1] < t[3])))
										h.label = p[1];
									else if (6 === p[0] && h.label < t[1])
										(h.label = t[1]), (t = p);
									else if (t && h.label < t[2]) (h.label = t[2]), h.ops.push(p);
									else {
										t[2] && h.ops.pop();
										h.trys.pop();
										continue;
									}
							}
							p = m.call(d, h);
						} catch (J) {
							(p = [6, J]), (v = 0);
						} finally {
							q = t = 0;
						}
					if (p[0] & 5) throw p[1];
					return { value: p[0] ? p[1] : void 0, done: !0 };
				}
				var h = {
						label: 0,
						sent: function () {
							if (t[0] & 1) throw t[1];
							return t[1];
						},
						trys: [],
						ops: [],
					},
					q,
					v,
					t,
					D = Object.create(
						("function" === typeof Iterator ? Iterator : Object).prototype,
					);
				return (
					(D.next = a(0)),
					(D["throw"] = a(1)),
					(D["return"] = a(2)),
					"function" === typeof Symbol &&
						(D[Symbol.iterator] = function () {
							return this;
						}),
					D
				);
			}
			k.d(l, "a", function () {
				return n;
			});
			k.d(l, "b", function () {
				return f;
			});
			k.d(l, "c", function () {
				return b;
			});
			var n = function () {
				n =
					Object.assign ||
					function (d) {
						for (var m, a = 1, e = arguments.length; a < e; a++) {
							m = arguments[a];
							for (var h in m)
								Object.prototype.hasOwnProperty.call(m, h) && (d[h] = m[h]);
						}
						return d;
					};
				return n.apply(this, arguments);
			};
		},
		function (g, l, k) {
			k.d(l, "b", function () {
				return f;
			});
			k.d(l, "a", function () {
				return b;
			});
			var f = function (n) {
					if ("string" === typeof n) {
						for (
							var d = new Uint8Array(n.length), m = n.length, a = 0;
							a < m;
							a++
						)
							d[a] = n.charCodeAt(a);
						return d;
					}
					return n;
				},
				b = function (n) {
					if ("string" !== typeof n) {
						for (var d = "", m = 0, a = n.length, e; m < a; )
							(e = n.subarray(m, m + 1024)),
								(m += 1024),
								(d += String.fromCharCode.apply(null, e));
						return d;
					}
					return n;
				};
		},
		function (g, l, k) {
			k.d(l, "a", function () {
				return h;
			});
			var f = k(3);
			g = k(8);
			var b = k.n(g),
				n = new Map(),
				d = function () {
					return ("undefined" === typeof window ? self : window).trustedTypes;
				},
				m = function (q, v) {
					return d().createPolicy(q, {
						createHTML: function (t) {
							return b.a.sanitize(
								t,
								Object(f.a)(Object(f.a)({}, v), { RETURN_TRUSTED_TYPE: !1 }),
							);
						},
						createScript: function (t) {
							return t;
						},
						createScriptURL: function (t) {
							return t;
						},
					});
				},
				a = function (q) {
					return d().createPolicy(q, {
						createHTML: function (v) {
							return v;
						},
						createScript: function (v) {
							return v;
						},
						createScriptURL: function (v) {
							return v;
						},
					});
				},
				e = function (q, v) {
					var t,
						D = {
							createHTML: function (p) {
								return b.a.sanitize(p, Object(f.a)({}, v));
							},
							createScript: function (p) {
								return p;
							},
							createScriptURL: function (p) {
								return p;
							},
						};
					if (null === (t = d()) || void 0 === t ? 0 : t.createPolicy)
						(t = "".concat(q, "-po")),
							n.has(q) || ((D = a(t)), n.set(t, D), (D = m(q, v)), n.set(q, D)),
							(D = n.get(v.createPolicyOnly ? t : q));
					return D;
				},
				h = function (q, v) {
					void 0 === v &&
						(v = { createPolicyOnly: !0, trustedTypesPolicyName: "webviewer" });
					return e(v.trustedTypesPolicyName, v).createScriptURL(q.toString());
				};
		},
		function (g, l, k) {
			function f(n, d, m) {
				return new Promise(function (a) {
					if (!n) return a();
					var e = m.document.createElement("script");
					e.type = "text/javascript";
					e.onload = function () {
						a();
					};
					e.onerror = function () {
						d && Object(b.b)(d);
						a();
					};
					e.src = n;
					m.document.getElementsByTagName("head")[0].appendChild(e);
				});
			}
			k.d(l, "a", function () {
				return f;
			});
			var b = k(0);
		},
		function (g, l, k) {
			function f(a, e, h, q) {
				return b(
					a,
					e,
					h,
					q,
					!!WebAssembly.instantiateStreaming,
					void 0,
					void 0,
				).then(function (v) {
					Object(n.a)(
						"load",
						"WASM compilation took ".concat(Date.now() - NaN, " ms"),
					);
					return v;
				});
			}
			function b(a, e, h, q, v, t, D) {
				t = t || Date.now();
				if (v && !q)
					return (
						Object(n.a)("load", "Try instantiateStreaming"),
						fetch(Object(d.a)(a))
							.then(function (p) {
								return WebAssembly.instantiateStreaming(p, e);
							})
							.catch(function (p) {
								Object(n.a)(
									"load",
									"instantiateStreaming Failed "
										.concat(a, " message ")
										.concat(p.message),
								);
								return b(a, e, h, q, !1, t, D);
							})
					);
				v = q
					? q.map(function (p, J) {
							return "".concat(p, "PDFNetCWasm-chunk-").concat(J, ".wasm");
						})
					: a;
				return Object(d.b)(v, h, !0, !0).then(function (p) {
					D = Date.now();
					Object(n.a)("load", "Request took ".concat(D - t, " ms"));
					return WebAssembly.instantiate(p, e);
				});
			}
			k.d(l, "a", function () {
				return f;
			});
			var n = k(0),
				d = k(2),
				m = k(6);
			k.d(l, "b", function () {
				return m.a;
			});
		},
		function (g, l, k) {
			(function (f, b) {
				g.exports = b();
			})(this, function () {
				function f(F) {
					return function (z) {
						z instanceof RegExp && (z.lastIndex = 0);
						for (
							var B = arguments.length, G = Array(1 < B ? B - 1 : 0), I = 1;
							I < B;
							I++
						)
							G[I - 1] = arguments[I];
						return X(F, z, G);
					};
				}
				function b(F, z) {
					var B =
						2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : w;
					h && h(F, null);
					for (var G = z.length; G--; ) {
						var I = z[G];
						if ("string" === typeof I) {
							var Y = B(I);
							Y !== I && (q(z) || (z[G] = Y), (I = Y));
						}
						F[I] = !0;
					}
					return F;
				}
				function n(F) {
					for (
						var z = J(null), B = $jscomp.makeIterator(e(F)), G = B.next();
						!G.done;
						G = B.next()
					) {
						var I = $jscomp.makeIterator(G.value);
						G = I.next().value;
						I = I.next().value;
						if (U(F, G))
							if (Array.isArray(I)) {
								for (var Y = 0; Y < I.length; Y++) U(I, Y) || (I[Y] = null);
								z[G] = I;
							} else
								z[G] =
									I && "object" === typeof I && I.constructor === Object
										? n(I)
										: I;
					}
					return z;
				}
				function d(F, z) {
					for (; null !== F; ) {
						var B = t(F, z);
						if (B) {
							if (B.get) return f(B.get);
							if ("function" === typeof B.value) return f(B.value);
						}
						F = v(F);
					}
					return function () {
						return null;
					};
				}
				function m() {
					function F(c, r, u) {
						ea(c, function (A) {
							A.call(B, r, u, fa);
						});
					}
					var z =
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: "undefined" === typeof window
									? null
									: window,
						B = function (c) {
							return m(c);
						};
					B.version = "3.2.5";
					B.removed = [];
					if (
						!z ||
						!z.document ||
						z.document.nodeType !== ma.document ||
						!z.Element
					)
						return (B.isSupported = !1), B;
					var G = z.document,
						I = G,
						Y = I.currentScript,
						Ya = z.DocumentFragment,
						M = z.HTMLTemplateElement,
						Da = z.Node,
						Za = z.Element,
						na = z.NodeFilter,
						Fb =
							void 0 === z.NamedNodeMap
								? z.NamedNodeMap || z.MozNamedAttrMap
								: z.NamedNodeMap,
						Gb = z.HTMLFormElement,
						Hb = z.DOMParser,
						ua = z.trustedTypes;
					z = Za.prototype;
					var Ib = d(z, "cloneNode"),
						Jb = d(z, "remove"),
						Kb = d(z, "nextSibling"),
						Lb = d(z, "childNodes"),
						va = d(z, "parentNode");
					"function" === typeof M &&
						((M = G.createElement("template")),
						M.content &&
							M.content.ownerDocument &&
							(G = M.content.ownerDocument));
					var S,
						oa = "";
					M = G;
					var Ea = M.implementation,
						Mb = M.createNodeIterator,
						Nb = M.createDocumentFragment,
						Ob = M.getElementsByTagName,
						Pb = I.importNode,
						T = $a();
					B.isSupported =
						"function" === typeof e &&
						"function" === typeof va &&
						Ea &&
						void 0 !== Ea.createHTMLDocument;
					M = ab;
					var Fa = M.MUSTACHE_EXPR,
						Ga = M.ERB_EXPR,
						Ha = M.TMPLIT_EXPR,
						Qb = M.DATA_ATTR,
						Rb = M.ARIA_ATTR,
						Sb = M.IS_SCRIPT_OR_DATA,
						bb = M.ATTR_WHITESPACE,
						cb = M.CUSTOM_ELEMENT,
						db = ab.IS_ALLOWED_URI,
						O = null,
						gb = b(
							{},
							[].concat(
								$jscomp.arrayFromIterable(eb),
								$jscomp.arrayFromIterable(Ia),
								$jscomp.arrayFromIterable(Ja),
								$jscomp.arrayFromIterable(Ka),
								$jscomp.arrayFromIterable(fb),
							),
						),
						R = null,
						jb = b(
							{},
							[].concat(
								$jscomp.arrayFromIterable(hb),
								$jscomp.arrayFromIterable(La),
								$jscomp.arrayFromIterable(ib),
								$jscomp.arrayFromIterable(wa),
							),
						),
						L = Object.seal(
							J(null, {
								tagNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								attributeNameCheck: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: null,
								},
								allowCustomizedBuiltInElements: {
									writable: !0,
									configurable: !1,
									enumerable: !0,
									value: !1,
								},
							}),
						),
						pa = null,
						Ma = null,
						kb = !0,
						Na = !0,
						lb = !1,
						mb = !0,
						ha = !1,
						Oa = !0,
						ca = !1,
						Pa = !1,
						Qa = !1,
						ia = !1,
						xa = !1,
						ya = !1,
						nb = !0,
						ob = !1,
						Ra = !0,
						qa = !1,
						ja = {},
						ka = null,
						pb = b(
							{},
							"annotation-xml audio colgroup desc foreignobject head iframe math mi mn mo ms mtext noembed noframes noscript plaintext script style svg template thead title video xmp".split(
								" ",
							),
						),
						qb = null,
						rb = b({}, "audio video img source image track".split(" ")),
						Sa = null,
						sb = b(
							{},
							"alt class for id label name pattern placeholder role summary title value style xmlns".split(
								" ",
							),
						),
						la = "http://www.w3.org/1999/xhtml",
						tb = !1,
						Ta = null,
						Tb = b(
							{},
							[
								"http://www.w3.org/1998/Math/MathML",
								"http://www.w3.org/2000/svg",
								"http://www.w3.org/1999/xhtml",
							],
							y,
						),
						za = b({}, ["mi", "mo", "mn", "ms", "mtext"]),
						Aa = b({}, ["annotation-xml"]),
						Ub = b({}, ["title", "style", "font", "a", "script"]),
						ra = null,
						Vb = ["application/xhtml+xml", "text/html"],
						P = null,
						fa = null,
						Wb = G.createElement("form"),
						ub = function (c) {
							return c instanceof RegExp || c instanceof Function;
						},
						Ua = function () {
							var c =
								0 < arguments.length && void 0 !== arguments[0]
									? arguments[0]
									: {};
							if (!fa || fa !== c) {
								(c && "object" === typeof c) || (c = {});
								c = n(c);
								ra =
									-1 === Vb.indexOf(c.PARSER_MEDIA_TYPE)
										? "text/html"
										: c.PARSER_MEDIA_TYPE;
								P = "application/xhtml+xml" === ra ? y : w;
								O = U(c, "ALLOWED_TAGS") ? b({}, c.ALLOWED_TAGS, P) : gb;
								R = U(c, "ALLOWED_ATTR") ? b({}, c.ALLOWED_ATTR, P) : jb;
								Ta = U(c, "ALLOWED_NAMESPACES")
									? b({}, c.ALLOWED_NAMESPACES, y)
									: Tb;
								Sa = U(c, "ADD_URI_SAFE_ATTR")
									? b(n(sb), c.ADD_URI_SAFE_ATTR, P)
									: sb;
								qb = U(c, "ADD_DATA_URI_TAGS")
									? b(n(rb), c.ADD_DATA_URI_TAGS, P)
									: rb;
								ka = U(c, "FORBID_CONTENTS") ? b({}, c.FORBID_CONTENTS, P) : pb;
								pa = U(c, "FORBID_TAGS") ? b({}, c.FORBID_TAGS, P) : {};
								Ma = U(c, "FORBID_ATTR") ? b({}, c.FORBID_ATTR, P) : {};
								ja = U(c, "USE_PROFILES") ? c.USE_PROFILES : !1;
								kb = !1 !== c.ALLOW_ARIA_ATTR;
								Na = !1 !== c.ALLOW_DATA_ATTR;
								lb = c.ALLOW_UNKNOWN_PROTOCOLS || !1;
								mb = !1 !== c.ALLOW_SELF_CLOSE_IN_ATTR;
								ha = c.SAFE_FOR_TEMPLATES || !1;
								Oa = !1 !== c.SAFE_FOR_XML;
								ca = c.WHOLE_DOCUMENT || !1;
								ia = c.RETURN_DOM || !1;
								xa = c.RETURN_DOM_FRAGMENT || !1;
								ya = c.RETURN_TRUSTED_TYPE || !1;
								Qa = c.FORCE_BODY || !1;
								nb = !1 !== c.SANITIZE_DOM;
								ob = c.SANITIZE_NAMED_PROPS || !1;
								Ra = !1 !== c.KEEP_CONTENT;
								qa = c.IN_PLACE || !1;
								db = c.ALLOWED_URI_REGEXP || vb;
								la = c.NAMESPACE || "http://www.w3.org/1999/xhtml";
								za = c.MATHML_TEXT_INTEGRATION_POINTS || za;
								Aa = c.HTML_INTEGRATION_POINTS || Aa;
								L = c.CUSTOM_ELEMENT_HANDLING || {};
								c.CUSTOM_ELEMENT_HANDLING &&
									ub(c.CUSTOM_ELEMENT_HANDLING.tagNameCheck) &&
									(L.tagNameCheck = c.CUSTOM_ELEMENT_HANDLING.tagNameCheck);
								c.CUSTOM_ELEMENT_HANDLING &&
									ub(c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck) &&
									(L.attributeNameCheck =
										c.CUSTOM_ELEMENT_HANDLING.attributeNameCheck);
								c.CUSTOM_ELEMENT_HANDLING &&
									"boolean" ===
										typeof c.CUSTOM_ELEMENT_HANDLING
											.allowCustomizedBuiltInElements &&
									(L.allowCustomizedBuiltInElements =
										c.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements);
								ha && (Na = !1);
								xa && (ia = !0);
								ja &&
									((O = b({}, fb)),
									(R = []),
									!0 === ja.html && (b(O, eb), b(R, hb)),
									!0 === ja.svg && (b(O, Ia), b(R, La), b(R, wa)),
									!0 === ja.svgFilters && (b(O, Ja), b(R, La), b(R, wa)),
									!0 === ja.mathMl && (b(O, Ka), b(R, ib), b(R, wa)));
								c.ADD_TAGS && (O === gb && (O = n(O)), b(O, c.ADD_TAGS, P));
								c.ADD_ATTR && (R === jb && (R = n(R)), b(R, c.ADD_ATTR, P));
								c.ADD_URI_SAFE_ATTR && b(Sa, c.ADD_URI_SAFE_ATTR, P);
								c.FORBID_CONTENTS &&
									(ka === pb && (ka = n(ka)), b(ka, c.FORBID_CONTENTS, P));
								Ra && (O["#text"] = !0);
								ca && b(O, ["html", "head", "body"]);
								O.table && (b(O, ["tbody"]), delete pa.tbody);
								if (c.TRUSTED_TYPES_POLICY) {
									if ("function" !== typeof c.TRUSTED_TYPES_POLICY.createHTML)
										throw ba(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.',
										);
									if (
										"function" !== typeof c.TRUSTED_TYPES_POLICY.createScriptURL
									)
										throw ba(
											'TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.',
										);
									S = c.TRUSTED_TYPES_POLICY;
									oa = S.createHTML("");
								} else
									void 0 === S && (S = Xb(ua, Y)),
										null !== S &&
											"string" === typeof oa &&
											(oa = S.createHTML(""));
								p && p(c);
								fa = c;
							}
						},
						wb = b(
							{},
							[].concat(
								$jscomp.arrayFromIterable(Ia),
								$jscomp.arrayFromIterable(Ja),
								$jscomp.arrayFromIterable(Yb),
							),
						),
						xb = b(
							{},
							[].concat(
								$jscomp.arrayFromIterable(Ka),
								$jscomp.arrayFromIterable(Zb),
							),
						),
						$b = function (c) {
							var r = va(c);
							(r && r.tagName) ||
								(r = { namespaceURI: la, tagName: "template" });
							var u = w(c.tagName),
								A = w(r.tagName);
							return Ta[c.namespaceURI]
								? "http://www.w3.org/2000/svg" === c.namespaceURI
									? "http://www.w3.org/1999/xhtml" === r.namespaceURI
										? "svg" === u
										: "http://www.w3.org/1998/Math/MathML" === r.namespaceURI
											? "svg" === u && ("annotation-xml" === A || za[A])
											: !!wb[u]
									: "http://www.w3.org/1998/Math/MathML" === c.namespaceURI
										? "http://www.w3.org/1999/xhtml" === r.namespaceURI
											? "math" === u
											: "http://www.w3.org/2000/svg" === r.namespaceURI
												? "math" === u && Aa[A]
												: !!xb[u]
										: "http://www.w3.org/1999/xhtml" === c.namespaceURI
											? ("http://www.w3.org/2000/svg" === r.namespaceURI &&
													!Aa[A]) ||
												("http://www.w3.org/1998/Math/MathML" ===
													r.namespaceURI &&
													!za[A])
												? !1
												: !xb[u] && (Ub[u] || !wb[u])
											: "application/xhtml+xml" === ra && Ta[c.namespaceURI]
												? !0
												: !1
								: !1;
						},
						Z = function (c) {
							aa(B.removed, { element: c });
							try {
								va(c).removeChild(c);
							} catch (r) {
								Jb(c);
							}
						},
						Ba = function (c, r) {
							try {
								aa(B.removed, { attribute: r.getAttributeNode(c), from: r });
							} catch (u) {
								aa(B.removed, { attribute: null, from: r });
							}
							r.removeAttribute(c);
							if ("is" === c)
								if (ia || xa)
									try {
										Z(r);
									} catch (u) {}
								else
									try {
										r.setAttribute(c, "");
									} catch (u) {}
						},
						yb = function (c) {
							var r = null,
								u = null;
							Qa
								? (c = "<remove></remove>" + c)
								: (u = (u = E(c, /^[\r\n\t ]+/)) && u[0]);
							"application/xhtml+xml" === ra &&
								"http://www.w3.org/1999/xhtml" === la &&
								(c =
									'<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>' +
									c +
									"</body></html>");
							var A = S ? S.createHTML(c) : c;
							if ("http://www.w3.org/1999/xhtml" === la)
								try {
									r = new Hb().parseFromString(A, ra);
								} catch (C) {}
							if (!r || !r.documentElement) {
								r = Ea.createDocument(la, "template", null);
								try {
									r.documentElement.innerHTML = tb ? oa : A;
								} catch (C) {}
							}
							A = r.body || r.documentElement;
							c &&
								u &&
								A.insertBefore(G.createTextNode(u), A.childNodes[0] || null);
							return "http://www.w3.org/1999/xhtml" === la
								? Ob.call(r, ca ? "html" : "body")[0]
								: ca
									? r.documentElement
									: A;
						},
						zb = function (c) {
							return Mb.call(
								c.ownerDocument || c,
								c,
								na.SHOW_ELEMENT |
									na.SHOW_COMMENT |
									na.SHOW_TEXT |
									na.SHOW_PROCESSING_INSTRUCTION |
									na.SHOW_CDATA_SECTION,
								null,
							);
						},
						Va = function (c) {
							return (
								c instanceof Gb &&
								("string" !== typeof c.nodeName ||
									"string" !== typeof c.textContent ||
									"function" !== typeof c.removeChild ||
									!(c.attributes instanceof Fb) ||
									"function" !== typeof c.removeAttribute ||
									"function" !== typeof c.setAttribute ||
									"string" !== typeof c.namespaceURI ||
									"function" !== typeof c.insertBefore ||
									"function" !== typeof c.hasChildNodes)
							);
						},
						Ab = function (c) {
							return "function" === typeof Da && c instanceof Da;
						},
						Bb = function (c) {
							var r = null;
							F(T.beforeSanitizeElements, c, null);
							if (Va(c)) return Z(c), !0;
							var u = P(c.nodeName);
							F(T.uponSanitizeElement, c, { tagName: u, allowedTags: O });
							if (
								(c.hasChildNodes() &&
									!Ab(c.firstElementChild) &&
									Q(/<[/\w!]/g, c.innerHTML) &&
									Q(/<[/\w!]/g, c.textContent)) ||
								c.nodeType === ma.progressingInstruction ||
								(Oa && c.nodeType === ma.comment && Q(/<[/\w]/g, c.data))
							)
								return Z(c), !0;
							if (!O[u] || pa[u]) {
								if (
									!pa[u] &&
									"annotation-xml" !== u &&
									E(u, cb) &&
									((L.tagNameCheck instanceof RegExp && Q(L.tagNameCheck, u)) ||
										(L.tagNameCheck instanceof Function && L.tagNameCheck(u)))
								)
									return !1;
								if (Ra && !ka[u]) {
									u = va(c) || c.parentNode;
									var A = Lb(c) || c.childNodes;
									if (A && u)
										for (var C = A.length - 1; 0 <= C; --C) {
											var N = Ib(A[C], !0);
											N.__removalCount = (c.__removalCount || 0) + 1;
											u.insertBefore(N, Kb(c));
										}
								}
								Z(c);
								return !0;
							}
							if (
								(c instanceof Za && !$b(c)) ||
								(("noscript" === u || "noembed" === u || "noframes" === u) &&
									Q(/<\/no(script|embed|frames)/i, c.innerHTML))
							)
								return Z(c), !0;
							ha &&
								c.nodeType === ma.text &&
								((r = c.textContent),
								ea([Fa, Ga, Ha], function (V) {
									r = H(r, V, " ");
								}),
								c.textContent !== r &&
									(aa(B.removed, { element: c.cloneNode() }),
									(c.textContent = r)));
							F(T.afterSanitizeElements, c, null);
							return !1;
						},
						Cb = function (c, r, u) {
							if (nb && ("id" === r || "name" === r) && (u in G || u in Wb))
								return !1;
							if (!Na || Ma[r] || !Q(Qb, r))
								if (!kb || !Q(Rb, r))
									if (!R[r] || Ma[r]) {
										if (
											!(
												("annotation-xml" !== c &&
													E(c, cb) &&
													((L.tagNameCheck instanceof RegExp &&
														Q(L.tagNameCheck, c)) ||
														(L.tagNameCheck instanceof Function &&
															L.tagNameCheck(c))) &&
													((L.attributeNameCheck instanceof RegExp &&
														Q(L.attributeNameCheck, r)) ||
														(L.attributeNameCheck instanceof Function &&
															L.attributeNameCheck(r)))) ||
												("is" === r &&
													L.allowCustomizedBuiltInElements &&
													((L.tagNameCheck instanceof RegExp &&
														Q(L.tagNameCheck, u)) ||
														(L.tagNameCheck instanceof Function &&
															L.tagNameCheck(u))))
											)
										)
											return !1;
									} else if (
										!(
											Sa[r] ||
											Q(db, H(u, bb, "")) ||
											(("src" === r || "xlink:href" === r || "href" === r) &&
												"script" !== c &&
												0 === K(u, "data:") &&
												qb[c]) ||
											(lb && !Q(Sb, H(u, bb, "")))
										) &&
										u
									)
										return !1;
							return !0;
						},
						Db = function (c) {
							F(T.beforeSanitizeAttributes, c, null);
							var r = c.attributes;
							if (r && !Va(c)) {
								for (
									var u = {
											attrName: "",
											attrValue: "",
											keepAttr: !0,
											allowedAttributes: R,
											forceKeepAttr: void 0,
										},
										A = r.length,
										C = {};
									A--;
								) {
									var N = r[A],
										V = N.name,
										Wa = N.namespaceURI,
										sa = N.value;
									N = P(V);
									C.$jscomp$loop$prop$value$5 = "value" === V ? sa : W(sa);
									u.attrName = N;
									u.attrValue = C.$jscomp$loop$prop$value$5;
									u.keepAttr = !0;
									u.forceKeepAttr = void 0;
									F(T.uponSanitizeAttribute, c, u);
									C.$jscomp$loop$prop$value$5 = u.attrValue;
									!ob ||
										("id" !== N && "name" !== N) ||
										(Ba(V, c),
										(C.$jscomp$loop$prop$value$5 =
											"user-content-" + C.$jscomp$loop$prop$value$5));
									if (
										Oa &&
										Q(
											/((--!?|])>)|<\/(style|title)/i,
											C.$jscomp$loop$prop$value$5,
										)
									)
										Ba(V, c);
									else if (!u.forceKeepAttr && (Ba(V, c), u.keepAttr))
										if (!mb && Q(/\/>/i, C.$jscomp$loop$prop$value$5)) Ba(V, c);
										else if (
											(ha &&
												ea(
													[Fa, Ga, Ha],
													(function (Xa) {
														return function (ac) {
															Xa.$jscomp$loop$prop$value$5 = H(
																Xa.$jscomp$loop$prop$value$5,
																ac,
																" ",
															);
														};
													})(C),
												),
											(sa = P(c.nodeName)),
											Cb(sa, N, C.$jscomp$loop$prop$value$5))
										) {
											if (
												S &&
												"object" === typeof ua &&
												"function" === typeof ua.getAttributeType &&
												!Wa
											)
												switch (ua.getAttributeType(sa, N)) {
													case "TrustedHTML":
														C.$jscomp$loop$prop$value$5 = S.createHTML(
															C.$jscomp$loop$prop$value$5,
														);
														break;
													case "TrustedScriptURL":
														C.$jscomp$loop$prop$value$5 = S.createScriptURL(
															C.$jscomp$loop$prop$value$5,
														);
												}
											try {
												Wa
													? c.setAttributeNS(Wa, V, C.$jscomp$loop$prop$value$5)
													: c.setAttribute(V, C.$jscomp$loop$prop$value$5),
													Va(c) ? Z(c) : ta(B.removed);
											} catch (Xa) {}
										}
									C = {
										$jscomp$loop$prop$value$5: C.$jscomp$loop$prop$value$5,
									};
								}
								F(T.afterSanitizeAttributes, c, null);
							}
						},
						bc = function u(r) {
							var A,
								C = zb(r);
							for (F(T.beforeSanitizeShadowDOM, r, null); (A = C.nextNode()); )
								F(T.uponSanitizeShadowNode, A, null),
									Bb(A),
									Db(A),
									A.content instanceof Ya && u(A.content);
							F(T.afterSanitizeShadowDOM, r, null);
						};
					B.sanitize = function (r) {
						var u =
								1 < arguments.length && void 0 !== arguments[1]
									? arguments[1]
									: {},
							A = null,
							C = null;
						C = C = null;
						(tb = !r) && (r = "\x3c!--\x3e");
						if ("string" !== typeof r && !Ab(r))
							if ("function" === typeof r.toString) {
								if (((r = r.toString()), "string" !== typeof r))
									throw ba("dirty is not a string, aborting");
							} else throw ba("toString is not a function");
						if (!B.isSupported) return r;
						Pa || Ua(u);
						B.removed = [];
						"string" === typeof r && (qa = !1);
						if (qa) {
							if (r.nodeName && ((u = P(r.nodeName)), !O[u] || pa[u]))
								throw ba(
									"root node is forbidden and cannot be sanitized in-place",
								);
						} else if (r instanceof Da)
							(A = yb("\x3c!----\x3e")),
								(C = A.ownerDocument.importNode(r, !0)),
								C.nodeType === ma.element && "BODY" === C.nodeName
									? (A = C)
									: "HTML" === C.nodeName
										? (A = C)
										: A.appendChild(C);
						else {
							if (!ia && !ha && !ca && -1 === r.indexOf("<"))
								return S && ya ? S.createHTML(r) : r;
							A = yb(r);
							if (!A) return ia ? null : ya ? oa : "";
						}
						A && Qa && Z(A.firstChild);
						for (u = zb(qa ? r : A); (C = u.nextNode()); )
							Bb(C), Db(C), C.content instanceof Ya && bc(C.content);
						if (qa) return r;
						if (ia) {
							if (xa)
								for (C = Nb.call(A.ownerDocument); A.firstChild; )
									C.appendChild(A.firstChild);
							else C = A;
							if (R.shadowroot || R.shadowrootmode) C = Pb.call(I, C, !0);
							return C;
						}
						var N = ca ? A.outerHTML : A.innerHTML;
						ca &&
							O["!doctype"] &&
							A.ownerDocument &&
							A.ownerDocument.doctype &&
							A.ownerDocument.doctype.name &&
							Q(Eb, A.ownerDocument.doctype.name) &&
							(N = "<!DOCTYPE " + A.ownerDocument.doctype.name + ">\n" + N);
						ha &&
							ea([Fa, Ga, Ha], function (V) {
								N = H(N, V, " ");
							});
						return S && ya ? S.createHTML(N) : N;
					};
					B.setConfig = function () {
						Ua(
							0 < arguments.length && void 0 !== arguments[0]
								? arguments[0]
								: {},
						);
						Pa = !0;
					};
					B.clearConfig = function () {
						fa = null;
						Pa = !1;
					};
					B.isValidAttribute = function (r, u, A) {
						fa || Ua({});
						r = P(r);
						u = P(u);
						return Cb(r, u, A);
					};
					B.addHook = function (r, u) {
						"function" === typeof u && aa(T[r], u);
					};
					B.removeHook = function (r, u) {
						return void 0 !== u
							? ((u = Ca(T[r], u)), -1 === u ? void 0 : x(T[r], u, 1)[0])
							: ta(T[r]);
					};
					B.removeHooks = function (r) {
						T[r] = [];
					};
					B.removeAllHooks = function () {
						T = $a();
					};
					return B;
				}
				var a = Object,
					e = a.entries,
					h = a.setPrototypeOf,
					q = a.isFrozen,
					v = a.getPrototypeOf,
					t = a.getOwnPropertyDescriptor,
					D = Object,
					p = D.freeze;
				a = D.seal;
				var J = D.create;
				D = "undefined" !== typeof Reflect && Reflect;
				var X = D.apply,
					da = D.construct;
				p ||
					(p = function (F) {
						return F;
					});
				a ||
					(a = function (F) {
						return F;
					});
				X ||
					(X = function (F, z, B) {
						return F.apply(z, B);
					});
				da ||
					(da = function (F, z) {
						return new (Function.prototype.bind.apply(
							F,
							[null].concat($jscomp.arrayFromIterable(z)),
						))();
					});
				var ea = f(Array.prototype.forEach),
					Ca = f(Array.prototype.lastIndexOf),
					ta = f(Array.prototype.pop),
					aa = f(Array.prototype.push),
					x = f(Array.prototype.splice),
					w = f(String.prototype.toLowerCase),
					y = f(String.prototype.toString),
					E = f(String.prototype.match),
					H = f(String.prototype.replace),
					K = f(String.prototype.indexOf),
					W = f(String.prototype.trim),
					U = f(Object.prototype.hasOwnProperty),
					Q = f(RegExp.prototype.test),
					ba = (function (F) {
						return function () {
							for (var z = arguments.length, B = Array(z), G = 0; G < z; G++)
								B[G] = arguments[G];
							return da(F, B);
						};
					})(TypeError),
					eb = p(
						"a abbr acronym address area article aside audio b bdi bdo big blink blockquote body br button canvas caption center cite code col colgroup content data datalist dd decorator del details dfn dialog dir div dl dt element em fieldset figcaption figure font footer form h1 h2 h3 h4 h5 h6 head header hgroup hr html i img input ins kbd label legend li main map mark marquee menu menuitem meter nav nobr ol optgroup option output p picture pre progress q rp rt ruby s samp section select shadow small source spacer span strike strong style sub summary sup table tbody td template textarea tfoot th thead time tr track tt u ul var video wbr".split(
							" ",
						),
					),
					Ia = p(
						"svg a altglyph altglyphdef altglyphitem animatecolor animatemotion animatetransform circle clippath defs desc ellipse filter font g glyph glyphref hkern image line lineargradient marker mask metadata mpath path pattern polygon polyline radialgradient rect stop style switch symbol text textpath title tref tspan view vkern".split(
							" ",
						),
					),
					Ja = p(
						"feBlend feColorMatrix feComponentTransfer feComposite feConvolveMatrix feDiffuseLighting feDisplacementMap feDistantLight feDropShadow feFlood feFuncA feFuncB feFuncG feFuncR feGaussianBlur feImage feMerge feMergeNode feMorphology feOffset fePointLight feSpecularLighting feSpotLight feTile feTurbulence".split(
							" ",
						),
					),
					Yb = p(
						"animate color-profile cursor discard font-face font-face-format font-face-name font-face-src font-face-uri foreignobject hatch hatchpath mesh meshgradient meshpatch meshrow missing-glyph script set solidcolor unknown use".split(
							" ",
						),
					),
					Ka = p(
						"math menclose merror mfenced mfrac mglyph mi mlabeledtr mmultiscripts mn mo mover mpadded mphantom mroot mrow ms mspace msqrt mstyle msub msup msubsup mtable mtd mtext mtr munder munderover mprescripts".split(
							" ",
						),
					),
					Zb = p(
						"maction maligngroup malignmark mlongdiv mscarries mscarry msgroup mstack msline msrow semantics annotation annotation-xml mprescripts none".split(
							" ",
						),
					),
					fb = p(["#text"]),
					hb = p(
						"accept action align alt autocapitalize autocomplete autopictureinpicture autoplay background bgcolor border capture cellpadding cellspacing checked cite class clear color cols colspan controls controlslist coords crossorigin datetime decoding default dir disabled disablepictureinpicture disableremoteplayback download draggable enctype enterkeyhint face for headers height hidden high href hreflang id inputmode integrity ismap kind label lang list loading loop low max maxlength media method min minlength multiple muted name nonce noshade novalidate nowrap open optimum pattern placeholder playsinline popover popovertarget popovertargetaction poster preload pubdate radiogroup readonly rel required rev reversed role rows rowspan spellcheck scope selected shape size sizes span srclang start src srcset step style summary tabindex title translate type usemap valign value width wrap xmlns slot".split(
							" ",
						),
					),
					La = p(
						"accent-height accumulate additive alignment-baseline amplitude ascent attributename attributetype azimuth basefrequency baseline-shift begin bias by class clip clippathunits clip-path clip-rule color color-interpolation color-interpolation-filters color-profile color-rendering cx cy d dx dy diffuseconstant direction display divisor dur edgemode elevation end exponent fill fill-opacity fill-rule filter filterunits flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight fx fy g1 g2 glyph-name glyphref gradientunits gradienttransform height href id image-rendering in in2 intercept k k1 k2 k3 k4 kerning keypoints keysplines keytimes lang lengthadjust letter-spacing kernelmatrix kernelunitlength lighting-color local marker-end marker-mid marker-start markerheight markerunits markerwidth maskcontentunits maskunits max mask media method mode min name numoctaves offset operator opacity order orient orientation origin overflow paint-order path pathlength patterncontentunits patterntransform patternunits points preservealpha preserveaspectratio primitiveunits r rx ry radius refx refy repeatcount repeatdur restart result rotate scale seed shape-rendering slope specularconstant specularexponent spreadmethod startoffset stddeviation stitchtiles stop-color stop-opacity stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke stroke-width style surfacescale systemlanguage tabindex tablevalues targetx targety transform transform-origin text-anchor text-decoration text-rendering textlength type u1 u2 unicode values viewbox visibility version vert-adv-y vert-origin-x vert-origin-y width word-spacing wrap writing-mode xchannelselector ychannelselector x x1 x2 xmlns y y1 y2 z zoomandpan".split(
							" ",
						),
					),
					ib = p(
						"accent accentunder align bevelled close columnsalign columnlines columnspan denomalign depth dir display displaystyle encoding fence frame height href id largeop length linethickness lspace lquote mathbackground mathcolor mathsize mathvariant maxsize minsize movablelimits notation numalign open rowalign rowlines rowspacing rowspan rspace rquote scriptlevel scriptminsize scriptsizemultiplier selection separator separators stretchy subscriptshift supscriptshift symmetric voffset width xmlns".split(
							" ",
						),
					),
					wa = p([
						"xlink:href",
						"xml:id",
						"xlink:title",
						"xml:space",
						"xmlns:xlink",
					]);
				D = a(/\{\{[\w\W]*|[\w\W]*\}\}/gm);
				var cc = a(/<%[\w\W]*|[\w\W]*%>/gm),
					dc = a(/\$\{[\w\W]*/gm),
					ec = a(/^data-[\-\w.\u00B7-\uFFFF]+$/),
					fc = a(/^aria-[\-\w]+$/),
					vb = a(
						/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
					),
					gc = a(/^(?:\w+script|data):/i),
					hc = a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),
					Eb = a(/^html$/i);
				a = a(/^[a-z][.\w]*(-[.\w]+)+$/i);
				var ab = Object.freeze({
						__proto__: null,
						ARIA_ATTR: fc,
						ATTR_WHITESPACE: hc,
						CUSTOM_ELEMENT: a,
						DATA_ATTR: ec,
						DOCTYPE_NAME: Eb,
						ERB_EXPR: cc,
						IS_ALLOWED_URI: vb,
						IS_SCRIPT_OR_DATA: gc,
						MUSTACHE_EXPR: D,
						TMPLIT_EXPR: dc,
					}),
					ma = {
						element: 1,
						attribute: 2,
						text: 3,
						cdataSection: 4,
						entityReference: 5,
						entityNode: 6,
						progressingInstruction: 7,
						comment: 8,
						document: 9,
						documentType: 10,
						documentFragment: 11,
						notation: 12,
					},
					Xb = function (F, z) {
						if ("object" !== typeof F || "function" !== typeof F.createPolicy)
							return null;
						var B = null;
						z &&
							z.hasAttribute("data-tt-policy-suffix") &&
							(B = z.getAttribute("data-tt-policy-suffix"));
						z = "dompurify" + (B ? "#" + B : "");
						try {
							return F.createPolicy(z, {
								createHTML: function (G) {
									return G;
								},
								createScriptURL: function (G) {
									return G;
								},
							});
						} catch (G) {
							return (
								console.warn(
									"TrustedTypes policy " + z + " could not be created.",
								),
								null
							);
						}
					},
					$a = function () {
						return {
							afterSanitizeAttributes: [],
							afterSanitizeElements: [],
							afterSanitizeShadowDOM: [],
							beforeSanitizeAttributes: [],
							beforeSanitizeElements: [],
							beforeSanitizeShadowDOM: [],
							uponSanitizeAttribute: [],
							uponSanitizeElement: [],
							uponSanitizeShadowNode: [],
						};
					};
				return m();
			});
		},
		function (g, l, k) {
			k.d(l, "a", function () {
				return a;
			});
			var f = k(2),
				b = k(7),
				n = k(10),
				d = k(5),
				m = (function () {
					function e(h) {
						var q = this;
						this.promise = h.then(function (v) {
							q.response = v;
							q.status = 200;
						});
					}
					e.prototype.addEventListener = function (h, q) {
						this.promise.then(q);
					};
					return e;
				})(),
				a = function (e, h, q, v) {
					if (Object(n.a)() && !q) {
						self.Module.instantiateWasm = function (D, p) {
							return Object(b.a)(
								"".concat(e, "Wasm.wasm"),
								D,
								h["Wasm.wasm"],
								v,
							).then(function (J) {
								p(J.instance);
							});
						};
						if (h.disableObjectURLBlobs) {
							importScripts("".concat(e, "Wasm.js"));
							return;
						}
						q = Object(f.b)(
							"".concat(e, "Wasm.js.mem"),
							h["Wasm.js.mem"],
							!1,
							!1,
						);
					} else {
						if (h.disableObjectURLBlobs) {
							importScripts(
								"".concat(
									(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + e,
									".js",
								),
							);
							return;
						}
						q = Object(f.b)(
							"".concat(
								(self.Module.asmjsPrefix ? self.Module.asmjsPrefix : "") + e,
								".js.mem",
							),
							h[".js.mem"],
							!1,
						);
						var t = Object(f.b)(
							"".concat(
								(self.Module.memoryInitializerPrefixURL
									? self.Module.memoryInitializerPrefixURL
									: "") + e,
								".mem",
							),
							h[".mem"],
							!0,
							!0,
						);
						self.Module.memoryInitializerRequest = new m(t);
					}
					q = new Blob([q], { type: "application/javascript" });
					importScripts(Object(d.a)(URL.createObjectURL(q)));
				};
		},
		function (g, l, k) {
			k.d(l, "a", function () {
				return D;
			});
			var f,
				b = "undefined" === typeof window ? self : window;
			g = (function () {
				var p = navigator.userAgent.toLowerCase();
				return (p =
					/(msie) ([\w.]+)/.exec(p) || /(trident)(?:.*? rv:([\w.]+)|)/.exec(p))
					? parseInt(p[2], 10)
					: p;
			})();
			var n = (function () {
				var p = b.navigator.userAgent.match(/OPR/),
					J = b.navigator.userAgent.match(/Maxthon/),
					X = b.navigator.userAgent.match(/Edge/);
				return b.navigator.userAgent.match(/Chrome\/(.*?) /) && !p && !J && !X;
			})();
			(function () {
				if (!n) return null;
				var p = b.navigator.userAgent.match(/Chrome\/([0-9]+)\./);
				return p ? parseInt(p[1], 10) : p;
			})();
			var d =
				!!navigator.userAgent.match(/Edge/i) ||
				(navigator.userAgent.match(/Edg\/(.*?)/) &&
					b.navigator.userAgent.match(/Chrome\/(.*?) /));
			(function () {
				if (!d) return null;
				var p = b.navigator.userAgent.match(/Edg\/([0-9]+)\./);
				return p ? parseInt(p[1], 10) : p;
			})();
			l =
				/iPad|iPhone|iPod/.test(b.navigator.platform) ||
				("MacIntel" === navigator.platform && 1 < navigator.maxTouchPoints) ||
				/iPad|iPhone|iPod/.test(b.navigator.userAgent);
			var m = (function () {
					var p = b.navigator.userAgent.match(
						/.*\/([0-9\.]+)\s(Safari|Mobile).*/i,
					);
					return p ? parseFloat(p[1]) : p;
				})(),
				a =
					/^((?!chrome|android).)*safari/i.test(b.navigator.userAgent) ||
					(/^((?!chrome|android).)*$/.test(b.navigator.userAgent) && l);
			a &&
				/^((?!chrome|android).)*safari/i.test(navigator.userAgent) &&
				parseInt(
					null === (f = navigator.userAgent.match(/Version\/(\d+)/)) ||
						void 0 === f
						? void 0
						: f[1],
					10,
				);
			var e = b.navigator.userAgent.match(/Firefox/);
			(function () {
				if (!e) return null;
				var p = b.navigator.userAgent.match(/Firefox\/([0-9]+)\./);
				return p ? parseInt(p[1], 10) : p;
			})();
			g || /Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);
			navigator.userAgent.match(/(iPad|iPhone|iPod)/i);
			b.navigator.userAgent.indexOf("Android");
			var h = /Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(
					b.navigator.userAgent,
				),
				q = b.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)
					? 14 <=
						parseInt(
							b.navigator.userAgent.match(
								/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i,
							)[3],
							10,
						)
					: !1,
				v = !(!self.WebAssembly || !self.WebAssembly.validate),
				t =
					-1 < b.navigator.userAgent.indexOf("Edge/16") ||
					-1 < b.navigator.userAgent.indexOf("MSAppHost"),
				D = function () {
					var p;
					if ((p = v && !t))
						(p = a && null !== m && 14 > m), (p = !(!q && (p || h)));
					return p;
				};
		},
		function (g, l, k) {
			g.exports = k(12);
		},
		function (g, l, k) {
			function f(n, d) {
				if (null == d || d > n.length) d = n.length;
				for (var m = 0, a = Array(d); m < d; m++) a[m] = n[m];
				return a;
			}
			k.r(l);
			g = k(9);
			var b = {};
			location.search
				.slice(1)
				.split("&")
				.forEach(function (n) {
					n = n.split("=");
					var d;
					var m = Array.isArray(n) ? n : void 0;
					if (!m)
						a: {
							var a =
								null == n
									? null
									: ("undefined" != typeof Symbol && n[Symbol.iterator]) ||
										n["@@iterator"];
							if (null != a) {
								var e,
									h,
									q = [],
									v = !0,
									t = !1;
								try {
									for (
										e = (a = a.call(n)).next;
										!(v = (d = e.call(a)).done) &&
										(q.push(d.value), 2 !== q.length);
										v = !0
									);
								} catch (p) {
									t = !0;
									var D = p;
								} finally {
									try {
										if (
											!v &&
											null != a["return"] &&
											((h = a["return"]()), Object(h) !== h)
										) {
											m = void 0;
											break a;
										}
									} finally {
										if (t) throw D;
									}
								}
								m = q;
							} else m = void 0;
						}
					if (!(d = m))
						a: {
							if (n) {
								if ("string" === typeof n) {
									d = f(n, 2);
									break a;
								}
								d = Object.prototype.toString.call(n).slice(8, -1);
								"Object" === d && n.constructor && (d = n.constructor.name);
								if ("Map" === d || "Set" === d) {
									d = Array.from(n);
									break a;
								}
								if (
									"Arguments" === d ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(d)
								) {
									d = f(n, 2);
									break a;
								}
							}
							d = void 0;
						}
					if (!(n = d))
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					b[n[0]] = n[1];
				});
			self.Module = {};
			self.basePath = b.externalPath;
			self.workerCallbackId = null;
			self.runtimeInitialized = !1;
			(function () {
				function n() {
					if (self.runtimeInitialized)
						try {
							Module._on_idle();
						} catch (q) {
							postMessage({
								action: "responseFromOnIdle",
								error: q.toString(),
							});
						}
					setTimeout(n, 200);
				}
				function d() {
					if (a && self.runtimeInitialized) {
						var q = a;
						a = null;
						q.forEach(function (v) {
							onmessage(v);
						});
					}
				}
				function m() {
					d();
					a && setTimeout(m, 100);
				}
				var a = null,
					e = null,
					h = 0;
				setTimeout(n, 200);
				onmessage = function (q) {
					if (self.runtimeInitialized) {
						d();
						var v = Module["_".concat(q.data.action)];
						if (!v)
							throw Error(
								"invalid worker function to call: ".concat(q.data.funcName),
							);
						var t = q.data.data;
						if (t) {
							t.byteLength || (t = new Uint8Array(t));
							if (!e || h < t.length)
								e && Module._free(e),
									(h = t.length),
									(e = Module._malloc(t.length));
							Module.HEAP8.set(t, e);
						}
						self.workerCallbackId = q.data.callbackId;
						try {
							t ? v(e, t.length) : v(0, 0);
						} catch (D) {
							postMessage({
								callbackId: self.workerCallbackId,
								data: { error: D.toString() },
							});
						}
					} else a || ((a = []), setTimeout(m, 100)), a.push(q);
				};
			})();
			Object(g.a)(
				"OfficeEditorWorker",
				{ "Wasm.wasm": 1e8, "Wasm.js.mem": 1e5, ".js.mem": 5e6, ".mem": 3e6 },
				!!navigator.userAgent.match(/Edge/i),
			);
		},
	]);
}).call(this || window);
