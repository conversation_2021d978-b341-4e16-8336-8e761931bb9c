{"action": {"addParagraph": "Add Paragraph", "add": "Add", "addSheet": "Add Sheet", "apply": "Apply", "applyAll": "Apply all", "calendar": "Calendar", "calibrate": "Calibrate", "cancel": "Cancel", "confirm": "Confirm", "currentPageIs": "Current page is", "clear": "Clear", "clearAll": "Clear all", "close": "Close", "undo": "Undo", "redo": "Redo", "comment": "Comment", "reply": "Add reply", "copy": "Copy", "cut": "Cut", "paste": "Paste", "pasteWithoutFormatting": "Paste without formatting", "delete": "Delete", "deleted": "Deleted", "group": "Group", "ungroup": "Ungroup", "download": "Download", "edit": "Edit", "collapse": "Collapse", "expand": "Expand", "extract": "Extract", "extractPage": "Extract Page", "enterFullscreen": "Enter Full Screen", "exitFullscreen": "Exit Full Screen", "fit": "Fit", "fitToPage": "Fit to page", "fitToWidth": "Fit to width", "more": "More", "openFile": "Open file", "showMoreFiles": "Show More Files", "page": "Page", "of": "of", "pagePrev": "Previous page", "pageNext": "Next page", "pageSet": "Set page", "print": "Print", "proceed": "Proceed", "name": "Name", "rename": "<PERSON><PERSON>", "remove": "Remove", "ok": "OK", "rotate": "Page Orientation", "rotate3D": "Rotate", "rotateClockwise": "Rotate Clockwise", "rotateCounterClockwise": "Rotate Counterclockwise", "rotatedClockwise": "rotated clockwise", "rotatedCounterClockwise": "rotated counterclockwise", "rotationIs": "current page rotation is ", "movedToBottomOfDocument": "moved to bottom of document", "movedToTopofDocument": "moved to top of document", "extracted": "extracted", "save": "Save", "post": "Post", "create": "Create", "update": "Update", "showMoreResults": "Show more results", "sign": "Sign", "style": "Style", "submit": "Submit", "zoom": "Zoom", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "zoomSet": "Set zoom", "zoomChanged": "Current zoom is", "zoomControls": "Zoom Controls", "draw": "Draw", "type": "Type", "upload": "Upload", "link": "Link", "unlink": "Delete Link", "fileAttachmentDownload": "Download attached file", "prevResult": "Previous result", "nextResult": "Next result", "prev": "Previous", "next": "Next", "startFormEditing": "Start Form Editing", "exitFormEditing": "Exit Form Editing Mode", "exit": "Exit", "addOption": "Add Option", "formFieldEdit": "Edit Form Field", "formFieldEditMode": "Edit Form Fields", "contentEditMode": "Edit Content", "viewShortCutKeysFor3D": "View Shortcut Keys", "markAllRead": "Mark all as read", "pageInsertion": "Page Insertion", "insertPage": "Insert Page", "insert": "Insert", "pageManipulation": "Page Manipulation", "replace": "Replace", "replacePage": "Replace Page", "modal": "modal", "isOpen": "is open", "setDestination": "Set Destination", "showLess": "show less", "showMore": "...more", "chooseFile": "Choose a file", "changeDate": "Change Date", "browse": "Browse Files", "selectYourOption": "Select your option", "open": "Open", "deselectAll": "Deselect All", "select": "Select", "moveToTop": "Move to Top", "moveToBottom": "Move to Bottom", "movePageToTop": "Move Page to Top", "movePageToBottom": "Move Page to Bottom", "moveUp": "Move Up", "moveDown": "Move Down", "moveLeft": "Move Left", "moveRight": "Move Right", "backToMenu": "Back to Menu", "redactPages": "Redact Pages", "playAudio": "Play audio", "pauseAudio": "Pause audio", "selectAll": "Select all", "unselect": "Unselect", "addMark": "Add Mark", "viewFile": "View file", "multiReplyAnnotations": "Reply to selected annotations ({{count}})", "comparePages": "Compare pages", "startComparison": "Start Comparison", "showComparison": "Show Comparison", "highlightChanges": "Highlight Changes", "back": "Back", "clearSignature": "Clear signature", "clearInitial": "Clear initial", "readOnlySignature": "Read-only signature cannot be deleted", "newDocument": "New Document", "sideBySideView": "Side By Side View", "pageNumberInput": "Page number input", "addNewColor": "Add New Color", "deleteColor": "Delete Selected Color", "copySelectedColor": "Copy Selected Color", "showMoreColors": "Show More Colors", "showLessColors": "Show Less Colors", "fromCustomColorPicker": "from Custom Color Picker", "newSpreadsheetDocument": "New Spreadsheet", "resetDefault": "Reset to default"}, "annotation": {"areaMeasurement": "Area", "arc": "Arc", "arcMeasurement": "Arc Measurement", "arrow": "Arrow", "callout": "Callout", "crop": "Crop Page", "caret": "<PERSON><PERSON>", "dateFreeText": "Calendar", "formFillCheckmark": "Tick", "formFillCross": "Cross", "distanceMeasurement": "Distance", "rectangularAreaMeasurement": "Rectangular Area", "ellipseMeasurement": "Ellipse Area", "countMeasurement": "Count measurement", "ellipse": "Ellipse", "eraser": "Eraser", "fileattachment": "File Attachment", "freehand": "Free Hand", "freeHandHighlight": "Free Hand Highlight", "freetext": "Free Text", "markInsertText": "Insert Text", "markReplaceText": "Replace Text", "highlight": "Highlight", "image": "Image", "line": "Line", "perimeterMeasurement": "Perimeter", "polygon": "Polygon", "polygonCloud": "Cloud", "polyline": "Polyline", "rectangle": "Rectangle", "redact": "Redact", "formFillDot": "Dot", "signature": "Signature", "snipping": "Snipping Tool", "squiggly": "S<PERSON>ggly", "stamp": "Stamp", "stickyNote": "Note", "strikeout": "Strikeout", "underline": "Underline", "custom": "Custom", "rubberStamp": "<PERSON><PERSON><PERSON>amp", "note": "Note", "textField": "Text Field", "signatureFormField": "Signature Field", "checkBoxFormField": "Checkbox Field", "radioButtonFormField": "Radio Button Field", "listBoxFormField": "List Box Field", "comboBoxFormField": "Combo Box Field", "link": "Link", "other": "Other", "3D": "3D", "sound": "Sound", "changeView": "Change View", "newImage": "New Image", "defaultCustomStampTitle": "Custom Stamp"}, "rubberStamp": {"Approved": "Approved", "AsIs": "As Is", "Completed": "Completed", "Confidential": "Confidential", "Departmental": "Departmental", "Draft": "Draft", "Experimental": "Experimental", "Expired": "Expired", "Final": "Final", "ForComment": "For Comment", "ForPublicRelease": "For Public Release", "InformationOnly": "Information Only", "NotApproved": "Not Approved", "NotForPublicRelease": "Not For Public Release", "PreliminaryResults": "Preliminary Results", "Sold": "Sold", "TopSecret": "Top Secret", "Void": "Void", "SHSignHere": "Sign Here", "SHWitness": "Witness", "SHInitialHere": "Initial Here", "SHAccepted": "Accepted", "SBRejected": "Rejected"}, "component": {"attachmentPanel": "Attachments", "leftPanel": "Left Panel", "toolsHeader": "Tools", "searchOverlay": "Search", "searchPanel": "Search", "menuOverlay": "<PERSON><PERSON>", "notesPanel": "Comments", "indexPanel": "Index Panel", "outlinePanel": "Outline", "outlinesPanel": "Outlines", "newOutlineTitle": "New Outline Title", "outlineTitle": "Outline Title", "destination": "Destination", "bookmarkPanel": "Bookmark", "bookmarksPanel": "Bookmarks", "bookmarkTitle": "Bookmark Title", "bookmarkPage": "Page", "signaturePanel": "Signatures", "layersPanel": "Layers", "thumbnailsPanel": "Thumbnails", "toolsButton": "Tools", "redaction": "Redaction", "viewControls": "View Controls", "pageControls": "Page Controls", "calibration": "Calibration", "zoomOverlay": "Zoom <PERSON>", "textPopup": "Text Popup", "createStampButton": "Create New Stamp", "filter": "Filter", "multiSelectButton": "Multi Select", "pageReplaceModalTitle": "Replace Page", "files": "Files", "file": "File", "editText": "Edit Text", "redactionPanel": "Redaction Panel", "tabLabel": "Tab", "noteGroupSection": {"open": "View All Annotations", "close": "Close All Annotations"}, "comparePanel": "Compare Panel", "watermarkPanel": "Watermark Panel", "mainMenu": "Main Menu"}, "message": {"showMore": "Show More", "showLess": "Show Less", "toolsOverlayNoPresets": "No Presets", "badDocument": "Failed to load document. The document is either corrupt or not valid.", "customPrintPlaceholder": "e.g. 3, 4-10", "encryptedAttemptsExceeded": "Failed to load encrypted document. Too many attempts.", "encryptedUserCancelled": "Failed to load encrypted document. Password entry cancelled.", "enterPassword": "This document is password protected. Please enter a password", "incorrectPassword": "Incorrect password, attempts left: {{ remainingAttempts }}", "noAnnotations": "Start making annotations to leave a comment.", "noAnnotationsReadOnly": "This document has no annotations.", "noAnnotationsFilter": "Start making annotations and filters will appear here.", "noBookmarks": "No bookmarks available", "noOutlines": "No outlines available", "noAttachments": "This document has no attachments.", "noResults": "No results found.", "numResultsFound": "results found", "loadError": "Error Loading Document", "notSupported": "That file type is not supported.", "passwordRequired": "Password required", "enterPasswordPlaceholder": "Enter password", "preparingToPrint": "Preparing to print...", "annotationReplyCount": "{{count}} Reply", "annotationReplyCount_plural": "{{count}} Replies", "printTotalPageCount": "Total: {{count}} page", "printTotalPageCount_plural": "Total: {{count}} pages", "processing": "Processing...", "searching": "Searching...", "searchCommentsPlaceholder": "Search comments", "searchDocumentPlaceholder": "Search document", "clearSearchResults": "Clear search results", "searchResultsCleared": "Search results cleared", "searchSettingsPlaceholder": "Search settings", "searchSuggestionsPlaceholder": "Search suggestions", "signHere": "Sign here", "insertTextHere": "Insert text here", "imageSignatureAcceptedFileTypes": "Only {{acceptedFileTypes}} are accepted", "signatureRequired": "A signature and initial is required to continue", "enterMeasurement": "Enter measurement between the two points", "errorEnterMeasurement": "The number you have entered is invalid, you can enter values like 7.5 or 7 1/2", "linkURLorPage": "Link URL or a Page", "warning": "Warning", "svgMalicious": "SVG script ignored for security", "doNotShowAgain": "Do not show me this again", "doNotAskAgain": "Do not ask again", "enterReplacementText": "Enter the text you want to replace", "sort": "Sort", "sortBy": "Sort by", "emptyCustomStampInput": "Stamp text cannot be empty", "unpostedComment": "Unposted Comment", "lockedLayer": "Layer is locked", "layerVisibililtyNoChange": "Layer visibililty can't be changed", "noLayers": "This document has no layers.", "noSignatureFields": "This document has no signature fields.", "untitled": "Untitled", "selectHowToLoadFile": "Select how to load your document", "openFileByUrl": "Open file by URL:", "enterUrlHere": "Enter URL here", "openLocalFile": "Open local file:", "selectFile": "Select file", "selectPageToReplace": "Select the pages in the document you would like to replace with.", "embeddedFiles": "Embedded Files", "pageNum": "Page", "viewBookmark": "View Bookmark on Page", "error": "Error", "errorPageNumber": "Invalid page number. Limit is ", "errorBlankPageNumber": "Please specify a page number", "errorLoadingDocument": "There was a problem reading this document and some pages may not be displayed. This indicates the document may be corrupted. The total page count is {{totalPageCount}} and the number pages being displayed is {{displayedPageCount}}.", "noRevisions": "This document has no revisions."}, "option": {"type": {"caret": "<PERSON><PERSON>", "custom": "Custom", "ellipse": "Ellipse", "fileattachment": "File Attachment", "freehand": "Free Hand", "callout": "Callout", "freetext": "Free Text", "line": "Line", "polygon": "Polygon", "polyline": "Polyline", "rectangle": "Rectangle", "redact": "Redact", "signature": "Signature", "stamp": "Stamp", "stickyNote": "<PERSON><PERSON>", "highlight": "Highlight", "strikeout": "Strikeout", "underline": "Underline", "squiggly": "S<PERSON>ggly", "3D": "3D", "other": "Other", "initials": "Initials", "saved": "Saved"}, "notesOrder": {"dropdownLabel": "Sort Order List", "position": "Position", "time": "Time", "status": "Status", "author": "Author", "type": "Type", "color": "Color", "createdDate": "Created Date", "modifiedDate": "Modified Date"}, "toolbarGroup": {"dropdownLabel": "Toolbar Groups", "flyoutLabel": "Ribbon", "toolbarGroup-View": "View", "toolbarGroup-Annotate": "Annotate", "toolbarGroup-Shapes": "<PERSON><PERSON><PERSON>", "toolbarGroup-Insert": "Insert", "toolbarGroup-Measure": "Measure", "toolbarGroup-Edit": "Edit", "toolbarGroup-EditText": "Edit Text", "toolbarGroup-FillAndSign": "Fill and Sign", "toolbarGroup-Forms": "Forms", "toolbarGroup-Redact": "Redact", "toolbarGroup-oe-Home": "Home", "toolbarGroup-oe-Insert": "Insert", "toolbarGroup-oe-Layout": "Layout", "toolbarGroup-oe-Review": "Review"}, "annotationColor": {"StrokeColor": "Stroke", "FillColor": "Fill", "TextColor": "Text"}, "colorPalette": {"colorLabel": "Color"}, "colorPalettePicker": {"addColor": "Add new color", "selectColor": "Select color"}, "displayMode": {"layout": "Page Layout", "pageTransition": "Page Transition"}, "documentControls": {"selectTooltip": "Select multiple pages", "closeTooltip": "Close multiselect"}, "bookmarkOutlineControls": {"edit": "Edit", "done": "Done", "reorder": "Reorder"}, "layout": {"cover": "Cover Facing Page", "double": "Double Page", "single": "Single Page"}, "mathSymbols": "Math symbols", "notesPanel": {"separator": {"today": "Today", "yesterday": "Yesterday", "unknown": "Unknown"}, "noteContent": {"noName": "(no name)", "noDate": "(no date)"}, "toggleMultiSelect": "Toggle Multi Select for Annotation"}, "pageTransition": {"continuous": "Continuous Page", "default": "Page by Page", "reader": "Reader"}, "print": {"all": "All", "current": "Current Page", "pages": "Pages to print", "specifyPages": "Specify Pages", "view": "Current View", "pageQuality": "Print Quality", "qualityNormal": "Normal", "qualityHigh": "High", "includeAnnotations": "Include annotations", "includeComments": "Include comments", "printSettings": "Print Settings", "printGrayscale": "Print Grayscale", "printCurrentDisabled": "Current view is only available when viewing a single page."}, "printInfo": {"author": "Author", "subject": "Subject", "date": "Date"}, "redaction": {"markForRedaction": "Mark for redaction"}, "searchPanel": {"caseSensitive": "Case sensitive", "wholeWordOnly": "Whole word", "wildcard": "Wildcard", "replace": "Replace", "replaceAll": "Replace All", "replaceText": "Replace Text", "confirmMessageReplaceAll": "Are you sure you want to replace all text?", "confirmMessageReplaceOne": "Are you sure you want to replace this text?", "moreOptions": "More options", "lessOptions": "Less options", "confirm": "Confirm"}, "toolsOverlay": {"currentStamp": "Current Stamp", "currentSignature": "Current Signature", "signatureAltText": "Signature"}, "stampOverlay": {"addStamp": "Add New Stamp"}, "signatureOverlay": {"addSignatureOrInitials": "New Signature/Initials", "addSignature": "New Signature"}, "signatureModal": {"modalName": "Create New Signature", "dragAndDrop": "Drag & Drop your image here", "or": "Or", "pickImage": "Choose a signature", "selectImage": "Select your image here", "typeSignature": "Type Signature*", "typeInitial": "Type Initials*", "drawSignature": "Draw Signature*", "drawInitial": "Draw Initials*", "imageSignature": "Image Signature", "imageInitial": "Image Initials", "pickInitialsFile": "Choose Initials", "noSignatures": "There are currently no saved signatures.", "fontStyle": "Font style", "textSignature": {"dropdownLabel": "Font Family"}}, "pageReplacementModal": {"yourFiles": "Your Files", "chooseFile": "Choose a file", "localFile": "Upload", "pageReplaceInputLabel": "Replace Page", "pageReplaceInputFromSource": "With <PERSON>(s)", "warning": {"title": "Exit replace page?", "message": "Exiting will cancel all of the selections you have made so far. Are you sure you still want to exit?"}}, "filterAnnotModal": {"color": "Color", "includeReplies": "Include replies", "filters": "Filters", "user": "User", "type": "Type", "status": "Status", "filterSettings": "Filter <PERSON>s", "filterDocument": "Filter document and comments panel"}, "state": {"accepted": "Accepted", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "set": "Set status:", "setBy": "set by", "none": "None", "marked": "Marked", "unmarked": "Unmarked"}, "measurementOverlay": {"scale": "Scale Ratio", "angle": "<PERSON><PERSON>", "distance": "Distance", "perimeter": "Perimeter", "area": "Area", "distanceMeasurement": "Distance Measurement", "perimeterMeasurement": "Perimeter Measurement", "arcMeasurement": "Arc Measurement", "areaMeasurement": "Area Measurement", "countMeasurement": "Count measurement", "radius": "<PERSON><PERSON>", "count": "Count", "length": "Length", "xAxis": "X-Axis", "yAxis": "Y-Axis"}, "freeTextOption": {"autoSizeFont": "Scale font size dynamically"}, "measurementOption": {"scale": "Scale", "selectScale": "Select Scale", "selectScaleDropdown": "Select Scale Dropdown"}, "measurement": {"scaleModal": {"calibrate": "Calibrate", "custom": "Custom", "fractionalUnits": "Fractional Units", "precision": "Precision", "preset": "Preset", "paperUnits": "Paper Units", "displayUnits": "Display Units", "fractionUnitsTooltip": "Fraction units only apply to in and ft-in", "incorrectSyntax": "Incorrect syntax", "units": "Units"}, "scaleOverlay": {"addNewScale": "Add New Scale", "selectTwoPoints": "Select two points of a known dimension to calibrate", "inputKnowDimension": "Input known dimension and units to calibrate", "multipleScales": "Multiple Scales"}, "deleteScaleModal": {"deleteScale": "Delete Scale", "scaleIsOn-delete-info": "This scale is currently in use on", "page-delete-info": "page", "appliedTo-delete-info": "and applied to", "measurement": "measurement", "measurements": "measurements", "deletionIs": "Deletion is", "irreversible": "irreversible", "willDeleteMeasurement": "and will delete the associated measurements.", "confirmDelete": "Are you sure you want to delete this scale?", "thisCantBeUndone": " This can't be undone.", "ifChangeScale": "If you change the scale for the selected measurement or tool, scale ", "notUsedWillDelete": " will no longer be used by any measurements or tools and will be deleted. Deletion is irreversible.", "ifToContinue": "Are you sure you'd like to continue?"}}, "contentEdit": {"deletionModal": {"title": "Delete Content", "message": "Are you sure you want to delete the selected content? This can't be undone."}, "digitalSign": {"title": "Warning Sealed Document", "message": "This document has been signed and cannot be altered or changed."}}, "stylePopup": {"textStyle": "Text Style", "colors": "Colors", "invalidFontSize": "Font size must be in the following range", "labelText": "Label Text", "labelTextPlaceholder": "Add label text"}, "styleOption": {"style": "Style", "solid": "Solid", "cloudy": "Cloudy"}, "slider": {"opacity": "Opacity", "thickness": "Stroke", "text": "Text Size"}, "shared": {"page": "Page", "precision": "Precision", "enableSnapping": "Enable snapping for measurement tools"}, "watermark": {"title": "Watermark", "addWatermark": "Add Watermark", "size": "Size", "location": "Choose a location to edit watermarks", "text": "Text", "style": "Style", "resetAllSettings": "Reset All Settings", "font": "Font", "addNew": "Add New", "locations": {"center": "Center", "topLeft": "Top Left", "topRight": "Top Right", "topCenter": "Top Center", "bottomLeft": "Bottom Left", "bottomRight": "Bottom Right", "bottomCenter": "Bottom Center"}}, "thumbnailPanel": {"delete": "Delete Page", "rotateClockwise": "Rotate Clockwise", "rotateCounterClockwise": "Rotate Counterclockwise", "rotatePageClockwise": "Rotate Page Clockwise", "rotatePageCounterClockwise": "Rotate Page Counterclockwise", "moreOptions": "More Options", "moreOptionsMenu": "More Thumbnails Options Menu", "enterPageNumbers": "Enter page numbers to select", "multiSelectPages": "Multi-Select Pages", "multiSelectPagesExample": "e.g. 1, 3, 5-10"}, "thumbnailsControlOverlay": {"move": "Move pages"}, "richText": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "strikeout": "Strikeout", "alignLeft": "Text align left", "alignRight": "Text align right", "alignCenter": "Text align center", "justifyCenter": "Text justify center", "alignTop": "Align top", "alignMiddle": "Align middle", "alignBottom": "Align bottom"}, "customStampModal": {"modalName": "Create New Stamp", "stampText": "Stamp text", "timestampText": "Timestamp text", "Username": "Username", "Date": "Date", "Time": "Time", "fontStyle": "Font style", "dateFormat": "Date format", "month": "Month", "day": "Day", "year": "Year", "hour": "Hour", "minute": "Minute", "second": "Second", "textColor": "Text color", "backgroundColor": "Background color", "dateToolTipLabel": "More info about date format", "previewCustomStamp": "Preview of"}, "pageRedactModal": {"addMark": "Add Mark", "pageSelection": "Page Selection", "current": "Current Page", "specify": "Specify Pages", "odd": "Odd Pages only", "even": "Even Pages only"}, "lineStyleOptions": {"title": "Style"}, "settings": {"settings": "Settings", "searchSettings": "Search Settings", "general": "General", "language": "Language", "theme": "Theme", "darkMode": "Dark mode", "lightMode": "Light mode", "advancedSetting": "Advanced Setting", "viewing": "Viewing", "disableFadePageNavigationComponent": "Disable Fade Page Navigation Component", "disableFadePageNavigationComponentDesc": "Always keep the Page Navigation Component on screen. Default behaviour is to fade it after certain period of inactivity.", "enabledFormFieldHighlighting": "Enable Form Field Highlights", "enabledFormFieldHighlightingDesc": "Enable highlighting of form fields in PDF documents. Enabled by default.", "disableNativeScrolling": "Disable Native Scrolling", "disableNativeScrollingDesc": "Disable native mobile device scrolling behavior if it had previously been enabled. Note that native mobile device scrolling behavior is off by default.", "annotations": "Annotations", "disableToolDefaultStyleUpdateFromAnnotationPopup": "Disable Tool Default Style Update From Annotation Popup", "disableToolDefaultStyleUpdateFromAnnotationPopupDesc": "Disables syncing of annotation style updates to the associated tool that created the annotation. So if an annotation's style is changed the tool default styles will not be updated.", "notesPanel": "Notes Panel", "disableNoteSubmissionWithEnter": "Disable Note Submission With Enter", "disableNoteSubmissionWithEnterDesc": "Disable the ability to submit notes by only pressing En<PERSON> if it had previously been enabled. This will revert note submission to the default which is Ctrl/Cmd + Enter.", "disableAutoExpandCommentThread": "Disable Auto Expand Comment Thread", "disableAutoExpandCommentThreadDesc": "Disables the automatic expansion of all the comments threads in the Notes Panel.", "disableReplyCollapse": "Disable Rep<PERSON>se", "disableReplyCollapseDesc": "Disables the collapsing of the replies in the Notes Panel.", "disableTextCollapse": "Disable Text Collapse", "disableTextCollapseDesc": "Disables the collapsing of the annotation's text in the Notes Panel.", "search": "Search", "disableClearSearchOnPanelClose": "Disable Clear Search On Panel Close", "disableClearSearchOnPanelCloseDesc": "Disable clearing search results when user closes search panel. When disabled, search results are kept even if user closes and reopens search panel. Note, mobile devices never clear search results even if this setting is enabled. This is because the panel needs to be closed to view the search results on the document.", "pageManipulation": "Page Manipulation", "disablePageDeletionConfirmationModal": "Disable Page Deletion Confirmation Modal", "disablePageDeletionConfirmationModalDesc": "Disable the confirmation modal when deleting a page from the thumbnail view", "disableMultiselect": "Disable Multiselect", "disableMultiselectDesc": "Disable multi select in the left thumbnail panel", "miscellaneous": "Miscellaneous", "keyboardShortcut": "Keyboard Shortcut", "command": "Command", "description": "Description", "action": "Action", "rotateDocumentClockwise": "Rotate the document clockwise", "rotateDocumentCounterclockwise": "Rotate the document counterclockwise", "copyText": "Copy selected text or annotations", "pasteText": "Paste text or annotations", "undoChange": "Undo an annotation change", "redoChange": "Redo an annotation change", "openFile": "Open the file picker", "openSearch": "Open the search overlay", "zoomOptions": "Zoom Options", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "setHeaderFocus": "Sets the focus to the header", "fitScreenWidth": "Fit the document to the screen width in a small screen(< 640px), otherwise fit it to its original size", "print": "Print", "bookmarkOpenPanel": "Quickly bookmark a page and open the bookmark panel", "goToPreviousPage": "Go to the previous page", "goToNextPage": "Go to the next page", "goToPreviousPageArrowUp": "Go to the previous page in single layout mode (ArrowUp)", "goToNextPageArrowDown": "Go to the next page in single layout mode (ArrowDown)", "holdSwitchPan": "Hold to switch to Pan mode and release to return to previous tool", "selectAnnotationEdit": "Select the AnnotationEdit tool", "selectPan": "Select the Pan tool", "selectCreateArrowTool": "Select the AnnotationCreateArrow tool", "selectCreateCalloutTool": "Select the AnnotationCreateCallout tool", "selectEraserTool": "Select the AnnotationEraser tool", "selectCreateFreeHandTool": "Select the AnnotationCreateFreeHand tool", "selectCreateStampTool": "Select the AnnotationCreateStamp tool", "selectCreateLineTool": "Select the AnnotationCreateLine tool", "selectCreateStickyTool": "Select the AnnotationCreateSticky tool", "selectCreateEllipseTool": "Select the AnnotationCreateEllipse tool", "selectCreateRectangleTool": "Select the AnnotationCreateRectangle tool", "selectCreateRubberStampTool": "Select the AnnotationCreateRubberStamp tool", "selectCreateFreeTextTool": "Select the AnnotationCreateFreeText tool", "openSignatureModal": "Open the signature modal or the overlay", "selectCreateTextSquigglyTool": "Select the AnnotationCreateTextSquiggly tool", "selectCreateTextHighlightTool": "Select the AnnotationCreateTextHighlight tool", "selectCreateTextStrikeoutTool": "Select the AnnotationCreateTextStrikeout tool", "selectCreateTextUnderlineTool": "Select the AnnotationCreateTextUnderline tool", "editKeyboardShorcut": "Edit Keyboard Shorcut", "setShortcut": "Set Shortcut", "editShortcut": "Edit Shortcut", "shortcutAlreadyExists": "The keyboard shortcut above already exists.", "close": "Close tooltip"}, "cellBorderStyle": {"none": "None", "solid": "Solid", "dashed": "Dashed", "dotted": "Dotted"}}, "warning": {"deletePage": {"deleteTitle": "Delete Page", "deleteMessage": "Are you sure you want to delete the selected page(s). This can't be undone.", "deleteLastPageMessage": "You cannot delete all pages in the document."}, "extractPage": {"title": "Extract Page", "message": "Are you sure you want to extract the selected page(s)?", "confirmBtn": "Extract", "secondaryBtn": "Extract and Delete"}, "redaction": {"applyTile": "Apply Redaction", "applyMessage": "This action will permanently remove all items selected for redaction. It cannot be undone."}, "deleteBookmark": {"title": "Delete Bookmark?", "message": "Are you sure you want to delete these bookmarks? You cannot undo this action."}, "deleteOutline": {"title": "Delete Outline?", "message": "Are you sure you want to delete these outlines?\n\nDeleting an outline that has nested outlines will result in the whole inner structure being deleted and, if needed, will have to be recreated."}, "selectPage": {"selectTitle": "No Pages Selected", "selectMessage": "Please select pages and try again."}, "colorPicker": {"deleteTitle": "Delete custom color", "deleteMessage": "Delete the selected custom color? It will be removed from your color palette."}, "colorPalettePicker": {"deleteTitle": "Delete custom color"}, "multiDeleteAnnotation": {"title": "Delete Annotations?", "message": "Deleting will remove all comments, replies, and grouping and cannot be undone. \n\n Are you sure you want to delete these annotations?"}, "closeFile": {"title": "Close without downloading?", "message": "There are changes made to this document, are you sure you want to close it without downloading your work? You can't undo this action.", "rejectDownloadButton": "Close without download"}, "connectToURL": {"title": "Security Warning", "message": "This document is trying to connect to: \n\n{{- uri}}\n\n If you trust this document, click Confirm to open it."}, "sheetTabRenameIssueOne": {"title": "Edit Warning", "message": "This name already exists. Please enter another name."}, "sheetTabRenameIssueTwo": {"title": "Edit Warning", "message": "The sheet name cannot be empty."}, "sheetTabDeleteMessage": {"title": "Edit Warning", "message": "Are you sure you want to delete this sheet?"}}, "shortcut": {"arrow": "(A)", "callout": "(C)", "copy": "(Ctrl C)", "delete": "(Del)", "ellipse": "(O)", "eraser": "(E)", "freehand": "(F)", "freetext": "(T)", "highlight": "(H)", "line": "(L)", "pan": "(P)", "rectangle": "(R)", "rotateClockwise": "(Ctrl Shift +)", "rotateCounterClockwise": "(Ctrl Shift -)", "select": "(Esc)", "signature": "(S)", "squiggly": "(G)", "image": "(I)", "redo": "(Ctrl Shift Z)", "redo_windows": "(Ctrl Y)", "undo": "(Ctrl Z)", "stickyNote": "(N)", "strikeout": "(K)", "underline": "(U)", "zoomIn": "(Ctrl +)", "zoomOut": "(Ctrl -)", "richText": {"bold": "(Ctrl B)", "italic": "(Ctrl I)", "underline": "(Ctrl U)", "strikeout": "(Ctrl K)"}, "rotate3D": "Shift + Drag", "zoom3D": "Shift + Scroll"}, "tool": {"pan": "Pan", "select": "Select", "selectAnOption": "Select an option", "Marquee": "Marquee Zoom", "Link": "Link URL or Page", "Standard": "Standard", "Custom": "Custom"}, "link": {"url": "URL", "page": "Page", "enterurl": "Enter URL you would like to link to", "enterUrlAlt": "Enter URL", "insertLink": "Insert Link", "insertLinkOrPage": "Insert Link or Page", "enterpage": "Enter the page number you would like to link to", "urlLink": "URL link"}, "Model3D": {"add3D": "Add 3D object", "enterurl": "Enter the URL of the 3D object in glTF format", "enterurlOrLocalFile": "Enter the URL or upload a 3D object in glTF format", "formatError": "Only glTF (.glb) format is supported"}, "OpenFile": {"enterUrlOrChooseFile": "Enter a URL or choose a file to load into WebViewer", "enterUrl": "Enter file URL", "extension": "File Extension", "existingFile": "File is already open", "addTab": "Add <PERSON>", "newTab": "New Tab"}, "datePicker": {"previousMonth": "Previous Month", "nextMonth": "Next Month", "months": ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"], "monthsShort": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"], "weekdays": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"], "weekdaysShort": ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "<PERSON>hu", "<PERSON><PERSON>", "Sat"], "today": "Today", "invalidDateTime": "Invalid Date/Time: Input should match format"}, "formField": {"indexPanel": {"formFieldList": "Form Field List", "notFields": "This document has no form fields"}, "formFieldPopup": {"fieldName": "Field Name", "fieldValue": "Default Value", "readOnly": "Read Only", "multiSelect": "Multi Select", "required": "Required", "multiLine": "Multiline", "apply": "Apply", "cancel": "Cancel", "flags": "Field Flags", "fieldSize": "Field Size", "options": "Options", "properties": "Properties", "radioGroups": "Radio buttons with the same Field Name will belong in the same grouping.", "nameRequired": "Field name is required", "fieldIndicator": "Field Indicators", "documentFieldIndicator": "Document Field Indicators", "includeFieldIndicator": "Include Field Indicator", "indicatorPlaceHolders": {"TextFormField": "Insert Text Here", "CheckBoxFormField": "Check", "RadioButtonFormField": "Fill", "ListBoxFormField": "Select", "ComboBoxFormField": "Select", "SignatureFormField": {"fullSignature": "Sign Here", "initialsSignature": "Initial Here"}}, "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "invalidField": {"duplicate": "Field Name already exists", "empty": "Field Name cannot be empty"}}, "formFieldPanel": {"SignatureFormField": "Signature Field Annotation", "CheckBoxFormField": "Checkbox Field Annotation", "RadioButtonFormField": "Radio Button Field Annotation", "ListBoxFormField": "List Box Field Annotation", "ComboBoxFormField": "Combo Box Field Annotation", "TextFormField": "Text Field Annotation"}, "apply": "Apply Fields", "type": "Field Type", "types": {"text": "Text", "signature": "Signature", "checkbox": "Check box", "radio": "Radio button", "listbox": "List box", "combobox": "Combo box", "button": "<PERSON><PERSON>"}}, "alignmentPopup": {"alignment": "Align", "alignLeft": "Align Left", "alignHorizontalCenter": "Align Horizontal Center", "alignVerticalCenter": "Align Vertical Center", "alignRight": "Align Right", "alignTop": "Align Top", "alignBottom": "Align Bottom", "distribute": "Distribute", "distributeHorizontal": "Distribute Horizontally", "distributeVertical": "Distribute Vertically"}, "digitalSignatureModal": {"certification": "certification", "Certification": "Certification", "signature": "signature", "Signature": "Signature", "valid": "valid", "invalid": "invalid", "unknown": "unknown", "title": "{{type}} Properties", "header": {"documentIntegrity": "Document Integrity", "identitiesTrust": "Identities & Trust", "generalErrors": "General Errors", "digestStatus": "Digest Status"}, "documentPermission": {"noChangesAllowed": "The {{editor}} has specified that no changes are allowed for this document", "formfillingSigningAllowed": "The {{editor}} has specified that Form Fill-in and Signing are allowed for this document. No other changes are permitted.", "annotatingFormfillingSigningAllowed": "The {{editor}} has specified that Form Fill-in, Signing and Commenting are allowed for this document. No other changes are permitted.", "unrestricted": "The {{editor}} has specified that there are no restrictions for this document."}, "digestAlgorithm": {"preamble": "The digest algorithm that was used to sign the signature: ", "unknown": "The digest algorithm that was used to sign the signature is unknown."}, "trustVerification": {"none": "No detailed trust verification result available.", "current": "Trust verification attempted with respect to current time", "signing": "Trust verification attempted with respect to signing time: {{trustVerificationTime}}", "timestamp": "Trust verification attempted with respect to secure embedded timestamp: {{trustVerificationTime}}"}, "signerIdentity": {"preamble": "The signer's identity is ", "valid": "valid.", "unknown": "unknown."}, "summaryBox": {"summary": "Digital {{type}} is {{status}}", "signedBy": ", signed by {{name}}"}}, "digitalSignatureVerification": {"certifier": "certifier", "certified": "certified", "Certified": "Certified", "Certification": "Certification", "signer": "signer", "signed": "signed", "Signed": "Signed", "Signature": "Signature", "by": "by", "on": "on", "disallowedChange": "Disallowed Change: {{type}}, objnum: {{objnum}}", "unsignedSignatureField": "Unsigned signature field: {{fieldName}}", "signatureProperties": "Signature Properties", "trustVerification": {"current": "Verification time used was the current time", "signing": "Verification time is from the clock on the signer's computer", "timestamp": "Verification time is from the secure timestamp embedded in the document", "verifiedTrust": "Trust verification result: Verified", "noTrustVerification": "No detailed trust verification result available."}, "permissionStatus": {"noPermissionsStatus": "No permissions status to report.", "permissionsVerificationDisabled": "Permissions verification has been disabled.", "hasAllowedChanges": "The document has changes that are allowed by the signature's permissions settings.", "invalidatedByDisallowedChanges": "The document has changes that are disallowed by the signature's permissions settings.", "unmodified": "The document has not been modified since it was", "unsupportedPermissionsFeatures": "Encountered unsupported permissions features."}, "trustStatus": {"trustVerified": "Established trust in {{verificationType}} successfully.", "untrusted": "Trust could not be established.", "trustVerificationDisabled": "Trust verification has been disabled.", "noTrustStatus": "No trust status to report."}, "digestStatus": {"digestInvalid": "The digest is incorrect.", "digestVerified": "The digest is correct.", "digestVerificationDisabled": "Digest verification has been disabled.", "weakDigestAlgorithmButDigestVerifiable": "The digest is correct, but the digest algorithm is weak and not secure.", "noDigestStatus": "No digest status to report.", "unsupportedEncoding": "No installed Signature<PERSON><PERSON><PERSON> was able to recognize the signature's encoding", "documentHasBeenAltered": "Document has been altered or corrupted since it was signed."}, "documentStatus": {"noError": "No general error to report.", "corruptFile": "SignatureHandler reported file corruption.", "unsigned": "The signature has not yet been cryptographically signed.", "badByteRanges": "SignatureHandler reports corruption in the ByteRanges in the digital signature.", "corruptCryptographicContents": "SignatureHandler reports corruption in the Contents of the digital signature."}, "signatureDetails": {"signatureDetails": "Signature Details", "contactInformation": "Contact Information", "location": "Location", "reason": "Reason", "signingTime": "Signing Time", "noContactInformation": "No contact information provided", "noLocation": "No location provided", "noReason": "No reason provided", "noSigningTime": "No signing time found"}, "panelMessages": {"noSignatureFields": "This document has no signature fields", "certificateDownloadError": "Error encountered when trying to download a trusted certificate", "localCertificateError": "There are some issues with reading a local certificate"}, "verificationStatus": {"valid": "{{verificationType}} is valid.", "failed": "{{verificationType}} failed."}}, "cropPopUp": {"title": "Pages to <PERSON><PERSON>", "allPages": "All", "singlePage": "Current Page", "multiPage": "Specify Page", "cropDimensions": "Crop Dimensions", "dimensionInput": {"unitOfMeasurement": "Unit", "autoTrim": "Presets", "autoTrimCustom": "Custom"}, "cropModal": {"applyTitle": "Apply crop?", "applyMessage": "This action will permanently crop all selected pages. It cannot be undone.", "cancelTitle": "Cancel crop?", "cancelMessage": "Are you sure you want to cancel cropping all selected pages?"}}, "snippingPopUp": {"title": "Snipping Tool", "clipboard": "Copy to Clipboard", "download": "Download", "cropAndRemove": "Crop and Remove", "snippingModal": {"applyTitle": "Apply snipping?", "applyMessage": "This action will permanently cut out the specified area and remove other pages. It cannot be undone.", "cancelTitle": "Cancel snipping?", "cancelMessage": "Are you sure you want to stop snipping?"}}, "textEditingPanel": {"paragraph": "Paragraph", "text": "Text"}, "redactionPanel": {"noMarkedRedactions": "Start redacting by marking text, regions, pages or making a search.", "redactionSearchPlaceholder": "Redact by keyboard or patterns", "redactionCounter": "Marked for Redaction", "clearMarked": "Clear", "redactAllMarked": "Redact All", "redactionItems": "Redaction Items", "redactionItem": {"regionRedaction": "Region redaction", "fullPageRedaction": "Full page redaction", "fullVideoFrameRedaction": "Video Redaction", "audioRedaction": "Audio Redaction", "fullVideoFrameAndAudioRedaction": "Video and Audio Redaction", "image": "Image"}, "expand": "Expand", "collapse": "Collapse", "searchResults": "Search Results", "search": {"creditCards": "Credit Card", "phoneNumbers": "Phone Numbers", "images": "Images", "emails": "Emails", "pattern": "Patterns", "start": "Start making your search"}}, "wv3dPropertiesPanel": {"propertiesHeader": "Properties", "miscValuesHeader": "All", "emptyPanelMessage": "Select an element to view its properties."}, "watermarkPanel": {"textWatermark": "Text Watermark", "uploadImage": "Upload Image", "browse": "Browse", "watermarkOptions": "Watermark Options", "watermarks": "Watermarks"}, "portfolio": {"createPDFPortfolio": "Create PDF Portfolio", "uploadFiles": "Upload Files", "uploadFolder": "Upload Folder", "addFiles": "Add Files", "addFile": "Add File", "addFolder": "Add Folder", "createFolder": "Create Folder", "portfolioPanelTitle": "PDF Portfolio", "portfolioNewFolder": "New Folder", "portfolioDocumentTitle": "Document Title", "portfolioFolderPlaceholder": "Folder Name", "portfolioFilePlaceholder": "File Name", "folderNameAlreadyExists": "Folder name already exists", "fileNameAlreadyExists": "File name already exists", "openFile": "Open in New Tab", "fileAlreadyExists": "File Already Exists", "fileAlreadyExistsMessage": "The file \"{{fileName}}\" already exists in the portfolio.", "deletePortfolio": "Are you sure you want to delete \"{{fileName}}\"?", "reselect": "Reselect"}, "languageModal": {"selectLanguage": "Select language"}, "officeEditor": {"bold": "Bold", "italic": "Italic", "underline": "Underline", "textColor": "Text color", "leftAlign": "Left align", "centerAlign": "Center align", "rightAlign": "Right align", "justify": "Justify", "lineSpacing": "Line & paragraph spacing", "lineSpacingMenu": "Line Spacing", "bulletList": "Bulleted list", "numberList": "Numbered list", "decreaseIndent": "Decrease indent", "increaseIndent": "Increase indent", "nonPrintingCharacters": "Non-printing characters", "insertLink": "Insert link", "insertImage": "Insert image", "image": "Image", "table": "Table", "insertRowAbove": "Insert Row Above", "insertRowBelow": "Insert Row Below", "insertColumnRight": "Insert Column Right", "insertColumnLeft": "Insert Column Left", "deleteRow": "Delete Row", "deleteColumn": "Delete Column", "deleteTable": "Delete Table", "deleted": "Deleted: ", "added": "Added: ", "editing": "Editing", "editingDescription": "Edit document", "reviewing": "Reviewing", "reviewingDescription": "Make suggestions", "viewOnly": "Viewing", "viewOnlyDescription": "View without suggestions", "notSupportedOnMobile": "Office editing is not supported on mobile devices.", "previewAllChanges": "Preview all changes", "accept": "Accept", "reject": "Reject", "pastingTitle": "Pasting not available", "pastingMessage": "Pasting is not supported in your browser. Instead, you can use a keyboard shortcut", "pastingWithoutFormatTitle": "Pasting without formatting not available", "pastingWithoutFormatMessage": "Pasting without formatting is not supported in your browser. Instead, you can use a keyboard shortcut", "breaks": "Breaks", "pageBreak": "Page Break", "pageBreakDescription": "End page and start on new page", "sectionBreakNextPage": "Section Break - Next Page", "sectionBreakNextPageDescription": "Insert section break and start on next page", "sectionBreakContinuous": "Section Break - Continuous", "sectionBreakContinuousDescription": "Insert section break and continue on the same page ", "section": "Section", "margins": "<PERSON><PERSON>", "normal": "Normal", "narrow": "<PERSON>rrow", "moderate": "Moderate", "wide": "Wide", "top": "Top", "bottom": "Bottom", "left": "Left", "right": "Right", "customMargins": "Custom Margins", "customMarginsDescription": "Define custom margins", "header": {"-1": "<PERSON><PERSON><PERSON>", "0": "Header", "1": "First Page Header", "2": "<PERSON> <PERSON>", "3": "<PERSON>er"}, "footer": {"-1": "<PERSON><PERSON><PERSON>", "0": "Footer", "1": "First Page Footer", "2": "<PERSON> <PERSON>", "3": "<PERSON>"}, "options": "Options", "pageOptions": "Page Options", "removeHeader": "<PERSON><PERSON><PERSON>", "removeFooter": "<PERSON><PERSON><PERSON>", "headerFooterOptionsModal": {"title": "Header & footer format", "headerFromTop": "Header from Top", "footerFromBottom": "Footer from Bottom", "layouts": {"layout": "Layout", "noSelection": "No Selection", "differentFirstPage": "Different First Page", "differentEvenOddPages": "Different Even & Odd Pages", "differentFirstEvenOddPages": "Different First, Even, and Odd Pages"}}, "marginsModal": {"title": "Custom Margin Adjustment", "leftMargin": "Left Margin", "rightMargin": "Right Margin", "topMargin": "Top Margin", "bottomMargin": "Bottom Margin"}, "lineSpacingOptions": {"single": "Single", "115": "1.15", "15": "1.5", "double": "Double", "custom": "Custom"}, "numberDropdown": {"dropdownLabel": "Numbering Presets", "6": "Number Latin Roman 1", "7": "Number Decimal", "8": "Number Latin Roman 2", "10": "Latin Roman", "11": "Roman Latin Number"}, "bulletDropdown": {"dropdownLabel": "Bullet Presets", "0": "Bullet", "1": "Bullet Square", "2": "Square Bullet", "3": "Diamond", "4": "Check", "5": "Arrow"}, "fontSize": {"dropdownLabel": "Font Size"}, "fontStyles": {"dropdownLabel": "<PERSON><PERSON>"}, "fontFamily": {"dropdownLabel": "Font Family"}}, "spreadsheetEditor": {"editing": "Editing", "viewOnly": "Viewing", "editingDescription": "Edit document", "viewOnlyDescription": "View only", "bold": "Bold", "italic": "Italic", "underline": "Underline", "strikethrough": "Strikethrough", "strikeout": "Strikeout", "cellBorderStyle": "Border Style", "merge": "<PERSON><PERSON>", "unmerge": "Unmerge", "cellFormat": "Cell Format", "automaticFormat": "Automatic", "plainTextFormat": "Plain text", "increaseDecimalFormat": "Increase decimal", "decreaseDecimalFormat": "Decrease decimal", "numberFormat": "Number", "percentFormat": "Percent", "accountingFormat": "Accounting", "financialFormat": "Financial", "currencyFormat": "<PERSON><PERSON><PERSON><PERSON>", "currencyRoundedFormat": "Currency rounded", "calendarFormat": "Date", "clockHourFormat": "Time", "calendarTimeFormat": "Date Time", "formatAsCurrency": "Format as currency", "formatAsPercent": "Format as percent", "formatAsDecDecimal": "Decrease decimal point", "formatAsIncDecimal": "Increase decimal point", "textColor": "Text Color", "cellBackgroundColor": "Background Color", "cellBorderColor": "Border Color", "textLabel": "Text", "backgroundLabel": "Background", "borderLabel": "Border", "textAlignment": "Text Alignment", "topAlign": "Top align", "middleAlign": "Middle align", "bottomAlign": "Bottom align", "cellAdjustment": "Cell Adjustment", "cellFormatMoreOptions": "More cell format options", "insertColumnLeft": "Insert column left", "insertColumnRight": "Insert column right", "insertRowTop": "Insert row above", "insertRowBottom": "Insert row below", "insertColumnShiftDown": "Insert cells and shift down", "insertColumnShiftRight": "Insert cells and shift right", "deleteColumn": "Delete column", "deleteRow": "Delete row", "deleteColumnShiftUp": "Delete cells and shift up", "deleteColumnShiftLeft": "Delete cells and shift left", "clearBorder": "Clear Borders", "cellBorderAll": "All Borders", "cellBorderInside": "Inside Borders", "cellBorderOutside": "Outside Borders", "cellBorderTop": "Top Border", "cellBorderBottom": "Bottom Border", "cellBorderLeft": "Left Border", "cellBorderRight": "Right Border", "cellBorderVertical": "Vertical Border", "cellBorderHorizontal": "Horizontal Border", "cellBorderNone": "No Borders", "borders": "Borders", "fontSize": {"dropdownLabel": "Font Size"}, "fontFamily": {"dropdownLabel": "Font Family"}, "blankSheet": "Blank Sheet"}, "insertPageModal": {"title": "Insert New Page", "tabs": {"blank": "Blank", "upload": "Upload"}, "pagePlacements": {"header": "Page Placement", "above": "Above Page", "below": "Below Page"}, "pageLocations": {"header": "Page Location", "specify": "Specify Page", "specifyLocation": "Specify Page Location", "amount": "Number Of Pages", "total": "Total", "pages": "Pages"}, "pageDimensions": {"header": "Page Dimensions", "subHeader": "Presets", "presets": {"letter": "Letter", "halfLetter": "Half letter", "juniorLegal": "Junior legal", "custom": "Custom"}, "units": "Units"}, "browse": "Browse Files", "fileSelected": {"title": "Select Pages to Add"}, "button": "Add Page(s)", "selectPages": "Select Pages to Add", "page": "Page", "warning": {"title": "Exit insert new page?", "message": "Exiting will cancel all of the selections you have made so far. Are you sure you still want to exit?"}}, "multiViewer": {"dragAndDrop": "Drag and Drop your file here to compare", "or": "Or", "browse": "Browse Files", "startSync": "Start Sync", "stopSync": "Stop Sync", "closeDocument": "Close Document", "save": "Save Document", "comparePanel": {"Find": "Find in document", "changesList": "Change List", "change": "Change", "old": "Old", "new": "New", "page": "Page", "textContent": "Text Content", "delete": "delete", "insert": "insert", "edit": "edit"}}, "saveModal": {"close": "Close", "saveAs": "Save As", "general": "General", "fileName": "File Name", "fileType": "File Type", "fileLocation": "File Location", "browse": "Browse", "pageRange": "Page Range", "all": "All", "currentView": "Current View", "currentPage": "Current Page", "specifyPage": "Specify Page", "properties": "Properties", "includeAnnotation": "Include Annotations", "includeComments": "Include Comments", "watermark": "Watermark", "addWatermark": "Add Watermark", "save": "Save File", "pageError": "Please enter a range between  1 - ", "fileNameCannotBeEmpty": "File Name cannot be empty"}, "filePicker": {"dragAndDrop": "Drag & Drop your file here", "selectFile": "Select your file here", "or": "Or"}, "stylePanel": {"headings": {"styles": "Styles", "annotation": "Annotation", "annotations": "Annotations", "tool": "Tool", "textStyles": "Text Styles", "currentColor": "Current Color", "customColors": "Custom Colors", "redactionTextLabel": "Add Text Label", "redactionMarkOutline": "<PERSON>", "redactionFill": "Redaction Color", "redactionTextPlaceholder": "Insert text label", "contentEdit": "Content Edit"}, "lineStyles": {"startLineStyleLabel": "Start Line Style", "middleLineStyleLabel": "Middle Line Style", "endLineStyleLabel": "End Line Style"}, "addColorToCustom": "Add to custom colors", "noToolSelected": "Select a tool to view tool properties", "noToolStyle": "Tool does not contain any styling properties.", "noSharedToolStyle": "Selected annotations do not share any styling properties.", "lineEnding": {"start": {"dropdownLabel": "Line Start"}, "end": {"dropdownLabel": "Line End"}, "middle": {"dropdownLabel": "Line Middle"}}, "borderStyle": {"dropdownLabel": "Border"}}, "signatureListPanel": {"header": "Signature List", "newSignature": "New Signature", "newSignatureAndInitial": "Create New Signature & Initials", "signatureList": {"signature": "Signature", "initials": "Initials"}}, "rubberStampPanel": {"header": "Stamps", "standard": "Standard Stamps"}, "colorPickerModal": {"modalTitle": "Color Picker"}, "accessibility": {"landmarks": {"topHeader": "Top Header", "leftHeader": "Left Header", "rightHeader": "Right Header", "bottomHeader": "Bottom Header", "documentContent": "Document Content"}, "label": "Accessibility", "accessibilityMode": "Accessibility Mode", "skipTo": "Skip to", "document": "Document", "notes": "Notes"}, "formulaBar": {"label": "Formula Bar", "range": "Range", "formulas": "Formulas", "sumif": "A conditional sum across a range", "sumsq": "Returns the sum of the squares of a range", "sum": "Adds all the numbers in a range", "asinh": "Returns the inverse hyperbolic sine of a number", "acos": "Returns the arccosine of a number, in radians", "cosh": "Returns the hyperbolic cosine of a number", "iseven": "Checks if a number is even", "isodd": "Checks if a number is odd"}}