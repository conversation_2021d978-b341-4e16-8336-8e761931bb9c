    ################### LOCATION: WEBSERVICES #####################
    location /webservices/ {

        rewrite (?i)^/webservices/expologic.cfc /webservices/ExpoLogic.cfc;
        rewrite (?i)^/webservices/lists.cfc /webservices/lists.cfc;
        rewrite (?i)^/webservices/cflists.cfc /webservices/CFlists.cfc;
        rewrite (?i)^/webservices/listsdocument.cfc /webservices/listsDocument.cfc;

        ################### WEBSERVICES HANDLER #####################
        location ~* (\.cfm|\.cfc)(.*)$ {
            proxy_pass  "http://MCDockerLocal";
            include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }
    }


    ################### LOCATION: ROOT #####################
    location / {

        ################### CF HANDLER #####################
        location = / {
            proxy_pass  "http://MCDockerLocal";
            include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }

        ################### CF HANDLER #####################
        location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
            proxy_pass  "http://MCDockerLocal";
            include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
            proxy_set_header X-MC-InternalRequest $isinternalrequest;
        }


		location /trialsmithdocumentchatapi/ {
			proxy_pass  "http://trialsmithdocumentchatapi.local.membercentral.com/api/";
			include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
			proxy_set_header X-MC-InternalRequest $isinternalrequest;
            proxy_set_header Origin "";
		}

		location ~* ^/(assets|sitecomponents) {
            root "$rootfolder";
			rewrite (?i)^/assets/([A-Za-z0-9]+)/([A-Za-z0-9]+)/(galleries|storeimages|usercss|userimages|blogimages)/(.*) /userassets/$1/$2/$3/$4;
			rewrite (?i)^/assets/common/(adsystem|groupimages|invoices|reports|searchads)(.*) /userassets/common/$1$2;
			rewrite (?i)^/assets/ts/ts/(.*) /assets/tsjn/tsjn/$1;
			
            ################### CF HANDLER #####################
            location ~* (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
                proxy_pass  "http://MCDockerLocal";
                include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
                proxy_set_header X-MC-InternalRequest $isinternalrequest;
            }
			location ~* ^/(assets|sitecomponents) {
                #try_files $uri $uri/ =404;
                include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
                proxy_set_header Host "mcassets-dev.local.membercentral.com";
                proxy_pass "http://MCAssetsLocal";
			}
		}

		location ~* ^/userassets {
			rewrite (?i)^/userassets/([A-Za-z0-9]+)/([A-Za-z0-9]+)/(memberphotos|memberphotosth)/(.*) /userassets/$1/$3/$4;
			rewrite (?i)^/userassets/ts/ts/(.*) /userassets/tsjn/tsjn/$1;
			
            ################### CF HANDLER #####################
            location ~ (\.cfm|\.cfml|\.cfc|\.jsp|\.cfr|flex2gateway|messagebroker|flashservices|openamf)(.*)$ {
                proxy_pass  "http://MCDockerLocal";
                include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
                proxy_set_header X-MC-InternalRequest $isinternalrequest;
            }
			location ~* ^/userassets {
                #try_files $uri $uri/ =404;
                include     /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
                proxy_set_header Host "mcassets-dev.local.membercentral.com";
                proxy_pass "http://MCAssetsLocal";

			}
		}
        ################### LOCATION: Shared System Paths #####################
     	location /temp {
            alias   /opt/sites/shared/wwwroot/membercentral/temp;
        }

        location /app/shared/backendroot/temp {
            alias   /opt/sites/shared/backendroot/temp; 
        }

     	location /app/shared/wwwroot/sitedocuments {
            internal;
            alias   /opt/sites/shared/wwwroot/sitedocuments; 
        }

     	location /app/shared/tlasites {
            internal;
            alias   /opt/sites/shared/tlasites; 
        }

     	location /app/shared/tlasites/imageraid {
            internal;
            alias   /opt/sites/shared/tlasites/imageraid; 
        }

     	location /app/shared/wwwroot/temp {
            internal;
            alias   /opt/sites/shared/wwwroot/temp; 
        }

     	location /app/shared/wwwroot/membercentral/temp {
            internal;
            alias   /opt/sites/shared/wwwroot/membercentral/temp; 
        }


        ## Old Locations to support developers on older docker

        location /app/backendroot/temp {
            alias   /opt/sites/shared/backendroot/temp; 
        }

     	location /app/wwwroot/sitedocuments {
            internal;
            alias   /opt/sites/shared/mcplatform/sitedocuments; 
        }

     	location /app/tlasites {
            internal;
            alias   /opt/sites/shared/tlasites; 
        }

     	location /app/tlasites/imageraid {
            internal;
            alias   /opt/sites/shared/tlasites/imageraid; 
        }

     	location /app/wwwroot/temp {
            internal;
            alias   /opt/sites/shared/mcplatform/temp; 
        }

     	location /app/wwwroot/membercentral/temp {
            internal;
            alias   /opt/sites/shared/mcplatform/membercentral/temp; 
        }


    }

    ################### REWRITE: SES RULES #####################
    # Rewrite for URL Mappings
    # If you don't use SES urls you could do something like this
    # location ~ \.(cfm|cfml|cfc)(.*)$ {
    location @rewrite {
        rewrite ^/(.*)? /index.cfm/$request_uri last;
        rewrite ^ /index.cfm last;
    }

  
   
    ################### SECURITY BLOCKING #####################
    # Block root box.json and server.json
    location ~* /(box.json|server.json){
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny all;
    }

    # Lucee Admin/Doc blocking
    location ~* /lucee/(admin|doc){
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny      all;    
        proxy_pass  "http://MCDockerLocal";
        include /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
    }

    # Lucee Server Context blocking
    location ~* /lucee\-server/{
        access_log off; log_not_found off;
        allow 127.0.0.1;
        #deny all;
        proxy_pass  "http://MCDockerLocal";
        include /opt/sites/mc/mc-automanaged/localDevelopment/mc-snippets/membercentral-proxy.conf;
    }

    location ~* ^/robots.txt$ {
        alias "/opt/sites/mc/mc-automanaged/localDevelopment/sitemaps/$sitecode/robots.txt";
    }

    location ~* ^/sitemap(.*?\.xml(\.gz)?)$ {
        alias "/opt/sites/mc/mc-automanaged/localDevelopment/sitemaps/$sitecode/sitemap$1";  
    }            