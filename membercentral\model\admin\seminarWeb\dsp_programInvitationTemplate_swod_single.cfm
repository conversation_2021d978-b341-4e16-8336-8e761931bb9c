<cfoutput>
<mjml>
	<mj-head>
		<cfif len(arguments.preheaderText)>
			<mj-preview>#arguments.preheaderText#</mj-preview>
		</cfif>
		<mj-attributes>
			<mj-all font-family="Helvetica, sans serif" color="##5a5a5a" font-size="16px" line-height="1.25em"></mj-all>
			<mj-class name="presentedBy" color="##A7A2A9" align="center" font-size="18px" font-weight="300"></mj-class>
			<mj-class name="format" background-color="##6B9E39" text-transform="uppercase" font-size="12px" color="##fff" padding="0" inner-padding="3px 5px 2px 5px" align="left"></mj-class>
			<mj-class name="register" font-size="16px" border="solid 2px ##6B9E39" color="##6B9E39" text-transform="uppercase" background-color="##fff" font-weight="400" align="left" padding="15px 0 10px 0" inner-padding="8px 20px"></mj-class>
			<mj-class name="learnMore" font-size="15px" text-transform="uppercase" color="##6B9E39" font-weight="400" text-decoration="none"></mj-class>
			<mj-class name="secHead" font-family="georgia, serif" font-size="19px" font-weight="600" padding="15px 0 0 0" color="##1A3958"></mj-class>
			<mj-class name="secBorder" border="1px solid ##e3e3e3" background-color="##fff"></mj-class>
		</mj-attributes>
		<mj-style inline="inline">
			a {color: ##6B9E39;}
			.titleLink {color: ##1A3958; text-decoration: none;}
			.learnMore a {text-decoration: none !important;}
			.checks li {list-style: square; font-size:22px; color: ##cfcfcf;}
			.checks li span {font-size: 16px; color: ##5a5a5a; }
			.upImage {width: 100%}
			.upEvent {margin-top: 5px; font-family: georgia, serif; color: ##1A3958; text-decoration: none;}
			.format { background-color: ##6B9E39; text-transform: uppercase; font-size: 12px; color: ##fff; border: none; border-radius: 2px; padding: 3px 8px; font-weight: 300; letter-spacing: .75px; margin-bottom: 10px;}
			.head {color: ##1A3958; font-size: 36px; font-family: Georgia, serif; font-weight: 600; line-height: 38px; margin-top: 5px; text-decoration: none; margin-bottom: 10px;}
			.subhead {color: ##1A3958; font-size: 20px; font-family: Georgia, serif; line-height: 22px; margin-top: 5px; text-decoration: none; font-weight: normal; }
			.featuredImage {width: 175px; margin-right: 15px;}
		</mj-style>
		<mj-style>
			.upEventTitle {font-size: 19px; line-height: 1.2;}
			.centerMobile {float: right;}
			@media (max-width: 480px) {
				*[class~=hide_on_mobile] { display: none !important;}
				.centerMobile {float: none; display: block; margin: 0 auto;}
				.featuredImage {width: 80px !important; display: none;}
				.head {font-size: 32px !important; line-height: 34px !important;}
				.subhead {font-size: 18px !important; line-height: 20px !important;}
				.upEventTitle {font-size: 15px !important;}
				.headline {text-align: center !important;}
			}
		</mj-style>
	</mj-head>
	<mj-body>
		<mj-wrapper>
			<mj-section padding-top="0">
				<mj-column><mj-text mj-class="presentedBy" padding="10px 40px">#local.strAssociation.qryAssociation.description#</mj-text></mj-column>
			</mj-section>
			<mj-wrapper mj-class="secBorder" css-class="imgBkgd">
				<mj-section padding="0" direction="rtl">
					<mj-column width="5%"></mj-column>
					<mj-column width="<cfif len(local.thisProgram.programLevelFeaturedImagePath)>50%<cfelse>90%</cfif>" padding="15px 0 10px 0" vertical-align="middle">
						<mj-text padding="10px" css-class="headline" <cfif NOT len(local.thisProgram.programLevelFeaturedImagePath)>align="center"</cfif>>
							<p class="headline"><a class="format">#local.strAssociation.qryAssociation.brandSWODTab#</a></p>
							<h2 class="head headline"><a href="#local.thisProgram.programDetailPageLink#" class="titleLink">#encodeForHTML(local.strSeminar.qrySeminar.seminarName)#</a></h2>
							<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
								<h4 class="subhead headline">#encodeForHTML(local.strSeminar.qrySeminar.seminarSubTitle)#</h4>
							</cfif>
						</mj-text>
					</mj-column>
					<cfif len(local.thisProgram.programLevelFeaturedImagePath)>
						<mj-column width="40%" vertical-align="middle">
							<mj-image css-class="centerMobile" src="#local.thisProgram.programLevelFeaturedImagePath#" css-class="featuredImage">
							</mj-image>
						</mj-column>
					</cfif>
					<mj-column width="5%"></mj-column>
				</mj-section>
				<mj-section padding="0">
					<mj-column>
						<mj-button align="center" href="#local.thisProgram.programDetailPageLink#" mj-class="register" title="Go to program details page." text-decoration="none">Watch Online</mj-button>
					</mj-column>
				</mj-section>
				<cfif local.strSeminar.qryLearningObjectives.recordCount>
					<mj-section padding="0 10px 10px 10px">
						<mj-column>
							<mj-text css-class="checks" padding="0 20px">
							<ul>
								<cfloop query="local.strSeminar.qryLearningObjectives">
									<li><span>#local.strSeminar.qryLearningObjectives.objective#</span></li>
								</cfloop>
							</ul>
							</mj-text>
						</mj-column>
					</mj-section>
				</cfif>
			</mj-wrapper>
			<mj-section padding="0"><mj-column padding="0"><mj-spacer></mj-spacer></mj-column></mj-section>
			<cfif local.thisProgram.creditAuthorityCount>
				<mj-section mj-class="secBorder" padding="10px 10px 15px 10px">
					<mj-column padding="0 20px">
						<mj-text mj-class="secHead">Offered Credit</mj-text>
						<mj-text padding="10px 0">
							#local.thisProgram.dspCredits#
						</mj-text>
					</mj-column>
				</mj-section>
				<mj-section padding="0"><mj-column padding="0"><mj-spacer></mj-spacer></mj-column></mj-section>
			</cfif>

			<mj-wrapper mj-class="secBorder">
				<mj-section padding="0 10px">
					<mj-column padding="0 20px 10px 20px">
					<mj-text mj-class="secHead" padding-bottom="0">Speakers</mj-text>
					</mj-column>
				</mj-section>
				<cfloop query="local.qrySpeakers">
					<cfsavecontent variable="local.authorFullName">
						<cfif len(local.qrySpeakers.prefix)>#local.qrySpeakers.prefix# </cfif>#local.qrySpeakers.firstname# #local.qrySpeakers.middlename# #local.qrySpeakers.lastname#<cfif len(local.qrySpeakers.suffix)>, #local.qrySpeakers.suffix#</cfif>
					</cfsavecontent>

					<cfset local.trimmedSpeakerBio = application.objCommon.fullleft(local.qrySpeakers.biography,local.bioCount)>
					<cfif local.speakersWithPhotosCount gt 0 or val(local.qrySpeakers.featureImageID) gt 0>
						<mj-section padding="0">
							<mj-column width="20%">
								<cfif val(local.qrySpeakers.featureImageID) gt 0>
									<mj-image align="left" alt="Photo of #local.authorFullName#" width="80px" src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/userassets/#LCASE(local.qrySpeakers.featureImageOrgCode)#/#LCASE(local.qrySpeakers.featureImageSiteCode)#/featuredimages/thumbnails/#local.qrySpeakers.featureImageID#-#local.qrySpeakers.featureImageSizeID#.#local.qrySpeakers.fileExtension#" href="#local.thisProgram.programDetailPageLink#"></mj-image>
								<cfelse>
									<mj-image align="left" alt="No Photo Available" width="80px" src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/assets/common/images/directory/default.jpg"></mj-image>
								</cfif>
							</mj-column>
							<mj-column width="80%">
								<mj-text line-height="19px"  font-size="15px">
									<span style="color: ##1A3958; text-transform: uppercase; font-weight: bold;">#local.authorFullName#</span><br />
									#local.trimmedSpeakerBio#<cfif len(local.qrySpeakers.biography) gt len(local.trimmedSpeakerBio)>... <a href="#local.thisProgram.programDetailPageLink#" title="View full #local.authorFullName# bio.">more</a></cfif>
								</mj-text>
							</mj-column>
						</mj-section>
					<cfelse>
						<mj-section padding="0">
							<mj-column width="100%">
								<mj-text line-height="19px"  font-size="15px">
									<span style="color: ##1A3958; text-transform: uppercase; font-weight: bold;">#local.authorFullName#</span><br />
									#local.trimmedSpeakerBio#<cfif len(local.qrySpeakers.biography) gt len(local.trimmedSpeakerBio)>... <a href="#local.thisProgram.programDetailPageLink#" title="View full #local.authorFullName# bio.">more</a></cfif>
								</mj-text>
							</mj-column>
						</mj-section>
					</cfif>
				</cfloop>
			</mj-wrapper>
			<mj-section padding="0"><mj-column padding="0"><mj-spacer></mj-spacer></mj-column></mj-section>
			<mj-wrapper mj-class="secBorder" padding="0">
				<mj-section padding="5px 10px">
					<mj-column width="50%" padding="0 20px">
						<mj-text mj-class="secHead">More Details</mj-text>
					</mj-column>
					<mj-column width="50%" padding="0 20px">
						<mj-text mj-class="learnMore" align="right" padding-top="15" padding-right="0"><a href="#local.thisProgram.programDetailPageLink#" style="font-size: 10px; color: ##6B9E39; text-decoration: none;" title="Go to program details page.">Learn More</a></mj-text>
					</mj-column>
				</mj-section>
				<mj-section padding="0 10px 10px 10px">
				<mj-column padding="0 20px">
					<mj-text color="##5a5a5a" padding="0" align="justify">
						<p>#local.thisProgram.trimmedProgramDesc#</p>
					</mj-text>
					<mj-text color="##5a5a5a" padding="0 0 10px 0">
						<p style="color: ##1A3958;"><b>Questions?</b></p>
						<p>For assistance contact us: <a href="tel:#local.strAssociation.qryAssociation.supportPhone#" style="color: ##6B9E39;">#local.strAssociation.qryAssociation.supportPhone#</a> (#local.strAssociation.qryAssociation.supportHours#) or <a href="mailto:<EMAIL>" style="color: ##6B9E39;"><EMAIL></a></p>
					</mj-text>
				</mj-column>
				</mj-section>
			</mj-wrapper>
			<cfif arrayLen(local.upcomingProgramsArr)>
				<mj-section padding="0"><mj-column padding="0"><mj-spacer></mj-spacer></mj-column></mj-section>
				<mj-wrapper mj-class="secBorder" padding="0">
					<mj-section padding="5px 10px">
						<mj-column width="50%" padding="0 20px">
							<mj-text mj-class="secHead">Featured Programs</mj-text>
						</mj-column>
						<mj-column width="50%" padding="0 20px">
							<mj-text mj-class="learnMore" align="right" padding-top="15" padding-right="0"><a href="#local.thisProgram.browsePageLink#" style="font-size: 10px; color: ##6B9E39; text-decoration: none;" title="Go to catalog browse page.">View Catalog</a></mj-text>
						</mj-column>
					</mj-section>
					<cfloop array="#local.upcomingProgramsArr#" index="local.thisProgram">
						<mj-section padding="0 10px 0 10px">
							<cfif len(local.thisProgram.featuredImagePath)>
								<mj-column width="20%" padding="0" vertical-align="top" css-class="hide_on_mobile">
									<mj-image padding="20px 0 0 25px" css-class="hide_on_mobile upImage" src="#local.thisProgram.featuredImagePath#" title="Go to program details page." href="#local.thisProgram.programDetailPageLink#"></mj-image>
								</mj-column>
							</cfif>
							<mj-column width="<cfif len(local.thisProgram.featuredImagePath)>80%<cfelse>100%</cfif>" padding="<cfif len(local.thisProgram.featuredImagePath)>5px 0<cfelse>5px</cfif>" vertical-align="top">
								<mj-text >
								<p style="margin-top: 5px;" class="upEventTitle"><a href="#local.thisProgram.programDetailPageLink#" class="upEvent" title="Go to program details page.">#local.thisProgram.programTitle#<cfif len(local.thisProgram.programSubTitle)>: #local.thisProgram.programSubTitle#</cfif></a></p>
								</mj-text>
								<mj-divider border-color="##fff" border-width="2px" padding-bottom="0"></mj-divider>
							</mj-column>
						</mj-section>
					</cfloop>
				</mj-wrapper>
			</cfif>
		</mj-wrapper>
		<mj-section padding-top="0">
		<mj-column width="100%">
			<mj-text align="center" font-size="15px" color="##A7A2A9" font-weight="300">
				<p style="margin:5px;">
					This promotional message was sent by SeminarWeb on behalf of <a href="#local.strAssociation.qryAssociation.CatalogURL#" style="color: ##A7A2A9;">#local.qryOrgIdentity.organizationName#</a><br>
					13359 N Hwy 183 ##406-1220, Austin, TX 78750 | (737) 201-2059
				</p>
				<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
				<p style="margin:5px;">#chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color: ##A7A2A9; font-size: 13px;">UNSUBSCRIBE</a></p>
				<IMG ALT="" SRC="http://lists.trialsmith.com/db/%%outmail.messageid%%/%%memberid%%/1.gif" WIDTH="1" HEIGHT="1">
			</mj-text>
		</mj-column>
		</mj-section>
	</mj-body>
</mjml>
</cfoutput>