{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/handleFreeTextAutoSizeToggle.js"], "names": ["annotation", "setAutoSizeFont", "isAutoSizeFont", "freeTextAnnot", "switchOutFromAutoFontSize", "switchToAutoFontSize", "core", "getAnnotationManager", "redrawAnnotation"], "mappings": "4FAAA,WASe,aAACA,EAAYC,EAAiBC,GAC3C,IAAMC,EAAgBH,EAClBE,EACFC,EAAcC,4BAEdD,EAAcE,uBAGhBJ,GAAiBC,GACjBI,IAAKC,uBAAuBC,iBAAiBL", "file": "chunks/chunk.11.js", "sourcesContent": ["import core from 'core';\n\n/**\n * @ignore\n * handler for auto size font toggle\n * @param {FreeTextAnnotation} annotation annotation to toggle auto size font\n * @param {function} setAutoSizeFont function to set auto size font\n * @param {boolean} isAutoSizeFont current auto size font value\n */\nexport default (annotation, setAutoSizeFont, isAutoSizeFont) => {\n  const freeTextAnnot = annotation;\n  if (isAutoSizeFont) {\n    freeTextAnnot.switchOutFromAutoFontSize();\n  } else {\n    freeTextAnnot.switchToAutoFontSize();\n  }\n\n  setAutoSizeFont(!isAutoSizeFont);\n  core.getAnnotationManager().redrawAnnotation(freeTextAnnot);\n};\n\n"], "sourceRoot": ""}