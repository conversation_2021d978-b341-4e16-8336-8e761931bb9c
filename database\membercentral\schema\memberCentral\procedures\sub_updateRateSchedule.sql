ALTER PROCEDURE dbo.sub_updateRateSchedule
@siteID int,
@scheduleID int,
@scheduleName varchar(200),
@scheduleUID uniqueidentifier,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;
	CREATE TABLE #tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Rate Schedule Name] varchar(max), [API ID] varchar(max));

	declare @checkScheduleID int, @orgID int, @prevScheduleName varchar(200), @msg varchar(max), @crlf varchar(2);

	IF len(@scheduleName) = 0
		RAISERROR('Schedule name invalid.',16,1);
	
	-- check to see if valid name
	SELECT TOP 1 @checkScheduleID = scheduleID
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID <> @scheduleID
	AND scheduleName = @scheduleName
	AND [status] <> 'D';

	IF @checkScheduleID is not null
		RAISERROR('Schedule name already in use.',16,1);

	select @checkScheduleID = scheduleID, @prevScheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID = @scheduleID;

	IF @checkScheduleID is null
		RAISERROR('Schedule name invalid.',16,1);

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	SET @crlf = char(13) + char(10);

	INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
	VALUES ('DATATYPECODE', 'STRING', 'STRING');

	INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
	SELECT 'OLDVAL', scheduleName, [uid]
	FROM dbo.sub_rateSchedules
	WHERE scheduleID = @checkScheduleID;

	BEGIN TRAN
		update dbo.sub_rateSchedules
		set scheduleName = @scheduleName
		where scheduleID = @checkScheduleID;

		IF @scheduleUID is not null
			update dbo.sub_rateSchedules
			set [uid] = @scheduleUID
			where scheduleID = @checkScheduleID;

		UPDATE dbo.ams_virtualGroupConditions
		SET [verbose] = dbo.ams_getVirtualGroupConditionVerbose(conditionID)
		WHERE orgID = @orgID
		and left(fieldCode,4) = 'sub_'
		and conditionID in (
			select c.conditionID
			from dbo.ams_virtualGroupConditions as c
			inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID
				and k.conditionKey = 'subRate'
			inner join dbo.sub_rates as r on r.rateID = cast(cv.conditionValue as int)
				and cv.conditionValue > 0
				and r.scheduleID = @checkScheduleID
			where c.orgID = @orgID
		);

		INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
		SELECT 'NEWVAL', scheduleName, [uid]
		FROM dbo.sub_rateSchedules
		WHERE scheduleID = @checkScheduleID;

		EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
		
		IF ISNULL(@msg,'') <> '' BEGIN
			DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID":'+CAST(@checkScheduleID AS varchar(10))+' }';
			SET @msg = 'Rate Schedule [' + STRING_ESCAPE(@prevScheduleName,'json') + '] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

			EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
		END
	COMMIT TRAN

	IF OBJECT_ID('tempdb..#tmpAuditLogData') IS NOT NULL
		DROP TABLE #tmpAuditLogData;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
