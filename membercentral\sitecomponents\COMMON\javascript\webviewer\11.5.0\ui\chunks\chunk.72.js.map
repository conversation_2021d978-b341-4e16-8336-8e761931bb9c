{"version": 3, "sources": ["webpack:///./src/ui/src/components/OfficeEditorMarginsModal/OfficeEditorMarginsModal.scss?2ee0", "webpack:///./src/ui/src/components/OfficeEditorMarginsModal/OfficeEditorMarginsModal.scss", "webpack:///./src/ui/src/components/OfficeEditorMarginsModal/OfficeEditorMarginsModal.js", "webpack:///./src/ui/src/components/OfficeEditorMarginsModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "OfficeEditorMarginsModal", "t", "useTranslation", "dispatch", "useDispatch", "useState", "leftMargin", "setLeftMargin", "<PERSON><PERSON><PERSON><PERSON>", "setRightMargin", "<PERSON><PERSON><PERSON><PERSON>", "setTopMargin", "bottom<PERSON>argin", "setBottomMargin", "left", "right", "top", "bottom", "marginsOnOpen", "setMarginsOnOpen", "maxLeftMarginInInches", "setMaxLeftMarginInInches", "maxRightMarginInInches", "setMaxRightMarginInInches", "maxTopAndBottomMarginsInInches", "setMaxTopAndBottomMarginsInInches", "pageWidth", "set<PERSON>ageWidth", "MARGIN_UNITS", "CM", "currentUnit", "setCurrentUnit", "useEffect", "core", "getOfficeEditor", "getEditingPageNumber", "pageNumber", "pageHeight", "getDocumentViewer", "getPageHeight", "PIXELS_PER_INCH", "getPageWidth", "MARGIN_TOP_AND_BOTTOM_MAX_PERCENTAGE", "getSec<PERSON><PERSON><PERSON><PERSON>", "margins", "<PERSON><PERSON><PERSON><PERSON>", "toFixed", "updateMaxHorizontalMargins", "calculateMaxHorizontalMargin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MINIMUM_COLUMN_WIDTH_IN_INCHES", "CM_PER_INCH", "maxLeft<PERSON>argin", "max<PERSON><PERSON><PERSON><PERSON>gin", "onInputBlur", "e", "updateMarginCallback", "target", "value", "onMarginChange", "maxMarginInInches", "validateMarginInput", "closeModal", "actions", "closeElement", "DataElements", "OFFICE_EDITOR_MARGINS_MODAL", "setTimeout", "focusContent", "inputElements", "id", "label", "onChange", "onBlur", "className", "data-element", "ModalWrapper", "title", "closehandler", "onCloseClick", "swipeToClose", "isOpen", "map", "input", "key", "htmlFor", "Input", "type", "min", "step", "<PERSON><PERSON>", "onClick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,gmNAAmmN,KAG5nN0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,4rBCTvB,8lGAAA3B,GAAA,wBAAAA,EAAA,sBAAAA,GAAA,iBAAAA,GAAA,ssDAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,4bAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,yhBAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,qGAAAA,EAAA,yBAAAA,GAAA,IAAAA,EAAA,uBAAAA,GAAA,+kBAAAA,GAAA,gEAAAA,GAAA,0JAAAA,EAAA,6FAAAA,GAAA,mIAAAA,IAAA,4SAAAA,IAAA,2OAAAA,EAAA,iBAAAA,EAAA,EAAAA,IAAA,EAAAA,GAAA,EAAAA,GAAA,SAmBA,IA6Je4B,EA7JkB,WAC/B,IAAOC,EAAqB,EAAhBC,cAAgB,GAApB,GACFC,EAAWC,cAE+B,IAAZC,mBAAS,IAAG,GAAzCC,EAAU,KAAEC,EAAa,KACkB,IAAZF,mBAAS,IAAG,GAA3CG,EAAW,KAAEC,EAAc,KACY,IAAZJ,mBAAS,IAAG,GAAvCK,EAAS,KAAEC,EAAY,KACsB,IAAZN,mBAAS,IAAG,GAA7CO,EAAY,KAAEC,EAAe,KAC4D,IAAtDR,mBAAS,CAAES,KAAM,GAAIC,MAAO,GAAIC,IAAK,GAAIC,OAAQ,KAAK,GAAzFC,EAAa,KAAEC,EAAgB,KAC+B,IAAXd,mBAAS,GAAE,GAA9De,EAAqB,KAAEC,EAAwB,KACiB,IAAXhB,mBAAS,GAAE,GAAhEiB,EAAsB,KAAEC,EAAyB,KAC+B,IAAXlB,mBAAS,GAAE,GAAhFmB,EAA8B,KAAEC,EAAiC,KAC3B,IAAXpB,mBAAS,GAAE,GAAtCqB,EAAS,KAAEC,EAAY,KACiC,IAAzBtB,mBAASuB,IAAaC,IAAG,GAAxDC,EAAW,KAAEC,EAAc,KAElCC,oBAAS,YAAC,0FACwB,OAAhCD,EAAeH,IAAaC,IAAI,SACPI,IAAKC,kBAAkBC,uBAAsB,OAIe,OAJ/EC,EAAa,EAAH,KACVC,EAAaJ,IAAKK,oBAAoBC,cAAcH,GAAcI,IAExEb,EAAaM,IAAKK,oBAAoBG,aAAaL,GAAcI,KACjEf,EAAkCY,EAAaK,KAAsC,SAE/DT,IAAKC,kBAAkBS,kBAAkBb,GAAY,OAArEc,EAAU,EAAH,KACPC,EAAmB,CACvB/B,KAAO8B,EAAQ9B,KAAMgC,QAAQ,GAC7B/B,MAAQ6B,EAAQ7B,MAAO+B,QAAQ,GAC/B9B,IAAM4B,EAAQ5B,IAAK8B,QAAQ,GAC3B7B,OAAS2B,EAAQ3B,OAAQ6B,QAAQ,IAGnCvC,EAAcsC,EAAiB/B,MAC/BL,EAAeoC,EAAiB9B,OAChCJ,EAAakC,EAAiB7B,KAC9BH,EAAgBgC,EAAiB5B,QACjCE,EAAiB0B,GAAkB,4CAClC,IAEHb,qBAAU,WACRe,MACC,CAACrB,EAAWpB,EAAYE,IAE3B,IAAMwC,EAA+B,SAACtB,EAAWuB,GAE/C,OADiBvB,EAAYuB,EAAsBC,KAI/CH,EAA6B,WACjC,IAAMF,EAAmB,CACvB/B,KAAOgB,IAAgBF,IAAaC,GAAMvB,EAAa6C,IAAc7C,EACrES,MAAQe,IAAgBF,IAAaC,GAAMrB,EAAc2C,IAAc3C,GAEnE4C,EAAgBJ,EAA6BtB,EAAWmB,EAAiB9B,OACzEsC,EAAiBL,EAA6BtB,EAAWmB,EAAiB/B,MAEhFO,EAAyB+B,GACzB7B,EAA0B8B,IAGtBC,EAAc,SAACC,EAAGC,GACC,KAAnBD,EAAEE,OAAOC,OACXF,EAAqB,MAInBG,EAAiB,SAACJ,EAAGC,EAAsBI,GAE/CJ,EADYK,YAAoBN,EAAEE,OAAOC,MAAOE,EAAmB9B,KAmB/DgC,EAAa,WACjB3D,EAAS4D,IAAQC,aAAaC,IAAaC,8BAE3CC,YAAW,WACTlC,IAAKC,kBAAkBkC,iBACtB,IAGCC,EAAgB,CACpB,CACEC,GAAI,kBACJC,MAAOtE,EAAE,wCACTyD,MAAOpD,EACPkE,SAAU,SAACjB,GAAC,OAAKI,EAAeJ,EAAGhD,EAAea,IAClDqD,OAAQ,SAAClB,GAAC,OAAKD,EAAYC,EAAGhD,KAEhC,CACE+D,GAAI,mBACJC,MAAOtE,EAAE,yCACTyD,MAAOlD,EACPgE,SAAU,SAACjB,GAAC,OAAKI,EAAeJ,EAAG9C,EAAgBa,IACnDmD,OAAQ,SAAClB,GAAC,OAAKD,EAAYC,EAAG9C,KAEhC,CACE6D,GAAI,iBACJC,MAAOtE,EAAE,uCACTyD,MAAOhD,EACP8D,SAAU,SAACjB,GAAC,OAAKI,EAAeJ,EAAG5C,EAAca,IACjDiD,OAAQ,SAAClB,GAAC,OAAKD,EAAYC,EAAG5C,KAEhC,CACE2D,GAAI,oBACJC,MAAOtE,EAAE,0CACTyD,MAAO9C,EACP4D,SAAU,SAACjB,GAAC,OAAKI,EAAeJ,EAAG1C,EAAiBW,IACpDiD,OAAQ,SAAClB,GAAC,OAAKD,EAAYC,EAAG1C,MAIlC,OACE,yBAAK6D,UAAU,2BAA2BC,eAAcV,IAAaC,6BACnE,kBAACU,EAAA,EAAY,CACXC,MAAO5E,EAAE,mCACT6E,aAAchB,EACdiB,aAAcjB,EACdkB,cAAY,EACZC,QAAM,GAEN,yBAAKP,UAAU,cACZL,EAAca,KAAI,SAACC,GAAK,OACvB,yBAAKC,IAAKD,EAAMb,GAAII,UAAU,mBAC5B,2BAAOW,QAASF,EAAMb,GAAII,UAAU,SAASS,EAAMZ,OACnD,kBAACe,EAAA,EAAK,CACJC,KAAK,SACLjB,GAAIa,EAAMb,GACVE,SAAUW,EAAMX,SAChBC,OAAQU,EAAMV,OACdf,MAAOyB,EAAMzB,MACb8B,IAAI,IACJC,KAAK,aAKb,yBAAKf,UAAU,UACb,kBAACgB,EAAA,EAAM,CAACC,QAhFA,WAGd,GAFAxF,EAAS4D,IAAQC,aAAaC,IAAaC,8BACpB5D,IAAeY,EAAcJ,MAAQN,IAAgBU,EAAcH,OAASL,IAAcQ,EAAcF,KAAOJ,IAAiBM,EAAcD,OACrK,CAGA,IAAM2B,EAAU,CACd9B,KAAMR,EACNS,MAAOP,EACPQ,IAAKN,EACLO,OAAQL,GAEV,OAAOqB,IAAKC,kBAAkB0D,kBAAkBhD,EAASd,KAoEzByC,MAAOtE,EAAE,sBCxK9BD", "file": "chunks/chunk.72.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./OfficeEditorMarginsModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.OfficeEditorMarginsModal{visibility:visible}.closed.OfficeEditorMarginsModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.OfficeEditorMarginsModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.OfficeEditorMarginsModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.OfficeEditorMarginsModal .footer .modal-button.cancel:hover,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.OfficeEditorMarginsModal .footer .modal-button.cancel,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.OfficeEditorMarginsModal .footer .modal-button.cancel.disabled,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.OfficeEditorMarginsModal .footer .modal-button.cancel.disabled span,.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.OfficeEditorMarginsModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.OfficeEditorMarginsModal .modal-container .wrapper .modal-content{padding:10px}.OfficeEditorMarginsModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.OfficeEditorMarginsModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.OfficeEditorMarginsModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.OfficeEditorMarginsModal .footer .modal-button.confirm{margin-left:4px}.OfficeEditorMarginsModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .footer .modal-button{padding:23px 8px}}.OfficeEditorMarginsModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .OfficeEditorMarginsModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .OfficeEditorMarginsModal .swipe-indicator{width:32px}}.OfficeEditorMarginsModal{flex-direction:column}.OfficeEditorMarginsModal .modal-container{display:flex;flex-direction:column;height:auto;width:480px}.OfficeEditorMarginsModal .modal-container .modal-body{padding:16px;display:flex;flex-direction:column;font-size:var(--font-size-default);font-family:var(--font-family);grid-gap:16px;gap:16px}.OfficeEditorMarginsModal .modal-container .modal-body .title{line-height:16px;font-weight:var(--font-weight-bold)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container{display:flex;flex-direction:column;grid-gap:8px;gap:8px}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .label{color:var(--gray-12);display:block;text-align:left;font-weight:var(--font-weight-normal)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input{border-color:var(--gray-5);position:relative}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input:after{content:\\\"cm\\\";font-size:13px;color:var(--gray-8);position:absolute;right:16px;pointer-events:none}.OfficeEditorMarginsModal .modal-container .modal-body .input-container .ui__input.ui__input--focused{box-shadow:none;border-color:var(--focus-border)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input{padding:8px 40px 8px 8px;height:32px;font-size:var(--font-size-default)}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input[type=number]{-moz-appearance:textfield}.OfficeEditorMarginsModal .modal-container .modal-body .input-container input::-webkit-inner-spin-button,.OfficeEditorMarginsModal .modal-container .modal-body .input-container input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.OfficeEditorMarginsModal .modal-container .footer{padding:16px;display:flex;justify-content:flex-end;border-top:1px solid var(--gray-5)}.OfficeEditorMarginsModal .modal-container .footer button{border:none;border-radius:4px;background:var(--primary-button);min-width:59px;width:auto;padding:8px 16px;height:32px;color:var(--primary-button-text)}.OfficeEditorMarginsModal .modal-container .footer button:hover{background:var(--primary-button-hover)}.OfficeEditorMarginsModal .modal-container .modal-body{display:grid;grid-template-columns:216px 216px;grid-row-gap:24px}.OfficeEditorMarginsModal .ui__input__wrapper{width:216px}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport actions from 'actions';\nimport DataElements from 'constants/dataElement';\nimport Button from 'components/Button';\nimport ModalWrapper from 'components/ModalWrapper';\nimport { Input } from '@pdftron/webviewer-react-toolkit';\nimport core from 'core';\nimport {\n  MARGIN_UNITS,\n  PIXELS_PER_INCH,\n  MARGIN_TOP_AND_BOTTOM_MAX_PERCENTAGE,\n  MINIMUM_COLUMN_WIDTH_IN_INCHES,\n  CM_PER_INCH\n} from 'constants/officeEditor';\nimport { validateMarginInput } from 'helpers/officeEditor';\n\nimport './OfficeEditorMarginsModal.scss';\n\nconst OfficeEditorMarginsModal = () => {\n  const [t] = useTranslation();\n  const dispatch = useDispatch();\n\n  const [leftMargin, setLeftMargin] = useState('');\n  const [rightMargin, setRightMargin] = useState('');\n  const [topMargin, setTopMargin] = useState('');\n  const [bottomMargin, setBottomMargin] = useState('');\n  const [marginsOnOpen, setMarginsOnOpen] = useState({ left: '', right: '', top: '', bottom: '' });\n  const [maxLeftMarginInInches, setMaxLeftMarginInInches] = useState(0);\n  const [maxRightMarginInInches, setMaxRightMarginInInches] = useState(0);\n  const [maxTopAndBottomMarginsInInches, setMaxTopAndBottomMarginsInInches] = useState(0);\n  const [pageWidth, setPageWidth] = useState(0);\n  const [currentUnit, setCurrentUnit] = useState(MARGIN_UNITS.CM);\n\n  useEffect(async () => {\n    setCurrentUnit(MARGIN_UNITS.CM);\n    const pageNumber = await core.getOfficeEditor().getEditingPageNumber();\n    const pageHeight = core.getDocumentViewer().getPageHeight(pageNumber) / PIXELS_PER_INCH;\n\n    setPageWidth(core.getDocumentViewer().getPageWidth(pageNumber) / PIXELS_PER_INCH);\n    setMaxTopAndBottomMarginsInInches(pageHeight * MARGIN_TOP_AND_BOTTOM_MAX_PERCENTAGE);\n\n    const margins = await core.getOfficeEditor().getSectionMargins(currentUnit);\n    const convertedMargins = {\n      left: (margins.left).toFixed(2),\n      right: (margins.right).toFixed(2),\n      top: (margins.top).toFixed(2),\n      bottom: (margins.bottom).toFixed(2)\n    };\n\n    setLeftMargin(convertedMargins.left);\n    setRightMargin(convertedMargins.right);\n    setTopMargin(convertedMargins.top);\n    setBottomMargin(convertedMargins.bottom);\n    setMarginsOnOpen(convertedMargins);\n  }, []);\n\n  useEffect(() => {\n    updateMaxHorizontalMargins();\n  }, [pageWidth, leftMargin, rightMargin]);\n\n  const calculateMaxHorizontalMargin = (pageWidth, oppositeMarginWidth) => {\n    const maxWidth = pageWidth - oppositeMarginWidth - MINIMUM_COLUMN_WIDTH_IN_INCHES;\n    return maxWidth;\n  };\n\n  const updateMaxHorizontalMargins = () => {\n    const convertedMargins = {\n      left: (currentUnit === MARGIN_UNITS.CM) ? leftMargin / CM_PER_INCH : leftMargin,\n      right: (currentUnit === MARGIN_UNITS.CM) ? rightMargin / CM_PER_INCH : rightMargin,\n    };\n    const maxLeftMargin = calculateMaxHorizontalMargin(pageWidth, convertedMargins.right);\n    const maxRightMargin = calculateMaxHorizontalMargin(pageWidth, convertedMargins.left);\n\n    setMaxLeftMarginInInches(maxLeftMargin);\n    setMaxRightMarginInInches(maxRightMargin);\n  };\n\n  const onInputBlur = (e, updateMarginCallback) => {\n    if (e.target.value === '') {\n      updateMarginCallback('0');\n    }\n  };\n\n  const onMarginChange = (e, updateMarginCallback, maxMarginInInches) => {\n    const val = validateMarginInput(e.target.value, maxMarginInInches, currentUnit);\n    updateMarginCallback(val);\n  };\n\n  const onApply = () => {\n    dispatch(actions.closeElement(DataElements.OFFICE_EDITOR_MARGINS_MODAL));\n    const marginsChanged = leftMargin !== marginsOnOpen.left || rightMargin !== marginsOnOpen.right || topMargin !== marginsOnOpen.top || bottomMargin !== marginsOnOpen.bottom;\n    if (!marginsChanged) {\n      return;\n    }\n    const margins = {\n      left: leftMargin,\n      right: rightMargin,\n      top: topMargin,\n      bottom: bottomMargin,\n    };\n    return core.getOfficeEditor().setSectionMargins(margins, currentUnit);\n  };\n\n  const closeModal = () => {\n    dispatch(actions.closeElement(DataElements.OFFICE_EDITOR_MARGINS_MODAL));\n\n    setTimeout(() => {\n      core.getOfficeEditor().focusContent();\n    }, 0);\n  };\n\n  const inputElements = [\n    {\n      id: 'leftMarginInput',\n      label: t('officeEditor.marginsModal.leftMargin'),\n      value: leftMargin,\n      onChange: (e) => onMarginChange(e, setLeftMargin, maxLeftMarginInInches),\n      onBlur: (e) => onInputBlur(e, setLeftMargin),\n    },\n    {\n      id: 'rightMarginInput',\n      label: t('officeEditor.marginsModal.rightMargin'),\n      value: rightMargin,\n      onChange: (e) => onMarginChange(e, setRightMargin, maxRightMarginInInches),\n      onBlur: (e) => onInputBlur(e, setRightMargin),\n    },\n    {\n      id: 'topMarginInput',\n      label: t('officeEditor.marginsModal.topMargin'),\n      value: topMargin,\n      onChange: (e) => onMarginChange(e, setTopMargin, maxTopAndBottomMarginsInInches),\n      onBlur: (e) => onInputBlur(e, setTopMargin),\n    },\n    {\n      id: 'bottomMarginInput',\n      label: t('officeEditor.marginsModal.bottomMargin'),\n      value: bottomMargin,\n      onChange: (e) => onMarginChange(e, setBottomMargin, maxTopAndBottomMarginsInInches),\n      onBlur: (e) => onInputBlur(e, setBottomMargin),\n    }\n  ];\n\n  return (\n    <div className='OfficeEditorMarginsModal' data-element={DataElements.OFFICE_EDITOR_MARGINS_MODAL}>\n      <ModalWrapper\n        title={t('officeEditor.marginsModal.title')}\n        closehandler={closeModal}\n        onCloseClick={closeModal}\n        swipeToClose\n        isOpen\n      >\n        <div className='modal-body'>\n          {inputElements.map((input) => (\n            <div key={input.id} className='input-container'>\n              <label htmlFor={input.id} className='label'>{input.label}</label>\n              <Input\n                type='number'\n                id={input.id}\n                onChange={input.onChange}\n                onBlur={input.onBlur}\n                value={input.value}\n                min='0'\n                step='any'\n              />\n            </div>\n          ))}\n        </div>\n        <div className='footer'>\n          <Button onClick={onApply} label={t('action.apply')} />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nexport default OfficeEditorMarginsModal;\n", "import OfficeEditorMarginsModal from './OfficeEditorMarginsModal';\n\nexport default OfficeEditorMarginsModal;\n"], "sourceRoot": ""}