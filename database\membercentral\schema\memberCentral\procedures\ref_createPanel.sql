ALTER PROCEDURE dbo.ref_createPanel
@uid varchar(50),
@referralID int,
@name varchar(255),
@shortDesc varchar(255) = NULL,
@longDesc varchar(max) = NULL,
@statusID int,
@internalNotes varchar(max) = NULL,
@dateCreated datetime = NULL,
@dateCommitteeApproved datetime = NULL,
@dateBoardApproved datetime = NULL,
@dateBoardNotified datetime = NULL,
@dateReviewed datetime = NULL,
@sendMail bit = NULL,
@maxNumMembers int = NULL,
@referralFeePercent decimal(4,2) = NULL,
@deductExpenseDesc varchar(max) = NULL,
@referralAmount decimal(18,2) = NULL,
@panelParentID int = NULL,
@GLAccountID int = NULL,
@clientFeeGLAccountID int = NULL,
@clientReferralAmount decimal(18,2) = NULL,
@sendReceiptEmail bit = NULL,
@allowPanelMgmt bit = NULL,
@feAllowClientReferral bit = NULL,
@feDspClientReferral bit = NULL,
@feConfirmReferralContent varchar(max) = NULL,
@feReviewSubmissionContent varchar(max) = NULL,
@ovFeeStructure bit = 0,
@memberID int,
@isActive bit = NULL,	
@panelID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @resourceTypeID int, @orgID int, @siteID int, @parentSiteResourceID int, @siteResourceID int, @feConfirmReferralContentID int,
		@feReviewSubmissionContentID int, @contentSiteResourceID int, @appCreatedContentResourceTypeID int, 
		@defaultLanguageID int, @parentPanelName varchar(255), @crlf varchar(10), @msgjson varchar(max);

	IF EXISTS (select panelID FROM dbo.ref_panels where [name] = @name and referralID = @referralID and statusID = 1 and panelParentID = @panelParentID)
		SET @panelID = 0;
	ELSE BEGIN
		set @crlf = char(13) + char(10);
		set @parentSiteResourceID = NULL;
		select @resourceTypeID = dbo.fn_getResourceTypeID('ReferralPanel');

		select @orgID = s.orgID, @siteID = s.siteID
		from dbo.cms_applicationInstances ai
		inner join dbo.ref_referrals r on r.applicationInstanceID = ai.applicationInstanceID
			and r.referralID = @referralID
		inner join dbo.sites s on s.siteID = ai.siteID;

		select @defaultLanguageID = defaultLanguageID from dbo.sites where siteID = @siteID;
		select @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

		IF @panelParentID is not null
			select @parentSiteResourceID = siteResourceID, @parentPanelName = [name]
			from dbo.ref_panels
			where panelid = @panelParentID;

		BEGIN TRAN;
			exec cms_createSiteResource @resourceTypeID=@resourceTypeID, @siteResourceStatusID=1, @siteID=@siteID,
				@isVisible=1, @parentSiteResourceID=@parentSiteResourceID, @siteResourceID=@siteResourceID OUTPUT;

			-- create feConfirmReferralContentID
			exec dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID,
				@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, @isActive=1,
				@contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@feConfirmReferralContentID OUTPUT,
				@siteResourceID=@contentSiteResourceID OUTPUT;

			-- create feReviewSubmissionContentID
			exec dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=@defaultLanguageID, 
				@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @contentID=@feReviewSubmissionContentID OUTPUT, 
				@siteResourceID=@contentSiteResourceID OUTPUT;

			insert into dbo.ref_panels ([uid], referralID, [name], shortDesc, longDesc, statusID, internalNotes, dateCreated,
				dateCommitteeApproved, dateBoardApproved, dateBoardNotified, dateReviewed, sendMail, maxNumMembers,
				referralFeePercent, deductExpenseDesc, referralAmount, panelParentID, GLAccountID, isActive, siteResourceID,
				clientFeeGLAccountID, clientReferralAmount, sendReceiptEmail, allowPanelMgmt, feAllowClientReferral, 
				feDspClientReferral, feConfirmReferralContentID, feReviewSubmissionContentID, ovFeeStructure)
			values (@uid, @referralID, @name, @shortDesc, @longDesc, @statusID, @internalNotes, @dateCreated, 
				@dateCommitteeApproved, @dateBoardApproved, @dateBoardNotified, @dateReviewed, @sendMail,
				@maxNumMembers, @referralFeePercent, @deductExpenseDesc, @referralAmount, @panelParentID, @GLAccountID,
				@isActive, @siteResourceID, @clientFeeGLAccountID, @clientReferralAmount, @sendReceiptEmail, @allowPanelMgmt,
				@feAllowClientReferral, @feDspClientReferral, @feConfirmReferralContentID, @feReviewSubmissionContentID, 
				@ovFeeStructure);

			SELECT @panelID = SCOPE_IDENTITY();

			if @feConfirmReferralContentID is not null
				EXEC dbo.cms_updateContent @contentID=@feConfirmReferralContentID, @languageID=1, @isHTML=1, @contentTitle='', 
					@contentDesc='', @rawContent=@feConfirmReferralContent, @memberID=@memberID;

			if @feReviewSubmissionContentID is not null
				EXEC dbo.cms_updateContent @contentID=@feReviewSubmissionContentID, @languageID=1, @isHTML=1, @contentTitle='', 
					@contentDesc='', @rawContent=@feReviewSubmissionContent, @memberID=@memberID;
		
			IF @parentPanelName IS NOT NULL
				SET @msgjson = 'New Sub-Panel [' + @name + '] has been created under [' + @parentPanelName + '].';
			ELSE
				SET @msgjson = 'New Panel [' + @name + '] has been created.';

			SET @msgjson = @msgjson
				+ CASE WHEN len(isnull(@shortDesc,'')) > 0 THEN @crlf + 'Short Description: ' + @shortDesc ELSE '' END
				+ @crlf + 'Display Panel in Front-End?: ' + CASE @feDspClientReferral WHEN 1 THEN 'Yes' ELSE 'No' END;

			SET @msgjson = STRING_ESCAPE(@msgjson,'json');

			EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
				@enteredByMemberID=@memberID;
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
