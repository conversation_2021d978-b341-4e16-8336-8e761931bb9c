{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/VerticalAlignmentButton.js"], "names": ["propTypes", "isFlyoutItem", "PropTypes", "bool", "alignment", "string", "style", "object", "className", "alignmentValue", "VerticalAlignmentButton", "props", "ref", "selector", "selectors", "getActiveCellRangeVerticalAlignment", "React", "forwardRef", "displayName"], "mappings": "sYAKA,IAAMA,EAAY,CAChBC,aAAcC,IAAUC,KACxBC,UAAWF,IAAUG,OACrBC,MAAOJ,IAAUK,OACjBC,UAAWN,IAAUG,OACrBI,eAAgBP,IAAUG,QAGtBK,EAA0B,SAACC,EAAOC,GAAG,OACzC,kBAAC,IAAmB,KAAKD,EAAK,CAAEC,IAAKA,EAAKC,SAAUC,IAAUC,wCAGjDC,cAAMC,WAAWP,GAChCA,EAAwBQ,YAAc,0BACtCR,EAAwBV,UAAYA", "file": "chunks/chunk.96.js", "sourcesContent": ["import React from 'react';\nimport BaseAlignmentButton from './BaseAlignmentButton';\nimport selectors from 'selectors';\nimport PropTypes from 'prop-types';\n\nconst propTypes = {\n  isFlyoutItem: PropTypes.bool,\n  alignment: PropTypes.string,\n  style: PropTypes.object,\n  className: PropTypes.string,\n  alignmentValue: PropTypes.string,\n};\n\nconst VerticalAlignmentButton = (props, ref) => (\n  <BaseAlignmentButton {...props} ref={ref} selector={selectors.getActiveCellRangeVerticalAlignment} />\n);\n\nexport default React.forwardRef(VerticalAlignmentButton);\nVerticalAlignmentButton.displayName = 'VerticalAlignmentButton';\nVerticalAlignmentButton.propTypes = propTypes;"], "sourceRoot": ""}