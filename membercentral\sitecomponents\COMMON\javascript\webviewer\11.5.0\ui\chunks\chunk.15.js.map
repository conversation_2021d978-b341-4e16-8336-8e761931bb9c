{"version": 3, "sources": ["webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/utils.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/interfaces.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/utils/predicates.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/utils/offsets.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/utils/supportsPassive.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/OptionsReader.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/TouchBackend.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/utils/math.js", "webpack:///./src/ui/node_modules/react-dnd-touch-backend/dist/esm/index.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/disposables.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/decorateHandler.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/hooks/useDragLayer.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/createTargetFactory.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/DropTarget.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/createSourceFactory.js", "webpack:///./src/ui/node_modules/react-dnd/dist/esm/decorators/DragSource.js", "webpack:///./src/ui/node_modules/react-dnd-html5-backend/dist/esm/getEmptyImage.js"], "names": ["_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "getDecoratedComponent", "instanceRef", "currentRef", "current", "decoratedRef", "isRefable", "C", "Component", "render", "_item$$$typeof", "item", "$$typeof", "toString", "isRefForwardingComponent", "checkDecoratorArguments", "functionName", "signature", "isFunction", "input", "noop", "isPlainObject", "isObjectLike", "Object", "getPrototypeOf", "proto", "isValidType", "type", "allowArray", "Array", "isArray", "every", "t", "ListenerType", "MouseButtons", "MouseB<PERSON>on", "eventShouldStartDrag", "e", "undefined", "button", "isTouchEvent", "targetTouches", "getEventClientOffset", "lastTargetTouchFallback", "length", "touches", "target", "getEventClientTouchOffset", "x", "clientX", "y", "clientY", "supportsPassive", "supported", "addEventListener", "defineProperty", "get", "_defineProperties", "props", "i", "descriptor", "enumerable", "configurable", "writable", "key", "_eventNames", "OptionsReader", "incoming", "context", "_this", "this", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_classCallCheck", "enableTouchEvents", "enableMouseEvents", "enableKeyboardEvents", "ignoreContextMenu", "enableHoverOutsideTarget", "touchSlop", "scrollAngleRanges", "delayTouchStart", "delay", "delayMouseStart", "keys", "for<PERSON>ach", "protoProps", "staticProps", "window", "document", "_defineProperty", "value", "eventNames", "mouse", "start", "move", "end", "contextmenu", "touch", "keyboard", "keydown", "TouchBackend", "manager", "options", "getSourceClientOffset", "sourceId", "node", "el", "nodeType", "parentElement", "_el$getBoundingClient", "getBoundingClientRect", "top", "left", "getNodeClientOffset", "sourceNodes", "handleTopMoveStartCapture", "moveStartSourceIds", "handleMoveStart", "unshift", "handleTopMoveStart", "clientOffset", "_mouseClientOffset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleTopMoveStartDelay", "timeout", "setTimeout", "bind", "handleTopMoveCapture", "dragOverTargetIds", "handleMove", "_", "targetId", "handleTopMove", "clearTimeout", "x1", "y1", "x2", "y2", "_isScrolling", "monitor", "isDragging", "angleRanges", "angle", "Math", "atan2", "PI", "inAngleRanges", "hasOwnProperty", "sqrt", "pow", "abs", "actions", "beginDrag", "publishSource", "sourceNode", "getSourceId", "installSourceNodeRemovalObserver", "publishDragSource", "preventDefault", "dragOverTargetNodes", "map", "targetNodes", "elementsAtPoint", "getDropTargetElementsAtPoint", "elementsFromPoint", "elementsAtPointExtended", "nodeId", "currentNode", "push", "indexOf", "orderedDragOverTargetIds", "filter", "id", "index", "ids", "contains", "reverse", "hover", "handleTopMoveEndCapture", "buttons", "eventShouldEndDrag", "didDrop", "uninstallSourceNodeRemovalObserver", "drop", "endDrag", "handleCancelOnEscape", "getActions", "getMonitor", "sourcePreviewNodes", "sourcePreviewNodeOptions", "listenerTypes", "invariant", "isSetUp", "getTopMoveStartHandler", "removeEventListener", "subject", "event", "handler", "capture", "passive", "listenerType", "evt", "_this2", "_this3", "_this4", "coords", "droppedOn", "elementFromPoint", "childMatch", "body", "_this5", "draggedSourceNode", "draggedSourceNodeRemovalObserver", "MutationObserver", "resurrectSourceNode", "observe", "childList", "style", "display", "removeAttribute", "append<PERSON><PERSON><PERSON>", "disconnect", "createBackend", "arguments", "_createClass", "Disposable", "action", "isDisposed", "d", "Boolean", "dispose", "result", "isDisposable", "empty", "CompositeDisposable", "_len", "disposables", "_key", "shouldDispose", "idx", "splice", "len", "currentDisposables", "_i", "_i2", "SerialDisposable", "old", "_slicedToArray", "arr", "_arrayWithHoles", "_s", "_e", "_arr", "_n", "_d", "call", "next", "done", "err", "_iterableToArrayLimit", "o", "minLen", "_arrayLikeToArray", "n", "slice", "name", "from", "test", "_unsupportedIterableToArray", "_nonIterableRest", "arr2", "_setPrototypeOf", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "valueOf", "_isNativeReflectConstruct", "Super", "_getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON>", "apply", "_possibleConstructorReturn", "self", "_assertThisInitialized", "ReferenceError", "decorate<PERSON><PERSON><PERSON>", "_ref", "DecoratedComponent", "createHandler", "createMonitor", "createConnector", "registerHandler", "containerDisplayName", "getType", "collect", "_options$arePropsEqua", "arePropsEqual", "shallowEqual", "Decorated", "displayName", "DragDropContainer", "_Component", "subClass", "superClass", "create", "_inherits", "_super", "createRef", "nextState", "getCurrentState", "state", "setState", "disposable", "receiveProps", "handlerId", "nextProps", "currentType", "handleChange", "prevProps", "receiveType", "handlerMonitor", "handlerConnector", "_registerHandler2", "unregister", "receiveHandlerId", "unsubscribe", "subscribeToStateChange", "handlerIds", "setDisposable", "hooks", "_jsx", "DndContext", "Consumer", "children", "_ref2", "dragDropManager", "receiveDragDropManager", "requestAnimationFrame", "_this2$handlerConnect", "reconnect", "assign", "ref", "getBackend", "concat", "hoistStatics", "useDragLayer", "useDragDropManager", "_useCollector2", "useCollector", "collected", "updateCollected", "useEffect", "subscribeToOffsetChange", "ALLOWED_SPEC_METHODS", "TargetImpl", "spec", "canDrop", "createTargetFactory", "join", "<PERSON><PERSON>arget", "createTarget", "registerTarget", "DropTargetMonitorImpl", "backend", "TargetConnector", "REQUIRED_SPEC_METHODS", "SourceImpl", "canDrag", "globalMonitor", "createSourceFactory", "DragSource", "createSource", "registerSource", "SourceConnector", "DragSourceMonitorImpl", "emptyImage", "getEmptyImage", "Image", "src"], "mappings": "yZAAA,SAASA,EAAQC,GAAmV,OAAtOD,EAArD,mBAAXE,QAAoD,iBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,GAA2B,SAAiBA,GAAO,OAAOA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,IAAyBA,GAE5W,SAASK,EAAsBC,GACpC,IAAIC,EAAaD,EAAYE,QAE7B,OAAkB,MAAdD,EACK,KACEA,EAAWE,aAEbF,EAAWE,aAAaD,QAExBD,EAYJ,SAASG,EAAUC,GACxB,OAV+BC,EAUPD,IATJC,EAAUR,WAAmD,mBAA/BQ,EAAUR,UAAUS,QAEjE,SAAkCF,GACvC,IAAIG,EAEAC,EAAOJ,EACX,MAAqK,+BAA7JI,SAAiF,QAApCD,EAAiBC,EAAKC,gBAAyC,IAAnBF,OAAtD,EAA2FA,EAAeG,YAGvHC,CAAyBP,GAVlD,IAA0BC,EAY1B,SAASO,EAAwBC,EAAcC,IAa/C,SAASC,EAAWC,GACzB,MAAwB,mBAAVA,EAET,SAASC,KAOT,SAASC,EAAcF,GAC5B,IALF,SAAsBA,GACpB,MAA0B,WAAnBxB,EAAQwB,IAAiC,OAAVA,EAIjCG,CAAaH,GAChB,OAAO,EAGT,GAAqC,OAAjCI,OAAOC,eAAeL,GACxB,OAAO,EAKT,IAFA,IAAIM,EAAQN,EAE4B,OAAjCI,OAAOC,eAAeC,IAC3BA,EAAQF,OAAOC,eAAeC,GAGhC,OAAOF,OAAOC,eAAeL,KAAWM,EAEnC,SAASC,EAAYC,EAAMC,GAChC,MAAuB,iBAATD,GAAuC,WAAlBhC,EAAQgC,MAAwBC,GAAcC,MAAMC,QAAQH,IAASA,EAAKI,OAAM,SAAUC,GAC3H,OAAON,EAAYM,GAAG,Q,6GCpEfC,E,SAEX,SAAWA,GACTA,EAAoB,MAAI,QACxBA,EAAoB,MAAI,QACxBA,EAAuB,SAAI,WAH7B,CAIGA,IAAiBA,EAAe,KCLnC,IAAIC,EACI,EAKJC,EACI,EASD,SAASC,EAAqBC,GAGnC,YAAoBC,IAAbD,EAAEE,QAAwBF,EAAEE,SAAWJ,EAczC,SAASK,EAAaH,GAC3B,QAASA,EAAEI,cCRN,SAASC,EAAqBL,EAAGM,GACtC,OAAIH,EAAaH,GAVZ,SAAmCA,EAAGM,GAC3C,OAA+B,IAA3BN,EAAEI,cAAcG,OACXF,EAAqBL,EAAEI,cAAc,IACnCE,GAAgD,IAArBN,EAAEQ,QAAQD,QAC1CP,EAAEQ,QAAQ,GAAGC,SAAWH,EAAwBG,OAC3CJ,EAAqBL,EAAEQ,QAAQ,SAFnC,EAQEE,CAA0BV,EAAGM,GAE7B,CACLK,EAAGX,EAAEY,QACLC,EAAGb,EAAEc,SCjCX,IAkBeC,EAlBO,WAEpB,IAAIC,GAAY,EAEhB,IACEC,iBAAiB,QAAQ,cACtB/B,OAAOgC,eAAe,GAAI,UAAW,CACtCC,IAAK,WAEH,OADAH,GAAY,GACL,MAGX,MAAOhB,IAGT,OAAOgB,EAfa,G,MCEtB,SAASI,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAItS,ICNHK,EDMOC,EAEX,WACE,SAASA,EAAcC,EAAUC,GAC/B,IAAIC,EAAQC,MAVhB,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAY5GC,CAAgBJ,KAAMJ,GAEtBI,KAAKK,mBAAoB,EACzBL,KAAKM,mBAAoB,EACzBN,KAAKO,sBAAuB,EAC5BP,KAAKQ,mBAAoB,EACzBR,KAAKS,0BAA2B,EAChCT,KAAKU,UAAY,EACjBV,KAAKW,uBAAoB3C,EACzBgC,KAAKF,QAAUA,EACfE,KAAKY,gBAAkBf,EAASe,iBAAmBf,EAASgB,OAAS,EACrEb,KAAKc,gBAAkBjB,EAASiB,iBAAmBjB,EAASgB,OAAS,EACrE5D,OAAO8D,KAAKlB,GAAUmB,SAAQ,SAAUtB,GACjB,MAAjBG,EAASH,KAEXK,EAAML,GAAOG,EAASH,OAvB9B,IAAsBQ,EAAae,EAAYC,EAkD7C,OAlDoBhB,EA4BPN,GA5BoBqB,EA4BL,CAAC,CAC3BvB,IAAK,SACLR,IAAK,WACH,OAAIc,KAAKF,SAAWE,KAAKF,QAAQqB,OACxBnB,KAAKF,QAAQqB,OACO,oBAAXA,OACTA,YADF,IAMR,CACDzB,IAAK,WACLR,IAAK,WACH,GAAIc,KAAKmB,OACP,OAAOnB,KAAKmB,OAAOC,cA3CmDjC,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAkD3KtB,EA9CT,GCJA,SAAST,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAI7S,SAAS+B,EAAgB/F,EAAKoE,EAAK4B,GAAiK,OAApJ5B,KAAOpE,EAAO2B,OAAOgC,eAAe3D,EAAKoE,EAAK,CAAE4B,MAAOA,EAAO/B,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBnE,EAAIoE,GAAO4B,EAAgBhG,EAS3M,IAAIiG,GAAgCF,EAAlB1B,EAAc,GAAiChC,EAAa6D,MAAO,CACnFC,MAAO,YACPC,KAAM,YACNC,IAAK,UACLC,YAAa,gBACXP,EAAgB1B,EAAahC,EAAakE,MAAO,CACnDJ,MAAO,aACPC,KAAM,YACNC,IAAK,aACHN,EAAgB1B,EAAahC,EAAamE,SAAU,CACtDC,QAAS,YACPpC,GAEAqC,EAEJ,WACE,SAASA,EAAaC,EAASnC,EAASoC,GACtC,IAAInC,EAAQC,MAhChB,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAkC5GC,CAAgBJ,KAAMgC,GAEtBhC,KAAKmC,sBAAwB,SAAUC,GACrC,OHrCC,SAA6BC,GAClC,IAAIC,EAFa,IAERD,EAAKE,SAA4BF,EAAOA,EAAKG,cAEtD,GAAKF,EAAL,CAIA,IAAIG,EAAwBH,EAAGI,wBAC3BC,EAAMF,EAAsBE,IAGhC,MAAO,CACLjE,EAHS+D,EAAsBG,KAI/BhE,EAAG+D,IGwBME,CAAoB9C,EAAM+C,YAAYV,KAG/CpC,KAAK+C,0BAA4B,SAAUhF,GACpCD,EAAqBC,KAI1BgC,EAAMiD,mBAAqB,KAG7BhD,KAAKiD,gBAAkB,SAAUb,GAG3B7E,MAAMC,QAAQuC,EAAMiD,qBACtBjD,EAAMiD,mBAAmBE,QAAQd,IAIrCpC,KAAKmD,mBAAqB,SAAUpF,GAClC,GAAKD,EAAqBC,GAA1B,CAQA,IAAIqF,EAAehF,EAAqBL,GAEpCqF,IACElF,EAAaH,KACfgC,EAAM1B,wBAA0BN,EAAEI,cAAc,IAGlD4B,EAAMsD,mBAAqBD,GAG7BrD,EAAMuD,iBAAkB,IAG1BtD,KAAKuD,wBAA0B,SAAUxF,GACvC,GAAKD,EAAqBC,GAA1B,CAIA,IAAI8C,EAAQ9C,EAAEV,OAASkE,EAAWM,MAAMJ,MAAQ1B,EAAMmC,QAAQtB,gBAAkBb,EAAMmC,QAAQpB,gBAC9Ff,EAAMyD,QAAUC,WAAW1D,EAAMoD,mBAAmBO,KAAK3D,EAAOhC,GAAI8C,GACpEd,EAAMuD,iBAAkB,IAG1BtD,KAAK2D,qBAAuB,WAC1B5D,EAAM6D,kBAAoB,IAG5B5D,KAAK6D,WAAa,SAAUC,EAAGC,GACzBhE,EAAM6D,mBACR7D,EAAM6D,kBAAkBV,QAAQa,IAIpC/D,KAAKgE,cAAgB,SAAUjG,GAK7B,GAJIgC,EAAMyD,SACRS,aAAalE,EAAMyD,SAGhBzD,EAAMqB,WAAYrB,EAAMuD,gBAA7B,CAIA,IC7GmBY,EAAIC,EAAIC,EAAIC,ED6G3BrB,EAAqBjD,EAAMiD,mBAC3BY,EAAoB7D,EAAM6D,kBAC1BnD,EAA2BV,EAAMmC,QAAQzB,yBACzC2C,EAAehF,EAAqBL,EAAGgC,EAAM1B,yBAEjD,GAAK+E,EAKL,GAAIrD,EAAMuE,eAAiBvE,EAAMwE,QAAQC,cCpHxC,SAAuBN,EAAIC,EAAIC,EAAIC,EAAII,GAC5C,IAAKA,EACH,OAAO,EAKT,IAFA,IAAIC,EAAuC,IAA/BC,KAAKC,MAAMP,EAAKF,EAAIC,EAAKF,GAAYS,KAAKE,GAAK,IAElDxF,EAAI,EAAGA,EAAIoF,EAAYnG,SAAUe,EACxC,IAA6B,MAAxBoF,EAAYpF,GAAGoC,OAAiBiD,GAASD,EAAYpF,GAAGoC,SAAiC,MAAtBgD,EAAYpF,GAAGsC,KAAe+C,GAASD,EAAYpF,GAAGsC,KAC5H,OAAO,EAIX,OAAO,EDuGsDmD,CAAc/E,EAAMsD,mBAAmB3E,GAAK,EAAGqB,EAAMsD,mBAAmBzE,GAAK,EAAGwE,EAAa1E,EAAG0E,EAAaxE,EAAGmB,EAAMmC,QAAQvB,mBACrLZ,EAAMuE,cAAe,OAgBvB,IAXKvE,EAAMwE,QAAQC,cACnBzE,EAAMsD,mBAAmB0B,eAAe,MAAQ/B,IC9H7BkB,ED8H4DnE,EAAMsD,mBAAmB3E,GAAK,EC9HtFyF,ED8HyFpE,EAAMsD,mBAAmBzE,GAAK,EC9HnHwF,ED8HsHhB,EAAa1E,EC9H/H2F,ED8HkIjB,EAAaxE,EC7H3K+F,KAAKK,KAAKL,KAAKM,IAAIN,KAAKO,IAAId,EAAKF,GAAK,GAAKS,KAAKM,IAAIN,KAAKO,IAAIb,EAAKF,GAAK,KD6H0GpE,EAAMmC,QAAQxB,UAAYX,EAAMmC,QAAQxB,UAAY,MACtOX,EAAMiD,wBAAqBhF,EAE3B+B,EAAMoF,QAAQC,UAAUpC,EAAoB,CAC1CI,aAAcrD,EAAMsD,mBACpBlB,sBAAuBpC,EAAMoC,sBAC7BkD,eAAe,KAIdtF,EAAMwE,QAAQC,aAAnB,CAIA,IAAIc,EAAavF,EAAM+C,YAAY/C,EAAMwE,QAAQgB,eAEjDxF,EAAMyF,iCAAiCF,GAEvCvF,EAAMoF,QAAQM,oBAEd1H,EAAE2H,iBAEF,IAAIC,GAAuB/B,GAAqB,IAAIgC,KAAI,SAAUlG,GAChE,OAAOK,EAAM8F,YAAYnG,MAGvBoG,EAAkB/F,EAAMmC,QAAQ6D,6BAA+BhG,EAAMmC,QAAQ6D,6BAA6B3C,EAAa1E,EAAG0E,EAAaxE,EAAG+G,GAAuB5F,EAAMqB,SAAS4E,kBAAkB5C,EAAa1E,EAAG0E,EAAaxE,GAE/NqH,EAA0B,GAE9B,IAAK,IAAIC,KAAUJ,EAEjB,GAAKA,EAAgBf,eAAemB,GAApC,CAIA,IAAIC,EAAcL,EAAgBI,GAGlC,IAFAD,EAAwBG,KAAKD,GAEtBA,GACLA,EAAcA,EAAY3D,eAE4B,IAAlDyD,EAAwBI,QAAQF,IAClCF,EAAwBG,KAAKD,GAKnC,IAAIG,EAA2BL,EAC9BM,QAAO,SAAUlE,GAChB,OAAOsD,EAAoBU,QAAQhE,IAAS,KAE7CuD,KAAI,SAAUvD,GACb,IAAK,IAAI0B,KAAYhE,EAAM8F,YACzB,GAAIxD,IAAStC,EAAM8F,YAAY9B,GAC7B,OAAOA,KAMZwC,QAAO,SAAUlE,GAChB,QAASA,KACRkE,QAAO,SAAUC,EAAIC,EAAOC,GAC7B,OAAOA,EAAIL,QAAQG,KAAQC,KAG7B,GAAIhG,EACF,IAAK,IAAIsD,KAAYhE,EAAM8F,YACzB,GAAI9F,EAAM8F,YAAY9B,IAAahE,EAAM8F,YAAY9B,GAAU4C,SAASrB,KAA+D,IAAhDgB,EAAyBD,QAAQtC,GAAkB,CACxIuC,EAAyBpD,QAAQa,GACjC,MAMNuC,EAAyBM,UAEzB7G,EAAMoF,QAAQ0B,MAAMP,EAA0B,CAC5ClD,aAAcA,OAIlBpD,KAAK8G,wBAA0B,SAAU/I,GACvCgC,EAAMuE,cAAe,EACrBvE,EAAM1B,6BAA0BL,EJvL/B,SAA4BD,GAGjC,YAAqBC,IAAdD,EAAEgJ,SAA6D,IAAnChJ,EAAEgJ,QAAUnJ,GIsLtCoJ,CAAmBjJ,KAInBgC,EAAMwE,QAAQC,eAAgBzE,EAAMwE,QAAQ0C,WAKjDlJ,EAAE2H,iBACF3F,EAAMsD,mBAAqB,GAE3BtD,EAAMmH,qCAENnH,EAAMoF,QAAQgC,OAEdpH,EAAMoF,QAAQiC,WAXZrH,EAAMiD,wBAAqBhF,IAc/BgC,KAAKqH,qBAAuB,SAAUtJ,GACtB,WAAVA,EAAE2B,KAAoBK,EAAMwE,QAAQC,eACtCzE,EAAMsD,mBAAqB,GAE3BtD,EAAMmH,qCAENnH,EAAMoF,QAAQiC,YAIlBpH,KAAKkC,QAAU,IAAItC,EAAcsC,EAASpC,GAC1CE,KAAKmF,QAAUlD,EAAQqF,aACvBtH,KAAKuE,QAAUtC,EAAQsF,aACvBvH,KAAK8C,YAAc,GACnB9C,KAAKwH,mBAAqB,GAC1BxH,KAAKyH,yBAA2B,GAChCzH,KAAK6F,YAAc,GACnB7F,KAAK0H,cAAgB,GACrB1H,KAAKqD,mBAAqB,GAC1BrD,KAAKsE,cAAe,EAEhBtE,KAAKkC,QAAQ5B,mBACfN,KAAK0H,cAActB,KAAKzI,EAAa6D,OAGnCxB,KAAKkC,QAAQ7B,mBACfL,KAAK0H,cAActB,KAAKzI,EAAakE,OAGnC7B,KAAKkC,QAAQ3B,sBACfP,KAAK0H,cAActB,KAAKzI,EAAamE,UAjQ3C,IAAsB5B,EAAae,EAAYC,EAkf7C,OAlfoBhB,EAsQP8B,GAtQoBf,EAsQN,CAAC,CAC1BvB,IAAK,QACL4B,MAAO,WACAtB,KAAKmB,SAIVwG,aAAW3F,EAAa4F,QAAS,oDACjC5F,EAAa4F,SAAU,EACvB5H,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,QAASnB,KAAK6H,0BACjD7H,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,QAASnB,KAAK+C,2BAA2B,GAC5E/C,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,OAAQnB,KAAKgE,eAChDhE,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,OAAQnB,KAAK2D,sBAAsB,GACtE3D,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,MAAOnB,KAAK8G,yBAAyB,GAEpE9G,KAAKkC,QAAQ5B,oBAAsBN,KAAKkC,QAAQ1B,mBAClDR,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,cAAenB,KAAK8G,yBAGrD9G,KAAKkC,QAAQ3B,sBACfP,KAAKhB,iBAAiBgB,KAAKmB,OAAQ,UAAWnB,KAAKqH,sBAAsB,MAG5E,CACD3H,IAAK,WACL4B,MAAO,WACAtB,KAAKmB,SAIVa,EAAa4F,SAAU,EACvB5H,KAAKqD,mBAAqB,GAC1BrD,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,QAASnB,KAAK+C,2BAA2B,GAC/E/C,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,QAASnB,KAAKmD,oBACpDnD,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,OAAQnB,KAAK2D,sBAAsB,GACzE3D,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,OAAQnB,KAAKgE,eACnDhE,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,MAAOnB,KAAK8G,yBAAyB,GAEvE9G,KAAKkC,QAAQ5B,oBAAsBN,KAAKkC,QAAQ1B,mBAClDR,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,cAAenB,KAAK8G,yBAGxD9G,KAAKkC,QAAQ3B,sBACfP,KAAK8H,oBAAoB9H,KAAKmB,OAAQ,UAAWnB,KAAKqH,sBAAsB,GAG9ErH,KAAKkH,wCAEN,CACDxH,IAAK,mBACL4B,MAAO,SAA0ByG,EAASC,EAAOC,EAASC,GACxD,IAAIhG,EAAUpD,EAAkB,CAC9BoJ,QAASA,EACTC,SAAS,GACPD,EACJlI,KAAK0H,cAAc1G,SAAQ,SAAUoH,GACnC,IAAIC,EAAM9G,EAAW6G,GAAcJ,GAE/BK,GACFN,EAAQ/I,iBAAiBqJ,EAAKJ,EAAS/F,QAI5C,CACDxC,IAAK,sBACL4B,MAAO,SAA6ByG,EAASC,EAAOC,EAASC,GAC3D,IAAIhG,EAAUpD,EAAkB,CAC9BoJ,QAASA,EACTC,SAAS,GACPD,EACJlI,KAAK0H,cAAc1G,SAAQ,SAAUoH,GACnC,IAAIC,EAAM9G,EAAW6G,GAAcJ,GAE/BK,GACFN,EAAQD,oBAAoBO,EAAKJ,EAAS/F,QAI/C,CACDxC,IAAK,oBACL4B,MAAO,SAA2Bc,EAAUC,GAC1C,IAAIiG,EAAStI,KAETiD,EAAkBjD,KAAKiD,gBAAgBS,KAAK1D,KAAMoC,GAGtD,OAFApC,KAAK8C,YAAYV,GAAYC,EAC7BrC,KAAKhB,iBAAiBqD,EAAM,QAASY,GAC9B,kBACEqF,EAAOxF,YAAYV,GAE1BkG,EAAOR,oBAAoBzF,EAAM,QAASY,MAG7C,CACDvD,IAAK,qBACL4B,MAAO,SAA4Bc,EAAUC,EAAMH,GACjD,IAAIqG,EAASvI,KAIb,OAFAA,KAAKyH,yBAAyBrF,GAAYF,EAC1ClC,KAAKwH,mBAAmBpF,GAAYC,EAC7B,kBACEkG,EAAOf,mBAAmBpF,UAC1BmG,EAAOd,yBAAyBrF,MAG1C,CACD1C,IAAK,oBACL4B,MAAO,SAA2ByC,EAAU1B,GAC1C,IAAImG,EAASxI,KAEb,IAAKA,KAAKoB,SACR,OAAO,WACL,OAAO,MAIX,IAAIyC,EAAa,SAAoB9F,GACnC,GAAKyK,EAAOpH,UAAaoH,EAAOjE,QAAQC,aAAxC,CAIA,IAAIiE,EAKJ,OAAQ1K,EAAEV,MACR,KAAKkE,EAAWC,MAAME,KACpB+G,EAAS,CACP/J,EAAGX,EAAEY,QACLC,EAAGb,EAAEc,SAEP,MAEF,KAAK0C,EAAWM,MAAMH,KACpB+G,EAAS,CACP/J,EAAGX,EAAEQ,QAAQ,GAAGI,QAChBC,EAAGb,EAAEQ,QAAQ,GAAGM,SAUtB,IAAI6J,EAAsB,MAAVD,EAAiBD,EAAOpH,SAASuH,iBAAiBF,EAAO/J,EAAG+J,EAAO7J,QAAKZ,EACpF4K,EAAaF,GAAarG,EAAKsE,SAAS+B,GAE5C,OAAIA,IAAcrG,GAAQuG,EACjBJ,EAAO3E,WAAW9F,EAAGgG,QAD9B,IAWF,OAFA/D,KAAKhB,iBAAiBgB,KAAKoB,SAASyH,KAAM,OAAQhF,GAClD7D,KAAK6F,YAAY9B,GAAY1B,EACtB,WACDmG,EAAOpH,kBACFoH,EAAO3C,YAAY9B,GAE1ByE,EAAOV,oBAAoBU,EAAOpH,SAASyH,KAAM,OAAQhF,OAI9D,CACDnE,IAAK,yBACL4B,MAAO,WACL,OAAKtB,KAAKkC,QAAQtB,iBAAoBZ,KAAKkC,QAAQpB,gBAI5Cd,KAAKuD,wBAHHvD,KAAKmD,qBAKf,CACDzD,IAAK,mCACL4B,MAAO,SAA0Ce,GAC/C,IAAIyG,EAAS9I,KAEbA,KAAKkH,qCACLlH,KAAK+I,kBAAoB1G,EACzBrC,KAAKgJ,iCAAmC,IAAIC,kBAAiB,WACvD5G,IAASA,EAAKG,gBAChBsG,EAAOI,sBAEPJ,EAAO5B,yCAIN7E,GAASA,EAAKG,eAInBxC,KAAKgJ,iCAAiCG,QAAQ9G,EAAKG,cAAe,CAChE4G,WAAW,MAGd,CACD1J,IAAK,sBACL4B,MAAO,WACDtB,KAAKoB,UAAYpB,KAAK+I,oBACxB/I,KAAK+I,kBAAkBM,MAAMC,QAAU,OACvCtJ,KAAK+I,kBAAkBQ,gBAAgB,gBACvCvJ,KAAKoB,SAASyH,KAAKW,YAAYxJ,KAAK+I,sBAGvC,CACDrJ,IAAK,qCACL4B,MAAO,WACDtB,KAAKgJ,kCACPhJ,KAAKgJ,iCAAiCS,aAGxCzJ,KAAKgJ,sCAAmChL,EACxCgC,KAAK+I,uBAAoB/K,IAE1B,CACD0B,IAAK,SACLR,IAAK,WACH,OAAOc,KAAKkC,QAAQf,SAGrB,CACDzB,IAAK,WACLR,IAAK,WACH,GAAIc,KAAKmB,OACP,OAAOnB,KAAKmB,OAAOC,cA3emDjC,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAkf3Kc,EAxdT,GEzBe0H,IALK,SAAuBzH,EAASnC,GAClD,IAAIoC,EAAUyH,UAAUrL,OAAS,QAAsBN,IAAjB2L,UAAU,GAAmBA,UAAU,GAAK,GAClF,OAAO,IAAI3H,EAAaC,EAASnC,EAASoC,K,4PCJ5C,SAAS9B,EAAgBH,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAEhH,SAAShB,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAE7S,SAASsK,EAAa1J,EAAae,EAAYC,GAAmJ,OAAhID,GAAY9B,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAAqBhB,EAEzM,SAASmB,EAAgB/F,EAAKoE,EAAK4B,GAAiK,OAApJ5B,KAAOpE,EAAO2B,OAAOgC,eAAe3D,EAAKoE,EAAK,CAAE4B,MAAOA,EAAO/B,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBnE,EAAIoE,GAAO4B,EAAgBhG,EASpM,IAAIuO,EAA0B,WACnC,SAASA,EAAWC,GAClB1J,EAAgBJ,KAAM6J,GAEtBxI,EAAgBrB,KAAM,cAAc,GAEpCqB,EAAgBrB,KAAM,cAAU,GAEhCA,KAAK8J,OAASlN,YAAWkN,GAAUA,EAAShN,IA+C9C,OA1CA8M,EAAaC,EAAY,CAAC,CACxBnK,IAAK,UACL4B,MAAO,WACAtB,KAAK+J,aACR/J,KAAK8J,SACL9J,KAAK+J,YAAa,MAGpB,CAAC,CACHrK,IAAK,eACL4B,MAUA,SAAsB0I,GACpB,OAAOC,QAAQD,GAAKpN,YAAWoN,EAAEE,YAElC,CACDxK,IAAK,SACL4B,MAAO,SAAgB6I,GACrB,OAAON,EAAWO,aAAaD,GAAUA,EAASN,EAAWQ,QAS9D,CACD3K,IAAK,SACL4B,MAAO,SAAgBwI,GACrB,OAAO,IAAID,EAAWC,OAInBD,EAvD4B,GA8DrCxI,EAAgBwI,EAAY,QAAS,CACnCK,QAASpN,MAGJ,IAAIwN,EAAmC,WAC5C,SAASA,IACPlK,EAAgBJ,KAAMsK,GAEtBjJ,EAAgBrB,KAAM,cAAc,GAEpCqB,EAAgBrB,KAAM,mBAAe,GAErC,IAAK,IAAIuK,EAAOZ,UAAUrL,OAAQkM,EAAc,IAAIjN,MAAMgN,GAAOE,EAAO,EAAGA,EAAOF,EAAME,IACtFD,EAAYC,GAAQd,UAAUc,GAGhCzK,KAAKwK,YAAcA,EAwFrB,OAhFAZ,EAAaU,EAAqB,CAAC,CACjC5K,IAAK,MACL4B,MAAO,SAAajF,GACd2D,KAAK+J,WACP1N,EAAK6N,UAELlK,KAAKwK,YAAYpE,KAAK/J,KASzB,CACDqD,IAAK,SACL4B,MAAO,SAAgBjF,GACrB,IAAIqO,GAAgB,EAEpB,IAAK1K,KAAK+J,WAAY,CACpB,IAAIY,EAAM3K,KAAKwK,YAAYnE,QAAQhK,IAEtB,IAATsO,IACFD,GAAgB,EAChB1K,KAAKwK,YAAYI,OAAOD,EAAK,GAC7BtO,EAAK6N,WAIT,OAAOQ,IAOR,CACDhL,IAAK,QACL4B,MAAO,WACL,IAAKtB,KAAK+J,WAAY,CAIpB,IAHA,IAAIc,EAAM7K,KAAKwK,YAAYlM,OACvBwM,EAAqB,IAAIvN,MAAMsN,GAE1BxL,EAAI,EAAGA,EAAIwL,EAAKxL,IACvByL,EAAmBzL,GAAKW,KAAKwK,YAAYnL,GAG3CW,KAAKwK,YAAc,GAEnB,IAAK,IAAIO,EAAK,EAAGA,EAAKF,EAAKE,IACzBD,EAAmBC,GAAIb,aAQ5B,CACDxK,IAAK,UACL4B,MAAO,WACL,IAAKtB,KAAK+J,WAAY,CACpB/J,KAAK+J,YAAa,EAIlB,IAHA,IAAIc,EAAM7K,KAAKwK,YAAYlM,OACvBwM,EAAqB,IAAIvN,MAAMsN,GAE1BxL,EAAI,EAAGA,EAAIwL,EAAKxL,IACvByL,EAAmBzL,GAAKW,KAAKwK,YAAYnL,GAG3CW,KAAKwK,YAAc,GAEnB,IAAK,IAAIQ,EAAM,EAAGA,EAAMH,EAAKG,IAC3BF,EAAmBE,GAAKd,eAMzBI,EApGqC,GA4GnCW,EAAgC,WACzC,SAASA,IACP7K,EAAgBJ,KAAMiL,GAEtB5J,EAAgBrB,KAAM,cAAc,GAEpCqB,EAAgBrB,KAAM,eAAW,GAgDnC,OA7CA4J,EAAaqB,EAAkB,CAAC,CAC9BvL,IAAK,gBACL4B,MAKA,WACE,OAAOtB,KAAKlE,UAEb,CACD4D,IAAK,gBACL4B,MAAO,SAAuBA,GAC5B,IAAIoJ,EAAgB1K,KAAK+J,WAEzB,IAAKW,EAAe,CAClB,IAAIQ,EAAMlL,KAAKlE,QACfkE,KAAKlE,QAAUwF,EAEX4J,GACFA,EAAIhB,UAIJQ,GAAiBpJ,GACnBA,EAAM4I,YAKT,CACDxK,IAAK,UACL4B,MAAO,WACL,IAAKtB,KAAK+J,WAAY,CACpB/J,KAAK+J,YAAa,EAClB,IAAImB,EAAMlL,KAAKlE,QACfkE,KAAKlE,aAAUkC,EAEXkN,GACFA,EAAIhB,eAMLe,EAtDkC,G,kBC7L3C,SAAS5P,EAAQC,GAAmV,OAAtOD,EAArD,mBAAXE,QAAoD,iBAApBA,OAAOC,SAAmC,SAAiBF,GAAO,cAAcA,GAA2B,SAAiBA,GAAO,OAAOA,GAAyB,mBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,IAAyBA,GAEnX,SAAS6P,EAAeC,EAAK/L,GAAK,OAUlC,SAAyB+L,GAAO,GAAI7N,MAAMC,QAAQ4N,GAAM,OAAOA,EAVtBC,CAAgBD,IAQzD,SAA+BA,EAAK/L,GAAK,IAAI0L,EAAY,MAAPK,EAAc,KAAyB,oBAAX7P,QAA0B6P,EAAI7P,OAAOC,WAAa4P,EAAI,cAAe,GAAU,MAANL,EAAY,OAAQ,IAAkDO,EAAIC,EAAlDC,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAmB,IAAM,IAAKX,EAAKA,EAAGY,KAAKP,KAAQK,GAAMH,EAAKP,EAAGa,QAAQC,QAAoBL,EAAKpF,KAAKkF,EAAGhK,QAAYjC,GAAKmM,EAAKlN,SAAWe,GAA3DoM,GAAK,IAAoE,MAAOK,GAAOJ,GAAK,EAAMH,EAAKO,EAAO,QAAU,IAAWL,GAAsB,MAAhBV,EAAW,QAAWA,EAAW,SAAO,QAAU,GAAIW,EAAI,MAAMH,GAAQ,OAAOC,EARzbO,CAAsBX,EAAK/L,IAI5F,SAAqC2M,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,EAAkBF,EAAGC,GAAS,IAAIE,EAAIlP,OAAOvB,UAAUa,SAASoP,KAAKK,GAAGI,MAAM,GAAI,GAAc,WAAND,GAAkBH,EAAEvQ,cAAa0Q,EAAIH,EAAEvQ,YAAY4Q,MAAM,GAAU,QAANF,GAAqB,QAANA,EAAa,OAAO5O,MAAM+O,KAAKN,GAAI,GAAU,cAANG,GAAqB,2CAA2CI,KAAKJ,GAAI,OAAOD,EAAkBF,EAAGC,GAJpTO,CAA4BpB,EAAK/L,IAEnI,WAA8B,MAAM,IAAIc,UAAU,6IAFuFsM,GAMzI,SAASP,EAAkBd,EAAKP,IAAkB,MAAPA,GAAeA,EAAMO,EAAI9M,UAAQuM,EAAMO,EAAI9M,QAAQ,IAAK,IAAIe,EAAI,EAAGqN,EAAO,IAAInP,MAAMsN,GAAMxL,EAAIwL,EAAKxL,IAAOqN,EAAKrN,GAAK+L,EAAI/L,GAAM,OAAOqN,EAQhL,SAASvN,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAM7S,SAASqN,EAAgBX,EAAGY,GAA+G,OAA1GD,EAAkB1P,OAAO4P,gBAAkB,SAAyBb,EAAGY,GAAsB,OAAjBZ,EAAEc,UAAYF,EAAUZ,IAA6BA,EAAGY,GAErK,SAASG,EAAaC,GAAW,IAAIC,EAMrC,WAAuC,GAAuB,oBAAZC,UAA4BA,QAAQC,UAAW,OAAO,EAAO,GAAID,QAAQC,UAAUC,KAAM,OAAO,EAAO,GAAqB,mBAAVC,MAAsB,OAAO,EAAM,IAAsF,OAAhFpD,QAAQvO,UAAU4R,QAAQ3B,KAAKuB,QAAQC,UAAUlD,QAAS,IAAI,iBAAyB,EAAQ,MAAOlM,GAAK,OAAO,GAN9PwP,GAA6B,OAAO,WAAkC,IAAsCpD,EAAlCqD,EAAQC,EAAgBT,GAAkB,GAAIC,EAA2B,CAAE,IAAIS,EAAYD,EAAgBzN,MAAMvE,YAAa0O,EAAS+C,QAAQC,UAAUK,EAAO7D,UAAW+D,QAAqBvD,EAASqD,EAAMG,MAAM3N,KAAM2J,WAAc,OAAOiE,EAA2B5N,KAAMmK,IAE5Z,SAASyD,EAA2BC,EAAMlC,GAAQ,GAAIA,IAA2B,WAAlBtQ,EAAQsQ,IAAsC,mBAATA,GAAwB,OAAOA,EAAa,QAAa,IAATA,EAAmB,MAAM,IAAIxL,UAAU,4DAA+D,OAAO2N,EAAuBD,GAExR,SAASC,EAAuBD,GAAQ,QAAa,IAATA,EAAmB,MAAM,IAAIE,eAAe,6DAAgE,OAAOF,EAI/J,SAASJ,EAAgBzB,GAAwJ,OAAnJyB,EAAkBxQ,OAAO4P,eAAiB5P,OAAOC,eAAiB,SAAyB8O,GAAK,OAAOA,EAAEc,WAAa7P,OAAOC,eAAe8O,KAA8BA,GAExM,SAAS3K,EAAgB/F,EAAKoE,EAAK4B,GAAiK,OAApJ5B,KAAOpE,EAAO2B,OAAOgC,eAAe3D,EAAKoE,EAAK,CAAE4B,MAAOA,EAAO/B,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBnE,EAAIoE,GAAO4B,EAAgBhG,EAWpM,SAAS0S,EAAgBC,GAC9B,IAAIC,EAAqBD,EAAKC,mBAC1BC,EAAgBF,EAAKE,cACrBC,EAAgBH,EAAKG,cACrBC,EAAkBJ,EAAKI,gBACvBC,EAAkBL,EAAKK,gBACvBC,EAAuBN,EAAKM,qBAC5BC,EAAUP,EAAKO,QACfC,EAAUR,EAAKQ,QAEfC,EADUT,EAAK/L,QACiByM,cAChCA,OAA0C,IAA1BD,EAAmCE,IAAeF,EAClEG,EAAYX,EACZY,EAAcZ,EAAmBY,aAAeZ,EAAmB7B,MAAQ,YAE3E0C,EAAiC,SAAUC,IAxCjD,SAAmBC,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAI/O,UAAU,sDAAyD8O,EAASvT,UAAYuB,OAAOkS,OAAOD,GAAcA,EAAWxT,UAAW,CAAED,YAAa,CAAE6F,MAAO2N,EAAUxP,UAAU,EAAMD,cAAc,KAAe0P,GAAYvC,EAAgBsC,EAAUC,GAyC/WE,CAAUL,EAAmBC,GAE7B,IA7CkB9O,EAAae,EAAYC,EA6CvCmO,EAAStC,EAAagC,GAE1B,SAASA,EAAkB3P,GACzB,IAAIW,EAoCJ,OAxFN,SAAyBE,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAsD1GC,CAAgBJ,KAAM+O,GAItB1N,EAAgByM,EAFhB/N,EAAQsP,EAAO1D,KAAK3L,KAAMZ,IAEqB,eAAgBkQ,uBAE/DjO,EAAgByM,EAAuB/N,GAAQ,iBAAa,GAE5DsB,EAAgByM,EAAuB/N,GAAQ,eAAW,GAE1DsB,EAAgByM,EAAuB/N,GAAQ,sBAAkB,GAEjEsB,EAAgByM,EAAuB/N,GAAQ,wBAAoB,GAEnEsB,EAAgByM,EAAuB/N,GAAQ,eAAW,GAE1DsB,EAAgByM,EAAuB/N,GAAQ,kBAAc,GAE7DsB,EAAgByM,EAAuB/N,GAAQ,mBAAe,GAE9DsB,EAAgByM,EAAuB/N,GAAQ,gBAAgB,WAC7D,IAAIwP,EAAYxP,EAAMyP,kBAEjBZ,YAAaW,EAAWxP,EAAM0P,QACjC1P,EAAM2P,SAASH,MAInBxP,EAAM4P,WAAa,IAAI1E,EAEvBlL,EAAM6P,aAAaxQ,GAEnBW,EAAMmK,UAECnK,EAmJT,OAvOkBG,EAuFL6O,GAvFkB9N,EAuFC,CAAC,CAC/BvB,IAAK,eACL4B,MAAO,WACL,OAAOtB,KAAK6P,YAEb,CACDnQ,IAAK,gCACL4B,MAAO,WAEL,OADAqG,YAAU3H,KAAKjE,aAAaD,QAAS,4HAC9BkE,KAAKjE,aAAaD,UAE1B,CACD4D,IAAK,wBACL4B,MAAO,SAA+BwO,EAAWP,GAC/C,OAAQZ,EAAcmB,EAAW9P,KAAKZ,SAAWwP,YAAaW,EAAWvP,KAAKyP,SAE/E,CACD/P,IAAK,oBACL4B,MAAO,WACLtB,KAAK2P,WAAa,IAAI1E,EACtBjL,KAAK+P,iBAAc/R,EACnBgC,KAAK4P,aAAa5P,KAAKZ,OACvBY,KAAKgQ,iBAEN,CACDtQ,IAAK,qBACL4B,MAAO,SAA4B2O,GAC5BtB,EAAc3O,KAAKZ,MAAO6Q,KAC7BjQ,KAAK4P,aAAa5P,KAAKZ,OACvBY,KAAKgQ,kBAGR,CACDtQ,IAAK,uBACL4B,MAAO,WACLtB,KAAKkK,YAEN,CACDxK,IAAK,eACL4B,MAAO,SAAsBlC,GACtBY,KAAKiI,UAIVjI,KAAKiI,QAAQ2H,aAAaxQ,GAC1BY,KAAKkQ,YAAY1B,EAAQpP,OAE1B,CACDM,IAAK,cACL4B,MAAO,SAAqBjE,GAC1B,GAAK2C,KAAKmQ,gBAAmBnQ,KAAKiC,SAAYjC,KAAKoQ,kBAI/C/S,IAAS2C,KAAK+P,YAAlB,CAIA/P,KAAK+P,YAAc1S,EAEnB,IACIgT,EAAoBlF,EADDmD,EAAgBjR,EAAM2C,KAAKiI,QAASjI,KAAKiC,SACP,GACrD4N,EAAYQ,EAAkB,GAC9BC,EAAaD,EAAkB,GAEnCrQ,KAAK6P,UAAYA,EACjB7P,KAAKmQ,eAAeI,iBAAiBV,GACrC7P,KAAKoQ,iBAAiBG,iBAAiBV,GACvC,IACIW,EADgBxQ,KAAKiC,QAAQsF,aACDkJ,uBAAuBzQ,KAAKgQ,aAAc,CACxEU,WAAY,CAACb,KAEf7P,KAAK2P,WAAWgB,cAAc,IAAIrG,EAAoB,IAAIT,EAAW2G,GAAc,IAAI3G,EAAWyG,QAEnG,CACD5Q,IAAK,UACL4B,MAAO,WACLtB,KAAK2P,WAAWzF,UAEZlK,KAAKoQ,kBACPpQ,KAAKoQ,iBAAiBG,iBAAiB,QAG1C,CACD7Q,IAAK,kBACL4B,MAAO,WACL,OAAKtB,KAAKoQ,iBAIM3B,EAAQzO,KAAKoQ,iBAAiBQ,MAAO5Q,KAAKmQ,eAAgBnQ,KAAKZ,OAHtE,KAWV,CACDM,IAAK,SACL4B,MAAO,WACL,IAAIgH,EAAStI,KAEb,OAAO6Q,cAAKC,IAAWC,SAAU,CAC/BC,SAAU,SAAkBC,GAC1B,IAAIC,EAAkBD,EAAMC,gBAY5B,OAVA5I,EAAO6I,uBAAuBD,GAEO,oBAA1BE,uBACTA,uBAAsB,WACpB,IAAIC,EAEJ,OAA6D,QAArDA,EAAwB/I,EAAO8H,wBAAwD,IAA1BiB,OAAmC,EAASA,EAAsBC,eAIpIT,cAAKhC,EAAW5R,OAAOsU,OAAO,GAAIjJ,EAAOlJ,MAAOkJ,EAAOkH,kBAAmB,CAE/EgC,IAAKxV,YAAU6S,GAAavG,EAAOvM,aAAe,YAChD,UAEL,KAEJ,CACD2D,IAAK,yBACL4B,MAAO,SAAgC4P,QAChBlT,IAAjBgC,KAAKiC,UAIT0F,iBAA8B3J,IAApBkT,EAA+B,uQAAkRpC,EAAaA,QAEhT9Q,IAApBkT,IAIJlR,KAAKiC,QAAUiP,EACflR,KAAKmQ,eAAiB/B,EAAc8C,GACpClR,KAAKoQ,iBAAmB/B,EAAgB6C,EAAgBO,cACxDzR,KAAKiI,QAAUkG,EAAcnO,KAAKmQ,eAAgBnQ,KAAKjE,qBAnOeoD,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAuOzK6N,EA7L4B,CA8LnC7S,aAMF,OAJAmF,EAAgB0N,EAAmB,qBAAsBb,GAEzD7M,EAAgB0N,EAAmB,cAAe,GAAG2C,OAAOnD,EAAsB,KAAKmD,OAAO5C,EAAa,MAEpG6C,IAAa5C,EAAmBb,K,yKChQzC,SAAS/C,EAAeC,EAAK/L,GAAK,OAUlC,SAAyB+L,GAAO,GAAI7N,MAAMC,QAAQ4N,GAAM,OAAOA,EAVtBC,CAAgBD,IAQzD,SAA+BA,EAAK/L,GAAK,IAAI0L,EAAY,MAAPK,EAAc,KAAyB,oBAAX7P,QAA0B6P,EAAI7P,OAAOC,WAAa4P,EAAI,cAAe,GAAU,MAANL,EAAY,OAAQ,IAAkDO,EAAIC,EAAlDC,EAAO,GAAQC,GAAK,EAAUC,GAAK,EAAmB,IAAM,IAAKX,EAAKA,EAAGY,KAAKP,KAAQK,GAAMH,EAAKP,EAAGa,QAAQC,QAAoBL,EAAKpF,KAAKkF,EAAGhK,QAAYjC,GAAKmM,EAAKlN,SAAWe,GAA3DoM,GAAK,IAAoE,MAAOK,GAAOJ,GAAK,EAAMH,EAAKO,EAAO,QAAU,IAAWL,GAAsB,MAAhBV,EAAW,QAAWA,EAAW,SAAO,QAAU,GAAIW,EAAI,MAAMH,GAAQ,OAAOC,EARzbO,CAAsBX,EAAK/L,IAI5F,SAAqC2M,EAAGC,GAAU,IAAKD,EAAG,OAAQ,GAAiB,iBAANA,EAAgB,OAAOE,EAAkBF,EAAGC,GAAS,IAAIE,EAAIlP,OAAOvB,UAAUa,SAASoP,KAAKK,GAAGI,MAAM,GAAI,GAAc,WAAND,GAAkBH,EAAEvQ,cAAa0Q,EAAIH,EAAEvQ,YAAY4Q,MAAM,GAAU,QAANF,GAAqB,QAANA,EAAa,OAAO5O,MAAM+O,KAAKN,GAAI,GAAU,cAANG,GAAqB,2CAA2CI,KAAKJ,GAAI,OAAOD,EAAkBF,EAAGC,GAJpTO,CAA4BpB,EAAK/L,IAEnI,WAA8B,MAAM,IAAIc,UAAU,6IAFuFsM,GAMzI,SAASP,EAAkBd,EAAKP,IAAkB,MAAPA,GAAeA,EAAMO,EAAI9M,UAAQuM,EAAMO,EAAI9M,QAAQ,IAAK,IAAIe,EAAI,EAAGqN,EAAO,IAAInP,MAAMsN,GAAMxL,EAAIwL,EAAKxL,IAAOqN,EAAKrN,GAAK+L,EAAI/L,GAAM,OAAOqN,EAczK,SAASkF,EAAanD,GAC3B,IACIlK,EADkBsN,cACQtK,aAG1BuK,EAAiB3G,EADD4G,YAAaxN,EAASkK,GACS,GAC/CuD,EAAYF,EAAe,GAC3BG,EAAkBH,EAAe,GAQrC,OANAI,qBAAU,WACR,OAAO3N,EAAQ4N,wBAAwBF,MAEzCC,qBAAU,WACR,OAAO3N,EAAQkM,uBAAuBwB,MAEjCD,I,iLCjCT,SAAS7S,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAI7S,SAAS+B,EAAgB/F,EAAKoE,EAAK4B,GAAiK,OAApJ5B,KAAOpE,EAAO2B,OAAOgC,eAAe3D,EAAKoE,EAAK,CAAE4B,MAAOA,EAAO/B,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBnE,EAAIoE,GAAO4B,EAAgBhG,EAI3M,IAAI8W,EAAuB,CAAC,UAAW,QAAS,QAE5CC,EAA0B,WAC5B,SAASA,EAAWC,EAAM/N,EAASiN,IAbrC,SAAyBvR,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAc5GC,CAAgBJ,KAAMqS,GAEtBhR,EAAgBrB,KAAM,QAAS,MAE/BqB,EAAgBrB,KAAM,YAAQ,GAE9BqB,EAAgBrB,KAAM,eAAW,GAEjCqB,EAAgBrB,KAAM,WAAO,GAE7BA,KAAKsS,KAAOA,EACZtS,KAAKuE,QAAUA,EACfvE,KAAKwR,IAAMA,EAtBf,IAAsBtR,EAAae,EAAYC,EAsE7C,OAtEoBhB,EAyBPmS,GAzBoBpR,EAyBR,CAAC,CACxBvB,IAAK,eACL4B,MAAO,SAAsBlC,GAC3BY,KAAKZ,MAAQA,IAEd,CACDM,IAAK,iBACL4B,MAAO,SAAwBiD,GAC7BvE,KAAKuE,QAAUA,IAEhB,CACD7E,IAAK,UACL4B,MAAO,WACL,OAAKtB,KAAKsS,KAAKC,SAIRvS,KAAKsS,KAAKC,QAAQvS,KAAKZ,MAAOY,KAAKuE,WAE3C,CACD7E,IAAK,QACL4B,MAAO,WACAtB,KAAKsS,KAAKzL,OAAU7G,KAAKZ,OAI9BY,KAAKsS,KAAKzL,MAAM7G,KAAKZ,MAAOY,KAAKuE,QAAS5I,YAAsBqE,KAAKwR,QAEtE,CACD9R,IAAK,OACL4B,MAAO,WACL,GAAKtB,KAAKsS,KAAKnL,KAUf,OANiBnH,KAAKsS,KAAKnL,KAAKnH,KAAKZ,MAAOY,KAAKuE,QAASvE,KAAKwR,IAAI1V,cA5DKqD,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAsE3KmR,EA9DqB,GAiEvB,SAASG,EAAoBF,GAKlC,OAJArV,OAAO8D,KAAKuR,GAAMtR,SAAQ,SAAUtB,GAClCiI,YAAUyK,EAAqB/L,QAAQ3G,IAAQ,EAAG,yNAAyO0S,EAAqBK,KAAK,MAAO/S,GAC5TiI,YAA+B,mBAAd2K,EAAK5S,GAAqB,oLAA+LA,EAAKA,EAAK4S,EAAK5S,OAEpP,SAAsB6E,EAASiN,GACpC,OAAO,IAAIa,EAAWC,EAAM/N,EAASiN,ICtElC,SAASkB,EAAWrV,EAAMiV,EAAM7D,GACrC,IAAIvM,EAAUyH,UAAUrL,OAAS,QAAsBN,IAAjB2L,UAAU,GAAmBA,UAAU,GAAK,GAClFlN,YAAwB,aAAc,iCAAkCY,EAAMiV,EAAM7D,EAASvM,GAC7F,IAAIsM,EAAUnR,EAEM,mBAATA,IACTsK,YAAUvK,YAAYC,GAAM,GAAO,0PAA0QA,GAE7SmR,EAAU,WACR,OAAOnR,IAIXsK,YAAU5K,YAAcuV,GAAO,iLAA4LA,GAC3N,IAAIK,EAAeH,EAAoBF,GAGvC,OAFA3K,YAA6B,mBAAZ8G,EAAwB,8NAA8OA,GACvR9G,YAAU5K,YAAcmF,GAAU,mMAAmNuM,GAC9O,SAAwBP,GAC7B,OAAOF,YAAgB,CACrBO,qBAAsB,aACtBJ,cAAewE,EACfrE,gBAAiBsE,IACjBxE,cAAe,SAAuBnM,GACpC,OAAO,IAAI4Q,IAAsB5Q,IAEnCoM,gBAAiB,SAAyByE,GACxC,OAAO,IAAIC,IAAgBD,IAE7B5E,mBAAoBA,EACpBM,QAASA,EACTC,QAASA,EACTvM,QAASA,O,2KC1Cf,SAAS/C,EAAkBX,EAAQY,GAAS,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMd,OAAQe,IAAK,CAAE,IAAIC,EAAaF,EAAMC,GAAIC,EAAWC,WAAaD,EAAWC,aAAc,EAAOD,EAAWE,cAAe,EAAU,UAAWF,IAAYA,EAAWG,UAAW,GAAMxC,OAAOgC,eAAeT,EAAQc,EAAWI,IAAKJ,IAI7S,SAAS+B,EAAgB/F,EAAKoE,EAAK4B,GAAiK,OAApJ5B,KAAOpE,EAAO2B,OAAOgC,eAAe3D,EAAKoE,EAAK,CAAE4B,MAAOA,EAAO/B,YAAY,EAAMC,cAAc,EAAMC,UAAU,IAAkBnE,EAAIoE,GAAO4B,EAAgBhG,EAI3M,IAAI8W,EAAuB,CAAC,UAAW,YAAa,aAAc,WAC9DY,EAAwB,CAAC,aAEzBC,EAA0B,WAC5B,SAASA,EAAWX,EAAM/N,EAASiN,GACjC,IAAIzR,EAAQC,MAfhB,SAAyBC,EAAUC,GAAe,KAAMD,aAAoBC,GAAgB,MAAM,IAAIC,UAAU,qCAiB5GC,CAAgBJ,KAAMiT,GAEtB5R,EAAgBrB,KAAM,QAAS,MAE/BqB,EAAgBrB,KAAM,YAAQ,GAE9BqB,EAAgBrB,KAAM,eAAW,GAEjCqB,EAAgBrB,KAAM,WAAO,GAE7BqB,EAAgBrB,KAAM,aAAa,WACjC,GAAKD,EAAMX,MAUX,OANWW,EAAMuS,KAAKlN,UAAUrF,EAAMX,MAAOW,EAAMwE,QAASxE,EAAMyR,IAAI1V,YASxEkE,KAAKsS,KAAOA,EACZtS,KAAKuE,QAAUA,EACfvE,KAAKwR,IAAMA,EAvCf,IAAsBtR,EAAae,EAAYC,EAwF7C,OAxFoBhB,EA0CP+S,GA1CoBhS,EA0CR,CAAC,CACxBvB,IAAK,eACL4B,MAAO,SAAsBlC,GAC3BY,KAAKZ,MAAQA,IAEd,CACDM,IAAK,UACL4B,MAAO,WACL,QAAKtB,KAAKZ,SAILY,KAAKsS,KAAKY,SAIRlT,KAAKsS,KAAKY,QAAQlT,KAAKZ,MAAOY,KAAKuE,YAE3C,CACD7E,IAAK,aACL4B,MAAO,SAAoB6R,EAAe/Q,GACxC,QAAKpC,KAAKZ,QAILY,KAAKsS,KAAK9N,WAIRxE,KAAKsS,KAAK9N,WAAWxE,KAAKZ,MAAOY,KAAKuE,SAHpCnC,IAAa+Q,EAAc5N,iBAKrC,CACD7F,IAAK,UACL4B,MAAO,WACAtB,KAAKZ,OAILY,KAAKsS,KAAKlL,SAIfpH,KAAKsS,KAAKlL,QAAQpH,KAAKZ,MAAOY,KAAKuE,QAAS5I,YAAsBqE,KAAKwR,WApFCrS,EAAkBe,EAAYxE,UAAWuF,GAAiBC,GAAa/B,EAAkBe,EAAagB,GAwF3K+R,EA/EqB,GAkFvB,SAASG,EAAoBd,GAQlC,OAPArV,OAAO8D,KAAKuR,GAAMtR,SAAQ,SAAUtB,GAClCiI,YAAUyK,EAAqB/L,QAAQ3G,IAAQ,EAAG,yNAAyO0S,EAAqBK,KAAK,MAAO/S,GAC5TiI,YAA+B,mBAAd2K,EAAK5S,GAAqB,oLAA+LA,EAAKA,EAAK4S,EAAK5S,OAE3PsT,EAAsBhS,SAAQ,SAAUtB,GACtCiI,YAA+B,mBAAd2K,EAAK5S,GAAqB,oLAA+LA,EAAKA,EAAK4S,EAAK5S,OAEpP,SAAsB6E,EAASiN,GACpC,OAAO,IAAIyB,EAAWX,EAAM/N,EAASiN,IC3FlC,SAAS6B,EAAWhW,EAAMiV,EAAM7D,GACrC,IAAIvM,EAAUyH,UAAUrL,OAAS,QAAsBN,IAAjB2L,UAAU,GAAmBA,UAAU,GAAK,GAClFlN,YAAwB,aAAc,iCAAkCY,EAAMiV,EAAM7D,EAASvM,GAC7F,IAAIsM,EAAUnR,EAEM,mBAATA,IACTsK,YAAUvK,YAAYC,GAAO,uOAAuPA,GAEpRmR,EAAU,WACR,OAAOnR,IAIXsK,YAAU5K,YAAcuV,GAAO,iLAA4LA,GAC3N,IAAIgB,EAAeF,EAAoBd,GAGvC,OAFA3K,YAA6B,mBAAZ8G,EAAwB,8NAA8OA,GACvR9G,YAAU5K,YAAcmF,GAAU,mMAAmNuM,GAC9O,SAAwBP,GAC7B,OAAOF,YAAgB,CACrBO,qBAAsB,aACtBJ,cAAemF,EACfhF,gBAAiBiF,IACjBlF,gBAAiB,SAAyByE,GACxC,OAAO,IAAIU,IAAgBV,IAE7B1E,cAAe,SAAuBnM,GACpC,OAAO,IAAIwR,IAAsBxR,IAEnCiM,mBAAoBA,EACpBM,QAASA,EACTC,QAASA,EACTvM,QAASA,O,iCC5Cf,IAAIwR,EACG,SAASC,IAMd,OALKD,KACHA,EAAa,IAAIE,OACNC,IAAM,8EAGZH,EAPT", "file": "chunks/chunk.15.js", "sourcesContent": ["function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nexport function getDecoratedComponent(instanceRef) {\n  var currentRef = instanceRef.current;\n\n  if (currentRef == null) {\n    return null;\n  } else if (currentRef.decoratedRef) {\n    // go through the private field in decorateHandler to avoid the invariant hit\n    return currentRef.decoratedRef.current;\n  } else {\n    return currentRef;\n  }\n}\nexport function isClassComponent(Component) {\n  return Component && Component.prototype && typeof Component.prototype.render === 'function';\n}\nexport function isRefForwardingComponent(C) {\n  var _item$$$typeof;\n\n  var item = C;\n  return (item === null || item === void 0 ? void 0 : (_item$$$typeof = item.$$typeof) === null || _item$$$typeof === void 0 ? void 0 : _item$$$typeof.toString()) === 'Symbol(react.forward_ref)';\n}\nexport function isRefable(C) {\n  return isClassComponent(C) || isRefForwardingComponent(C);\n}\nexport function checkDecoratorArguments(functionName, signature) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var i = 0; i < (arguments.length <= 2 ? 0 : arguments.length - 2); i++) {\n      var arg = i + 2 < 2 || arguments.length <= i + 2 ? undefined : arguments[i + 2];\n\n      if (arg && arg.prototype && arg.prototype.render) {\n        // eslint-disable-next-line no-console\n        console.error('You seem to be applying the arguments in the wrong order. ' + \"It should be \".concat(functionName, \"(\").concat(signature, \")(Component), not the other way around. \") + 'Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#you-seem-to-be-applying-the-arguments-in-the-wrong-order');\n        return;\n      }\n    }\n  }\n}\nexport function isFunction(input) {\n  return typeof input === 'function';\n}\nexport function noop() {// noop\n}\n\nfunction isObjectLike(input) {\n  return _typeof(input) === 'object' && input !== null;\n}\n\nexport function isPlainObject(input) {\n  if (!isObjectLike(input)) {\n    return false;\n  }\n\n  if (Object.getPrototypeOf(input) === null) {\n    return true;\n  }\n\n  var proto = input;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(input) === proto;\n}\nexport function isValidType(type, allowArray) {\n  return typeof type === 'string' || _typeof(type) === 'symbol' || !!allowArray && Array.isArray(type) && type.every(function (t) {\n    return isValidType(t, false);\n  });\n}", "export var ListenerType;\n\n(function (ListenerType) {\n  ListenerType[\"mouse\"] = \"mouse\";\n  ListenerType[\"touch\"] = \"touch\";\n  ListenerType[\"keyboard\"] = \"keyboard\";\n})(ListenerType || (ListenerType = {}));", "// Used for MouseEvent.buttons (note the s on the end).\nvar MouseButtons = {\n  Left: 1,\n  Right: 2,\n  Center: 4\n}; // Used for e.button (note the lack of an s on the end).\n\nvar MouseButton = {\n  Left: 0,\n  Center: 1,\n  Right: 2\n};\n/**\n * Only touch events and mouse events where the left button is pressed should initiate a drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\n\nexport function eventShouldStartDrag(e) {\n  // For touch events, button will be undefined. If e.button is defined,\n  // then it should be MouseButton.Left.\n  return e.button === undefined || e.button === MouseButton.Left;\n}\n/**\n * Only touch events and mouse events where the left mouse button is no longer held should end a drag.\n * It's possible the user mouse downs with the left mouse button, then mouse down and ups with the right mouse button.\n * We don't want releasing the right mouse button to end the drag.\n * @param {MouseEvent | TouchEvent} e The event\n */\n\nexport function eventShouldEndDrag(e) {\n  // Touch events will have buttons be undefined, while mouse events will have e.buttons's left button\n  // bit field unset if the left mouse button has been released\n  return e.buttons === undefined || (e.buttons & MouseButtons.Left) === 0;\n}\nexport function isTouchEvent(e) {\n  return !!e.targetTouches;\n}", "import { isTouchEvent } from './predicates';\nvar ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  var el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n\n  if (!el) {\n    return undefined;\n  }\n\n  var _el$getBoundingClient = el.getBoundingClientRect(),\n      top = _el$getBoundingClient.top,\n      left = _el$getBoundingClient.left;\n\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientTouchOffset(e, lastTargetTouchFallback) {\n  if (e.targetTouches.length === 1) {\n    return getEventClientOffset(e.targetTouches[0]);\n  } else if (lastTargetTouchFallback && e.touches.length === 1) {\n    if (e.touches[0].target === lastTargetTouchFallback.target) {\n      return getEventClientOffset(e.touches[0]);\n    }\n  }\n}\nexport function getEventClientOffset(e, lastTargetTouchFallback) {\n  if (isTouchEvent(e)) {\n    return getEventClientTouchOffset(e, lastTargetTouchFallback);\n  } else {\n    return {\n      x: e.clientX,\n      y: e.clientY\n    };\n  }\n}", "var supportsPassive = function () {\n  // simular to <PERSON><PERSON><PERSON><PERSON>'s test\n  var supported = false;\n\n  try {\n    addEventListener('test', function () {// do nothing\n    }, Object.defineProperty({}, 'passive', {\n      get: function get() {\n        supported = true;\n        return true;\n      }\n    }));\n  } catch (e) {// do nothing\n  }\n\n  return supported;\n}();\n\nexport default supportsPassive;", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nexport var OptionsReader =\n/*#__PURE__*/\nfunction () {\n  function OptionsReader(incoming, context) {\n    var _this = this;\n\n    _classCallCheck(this, OptionsReader);\n\n    this.enableTouchEvents = true;\n    this.enableMouseEvents = false;\n    this.enableKeyboardEvents = false;\n    this.ignoreContextMenu = false;\n    this.enableHoverOutsideTarget = false;\n    this.touchSlop = 0;\n    this.scrollAngleRanges = undefined;\n    this.context = context;\n    this.delayTouchStart = incoming.delayTouchStart || incoming.delay || 0;\n    this.delayMouseStart = incoming.delayMouseStart || incoming.delay || 0;\n    Object.keys(incoming).forEach(function (key) {\n      if (incoming[key] != null) {\n        ;\n        _this[key] = incoming[key];\n      }\n    });\n  }\n\n  _createClass(OptionsReader, [{\n    key: \"window\",\n    get: function get() {\n      if (this.context && this.context.window) {\n        return this.context.window;\n      } else if (typeof window !== 'undefined') {\n        return window;\n      }\n\n      return undefined;\n    }\n  }, {\n    key: \"document\",\n    get: function get() {\n      if (this.window) {\n        return this.window.document;\n      }\n\n      return undefined;\n    }\n  }]);\n\n  return OptionsReader;\n}();", "var _eventNames;\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nimport { ListenerType } from './interfaces';\nimport { eventShouldStartDrag, eventShouldEndDrag, isTouchEvent } from './utils/predicates';\nimport { getEventClientOffset, getNodeClientOffset } from './utils/offsets';\nimport { distance, inAngleRanges } from './utils/math';\nimport supportsPassive from './utils/supportsPassive';\nimport { OptionsReader } from './OptionsReader';\nvar eventNames = (_eventNames = {}, _defineProperty(_eventNames, ListenerType.mouse, {\n  start: 'mousedown',\n  move: 'mousemove',\n  end: 'mouseup',\n  contextmenu: 'contextmenu'\n}), _defineProperty(_eventNames, ListenerType.touch, {\n  start: 'touchstart',\n  move: 'touchmove',\n  end: 'touchend'\n}), _defineProperty(_eventNames, ListenerType.keyboard, {\n  keydown: 'keydown'\n}), _eventNames);\n\nvar TouchBackend =\n/*#__PURE__*/\nfunction () {\n  function TouchBackend(manager, context, options) {\n    var _this = this;\n\n    _classCallCheck(this, TouchBackend);\n\n    this.getSourceClientOffset = function (sourceId) {\n      return getNodeClientOffset(_this.sourceNodes[sourceId]);\n    };\n\n    this.handleTopMoveStartCapture = function (e) {\n      if (!eventShouldStartDrag(e)) {\n        return;\n      }\n\n      _this.moveStartSourceIds = [];\n    };\n\n    this.handleMoveStart = function (sourceId) {\n      // Just because we received an event doesn't necessarily mean we need to collect drag sources.\n      // We only collect start collecting drag sources on touch and left mouse events.\n      if (Array.isArray(_this.moveStartSourceIds)) {\n        _this.moveStartSourceIds.unshift(sourceId);\n      }\n    };\n\n    this.handleTopMoveStart = function (e) {\n      if (!eventShouldStartDrag(e)) {\n        return;\n      } // Don't prematurely preventDefault() here since it might:\n      // 1. Mess up scrolling\n      // 2. Mess up long tap (which brings up context menu)\n      // 3. If there's an anchor link as a child, tap won't be triggered on link\n\n\n      var clientOffset = getEventClientOffset(e);\n\n      if (clientOffset) {\n        if (isTouchEvent(e)) {\n          _this.lastTargetTouchFallback = e.targetTouches[0];\n        }\n\n        _this._mouseClientOffset = clientOffset;\n      }\n\n      _this.waitingForDelay = false;\n    };\n\n    this.handleTopMoveStartDelay = function (e) {\n      if (!eventShouldStartDrag(e)) {\n        return;\n      }\n\n      var delay = e.type === eventNames.touch.start ? _this.options.delayTouchStart : _this.options.delayMouseStart;\n      _this.timeout = setTimeout(_this.handleTopMoveStart.bind(_this, e), delay);\n      _this.waitingForDelay = true;\n    };\n\n    this.handleTopMoveCapture = function () {\n      _this.dragOverTargetIds = [];\n    };\n\n    this.handleMove = function (_, targetId) {\n      if (_this.dragOverTargetIds) {\n        _this.dragOverTargetIds.unshift(targetId);\n      }\n    };\n\n    this.handleTopMove = function (e) {\n      if (_this.timeout) {\n        clearTimeout(_this.timeout);\n      }\n\n      if (!_this.document || _this.waitingForDelay) {\n        return;\n      }\n\n      var moveStartSourceIds = _this.moveStartSourceIds,\n          dragOverTargetIds = _this.dragOverTargetIds;\n      var enableHoverOutsideTarget = _this.options.enableHoverOutsideTarget;\n      var clientOffset = getEventClientOffset(e, _this.lastTargetTouchFallback);\n\n      if (!clientOffset) {\n        return;\n      } // If the touch move started as a scroll, or is is between the scroll angles\n\n\n      if (_this._isScrolling || !_this.monitor.isDragging() && inAngleRanges(_this._mouseClientOffset.x || 0, _this._mouseClientOffset.y || 0, clientOffset.x, clientOffset.y, _this.options.scrollAngleRanges)) {\n        _this._isScrolling = true;\n        return;\n      } // If we're not dragging and we've moved a little, that counts as a drag start\n\n\n      if (!_this.monitor.isDragging() && // eslint-disable-next-line no-prototype-builtins\n      _this._mouseClientOffset.hasOwnProperty('x') && moveStartSourceIds && distance(_this._mouseClientOffset.x || 0, _this._mouseClientOffset.y || 0, clientOffset.x, clientOffset.y) > (_this.options.touchSlop ? _this.options.touchSlop : 0)) {\n        _this.moveStartSourceIds = undefined;\n\n        _this.actions.beginDrag(moveStartSourceIds, {\n          clientOffset: _this._mouseClientOffset,\n          getSourceClientOffset: _this.getSourceClientOffset,\n          publishSource: false\n        });\n      }\n\n      if (!_this.monitor.isDragging()) {\n        return;\n      }\n\n      var sourceNode = _this.sourceNodes[_this.monitor.getSourceId()];\n\n      _this.installSourceNodeRemovalObserver(sourceNode);\n\n      _this.actions.publishDragSource();\n\n      e.preventDefault(); // Get the node elements of the hovered DropTargets\n\n      var dragOverTargetNodes = (dragOverTargetIds || []).map(function (key) {\n        return _this.targetNodes[key];\n      }); // Get the a ordered list of nodes that are touched by\n\n      var elementsAtPoint = _this.options.getDropTargetElementsAtPoint ? _this.options.getDropTargetElementsAtPoint(clientOffset.x, clientOffset.y, dragOverTargetNodes) : _this.document.elementsFromPoint(clientOffset.x, clientOffset.y); // Extend list with parents that are not receiving elementsFromPoint events (size 0 elements and svg groups)\n\n      var elementsAtPointExtended = [];\n\n      for (var nodeId in elementsAtPoint) {\n        // eslint-disable-next-line no-prototype-builtins\n        if (!elementsAtPoint.hasOwnProperty(nodeId)) {\n          continue;\n        }\n\n        var currentNode = elementsAtPoint[nodeId];\n        elementsAtPointExtended.push(currentNode);\n\n        while (currentNode) {\n          currentNode = currentNode.parentElement;\n\n          if (elementsAtPointExtended.indexOf(currentNode) === -1) {\n            elementsAtPointExtended.push(currentNode);\n          }\n        }\n      }\n\n      var orderedDragOverTargetIds = elementsAtPointExtended // Filter off nodes that arent a hovered DropTargets nodes\n      .filter(function (node) {\n        return dragOverTargetNodes.indexOf(node) > -1;\n      }) // Map back the nodes elements to targetIds\n      .map(function (node) {\n        for (var targetId in _this.targetNodes) {\n          if (node === _this.targetNodes[targetId]) {\n            return targetId;\n          }\n        }\n\n        return undefined;\n      }) // Filter off possible null rows\n      .filter(function (node) {\n        return !!node;\n      }).filter(function (id, index, ids) {\n        return ids.indexOf(id) === index;\n      }); // Invoke hover for drop targets when source node is still over and pointer is outside\n\n      if (enableHoverOutsideTarget) {\n        for (var targetId in _this.targetNodes) {\n          if (_this.targetNodes[targetId] && _this.targetNodes[targetId].contains(sourceNode) && orderedDragOverTargetIds.indexOf(targetId) === -1) {\n            orderedDragOverTargetIds.unshift(targetId);\n            break;\n          }\n        }\n      } // Reverse order because dnd-core reverse it before calling the DropTarget drop methods\n\n\n      orderedDragOverTargetIds.reverse();\n\n      _this.actions.hover(orderedDragOverTargetIds, {\n        clientOffset: clientOffset\n      });\n    };\n\n    this.handleTopMoveEndCapture = function (e) {\n      _this._isScrolling = false;\n      _this.lastTargetTouchFallback = undefined;\n\n      if (!eventShouldEndDrag(e)) {\n        return;\n      }\n\n      if (!_this.monitor.isDragging() || _this.monitor.didDrop()) {\n        _this.moveStartSourceIds = undefined;\n        return;\n      }\n\n      e.preventDefault();\n      _this._mouseClientOffset = {};\n\n      _this.uninstallSourceNodeRemovalObserver();\n\n      _this.actions.drop();\n\n      _this.actions.endDrag();\n    };\n\n    this.handleCancelOnEscape = function (e) {\n      if (e.key === 'Escape' && _this.monitor.isDragging()) {\n        _this._mouseClientOffset = {};\n\n        _this.uninstallSourceNodeRemovalObserver();\n\n        _this.actions.endDrag();\n      }\n    };\n\n    this.options = new OptionsReader(options, context);\n    this.actions = manager.getActions();\n    this.monitor = manager.getMonitor();\n    this.sourceNodes = {};\n    this.sourcePreviewNodes = {};\n    this.sourcePreviewNodeOptions = {};\n    this.targetNodes = {};\n    this.listenerTypes = [];\n    this._mouseClientOffset = {};\n    this._isScrolling = false;\n\n    if (this.options.enableMouseEvents) {\n      this.listenerTypes.push(ListenerType.mouse);\n    }\n\n    if (this.options.enableTouchEvents) {\n      this.listenerTypes.push(ListenerType.touch);\n    }\n\n    if (this.options.enableKeyboardEvents) {\n      this.listenerTypes.push(ListenerType.keyboard);\n    }\n  } // public for test\n\n\n  _createClass(TouchBackend, [{\n    key: \"setup\",\n    value: function setup() {\n      if (!this.window) {\n        return;\n      }\n\n      invariant(!TouchBackend.isSetUp, 'Cannot have two Touch backends at the same time.');\n      TouchBackend.isSetUp = true;\n      this.addEventListener(this.window, 'start', this.getTopMoveStartHandler());\n      this.addEventListener(this.window, 'start', this.handleTopMoveStartCapture, true);\n      this.addEventListener(this.window, 'move', this.handleTopMove);\n      this.addEventListener(this.window, 'move', this.handleTopMoveCapture, true);\n      this.addEventListener(this.window, 'end', this.handleTopMoveEndCapture, true);\n\n      if (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {\n        this.addEventListener(this.window, 'contextmenu', this.handleTopMoveEndCapture);\n      }\n\n      if (this.options.enableKeyboardEvents) {\n        this.addEventListener(this.window, 'keydown', this.handleCancelOnEscape, true);\n      }\n    }\n  }, {\n    key: \"teardown\",\n    value: function teardown() {\n      if (!this.window) {\n        return;\n      }\n\n      TouchBackend.isSetUp = false;\n      this._mouseClientOffset = {};\n      this.removeEventListener(this.window, 'start', this.handleTopMoveStartCapture, true);\n      this.removeEventListener(this.window, 'start', this.handleTopMoveStart);\n      this.removeEventListener(this.window, 'move', this.handleTopMoveCapture, true);\n      this.removeEventListener(this.window, 'move', this.handleTopMove);\n      this.removeEventListener(this.window, 'end', this.handleTopMoveEndCapture, true);\n\n      if (this.options.enableMouseEvents && !this.options.ignoreContextMenu) {\n        this.removeEventListener(this.window, 'contextmenu', this.handleTopMoveEndCapture);\n      }\n\n      if (this.options.enableKeyboardEvents) {\n        this.removeEventListener(this.window, 'keydown', this.handleCancelOnEscape, true);\n      }\n\n      this.uninstallSourceNodeRemovalObserver();\n    }\n  }, {\n    key: \"addEventListener\",\n    value: function addEventListener(subject, event, handler, capture) {\n      var options = supportsPassive ? {\n        capture: capture,\n        passive: false\n      } : capture;\n      this.listenerTypes.forEach(function (listenerType) {\n        var evt = eventNames[listenerType][event];\n\n        if (evt) {\n          subject.addEventListener(evt, handler, options);\n        }\n      });\n    }\n  }, {\n    key: \"removeEventListener\",\n    value: function removeEventListener(subject, event, handler, capture) {\n      var options = supportsPassive ? {\n        capture: capture,\n        passive: false\n      } : capture;\n      this.listenerTypes.forEach(function (listenerType) {\n        var evt = eventNames[listenerType][event];\n\n        if (evt) {\n          subject.removeEventListener(evt, handler, options);\n        }\n      });\n    }\n  }, {\n    key: \"connectDragSource\",\n    value: function connectDragSource(sourceId, node) {\n      var _this2 = this;\n\n      var handleMoveStart = this.handleMoveStart.bind(this, sourceId);\n      this.sourceNodes[sourceId] = node;\n      this.addEventListener(node, 'start', handleMoveStart);\n      return function () {\n        delete _this2.sourceNodes[sourceId];\n\n        _this2.removeEventListener(node, 'start', handleMoveStart);\n      };\n    }\n  }, {\n    key: \"connectDragPreview\",\n    value: function connectDragPreview(sourceId, node, options) {\n      var _this3 = this;\n\n      this.sourcePreviewNodeOptions[sourceId] = options;\n      this.sourcePreviewNodes[sourceId] = node;\n      return function () {\n        delete _this3.sourcePreviewNodes[sourceId];\n        delete _this3.sourcePreviewNodeOptions[sourceId];\n      };\n    }\n  }, {\n    key: \"connectDropTarget\",\n    value: function connectDropTarget(targetId, node) {\n      var _this4 = this;\n\n      if (!this.document) {\n        return function () {\n          return null;\n        };\n      }\n\n      var handleMove = function handleMove(e) {\n        if (!_this4.document || !_this4.monitor.isDragging()) {\n          return;\n        }\n\n        var coords;\n        /**\n         * Grab the coordinates for the current mouse/touch position\n         */\n\n        switch (e.type) {\n          case eventNames.mouse.move:\n            coords = {\n              x: e.clientX,\n              y: e.clientY\n            };\n            break;\n\n          case eventNames.touch.move:\n            coords = {\n              x: e.touches[0].clientX,\n              y: e.touches[0].clientY\n            };\n            break;\n        }\n        /**\n         * Use the coordinates to grab the element the drag ended on.\n         * If the element is the same as the target node (or any of it's children) then we have hit a drop target and can handle the move.\n         */\n\n\n        var droppedOn = coords != null ? _this4.document.elementFromPoint(coords.x, coords.y) : undefined;\n        var childMatch = droppedOn && node.contains(droppedOn);\n\n        if (droppedOn === node || childMatch) {\n          return _this4.handleMove(e, targetId);\n        }\n      };\n      /**\n       * Attaching the event listener to the body so that touchmove will work while dragging over multiple target elements.\n       */\n\n\n      this.addEventListener(this.document.body, 'move', handleMove);\n      this.targetNodes[targetId] = node;\n      return function () {\n        if (_this4.document) {\n          delete _this4.targetNodes[targetId];\n\n          _this4.removeEventListener(_this4.document.body, 'move', handleMove);\n        }\n      };\n    }\n  }, {\n    key: \"getTopMoveStartHandler\",\n    value: function getTopMoveStartHandler() {\n      if (!this.options.delayTouchStart && !this.options.delayMouseStart) {\n        return this.handleTopMoveStart;\n      }\n\n      return this.handleTopMoveStartDelay;\n    }\n  }, {\n    key: \"installSourceNodeRemovalObserver\",\n    value: function installSourceNodeRemovalObserver(node) {\n      var _this5 = this;\n\n      this.uninstallSourceNodeRemovalObserver();\n      this.draggedSourceNode = node;\n      this.draggedSourceNodeRemovalObserver = new MutationObserver(function () {\n        if (node && !node.parentElement) {\n          _this5.resurrectSourceNode();\n\n          _this5.uninstallSourceNodeRemovalObserver();\n        }\n      });\n\n      if (!node || !node.parentElement) {\n        return;\n      }\n\n      this.draggedSourceNodeRemovalObserver.observe(node.parentElement, {\n        childList: true\n      });\n    }\n  }, {\n    key: \"resurrectSourceNode\",\n    value: function resurrectSourceNode() {\n      if (this.document && this.draggedSourceNode) {\n        this.draggedSourceNode.style.display = 'none';\n        this.draggedSourceNode.removeAttribute('data-reactid');\n        this.document.body.appendChild(this.draggedSourceNode);\n      }\n    }\n  }, {\n    key: \"uninstallSourceNodeRemovalObserver\",\n    value: function uninstallSourceNodeRemovalObserver() {\n      if (this.draggedSourceNodeRemovalObserver) {\n        this.draggedSourceNodeRemovalObserver.disconnect();\n      }\n\n      this.draggedSourceNodeRemovalObserver = undefined;\n      this.draggedSourceNode = undefined;\n    }\n  }, {\n    key: \"window\",\n    get: function get() {\n      return this.options.window;\n    } // public for test\n\n  }, {\n    key: \"document\",\n    get: function get() {\n      if (this.window) {\n        return this.window.document;\n      }\n\n      return undefined;\n    }\n  }]);\n\n  return TouchBackend;\n}();\n\nexport { TouchBackend as default };", "export function distance(x1, y1, x2, y2) {\n  return Math.sqrt(Math.pow(Math.abs(x2 - x1), 2) + Math.pow(Math.abs(y2 - y1), 2));\n}\nexport function inAngleRanges(x1, y1, x2, y2, angleRanges) {\n  if (!angleRanges) {\n    return false;\n  }\n\n  var angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI + 180;\n\n  for (var i = 0; i < angleRanges.length; ++i) {\n    if ((angleRanges[i].start == null || angle >= angleRanges[i].start) && (angleRanges[i].end == null || angle <= angleRanges[i].end)) {\n      return true;\n    }\n  }\n\n  return false;\n}", "import TouchBackend from './TouchBackend';\n\nvar createBackend = function createBackend(manager, context) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return new TouchBackend(manager, context, options);\n};\n\nexport default createBackend;", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { isFunction, noop } from './utils';\n/**\n * Provides a set of static methods for creating Disposables.\n * @param {Function} action Action to run during the first call to dispose.\n * The action is guaranteed to be run at most once.\n */\n\nexport var Disposable = /*#__PURE__*/function () {\n  function Disposable(action) {\n    _classCallCheck(this, Disposable);\n\n    _defineProperty(this, \"isDisposed\", false);\n\n    _defineProperty(this, \"action\", void 0);\n\n    this.action = isFunction(action) ? action : noop;\n  }\n  /** Performs the task of cleaning up resources. */\n\n\n  _createClass(Disposable, [{\n    key: \"dispose\",\n    value: function dispose() {\n      if (!this.isDisposed) {\n        this.action();\n        this.isDisposed = true;\n      }\n    }\n  }], [{\n    key: \"isDisposable\",\n    value:\n    /**\n     * Gets the disposable that does nothing when disposed.\n     */\n\n    /**\n     * Validates whether the given object is a disposable\n     * @param {Object} Object to test whether it has a dispose method\n     * @returns {Boolean} true if a disposable object, else false.\n     */\n    function isDisposable(d) {\n      return Boolean(d && isFunction(d.dispose));\n    }\n  }, {\n    key: \"_fixup\",\n    value: function _fixup(result) {\n      return Disposable.isDisposable(result) ? result : Disposable.empty;\n    }\n    /**\n     * Creates a disposable object that invokes the specified action when disposed.\n     * @param {Function} dispose Action to run during the first call to dispose.\n     * The action is guaranteed to be run at most once.\n     * @return {Disposable} The disposable object that runs the given action upon disposal.\n     */\n\n  }, {\n    key: \"create\",\n    value: function create(action) {\n      return new Disposable(action);\n    }\n  }]);\n\n  return Disposable;\n}();\n/**\n * Represents a group of disposable resources that are disposed together.\n * @constructor\n */\n\n_defineProperty(Disposable, \"empty\", {\n  dispose: noop\n});\n\nexport var CompositeDisposable = /*#__PURE__*/function () {\n  function CompositeDisposable() {\n    _classCallCheck(this, CompositeDisposable);\n\n    _defineProperty(this, \"isDisposed\", false);\n\n    _defineProperty(this, \"disposables\", void 0);\n\n    for (var _len = arguments.length, disposables = new Array(_len), _key = 0; _key < _len; _key++) {\n      disposables[_key] = arguments[_key];\n    }\n\n    this.disposables = disposables;\n  }\n  /**\n   * Adds a disposable to the CompositeDisposable or disposes the disposable if the CompositeDisposable is disposed.\n   * @param {Any} item Disposable to add.\n   */\n\n\n  _createClass(CompositeDisposable, [{\n    key: \"add\",\n    value: function add(item) {\n      if (this.isDisposed) {\n        item.dispose();\n      } else {\n        this.disposables.push(item);\n      }\n    }\n    /**\n     * Removes and disposes the first occurrence of a disposable from the CompositeDisposable.\n     * @param {Any} item Disposable to remove.\n     * @returns {Boolean} true if found; false otherwise.\n     */\n\n  }, {\n    key: \"remove\",\n    value: function remove(item) {\n      var shouldDispose = false;\n\n      if (!this.isDisposed) {\n        var idx = this.disposables.indexOf(item);\n\n        if (idx !== -1) {\n          shouldDispose = true;\n          this.disposables.splice(idx, 1);\n          item.dispose();\n        }\n      }\n\n      return shouldDispose;\n    }\n    /**\n     *  Disposes all disposables in the group and removes them from the group but\n     *  does not dispose the CompositeDisposable.\n     */\n\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      if (!this.isDisposed) {\n        var len = this.disposables.length;\n        var currentDisposables = new Array(len);\n\n        for (var i = 0; i < len; i++) {\n          currentDisposables[i] = this.disposables[i];\n        }\n\n        this.disposables = [];\n\n        for (var _i = 0; _i < len; _i++) {\n          currentDisposables[_i].dispose();\n        }\n      }\n    }\n    /**\n     *  Disposes all disposables in the group and removes them from the group.\n     */\n\n  }, {\n    key: \"dispose\",\n    value: function dispose() {\n      if (!this.isDisposed) {\n        this.isDisposed = true;\n        var len = this.disposables.length;\n        var currentDisposables = new Array(len);\n\n        for (var i = 0; i < len; i++) {\n          currentDisposables[i] = this.disposables[i];\n        }\n\n        this.disposables = [];\n\n        for (var _i2 = 0; _i2 < len; _i2++) {\n          currentDisposables[_i2].dispose();\n        }\n      }\n    }\n  }]);\n\n  return CompositeDisposable;\n}();\n/**\n * Represents a disposable resource whose underlying disposable resource can\n * be replaced by another disposable resource, causing automatic disposal of\n * the previous underlying disposable resource.\n */\n\nexport var SerialDisposable = /*#__PURE__*/function () {\n  function SerialDisposable() {\n    _classCallCheck(this, SerialDisposable);\n\n    _defineProperty(this, \"isDisposed\", false);\n\n    _defineProperty(this, \"current\", void 0);\n  }\n\n  _createClass(SerialDisposable, [{\n    key: \"getDisposable\",\n    value:\n    /**\n     * Gets the underlying disposable.\n     * @returns {Any} the underlying disposable.\n     */\n    function getDisposable() {\n      return this.current;\n    }\n  }, {\n    key: \"setDisposable\",\n    value: function setDisposable(value) {\n      var shouldDispose = this.isDisposed;\n\n      if (!shouldDispose) {\n        var old = this.current;\n        this.current = value;\n\n        if (old) {\n          old.dispose();\n        }\n      }\n\n      if (shouldDispose && value) {\n        value.dispose();\n      }\n    }\n    /** Performs the task of cleaning up resources. */\n\n  }, {\n    key: \"dispose\",\n    value: function dispose() {\n      if (!this.isDisposed) {\n        this.isDisposed = true;\n        var old = this.current;\n        this.current = undefined;\n\n        if (old) {\n          old.dispose();\n        }\n      }\n    }\n  }]);\n\n  return SerialDisposable;\n}();", "function _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createRef, Component } from 'react';\nimport { shallowEqual } from '@react-dnd/shallowequal';\nimport { invariant } from '@react-dnd/invariant';\nimport { DndContext } from '../core';\nimport { isPlainObject } from './utils';\nimport { Disposable, CompositeDisposable, SerialDisposable } from './disposables';\nimport { isRefable } from './utils';\nimport hoistStatics from 'hoist-non-react-statics';\nexport function decorateHandler(_ref) {\n  var DecoratedComponent = _ref.DecoratedComponent,\n      createHandler = _ref.createHandler,\n      createMonitor = _ref.createMonitor,\n      createConnector = _ref.createConnector,\n      registerHandler = _ref.registerHandler,\n      containerDisplayName = _ref.containerDisplayName,\n      getType = _ref.getType,\n      collect = _ref.collect,\n      options = _ref.options;\n  var _options$arePropsEqua = options.arePropsEqual,\n      arePropsEqual = _options$arePropsEqua === void 0 ? shallowEqual : _options$arePropsEqua;\n  var Decorated = DecoratedComponent;\n  var displayName = DecoratedComponent.displayName || DecoratedComponent.name || 'Component';\n\n  var DragDropContainer = /*#__PURE__*/function (_Component) {\n    _inherits(DragDropContainer, _Component);\n\n    var _super = _createSuper(DragDropContainer);\n\n    function DragDropContainer(props) {\n      var _this;\n\n      _classCallCheck(this, DragDropContainer);\n\n      _this = _super.call(this, props);\n\n      _defineProperty(_assertThisInitialized(_this), \"decoratedRef\", createRef());\n\n      _defineProperty(_assertThisInitialized(_this), \"handlerId\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"manager\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"handlerMonitor\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"handlerConnector\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"handler\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"disposable\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"currentType\", void 0);\n\n      _defineProperty(_assertThisInitialized(_this), \"handleChange\", function () {\n        var nextState = _this.getCurrentState();\n\n        if (!shallowEqual(nextState, _this.state)) {\n          _this.setState(nextState);\n        }\n      });\n\n      _this.disposable = new SerialDisposable();\n\n      _this.receiveProps(props);\n\n      _this.dispose();\n\n      return _this;\n    }\n\n    _createClass(DragDropContainer, [{\n      key: \"getHandlerId\",\n      value: function getHandlerId() {\n        return this.handlerId;\n      }\n    }, {\n      key: \"getDecoratedComponentInstance\",\n      value: function getDecoratedComponentInstance() {\n        invariant(this.decoratedRef.current, 'In order to access an instance of the decorated component, it must either be a class component or use React.forwardRef()');\n        return this.decoratedRef.current;\n      }\n    }, {\n      key: \"shouldComponentUpdate\",\n      value: function shouldComponentUpdate(nextProps, nextState) {\n        return !arePropsEqual(nextProps, this.props) || !shallowEqual(nextState, this.state);\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.disposable = new SerialDisposable();\n        this.currentType = undefined;\n        this.receiveProps(this.props);\n        this.handleChange();\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        if (!arePropsEqual(this.props, prevProps)) {\n          this.receiveProps(this.props);\n          this.handleChange();\n        }\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.dispose();\n      }\n    }, {\n      key: \"receiveProps\",\n      value: function receiveProps(props) {\n        if (!this.handler) {\n          return;\n        }\n\n        this.handler.receiveProps(props);\n        this.receiveType(getType(props));\n      }\n    }, {\n      key: \"receiveType\",\n      value: function receiveType(type) {\n        if (!this.handlerMonitor || !this.manager || !this.handlerConnector) {\n          return;\n        }\n\n        if (type === this.currentType) {\n          return;\n        }\n\n        this.currentType = type;\n\n        var _registerHandler = registerHandler(type, this.handler, this.manager),\n            _registerHandler2 = _slicedToArray(_registerHandler, 2),\n            handlerId = _registerHandler2[0],\n            unregister = _registerHandler2[1];\n\n        this.handlerId = handlerId;\n        this.handlerMonitor.receiveHandlerId(handlerId);\n        this.handlerConnector.receiveHandlerId(handlerId);\n        var globalMonitor = this.manager.getMonitor();\n        var unsubscribe = globalMonitor.subscribeToStateChange(this.handleChange, {\n          handlerIds: [handlerId]\n        });\n        this.disposable.setDisposable(new CompositeDisposable(new Disposable(unsubscribe), new Disposable(unregister)));\n      }\n    }, {\n      key: \"dispose\",\n      value: function dispose() {\n        this.disposable.dispose();\n\n        if (this.handlerConnector) {\n          this.handlerConnector.receiveHandlerId(null);\n        }\n      }\n    }, {\n      key: \"getCurrentState\",\n      value: function getCurrentState() {\n        if (!this.handlerConnector) {\n          return {};\n        }\n\n        var nextState = collect(this.handlerConnector.hooks, this.handlerMonitor, this.props);\n\n        if (process.env.NODE_ENV !== 'production') {\n          invariant(isPlainObject(nextState), 'Expected `collect` specified as the second argument to ' + '%s for %s to return a plain object of props to inject. ' + 'Instead, received %s.', containerDisplayName, displayName, nextState);\n        }\n\n        return nextState;\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        var _this2 = this;\n\n        return _jsx(DndContext.Consumer, {\n          children: function children(_ref2) {\n            var dragDropManager = _ref2.dragDropManager;\n\n            _this2.receiveDragDropManager(dragDropManager);\n\n            if (typeof requestAnimationFrame !== 'undefined') {\n              requestAnimationFrame(function () {\n                var _this2$handlerConnect;\n\n                return (_this2$handlerConnect = _this2.handlerConnector) === null || _this2$handlerConnect === void 0 ? void 0 : _this2$handlerConnect.reconnect();\n              });\n            }\n\n            return _jsx(Decorated, Object.assign({}, _this2.props, _this2.getCurrentState(), {\n              // NOTE: if Decorated is a Function Component, decoratedRef will not be populated unless it's a refforwarding component.\n              ref: isRefable(Decorated) ? _this2.decoratedRef : null\n            }), void 0);\n          }\n        }, void 0);\n      }\n    }, {\n      key: \"receiveDragDropManager\",\n      value: function receiveDragDropManager(dragDropManager) {\n        if (this.manager !== undefined) {\n          return;\n        }\n\n        invariant(dragDropManager !== undefined, 'Could not find the drag and drop manager in the context of %s. ' + 'Make sure to render a DndProvider component in your top-level component. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/troubleshooting#could-not-find-the-drag-and-drop-manager-in-the-context', displayName, displayName);\n\n        if (dragDropManager === undefined) {\n          return;\n        }\n\n        this.manager = dragDropManager;\n        this.handlerMonitor = createMonitor(dragDropManager);\n        this.handlerConnector = createConnector(dragDropManager.getBackend());\n        this.handler = createHandler(this.handlerMonitor, this.decoratedRef);\n      }\n    }]);\n\n    return DragDropContainer;\n  }(Component);\n\n  _defineProperty(DragDropContainer, \"DecoratedComponent\", DecoratedComponent);\n\n  _defineProperty(DragDropContainer, \"displayName\", \"\".concat(containerDisplayName, \"(\").concat(displayName, \")\"));\n\n  return hoistStatics(DragDropContainer, DecoratedComponent);\n}", "function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport { useEffect } from 'react';\nimport { useDragDropManager } from './useDragDropManager';\nimport { useCollector } from './useCollector';\n/**\n * useDragLayer Hook\n * @param collector The property collector\n */\n\nexport function useDragLayer(collect) {\n  var dragDropManager = useDragDropManager();\n  var monitor = dragDropManager.getMonitor();\n\n  var _useCollector = useCollector(monitor, collect),\n      _useCollector2 = _slicedToArray(_useCollector, 2),\n      collected = _useCollector2[0],\n      updateCollected = _useCollector2[1];\n\n  useEffect(function () {\n    return monitor.subscribeToOffsetChange(updateCollected);\n  });\n  useEffect(function () {\n    return monitor.subscribeToStateChange(updateCollected);\n  });\n  return collected;\n}", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nimport { isPlainObject, getDecoratedComponent } from './utils';\nvar ALLOWED_SPEC_METHODS = ['canDrop', 'hover', 'drop'];\n\nvar TargetImpl = /*#__PURE__*/function () {\n  function TargetImpl(spec, monitor, ref) {\n    _classCallCheck(this, TargetImpl);\n\n    _defineProperty(this, \"props\", null);\n\n    _defineProperty(this, \"spec\", void 0);\n\n    _defineProperty(this, \"monitor\", void 0);\n\n    _defineProperty(this, \"ref\", void 0);\n\n    this.spec = spec;\n    this.monitor = monitor;\n    this.ref = ref;\n  }\n\n  _createClass(TargetImpl, [{\n    key: \"receiveProps\",\n    value: function receiveProps(props) {\n      this.props = props;\n    }\n  }, {\n    key: \"receiveMonitor\",\n    value: function receiveMonitor(monitor) {\n      this.monitor = monitor;\n    }\n  }, {\n    key: \"canDrop\",\n    value: function canDrop() {\n      if (!this.spec.canDrop) {\n        return true;\n      }\n\n      return this.spec.canDrop(this.props, this.monitor);\n    }\n  }, {\n    key: \"hover\",\n    value: function hover() {\n      if (!this.spec.hover || !this.props) {\n        return;\n      }\n\n      this.spec.hover(this.props, this.monitor, getDecoratedComponent(this.ref));\n    }\n  }, {\n    key: \"drop\",\n    value: function drop() {\n      if (!this.spec.drop) {\n        return undefined;\n      }\n\n      var dropResult = this.spec.drop(this.props, this.monitor, this.ref.current);\n\n      if (process.env.NODE_ENV !== 'production') {\n        invariant(typeof dropResult === 'undefined' || isPlainObject(dropResult), 'drop() must either return undefined, or an object that represents the drop result. ' + 'Instead received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', dropResult);\n      }\n\n      return dropResult;\n    }\n  }]);\n\n  return TargetImpl;\n}();\n\nexport function createTargetFactory(spec) {\n  Object.keys(spec).forEach(function (key) {\n    invariant(ALLOWED_SPEC_METHODS.indexOf(key) > -1, 'Expected the drop target specification to only have ' + 'some of the following keys: %s. ' + 'Instead received a specification with an unexpected \"%s\" key. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', ALLOWED_SPEC_METHODS.join(', '), key);\n    invariant(typeof spec[key] === 'function', 'Expected %s in the drop target specification to be a function. ' + 'Instead received a specification with %s: %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', key, key, spec[key]);\n  });\n  return function createTarget(monitor, ref) {\n    return new TargetImpl(spec, monitor, ref);\n  };\n}", "import { invariant } from '@react-dnd/invariant';\nimport { TargetConnector, DropTargetMonitorImpl, registerTarget } from '../internals';\nimport { isPlainObject, isValidType } from './utils';\nimport { checkDecoratorArguments } from './utils';\nimport { decorate<PERSON><PERSON><PERSON> } from './decorateHandler';\nimport { createTargetFactory } from './createTargetFactory';\n/**\n * @param type The accepted target type\n * @param spec The DropTarget specification\n * @param collect The props collector function\n * @param options Options\n */\n\nexport function DropTarget(type, spec, collect) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  checkDecoratorArguments('DropTarget', 'type, spec, collect[, options]', type, spec, collect, options);\n  var getType = type;\n\n  if (typeof type !== 'function') {\n    invariant(isValidType(type, true), 'Expected \"type\" provided as the first argument to DropTarget to be ' + 'a string, an array of strings, or a function that returns either given ' + 'the current props. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', type);\n\n    getType = function getType() {\n      return type;\n    };\n  }\n\n  invariant(isPlainObject(spec), 'Expected \"spec\" provided as the second argument to DropTarget to be ' + 'a plain object. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', spec);\n  var createTarget = createTargetFactory(spec);\n  invariant(typeof collect === 'function', 'Expected \"collect\" provided as the third argument to DropTarget to be ' + 'a function that returns a plain object of props to inject. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  invariant(isPlainObject(options), 'Expected \"options\" provided as the fourth argument to DropTarget to be ' + 'a plain object when specified. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target', collect);\n  return function decorateTarget(DecoratedComponent) {\n    return decorateHandler({\n      containerDisplayName: 'DropTarget',\n      createHandler: createTarget,\n      registerHandler: registerTarget,\n      createMonitor: function createMonitor(manager) {\n        return new DropTargetMonitorImpl(manager);\n      },\n      createConnector: function createConnector(backend) {\n        return new TargetConnector(backend);\n      },\n      DecoratedComponent: DecoratedComponent,\n      getType: getType,\n      collect: collect,\n      options: options\n    });\n  };\n}", "function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { invariant } from '@react-dnd/invariant';\nimport { isPlainObject, getDecoratedComponent } from './utils';\nvar ALLOWED_SPEC_METHODS = ['canDrag', 'beginDrag', 'isDragging', 'endDrag'];\nvar REQUIRED_SPEC_METHODS = ['beginDrag'];\n\nvar SourceImpl = /*#__PURE__*/function () {\n  function SourceImpl(spec, monitor, ref) {\n    var _this = this;\n\n    _classCallCheck(this, SourceImpl);\n\n    _defineProperty(this, \"props\", null);\n\n    _defineProperty(this, \"spec\", void 0);\n\n    _defineProperty(this, \"monitor\", void 0);\n\n    _defineProperty(this, \"ref\", void 0);\n\n    _defineProperty(this, \"beginDrag\", function () {\n      if (!_this.props) {\n        return;\n      }\n\n      var item = _this.spec.beginDrag(_this.props, _this.monitor, _this.ref.current);\n\n      if (process.env.NODE_ENV !== 'production') {\n        invariant(isPlainObject(item), 'beginDrag() must return a plain object that represents the dragged item. ' + 'Instead received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', item);\n      }\n\n      return item;\n    });\n\n    this.spec = spec;\n    this.monitor = monitor;\n    this.ref = ref;\n  }\n\n  _createClass(SourceImpl, [{\n    key: \"receiveProps\",\n    value: function receiveProps(props) {\n      this.props = props;\n    }\n  }, {\n    key: \"canDrag\",\n    value: function canDrag() {\n      if (!this.props) {\n        return false;\n      }\n\n      if (!this.spec.canDrag) {\n        return true;\n      }\n\n      return this.spec.canDrag(this.props, this.monitor);\n    }\n  }, {\n    key: \"isDragging\",\n    value: function isDragging(globalMonitor, sourceId) {\n      if (!this.props) {\n        return false;\n      }\n\n      if (!this.spec.isDragging) {\n        return sourceId === globalMonitor.getSourceId();\n      }\n\n      return this.spec.isDragging(this.props, this.monitor);\n    }\n  }, {\n    key: \"endDrag\",\n    value: function endDrag() {\n      if (!this.props) {\n        return;\n      }\n\n      if (!this.spec.endDrag) {\n        return;\n      }\n\n      this.spec.endDrag(this.props, this.monitor, getDecoratedComponent(this.ref));\n    }\n  }]);\n\n  return SourceImpl;\n}();\n\nexport function createSourceFactory(spec) {\n  Object.keys(spec).forEach(function (key) {\n    invariant(ALLOWED_SPEC_METHODS.indexOf(key) > -1, 'Expected the drag source specification to only have ' + 'some of the following keys: %s. ' + 'Instead received a specification with an unexpected \"%s\" key. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', ALLOWED_SPEC_METHODS.join(', '), key);\n    invariant(typeof spec[key] === 'function', 'Expected %s in the drag source specification to be a function. ' + 'Instead received a specification with %s: %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', key, key, spec[key]);\n  });\n  REQUIRED_SPEC_METHODS.forEach(function (key) {\n    invariant(typeof spec[key] === 'function', 'Expected %s in the drag source specification to be a function. ' + 'Instead received a specification with %s: %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', key, key, spec[key]);\n  });\n  return function createSource(monitor, ref) {\n    return new SourceImpl(spec, monitor, ref);\n  };\n}", "import { invariant } from '@react-dnd/invariant';\nimport { registerSource, DragSourceMonitorImpl, SourceConnector } from '../internals';\nimport { checkDecoratorArguments, isPlainObject, isValidType } from './utils';\nimport { decorate<PERSON><PERSON><PERSON> } from './decorateHandler';\nimport { createSourceFactory } from './createSourceFactory';\n/**\n * Decorates a component as a dragsource\n * @param type The dragsource type\n * @param spec The drag source specification\n * @param collect The props collector function\n * @param options DnD options\n */\n\nexport function DragSource(type, spec, collect) {\n  var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  checkDecoratorArguments('DragSource', 'type, spec, collect[, options]', type, spec, collect, options);\n  var getType = type;\n\n  if (typeof type !== 'function') {\n    invariant(isValidType(type), 'Expected \"type\" provided as the first argument to DragSource to be ' + 'a string, or a function that returns a string given the current props. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', type);\n\n    getType = function getType() {\n      return type;\n    };\n  }\n\n  invariant(isPlainObject(spec), 'Expected \"spec\" provided as the second argument to DragSource to be ' + 'a plain object. Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', spec);\n  var createSource = createSourceFactory(spec);\n  invariant(typeof collect === 'function', 'Expected \"collect\" provided as the third argument to DragSource to be ' + 'a function that returns a plain object of props to inject. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', collect);\n  invariant(isPlainObject(options), 'Expected \"options\" provided as the fourth argument to DragSource to be ' + 'a plain object when specified. ' + 'Instead, received %s. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source', collect);\n  return function decorateSource(DecoratedComponent) {\n    return decorateHandler({\n      containerDisplayName: 'DragSource',\n      createHandler: createSource,\n      registerHandler: registerSource,\n      createConnector: function createConnector(backend) {\n        return new SourceConnector(backend);\n      },\n      createMonitor: function createMonitor(manager) {\n        return new DragSourceMonitorImpl(manager);\n      },\n      DecoratedComponent: DecoratedComponent,\n      getType: getType,\n      collect: collect,\n      options: options\n    });\n  };\n}", "var emptyImage;\nexport function getEmptyImage() {\n  if (!emptyImage) {\n    emptyImage = new Image();\n    emptyImage.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';\n  }\n\n  return emptyImage;\n}"], "sourceRoot": ""}