/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[5],
		{
			631: function (ya, va, r) {
				r.r(va);
				var la = r(0);
				ya = r(53);
				var na = r(246),
					ma = r(547),
					ja = r(308),
					ia = window;
				r = (function () {
					function ca(y, x) {
						this.fca = function (n) {
							n = n.split(".");
							return n[n.length - 1].match(/(jpg|jpeg|png|gif)$/i);
						};
						x = x || {};
						this.url = y;
						this.filename = x.filename || y;
						this.Sf = x.customHeaders;
						this.PVa = !!x.useDownloader;
						this.withCredentials = !!x.withCredentials;
					}
					ca.prototype.hP = function (y) {
						this.Sf = y;
					};
					ca.prototype.getCustomHeaders = function () {
						return this.Sf;
					};
					ca.prototype.getFileData = function (y) {
						var x = this,
							n = this,
							h = new XMLHttpRequest(),
							b = 0 === this.url.indexOf("blob:") ? "blob" : "arraybuffer";
						h.open("GET", this.url, !0);
						h.withCredentials = this.withCredentials;
						h.responseType = b;
						this.Sf &&
							Object.keys(this.Sf).forEach(function (f) {
								h.setRequestHeader(f, x.Sf[f]);
							});
						var e = /^https?:/i.test(this.url);
						h.addEventListener(
							"load",
							function (f) {
								return Object(la.b)(this, void 0, void 0, function () {
									var a, w, z, aa, ea, ba;
									return Object(la.d)(this, function (fa) {
										switch (fa.label) {
											case 0:
												if (200 !== this.status && (e || 0 !== this.status))
													return [3, 10];
												n.trigger(ca.Events.DOCUMENT_LOADING_PROGRESS, [
													f.loaded,
													f.loaded,
												]);
												if ("blob" !== this.responseType) return [3, 4];
												a = this.response;
												return n.fca(n.filename)
													? [4, Object(ja.b)(a)]
													: [3, 2];
											case 1:
												return (
													(w = fa.aa()),
													(n.fileSize = w.byteLength),
													y(new Uint8Array(w)),
													[3, 3]
												);
											case 2:
												(z = new FileReader()),
													(z.onload = function (ha) {
														ha = new Uint8Array(ha.target.result);
														n.fileSize = ha.length;
														y(ha);
													}),
													z.readAsArrayBuffer(a),
													(fa.label = 3);
											case 3:
												return [3, 9];
											case 4:
												fa.gd.push([4, 8, , 9]);
												aa = new Uint8Array(this.response);
												if (!n.fca(n.filename)) return [3, 6];
												a = new Blob([aa.buffer]);
												return [4, Object(ja.b)(a)];
											case 5:
												return (
													(w = fa.aa()),
													(n.fileSize = w.byteLength),
													y(new Uint8Array(w)),
													[3, 7]
												);
											case 6:
												(n.fileSize = aa.length), y(aa), (fa.label = 7);
											case 7:
												return [3, 9];
											case 8:
												return (
													fa.aa(),
													n.trigger(ca.Events.ERROR, [
														"pdfLoad",
														"Out of memory",
													]),
													[3, 9]
												);
											case 9:
												return [3, 11];
											case 10:
												(ea = f.currentTarget),
													(ba = Object(na.b)(ea)),
													n.trigger(ca.Events.ERROR, [
														"pdfLoad",
														"".concat(this.status, " ").concat(ea.statusText),
														ba,
													]),
													(fa.label = 11);
											case 11:
												return (n.$H = null), [2];
										}
									});
								});
							},
							!1,
						);
						h.onprogress = function (f) {
							n.trigger(ca.Events.DOCUMENT_LOADING_PROGRESS, [
								f.loaded,
								0 < f.total ? f.total : 0,
							]);
						};
						h.addEventListener(
							"error",
							function () {
								n.trigger(ca.Events.ERROR, ["pdfLoad", "Network failure"]);
								n.$H = null;
							},
							!1,
						);
						h.send();
						this.$H = h;
					};
					ca.prototype.getFile = function () {
						var y = this;
						return new Promise(function (x) {
							ia.da.isJSWorker && x(y.url);
							if (y.PVa) {
								var n = Object(la.a)(
									{ url: y.url },
									y.Sf ? { customHeaders: y.Sf } : {},
								);
								x(n);
							}
							x(null);
						});
					};
					ca.prototype.abort = function () {
						this.$H && (this.$H.abort(), (this.$H = null));
					};
					ca.Events = {
						DOCUMENT_LOADING_PROGRESS: "documentLoadingProgress",
						ERROR: "error",
					};
					return ca;
				})();
				Object(ya.a)(r);
				Object(ma.a)(r);
				Object(ma.b)(r);
				va["default"] = r;
			},
		},
	]);
}).call(this || window);
