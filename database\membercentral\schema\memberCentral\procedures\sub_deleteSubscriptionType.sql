ALTER PROC dbo.sub_deleteSubscriptionType
@orgID INT, 
@siteid INT,
@subTypeID INT,
@isImport BIT = 0,
@recordedByMemberID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @minConditionID int, @contentSRID int, @CIDList varchar(max), @msg varchar(max),
		@subKeyMapJSON varchar(100), @crlf varchar(2) = char(13) + char(10);

	IF EXISTS (
		SELECT subs.subscriptionID
		FROM dbo.sub_Types st
		INNER join dbo.sub_subscriptions subs on subs.typeID = st.typeID AND subs.status ='A'
		WHERE siteID = @siteid    
		and st.typeID = @subTypeID)
		GOTO on_done;

	SELECT @msg = 'Subscription Type [' + STRING_ESCAPE(typeName,'json') + '] deleted.'
	FROM dbo.sub_Types
	WHERE typeID = @subTypeID
	AND siteID = @siteID;
	
	SET @subKeyMapJSON = '{ "TYPEID":'+CAST(@subTypeID AS varchar(10))+' }';

	select @CIDList = COALESCE(@CIDList + ',', '') + cast(c.conditionID as varchar(10)) 
	from dbo.ams_virtualGroupConditions as c
	inner join dbo.ams_virtualGroupConditionValues cv on cv.conditionID = c.conditionID
		and cv.conditionValue = cast(@subTypeID as varchar(10))
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID 
		and k.conditionKey = 'subSubType';
	
	BEGIN TRAN;
		-- delete any conditions that depend on this subscription type
		IF @CIDList is not null
			EXEC dbo.ams_deleteVirtualGroupCondition @orgID=@orgID, @conditionIDList=@CIDList, 
				@recordedByMemberID=@recordedByMemberID, @bypassQueue=0;

		-- mark type as deleted	
		UPDATE dbo.sub_Types
		set [status] = 'D'
		where typeID = @subTypeID
		and siteID = @siteid;

		-- delete content object
		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t
		inner join dbo.cms_content cc on t.frontEndContentID = cc.contentID
			and t.typeID = @subTypeID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t
		inner join dbo.cms_content cc on t.frontEndCompletedContentID = cc.contentID
			and t.typeID = @subTypeID
		inner join dbo.cms_siteResources sr on cc.siteResourceID = sr.siteResourceID;

		update sr
		set sr.siteResourceStatusID = 3
		from dbo.sub_Types t			
		inner join dbo.cms_siteResources sr on t.siteResourceID = sr.siteResourceID
			and t.typeID = @subTypeID;

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBTYPE', @msgjson=@msg,
				@subKeyMapJSON=@subKeyMapJSON, @isImport=@isImport, @enteredByMemberID=@recordedByMemberID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
