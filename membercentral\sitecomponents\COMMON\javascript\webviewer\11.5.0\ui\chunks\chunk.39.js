(window.webpackJsonp = window.webpackJsonp || []).push([
	[39],
	{
		1539: function (e, t, n) {
			var o = n(30),
				r = n(1667);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1663: function (e, t, n) {
			var o = n(30),
				r = n(1664);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1664: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnailControls-overlay{display:grid;text-align:center;z-index:2;margin-top:5px;grid-template-areas:"rotate delete . more";grid-template-columns:repeat(3,1fr)}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.thumbnailControls-overlay{display:flex}}.thumbnailControls-overlay .Button{height:32px;padding:0;width:32px}.thumbnailControls-overlay .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls-overlay .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnailControls-overlay .Button.active{background:var(--view-header-button-active)}.thumbnailControls-overlay .Button.active .Icon{color:var(--selected-icon-color)}.thumbnailControls-overlay.modular-ui .Button:hover{border:1px solid var(--focus-border);background:var(--tools-button-hover)}.thumbnailControls-overlay .rotate-button{grid-area:rotate}.thumbnailControls-overlay .delete-button{grid-area:delete}.thumbnailControls-overlay .more-options{grid-area:more}.thumbnailControls-overlay.custom-buttons .Button{grid-area:auto}.thumbnailControls{display:flex;flex-direction:row;text-align:center;z-index:2;margin-top:5px}.thumbnailControls .Button{height:32px;padding:0;width:32px;margin:0 4px}.thumbnailControls .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.thumbnailControls .Button:hover{background:var(--view-header-button-hover);border-radius:4px}',
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1665: function (e, t, n) {
			var o = n(30),
				r = n(1666);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1666: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.Thumbnail{display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;cursor:pointer}.Thumbnail.active .container .page-image{border:2px solid var(--focus-border);box-shadow:none;box-sizing:content-box}.Thumbnail .container{position:relative;display:flex;justify-content:center;align-items:center;cursor:pointer}.Thumbnail .container .page-image{box-shadow:0 0 3px 0 var(--box-shadow)}.Thumbnail .container .annotation-image,.Thumbnail .container .page-image{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}.Thumbnail .container .checkbox{position:absolute;border-radius:4px;z-index:4}.Thumbnail .container .default{top:3%;right:15%}.Thumbnail .container .rotated{top:15%;right:3%}.Thumbnail .page-label{margin-top:11px}.Thumbnail.selected .container .thumbnail:before{color:#fff;background:var(--focus-border);width:16px;height:16px;position:absolute;z-index:10}.Thumbnail.selected .container canvas{background:hsla(0,0%,100%,.6)}.Thumbnail.active .page-label{color:var(--focus-border)!important}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1667: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControlsContainer{margin-left:16px;margin-right:16px;margin-bottom:16px}}.documentControlsContainer .PageControlContainer{display:flex;background-color:var(--gray-2);justify-content:center;align-content:center;border-radius:4px}.documentControlsContainer .PageControlContainer .dropdown-menu{position:relative}.documentControlsContainer .PageControlContainer .dropdown-menu .indicator{position:absolute;bottom:1px;right:1px;width:0;height:0;border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid #c4c4c4;transform:rotateY(0deg) rotate(315deg)}.documentControlsContainer .PageControlContainer button .Icon{height:21px;width:21px;color:var(--icon-color)}.documentControlsContainer .PageControlContainer .button-hover:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControlsContainer .PageControlContainer .divider{height:20px;width:1px;background:var(--divider);margin:6px;display:block!important}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1668: function (e, t, n) {
			var o = n(30),
				r = n(1669);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1669: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.documentControls{display:flex;flex-direction:column}.documentControls .divider{height:1px;background:var(--divider);margin:16px 0 8px}.documentControls .documentControlsInput{display:flex;flex-direction:row;padding-bottom:16px;padding-top:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput{padding-bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput{padding-bottom:0}}.documentControls .documentControlsInput.customizableUI{padding:8px 0}.documentControls .documentControlsInput .pagesInput{width:100%;height:30px;border:1px solid var(--border);border-radius:4px;color:var(--text-color);padding:4px 8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsInput .pagesInput{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsInput .pagesInput{font-size:13px}}.documentControls .documentControlsInput .pagesInput:focus{outline:none;border:1px solid var(--focus-border)}.documentControls .documentControlsInput .pagesInput::-moz-placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .pagesInput::placeholder{color:var(--placeholder-text)}.documentControls .documentControlsInput .documentControlsButton{display:flex;flex-direction:row;padding-left:2px}.documentControls .documentControlsInput .documentControlsButton .Button{height:30px;padding:0;width:30px;margin:0 4px}.documentControls .documentControlsInput .documentControlsButton .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.documentControls .documentControlsInput .documentControlsButton .Button:hover{background:var(--view-header-button-hover);border-radius:4px}.documentControls .documentControlsLabel{margin-top:16px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .documentControls .documentControlsLabel{margin-top:8px;font-size:13px}}.documentControls .documentControlsLabel .multiSelectExampleLabel{color:var(--faded-text);margin-left:2px}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1670: function (e, t, n) {
			var o = n(30),
				r = n(1671);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const o = [];
								return (
									n.querySelectorAll(t).forEach((e) => o.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && o.push(...e(t, n.shadowRoot));
									}),
									o
								);
							})("apryse-webviewer"));
					const n = [];
					for (let o = 0; o < t.length; o++) {
						const r = t[o];
						if (0 === o)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							r.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			o(r, a);
			e.exports = r.locals || {};
		},
		1671: function (e, t, n) {
			(t = e.exports = n(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.thumbnail-slider-container{display:flex;align-items:center;width:230px;margin:0 auto;height:40px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container{width:inherit;margin:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container{width:inherit;margin:16px}}.thumbnail-slider-container .thumbnail-slider{width:100%;height:20px;padding:0;color:transparent;background-color:transparent;border:0 transparent}.thumbnail-slider-container input[type=range]{-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;accent-color:green;height:3px;background:#2980b9}.thumbnail-slider-container input[type=range]::-webkit-slider-runnable-track{height:5px;border-radius:6px}.thumbnail-slider-container input[type=range]::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;height:14px;width:14px;background-color:var(--slider-filled);border-radius:50%;border:2px solid var(--slider-filled);margin-top:-4px}.thumbnail-slider-container input[type=range]::-moz-range-track{height:5px;border-radius:4px}.thumbnail-slider-container Button{width:15px;height:15px;margin:2.5px;padding-top:6px}.thumbnail-slider-container Button:hover{background:var(--view-header-button-hover);border-radius:4px}.thumbnail-slider-container .slider{width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider{margin-top:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider{margin-top:0}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .thumbnail-slider-container .slider .slider-element-container{width:auto;margin-left:auto}}.ThumbnailsPanel{overflow:hidden!important;display:flex;height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ThumbnailsPanel{width:inherit;margin:0 16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ThumbnailsPanel{width:inherit;margin:0 16px}}.ThumbnailsPanel #virtualized-thumbnails-container{flex:1}.ThumbnailsPanel .row{display:flex;justify-content:space-around;align-items:center;flex-direction:column}.ThumbnailsPanel .thumbnailPlaceholder{width:150px;margin:2px;border:1px solid var(--focus-border)}.ThumbnailsPanel .columnsOfThumbnails.row{display:flex;justify-content:left;align-items:center;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .cellThumbContainer{display:flex;flex-direction:row}.ThumbnailsPanel .columnsOfThumbnails .Thumbnail{display:inline-flex}.ThumbnailsPanel .columnsOfThumbnails .thumbnailPlaceholder{width:116px;min-width:116px;height:150px;margin-bottom:30px}.cellThumbContainer{border-radius:4px}.thumbnailAutoScrollArea{position:absolute;width:calc(100% - 55px);z-index:10;background:hsla(0,0%,100%,0)}",
				"",
			]),
				(t.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1702: function (e, t, n) {
			"use strict";
			n.r(t);
			n(36),
				n(20),
				n(9),
				n(29),
				n(8),
				n(79),
				n(41),
				n(23),
				n(24),
				n(101),
				n(102),
				n(128),
				n(46),
				n(52),
				n(124),
				n(591),
				n(25),
				n(19),
				n(12),
				n(13),
				n(14),
				n(10),
				n(11),
				n(16),
				n(15),
				n(18),
				n(27),
				n(28),
				n(22),
				n(32),
				n(47),
				n(49),
				n(48);
			var o = n(111),
				r = n.n(o),
				a = n(0),
				i = n.n(a),
				l = n(6),
				c = n(1583),
				u = n(196),
				s = n(17),
				d = n.n(s),
				m = n(38),
				p = (n(58), n(44), n(4)),
				h = n(2),
				f =
					(n(90),
					n(116),
					n(145),
					n(54),
					n(61),
					n(62),
					n(63),
					n(64),
					n(37),
					n(39),
					n(40),
					n(60),
					n(222)),
				b = n(1),
				g = n(3),
				v = n.n(g),
				y = n(123),
				w = n(42),
				x = (n(1663), n(5)),
				E = n(94);
			var C = function (e) {
					var t = e.className,
						n = e.pageIndex,
						o = Object(l.d)(),
						r = Object(l.e)(function (e) {
							return p.a.getSelectedThumbnailPageIndexes(e);
						});
					return i.a.createElement(
						"div",
						{
							className: t,
							onClick: function () {
								-1 === r.indexOf(n) && o(h.a.setSelectedPageThumbnails([n]));
							},
						},
						i.a.createElement(E.a, {
							dataElement: x.a.PAGE_MANIPULATION_OVERLAY_BUTTON,
							toggleElement: x.a.PAGE_MANIPULATION,
							img: "icon-tool-more",
							title: "option.thumbnailPanel.moreOptions",
						}),
					);
				},
				O = n(57),
				T = n(347),
				j = n(254);
			function A(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return P(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return P(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function P(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var k = { index: v.a.number.isRequired },
				S = function (e) {
					var t = e.index,
						n = Object(T.a)().t,
						o = A(
							Object(l.e)(function (e) {
								return [p.a.isElementDisabled(e, "thumbnailControl")];
							}),
							1,
						)[0],
						r = A(
							Object(l.e)(function (e) {
								return [
									p.a.isElementDisabled(
										e,
										x.a.PAGE_MANIPULATION_OVERLAY_BUTTON,
									),
								];
							}),
							1,
						)[0],
						c = A(
							Object(l.e)(function (e) {
								return [
									p.a.pageDeletionConfirmationModalEnabled(e),
									p.a.getSelectedThumbnailPageIndexes(e),
								];
							}),
							2,
						),
						u = c[0],
						s = c[1],
						m = Object(l.d)(),
						h = Object(a.useRef)([]),
						f = Object(a.useRef)(null),
						g = A(
							Object(l.e)(function (e) {
								return [
									p.a.getCurrentPage(e),
									p.a.getThumbnailControlMenuItems(e),
									p.a.getFeatureFlags(e),
								];
							}, l.c),
							3,
						),
						v = g[0],
						E = g[1],
						P = g[2],
						k =
							s.length > 0
								? s.map(function (e) {
										return e + 1;
									})
								: [t + 1],
						S = k.includes(v),
						I = P.customizableUI;
					S || (k = [v]);
					var L = b.a.getDocument(),
						N = null == L ? void 0 : L.type,
						R = N === O.a.XOD,
						D = N === O.a.OFFICE || N === O.a.LEGACY_OFFICE,
						M = {
							thumbRotateClockwise: i.a.createElement(w.a, {
								className: "rotate-button",
								img: "icon-header-page-manipulation-page-rotation-clockwise-line",
								onClick: function () {
									return Object(y.o)(k);
								},
								title: "option.thumbnailPanel.rotatePageClockwise",
								dataElement: "thumbRotateClockwise",
							}),
							thumbRotateCounterClockwise: i.a.createElement(w.a, {
								img: "icon-header-page-manipulation-page-rotation-counterclockwise-line",
								onClick: function () {
									return Object(y.p)(k);
								},
								title: "option.thumbnailPanel.rotatePageCounterClockwise",
								dataElement: "thumbRotateCounterClockwise",
							}),
							thumbDelete: i.a.createElement(w.a, {
								className: "delete-button",
								img: "icon-delete-line",
								onClick: function () {
									return Object(y.b)(k, m, u);
								},
								title: "option.thumbnailPanel.delete",
								dataElement: "thumbDelete",
								onClickAnnouncement: ""
									.concat(n("action.delete"), " ")
									.concat(n("action.modal"), " ")
									.concat(n("action.isOpen")),
							}),
						},
						_ = !1,
						H = [],
						z = E.map(function (e) {
							var t = e.dataElement,
								n = t,
								o = M[t];
							if (H.indexOf(t) > -1) return null;
							if ((H.push(t), !o)) {
								_ = !0;
								var r = e.img,
									a = e.onClick,
									l = e.title;
								o = i.a.createElement(w.a, {
									className: "".concat(t, "-button"),
									img: r,
									onClick: function () {
										return a(v);
									},
									title: l,
									dataElement: t,
								});
							}
							return o ? i.a.cloneElement(o, { key: n }) : null;
						});
					return (
						Object(a.useEffect)(
							function () {
								(h.current = Object(j.a)(f.current)),
									h.current.length > 0 &&
										h.current.forEach(function (e) {
											e.tabIndex = -1;
										});
							},
							[h.current, z],
						),
						o
							? null
							: R || D || (null != L && L.isWebViewerServerDocument())
								? i.a.createElement(
										"div",
										{
											className: "thumbnailControls-overlay",
											"data-element": "thumbnailControl",
											style: { display: "flex" },
										},
										i.a.createElement(w.a, {
											img: "icon-header-page-manipulation-page-rotation-counterclockwise-line",
											onClick: function () {
												return Object(y.p)(k);
											},
											title: "option.thumbnailPanel.rotatePageCounterClockwise",
											dataElement: "thumbRotateCounterClockwise",
										}),
										i.a.createElement(w.a, {
											img: "icon-header-page-manipulation-page-rotation-clockwise-line",
											onClick: function () {
												return Object(y.o)(k);
											},
											title: "option.thumbnailPanel.rotatePageClockwise",
											dataElement: "thumbRotateClockwise",
										}),
									)
								: i.a.createElement(
										"div",
										{
											className: d()({
												"thumbnailControls-overlay": !0,
												"custom-buttons": _,
												"modular-ui": I,
											}),
											"data-element": "thumbnailControl",
											ref: f,
										},
										z,
										r
											? null
											: i.a.createElement(C, {
													className: "more-options",
													pageIndex: t,
												}),
									)
					);
				};
			S.propTypes = k;
			var I = S,
				L = n(388),
				N = (n(1665), n(1355)),
				R = n(21);
			function D(e) {
				return (D =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function M(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return B(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					U(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function _() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _ =
					function () {
						return e;
					};
				var e = {},
					t = Object.prototype,
					n = t.hasOwnProperty,
					o =
						Object.defineProperty ||
						function (e, t, n) {
							e[t] = n.value;
						},
					r = "function" == typeof Symbol ? Symbol : {},
					a = r.iterator || "@@iterator",
					i = r.asyncIterator || "@@asyncIterator",
					l = r.toStringTag || "@@toStringTag";
				function c(e, t, n) {
					return (
						Object.defineProperty(e, t, {
							value: n,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[t]
					);
				}
				try {
					c({}, "");
				} catch (e) {
					c = function (e, t, n) {
						return (e[t] = n);
					};
				}
				function u(e, t, n, r) {
					var a = t && t.prototype instanceof m ? t : m,
						i = Object.create(a.prototype),
						l = new T(r || []);
					return o(i, "_invoke", { value: x(e, n, l) }), i;
				}
				function s(e, t, n) {
					try {
						return { type: "normal", arg: e.call(t, n) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = u;
				var d = {};
				function m() {}
				function p() {}
				function h() {}
				var f = {};
				c(f, a, function () {
					return this;
				});
				var b = Object.getPrototypeOf,
					g = b && b(b(j([])));
				g && g !== t && n.call(g, a) && (f = g);
				var v = (h.prototype = m.prototype = Object.create(f));
				function y(e) {
					["next", "throw", "return"].forEach(function (t) {
						c(e, t, function (e) {
							return this._invoke(t, e);
						});
					});
				}
				function w(e, t) {
					var r;
					o(this, "_invoke", {
						value: function (o, a) {
							function i() {
								return new t(function (r, i) {
									!(function o(r, a, i, l) {
										var c = s(e[r], e, a);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == D(d) && n.call(d, "__await")
												? t.resolve(d.__await).then(
														function (e) {
															o("next", e, i, l);
														},
														function (e) {
															o("throw", e, i, l);
														},
													)
												: t.resolve(d).then(
														function (e) {
															(u.value = e), i(u);
														},
														function (e) {
															return o("throw", e, i, l);
														},
													);
										}
										l(c.arg);
									})(o, a, r, i);
								});
							}
							return (r = r ? r.then(i, i) : i());
						},
					});
				}
				function x(e, t, n) {
					var o = "suspendedStart";
					return function (r, a) {
						if ("executing" === o)
							throw new Error("Generator is already running");
						if ("completed" === o) {
							if ("throw" === r) throw a;
							return A();
						}
						for (n.method = r, n.arg = a; ; ) {
							var i = n.delegate;
							if (i) {
								var l = E(i, n);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === n.method) n.sent = n._sent = n.arg;
							else if ("throw" === n.method) {
								if ("suspendedStart" === o) throw ((o = "completed"), n.arg);
								n.dispatchException(n.arg);
							} else "return" === n.method && n.abrupt("return", n.arg);
							o = "executing";
							var c = s(e, t, n);
							if ("normal" === c.type) {
								if (
									((o = n.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: n.done };
							}
							"throw" === c.type &&
								((o = "completed"), (n.method = "throw"), (n.arg = c.arg));
						}
					};
				}
				function E(e, t) {
					var n = t.method,
						o = e.iterator[n];
					if (void 0 === o)
						return (
							(t.delegate = null),
							("throw" === n &&
								e.iterator.return &&
								((t.method = "return"),
								(t.arg = void 0),
								E(e, t),
								"throw" === t.method)) ||
								("return" !== n &&
									((t.method = "throw"),
									(t.arg = new TypeError(
										"The iterator does not provide a '" + n + "' method",
									)))),
							d
						);
					var r = s(o, e.iterator, t.arg);
					if ("throw" === r.type)
						return (
							(t.method = "throw"), (t.arg = r.arg), (t.delegate = null), d
						);
					var a = r.arg;
					return a
						? a.done
							? ((t[e.resultName] = a.value),
								(t.next = e.nextLoc),
								"return" !== t.method &&
									((t.method = "next"), (t.arg = void 0)),
								(t.delegate = null),
								d)
							: a
						: ((t.method = "throw"),
							(t.arg = new TypeError("iterator result is not an object")),
							(t.delegate = null),
							d);
				}
				function C(e) {
					var t = { tryLoc: e[0] };
					1 in e && (t.catchLoc = e[1]),
						2 in e && ((t.finallyLoc = e[2]), (t.afterLoc = e[3])),
						this.tryEntries.push(t);
				}
				function O(e) {
					var t = e.completion || {};
					(t.type = "normal"), delete t.arg, (e.completion = t);
				}
				function T(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(C, this),
						this.reset(!0);
				}
				function j(e) {
					if (e) {
						var t = e[a];
						if (t) return t.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var o = -1,
								r = function t() {
									for (; ++o < e.length; )
										if (n.call(e, o)) return (t.value = e[o]), (t.done = !1), t;
									return (t.value = void 0), (t.done = !0), t;
								};
							return (r.next = r);
						}
					}
					return { next: A };
				}
				function A() {
					return { value: void 0, done: !0 };
				}
				return (
					(p.prototype = h),
					o(v, "constructor", { value: h, configurable: !0 }),
					o(h, "constructor", { value: p, configurable: !0 }),
					(p.displayName = c(h, l, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var t = "function" == typeof e && e.constructor;
						return (
							!!t &&
							(t === p || "GeneratorFunction" === (t.displayName || t.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, h)
								: ((e.__proto__ = h), c(e, l, "GeneratorFunction")),
							(e.prototype = Object.create(v)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					y(w.prototype),
					c(w.prototype, i, function () {
						return this;
					}),
					(e.AsyncIterator = w),
					(e.async = function (t, n, o, r, a) {
						void 0 === a && (a = Promise);
						var i = new w(u(t, n, o, r), a);
						return e.isGeneratorFunction(n)
							? i
							: i.next().then(function (e) {
									return e.done ? e.value : i.next();
								});
					}),
					y(v),
					c(v, l, "Generator"),
					c(v, a, function () {
						return this;
					}),
					c(v, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var t = Object(e),
							n = [];
						for (var o in t) n.push(o);
						return (
							n.reverse(),
							function e() {
								for (; n.length; ) {
									var o = n.pop();
									if (o in t) return (e.value = o), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = j),
					(T.prototype = {
						constructor: T,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(O),
								!e)
							)
								for (var t in this)
									"t" === t.charAt(0) &&
										n.call(this, t) &&
										!isNaN(+t.slice(1)) &&
										(this[t] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var t = this;
							function o(n, o) {
								return (
									(i.type = "throw"),
									(i.arg = e),
									(t.next = n),
									o && ((t.method = "next"), (t.arg = void 0)),
									!!o
								);
							}
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var a = this.tryEntries[r],
									i = a.completion;
								if ("root" === a.tryLoc) return o("end");
								if (a.tryLoc <= this.prev) {
									var l = n.call(a, "catchLoc"),
										c = n.call(a, "finallyLoc");
									if (l && c) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									} else if (l) {
										if (this.prev < a.catchLoc) return o(a.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < a.finallyLoc) return o(a.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, t) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var r = this.tryEntries[o];
								if (
									r.tryLoc <= this.prev &&
									n.call(r, "finallyLoc") &&
									this.prev < r.finallyLoc
								) {
									var a = r;
									break;
								}
							}
							a &&
								("break" === e || "continue" === e) &&
								a.tryLoc <= t &&
								t <= a.finallyLoc &&
								(a = null);
							var i = a ? a.completion : {};
							return (
								(i.type = e),
								(i.arg = t),
								a
									? ((this.method = "next"), (this.next = a.finallyLoc), d)
									: this.complete(i)
							);
						},
						complete: function (e, t) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && t && (this.next = t),
								d
							);
						},
						finish: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.finallyLoc === e)
									return this.complete(n.completion, n.afterLoc), O(n), d;
							}
						},
						catch: function (e) {
							for (var t = this.tryEntries.length - 1; t >= 0; --t) {
								var n = this.tryEntries[t];
								if (n.tryLoc === e) {
									var o = n.completion;
									if ("throw" === o.type) {
										var r = o.arg;
										O(n);
									}
									return r;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, t, n) {
							return (
								(this.delegate = { iterator: j(e), resultName: t, nextLoc: n }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					e
				);
			}
			function H(e, t, n, o, r, a, i) {
				try {
					var l = e[a](i),
						c = l.value;
				} catch (e) {
					return void n(e);
				}
				l.done ? t(c) : Promise.resolve(c).then(o, r);
			}
			function z(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					U(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function U(e, t) {
				if (e) {
					if ("string" == typeof e) return B(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? B(e, t)
								: void 0
					);
				}
			}
			function B(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var F = i.a.forwardRef(function (e, t) {
				var n = e.index,
					o = e.isSelected,
					r = e.updateAnnotations,
					c = e.shiftKeyThumbnailPivotIndex,
					u = e.onFinishLoading,
					s = e.onLoad,
					m = e.onRemove,
					h = void 0 === m ? function () {} : m,
					g = e.onDragStart,
					v = e.onDragOver,
					y = e.isDraggable,
					w = e.shouldShowControls,
					x = e.thumbnailSize,
					E = e.currentPage,
					C = e.pageLabels,
					O = void 0 === C ? [] : C,
					A = e.selectedPageIndexes,
					P = e.isThumbnailMultiselectEnabled,
					k = e.isReaderModeOrReadOnly,
					S = e.dispatch,
					D = e.actions,
					U = e.isMobile,
					B = e.canLoad,
					F = e.onCancel,
					W = e.isThumbnailSelectingPages,
					G = e.thumbnailSelectionMode,
					V = e.activeDocumentViewerKey,
					K = e.panelSelector,
					q = e.parentKeyListener,
					Y = x ? Number(x) : 150,
					$ = z(Object(a.useState)(-1), 2),
					X = $[0],
					J = $[1],
					Q = Object(a.useRef)(null),
					Z = Object(a.useRef)([]),
					ee = Object(a.useRef)([]),
					te = z(Object(a.useState)({ width: Y, height: Y }), 2),
					ne = te[0],
					oe = te[1],
					re = Object(T.a)().t,
					ae = z(Object(a.useState)(!1), 2),
					ie = ae[0],
					le = ae[1],
					ce = Object(l.e)(p.a.isContentEditingEnabled),
					ue = null;
				Object(a.useEffect)(function () {
					var e = function (e) {
						Q.current && !Q.current.contains(e.target) && ve();
					};
					return (
						document.addEventListener("mousedown", e),
						function () {
							document.removeEventListener("mousedown", e);
						}
					);
				}, []);
				var se = function () {
					ue = setTimeout(function () {
						var e,
							t,
							o = Object(R.a)().querySelector(
								".ThumbnailsPanel.".concat(K, " #pageThumb").concat(n),
							),
							a = n + 1,
							i = b.a.getRotation(a),
							l = b.a.getDocument(V);
						if (l && l.getPageInfo(a)) {
							var c = l.loadCanvas({
								pageNumber: a,
								width: Y,
								height: Y,
								drawComplete:
									((e = _().mark(function e(t) {
										var o, a, l, c;
										return _().wrap(function (e) {
											for (;;)
												switch ((e.prev = e.next)) {
													case 0:
														(o = Object(R.a)().querySelector(
															".ThumbnailsPanel."
																.concat(K, " #pageThumb")
																.concat(n),
														)) &&
															((a = o.querySelector(".page-image")) &&
																o.removeChild(a),
															(t.className = "page-image"),
															(l = Math.min(Y / t.width, Y / t.height)),
															(t.style.width = "".concat(t.width * l, "px")),
															(t.style.height = "".concat(t.height * l, "px")),
															oe({
																width: Number(t.width),
																height: Number(t.height),
															}),
															Math.abs(i) &&
																((c = "rotate(".concat(
																	90 * i,
																	"deg) translate(-50%,-50%)",
																)),
																(t.style.transform = c),
																(t.style["transform-origin"] = "top left"),
																(t.style["ms-transform"] = c),
																(t.style["ms-transform-origin"] = "top left"),
																(t.style["-moz-transform"] = c),
																(t.style["-moz-transform-origin"] = "top left"),
																(t.style["-webkit-transform-origin"] =
																	"top left"),
																(t.style["-webkit-transform"] = c),
																(t.style["-o-transform"] = c),
																(t.style["-o-transform-origin"] = "top left")),
															o.appendChild(t)),
															r && r(n),
															u(n),
															le(!0);
													case 5:
													case "end":
														return e.stop();
												}
										}, e);
									})),
									(t = function () {
										var t = this,
											n = arguments;
										return new Promise(function (o, r) {
											var a = e.apply(t, n);
											function i(e) {
												H(a, o, r, i, l, "next", e);
											}
											function l(e) {
												H(a, o, r, i, l, "throw", e);
											}
											i(void 0);
										});
									}),
									function (e) {
										return t.apply(this, arguments);
									}),
								allowUseOfOptimizedThumbnail: !0,
							});
							s(n, o, c);
						}
					}, 50);
				};
				Object(a.useEffect)(function () {
					var e = function (e) {
							var t = e.contentChanged,
								o = e.moved,
								r = e.added,
								a = e.removed,
								i = n + 1,
								l = r.includes(i),
								c = t.some(function (e) {
									return i === e;
								}),
								u = Object.keys(o).some(function (e) {
									return i === parseInt(e);
								}),
								s = a.includes(i),
								d = b.a.getTotalPages();
							(a.length > 0 && n + 1 > d) || ((l || c || u || s) && se());
						},
						t = function () {
							le(!1), se();
						};
					return (
						b.a.addEventListener("pagesUpdated", e),
						b.a.addEventListener("rotationUpdated", t),
						B && se(),
						function () {
							b.a.removeEventListener("pagesUpdated", e),
								b.a.removeEventListener("rotationUpdated", t),
								clearTimeout(ue),
								h(n);
						}
					);
				}, []),
					Object(f.a)(
						function () {
							B ? (se(), r(n)) : F(n);
						},
						[B, V],
					);
				var de = E === n + 1,
					me = O[n],
					pe = "default",
					he = b.a.getRotation(n + 1);
				(((!he || 2 === he) && ne.width > ne.height) ||
					((1 === he || 3 === he) && ne.width < ne.height)) &&
					(pe = "rotated"),
					Object(a.useImperativeHandle)(t, function () {
						return {
							focusInput: function () {
								W && ie
									? (fe(ee.current[0]), J(0))
									: Z.current &&
										setTimeout(function () {
											fe(Z.current[0]), J(0);
										}, 0);
							},
						};
					});
				var fe = function (e) {
						e && ((e.ariaCurrent = "page"), e.focus());
					},
					be = function (e) {
						e && (e.ariaCurrent = void 0);
					},
					ge = function (e) {
						e.preventDefault(), ve();
					},
					ve = function () {
						Z.current.forEach(function (e) {
							be(e);
						});
					},
					ye = Object(a.useCallback)(
						function (e) {
							e.stopPropagation(), q(e);
							var t,
								n = {
									Tab: function () {
										return ge(e);
									},
									Escape: function () {
										return ge(e);
									},
								};
							n[e.key] &&
								(null === (t = n[e.key]) || void 0 === t || t.call(n));
							var o = {
								ArrowUp: function () {
									return we(e, -1);
								},
								ArrowDown: function () {
									return we(e, 1);
								},
								ArrowLeft: function () {
									return we(e, -1);
								},
								ArrowRight: function () {
									return we(e, 1);
								},
							};
							o[e.key] && !Ee && o[e.key]();
						},
						[Z.current, X],
					),
					we = function (e, t) {
						e.preventDefault(),
							0 !== Z.current.length &&
								J(function (e) {
									var n = e + t;
									return (
										n < 0
											? (n = Z.current.length - 1)
											: n >= Z.current.length && (n = 0),
										xe(Z.current[n]),
										n
									);
								});
					},
					xe = function (e) {
						Z.current.forEach(function (t) {
							t === e ? fe(t) : be(t);
						});
					};
				Object(a.useEffect)(
					function () {
						Q.current && (Z.current = Object(j.a)(Q.current));
					},
					[w, de, ie],
				),
					Object(a.useEffect)(
						function () {
							Q.current && (ee.current = Object(j.a)(Q.current));
						},
						[W, ie],
					);
				var Ee = W && ie;
				return i.a.createElement(
					"button",
					{
						className: d()({ Thumbnail: !0, active: de, selected: o && W }),
						onDragOver: function (e) {
							return v(e, n);
						},
						id: "Thumbnail-container-".concat(n),
						ref: Q,
						onKeyDown: function (e) {
							return ye(e);
						},
						onClick: function (e) {
							var t = e.target.type && "checkbox" === e.target.type;
							if (P && !k) {
								var o = e.ctrlKey || e.metaKey,
									r = e.shiftKey,
									a = M(A);
								if (r) {
									S(D.setThumbnailSelectingPages(!0));
									var i = c;
									null === i &&
										((i = E - 1), S(D.setShiftKeyThumbnailsPivotIndex(i)));
									var l = Math.min(i, n),
										u = Math.max(i, n);
									a = M(
										new Set(
											M(
												Array.from({ length: u - l + 1 }, function (e, t) {
													return t + l;
												}),
											),
										),
									);
								} else
									o || W
										? (S(D.setThumbnailSelectingPages(!0)),
											(o || t || G === L.a.THUMBNAIL) &&
												(0 !== A.length || W
													? A.includes(n)
														? (a = A.filter(function (e) {
																return n !== e;
															}))
														: a.push(n)
													: a.push(E - 1)),
											S(D.setShiftKeyThumbnailsPivotIndex(n)))
										: (a = [n]);
								var s = a[a.length - 1];
								!W && !r && S(D.setShiftKeyThumbnailsPivotIndex(s)),
									S(D.setSelectedPageThumbnails(a));
							} else U() && S(D.closeElement("leftPanel"));
							setTimeout(function () {
								(t && G !== L.a.THUMBNAIL) || b.a.setCurrentPage(n + 1);
							}, 0);
						},
						style: {
							width: Y,
							cursor: "pointer",
							background: "none",
							border: "none",
						},
						tabIndex: -1,
					},
					i.a.createElement(
						"div",
						{
							className: "container",
							style: { height: Y, width: Y },
							onDragStart: function (e) {
								return g(e, n);
							},
							draggable: y,
							tabIndex: -1,
						},
						i.a.createElement("div", {
							id: "pageThumb".concat(n),
							className: "thumbnail",
						}),
						W &&
							ie &&
							i.a.createElement(N.a, {
								className: "checkbox ".concat(pe),
								checked: A.includes(n),
								"aria-label": ""
									.concat(re("action.page"), " ")
									.concat(me, " ")
									.concat(re("formField.types.checkbox")),
								tabIndex: -1,
							}),
					),
					i.a.createElement("div", { className: "page-label" }, me),
					!W && de && w && !ce && i.a.createElement(I, { index: n }),
				);
			});
			(F.displayName = "Thumbnail"),
				(F.propTypes = {
					index: v.a.number,
					isSelected: v.a.bool,
					updateAnnotations: v.a.func,
					shiftKeyThumbnailPivotIndex: v.a.number,
					onFinishLoading: v.a.func,
					onLoad: v.a.func,
					onRemove: v.a.func,
					onDragStart: v.a.func,
					onDragOver: v.a.func,
					isDraggable: v.a.bool,
					shouldShowControls: v.a.bool,
					thumbnailSize: v.a.number,
					currentPage: v.a.number,
					pageLabels: v.a.array,
					selectedPageIndexes: v.a.array,
					isThumbnailMultiselectEnabled: v.a.bool,
					isReaderModeOrReadOnly: v.a.bool,
					dispatch: v.a.func,
					actions: v.a.object,
					isMobile: v.a.func,
					canLoad: v.a.bool,
					onCancel: v.a.func,
					isThumbnailSelectingPages: v.a.bool,
					thumbnailSelectionMode: v.a.string,
					activeDocumentViewerKey: v.a.number,
					panelSelector: v.a.string,
					parentKeyListener: v.a.func,
				});
			var W = F;
			function G() {
				return (G = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function V(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return K(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return K(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function K(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var q = i.a.forwardRef(function (e, t) {
				var n = V(
						Object(l.e)(function (e) {
							return [
								p.a.getCurrentPage(e),
								p.a.getPageLabels(e),
								p.a.getSelectedThumbnailPageIndexes(e),
								p.a.isThumbnailMultiselectEnabled(e),
								p.a.isReaderMode(e),
								p.a.isDocumentReadOnly(e),
								p.a.getShiftKeyThumbnailPivotIndex(e),
								p.a.isThumbnailSelectingPages(e),
								p.a.getThumbnailSelectionMode(e),
								p.a.getActiveDocumentViewerKey(e),
							];
						}, l.c),
						11,
					),
					o = n[0],
					r = n[1],
					a = n[2],
					c = n[3],
					u = n[4],
					s = n[5],
					d = n[6],
					f = n[7],
					b = n[8],
					g = n[9],
					v = n[10],
					y = Object(l.d)();
				return i.a.createElement(
					W,
					G({}, e, {
						ref: t,
						currentPage: o,
						pageLabels: r,
						selectedPageIndexes: a,
						isThumbnailMultiselectEnabled: c,
						isReaderModeOrReadOnly: u || s,
						dispatch: y,
						actions: h.a,
						isMobile: m.k,
						shiftKeyThumbnailPivotIndex: d,
						isThumbnailSelectingPages: f,
						thumbnailSelectionMode: b,
						selectionModes: v,
						activeDocumentViewerKey: g,
					}),
				);
			});
			q.displayName = "ThumbnailRedux";
			var Y = q,
				$ = (n(130), n(109), n(386)),
				X = n(34);
			n(1539);
			function J(e) {
				var t = e.moveToTop,
					n = e.moveToBottom;
				return i.a.createElement(
					i.a.Fragment,
					null,
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "moveToTop",
						img: "icon-page-move-up",
						onClick: t,
						title: "action.moveToTop",
					}),
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "moveToBottom",
						img: "icon-page-move-down",
						onClick: n,
						title: "action.moveToBottom",
					}),
				);
			}
			J.propTypes = { moveToTop: v.a.func, moveToBottom: v.a.func };
			var Q = J;
			function Z(e) {
				var t = e.onInsert,
					n = e.onReplace,
					o = e.onExtractPages,
					r = e.onDeletePages;
				return i.a.createElement(
					i.a.Fragment,
					null,
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlInsert",
						img: "icon-page-insertion-insert",
						onClick: t,
						title: "action.insert",
					}),
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlReplace",
						img: "icon-page-replacement",
						onClick: n,
						title: "action.replace",
					}),
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlExtract",
						img: "icon-page-manipulation-extract",
						onClick: o,
						title: "action.extract",
					}),
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlDelete",
						img: "icon-delete-line",
						onClick: r,
						title: "action.delete",
					}),
				);
			}
			Z.propTypes = {
				onInsert: v.a.func,
				onReplace: v.a.func,
				onExtractPages: v.a.func,
				onDeletePages: v.a.func,
			};
			var ee = Z;
			function te(e) {
				var t = e.onRotateClockwise,
					n = e.onRotateCounterClockwise;
				return i.a.createElement(
					i.a.Fragment,
					null,
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlRotateCounterClockwise",
						img: "icon-header-page-manipulation-page-rotation-counterclockwise-line",
						onClick: n,
						title: "action.rotateCounterClockwise",
					}),
					i.a.createElement(w.a, {
						className: "button-hover",
						dataElement: "thumbnailsControlRotateClockwise",
						img: "icon-header-page-manipulation-page-rotation-clockwise-line",
						onClick: t,
						title: "action.rotateClockwise",
					}),
				);
			}
			te.propTypes = {
				onRotateClockwise: v.a.func,
				onRotateCounterClockwise: v.a.func,
			};
			var ne = te,
				oe = n(170);
			function re() {
				return (re = Object.assign
					? Object.assign.bind()
					: function (e) {
							for (var t = 1; t < arguments.length; t++) {
								var n = arguments[t];
								for (var o in n)
									Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
							}
							return e;
						}).apply(this, arguments);
			}
			function ae(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return ce(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					le(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function ie(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					le(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function le(e, t) {
				if (e) {
					if ("string" == typeof e) return ce(e, t);
					var n = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === n && e.constructor && (n = e.constructor.name),
						"Map" === n || "Set" === n
							? Array.from(e)
							: "Arguments" === n ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
								? ce(e, t)
								: void 0
					);
				}
			}
			function ce(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			function ue(e) {
				var t,
					n = e.parentElement,
					o = Object(l.f)(),
					r = Object(l.d)(),
					c = Object(m.k)(),
					u = Object(l.e)(p.a.getSelectedThumbnailPageIndexes),
					s = Object(l.e)(function (e) {
						return n && "leftPanel" !== n
							? p.a.getPanelWidth(e, n)
							: p.a.getLeftPanelWidth(e);
					}),
					d = Object(l.e)(p.a.pageDeletionConfirmationModalEnabled),
					f = Object(l.e)(p.a.isInDesktopOnlyMode),
					g = Object(l.e)(p.a.getMultiPageManipulationControlsItems, l.c),
					v = ie(Object(a.useState)(!1), 2),
					C = v[0],
					T = v[1],
					j = Object(a.useMemo)(
						function () {
							return u.map(function (e) {
								return e + 1;
							});
						},
						[u],
					),
					A = Object(a.useMemo)(
						function () {
							return {
								onReplace: function () {
									return !Object(y.k)(j, r) && Object(y.m)(r);
								},
								onExtractPages: function () {
									return !Object(y.k)(j, r) && Object(y.e)(j, r);
								},
								onDeletePages: function () {
									return !Object(y.k)(j, r) && Object(y.b)(j, r, d);
								},
								onRotateCounterClockwise: function () {
									return !Object(y.k)(j, r) && Object(y.p)(j);
								},
								onRotateClockwise: function () {
									return !Object(y.k)(j, r) && Object(y.o)(j);
								},
								onInsert: function () {
									return (
										!Object(y.k)(j, r) &&
										(r(h.a.closeElement(x.a.PAGE_MANIPULATION_OVERLAY)),
										void r(h.a.openElement("insertPageModal")))
									);
								},
								moveToTop: function () {
									return !Object(y.k)(j, r) && Object(y.j)(j);
								},
								moveToBottom: function () {
									return !Object(y.k)(j, r) && Object(y.i)(j);
								},
								pageNumbers: j,
							};
						},
						[j, d],
					),
					P = A.onRotateClockwise,
					k = A.onRotateCounterClockwise,
					S = b.a.getDocument(),
					I = null == S ? void 0 : S.type,
					L = I === O.a.XOD,
					N = I === O.a.OFFICE || I === O.a.LEGACY_OFFICE;
				if (!f && c)
					try {
						t =
							Object(R.a)().querySelector(".App").getBoundingClientRect()
								.width - 88;
					} catch (e) {
						t = (s || X.d) - 88;
					}
				else t = (s || X.d) - 88;
				var D = t < 190,
					M = t > 290;
				Object(a.useEffect)(
					function () {
						var e,
							t = [];
						e = D ? 1 : M ? 3 : 2;
						var n = 0;
						g.forEach(function (r) {
							var a = !1;
							(n < e ||
								(0 === t.length &&
									"divider" === (null == r ? void 0 : r.type))) &&
								(a = !0),
								r && "divider" !== r.type && n++,
								!a &&
									t.push.apply(
										t,
										ae(
											(function (e) {
												return "leftPanelPageTabsRotate" === e.dataElement
													? Object(oe.e)(o, !0)
													: "leftPanelPageTabsMove" === e.dataElement
														? Object(oe.a)(o, !0)
														: "leftPanelPageTabsMore" === e.dataElement
															? Object(oe.c)(o, !0)
															: "customPageOperation" === e.type
																? Object(oe.b)(o, e)
																: "divider" === e.type
																	? ["divider"]
																	: void 0;
											})(r),
										),
									);
						});
						var a = {
							dataElement: x.a.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,
							className: x.a.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,
							items: t,
						};
						M && r(h.a.closeElement(a.dataElement)),
							a.items.length
								? (r(h.a.updateFlyout(a.dataElement, a)), T(!0))
								: (r(h.a.removeFlyout(a.dataElement)), T(!1));
					},
					[o, M, D, g],
				);
				var _ = Object(a.useMemo)(
					function () {
						var e = !1,
							t = 0;
						return g.map(function (n, o) {
							var r;
							return (
								(r = D ? 0 : M ? 2 : 1),
								"divider" !== (null == n ? void 0 : n.type) || e
									? t > r
										? null
										: (n && "divider" !== n.type && t++,
											"leftPanelPageTabsRotate" === n.dataElement
												? i.a.createElement(
														ne,
														re({}, A, { key: "leftPanelPageTabsRotate" }),
													)
												: "leftPanelPageTabsMove" === n.dataElement
													? i.a.createElement(
															Q,
															re({}, A, { key: "leftPanelPageTabsMove" }),
														)
													: "leftPanelPageTabsMore" === n.dataElement
														? i.a.createElement(
																ee,
																re({}, A, { key: "leftPanelPageTabsMore" }),
															)
														: "customPageOperation" === n.type
															? n.operations
																? n.operations.map(function (e) {
																		return i.a.createElement(w.a, {
																			key: e.dataElement,
																			className: "button-hover",
																			dataElement: e.dataElement,
																			img: e.img,
																			onClick: function () {
																				return e.onClick(j);
																			},
																			title: e.title,
																		});
																	})
																: (t--, null)
															: null)
									: (t > r && (e = !0),
										i.a.createElement("div", {
											key: "divider".concat(o),
											className: "divider",
										}))
							);
						});
					},
					[g, A, D, M],
				);
				return L || N || (null != S && S.isWebViewerServerDocument())
					? i.a.createElement(
							"div",
							{ className: "PageControlContainer root small" },
							i.a.createElement(ne, {
								onRotateClockwise: P,
								onRotateCounterClockwise: k,
							}),
						)
					: i.a.createElement(
							"div",
							{ className: "PageControlContainer root" },
							_,
							C &&
								i.a.createElement(
									"div",
									{ className: "dropdown-menu" },
									i.a.createElement(E.a, {
										dataElement: "".concat(
											x.a.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,
											"Button",
										),
										toggleElement: x.a.PAGE_MANIPULATION_FLYOUT_MULTI_SELECT,
										title: "action.more",
										img: "icon-tool-more",
									}),
									i.a.createElement("div", { className: "indicator" }),
								),
						);
			}
			ue.propTypes = { parentElement: v.a.string };
			var se = ue;
			n(1668);
			function de(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return me(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return me(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function me(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			function pe(e, t) {
				for (
					var n = "",
						o = e.sort(function (e, t) {
							return e - t;
						}),
						r = null,
						a = 0;
					o.length > a;
					a++
				)
					o[a + 1] === o[a] + 1
						? (r = null !== r ? r : o[a])
						: null !== r
							? ((n = "".concat(n).concat(t[r], "-").concat(t[o[a]], ", ")),
								(r = null))
							: (n = "".concat(n).concat(t[o[a]], ", "));
				return n.slice(0, -2);
			}
			var he = function (e) {
				var t = e.shouldShowControls,
					n = e.parentElement,
					o = de(Object(T.a)(), 1)[0],
					r = Object(l.d)(),
					c = de(
						Object(l.e)(function (e) {
							return [
								p.a.getSelectedThumbnailPageIndexes(e),
								p.a.isElementDisabled(e, "documentControl"),
								p.a.getPageLabels(e),
								p.a.isThumbnailSelectingPages(e),
								p.a.getFeatureFlags(e),
							];
						}),
						5,
					),
					u = c[0],
					s = c[1],
					d = c[2],
					m = c[3],
					f = c[4],
					g = pe(u, d),
					v = de(Object(a.useState)(g), 2),
					y = v[0],
					x = v[1],
					E = de(Object(a.useState)(g), 2),
					C = E[0],
					O = E[1],
					j = f.customizableUI;
				Object(a.useEffect)(
					function () {
						x(pe(u, d));
					},
					[x, u, t, d],
				);
				var A = function () {
					r(h.a.setThumbnailSelectingPages(!0));
				};
				return s
					? null
					: i.a.createElement(
							"div",
							{
								className: "documentControlsContainer",
								"data-element": "documentControl",
							},
							t
								? i.a.createElement(
										"div",
										{ className: "documentControls" },
										i.a.createElement("div", { className: "divider" }),
										m && i.a.createElement(se, { parentElement: n }),
										j &&
											i.a.createElement(
												"label",
												{
													className: "documentControlsLabel",
													htmlFor: "pageNumbersInput",
												},
												i.a.createElement(
													"span",
													null,
													o("option.thumbnailPanel.multiSelectPages"),
													" -",
												),
												i.a.createElement(
													"span",
													{ className: "multiSelectExampleLabel" },
													o("option.thumbnailPanel.multiSelectPagesExample"),
												),
											),
										i.a.createElement(
											"div",
											{ className: "documentControlsInput" },
											i.a.createElement("input", {
												name: "pageNumbersInput",
												onBlur: function (e) {
													var t = e.target.value.replace(/ /g, ""),
														n = t ? Object($.a)(t, d) : [],
														o = n.map(function (e) {
															return e - 1;
														});
													if (n.length || !t) {
														r(h.a.setSelectedPageThumbnails(o));
														var a = pe(u, d);
														x(a), O(a);
													} else x(C);
													u.length > 0 &&
														!m &&
														setTimeout(function () {
															y !== C && A();
														}, 100);
												},
												onChange: function (e) {
													x(e.target.value);
												},
												value: y,
												placeholder: j ? "" : "1, 3, 5-10",
												"aria-label": o(
													"option.thumbnailPanel.enterPageNumbers",
												),
												className: "pagesInput",
												type: "text",
											}),
											i.a.createElement(
												"div",
												{ className: "documentControlsButton" },
												m
													? i.a.createElement(w.a, {
															img: "icon-close",
															title: "option.documentControls.closeTooltip",
															onClick: function () {
																r(
																	h.a.setSelectedPageThumbnails([
																		b.a.getCurrentPage() - 1,
																	]),
																),
																	r(h.a.setThumbnailSelectingPages(!1));
															},
															dataElement: "thumbCloseMultiSelect",
														})
													: i.a.createElement(w.a, {
															img: "icon-tool-select-pages",
															title: "option.documentControls.selectTooltip",
															onClick: A,
															dataElement: "thumbMultiSelect",
														}),
											),
										),
									)
								: null,
						);
			};
			he.propTypes = {
				isDisabled: v.a.bool,
				pageLabels: v.a.arrayOf(v.a.string),
				toggleDocumentControl: v.a.func,
				shouldShowControls: v.a.bool,
			};
			var fe = he,
				be = n(449),
				ge = n(278),
				ve = n(67),
				ye = n(55),
				we = function (e, t) {
					var n =
						!(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2];
					return function (o) {
						return (
							o(h.a.openElement(x.a.LOADING_MODAL)),
							new Promise(function (r, a) {
								b.a
									.mergeDocument(e, t)
									.then(function (e) {
										o(h.a.closeElement(x.a.LOADING_MODAL)),
											b.a.setCurrentPage(t),
											n && Object(ve.a)(ye.a.DOCUMENT_MERGED, e),
											r(e);
									})
									.catch(function (e) {
										a(e), o(h.a.closeElement(x.a.LOADING_MODAL));
									});
							})
						);
					};
				};
			n(1670);
			function xe(e) {
				return (xe =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function Ee(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var o = Object.getOwnPropertySymbols(e);
					t &&
						(o = o.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, o);
				}
				return n;
			}
			function Ce(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? Ee(Object(n), !0).forEach(function (t) {
								Oe(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: Ee(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function Oe(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== xe(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var o = n.call(e, t || "default");
								if ("object" !== xe(o)) return o;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === xe(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function Te(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var o,
								r,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (o = a.call(n)).done) &&
										(l.push(o.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return je(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return je(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function je(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, o = new Array(t); n < t; n++) o[n] = e[n];
				return o;
			}
			var Ae = function (e) {
				var t = e.panelSelector,
					n = e.parentDataElement,
					o = Object(l.e)(function (e) {
						return p.a.isElementOpen(e, "leftPanel");
					}),
					s = Object(l.e)(function (e) {
						return p.a.isElementDisabled(e, "thumbnailsPanel");
					}),
					f = Object(l.e)(p.a.getTotalPages),
					g = Object(l.e)(p.a.getCurrentPage),
					v = Object(l.e)(function (e) {
						return p.a.getSelectedThumbnailPageIndexes(e);
					}, l.c),
					y = Object(l.e)(p.a.getIsThumbnailMergingEnabled),
					E = Object(l.e)(p.a.getIsThumbnailReorderingEnabled),
					C = Object(l.e)(p.a.getIsMultipleViewerMerging),
					O = Object(l.e)(function (e) {
						return p.a.isElementDisabled(e, "thumbnailControl");
					}),
					j = Object(l.e)(function (e) {
						return p.a.isElementDisabled(e, "thumbnailsSizeSlider");
					}),
					A = Object(l.e)(p.a.isReaderMode),
					P = Object(l.e)(p.a.isDocumentReadOnly),
					k = Object(l.e)(function (e) {
						return p.a.getTotalPages(e, 2);
					}),
					S = Object(l.e)(p.a.getActiveDocumentViewerKey),
					I = Object(l.e)(
						p.a.openingPageManipulationOverlayByRightClickEnabled,
					),
					L = Object(l.e)(p.a.getFeatureFlags, l.c),
					N = Object(l.e)(p.a.isContentEditingEnabled),
					D = Te(Object(T.a)(), 1)[0],
					M = Object(a.useRef)(),
					_ = Object(a.useRef)([]),
					H = Object(a.useRef)([]),
					z = Object(a.useRef)(null),
					U = Te(Object(a.useState)(!1), 2),
					B = U[0],
					F = U[1],
					W = Te(Object(a.useState)(!0), 2),
					G = W[0],
					V = W[1],
					K = Te(Object(a.useState)(0), 2),
					q = K[0],
					$ = K[1],
					X = Te(Object(a.useState)(0), 2),
					J = X[0],
					Q = X[1],
					Z = Te(Object(a.useState)(null), 2),
					ee = Z[0],
					te = Z[1],
					ne = Te(Object(a.useState)(!1), 2),
					oe = ne[0],
					re = ne[1],
					ae = Te(Object(a.useState)(1), 2),
					ie = ae[0],
					le = ae[1],
					ce = Te(Object(a.useState)(!1), 2),
					ue = ce[0],
					se = ce[1],
					de = Te(Object(a.useState)(0), 2),
					me = de[0],
					pe = de[1],
					he = Object(a.useRef)([]),
					xe = Object(a.useRef)([]),
					Ee = Object(a.useRef)(null),
					Oe = Te(Object(a.useState)(150), 2),
					je = Oe[0],
					Ae = Oe[1],
					Pe = Te(Object(a.useState)(0), 2),
					ke = Pe[0],
					Se = Pe[1],
					Ie = Te(Object(a.useState)(0), 2),
					Le = Ie[0],
					Ne = Ie[1],
					Re = 2 === S ? k : f,
					De = null == L ? void 0 : L.customizableUI,
					Me = Object(l.d)(),
					_e = {};
				Object(a.useEffect)(function () {
					var e = function (e) {
						Ee.current && !Ee.current.contains(e.target) && Be(Ee.current);
					};
					return (
						document.addEventListener("mousedown", e),
						function () {
							document.removeEventListener("mousedown", e);
						}
					);
				}, []);
				var He = Object(a.useCallback)(
						function (e, t, n) {
							var o;
							e.stopPropagation();
							var r = {
									ArrowLeft: function () {
										return We(e, t, -1);
									},
									ArrowRight: function () {
										return We(e, t, 1);
									},
									ArrowUp: function () {
										return We(e, t, -n);
									},
									ArrowDown: function () {
										return We(e, t, n);
									},
								},
								a = {
									Enter: function () {
										return Ge(e, t);
									},
									Tab: function () {
										return Fe(t);
									},
								};
							r[e.key] && (o = r[e.key]()),
								a[e.key] && a[e.key](),
								void 0 !== o &&
									o !== t &&
									(pe(o), ze(he.current[o]), Ue(he.current[t]));
						},
						[xe, he],
					),
					ze = function (e) {
						(e.tabIndex = 0),
							(e.ariaCurrent = "page"),
							(e.style.outline = "var(--focus-visible-outline)"),
							e.focus(),
							(Ee.current = e);
					},
					Ue = function (e) {
						(e.tabIndex = -1), (e.ariaCurrent = void 0), Be(e);
					},
					Be = function (e) {
						e.style.outline = "none";
					},
					Fe = function (e) {
						Be(he.current[e]);
					},
					We = function (e, t, n) {
						var o = t + n;
						return o < 0 || o >= Re ? t : o;
					},
					Ge = function (e, t) {
						e.preventDefault(),
							ze(he.current[t]),
							b.a.setCurrentPage(t + 1),
							xe.current[t].focusInput();
					},
					Ve = Object(a.useCallback)(
						function (e, t) {
							e.stopPropagation();
							var n,
								o = {
									Tab: function () {
										return qe(e, t);
									},
									Escape: function () {
										return Ke(e, t);
									},
								};
							o[e.key] &&
								(null === (n = o[e.key]) || void 0 === n || n.call(o));
						},
						[he],
					),
					Ke = function (e, t) {
						e.preventDefault(), ze(he.current[t]);
					},
					qe = function (e, t) {
						var n = t + (e.shiftKey ? -1 : 1);
						(n < 0 || n >= Re) && (n = t),
							pe(n),
							ze(he.current[n]),
							n !== t && Ue(he.current[t]);
					},
					Ye = function (e) {
						var t = H.current && H.current[e] && H.current[e].element;
						if (t) {
							var n = e + 1,
								o = b.a.getPageWidth(n),
								a = (function (e, t) {
									var n, o, r;
									return (
										e > t
											? ((r = e / je), (n = je), (o = Math.round(t / r)))
											: ((r = t / je), (n = Math.round(e / r)), (o = je)),
										{ width: n, height: o }
									);
								})(o, b.a.getPageHeight(n)),
								i = a.width,
								l = a.height,
								c =
									t.querySelector(".annotation-image") ||
									document.createElement("canvas");
							(c.className = "annotation-image"),
								(c.role = "img"),
								(c.ariaLabel = "".concat(D("action.page"), " ").concat(n)),
								(c.style.maxWidth = "".concat(je, "px")),
								(c.style.maxHeight = "".concat(je, "px"));
							var u = c.getContext("2d"),
								s = 1,
								d = b.a.getCompleteRotation(n);
							d < 0 && (d += 4);
							var m = window.Core.getCanvasMultiplier();
							d % 2 == 0
								? ((c.width = i), (c.height = l), (s = c.width / o), (s /= m))
								: ((c.width = l), (c.height = i), (s = c.height / o), (s /= m)),
								t.appendChild(c),
								b.a.setAnnotationCanvasTransform(u, s, d);
							var p = { pageNumber: n, overrideCanvas: c },
								h = t.querySelector(".page-image");
							if (h)
								(p = Ce(
									Ce({}, p),
									{},
									{ overridePageRotation: d, overridePageCanvas: h },
								)),
									_e[n] || (_e[n] = r()(b.a.drawAnnotations, 112)),
									(0, _e[n])(p);
						}
					};
				if (
					(Object(a.useEffect)(function () {
						var e = function () {
								V(!1);
							},
							t = function (e) {
								e || V(!0);
							},
							n = function () {
								var e;
								"officeEditor" ===
								(null === (e = b.a.getDocument()) || void 0 === e
									? void 0
									: e.getType())
									? F(!0)
									: F(!1),
									(_e = {}),
									Me(h.a.setSelectedPageThumbnails([]));
							},
							o = function () {
								z.current &&
									(b.a.setCurrentPage(z.current), (z.current = null));
							};
						return (
							b.a.addEventListener("beginRendering", e),
							b.a.addEventListener("finishedRendering", t),
							b.a.addEventListener("documentLoaded", n),
							b.a.addEventListener("pageComplete", o),
							b.a.getDocument() && n(),
							function () {
								b.a.removeEventListener("beginRendering", e),
									b.a.removeEventListener("finishedRendering", t),
									b.a.removeEventListener("documentLoaded", n),
									b.a.removeEventListener("pageComplete", o);
							}
						);
					}, []),
					Object(a.useEffect)(
						function () {
							var e = function (e) {
								if (e) {
									var t = Array.from(v);
									e.removed &&
										(t = t.filter(function (t) {
											return -1 === e.removed.indexOf(t + 1);
										})),
										e.moved &&
											(t = t.map(function (t) {
												return e.moved[t + 1] ? e.moved[t + 1] - 1 : t;
											}));
									var n = e.added && e.added[0] - 1 <= t[0];
									1 === t.length &&
										n &&
										(t = e.added.map(function (e) {
											return e - 1;
										})),
										Me(h.a.setSelectedPageThumbnails(t));
								}
							};
							return (
								b.a.addEventListener("pagesUpdated", e),
								function () {
									return b.a.removeEventListener("pagesUpdated", e);
								}
							);
						},
						[v],
					),
					Object(a.useEffect)(
						function () {
							var e;
							null === (e = M.current) ||
								void 0 === e ||
								e.scrollToRow(Math.floor((g - 1) / ie));
							var t = function (e) {
									var t = [];
									e.forEach(function (e) {
										var n = e.PageNumber - 1;
										!e.Listable || t.indexOf(n) > -1 || (t.push(n), Ye(n));
									});
								},
								n = function (e) {
									var t,
										n = e - 1;
									null === (t = M.current) ||
										void 0 === t ||
										t.scrollToRow(Math.floor(n / ie));
								};
							return (
								b.a.addEventListener("pageNumberUpdated", n),
								b.a.addEventListener("annotationChanged", t),
								b.a.addEventListener("annotationHidden", t),
								function () {
									b.a.removeEventListener("pageNumberUpdated", n),
										b.a.removeEventListener("annotationChanged", t),
										b.a.removeEventListener("annotationHidden", t);
								}
							);
						},
						[je, ie],
					),
					Object(a.useEffect)(
						function () {
							(A || P) &&
								(Me(h.a.setSelectedPageThumbnails([])),
								Me(h.a.setThumbnailSelectingPages(!1)));
						},
						[A, P],
					),
					s || B || (!o && !t && !De))
				)
					return null;
				var $e = function () {
						se(!1), te(null);
					},
					Xe = function (e, t, n) {
						var o,
							r = new Date().getTime();
						return e < Re - 1 && e > 0 && r - ke >= n
							? (null === (o = M.current) ||
									void 0 === o ||
									o.scrollToRow(Math.floor((e + t) / ie)),
								Se(r),
								e + t)
							: e;
					},
					Je = function (e, t) {
						if ((e.preventDefault(), e.stopPropagation(), E || y)) {
							var n = e.target.getBoundingClientRect();
							re(
								ie > 1
									? !(e.pageX > n.x + n.width / 2)
									: !(e.pageY > n.y + n.height / 2),
							),
								te(t);
							var o = Object(R.a)()
									.querySelector("#virtualized-thumbnails-container")
									.getBoundingClientRect(),
								r = o.y,
								a = o.bottom;
							e.pageY < r + 100
								? Ne(Xe(t, -1, 400))
								: e.pageY > a - 100 && Ne(Xe(t, 1, 400));
						}
					},
					Qe = function () {
						Ne(Xe(Le, 1, 200));
					},
					Ze = function () {
						Ne(Xe(Le, -1, 200));
					},
					et = function () {
						return window.isApryseWebViewerWebComponent
							? Object(R.a)().host.id
							: window.frameElement.id;
					},
					tt = function (e, t) {
						Ne(t), se(!0);
						var n,
							o = v.some(function (e) {
								return e === t;
							}),
							r = o
								? v.map(function (e) {
										return e + 1;
									})
								: [t + 1];
						Object(ve.a)(ye.a.THUMBNAIL_DRAGGED),
							e.dataTransfer.setData("text", ""),
							r.length > 1 && e.dataTransfer.setDragImage(new Image(), 0, 0),
							y &&
								C &&
								((e.dataTransfer.dropEffect = "move"),
								(e.dataTransfer.effectAllowed = "all"),
								e.dataTransfer.setData("dataTransferWebViewerFrame", et()),
								(n = r),
								(window.extractedDataPromise = Object(ge.a)(n)),
								(window.pagesExtracted = n)),
							o || Me(h.a.setSelectedPageThumbnails([t])),
							b.a.setCurrentPage(t + 1);
					},
					nt = function (e) {
						e.preventDefault();
						var t,
							n = e.dataTransfer.files,
							o = oe ? ee + 1 : ee + 2;
						m.f || (t = e.dataTransfer.getData("dataTransferWebViewerFrame"));
						var r,
							a,
							i = (t && et() !== t) || n.length,
							l = g - 1;
						if (y && i)
							t && et() !== t
								? Me(
										((r = t),
										(a = o),
										function (e) {
											return new Promise(function (t, n) {
												var o = window.parent.document.querySelector(
													"#".concat(r),
												);
												o ||
													(console.warn(
														"Could not find other instance of WebViewer",
													),
													n());
												var i = o.contentWindow;
												window.isApryseWebViewerWebComponent && (i = window);
												var l = i.extractedDataPromise;
												l ||
													(console.warn(
														"Could not retrieve data from other instance of WebViewer",
													),
													n()),
													e(h.a.openElement(x.a.LOADING_MODAL)),
													l
														.then(function (n) {
															e(we(n, a, !1)).then(function (n) {
																var o = n.filename,
																	r = n.pages;
																Object(ve.a)(ye.a.DOCUMENT_MERGED, {
																	filename: o,
																	pages: i.pagesExtracted,
																}),
																	e(h.a.closeElement(x.a.LOADING_MODAL)),
																	t({ filename: o, pages: r });
															});
														})
														.catch(function (t) {
															e(h.a.closeElement(x.a.LOADING_MODAL)), n(t);
														});
											});
										}),
									)
								: n.length &&
									Array.from(n).forEach(function (e) {
										Me(we(e, o));
									});
						else if (E && !i && null !== ee) {
							var c = oe ? ee + 1 : ee + 2,
								u = v.some(function (e) {
									return e === l;
								})
									? v.map(function (e) {
											return e + 1;
										})
									: [g];
							(z.current =
								c -
								u.filter(function (e) {
									return e < c;
								}).length),
								b.a.movePages(u, c);
							for (var s = [], d = 0; d < u.length; d++) s.push(z.current + d);
							Object(ve.a)(ye.a.THUMBNAIL_DROPPED, {
								pageNumbersBeforeMove: u,
								pagesNumbersAfterMove: s,
								numberOfPagesMoved: s.length,
							});
						}
						te(null), se(!1);
					},
					ot = function (e, t, n) {
						at(e) ||
							it(e) ||
							((H.current[e] = { element: t, loaded: !1 }),
							_.current.push({ pageIndex: e, id: n }));
					},
					rt = function (e) {
						var t = ct(e);
						-1 !== t && _.current.splice(t, 1);
					},
					at = function (e) {
						var t;
						return null === (t = H.current[e]) || void 0 === t
							? void 0
							: t.loaded;
					},
					it = function (e) {
						return -1 !== ct(e);
					},
					lt = function (e) {
						var t = ct(e);
						-1 !== t &&
							(b.a.cancelLoadThumbnail(_.current[t].id),
							_.current.splice(t, 1));
					},
					ct = function (e) {
						return _.current.findIndex(function (t) {
							return t.pageIndex === e;
						});
					},
					ut = function (e) {
						var t, n;
						lt(e);
						var o =
							null === (t = H.current[e]) ||
							void 0 === t ||
							null === (n = t.element) ||
							void 0 === n
								? void 0
								: n.querySelectorAll("canvas");
						null != o &&
							o.length &&
							o.forEach(function (e) {
								(e.height = 0), (e.width = 0);
							}),
							_e[e] && _e[e].cancel(),
							(H.current[e] = null);
					},
					st = function (e) {
						var n = e.index,
							o = e.key,
							r = e.style,
							a = d()({ columnsOfThumbnails: ie > 1, row: !0 }),
							l = !(A || P);
						return i.a.createElement(
							"div",
							{
								role: "row",
								"aria-label": "row",
								className: a,
								key: o,
								style: r,
							},
							new Array(ie).fill().map(function (e, o) {
								var r = n * ie + o,
									a = l && (y || E),
									c = a && ee === r;
								return r < Re
									? i.a.createElement(
											i.a.Fragment,
											{ key: r },
											(ie > 1 || 0 === r) &&
												c &&
												oe &&
												i.a.createElement("div", {
													key: "placeholder1-".concat(r),
													className: "thumbnailPlaceholder",
												}),
											i.a.createElement(
												"td",
												{
													ref: function (e) {
														return (he.current[r] = e);
													},
													key: r,
													role: "gridcell",
													tabIndex: me === r ? 0 : -1,
													"aria-current": me === r ? "page" : void 0,
													onDragEnd: $e,
													className: "cellThumbContainer",
													onKeyDown: function (e) {
														return He(e, r, ie);
													},
													onContextMenu: function (e) {
														return (
															I &&
															((n = r),
															(t = e).preventDefault(),
															b.a.setCurrentPage(n + 1),
															v.includes(n) ||
																Me(h.a.setSelectedPageThumbnails([n])),
															void (
																A ||
																P ||
																N ||
																(Me(
																	h.a.setFlyoutPosition({
																		x: t.pageX,
																		y: t.pageY,
																	}),
																),
																Me(h.a.openElements([x.a.PAGE_MANIPULATION])))
															))
														);
														var t, n;
													},
													onFocus: function (e) {
														return (function (e, t) {
															he.current &&
																he.current.includes(e.target) &&
																ze(he.current[t]);
														})(e, r);
													},
												},
												i.a.createElement(Y, {
													ref: function (e) {
														return (xe.current[r] = e);
													},
													isDraggable: a,
													isSelected: v.includes(r),
													index: r,
													canLoad: G,
													onLoad: ot,
													onCancel: lt,
													onRemove: ut,
													onDragStart: tt,
													onDragOver: Je,
													onFinishLoading: rt,
													updateAnnotations: Ye,
													shouldShowControls: l,
													thumbnailSize: je,
													panelSelector: t,
													parentKeyListener: function (e) {
														return Ve(e, r);
													},
												}),
											),
											c &&
												!oe &&
												i.a.createElement("div", {
													key: "placeholder2-".concat(r),
													className: "thumbnailPlaceholder",
												}),
										)
									: null;
							}),
						);
					},
					dt = function () {
						le(Math.min(16, Math.max(1, Math.floor(J / je))));
					},
					mt = O ? Number(je) + 50 : Number(je) + 80,
					pt = !(A || P || N),
					ht = { height: "".concat(25, "px") },
					ft = function (e, t) {
						var n = "1000" * Number(t);
						n < 100 && (n = 100), Ae(n), dt();
					};
				return i.a.createElement(
					i.a.Fragment,
					null,
					!j &&
						i.a.createElement(
							"div",
							{
								"data-element": "thumbnailsSizeSlider",
								className: "thumbnail-slider-container",
							},
							i.a.createElement(w.a, {
								img: "icon-zoom-thumb-out",
								title: "action.zoomOut",
								hideTooltipShortcut: !0,
								onClick: function () {
									je - "50" > "50" && (Ae(je - "50"), dt());
								},
								dataElement: "zoomThumbOutButton",
							}),
							De &&
								i.a.createElement(be.a, {
									dataElement: "thumbnailsSizeSlider",
									property: "zoom",
									displayProperty: "zoom",
									min: 0,
									max: 1,
									step: 0.01,
									value: je / 1e3,
									getDisplayValue: function () {
										return je;
									},
									onSliderChange: ft,
									onStyleChange: ft,
									shouldHideSliderTitle: !0,
									shouldHideSliderValue: !0,
								}),
							!De &&
								i.a.createElement("input", {
									role: "slider",
									type: "range",
									"aria-label": "thumbnail size slider",
									min: "100",
									max: "1000",
									value: je,
									"aria-valuemin": "100",
									"aria-valuemax": "1000",
									"aria-valuenow": je,
									onChange: function (e) {
										Ae(Number(e.target.value)), dt();
									},
									step: "50",
									className: "thumbnail-slider",
									id: "thumbnailSize",
								}),
							i.a.createElement(w.a, {
								img: "icon-zoom-thumb-in",
								title: "action.zoomIn",
								hideTooltipShortcut: !0,
								onClick: function () {
									je + Number("50") < 1001 && (Ae(je + Number("50")), dt());
								},
								dataElement: "zoomThumbInButton",
							}),
						),
					i.a.createElement(
						u.a,
						{
							bounds: !0,
							onResize: function (e) {
								var t = e.bounds;
								$(t.height),
									Q(t.width),
									le(Math.min(16, Math.max(1, Math.floor(t.width / je))));
							},
							key: je,
						},
						function (e) {
							var n = e.measureRef;
							return i.a.createElement(
								"div",
								{
									className: "Panel ThumbnailsPanel ".concat(t),
									id: "virtualized-thumbnails-container",
									"data-element": "thumbnailsPanel",
									onDrop: nt,
									ref: n,
								},
								i.a.createElement(
									"div",
									{ className: "virtualized-thumbnails-container" },
									ue
										? i.a.createElement("div", {
												className: "thumbnailAutoScrollArea",
												onDragOver: Ze,
												style: ht,
											})
										: "",
									i.a.createElement(c.c, {
										ref: M,
										height: q,
										width: J,
										rowHeight: mt,
										rowCount: Math.ceil(Re / ie),
										rowRenderer: st,
										overscanRowCount: 3,
										className: "thumbnailsList",
										scrollToIndex: Math.floor((g - 1) / ie),
										role: "grid",
										"aria-label": D("component.thumbnailsPanel"),
										tabIndex: -1,
									}),
									ue
										? i.a.createElement("div", {
												className: "thumbnailAutoScrollArea",
												onDragOver: Qe,
												style: Ce(Ce({}, ht), {}, { bottom: "70px" }),
											})
										: "",
								),
							);
						},
					),
					i.a.createElement(fe, {
						shouldShowControls: pt,
						parentElement: n || t,
					}),
				);
			};
			t.default = Ae;
		},
	},
]);
//# sourceMappingURL=chunk.39.js.map
