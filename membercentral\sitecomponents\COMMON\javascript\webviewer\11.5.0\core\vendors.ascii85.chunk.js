/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[17],
		{
			637: function (ya, va, r) {
				(function (la) {
					function na(e) {
						this.wi = e = e || {};
						if (Array.isArray(e.table)) {
							var f = [];
							e.table.forEach(function (a, w) {
								f[a.charCodeAt(0)] = w;
							});
							e.xza = e.table;
							e.bwa = f;
						}
					}
					var ma =
							la.from ||
							function () {
								switch (arguments.length) {
									case 1:
										return new la(arguments[0]);
									case 2:
										return new la(arguments[0], arguments[1]);
									case 3:
										return new la(arguments[0], arguments[1], arguments[2]);
									default:
										throw new Exception("unexpected call.");
								}
							},
						ja =
							la.allocUnsafe ||
							function (e) {
								return new la(e);
							},
						ia = (function () {
							return "undefined" === typeof Uint8Array
								? function (e) {
										return Array(e);
									}
								: function (e) {
										return new Uint8Array(e);
									};
						})(),
						ca = String.fromCharCode(0),
						y = ca + ca + ca + ca,
						x = ma("<~").EG(0),
						n = ma("~>").EG(0),
						h = (function () {
							var e = Array(85),
								f;
							for (f = 0; 85 > f; f++) e[f] = String.fromCharCode(33 + f);
							return e;
						})(),
						b = (function () {
							var e = Array(256),
								f;
							for (f = 0; 85 > f; f++) e[33 + f] = f;
							return e;
						})();
					ca = ya.exports = new na();
					na.prototype.encode = function (e, f) {
						var a = ia(5),
							w = e,
							z = this.wi,
							aa,
							ea;
						"string" === typeof w
							? (w = ma(w, "binary"))
							: w instanceof la || (w = ma(w));
						f = f || {};
						if (Array.isArray(f)) {
							e = f;
							var ba = z.aL || !1;
							var fa = z.NW || !1;
						} else
							(e = f.table || z.xza || h),
								(ba = void 0 === f.aL ? z.aL || !1 : !!f.aL),
								(fa = void 0 === f.NW ? z.NW || !1 : !!f.NW);
						z = 0;
						var ha = Math.ceil((5 * w.length) / 4) + 4 + (ba ? 4 : 0);
						f = ja(ha);
						ba && (z += f.write("<~", z));
						var pa = (aa = ea = 0);
						for (ha = w.length; pa < ha; pa++) {
							var oa = w.XZ(pa);
							ea *= 256;
							ea += oa;
							aa++;
							if (!(aa % 4)) {
								if (fa && 538976288 === ea) z += f.write("y", z);
								else if (ea) {
									for (aa = 4; 0 <= aa; aa--)
										(oa = ea % 85), (a[aa] = oa), (ea = (ea - oa) / 85);
									for (aa = 0; 5 > aa; aa++) z += f.write(e[a[aa]], z);
								} else z += f.write("z", z);
								aa = ea = 0;
							}
						}
						if (aa)
							if (ea) {
								w = 4 - aa;
								for (pa = 4 - aa; 0 < pa; pa--) ea *= 256;
								for (aa = 4; 0 <= aa; aa--)
									(oa = ea % 85), (a[aa] = oa), (ea = (ea - oa) / 85);
								for (aa = 0; 5 > aa; aa++) z += f.write(e[a[aa]], z);
								z -= w;
							} else for (pa = 0; pa < aa + 1; pa++) z += f.write(e[0], z);
						ba && (z += f.write("~>", z));
						return f.slice(0, z);
					};
					na.prototype.decode = function (e, f) {
						var a = this.wi,
							w = !0,
							z = !0,
							aa,
							ea,
							ba;
						f = f || a.bwa || b;
						if (!Array.isArray(f) && ((f = f.table || f), !Array.isArray(f))) {
							var fa = [];
							Object.keys(f).forEach(function (ka) {
								fa[ka.charCodeAt(0)] = f[ka];
							});
							f = fa;
						}
						w = !f[122];
						z = !f[121];
						e instanceof la || (e = ma(e));
						fa = 0;
						if (w || z) {
							var ha = 0;
							for (ba = e.length; ha < ba; ha++) {
								var pa = e.XZ(ha);
								w && 122 === pa && fa++;
								z && 121 === pa && fa++;
							}
						}
						var oa = 0;
						ba = Math.ceil((4 * e.length) / 5) + 4 * fa + 5;
						a = ja(ba);
						if (4 <= e.length && e.EG(0) === x) {
							for (ha = e.length - 2; 2 < ha && e.EG(ha) !== n; ha--);
							if (2 >= ha)
								throw Error("Invalid ascii85 string delimiter pair.");
							e = e.slice(2, ha);
						}
						ha = aa = ea = 0;
						for (ba = e.length; ha < ba; ha++)
							(pa = e.XZ(ha)),
								w && 122 === pa
									? (oa += a.write(y, oa))
									: z && 121 === pa
										? (oa += a.write("    ", oa))
										: void 0 !== f[pa] &&
											((ea *= 85),
											(ea += f[pa]),
											aa++,
											aa % 5 || ((oa = a.lWa(ea, oa)), (aa = ea = 0)));
						if (aa) {
							e = 5 - aa;
							for (ha = 0; ha < e; ha++) (ea *= 85), (ea += 84);
							ha = 3;
							for (ba = e - 1; ha > ba; ha--)
								oa = a.mWa((ea >>> (8 * ha)) & 255, oa);
						}
						return a.slice(0, oa);
					};
					ca.aYa = new na({
						table:
							"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#".split(
								"",
							),
					});
					ca.jXa = new na({ aL: !0 });
					ca.Uka = na;
				}).call(this, r(548).Buffer);
			},
		},
	]);
}).call(this || window);
