{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/headerFooterModalState.js"], "names": ["pageNumber", "HeaderFooterModalState", "setPageNumber", "val", "getPageNumber"], "mappings": "4FAAA,IACMA,EADAC,GACAD,EAAa,EACV,CACLE,cAAe,SAACC,GAAG,OAAKH,EAAaG,GACrCC,cAAe,kBAAMJ,KAIVC", "file": "chunks/chunk.21.js", "sourcesContent": ["const HeaderFooterModalState = (() => {\n  let pageNumber = 0;\n  return {\n    setPageNumber: (val) => pageNumber = val,\n    getPageNumber: () => pageNumber,\n  };\n})();\n\nexport default HeaderFooterModalState;\n"], "sourceRoot": ""}