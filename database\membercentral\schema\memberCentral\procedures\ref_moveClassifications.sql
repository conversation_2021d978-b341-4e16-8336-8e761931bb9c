ALTER PROC dbo.ref_moveClassifications
@classificationID int,
@recordedByMemberID int,
@dir varchar(4)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @classificationOrder int, @referralID int,
		@name varchar(200), @msg varchar(500);

	select @classificationOrder = rc.classificationOrder, @referralID = r.referralID,
		@name = CASE WHEN len(rc.[name]) > 0 THEN rc.[name] ELSE mgs.groupSetName END,
		@orgID = s.orgID, @siteID = s.siteID
	from dbo.ref_classifications as rc
	inner join dbo.ref_referrals as r on r.referralID = rc.referralID
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID
	inner join dbo.sites s on s.siteID = ai.siteID
	left join dbo.ams_memberGroupSets as mgs on mgs.groupSetID = rc.groupSetID
	where classificationID = @classificationID;

	IF @classificationOrder IS NULL
		RAISERROR('invalid Classification',16,1);

	BEGIN TRAN;
		IF @dir = 'up' BEGIN
			update dbo.ref_classifications
			set classificationOrder = classificationOrder + 1
			where referralID = @referralID
			and classificationOrder >= @classificationOrder - 1;

			update dbo.ref_classifications
			set classificationOrder = classificationOrder  - 2
			where classificationID = @classificationID;
		END
		ELSE BEGIN
			update dbo.ref_classifications
			set classificationOrder = classificationOrder - 1
			where referralID = @referralID
			AND classificationOrder <= @classificationOrder + 1;

			update dbo.ref_classifications
			set classificationOrder = classificationOrder  + 2
			where classificationID = @classificationID;
		END

		EXEC dbo.ref_reorderClassifications @referralID=@referralID;
	COMMIT TRAN;

	SET @msg = STRING_ESCAPE('Classification [' + @name + '] moved ' + @dir + '.','json');

	EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='CLASSIFICATIONS',
		@msgjson=@msg, @enteredByMemberID=@recordedByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
