(window.webpackJsonp = window.webpackJsonp || []).push([
	[132],
	{
		1390: function (_, t, e) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					e = {
						name: "el",
						weekdays:
							"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split(
								"_",
							),
						weekdaysShort: "Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),
						weekdaysMin: "Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),
						months:
							"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split(
								"_",
							),
						monthsShort:
							"Ιαν_Φεβ_Μαρ_Απρ_Μαι_Ιουν_Ιουλ_Αυγ_Σεπτ_Οκτ_Νοε_Δεκ".split("_"),
						ordinal: function (_) {
							return _;
						},
						weekStart: 1,
						relativeTime: {
							future: "σε %s",
							past: "πριν %s",
							s: "μερικά δευτερόλεπτα",
							m: "ένα λεπτό",
							mm: "%d λεπτά",
							h: "μία ώρα",
							hh: "%d ώρες",
							d: "μία μέρα",
							dd: "%d μέρες",
							M: "ένα μήνα",
							MM: "%d μήνες",
							y: "ένα χρόνο",
							yy: "%d χρόνια",
						},
						formats: {
							LT: "h:mm A",
							LTS: "h:mm:ss A",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY h:mm A",
							LLLL: "dddd, D MMMM YYYY h:mm A",
						},
					};
				return t.default.locale(e, null, !0), e;
			})(e(105));
		},
	},
]);
//# sourceMappingURL=chunk.132.js.map
