/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[12],
		{
			636: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(1);
				r.n(na);
				ya = r(129);
				r = r(547);
				ya = (function (ma) {
					function ja(ia, ca, y) {
						ca = ma.call(this, ia, ca, y) || this;
						ca.db = ia;
						return ca;
					}
					Object(la.c)(ja, ma);
					ja.prototype.request = function (ia) {
						var ca = this;
						Object(na.each)(ia, function (y) {
							ca.db.get(y, function (x, n, h) {
								return x
									? ca.trigger("partReady.partRetriever", { Qb: y, error: x })
									: ca.trigger("partReady.partRetriever", {
											Qb: y,
											data: n,
											Yl: !1,
											Bi: !1,
											error: null,
											Ie: h,
										});
							});
						});
					};
					ja.prototype.yA = function (ia) {
						ia();
					};
					return ja;
				})(ya.a);
				Object(r.a)(ya);
				Object(r.b)(ya);
				va["default"] = ya;
			},
		},
	]);
}).call(this || window);
