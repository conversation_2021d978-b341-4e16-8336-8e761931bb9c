(window.webpackJsonp = window.webpackJsonp || []).push([
	[40],
	{
		1523: function (e, o, t) {
			var n = t(30),
				r = t(1578);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let o;
					(o = document.getElementsByTagName("apryse-webviewer")),
						o.length ||
							(o = (function e(o, t = document) {
								const n = [];
								return (
									t.querySelectorAll(o).forEach((e) => n.push(e)),
									t.querySelectorAll("*").forEach((t) => {
										t.shadowRoot && n.push(...e(o, t.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const t = [];
					for (let n = 0; n < o.length; n++) {
						const r = o[n];
						if (0 === n)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									t.length > 0 &&
										t.forEach((o) => {
											o.innerHTML = e.innerHTML;
										});
								});
						else {
							const o = e.cloneNode(!0);
							r.shadowRoot.appendChild(o), t.push(o);
						}
					}
				},
				singleton: !1,
			};
			n(r, i);
			e.exports = r.locals || {};
		},
		1530: function (e, o, t) {
			"use strict";
			t.d(o, "c", function () {
				return n;
			}),
				t.d(o, "b", function () {
					return r;
				}),
				t.d(o, "a", function () {
					return i;
				});
			var n = { OUTLINE: "outline", PORTFOLIO: "portfolio" },
				r = {
					ON_TARGET_HORIZONTAL_MIDPOINT: "onTargetHorizontalMidPoint",
					ABOVE_TARGET: "aboveTarget",
					BELOW_TARGET: "belowTarget",
					INITIAL: "initial",
				},
				i = 8;
		},
		1578: function (e, o, t) {
			(e.exports = t(31)(!1)).push([
				e.i,
				".bookmark-outline-panel{display:flex;padding-left:var(--padding);padding-right:var(--padding-small)}.bookmark-outline-control-button{width:auto}.bookmark-outline-control-button span{color:inherit}.bookmark-outline-control-button,.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{color:var(--secondary-button-text)}.bookmark-outline-control-button.disabled,.bookmark-outline-control-button[disabled]{opacity:.5}.bookmark-outline-control-button.disabled span,.bookmark-outline-control-button[disabled] span{color:inherit}.bookmark-outline-control-button:not(.disabled):active,.bookmark-outline-control-button:not(.disabled):hover,.bookmark-outline-control-button:not([disabled]):active,.bookmark-outline-control-button:not([disabled]):hover{color:var(--secondary-button-hover)}.bookmark-outline-panel-header{display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center;padding:var(--padding-tiny);border-bottom:1px solid var(--divider)}.bookmark-outline-panel-header .header-title{font-size:16px}.bookmark-outline-row{flex-grow:1;overflow-y:auto}.msg-no-bookmark-outline{color:var(--placeholder-text);text-align:center}.bookmark-outline-single-container{display:flex;flex-flow:row nowrap;align-items:flex-start;border-radius:4px;margin-left:2px;margin-right:2px}.bookmark-outline-single-container.default{padding:var(--padding-tiny);border:1px solid transparent}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:hover,.bookmark-outline-single-container.default[focus-within]{cursor:pointer}.bookmark-outline-single-container.default.hover,.bookmark-outline-single-container.default:focus-within,.bookmark-outline-single-container.default:hover{cursor:pointer}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button,.bookmark-outline-single-container.default[focus-within] .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default.hover .bookmark-outline-more-button,.bookmark-outline-single-container.default:focus-within .bookmark-outline-more-button,.bookmark-outline-single-container.default:hover .bookmark-outline-more-button{display:flex;background-color:transparent}.bookmark-outline-single-container.default[focus-within]{border-color:transparent}.bookmark-outline-single-container.default:focus-within{border-color:transparent}.bookmark-outline-single-container.default .bookmark-outline-label-row{overflow:hidden}.bookmark-outline-single-container.default.focus-visible,.bookmark-outline-single-container.default:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.bookmark-outline-single-container.editing.focus-visible,.bookmark-outline-single-container.editing:focus-visible{outline:var(--focus-visible-outline)!important}.bookmark-outline-single-container.preview{display:inline-flex;margin-top:0;padding:var(--padding-small);background-color:var(--component-background);box-shadow:0 0 3px var(--note-box-shadow)}.bookmark-outline-single-container .bookmark-outline-checkbox{flex-grow:0;flex-shrink:0;margin-top:2px;margin-bottom:2px;margin-right:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-label-row{flex-grow:1;flex-shrink:1;display:flex;flex-flow:row wrap;align-items:flex-start;position:relative;overflow:hidden}.bookmark-outline-single-container .bookmark-outline-label{font-weight:600;flex-grow:1;flex-shrink:1;margin-bottom:var(--padding-small)}.bookmark-outline-single-container .bookmark-outline-input,.bookmark-outline-single-container .bookmark-outline-text{flex-grow:1;flex-shrink:1;flex-basis:calc(100% - 22px);margin-top:2px;margin-bottom:2px}.bookmark-outline-single-container .bookmark-text-input{margin-left:var(--padding-large)}.bookmark-outline-single-container .bookmark-outline-input{color:var(--text-color);width:calc(100% - var(--padding-large));padding:var(--padding-small);border:1px solid var(--border)}.bookmark-outline-single-container .bookmark-outline-input:focus{border-color:var(--outline-color)}.bookmark-outline-single-container .bookmark-outline-input::-moz-placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-input::placeholder{color:var(--placeholder-text)}.bookmark-outline-single-container .bookmark-outline-more-button{display:none;flex-grow:0;flex-shrink:0;width:16px;height:16px;margin:2px 2px 2px var(--padding-tiny)}.bookmark-outline-single-container .bookmark-outline-more-button .Icon{width:14px;height:14px}.bookmark-outline-single-container .bookmark-outline-more-button.icon-only:hover:not(:disabled):not(.disabled){box-shadow:none;outline:solid 1px var(--hover-border)}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within].icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within.icon-only{border:none;box-shadow:none}.bookmark-outline-single-container .bookmark-outline-more-button[focus-within] .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-more-button:focus-within .Icon{color:var(--focus-border)}.bookmark-outline-single-container .bookmark-outline-editing-controls{padding:2px;flex-basis:100%;display:flex;flex-flow:row wrap;justify-content:flex-end;align-items:center;margin-top:var(--padding-medium)}.bookmark-outline-single-container .bookmark-outline-cancel-button,.bookmark-outline-single-container .bookmark-outline-save-button{width:auto;padding:6px var(--padding)}.bookmark-outline-single-container .bookmark-outline-cancel-button{color:var(--secondary-button-text)}.bookmark-outline-single-container .bookmark-outline-cancel-button:hover{color:var(--secondary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button{color:var(--primary-button-text);background-color:var(--primary-button);margin-left:var(--padding-tiny);border-radius:4px}.bookmark-outline-single-container .bookmark-outline-save-button:hover{background-color:var(--primary-button-hover)}.bookmark-outline-single-container .bookmark-outline-save-button.disabled,.bookmark-outline-single-container .bookmark-outline-save-button:disabled{background-color:var(--primary-button)!important;opacity:.5}.bookmark-outline-single-container .bookmark-outline-save-button.disabled span,.bookmark-outline-single-container .bookmark-outline-save-button:disabled span{color:var(--primary-button-text)}.bookmark-outline-footer{border-top:1.5px solid var(--gray-4);padding-top:var(--padding-medium);padding-bottom:var(--padding-medium);display:flex;justify-content:center;align-items:center}.bookmark-outline-footer .add-new-button .Icon{width:14px;height:14px;margin-right:var(--padding-tiny);color:inherit;fill:currentColor}.bookmark-outline-footer .add-new-button.disabled .Icon.disabled,.bookmark-outline-footer .add-new-button.disabled .Icon.disabled path,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled,.bookmark-outline-footer .add-new-button[disabled] .Icon.disabled path{color:inherit;fill:currentColor}.bookmark-outline-footer .multi-selection-button{width:auto;padding:7px}.bookmark-outline-footer .multi-selection-button .Icon{width:18px;height:18px}.bookmark-outline-footer .multi-selection-button:not(:first-child){margin-left:var(--padding-tiny)}.bookmark-outline-footer .multi-selection-button:hover{background-color:transparent}.bookmark-outline-footer .multi-selection-button.disabled:hover,.bookmark-outline-footer .multi-selection-button:disabled:hover{box-shadow:none}",
				"",
			]);
		},
		1687: function (e, o, t) {
			var n = t(30),
				r = t(1688);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let o;
					(o = document.getElementsByTagName("apryse-webviewer")),
						o.length ||
							(o = (function e(o, t = document) {
								const n = [];
								return (
									t.querySelectorAll(o).forEach((e) => n.push(e)),
									t.querySelectorAll("*").forEach((t) => {
										t.shadowRoot && n.push(...e(o, t.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const t = [];
					for (let n = 0; n < o.length; n++) {
						const r = o[n];
						if (0 === n)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									t.length > 0 &&
										t.forEach((o) => {
											o.innerHTML = e.innerHTML;
										});
								});
						else {
							const o = e.cloneNode(!0);
							r.shadowRoot.appendChild(o), t.push(o);
						}
					}
				},
				singleton: !1,
			};
			n(r, i);
			e.exports = r.locals || {};
		},
		1688: function (e, o, t) {
			(o = e.exports = t(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row{align-items:center}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing{background-color:var(--faded-component-background);padding:var(--padding-medium) 20px}.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing.focus-visible,.PortfolioPanel .bookmark-outline-single-container .bookmark-outline-label-row.editing:focus-visible{outline:var(--focus-visible-outline)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input-label{font-weight:600;padding-bottom:var(--padding-small)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input{-webkit-font-smoothing:antialiased}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input{border-color:var(--border)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning{border-color:var(--error-border-color)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning.ui__input--focused{box-shadow:none}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-warning .ui__input__icon svg{fill:var(--error-border-color)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input.ui__input--message-default.ui__input--focused{border-color:var(--outline-color);box-shadow:none}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input{padding:var(--padding-small);color:var(--text-color);height:auto;overflow:auto;line-height:normal}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input::-moz-placeholder{color:var(--placeholder-text)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input .ui__input__input::placeholder{color:var(--placeholder-text)}.PortfolioPanel .bookmark-outline-single-container .portfolio-input .ui__input__messageText{color:var(--error-text-color);margin-top:var(--padding-small);font-size:inherit}",
				"",
			]),
				(o.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1689: function (e, o, t) {
			var n = t(30),
				r = t(1690);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let o;
					(o = document.getElementsByTagName("apryse-webviewer")),
						o.length ||
							(o = (function e(o, t = document) {
								const n = [];
								return (
									t.querySelectorAll(o).forEach((e) => n.push(e)),
									t.querySelectorAll("*").forEach((t) => {
										t.shadowRoot && n.push(...e(o, t.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const t = [];
					for (let n = 0; n < o.length; n++) {
						const r = o[n];
						if (0 === n)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									t.length > 0 &&
										t.forEach((o) => {
											o.innerHTML = e.innerHTML;
										});
								});
						else {
							const o = e.cloneNode(!0);
							r.shadowRoot.appendChild(o), t.push(o);
						}
					}
				},
				singleton: !1,
			};
			n(r, i);
			e.exports = r.locals || {};
		},
		1690: function (e, o, t) {
			(e.exports = t(31)(!1)).push([e.i, "", ""]);
		},
		1691: function (e, o, t) {
			var n = t(30),
				r = t(1692);
			"string" == typeof (r = r.__esModule ? r.default : r) &&
				(r = [[e.i, r, ""]]);
			var i = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let o;
					(o = document.getElementsByTagName("apryse-webviewer")),
						o.length ||
							(o = (function e(o, t = document) {
								const n = [];
								return (
									t.querySelectorAll(o).forEach((e) => n.push(e)),
									t.querySelectorAll("*").forEach((t) => {
										t.shadowRoot && n.push(...e(o, t.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const t = [];
					for (let n = 0; n < o.length; n++) {
						const r = o[n];
						if (0 === n)
							r.shadowRoot.appendChild(e),
								(e.onload = function () {
									t.length > 0 &&
										t.forEach((o) => {
											o.innerHTML = e.innerHTML;
										});
								});
						else {
							const o = e.cloneNode(!0);
							r.shadowRoot.appendChild(o), t.push(o);
						}
					}
				},
				singleton: !1,
			};
			n(r, i);
			e.exports = r.locals || {};
		},
		1692: function (e, o, t) {
			(o = e.exports = t(31)(!1)).push([
				e.i,
				":host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PortfolioPanel .portfolio-panel-control{display:flex}.PortfolioPanel .bookmark-outline-row{padding-top:6px}.PortfolioPanel .bookmark-outline-panel-header .header-title{font-size:16px;font-weight:400;margin:0}.PortfolioPanel .portfolio-panel-list{padding-top:8px}",
				"",
			]),
				(o.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1704: function (e, o, t) {
			"use strict";
			t.r(o);
			t(102),
				t(8),
				t(15),
				t(88),
				t(29),
				t(124),
				t(128),
				t(10),
				t(11),
				t(41),
				t(19),
				t(12),
				t(13),
				t(14),
				t(9),
				t(16),
				t(20),
				t(18),
				t(54),
				t(22),
				t(61),
				t(62),
				t(63),
				t(64),
				t(37),
				t(39),
				t(23),
				t(24),
				t(40),
				t(60);
			var n = t(0),
				r = t.n(n),
				i = t(6),
				a = t(347),
				l = t(1509),
				c = t(1590),
				u = t(444),
				s = t(4),
				d = t(2),
				f = t(42),
				p = r.a.createContext(),
				m = t(3),
				b = t.n(m),
				h = t(1811),
				g = t(1812),
				k = t(592),
				v = t(1530),
				y = (t(36), t(1529)),
				w = t(5),
				x = t(2007),
				E = t(251),
				O = (t(1523), t(1687), t(1585)),
				P = t(17),
				_ = t.n(P);
			function T(e, o) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, o) {
						var t =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != t) {
							var n,
								r,
								i,
								a,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((i = (t = t.call(e)).next), 0 === o)) {
									if (Object(t) !== t) return;
									c = !1;
								} else
									for (
										;
										!(c = (n = i.call(t)).done) &&
										(l.push(n.value), l.length !== o);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != t.return &&
										((a = t.return()), Object(a) !== a)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, o) ||
					(function (e, o) {
						if (!e) return;
						if ("string" == typeof e) return I(e, o);
						var t = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === t && e.constructor && (t = e.constructor.name);
						if ("Map" === t || "Set" === t) return Array.from(e);
						if (
							"Arguments" === t ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)
						)
							return I(e, o);
					})(e, o) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function I(e, o) {
				(null == o || o > e.length) && (o = e.length);
				for (var t = 0, n = new Array(o); t < o; t++) n[t] = e[t];
				return n;
			}
			var A = {
					portfolioItem: b.a.object.isRequired,
					isPortfolioRenaming: b.a.bool,
					setPortfolioRenaming: b.a.func,
					movePortfolio: b.a.func,
				},
				L = function (e) {
					var o = e.portfolioItem,
						t = e.isPortfolioRenaming,
						l = e.setPortfolioRenaming,
						c = e.movePortfolio,
						u = Object(n.useContext)(p),
						d = u.refreshPortfolio,
						m = u.renamePortfolioItem,
						b = u.removePortfolioItem,
						h = u.openPortfolioItem,
						g = u.downloadPortfolioItem,
						k = u.isNameDuplicated,
						v = u.setActivePortfolioItem,
						P = o.name,
						I = o.nameWithoutExtension,
						A = o.extension,
						L = o.id,
						N = T(Object(a.a)(), 1)[0],
						j = Object(n.useRef)(),
						D = T(Object(n.useState)(!1), 2),
						R = D[0],
						S = D[1],
						C = T(Object(n.useState)(I), 2),
						M = C[0],
						F = C[1],
						G = Object(n.useCallback)(
							function () {
								t || (h(o), v(o.id));
							},
							[t, o, h, v],
						),
						W = function () {
							return !M || I === M || k("".concat(M, ".").concat(A), L);
						},
						B = function () {
							l(!1), m(L, "".concat(M, ".").concat(A));
						},
						H = function () {
							t && (l(!1), F(I)), d();
						};
					Object(n.useEffect)(
						function () {
							t && (j.current.focus(), j.current.select()), S(!t);
						},
						[t],
					);
					var V = "".concat(w.a.BOOKMARK_OUTLINE_FLYOUT, "-").concat(L),
						U = Object(i.e)(function (e) {
							return s.a.getFlyout(e, V);
						}),
						q = {
							moreOptionsDataElement: "portfolio-item-more-button-".concat(L),
							flyoutToggleElement: V,
						},
						z = {
							shouldHideDeleteButton: !1,
							currentFlyout: U,
							flyoutSelector: V,
							type: "portfolio",
							handleOnClick: function (e) {
								switch (e) {
									case y.b.MOVE_UP:
									case y.b.MOVE_DOWN:
										c(L, e);
										break;
									case y.b.OPENFILE:
										Object(E.g)(A) && h(o);
										break;
									case y.b.RENAME:
										l(!0);
										break;
									case y.b.DOWNLOAD:
										g(o);
										break;
									case y.b.DELETE:
										b(L);
								}
							},
						};
					return r.a.createElement(
						"div",
						{ className: "bookmark-outline-single-container" },
						R &&
							r.a.createElement(O.a, {
								iconGlyph:
									"icon-header-page-manipulation-page-layout-single-page-line",
								labelHeader: P,
								enableMoreOptionsContextMenuFlyout: !0,
								onDoubleClick: G,
								contextMenuMoreButtonOptions: q,
								contentMenuFlyoutOptions: z,
							}),
						t &&
							r.a.createElement(
								"div",
								{
									className: _()({
										"bookmark-outline-label-row": !0,
										editing: t,
									}),
								},
								r.a.createElement(
									"label",
									{ className: "portfolio-input-label", htmlFor: L },
									N("portfolio.portfolioDocumentTitle"),
								),
								r.a.createElement(x.a, {
									id: L,
									type: "text",
									name: "outline",
									ref: j,
									wrapperClassName: "portfolio-input",
									value: M,
									onKeyDown: function (e) {
										"Enter" === e.key &&
											(e.stopPropagation(), t && !W() && B()),
											"Escape" === e.key && H();
									},
									onChange: function (e) {
										return F(e.target.value);
									},
									fillWidth: !0,
									messageText: k("".concat(M, ".").concat(A), L)
										? N("portfolio.fileNameAlreadyExists")
										: "",
									message: k("".concat(M, ".").concat(A), L)
										? "warning"
										: "default",
									"aria-label": ""
										.concat(N("action.rename"), " ")
										.concat(M, ".")
										.concat(A),
								}),
								r.a.createElement(
									"div",
									{ className: "bookmark-outline-editing-controls" },
									r.a.createElement(f.a, {
										className: "bookmark-outline-cancel-button",
										label: N("action.cancel"),
										onClick: H,
									}),
									t &&
										r.a.createElement(f.a, {
											className: "bookmark-outline-save-button",
											label: N("action.save"),
											isSubmitType: !0,
											disabled: W(),
											onClick: B,
										}),
								),
							),
					);
				};
			L.propTypes = A;
			var N = L;
			t(1689);
			function j(e, o) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, o) {
						var t =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != t) {
							var n,
								r,
								i,
								a,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((i = (t = t.call(e)).next), 0 === o)) {
									if (Object(t) !== t) return;
									c = !1;
								} else
									for (
										;
										!(c = (n = i.call(t)).done) &&
										(l.push(n.value), l.length !== o);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != t.return &&
										((a = t.return()), Object(a) !== a)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, o) ||
					(function (e, o) {
						if (!e) return;
						if ("string" == typeof e) return D(e, o);
						var t = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === t && e.constructor && (t = e.constructor.name);
						if ("Map" === t || "Set" === t) return Array.from(e);
						if (
							"Arguments" === t ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)
						)
							return D(e, o);
					})(e, o) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function D(e, o) {
				(null == o || o > e.length) && (o = e.length);
				for (var t = 0, n = new Array(o); t < o; t++) n[t] = e[t];
				return n;
			}
			var R = {
					portfolioItem: b.a.object.isRequired,
					connectDragSource: b.a.func,
					connectDragPreview: b.a.func,
					connectDropTarget: b.a.func,
					isDragging: b.a.bool,
					isDraggedUpwards: b.a.bool,
					isDraggedDownwards: b.a.bool,
					movePortfolio: b.a.func,
				},
				S = Object(n.forwardRef)(function (e, o) {
					var t = e.portfolioItem,
						i = e.connectDragSource,
						a = e.connectDragPreview,
						l = e.connectDropTarget,
						c = e.isDragging,
						u = e.isDraggedUpwards,
						s = e.isDraggedDownwards,
						d = e.movePortfolio,
						f = Object(n.useRef)(null);
					i(f), a(Object(k.a)(), { captureDraggingState: !0 }), l(f);
					var p = c ? 0.5 : 1;
					Object(n.useImperativeHandle)(o, function () {
						return {
							getNode: function () {
								return f.current;
							},
						};
					});
					var m = j(Object(n.useState)(!1), 2),
						b = m[0],
						h = m[1];
					return r.a.createElement(
						"div",
						{
							ref: f,
							className: "outline-drag-container",
							style: { opacity: p },
						},
						r.a.createElement("div", {
							className: "outline-drag-line",
							style: { opacity: u ? 1 : 0 },
						}),
						r.a.createElement(N, {
							movePortfolio: d,
							portfolioItem: t,
							isPortfolioRenaming: b,
							setPortfolioRenaming: h,
						}),
						r.a.createElement("div", {
							className: "outline-drag-line",
							style: { opacity: s ? 1 : 0 },
						}),
					);
				});
			(S.propTypes = R), (S.displayName = "PortfolioItem");
			var C = Object(h.a)(
				v.c.PORTFOLIO,
				{
					hover: function (e, o, t) {
						if (t) {
							var n = o.getItem();
							if (n) {
								var r = n.dragPortfolioItem,
									i = n.dragSourceNode,
									a = e.portfolioItem,
									l = t.getNode();
								if (i && l) {
									if (i.contains(l))
										return (
											(n.dropTargetNode = void 0),
											void (n.dropLocation = v.b.INITIAL)
										);
									if (((n.dropTargetNode = l), r.id !== a.id)) {
										var c = l.getBoundingClientRect(),
											u = c.height / 2 + c.top,
											s = o.getClientOffset().y;
										switch (!0) {
											case s > u:
												(n.dropLocation = v.b.BELOW_TARGET),
													l.classList.remove("isNesting");
												break;
											case s < u:
												(n.dropLocation = v.b.ABOVE_TARGET),
													l.classList.remove("isNesting");
												break;
											default:
												(n.dropLocation = v.b.INITIAL),
													l.classList.remove("isNesting");
										}
									}
								}
							}
						}
					},
					drop: function (e, o, t) {
						if (t) {
							var n = o.getItem(),
								r = n.dragPortfolioItem,
								i = n.dropTargetNode,
								a = e.portfolioItem,
								l = e.movePortfolioInward,
								c = e.movePortfolioBeforeTarget,
								u = e.movePortfolioAfterTarget;
							if (i) {
								switch (n.dropLocation) {
									case v.b.ON_TARGET_HORIZONTAL_MIDPOINT:
										l(r, a);
										break;
									case v.b.ABOVE_TARGET:
										c(r.id, a.id);
										break;
									case v.b.BELOW_TARGET:
										u(r.id, a.id);
								}
								i.classList.remove("isNesting"), (n.dropLocation = v.b.INITIAL);
							}
						}
					},
				},
				function (e, o) {
					var t, n;
					return {
						connectDropTarget: e.dropTarget(),
						isDraggedUpwards:
							o.isOver({ shallow: !0 }) &&
							(null === (t = o.getItem()) || void 0 === t
								? void 0
								: t.dropLocation) === v.b.ABOVE_TARGET,
						isDraggedDownwards:
							o.isOver({ shallow: !0 }) &&
							(null === (n = o.getItem()) || void 0 === n
								? void 0
								: n.dropLocation) === v.b.BELOW_TARGET,
					};
				},
			)(
				Object(g.a)(
					v.c.PORTFOLIO,
					{
						beginDrag: function (e, o, t) {
							return {
								sourceId: o.sourceId,
								dragPortfolioItem: e.portfolioItem,
								dragSourceNode: t.getNode(),
								dropLocation: v.b.INITIAL,
							};
						},
						canDrag: function () {
							return !0;
						},
					},
					function (e, o) {
						return {
							connectDragSource: e.dragSource(),
							connectDragPreview: e.dragPreview(),
							isDragging: o.isDragging(),
						};
					},
				)(S),
			);
			C.propTypes = R;
			var M = C,
				F = t(1801),
				G = {
					position: "fixed",
					pointerEvents: "none",
					zIndex: 99999,
					left: 0,
					top: 0,
					width: "100%",
					height: "100%",
				},
				W = function (e, o) {
					if (!e || !o) return { display: "none" };
					var t = o.x,
						n = o.y,
						r = "translate(calc("
							.concat(t, "px - 50%), calc(")
							.concat(n, "px - 100%))");
					return { transform: r, WebkitTransform: r };
				},
				B = function () {
					var e = Object(F.a)(function (e) {
							return {
								itemType: e.getItemType(),
								item: e.getItem(),
								isDragging: e.isDragging(),
								initialOffset: e.getInitialSourceClientOffset(),
								currentOffset: e.getClientOffset(),
							};
						}),
						o = e.itemType,
						t = e.item,
						n = e.isDragging,
						i = e.initialOffset,
						a = e.currentOffset;
					return n
						? r.a.createElement(
								"div",
								{ style: G },
								r.a.createElement(
									"div",
									{
										className: "bookmark-outline-single-container preview",
										style: W(i, a),
									},
									(function () {
										if (!t) return null;
										var e = t.dragPortfolioItem;
										return o === v.c.PORTFOLIO
											? r.a.createElement(r.a.Fragment, null, e.name)
											: null;
									})(),
								),
							)
						: null;
				},
				H = t(75),
				V = t(38),
				U = t(134),
				q = t(1);
			t(1691);
			function z(e) {
				return (z =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function K(e, o) {
				var t =
					("undefined" != typeof Symbol && e[Symbol.iterator]) ||
					e["@@iterator"];
				if (!t) {
					if (
						Array.isArray(e) ||
						(t = X(e)) ||
						(o && e && "number" == typeof e.length)
					) {
						t && (e = t);
						var n = 0,
							r = function () {};
						return {
							s: r,
							n: function () {
								return n >= e.length
									? { done: !0 }
									: { done: !1, value: e[n++] };
							},
							e: function (e) {
								throw e;
							},
							f: r,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var i,
					a = !0,
					l = !1;
				return {
					s: function () {
						t = t.call(e);
					},
					n: function () {
						var e = t.next();
						return (a = e.done), e;
					},
					e: function (e) {
						(l = !0), (i = e);
					},
					f: function () {
						try {
							a || null == t.return || t.return();
						} finally {
							if (l) throw i;
						}
					},
				};
			}
			function Y(e) {
				return (
					(function (e) {
						if (Array.isArray(e)) return ee(e);
					})(e) ||
					(function (e) {
						if (
							("undefined" != typeof Symbol && null != e[Symbol.iterator]) ||
							null != e["@@iterator"]
						)
							return Array.from(e);
					})(e) ||
					X(e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function $() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ $ =
					function () {
						return e;
					};
				var e = {},
					o = Object.prototype,
					t = o.hasOwnProperty,
					n =
						Object.defineProperty ||
						function (e, o, t) {
							e[o] = t.value;
						},
					r = "function" == typeof Symbol ? Symbol : {},
					i = r.iterator || "@@iterator",
					a = r.asyncIterator || "@@asyncIterator",
					l = r.toStringTag || "@@toStringTag";
				function c(e, o, t) {
					return (
						Object.defineProperty(e, o, {
							value: t,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						e[o]
					);
				}
				try {
					c({}, "");
				} catch (e) {
					c = function (e, o, t) {
						return (e[o] = t);
					};
				}
				function u(e, o, t, r) {
					var i = o && o.prototype instanceof f ? o : f,
						a = Object.create(i.prototype),
						l = new P(r || []);
					return n(a, "_invoke", { value: w(e, t, l) }), a;
				}
				function s(e, o, t) {
					try {
						return { type: "normal", arg: e.call(o, t) };
					} catch (e) {
						return { type: "throw", arg: e };
					}
				}
				e.wrap = u;
				var d = {};
				function f() {}
				function p() {}
				function m() {}
				var b = {};
				c(b, i, function () {
					return this;
				});
				var h = Object.getPrototypeOf,
					g = h && h(h(_([])));
				g && g !== o && t.call(g, i) && (b = g);
				var k = (m.prototype = f.prototype = Object.create(b));
				function v(e) {
					["next", "throw", "return"].forEach(function (o) {
						c(e, o, function (e) {
							return this._invoke(o, e);
						});
					});
				}
				function y(e, o) {
					var r;
					n(this, "_invoke", {
						value: function (n, i) {
							function a() {
								return new o(function (r, a) {
									!(function n(r, i, a, l) {
										var c = s(e[r], e, i);
										if ("throw" !== c.type) {
											var u = c.arg,
												d = u.value;
											return d && "object" == z(d) && t.call(d, "__await")
												? o.resolve(d.__await).then(
														function (e) {
															n("next", e, a, l);
														},
														function (e) {
															n("throw", e, a, l);
														},
													)
												: o.resolve(d).then(
														function (e) {
															(u.value = e), a(u);
														},
														function (e) {
															return n("throw", e, a, l);
														},
													);
										}
										l(c.arg);
									})(n, i, r, a);
								});
							}
							return (r = r ? r.then(a, a) : a());
						},
					});
				}
				function w(e, o, t) {
					var n = "suspendedStart";
					return function (r, i) {
						if ("executing" === n)
							throw new Error("Generator is already running");
						if ("completed" === n) {
							if ("throw" === r) throw i;
							return T();
						}
						for (t.method = r, t.arg = i; ; ) {
							var a = t.delegate;
							if (a) {
								var l = x(a, t);
								if (l) {
									if (l === d) continue;
									return l;
								}
							}
							if ("next" === t.method) t.sent = t._sent = t.arg;
							else if ("throw" === t.method) {
								if ("suspendedStart" === n) throw ((n = "completed"), t.arg);
								t.dispatchException(t.arg);
							} else "return" === t.method && t.abrupt("return", t.arg);
							n = "executing";
							var c = s(e, o, t);
							if ("normal" === c.type) {
								if (
									((n = t.done ? "completed" : "suspendedYield"), c.arg === d)
								)
									continue;
								return { value: c.arg, done: t.done };
							}
							"throw" === c.type &&
								((n = "completed"), (t.method = "throw"), (t.arg = c.arg));
						}
					};
				}
				function x(e, o) {
					var t = o.method,
						n = e.iterator[t];
					if (void 0 === n)
						return (
							(o.delegate = null),
							("throw" === t &&
								e.iterator.return &&
								((o.method = "return"),
								(o.arg = void 0),
								x(e, o),
								"throw" === o.method)) ||
								("return" !== t &&
									((o.method = "throw"),
									(o.arg = new TypeError(
										"The iterator does not provide a '" + t + "' method",
									)))),
							d
						);
					var r = s(n, e.iterator, o.arg);
					if ("throw" === r.type)
						return (
							(o.method = "throw"), (o.arg = r.arg), (o.delegate = null), d
						);
					var i = r.arg;
					return i
						? i.done
							? ((o[e.resultName] = i.value),
								(o.next = e.nextLoc),
								"return" !== o.method &&
									((o.method = "next"), (o.arg = void 0)),
								(o.delegate = null),
								d)
							: i
						: ((o.method = "throw"),
							(o.arg = new TypeError("iterator result is not an object")),
							(o.delegate = null),
							d);
				}
				function E(e) {
					var o = { tryLoc: e[0] };
					1 in e && (o.catchLoc = e[1]),
						2 in e && ((o.finallyLoc = e[2]), (o.afterLoc = e[3])),
						this.tryEntries.push(o);
				}
				function O(e) {
					var o = e.completion || {};
					(o.type = "normal"), delete o.arg, (e.completion = o);
				}
				function P(e) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						e.forEach(E, this),
						this.reset(!0);
				}
				function _(e) {
					if (e) {
						var o = e[i];
						if (o) return o.call(e);
						if ("function" == typeof e.next) return e;
						if (!isNaN(e.length)) {
							var n = -1,
								r = function o() {
									for (; ++n < e.length; )
										if (t.call(e, n)) return (o.value = e[n]), (o.done = !1), o;
									return (o.value = void 0), (o.done = !0), o;
								};
							return (r.next = r);
						}
					}
					return { next: T };
				}
				function T() {
					return { value: void 0, done: !0 };
				}
				return (
					(p.prototype = m),
					n(k, "constructor", { value: m, configurable: !0 }),
					n(m, "constructor", { value: p, configurable: !0 }),
					(p.displayName = c(m, l, "GeneratorFunction")),
					(e.isGeneratorFunction = function (e) {
						var o = "function" == typeof e && e.constructor;
						return (
							!!o &&
							(o === p || "GeneratorFunction" === (o.displayName || o.name))
						);
					}),
					(e.mark = function (e) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(e, m)
								: ((e.__proto__ = m), c(e, l, "GeneratorFunction")),
							(e.prototype = Object.create(k)),
							e
						);
					}),
					(e.awrap = function (e) {
						return { __await: e };
					}),
					v(y.prototype),
					c(y.prototype, a, function () {
						return this;
					}),
					(e.AsyncIterator = y),
					(e.async = function (o, t, n, r, i) {
						void 0 === i && (i = Promise);
						var a = new y(u(o, t, n, r), i);
						return e.isGeneratorFunction(t)
							? a
							: a.next().then(function (e) {
									return e.done ? e.value : a.next();
								});
					}),
					v(k),
					c(k, l, "Generator"),
					c(k, i, function () {
						return this;
					}),
					c(k, "toString", function () {
						return "[object Generator]";
					}),
					(e.keys = function (e) {
						var o = Object(e),
							t = [];
						for (var n in o) t.push(n);
						return (
							t.reverse(),
							function e() {
								for (; t.length; ) {
									var n = t.pop();
									if (n in o) return (e.value = n), (e.done = !1), e;
								}
								return (e.done = !0), e;
							}
						);
					}),
					(e.values = _),
					(P.prototype = {
						constructor: P,
						reset: function (e) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(O),
								!e)
							)
								for (var o in this)
									"t" === o.charAt(0) &&
										t.call(this, o) &&
										!isNaN(+o.slice(1)) &&
										(this[o] = void 0);
						},
						stop: function () {
							this.done = !0;
							var e = this.tryEntries[0].completion;
							if ("throw" === e.type) throw e.arg;
							return this.rval;
						},
						dispatchException: function (e) {
							if (this.done) throw e;
							var o = this;
							function n(t, n) {
								return (
									(a.type = "throw"),
									(a.arg = e),
									(o.next = t),
									n && ((o.method = "next"), (o.arg = void 0)),
									!!n
								);
							}
							for (var r = this.tryEntries.length - 1; r >= 0; --r) {
								var i = this.tryEntries[r],
									a = i.completion;
								if ("root" === i.tryLoc) return n("end");
								if (i.tryLoc <= this.prev) {
									var l = t.call(i, "catchLoc"),
										c = t.call(i, "finallyLoc");
									if (l && c) {
										if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
										if (this.prev < i.finallyLoc) return n(i.finallyLoc);
									} else if (l) {
										if (this.prev < i.catchLoc) return n(i.catchLoc, !0);
									} else {
										if (!c)
											throw new Error("try statement without catch or finally");
										if (this.prev < i.finallyLoc) return n(i.finallyLoc);
									}
								}
							}
						},
						abrupt: function (e, o) {
							for (var n = this.tryEntries.length - 1; n >= 0; --n) {
								var r = this.tryEntries[n];
								if (
									r.tryLoc <= this.prev &&
									t.call(r, "finallyLoc") &&
									this.prev < r.finallyLoc
								) {
									var i = r;
									break;
								}
							}
							i &&
								("break" === e || "continue" === e) &&
								i.tryLoc <= o &&
								o <= i.finallyLoc &&
								(i = null);
							var a = i ? i.completion : {};
							return (
								(a.type = e),
								(a.arg = o),
								i
									? ((this.method = "next"), (this.next = i.finallyLoc), d)
									: this.complete(a)
							);
						},
						complete: function (e, o) {
							if ("throw" === e.type) throw e.arg;
							return (
								"break" === e.type || "continue" === e.type
									? (this.next = e.arg)
									: "return" === e.type
										? ((this.rval = this.arg = e.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === e.type && o && (this.next = o),
								d
							);
						},
						finish: function (e) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var t = this.tryEntries[o];
								if (t.finallyLoc === e)
									return this.complete(t.completion, t.afterLoc), O(t), d;
							}
						},
						catch: function (e) {
							for (var o = this.tryEntries.length - 1; o >= 0; --o) {
								var t = this.tryEntries[o];
								if (t.tryLoc === e) {
									var n = t.completion;
									if ("throw" === n.type) {
										var r = n.arg;
										O(t);
									}
									return r;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (e, o, t) {
							return (
								(this.delegate = { iterator: _(e), resultName: o, nextLoc: t }),
								"next" === this.method && (this.arg = void 0),
								d
							);
						},
					}),
					e
				);
			}
			function J(e, o, t, n, r, i, a) {
				try {
					var l = e[i](a),
						c = l.value;
				} catch (e) {
					return void t(e);
				}
				l.done ? o(c) : Promise.resolve(c).then(n, r);
			}
			function Z(e) {
				return function () {
					var o = this,
						t = arguments;
					return new Promise(function (n, r) {
						var i = e.apply(o, t);
						function a(e) {
							J(i, n, r, a, l, "next", e);
						}
						function l(e) {
							J(i, n, r, a, l, "throw", e);
						}
						a(void 0);
					});
				};
			}
			function Q(e, o) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, o) {
						var t =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != t) {
							var n,
								r,
								i,
								a,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((i = (t = t.call(e)).next), 0 === o)) {
									if (Object(t) !== t) return;
									c = !1;
								} else
									for (
										;
										!(c = (n = i.call(t)).done) &&
										(l.push(n.value), l.length !== o);
										c = !0
									);
							} catch (e) {
								(u = !0), (r = e);
							} finally {
								try {
									if (
										!c &&
										null != t.return &&
										((a = t.return()), Object(a) !== a)
									)
										return;
								} finally {
									if (u) throw r;
								}
							}
							return l;
						}
					})(e, o) ||
					X(e, o) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function X(e, o) {
				if (e) {
					if ("string" == typeof e) return ee(e, o);
					var t = Object.prototype.toString.call(e).slice(8, -1);
					return (
						"Object" === t && e.constructor && (t = e.constructor.name),
						"Map" === t || "Set" === t
							? Array.from(e)
							: "Arguments" === t ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)
								? ee(e, o)
								: void 0
					);
				}
			}
			function ee(e, o) {
				(null == o || o > e.length) && (o = e.length);
				for (var t = 0, n = new Array(o); t < o; t++) n[t] = e[t];
				return n;
			}
			var oe = function () {
				var e = Q(
						Object(i.e)(function (e) {
							return [
								s.a.isElementDisabled(e, w.a.PORTFOLIO_PANEL),
								s.a.getTabManager(e),
								s.a.getPortfolio(e),
							];
						}, i.c),
						3,
					),
					o = e[0],
					t = e[1],
					m = e[2],
					b = Object(i.d)(),
					h = Q(Object(a.a)(), 1)[0],
					g = Q(Object(n.useState)(null), 2),
					k = g[0],
					x = g[1],
					O = Q(Object(n.useState)(!1), 2),
					P = O[0],
					_ = O[1],
					T = Object(n.useRef)(null),
					I = function () {
						var e;
						null == T || null === (e = T.current) || void 0 === e || e.click();
					},
					A = (function () {
						var e = Z(
							$().mark(function e(o) {
								var t, n, r, i, a, l, c, u;
								return $().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												if (1 !== (t = o.target.files).length) {
													e.next = 21;
													break;
												}
												if (
													((n = t[0]),
													!m.some(function (e) {
														return e.name === n.name;
													}))
												) {
													e.next = 12;
													break;
												}
												(r = h("portfolio.fileAlreadyExistsMessage", {
													fileName: n.name,
												})),
													(i = h("portfolio.fileAlreadyExists")),
													(a = h("portfolio.reselect")),
													(l = {
														message: r,
														title: i,
														confirmBtnText: a,
														onConfirm: function () {
															return I();
														},
													}),
													b(d.a.showWarningMessage(l)),
													(e.next = 21);
												break;
											case 12:
												if (!(c = q.a.getDocument())) {
													e.next = 21;
													break;
												}
												return (e.next = 16), c.getPDFDoc();
											case 16:
												if (!(u = e.sent)) {
													e.next = 21;
													break;
												}
												return (e.next = 20), Object(E.b)(u, n);
											case 20:
												j();
											case 21:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function (o) {
							return e.apply(this, arguments);
						};
					})(),
					L = (function () {
						var e = Z(
							$().mark(function e(o, t) {
								return $().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												return (e.next = 2), Object(E.h)(o, t);
											case 2:
												j();
											case 3:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function (o, t) {
							return e.apply(this, arguments);
						};
					})(),
					j = (function () {
						var e = Z(
							$().mark(function e() {
								return $().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												return (
													(e.t0 = b), (e.t1 = d.a), (e.next = 4), Object(E.f)()
												);
											case 4:
												(e.t2 = e.sent),
													(e.t3 = e.t1.setPortfolio.call(e.t1, e.t2)),
													(0, e.t0)(e.t3),
													_(!1);
											case 8:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function () {
							return e.apply(this, arguments);
						};
					})(),
					D = (function () {
						var e = Z(
							$().mark(function e(o) {
								return $().wrap(function (e) {
									for (;;)
										switch ((e.prev = e.next)) {
											case 0:
												return (
													b(d.a.openElement(w.a.LOADING_MODAL)),
													(e.next = 3),
													Object(E.e)(o)
												);
											case 3:
												b(d.a.closeElement(w.a.LOADING_MODAL));
											case 4:
											case "end":
												return e.stop();
										}
								}, e);
							}),
						);
						return function (o) {
							return e.apply(this, arguments);
						};
					})(),
					R = function (e, o) {
						console.log(e.name, "Inward", o.name);
					},
					S = (function () {
						var e = Z(
							$().mark(function e(o, t, n, r) {
								var i, a, l, c, u, s, d, f, p;
								return $().wrap(
									function (e) {
										for (;;)
											switch ((e.prev = e.next)) {
												case 0:
													(i = Y(o)),
														(a = i.findIndex(function (e) {
															return e.id === t;
														})),
														(l = i.findIndex(function (e) {
															return e.id === n;
														})),
														(c = l),
														r === v.b.ABOVE_TARGET && a < l && (c = l - 1),
														r === v.b.BELOW_TARGET && a > l && (c = l + 1),
														i.splice(c, 0, i.splice(a, 1)[0]),
														(u = K(i.entries())),
														(e.prev = 8),
														u.s();
												case 10:
													if ((s = u.n()).done) {
														e.next = 17;
														break;
													}
													if (
														((d = Q(s.value, 2)),
														(f = d[0]),
														(p = d[1]).order === f)
													) {
														e.next = 15;
														break;
													}
													return (e.next = 15), Object(E.i)(p.id, f);
												case 15:
													e.next = 10;
													break;
												case 17:
													e.next = 22;
													break;
												case 19:
													(e.prev = 19), (e.t0 = e.catch(8)), u.e(e.t0);
												case 22:
													return (e.prev = 22), u.f(), e.finish(22);
												case 25:
												case "end":
													return e.stop();
											}
									},
									e,
									null,
									[[8, 19, 22, 25]],
								);
							}),
						);
						return function (o, t, n, r) {
							return e.apply(this, arguments);
						};
					})(),
					C = (function () {
						var e = Z(
							$().mark(function e(o, t) {
								var n, r, i, a, l, c, u, s;
								return $().wrap(
									function (e) {
										for (;;)
											switch ((e.prev = e.next)) {
												case 0:
													return (e.next = 2), Object(E.f)();
												case 2:
													if (
														((n = e.sent),
														(r = n.findIndex(function (e) {
															return e.id === o;
														})),
														!(
															(0 === r && t === y.b.MOVE_UP) ||
															(r === n.length - 1 && t === y.b.MOVE_DOWN)
														))
													) {
														e.next = 7;
														break;
													}
													return e.abrupt("return");
												case 7:
													(i = t === y.b.MOVE_UP ? r - 1 : r + 1),
														n.splice(i, 0, n.splice(r, 1)[0]),
														(a = K(n.entries())),
														(e.prev = 10),
														a.s();
												case 12:
													if ((l = a.n()).done) {
														e.next = 19;
														break;
													}
													if (
														((c = Q(l.value, 2)),
														(u = c[0]),
														(s = c[1]).order === u)
													) {
														e.next = 17;
														break;
													}
													return (e.next = 17), Object(E.i)(s.id, u);
												case 17:
													e.next = 12;
													break;
												case 19:
													e.next = 24;
													break;
												case 21:
													(e.prev = 21), (e.t0 = e.catch(10)), a.e(e.t0);
												case 24:
													return (e.prev = 24), a.f(), e.finish(24);
												case 27:
													return (e.next = 29), j();
												case 29:
												case "end":
													return e.stop();
											}
									},
									e,
									null,
									[[10, 21, 24, 27]],
								);
							}),
						);
						return function (o, t) {
							return e.apply(this, arguments);
						};
					})(),
					F = Object(n.useCallback)(
						(function () {
							var e = Z(
								$().mark(function e(o, t) {
									return $().wrap(function (e) {
										for (;;)
											switch ((e.prev = e.next)) {
												case 0:
													return (e.next = 2), S(m, o, t, v.b.ABOVE_TARGET);
												case 2:
													j();
												case 3:
												case "end":
													return e.stop();
											}
									}, e);
								}),
							);
							return function (o, t) {
								return e.apply(this, arguments);
							};
						})(),
						[m],
					),
					G = Object(n.useCallback)(
						(function () {
							var e = Z(
								$().mark(function e(o, t) {
									return $().wrap(function (e) {
										for (;;)
											switch ((e.prev = e.next)) {
												case 0:
													return (e.next = 2), S(m, o, t, v.b.BELOW_TARGET);
												case 2:
													j();
												case 3:
												case "end":
													return e.stop();
											}
									}, e);
								}),
							);
							return function (o, t) {
								return e.apply(this, arguments);
							};
						})(),
						[m],
					);
				return o
					? null
					: r.a.createElement(
							H.a,
							{
								className: "Panel PortfolioPanel bookmark-outline-panel",
								dataElement: w.a.PORTFOLIO_PANEL,
							},
							r.a.createElement(
								"div",
								{ className: "bookmark-outline-panel-header" },
								r.a.createElement(
									"h2",
									{ className: "header-title" },
									h("portfolio.portfolioPanelTitle"),
								),
								r.a.createElement(
									"div",
									{ className: "portfolio-panel-control" },
									r.a.createElement(f.a, {
										className: "portfolio-panel-control-button",
										dataElement: w.a.PORTFOLIO_ADD_FILE,
										img: "icon-add-file",
										title: h("portfolio.addFile"),
										disabled: P,
										onClick: I,
									}),
									r.a.createElement("input", {
										ref: T,
										style: { display: "none" },
										type: "file",
										onChange: function (e) {
											A(e), (e.target.value = null);
										},
									}),
								),
							),
							r.a.createElement(
								p.Provider,
								{
									value: {
										activePortfolioItem: k,
										setActivePortfolioItem: x,
										isPortfolioItemActive: function (e) {
											return (null == e ? void 0 : e.id) === k;
										},
										isAddingNewFolder: P,
										setAddingNewFolder: _,
										addNewFolder: function (e) {
											e || (e = h("message.untitled")), j();
										},
										renamePortfolioItem: L,
										removePortfolioItem: function (e) {
											var o,
												t = m.find(function (o) {
													return o.id === e;
												}),
												n = {
													message: h("portfolio.deletePortfolio", {
														fileName: t.name,
													}),
													title: h("action.delete"),
													confirmBtnText: h("action.delete"),
													onConfirm:
														((o = Z(
															$().mark(function o() {
																return $().wrap(function (o) {
																	for (;;)
																		switch ((o.prev = o.next)) {
																			case 0:
																				return (o.next = 2), Object(E.d)(e);
																			case 2:
																				j();
																			case 3:
																			case "end":
																				return o.stop();
																		}
																}, o);
															}),
														)),
														function () {
															return o.apply(this, arguments);
														}),
												};
											b(d.a.showWarningMessage(n));
										},
										openPortfolioItem: function (e) {
											Object(E.g)(e.extension) &&
												(b(Object(U.b)()), b(d.a.addPortfolioTab(e)));
										},
										downloadPortfolioItem: D,
										refreshPortfolio: j,
										isNameDuplicated: function (e, o) {
											return m
												.filter(function (e) {
													return e.id !== o;
												})
												.some(function (o) {
													return o.name === e;
												});
										},
										tabManager: t,
									},
								},
								r.a.createElement(
									l.a,
									{ backend: V.l ? c.a : u.a },
									r.a.createElement(B, null),
									r.a.createElement(
										"div",
										{ className: "portfolio-panel-list" },
										m.map(function (e) {
											return r.a.createElement(M, {
												key: e.id,
												portfolioItem: e,
												movePortfolioInward: R,
												movePortfolioBeforeTarget: F,
												movePortfolioAfterTarget: G,
												movePortfolio: C,
											});
										}),
										P &&
											r.a.createElement(
												H.a,
												{
													className:
														"bookmark-outline-single-container editing",
												},
												r.a.createElement(N, {
													name: "",
													id: "0",
													isFolder: !0,
													isAdding: !0,
												}),
											),
									),
								),
							),
						);
			};
			o.default = oe;
		},
	},
]);
//# sourceMappingURL=chunk.40.js.map
