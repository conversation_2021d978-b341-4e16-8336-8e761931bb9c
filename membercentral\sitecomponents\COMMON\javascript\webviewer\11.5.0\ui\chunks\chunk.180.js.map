{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/ku.js"], "names": ["e", "t", "r", "default", "n", "d", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "o", "u", "i", "name", "months", "monthsShort", "weekdays", "split", "weekdaysShort", "weekStart", "weekdaysMin", "preparse", "replace", "postformat", "ordinal", "formats", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiem", "relativeTime", "future", "past", "s", "m", "mm", "h", "hh", "dd", "M", "MM", "y", "yy", "locale", "englishToArabicNumbersMap", "Object", "defineProperty", "value", "exports"], "mappings": "iFAA8P,SAAUA,EAAEC,GAAG,aAAqF,IAAIC,EAA5E,SAAWF,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACG,QAAQH,GAASI,CAAEH,GAAGI,EAAE,CAACC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,IAAIC,EAAE,KAAKC,EAAE,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKC,EAAE,CAAC,gBAAgB,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,UAAU,MAAM,UAAU,eAAe,eAAe,iBAAiBC,EAAE,CAACC,KAAK,KAAKC,OAAOH,EAAEI,YAAYJ,EAAEK,SAAS,4DAA4DC,MAAM,KAAKC,cAAc,kDAAkDD,MAAM,KAAKE,UAAU,EAAEC,YAAY,iBAAiBH,MAAM,KAAKI,SAAS,SAAS3B,GAAG,OAAOA,EAAE4B,QAAQ,iBAAgB,SAAU5B,GAAG,OAAOgB,EAAEhB,MAAM4B,QAAQ,KAAK,MAAMC,WAAW,SAAS7B,GAAG,OAAOA,EAAE4B,QAAQ,OAAM,SAAU5B,GAAG,OAAOK,EAAEL,MAAM4B,QAAQ,KAAK,MAAME,QAAQ,SAAS9B,GAAG,OAAOA,GAAG+B,QAAQ,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,cAAcC,IAAI,oBAAoBC,KAAK,2BAA2BC,SAAS,SAAStC,GAAG,OAAOA,EAAE,GAAG,MAAM,OAAOuC,aAAa,CAACC,OAAO,QAAQC,KAAK,KAAKC,EAAE,eAAeC,EAAE,YAAYC,GAAG,WAAWC,EAAE,cAAcC,GAAG,aAAazC,EAAE,UAAU0C,GAAG,SAASC,EAAE,WAAWC,GAAG,UAAUC,EAAE,UAAUC,GAAG,WAAWjD,EAAEC,QAAQiD,OAAOlC,EAAE,MAAK,GAAIlB,EAAEG,QAAQe,EAAElB,EAAEqD,0BAA0BhD,EAAEiD,OAAOC,eAAevD,EAAE,aAAa,CAACwD,OAAM,IAAl/CvD,CAAEwD,EAAQ,EAAQ", "file": "chunks/chunk.180.js", "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?t(exports,require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"exports\",\"dayjs\"],t):t((e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_ku={},e.dayjs)}(this,(function(e,t){\"use strict\";function n(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var r=n(t),d={1:\"١\",2:\"٢\",3:\"٣\",4:\"٤\",5:\"٥\",6:\"٦\",7:\"٧\",8:\"٨\",9:\"٩\",0:\"٠\"},o={\"١\":\"1\",\"٢\":\"2\",\"٣\":\"3\",\"٤\":\"4\",\"٥\":\"5\",\"٦\":\"6\",\"٧\":\"7\",\"٨\":\"8\",\"٩\":\"9\",\"٠\":\"0\"},u=[\"کانوونی دووەم\",\"شوبات\",\"ئادار\",\"نیسان\",\"ئایار\",\"حوزەیران\",\"تەممووز\",\"ئاب\",\"ئەیلوول\",\"تشرینی یەکەم\",\"تشرینی دووەم\",\"کانوونی یەکەم\"],i={name:\"ku\",months:u,monthsShort:u,weekdays:\"یەکشەممە_دووشەممە_سێشەممە_چوارشەممە_پێنجشەممە_هەینی_شەممە\".split(\"_\"),weekdaysShort:\"یەکشەم_دووشەم_سێشەم_چوارشەم_پێنجشەم_هەینی_شەممە\".split(\"_\"),weekStart:6,weekdaysMin:\"ی_د_س_چ_پ_هـ_ش\".split(\"_\"),preparse:function(e){return e.replace(/[١٢٣٤٥٦٧٨٩٠]/g,(function(e){return o[e]})).replace(/،/g,\",\")},postformat:function(e){return e.replace(/\\d/g,(function(e){return d[e]})).replace(/,/g,\"،\")},ordinal:function(e){return e},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"DD/MM/YYYY\",LL:\"D MMMM YYYY\",LLL:\"D MMMM YYYY HH:mm\",LLLL:\"dddd, D MMMM YYYY HH:mm\"},meridiem:function(e){return e<12?\"پ.ن\":\"د.ن\"},relativeTime:{future:\"لە %s\",past:\"%s\",s:\"چەند چرکەیەک\",m:\"یەک خولەک\",mm:\"%d خولەک\",h:\"یەک کاتژمێر\",hh:\"%d کاتژمێر\",d:\"یەک ڕۆژ\",dd:\"%d ڕۆژ\",M:\"یەک مانگ\",MM:\"%d مانگ\",y:\"یەک ساڵ\",yy:\"%d ساڵ\"}};r.default.locale(i,null,!0),e.default=i,e.englishToArabicNumbersMap=d,Object.defineProperty(e,\"__esModule\",{value:!0})}));"], "sourceRoot": ""}