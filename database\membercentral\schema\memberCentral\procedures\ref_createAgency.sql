ALTER PROC dbo.ref_createAgency
@referralID int,
@name varchar(255) = NULL,
@importCode varchar(75) = NULL,
@description varchar(255) = NULL,
@isActive bit = NULL,
@address1 varchar(255) = NULL,
@address2 varchar(255) = NULL,
@address3 varchar(255) = NULL,
@city varchar(100) = NULL,
@stateID int = NULL,
@postalCode varchar(25) = NULL,
@phone varchar(40) = NULL,
@alternatePhone varchar(40) = NULL,
@email varchar(255) = NULL,
@website varchar(255) = NULL,
@createdBy int,
@agencyID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @stateName varchar(40), @crlf varchar(10), @msgjson varchar(max);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	IF @stateID IS NOT NULL
		SELECT @stateName = name FROM dbo.ams_states WHERE stateID = @stateID;

	INSERT INTO dbo.ref_agencies(referralID, [name], importCode, [description], isActive, createdBy, address1,
		address2, address3, city, [state], postalCode, phone, alternatePhone, email, website, dateCreated)
	VALUES (@referralID, @name, NULLIF(@importCode,''), NULLIF(@description,''), NULLIF(@isActive,0), @createdBy,
		NULLIF(@address1,''), NULLIF(@address2,''), NULLIF(@address3,''), NULLIF(@city,''), NULLIF(@stateID,0),
		NULLIF(@postalCode,''), NULLIF(@phone,''), NULLIF(@alternatePhone,''), NULLIF(@email,''), NULLIF(@website,''),
		GETDATE());

	SELECT @agencyID = SCOPE_IDENTITY();
					
	SET @msgjson = 'New Agency [' + @name + '] has been created.'
		+ CASE WHEN NULLIF(@importCode,'') IS NOT NULL THEN @crlf + 'Import Code: ' + @importCode ELSE '' END
		+ CASE WHEN NULLIF(@address1,'') IS NOT NULL THEN @crlf + 'Address 1: ' + @address1 ELSE '' END
		+ CASE WHEN NULLIF(@address2,'') IS NOT NULL THEN @crlf + 'Address 2: ' + @address2 ELSE '' END
		+ CASE WHEN NULLIF(@address3,'') IS NOT NULL THEN @crlf + 'Address 3: ' + @address3 ELSE '' END
		+ CASE WHEN NULLIF(@city,'') IS NOT NULL THEN @crlf + 'City: ' + @city ELSE '' END
		+ CASE WHEN NULLIF(@stateName,'') IS NOT NULL THEN @crlf + 'State: ' + @stateName ELSE '' END
		+ CASE WHEN NULLIF(@postalCode,'') IS NOT NULL THEN @crlf + 'Postal Code: ' + @postalCode ELSE '' END
		+ CASE WHEN NULLIF(@email,'') IS NOT NULL THEN @crlf + 'Email: ' + @email ELSE '' END
		+ CASE WHEN NULLIF(@phone,'') IS NOT NULL THEN @crlf + 'Phone: ' + @phone ELSE '' END
		+ CASE WHEN NULLIF(@alternatePhone,'') IS NOT NULL THEN @crlf + 'Alternate Phone: ' + @alternatePhone ELSE '' END
		+ CASE WHEN NULLIF(@website,'') IS NOT NULL THEN @crlf + 'Website: ' + @website ELSE '' END
		+ @crlf + 'Is Active?: ' + CASE @isActive WHEN 1 THEN 'Yes' ELSE 'No' END;

	SET @msgjson = STRING_ESCAPE(@msgjson,'json');

	EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='AGENCIES', @msgjson=@msgjson, @enteredByMemberID=@createdBy;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
