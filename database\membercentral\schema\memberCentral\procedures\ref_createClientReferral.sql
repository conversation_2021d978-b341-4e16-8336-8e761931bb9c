ALTER PROCEDURE dbo.ref_createClientReferral
@referralID int,
@clientID int,
@representativeID int = NULL,
@memberID int = NULL,
@enteredByMemberID int,
@sourceID int,
@otherSource varchar(100) = NULL,
@communicateLanguageID int,
@issueDesc varchar(max),
@agencyID int = NULL,
@sendSurvey	bit = NULL,
@statusID int,
@typeID	int,
@sendNewsBlog bit = NULL,
@panelID int = NULL,
@clientReferralID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @clientReferralDate datetime, @nowDate datetime = getDate(),
		@isReferred bit, @thisUUID varchar(36), @crlf varchar(10), @message varchar(max),
		@statusName varchar(255), @uniqueCode char(8);

	SET @crlf = char(13) + char(10);

	SELECT @orgID = s.orgID, @siteID = s.siteID
	FROM dbo.ref_referrals AS r
	INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
	INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
	WHERE r.referralID = @referralID;

	SELECT @isReferred = isReferred, @statusName = statusName
	FROM dbo.ref_clientReferralStatus
	WHERE clientReferralStatusID = @statusID;
		
	IF @isReferred = 1
		SET @clientReferralDate = @nowDate;

	SET @thisUUID = cast(newid() as varchar(36));
	SET @thisUUID = left(@thisUUID, 23) + right(@thisUUID,12);

	EXEC dbo.getUniqueCode @uniqueCode=@uniqueCode OUTPUT;

	INSERT INTO dbo.ref_clientReferrals (clientID, representativeID, memberID, enteredByMemberID, sourceID, otherSource, communicateLanguageID, issueDesc,
		agencyID, sendSurvey, statusID, typeID, clientReferralDate, dateCreated, sendNewsBlog, callUID, panelID, referralID,referralCode, [uid])
	VALUES (@clientID, @representativeID, @memberID, @enteredByMemberID, @sourceID, @otherSource, @communicateLanguageID, @issueDesc,
		@agencyID, @sendSurvey, @statusID, @typeID, @clientReferralDate, @nowDate, @sendNewsBlog, @thisUUID, @panelID, @referralID,@uniqueCode, NEWID());

	SELECT @clientReferralID = SCOPE_IDENTITY();

	SELECT @message = 'Referral Created'
		+ @crlf + 'Client: ' + c.firstName + ' ' + c.lastName
		+ CASE WHEN rep.clientID IS NOT NULL THEN @crlf + 'Representative: ' + rep.firstName + ' ' + rep.lastName ELSE '' END
		+ CASE WHEN s.clientReferralSourceID IS NOT NULL THEN @crlf + 'Source: ' + s.clientReferralSource ELSE '' END
		+ CASE WHEN m.memberID IS NOT NULL THEN @crlf + 'Counselor Name: ' + m.firstName + ' ' + m.lastName ELSE '' END
		+ CASE WHEN t.clientReferralTypeID IS NOT NULL THEN @crlf + 'Call Type: ' + t.clientReferralType ELSE '' END
		+ CASE WHEN a.agencyID IS NOT NULL THEN @crlf + 'Agency: ' + a.[name] ELSE '' END
		+ CASE WHEN s.clientReferralSourceID IS NOT NULL THEN @crlf + 'Source: ' + s.clientReferralSource ELSE '' END
		+ CASE WHEN @clientReferralDate IS NOT NULL THEN @crlf + 'Referral Date: ' + convert(varchar(20),@clientReferralDate,120) ELSE '' END
		+ @crlf + 'Referral Status: ' + @statusName
		+ @crlf + 'Referral Code: ' + @uniqueCode
	FROM dbo.ref_clientReferrals AS cr
	INNER JOIN dbo.ref_clients AS c ON c.clientID = cr.clientID
	LEFT OUTER JOIN dbo.ref_clients AS rep ON rep.clientID = cr.representativeID
	LEFT OUTER JOIN dbo.ref_clientReferralSources AS s ON s.clientReferralSourceID = cr.sourceID
	LEFT OUTER JOIN dbo.ams_members AS m ON m.memberID = cr.enteredByMemberID
	LEFT OUTER JOIN dbo.ref_clientReferralTypes AS t ON t.clientReferralTypeID = cr.typeID
	LEFT OUTER JOIN dbo.ref_agencies AS a ON a.agencyID = cr.agencyID
	WHERE cr.clientReferralID = @clientReferralID;

	SET @message = STRING_ESCAPE(@message,'json');

	EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
		@message=@message, @enteredByMemberID=@enteredByMemberID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
