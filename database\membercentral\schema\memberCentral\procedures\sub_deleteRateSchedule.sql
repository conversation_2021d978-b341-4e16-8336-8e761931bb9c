ALTER PROCEDURE dbo.sub_deleteRateSchedule
@siteID int,
@scheduleID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @tblRates TABLE (rateID int);
	DECLARE @orgID int, @rateID int, @checkScheduleID int, @subscriptionID int, @scheduleName varchar(200), 
		@crlf varchar(10) = char(13) + char(10), @msgjson varchar(max);
	
	SELECT @orgID = orgID 
	FROM dbo.sites
	WHERE siteID = @siteID;
	
	SELECT @checkScheduleID = scheduleID, @scheduleName = scheduleName
	FROM dbo.sub_rateSchedules
	WHERE siteID = @siteID
	AND scheduleID = @scheduleID;

	IF @checkScheduleID is null
		RAISERROR('Invalid Rate Schedule.',16,1);

	SELECT TOP 1 @subscriptionID = subscriptionID
	FROM dbo.sub_subscriptions
	WHERE orgID = @orgID
	AND scheduleID = @scheduleID;

	IF @subscriptionID is not null
		RAISERROR('Rate Schedule in use by a subscription.',16,1);

	INSERT INTO @tblRates (rateID)
	select r.rateID
	from dbo.sub_rates as r
	inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
		and sr.siteResourceID = r.siteResourceID
		and sr.siteResourceStatusID = 1
	where r.scheduleID = @scheduleID;
	
	BEGIN TRAN;
		select @rateID = min(rateID) from @tblRates;
		while @rateID is not null begin
			EXEC dbo.sub_deleteRate @orgID=@orgID, @rateID=@rateID, @recordedByMemberID=@enteredByMemberID;
			select @rateID = min(rateID) from @tblRates where rateID > @rateID;
		end

		UPDATE dbo.sub_rateSchedules
		set [status] = 'D'
		where scheduleID = @scheduleID;

		-- audit log
		DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID":' + CAST(@checkScheduleID AS varchar(10)) +' }';
		SET @msgjson = 'Subscription Rate Schedule [' + STRING_ESCAPE(@scheduleName,'json') + '] and its rates have been deleted.'

		EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msgjson,
				@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@enteredByMemberID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
