(window.webpackJsonp = window.webpackJsonp || []).push([
	[89],
	{
		2013: function (t, e, n) {
			"use strict";
			n.r(e);
			n(97),
				n(27),
				n(28),
				n(12),
				n(13),
				n(8),
				n(25),
				n(22),
				n(58),
				n(44),
				n(14),
				n(10),
				n(9),
				n(11);
			var r = n(0),
				o = n.n(r),
				i = n(82),
				a = n(1),
				l = n(6),
				u = n(4),
				c = n(3),
				s = n.n(c),
				y = n(68),
				f = n(70),
				b = n(7);
			function p(t) {
				return (p =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function m() {
				return (m = Object.assign
					? Object.assign.bind()
					: function (t) {
							for (var e = 1; e < arguments.length; e++) {
								var n = arguments[e];
								for (var r in n)
									Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r]);
							}
							return t;
						}).apply(this, arguments);
			}
			function v(t, e, n) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== p(t) || null === t) return t;
							var n = t[Symbol.toPrimitive];
							if (void 0 !== n) {
								var r = n.call(t, e || "default");
								if ("object" !== p(r)) return r;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === p(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = n),
					t
				);
			}
			var d = {
					styleType: s.a.oneOf(Object.values(b.t)).isRequired,
					isFlyoutItem: s.a.bool,
					style: s.a.object,
					className: s.a.string,
				},
				g = Object(r.forwardRef)(function (t, e) {
					var n = t.isFlyoutItem,
						r = t.styleType,
						c = t.style,
						s = t.className,
						b = Object(l.e)(function (t) {
							return u.a.isStyleButtonActive(t, r);
						}),
						p = f.b["".concat(r, "Button")],
						d = p.dataElement,
						g = p.icon,
						S = p.title,
						w = function () {
							a.a.getOfficeEditor().updateSelectionAndCursorStyle(v({}, r, !0));
						};
					return n
						? o.a.createElement(
								y.a,
								m({}, t, {
									ref: e,
									onClick: w,
									additionalClass: b ? "active" : "",
								}),
							)
						: o.a.createElement(i.a, {
								key: r,
								isActive: b,
								onClick: w,
								dataElement: d,
								title: S,
								img: g,
								ariaPressed: b,
								style: c,
								className: s,
							});
				});
			(g.propTypes = d),
				(g.displayName = "FontStyleToggleButton"),
				(e.default = g);
		},
	},
]);
//# sourceMappingURL=chunk.89.js.map
