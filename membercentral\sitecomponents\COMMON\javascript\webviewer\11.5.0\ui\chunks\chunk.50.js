(window.webpackJsonp = window.webpackJsonp || []).push([
	[50],
	{
		1896: function (t, e, o) {
			var n = o(30),
				a = o(1897);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[t.i, a, ""]]);
			var r = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const a = e[n];
						if (0 === n)
							a.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							a.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(a, r);
			t.exports = a.locals || {};
		},
		1897: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				':host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.text-customstamp .color-container .ColorPalette .cell-tool:hover{cursor:pointer;border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6);background-color:var(--faded-component-background)}.text-customstamp{padding:16px;margin:0;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp{width:100%;padding:4px 8px}.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .scroll-container{width:100%;max-height:50%;padding-bottom:0;margin-bottom:0;overflow-y:auto;overflow-x:visible}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp{width:100%;padding:4px 8px}.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .scroll-container{width:100%;max-height:50%;padding-bottom:0;margin-bottom:0;overflow-y:auto;overflow-x:visible}}.text-customstamp-input{outline:none;border-radius:4px;border:1px solid var(--gray-6);box-shadow:none;padding:5px}.text-customstamp .txt-uppercase{text-transform:uppercase}.text-customstamp-container{position:relative;margin-top:10px;flex:1;border:1px solid var(--modal-stroke-and-border);border-radius:4px;overflow:auto}.text-customstamp-container canvas{position:absolute;visibility:hidden;z-index:-1;width:100%;height:100%}.text-customstamp-inner-container{position:absolute;min-width:100%}.text-customstamp .canvas-container{border:1px solid var(--gray-6);box-sizing:border-box;position:relative;overflow:hidden;background-color:var(--signature-draw-background);display:flex;flex-direction:row;justify-content:center;padding:48px 74px;border-radius:4px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .canvas-container{padding:12px .5px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .canvas-container{padding:12px .5px}}.text-customstamp .canvas-container .custom-stamp-canvas{box-sizing:border-box}@media(max-width:430px){.text-customstamp .canvas-container .custom-stamp-canvas{transform:scale(.8)}}.text-customstamp .color-container{margin-top:16px;width:100%}.text-customstamp .color-container .ColorPalette{display:flex;flex-wrap:wrap;width:100%;grid-gap:4px;gap:4px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .color-container .ColorPalette{padding:4px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .color-container .ColorPalette{padding:4px}}.text-customstamp .color-container .ColorPalette .cell-outer{margin-right:16px}.text-customstamp .color-container .ColorPalette .cell-outer.active{margin-left:-4px;margin-right:10px;width:28px;height:28px}.text-customstamp .color-container .ColorPalette .cell-outer .cell{width:16px;height:16px}.text-customstamp .color-container .ColorPalette .cell-container{flex:unset;width:auto;height:auto}.text-customstamp .color-container .ColorPalette .cell-tool{margin-right:8px;margin-left:-4px}.text-customstamp .color-container .ColorPalette .cell-tool .cell-outer{margin:4px}.text-customstamp .color-container .ColorPalette .cell-tool.active{box-shadow:inset 0 0 0 1px var(--blue-5);background:var(--tools-button-active)}.text-customstamp .color-container .ColorPalette .cell-tool.active .Icon{color:var(--blue-5)}.text-customstamp .color-container .ColorPalette .cell-tool:disabled{box-shadow:none;background:transparent}.text-customstamp .stamp-input-container{margin-top:16px;padding:0;position:relative}.text-customstamp .stamp-input-container .text-customstamp-input{margin-top:8px;height:32px;width:100%;padding:6px}.text-customstamp .stamp-input-container .text-customstamp-input.error{border:1px solid var(--error-text-color)}.text-customstamp .stamp-input-container .error-icon{position:absolute;right:8px;top:28px;color:var(--error-text-color)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-input-container .error-icon{top:30px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-input-container .error-icon{top:30px}}.text-customstamp .stamp-input-container .empty-stamp-input{height:16px;margin-top:4px;color:var(--error-text-color)}.text-customstamp .stamp-label,.text-customstamp .stamp-sublabel{font-weight:700;margin-bottom:8px;text-transform:capitalize}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-label,.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .stamp-sublabel{font-size:13px;height:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-label,.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .stamp-sublabel{font-size:13px;height:16px}}.text-customstamp .timestamp-container{margin-top:16px;padding:0}.text-customstamp .timestamp-container .timeStamp-choice{height:16px;display:grid;width:100%;grid-template-columns:22.32% 22.32% 22.32% 33.04%;grid-gap:16px}@media(max-width:430px){.text-customstamp .timestamp-container .timeStamp-choice{grid-template-columns:30% 30% 30%;height:100%;font-size:13px}}@media (-ms-high-contrast:active),(-ms-high-contrast:none){.text-customstamp .timestamp-container .timeStamp-choice .ui__choice{display:-ms-inline-flexbox;width:33.33%}}.text-customstamp .timestamp-container .timeStamp-choice .ui__choice{margin:0;line-height:1}.text-customstamp .timestamp-container .timeStamp-choice .ui__choice .ui__choice__label{padding-left:4px}.text-customstamp .date-format-container{margin-top:16px;display:flex;flex-wrap:wrap}.text-customstamp .date-format-container .Dropdown__wrapper{width:100%}.text-customstamp .date-format-container .Dropdown__wrapper .Dropdown{height:32px;width:100%!important;text-align:left}.text-customstamp .date-format-container .Dropdown__wrapper .arrow{flex:unset}.text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{z-index:101;top:auto;left:0;right:auto;width:100%!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{max-height:200px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .date-format-container .Dropdown__wrapper .Dropdown__items{max-height:200px}}.text-customstamp .date-format-container .hover-icon{margin-left:2px;position:relative}.text-customstamp .date-format-container .hover-icon .Icon{width:18px;height:18px}.text-customstamp .date-format-container .hover-icon .date-format-description{display:flex;flex-direction:column;text-align:left;position:absolute;background-color:#000;border-radius:5px;padding:12px;color:#fff;width:120px;z-index:101;bottom:100%;top:auto;left:0;right:auto}.text-customstamp .date-format-container .hover-icon:hover .Icon{color:var(--blue-6)}.text-customstamp .font-container{margin-top:8px}.text-customstamp .font-container .font-inner-container{display:flex;grid-gap:12px;gap:12px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .font-inner-container{grid-gap:6px;gap:6px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .font-inner-container{grid-gap:6px;gap:6px}}.text-customstamp .font-container .stamp-sublabel{font-weight:700;margin-bottom:8px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .stamp-sublabel{font-size:13px;height:16px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .stamp-sublabel{font-size:13px;height:16px}}.text-customstamp .font-container .Dropdown__wrapper .Dropdown{margin-right:4px;height:32px;width:268px!important;text-align:left}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .Dropdown__wrapper .Dropdown{width:200px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .Dropdown__wrapper .Dropdown{width:200px!important}}.text-customstamp .font-container .Dropdown__wrapper .arrow{flex:unset}.text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{top:auto;left:0;right:auto;width:268px!important}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{bottom:auto;height:200px;width:200px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .text-customstamp .font-container .Dropdown__wrapper .Dropdown__items{bottom:auto;height:200px;width:200px!important}}.text-customstamp .font-container .Button{background:none;width:32px;height:32px;margin:0}.text-customstamp .font-container .Button .Icon{height:24px;width:24px;color:var(--icon-color)}.text-customstamp .font-container .Button:hover{background:var(--popup-button-hover)}.text-customstamp .font-container .Button:hover .Icon .cls-1{fill:currentColor}.text-customstamp .font-container .Button.active{background:var(--popup-button-active)}.text-customstamp .font-container .Button.active .Icon{color:var(--blue-5)}.text-customstamp .font-container .Button.active .Icon .cls-1{fill:currentColor}.text-customstamp .custom-checkbox{position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;margin-bottom:5px;margin-top:5px}.text-customstamp .custom-checkbox input{display:none}.text-customstamp .custom-checkbox input+label:before{cursor:pointer;content:"";margin:1px 10px 0 0;display:inline-block;vertical-align:text-top;width:16px;height:16px;border-radius:4px;border:1px solid #979797}.text-customstamp .custom-checkbox input:checked+label:before{background:#00a5e4;border:1px solid transparent}.text-customstamp .custom-checkbox input:checked+label:after{content:"";position:absolute;left:4px;top:9px;background:var(--gray-0);width:2px;height:2px;box-shadow:2px 0 0 var(--gray-0),4px 0 0 var(--gray-0),4px -2px 0 var(--gray-0),4px -4px 0 var(--gray-0),4px -6px 0 var(--gray-0),4px -8px 0 var(--gray-0);transform:rotate(45deg)}.text-customstamp .custom-checkbox label{align-items:center}',
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		1898: function (t, e, o) {
			var n = o(30),
				a = o(1899);
			"string" == typeof (a = a.__esModule ? a.default : a) &&
				(a = [[t.i, a, ""]]);
			var r = {
				insert: function (t) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(t);
					let e;
					(e = document.getElementsByTagName("apryse-webviewer")),
						e.length ||
							(e = (function t(e, o = document) {
								const n = [];
								return (
									o.querySelectorAll(e).forEach((t) => n.push(t)),
									o.querySelectorAll("*").forEach((o) => {
										o.shadowRoot && n.push(...t(e, o.shadowRoot));
									}),
									n
								);
							})("apryse-webviewer"));
					const o = [];
					for (let n = 0; n < e.length; n++) {
						const a = e[n];
						if (0 === n)
							a.shadowRoot.appendChild(t),
								(t.onload = function () {
									o.length > 0 &&
										o.forEach((e) => {
											e.innerHTML = t.innerHTML;
										});
								});
						else {
							const e = t.cloneNode(!0);
							a.shadowRoot.appendChild(e), o.push(e);
						}
					}
				},
				singleton: !1,
			};
			n(a, r);
			t.exports = a.locals || {};
		},
		1899: function (t, e, o) {
			(e = t.exports = o(31)(!1)).push([
				t.i,
				".open.CustomStampModal{visibility:visible}.closed.CustomStampModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.CustomStampModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.CustomStampModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.CustomStampModal .footer .modal-button.cancel:hover,.CustomStampModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.CustomStampModal .footer .modal-button.cancel,.CustomStampModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.CustomStampModal .footer .modal-button.cancel.disabled,.CustomStampModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.CustomStampModal .footer .modal-button.cancel.disabled span,.CustomStampModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.CustomStampModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.CustomStampModal .modal-container .wrapper .modal-content{padding:10px}.CustomStampModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.CustomStampModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.CustomStampModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.CustomStampModal .footer .modal-button.confirm{margin-left:4px}.CustomStampModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .footer .modal-button{padding:23px 8px}}.CustomStampModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .swipe-indicator{width:32px}}.CustomStampModal .modal-container{display:flex;flex-direction:column;width:480px;padding:0;border-radius:4px;background:var(--component-background);overflow:visible;max-height:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .modal-container{width:100%;border-radius:0;left:0;bottom:0}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .modal-container{width:100%;border-radius:0;left:0;bottom:0}}.CustomStampModal .modal-container .header{box-shadow:inset 0 -1px 0 var(--modal-stroke-and-border);margin:0;display:flex;align-items:center;width:100%;padding:16px}.CustomStampModal .modal-container .header p{font-size:16px;font-weight:700;width:89.286%;margin:0 16px 0 0}.CustomStampModal .modal-container .header .Button{position:static;height:32px;width:32px;border-radius:4px}.CustomStampModal .modal-container .header .Button:hover{background:var(--popup-button-hover)}.CustomStampModal .modal-container .header .Button.selected{background:var(--popup-button-active);cursor:default}.CustomStampModal .modal-container .footer{display:flex;align-items:center;flex-direction:row-reverse;justify-content:space-between;margin:0;top:460px;padding:16px;box-shadow:inset 0 1px 0 var(--modal-stroke-and-border)}.CustomStampModal .modal-container .footer .stamp-close{border:none;background-color:transparent;color:var(--secondary-button-text);padding:0 8px;height:32px;display:flex;align-items:center;justify-content:center;cursor:pointer}:host(:not([data-tabbing=true])) .CustomStampModal .modal-container .footer .stamp-close,html:not([data-tabbing=true]) .CustomStampModal .modal-container .footer .stamp-close{outline:none}.CustomStampModal .modal-container .footer .stamp-close:hover{color:var(--secondary-button-hover)}.CustomStampModal .modal-container .footer .stamp-create{border:none;background-color:transparent;background:var(--primary-button);border-radius:4px;height:32px;display:flex;align-items:center;padding:8px 16px;width:72px;justify-content:center;position:relative;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .CustomStampModal .modal-container .footer .stamp-create,html:not([data-tabbing=true]) .CustomStampModal .modal-container .footer .stamp-create{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .CustomStampModal .modal-container .footer .stamp-create{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .CustomStampModal .modal-container .footer .stamp-create{font-size:13px}}.CustomStampModal .modal-container .footer .stamp-create:hover{background:var(--primary-button-hover)}.CustomStampModal .modal-container .footer .stamp-create:disabled{background:var(--primary-button);opacity:.5;cursor:default}",
				"",
			]),
				(e.locals = { LEFT_HEADER_WIDTH: "41px", RIGHT_HEADER_WIDTH: "41px" });
		},
		2040: function (t, e, o) {
			"use strict";
			o.r(e);
			o(19),
				o(12),
				o(13),
				o(8),
				o(14),
				o(10),
				o(9),
				o(11),
				o(16),
				o(15),
				o(20),
				o(18),
				o(54),
				o(22),
				o(61),
				o(62),
				o(63),
				o(64),
				o(37),
				o(39),
				o(23),
				o(24),
				o(40),
				o(60);
			var n = o(0),
				a = o.n(n),
				r = o(17),
				i = o.n(r),
				c = o(6),
				l = o(1),
				s = o(2),
				p = o(4),
				m = o(347),
				d =
					(o(36),
					o(101),
					o(290),
					o(353),
					o(29),
					o(145),
					o(41),
					o(27),
					o(28),
					o(25),
					o(32),
					o(47),
					o(49),
					o(48),
					o(155)),
				u = o(78),
				f = o(42),
				h = o(43),
				x = o(523),
				b = o(55),
				w = o(3),
				v = o.n(w),
				g = (o(1896), o(21)),
				y = o(106);
			function S(t) {
				return (S =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function C(t, e) {
				var o = Object.keys(t);
				if (Object.getOwnPropertySymbols) {
					var n = Object.getOwnPropertySymbols(t);
					e &&
						(n = n.filter(function (e) {
							return Object.getOwnPropertyDescriptor(t, e).enumerable;
						})),
						o.push.apply(o, n);
				}
				return o;
			}
			function k(t) {
				for (var e = 1; e < arguments.length; e++) {
					var o = null != arguments[e] ? arguments[e] : {};
					e % 2
						? C(Object(o), !0).forEach(function (e) {
								E(t, e, o[e]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(t, Object.getOwnPropertyDescriptors(o))
							: C(Object(o)).forEach(function (e) {
									Object.defineProperty(
										t,
										e,
										Object.getOwnPropertyDescriptor(o, e),
									);
								});
				}
				return t;
			}
			function E(t, e, o) {
				return (
					(e = (function (t) {
						var e = (function (t, e) {
							if ("object" !== S(t) || null === t) return t;
							var o = t[Symbol.toPrimitive];
							if (void 0 !== o) {
								var n = o.call(t, e || "default");
								if ("object" !== S(n)) return n;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === e ? String : Number)(t);
						})(t, "string");
						return "symbol" === S(e) ? e : String(e);
					})(e)) in t
						? Object.defineProperty(t, e, {
								value: o,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (t[e] = o),
					t
				);
			}
			function M(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								a,
								r,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((r = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									l = !1;
								} else
									for (
										;
										!(l = (n = r.call(o)).done) &&
										(c.push(n.value), c.length !== e);
										l = !0
									);
							} catch (t) {
								(s = !0), (a = t);
							} finally {
								try {
									if (
										!l &&
										null != o.return &&
										((i = o.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw a;
								}
							}
							return c;
						}
					})(t, e) ||
					(function (t, e) {
						if (!t) return;
						if ("string" == typeof t) return O(t, e);
						var o = Object.prototype.toString.call(t).slice(8, -1);
						"Object" === o && t.constructor && (o = t.constructor.name);
						if ("Map" === o || "Set" === o) return Array.from(t);
						if (
							"Arguments" === o ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
						)
							return O(t, e);
					})(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function O(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var A = { date: "MM/DD/YYYY", time: "hh:mm A", timeFirst: !1 },
				_ = function (t) {
					var e =
							!(arguments.length > 1 && void 0 !== arguments[1]) ||
							arguments[1],
						o =
							!(arguments.length > 2 && void 0 !== arguments[2]) ||
							arguments[2];
					return e
						? o
							? t.timeFirst
								? "".concat(t.time, " ").concat(t.date)
								: "".concat(t.date, " ").concat(t.time)
							: t.date
								? t.date
								: ""
						: t.time
							? t.time
							: "";
				},
				T = {
					state: v.a.object,
					setState: v.a.func,
					setEmptyInput: v.a.func,
					fonts: v.a.array,
					openColorPicker: v.a.func,
					getCustomColorAndRemove: v.a.func,
					openDeleteModal: v.a.func,
					dateTimeFormats: v.a.array,
					stampTool: v.a.object,
					userName: v.a.string,
				},
				j = function (t) {
					var e = t.state,
						o = t.setState,
						r = t.setEmptyInput,
						c = t.fonts,
						s = t.openColorPicker,
						p = t.getCustomColorAndRemove,
						w = t.openDeleteModal,
						v = t.dateTimeFormats,
						S = t.stampTool,
						C = t.userName,
						O = function (t, e, o) {
							var n = "";
							return t && (n += "[$currentUser] "), e && (n += o), n;
						},
						T = v && v[0] ? v[0] : A,
						j = M(Object(n.useState)(_(T)), 2),
						L = j[0],
						D = j[1],
						N = M(Object(n.useState)(!0), 2),
						I = N[0],
						P = N[1],
						R = M(Object(n.useState)(!0), 2),
						B = R[0],
						H = R[1],
						F = M(Object(n.useState)(!0), 2),
						z = F[0],
						U = F[1],
						G = M(Object(n.useState)("Draft"), 2),
						W = G[0],
						Y = G[1],
						V = M(Object(m.a)(), 1)[0],
						X = O(I, B, L),
						q = M(Object(n.useState)(X), 2),
						K = q[0],
						$ = q[1],
						J = M(Object(n.useState)(!1), 2),
						Q = J[0],
						Z = J[1],
						tt = Object(n.useRef)(null),
						et = l.a.getCurrentUser(),
						ot = new Date().toLocaleString(),
						nt = function (t) {
							tt.current && !tt.current.contains(t.target) && Z(!1);
						};
					Object(n.useEffect)(
						function () {
							return (
								Q
									? document.addEventListener("click", nt)
									: document.removeEventListener("click", nt),
								function () {
									document.removeEventListener("click", nt);
								}
							);
						},
						[Q],
					);
					var at = function () {
							Z(!Q);
						},
						rt = Object(n.useRef)(),
						it = Object(n.useRef)(),
						ct = Object(n.useRef)(),
						lt = function (t, n) {
							var a =
									arguments.length > 2 && void 0 !== arguments[2]
										? arguments[2]
										: e,
								r = {
									canvas: rt.current,
									title: t,
									subtitle: n,
									width: 300,
									height: 100,
									color: a.color,
									textColor: a.textColor,
									canvasParent: it.current,
									font: a.font,
									bold: a.bold,
									italic: a.italic,
									underline: a.underline,
									strikeout: a.strikeout,
								},
								i = S.drawCustomStamp(r),
								c = rt.current.toDataURL();
							o(
								k(
									k({}, a),
									{},
									{
										width: i,
										title: t,
										subtitle: n,
										height: r.height,
										dataURL: c,
									},
								),
							);
						};
					Object(n.useEffect)(
						function () {
							lt(W, K, e);
						},
						[C],
					);
					var st = function (t) {
							lt(W, K, k(k({}, e), {}, E({}, t, !e[t])));
						},
						pt = V("option.customStampModal.stampText"),
						mt = function (t) {
							return t && t.A ? t.toHexString().toLowerCase() : y.b.black;
						},
						dt = function (t, o) {
							var n = "text" === o;
							s();
							Object(g.c)().instance.UI.addEventListener(
								b.a.VISIBILITY_CHANGED,
								function t(o) {
									var a = o.detail,
										r = a.element,
										i = a.isVisible;
									if ("ColorPickerModal" === r && !i) {
										var c = p();
										if (c) {
											var l = mt(c);
											n ? At(l) : vt(l);
											var s =
												window.Core.Tools.RubberStampCreateTool[
													n ? "TEXT_COLORS" : "FILL_COLORS"
												];
											s.push(l),
												n ? Et(s) : xt(s),
												(e = k(
													k({}, e),
													{},
													E({}, n ? "textColor" : "color", l),
												)),
												lt(W, K, e);
										}
										Object(g.c)().instance.UI.removeEventListener(
											b.a.VISIBILITY_CHANGED,
											t,
										);
									}
								},
							);
						},
						ut = function (t) {
							var e = "text" === t;
							w(function () {
								var t = e
									? kt.filter(function (t) {
											return t !== Tt;
										})
									: ht.filter(function (t) {
											return t !== yt;
										});
								e ? Et(t) : xt(t),
									e ? jt(null) : St(null),
									(window.Core.Tools.RubberStampCreateTool[
										e ? "TEXT_COLORS" : "FILL_COLORS"
									] = t);
							});
						},
						ft = M(
							Object(n.useState)(
								window.Core.Tools.RubberStampCreateTool.FILL_COLORS,
							),
							2,
						),
						ht = ft[0],
						xt = ft[1],
						bt = M(
							Object(n.useState)(
								e.color ||
									window.Core.Tools.RubberStampCreateTool.FILL_COLORS[0],
							),
							2,
						),
						wt = bt[0],
						vt = bt[1],
						gt = M(Object(n.useState)(null), 2),
						yt = gt[0],
						St = gt[1],
						Ct = M(
							Object(n.useState)(
								window.Core.Tools.RubberStampCreateTool.TEXT_COLORS,
							),
							2,
						),
						kt = Ct[0],
						Et = Ct[1],
						Mt = M(
							Object(n.useState)(
								window.Core.Tools.RubberStampCreateTool.TEXT_COLORS[0],
							),
							2,
						),
						Ot = Mt[0],
						At = Mt[1],
						_t = M(Object(n.useState)(null), 2),
						Tt = _t[0],
						jt = _t[1],
						Lt = function (t) {
							At(t), (e = k(k({}, e), {}, { textColor: t })), lt(W, K, e);
						},
						Dt = function (t) {
							vt(t), (e = k(k({}, e), {}, { color: t })), lt(W, K, e);
						},
						Nt = v || [A],
						It = Array.from(
							new Set(
								Nt.map(function (t) {
									return _(t, B, z);
								}),
							),
						).filter(function (t) {
							return "" !== t;
						});
					return a.a.createElement(
						"div",
						{ className: "text-customstamp" },
						a.a.createElement(
							"div",
							{ className: "canvas-container", ref: it },
							a.a.createElement("canvas", {
								className: "custom-stamp-canvas",
								ref: rt,
								role: "img",
								"aria-label": ""
									.concat(V("option.customStampModal.previewCustomStamp"), " ")
									.concat(W, ", ")
									.concat(et, " ")
									.concat(ot),
							}),
						),
						a.a.createElement(
							"div",
							{ className: "scroll-container" },
							a.a.createElement(
								"div",
								{ className: "stamp-input-container" },
								a.a.createElement(
									"label",
									{ htmlFor: "stampTextInput", className: "stamp-label" },
									" ",
									pt,
									"*",
								),
								a.a.createElement("input", {
									id: "stampTextInput",
									className: i()("text-customstamp-input", { error: !W }),
									ref: ct,
									type: "text",
									"aria-label": pt,
									value: W,
									onChange: function (t) {
										var e = t.target.value || "";
										Y(e), r(!e), lt(e, K);
									},
								}),
								!W &&
									a.a.createElement(h.a, {
										glyph: "icon-alert",
										className: "error-icon",
										role: "presentation",
									}),
								a.a.createElement(
									"div",
									{ className: "empty-stamp-input", "aria-live": "assertive" },
									!W &&
										a.a.createElement(
											"p",
											{ className: "no-margin" },
											V("message.emptyCustomStampInput"),
										),
								),
							),
							a.a.createElement(
								"div",
								{ className: "font-container" },
								a.a.createElement(
									"div",
									{
										className: "stamp-sublabel",
										id: "custom-stamp-font-family-label",
									},
									" ",
									V("option.customStampModal.fontStyle"),
									" ",
								),
								a.a.createElement(
									"div",
									{ className: "font-inner-container" },
									a.a.createElement(u.a, {
										id: "custom-stamp-font",
										labelledById: "custom-stamp-font-family-label",
										items: c,
										ariaLabel: V("option.customStampModal.fontStyle"),
										onClickItem: function (t) {
											lt(W, K, k(k({}, e), {}, { font: t }));
										},
										currentSelectionKey: e.font || c[0],
										getCustomItemStyle: function (t) {
											return { fontFamily: t };
										},
										maxHeight: 200,
									}),
									a.a.createElement(f.a, {
										dataElement: "stampTextBoldButton",
										onClick: function () {
											return st("bold");
										},
										img: "icon-menu-bold",
										title: "option.richText.bold",
										isActive: e.bold,
										ariaPressed: e.bold,
									}),
									a.a.createElement(f.a, {
										dataElement: "stampTextItalicButton",
										onClick: function () {
											return st("italic");
										},
										img: "icon-menu-italic",
										title: "option.richText.italic",
										isActive: e.italic,
										ariaPressed: e.italic,
									}),
									a.a.createElement(f.a, {
										dataElement: "stampTextUnderlineButton",
										onClick: function () {
											return st("underline");
										},
										img: "icon-menu-text-underline",
										title: "option.richText.underline",
										isActive: e.underline,
										ariaPressed: e.underline,
									}),
									a.a.createElement(f.a, {
										dataElement: "stampTextStrikeoutButton",
										onClick: function () {
											return st("strikeout");
										},
										img: "icon-tool-text-manipulation-strikethrough",
										title: "option.richText.strikeout",
										isActive: e.strikeout,
										ariaPressed: e.strikeout,
									}),
								),
							),
							a.a.createElement(
								"div",
								{ className: "color-container" },
								a.a.createElement(
									"div",
									{ id: "stamp-text-color-label", className: "stamp-sublabel" },
									V("option.customStampModal.textColor"),
								),
								a.a.createElement(
									"div",
									{ className: "colorpalette-container" },
									a.a.createElement(x.a, {
										getHexColor: mt,
										color: Ot,
										setColorToBeDeleted: jt,
										colorToBeDeleted: Tt,
										customColors: kt,
										onStyleChange: At,
										handleColorOnClick: Lt,
										handleOnClick: Lt,
										openColorPicker: function (t) {
											return dt(0, "text");
										},
										openDeleteModal: function () {
											return ut("text");
										},
										ariaLabelledBy: "stamp-text-color-label",
										toolTipXOffset: -7,
										disableTitle: !0,
										enableEdit: !0,
										colorsAreHex: !0,
									}),
								),
							),
							a.a.createElement(
								"div",
								{ className: "color-container" },
								a.a.createElement(
									"div",
									{
										id: "stamp-background-color-label",
										className: "stamp-sublabel",
									},
									V("option.customStampModal.backgroundColor"),
								),
								a.a.createElement(
									"div",
									{ className: "colorpalette-container" },
									a.a.createElement(x.a, {
										getHexColor: mt,
										color: wt,
										setColorToBeDeleted: St,
										colorToBeDeleted: yt,
										customColors: ht,
										onStyleChange: vt,
										handleColorOnClick: Dt,
										handleOnClick: Dt,
										openColorPicker: function (t) {
											return dt(0, "fill");
										},
										openDeleteModal: function () {
											return ut("fill");
										},
										ariaLabelledBy: "stamp-background-color-label",
										toolTipXOffset: -7,
										disableTitle: !0,
										enableEdit: !0,
										colorsAreHex: !0,
									}),
								),
							),
							a.a.createElement(
								"div",
								{ className: "timestamp-container" },
								a.a.createElement(
									"div",
									{ id: "timestamp-label", className: "stamp-sublabel" },
									V("option.customStampModal.timestampText"),
								),
								a.a.createElement(
									"div",
									{
										className: "timeStamp-choice",
										role: "group",
										"aria-labelledby": "timestamp-label",
									},
									a.a.createElement(d.a, {
										id: "default-username",
										checked: I,
										onChange: function () {
											P(!I);
											var t = O(!I, B, L);
											$(t), lt(W, t);
										},
										label: V("option.customStampModal.Username"),
									}),
									a.a.createElement(d.a, {
										id: "default-date",
										checked: B,
										onChange: function () {
											H(!B);
											var t = _(T, !B, z);
											D(t);
											var e = O(I, !B || z, t);
											$(e), lt(W, e);
										},
										label: V("option.customStampModal.Date"),
									}),
									a.a.createElement(d.a, {
										id: "default-time",
										checked: z,
										onChange: function () {
											U(!z);
											var t = _(T, B, !z);
											D(t);
											var e = O(I, B || !z, t);
											$(e), lt(W, e);
										},
										label: V("option.customStampModal.Time"),
									}),
								),
							),
							(B || z) &&
								a.a.createElement(
									"div",
									{ className: "date-format-container" },
									a.a.createElement(
										"div",
										{
											className: "stamp-sublabel",
											id: "custom-stamp-date-format-label",
										},
										V("option.customStampModal.dateFormat"),
									),
									a.a.createElement(
										"button",
										{
											className: "hover-icon",
											ref: tt,
											onClick: at,
											"aria-label": "".concat(
												V("option.customStampModal.dateToolTipLabel"),
											),
											type: "button",
											tabIndex: "0",
											onKeyDown: function (t) {
												("Enter" !== t.key && " " !== t.key) ||
													(t.preventDefault(), at());
											},
											style: {
												background: "none",
												border: "none",
												padding: 0,
												display: "flex",
												alignItems: "flex-start",
												cursor: "pointer",
											},
										},
										a.a.createElement(h.a, { glyph: "icon-info" }),
										Q &&
											a.a.createElement(
												"div",
												{ className: "date-format-description" },
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"M = ",
													V("option.customStampModal.month"),
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"D = ",
													V("option.customStampModal.day"),
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"Y = ",
													V("option.customStampModal.year"),
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"H = ",
													V("option.customStampModal.hour"),
													" (24hr)",
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"h = ",
													V("option.customStampModal.hour"),
													" (12hr)",
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"m = ",
													V("option.customStampModal.minute"),
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"s = ",
													V("option.customStampModal.second"),
												),
												a.a.createElement(
													"div",
													{ className: "date-format-cell" },
													"A = AM/PM",
												),
											),
									),
									a.a.createElement(u.a, {
										id: "custom-stamp-date-format-dropdown",
										labelledById: "custom-stamp-date-format-label",
										items: It,
										ariaLabel: ""
											.concat(V("option.customStampModal.dateFormat"), " - ")
											.concat(L),
										currentSelectionKey: L,
										onClickItem: function (t) {
											D(t);
											var e = O(I, B || z, t);
											$(e), lt(W, e);
										},
										maxHeight: 200,
									}),
								),
						),
					);
				};
			j.propTypes = T;
			var L = j,
				D = o(5),
				N = o(351),
				I = o(173);
			o(1898);
			function P(t) {
				return (P =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (t) {
								return typeof t;
							}
						: function (t) {
								return t &&
									"function" == typeof Symbol &&
									t.constructor === Symbol &&
									t !== Symbol.prototype
									? "symbol"
									: typeof t;
							})(t);
			}
			function R(t, e) {
				var o =
					("undefined" != typeof Symbol && t[Symbol.iterator]) ||
					t["@@iterator"];
				if (!o) {
					if (
						Array.isArray(t) ||
						(o = U(t)) ||
						(e && t && "number" == typeof t.length)
					) {
						o && (t = o);
						var n = 0,
							a = function () {};
						return {
							s: a,
							n: function () {
								return n >= t.length
									? { done: !0 }
									: { done: !1, value: t[n++] };
							},
							e: function (t) {
								throw t;
							},
							f: a,
						};
					}
					throw new TypeError(
						"Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
					);
				}
				var r,
					i = !0,
					c = !1;
				return {
					s: function () {
						o = o.call(t);
					},
					n: function () {
						var t = o.next();
						return (i = t.done), t;
					},
					e: function (t) {
						(c = !0), (r = t);
					},
					f: function () {
						try {
							i || null == o.return || o.return();
						} finally {
							if (c) throw r;
						}
					},
				};
			}
			function B() {
				/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ B =
					function () {
						return t;
					};
				var t = {},
					e = Object.prototype,
					o = e.hasOwnProperty,
					n =
						Object.defineProperty ||
						function (t, e, o) {
							t[e] = o.value;
						},
					a = "function" == typeof Symbol ? Symbol : {},
					r = a.iterator || "@@iterator",
					i = a.asyncIterator || "@@asyncIterator",
					c = a.toStringTag || "@@toStringTag";
				function l(t, e, o) {
					return (
						Object.defineProperty(t, e, {
							value: o,
							enumerable: !0,
							configurable: !0,
							writable: !0,
						}),
						t[e]
					);
				}
				try {
					l({}, "");
				} catch (t) {
					l = function (t, e, o) {
						return (t[e] = o);
					};
				}
				function s(t, e, o, a) {
					var r = e && e.prototype instanceof d ? e : d,
						i = Object.create(r.prototype),
						c = new E(a || []);
					return n(i, "_invoke", { value: y(t, o, c) }), i;
				}
				function p(t, e, o) {
					try {
						return { type: "normal", arg: t.call(e, o) };
					} catch (t) {
						return { type: "throw", arg: t };
					}
				}
				t.wrap = s;
				var m = {};
				function d() {}
				function u() {}
				function f() {}
				var h = {};
				l(h, r, function () {
					return this;
				});
				var x = Object.getPrototypeOf,
					b = x && x(x(M([])));
				b && b !== e && o.call(b, r) && (h = b);
				var w = (f.prototype = d.prototype = Object.create(h));
				function v(t) {
					["next", "throw", "return"].forEach(function (e) {
						l(t, e, function (t) {
							return this._invoke(e, t);
						});
					});
				}
				function g(t, e) {
					var a;
					n(this, "_invoke", {
						value: function (n, r) {
							function i() {
								return new e(function (a, i) {
									!(function n(a, r, i, c) {
										var l = p(t[a], t, r);
										if ("throw" !== l.type) {
											var s = l.arg,
												m = s.value;
											return m && "object" == P(m) && o.call(m, "__await")
												? e.resolve(m.__await).then(
														function (t) {
															n("next", t, i, c);
														},
														function (t) {
															n("throw", t, i, c);
														},
													)
												: e.resolve(m).then(
														function (t) {
															(s.value = t), i(s);
														},
														function (t) {
															return n("throw", t, i, c);
														},
													);
										}
										c(l.arg);
									})(n, r, a, i);
								});
							}
							return (a = a ? a.then(i, i) : i());
						},
					});
				}
				function y(t, e, o) {
					var n = "suspendedStart";
					return function (a, r) {
						if ("executing" === n)
							throw new Error("Generator is already running");
						if ("completed" === n) {
							if ("throw" === a) throw r;
							return O();
						}
						for (o.method = a, o.arg = r; ; ) {
							var i = o.delegate;
							if (i) {
								var c = S(i, o);
								if (c) {
									if (c === m) continue;
									return c;
								}
							}
							if ("next" === o.method) o.sent = o._sent = o.arg;
							else if ("throw" === o.method) {
								if ("suspendedStart" === n) throw ((n = "completed"), o.arg);
								o.dispatchException(o.arg);
							} else "return" === o.method && o.abrupt("return", o.arg);
							n = "executing";
							var l = p(t, e, o);
							if ("normal" === l.type) {
								if (
									((n = o.done ? "completed" : "suspendedYield"), l.arg === m)
								)
									continue;
								return { value: l.arg, done: o.done };
							}
							"throw" === l.type &&
								((n = "completed"), (o.method = "throw"), (o.arg = l.arg));
						}
					};
				}
				function S(t, e) {
					var o = e.method,
						n = t.iterator[o];
					if (void 0 === n)
						return (
							(e.delegate = null),
							("throw" === o &&
								t.iterator.return &&
								((e.method = "return"),
								(e.arg = void 0),
								S(t, e),
								"throw" === e.method)) ||
								("return" !== o &&
									((e.method = "throw"),
									(e.arg = new TypeError(
										"The iterator does not provide a '" + o + "' method",
									)))),
							m
						);
					var a = p(n, t.iterator, e.arg);
					if ("throw" === a.type)
						return (
							(e.method = "throw"), (e.arg = a.arg), (e.delegate = null), m
						);
					var r = a.arg;
					return r
						? r.done
							? ((e[t.resultName] = r.value),
								(e.next = t.nextLoc),
								"return" !== e.method &&
									((e.method = "next"), (e.arg = void 0)),
								(e.delegate = null),
								m)
							: r
						: ((e.method = "throw"),
							(e.arg = new TypeError("iterator result is not an object")),
							(e.delegate = null),
							m);
				}
				function C(t) {
					var e = { tryLoc: t[0] };
					1 in t && (e.catchLoc = t[1]),
						2 in t && ((e.finallyLoc = t[2]), (e.afterLoc = t[3])),
						this.tryEntries.push(e);
				}
				function k(t) {
					var e = t.completion || {};
					(e.type = "normal"), delete e.arg, (t.completion = e);
				}
				function E(t) {
					(this.tryEntries = [{ tryLoc: "root" }]),
						t.forEach(C, this),
						this.reset(!0);
				}
				function M(t) {
					if (t) {
						var e = t[r];
						if (e) return e.call(t);
						if ("function" == typeof t.next) return t;
						if (!isNaN(t.length)) {
							var n = -1,
								a = function e() {
									for (; ++n < t.length; )
										if (o.call(t, n)) return (e.value = t[n]), (e.done = !1), e;
									return (e.value = void 0), (e.done = !0), e;
								};
							return (a.next = a);
						}
					}
					return { next: O };
				}
				function O() {
					return { value: void 0, done: !0 };
				}
				return (
					(u.prototype = f),
					n(w, "constructor", { value: f, configurable: !0 }),
					n(f, "constructor", { value: u, configurable: !0 }),
					(u.displayName = l(f, c, "GeneratorFunction")),
					(t.isGeneratorFunction = function (t) {
						var e = "function" == typeof t && t.constructor;
						return (
							!!e &&
							(e === u || "GeneratorFunction" === (e.displayName || e.name))
						);
					}),
					(t.mark = function (t) {
						return (
							Object.setPrototypeOf
								? Object.setPrototypeOf(t, f)
								: ((t.__proto__ = f), l(t, c, "GeneratorFunction")),
							(t.prototype = Object.create(w)),
							t
						);
					}),
					(t.awrap = function (t) {
						return { __await: t };
					}),
					v(g.prototype),
					l(g.prototype, i, function () {
						return this;
					}),
					(t.AsyncIterator = g),
					(t.async = function (e, o, n, a, r) {
						void 0 === r && (r = Promise);
						var i = new g(s(e, o, n, a), r);
						return t.isGeneratorFunction(o)
							? i
							: i.next().then(function (t) {
									return t.done ? t.value : i.next();
								});
					}),
					v(w),
					l(w, c, "Generator"),
					l(w, r, function () {
						return this;
					}),
					l(w, "toString", function () {
						return "[object Generator]";
					}),
					(t.keys = function (t) {
						var e = Object(t),
							o = [];
						for (var n in e) o.push(n);
						return (
							o.reverse(),
							function t() {
								for (; o.length; ) {
									var n = o.pop();
									if (n in e) return (t.value = n), (t.done = !1), t;
								}
								return (t.done = !0), t;
							}
						);
					}),
					(t.values = M),
					(E.prototype = {
						constructor: E,
						reset: function (t) {
							if (
								((this.prev = 0),
								(this.next = 0),
								(this.sent = this._sent = void 0),
								(this.done = !1),
								(this.delegate = null),
								(this.method = "next"),
								(this.arg = void 0),
								this.tryEntries.forEach(k),
								!t)
							)
								for (var e in this)
									"t" === e.charAt(0) &&
										o.call(this, e) &&
										!isNaN(+e.slice(1)) &&
										(this[e] = void 0);
						},
						stop: function () {
							this.done = !0;
							var t = this.tryEntries[0].completion;
							if ("throw" === t.type) throw t.arg;
							return this.rval;
						},
						dispatchException: function (t) {
							if (this.done) throw t;
							var e = this;
							function n(o, n) {
								return (
									(i.type = "throw"),
									(i.arg = t),
									(e.next = o),
									n && ((e.method = "next"), (e.arg = void 0)),
									!!n
								);
							}
							for (var a = this.tryEntries.length - 1; a >= 0; --a) {
								var r = this.tryEntries[a],
									i = r.completion;
								if ("root" === r.tryLoc) return n("end");
								if (r.tryLoc <= this.prev) {
									var c = o.call(r, "catchLoc"),
										l = o.call(r, "finallyLoc");
									if (c && l) {
										if (this.prev < r.catchLoc) return n(r.catchLoc, !0);
										if (this.prev < r.finallyLoc) return n(r.finallyLoc);
									} else if (c) {
										if (this.prev < r.catchLoc) return n(r.catchLoc, !0);
									} else {
										if (!l)
											throw new Error("try statement without catch or finally");
										if (this.prev < r.finallyLoc) return n(r.finallyLoc);
									}
								}
							}
						},
						abrupt: function (t, e) {
							for (var n = this.tryEntries.length - 1; n >= 0; --n) {
								var a = this.tryEntries[n];
								if (
									a.tryLoc <= this.prev &&
									o.call(a, "finallyLoc") &&
									this.prev < a.finallyLoc
								) {
									var r = a;
									break;
								}
							}
							r &&
								("break" === t || "continue" === t) &&
								r.tryLoc <= e &&
								e <= r.finallyLoc &&
								(r = null);
							var i = r ? r.completion : {};
							return (
								(i.type = t),
								(i.arg = e),
								r
									? ((this.method = "next"), (this.next = r.finallyLoc), m)
									: this.complete(i)
							);
						},
						complete: function (t, e) {
							if ("throw" === t.type) throw t.arg;
							return (
								"break" === t.type || "continue" === t.type
									? (this.next = t.arg)
									: "return" === t.type
										? ((this.rval = this.arg = t.arg),
											(this.method = "return"),
											(this.next = "end"))
										: "normal" === t.type && e && (this.next = e),
								m
							);
						},
						finish: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.finallyLoc === t)
									return this.complete(o.completion, o.afterLoc), k(o), m;
							}
						},
						catch: function (t) {
							for (var e = this.tryEntries.length - 1; e >= 0; --e) {
								var o = this.tryEntries[e];
								if (o.tryLoc === t) {
									var n = o.completion;
									if ("throw" === n.type) {
										var a = n.arg;
										k(o);
									}
									return a;
								}
							}
							throw new Error("illegal catch attempt");
						},
						delegateYield: function (t, e, o) {
							return (
								(this.delegate = { iterator: M(t), resultName: e, nextLoc: o }),
								"next" === this.method && (this.arg = void 0),
								m
							);
						},
					}),
					t
				);
			}
			function H(t, e, o, n, a, r, i) {
				try {
					var c = t[r](i),
						l = c.value;
				} catch (t) {
					return void o(t);
				}
				c.done ? e(l) : Promise.resolve(l).then(n, a);
			}
			function F(t) {
				return function () {
					var e = this,
						o = arguments;
					return new Promise(function (n, a) {
						var r = t.apply(e, o);
						function i(t) {
							H(r, n, a, i, c, "next", t);
						}
						function c(t) {
							H(r, n, a, i, c, "throw", t);
						}
						i(void 0);
					});
				};
			}
			function z(t, e) {
				return (
					(function (t) {
						if (Array.isArray(t)) return t;
					})(t) ||
					(function (t, e) {
						var o =
							null == t
								? null
								: ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
									t["@@iterator"];
						if (null != o) {
							var n,
								a,
								r,
								i,
								c = [],
								l = !0,
								s = !1;
							try {
								if (((r = (o = o.call(t)).next), 0 === e)) {
									if (Object(o) !== o) return;
									l = !1;
								} else
									for (
										;
										!(l = (n = r.call(o)).done) &&
										(c.push(n.value), c.length !== e);
										l = !0
									);
							} catch (t) {
								(s = !0), (a = t);
							} finally {
								try {
									if (
										!l &&
										null != o.return &&
										((i = o.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (s) throw a;
								}
							}
							return c;
						}
					})(t, e) ||
					U(t, e) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function U(t, e) {
				if (t) {
					if ("string" == typeof t) return G(t, e);
					var o = Object.prototype.toString.call(t).slice(8, -1);
					return (
						"Object" === o && t.constructor && (o = t.constructor.name),
						"Map" === o || "Set" === o
							? Array.from(t)
							: "Arguments" === o ||
									/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)
								? G(t, e)
								: void 0
					);
				}
			}
			function G(t, e) {
				(null == e || e > t.length) && (e = t.length);
				for (var o = 0, n = new Array(e); o < e; o++) n[o] = t[o];
				return n;
			}
			var W = window.Core.Tools.RubberStampCreateTool.FILL_COLORS,
				Y = function () {
					var t = z(
							Object(n.useState)({ font: "Helvetica", bold: !0, color: W[0] }),
							2,
						),
						e = t[0],
						o = t[1],
						r = l.a.getToolsFromAllDocumentViewers(
							"AnnotationCreateRubberStamp",
						),
						d = z(Object(m.a)(), 1)[0],
						u = Object(c.f)(),
						h = z(Object(n.useState)(!1), 2),
						x = h[0],
						b = h[1],
						w = z(
							Object(c.e)(function (t) {
								return [
									p.a.isElementOpen(t, D.a.CUSTOM_STAMP_MODAL),
									p.a.getFonts(t),
									p.a.getDateTimeFormats(t),
									p.a.getUserName(t),
								];
							}),
							4,
						),
						v = w[0],
						g = w[1],
						y = w[2],
						S = w[3],
						C = Object(c.d)();
					Object(n.useEffect)(
						function () {
							v && l.a.deselectAllAnnotations();
						},
						[v],
					);
					var k = Object(I.a)(function () {
							C(s.a.closeElement(D.a.CUSTOM_STAMP_MODAL));
						}),
						E = (function () {
							var t = F(
								B().mark(function t(e) {
									var o, n, a, r;
									return B().wrap(function (t) {
										for (;;)
											switch ((t.prev = t.next)) {
												case 0:
													(o = d("warning.colorPicker.deleteMessage")),
														(n = d("warning.colorPicker.deleteTitle")),
														(a = d("action.ok")),
														(r = {
															message: o,
															title: n,
															confirmBtnText: a,
															onConfirm: e,
														}),
														C(s.a.showWarningMessage(r));
												case 5:
												case "end":
													return t.stop();
											}
									}, t);
								}),
							);
							return function (e) {
								return t.apply(this, arguments);
							};
						})(),
						M = i()({ Modal: !0, CustomStampModal: !0, open: v, closed: !v }),
						O = (function () {
							var t = F(
								B().mark(function t() {
									var o, n, a, i, c, p;
									return B().wrap(
										function (t) {
											for (;;)
												switch ((t.prev = t.next)) {
													case 0:
														l.a.setToolMode("AnnotationCreateRubberStamp"),
															(o = R(r)),
															(t.prev = 2),
															o.s();
													case 4:
														if ((n = o.n()).done) {
															t.next = 15;
															break;
														}
														return (
															(a = n.value).addCustomStamp(e),
															(t.next = 9),
															a.createCustomStampAnnotation(e)
														);
													case 9:
														return (
															(i = t.sent), (t.next = 12), a.setRubberStamp(i)
														);
													case 12:
														a.showPreview();
													case 13:
														t.next = 4;
														break;
													case 15:
														t.next = 20;
														break;
													case 17:
														(t.prev = 17), (t.t0 = t.catch(2)), o.e(t.t0);
													case 20:
														return (t.prev = 20), o.f(), t.finish(20);
													case 23:
														C(s.a.closeElement(D.a.CUSTOM_STAMP_MODAL)),
															(c = r[0].getStandardStamps().length),
															(p = r[0].getCustomStamps().length),
															C(s.a.setSelectedStampIndex(c + p - 1));
													case 27:
													case "end":
														return t.stop();
												}
										},
										t,
										null,
										[[2, 17, 20, 23]],
									);
								}),
							);
							return function () {
								return t.apply(this, arguments);
							};
						})(),
						A = Object(I.a)(function () {
							x || O();
						});
					return v
						? a.a.createElement(
								"div",
								{ className: M, "data-element": D.a.CUSTOM_STAMP_MODAL },
								a.a.createElement(
									N.a,
									{
										title: d("option.customStampModal.modalName"),
										closeHandler: k,
										onCloseClick: k,
										isOpen: v,
										swipeToClose: !0,
									},
									a.a.createElement(
										"div",
										{
											className: "container",
											onMouseDown: function (t) {
												return t.stopPropagation();
											},
										},
										a.a.createElement(L, {
											openDeleteModal: E,
											getCustomColorAndRemove: function () {
												var t = p.a.getCustomColor(u.getState());
												return C(s.a.setCustomColor(null)), t;
											},
											openColorPicker: function () {
												C(s.a.openElement("ColorPickerModal"));
											},
											isModalOpen: v,
											state: e,
											setState: o,
											closeModal: k,
											setEmptyInput: b,
											fonts: g,
											dateTimeFormats: y,
											stampTool: r[0],
											userName: S,
										}),
										a.a.createElement(
											"div",
											{ className: "footer" },
											a.a.createElement(f.a, {
												label: d("action.create"),
												title: d("action.create"),
												onClick: A,
												disabled: x,
												className: "stamp-create",
											}),
										),
									),
								),
							)
						: null;
				};
			e.default = Y;
		},
	},
]);
//# sourceMappingURL=chunk.50.js.map
