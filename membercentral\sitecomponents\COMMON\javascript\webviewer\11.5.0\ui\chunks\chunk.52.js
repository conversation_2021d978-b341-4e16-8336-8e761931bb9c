(window.webpackJsonp = window.webpackJsonp || []).push([
	[52],
	{
		1992: function (e, t, n) {
			var r = n(30),
				o = n(1993);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[e.i, o, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const r = [];
								return (
									n.querySelectorAll(t).forEach((e) => r.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...e(t, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < t.length; r++) {
						const o = t[r];
						if (0 === r)
							o.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							o.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			e.exports = o.locals || {};
		},
		1993: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".sheet-tab{cursor:pointer;border-bottom:none;border-top-right-radius:4px;border-top-left-radius:4px;min-width:170px;height:40px;position:relative;border-right:1px solid transparent}.sheet-tab:hover{background:var(--faded-component-background)}.sheet-tab.no-left-border .tab-body{border-right:1px solid transparent}.sheet-tab .tab-body{height:24px;display:flex;padding:0 8px;width:100%;position:absolute;top:50%;transform:translateY(-50%);border-right:1px solid var(--border)}.sheet-tab .tab-body.input-mode{padding-left:5px;padding-right:6px}.sheet-tab .tab-body .sheet-label{width:calc(100% - 24px);text-align:left;justify-content:left;height:24px}.sheet-tab .tab-body .sheet-label span{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.sheet-tab .tab-body .sheet-input{max-width:100%}.sheet-tab .tab-body input.input-error{border-color:red}.sheet-tab.active{border:1px solid var(--border);border-bottom:none;background:var(--gray-0)}.sheet-tab.active .tab-body{border-right:1px solid transparent}.sheet-tab .sheet-options .ToggleElementButton button{width:24px;height:24px;min-width:24px}.sheet-tab .sheet-options .ToggleElementButton button .Icon{width:16px;height:14px}.sheet-tab .sheet-options .more-options-icon{height:24px;width:24px}.sheet-tab .sheet-options .more-options-icon .Icon{width:16px;height:16px}",
				"",
			]);
		},
		1994: function (e, t, n) {
			var r = n(30),
				o = n(1995);
			"string" == typeof (o = o.__esModule ? o.default : o) &&
				(o = [[e.i, o, ""]]);
			var a = {
				insert: function (e) {
					if (!window.isApryseWebViewerWebComponent)
						return void document.head.appendChild(e);
					let t;
					(t = document.getElementsByTagName("apryse-webviewer")),
						t.length ||
							(t = (function e(t, n = document) {
								const r = [];
								return (
									n.querySelectorAll(t).forEach((e) => r.push(e)),
									n.querySelectorAll("*").forEach((n) => {
										n.shadowRoot && r.push(...e(t, n.shadowRoot));
									}),
									r
								);
							})("apryse-webviewer"));
					const n = [];
					for (let r = 0; r < t.length; r++) {
						const o = t[r];
						if (0 === r)
							o.shadowRoot.appendChild(e),
								(e.onload = function () {
									n.length > 0 &&
										n.forEach((t) => {
											t.innerHTML = e.innerHTML;
										});
								});
						else {
							const t = e.cloneNode(!0);
							o.shadowRoot.appendChild(t), n.push(t);
						}
					}
				},
				singleton: !1,
			};
			r(o, a);
			e.exports = o.locals || {};
		},
		1995: function (e, t, n) {
			(e.exports = n(31)(!1)).push([
				e.i,
				".Flyout[\\:has\\(.SheetOptionsFlyout\\)]{z-index:130}.Flyout:has(.SheetOptionsFlyout){z-index:130}.SpreadsheetSwitcher{padding:0}.GenericFileTab{display:flex;padding-left:4px;padding-top:4px}.GenericFileTab .dropdown-menu{border:1px solid var(--border);margin-top:3px;margin-left:5px}.GenericFileTab .dropdown-menu .Icon{position:absolute}.GenericFileTab .add-sheet-tab{margin-top:3px;margin-left:5px}.GenericFileTab .add-sheet-tab .Icon{width:14px;height:14px}",
				"",
			]);
		},
		2022: function (e, t, n) {
			"use strict";
			n.r(t);
			n(15),
				n(36),
				n(19),
				n(12),
				n(13),
				n(8),
				n(14),
				n(10),
				n(9),
				n(11),
				n(16),
				n(20),
				n(18),
				n(27),
				n(28),
				n(25),
				n(22),
				n(32),
				n(29),
				n(47),
				n(23),
				n(24),
				n(49),
				n(48);
			var r = n(0),
				o = n.n(r),
				a = n(1),
				i = (n(41), n(102), n(101), n(112), n(6)),
				l = n(347),
				c = n(534),
				u = n(42),
				s = n(94),
				d = n(43),
				f = n(5),
				b = n(100),
				h = n(4),
				p = n(2),
				m = n(3),
				g = n.n(m),
				y = function (e) {
					var t = e.id,
						n = void 0 === t ? "" : t,
						o = e.additionalTabs,
						a = e.tabsForReference,
						l = e.onClick,
						c = e.activeItem,
						u = Object(i.d)(),
						s = Object(i.e)(function (e) {
							return h.a.getFlyout(e, n);
						});
					return (
						Object(r.useLayoutEffect)(
							function () {
								var e = {
									dataElement: n,
									className: "AdditionalTabsFlyout",
									items: o.map(function (e) {
										var t = e.name;
										return {
											label: t,
											title: t,
											option: t,
											disabled: e.disabled,
											isActive: t === c,
											dataElement: Symbol(t).toString(),
											onClick: function () {
												return l(t, e.sheetIndex);
											},
										};
									}),
								};
								u(s ? p.a.updateFlyout(e.dataElement, e) : p.a.addFlyout(e));
							},
							[a, c],
						),
						null
					);
				};
			y.propTypes = {
				id: g.a.string,
				additionalTabs: g.a.arrayOf(
					g.a.shape({
						name: g.a.string,
						sheetIndex: g.a.number,
						disabled: g.a.bool,
					}),
				),
				tabsForReference: g.a.array,
				onClick: g.a.func,
				activeItem: g.a.string,
			};
			var v = y,
				S = (n(154), n(109), n(17)),
				w = n.n(S);
			function O(e) {
				return (O =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function E(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var r = Object.getOwnPropertySymbols(e);
					t &&
						(r = r.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, r);
				}
				return n;
			}
			function j(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? E(Object(n), !0).forEach(function (t) {
								x(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: E(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function x(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== O(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var r = n.call(e, t || "default");
								if ("object" !== O(r)) return r;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === O(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			var T = function (e, t) {
					return {
						label: "action.".concat(e.toLowerCase()),
						title: "action.".concat(e.toLowerCase()),
						option: e,
						dataElement: t,
					};
				},
				A = [
					T("Rename", "sheetTabRenameOption"),
					T("Delete", "sheetTabDeleteOption"),
				],
				C = function (e) {
					var t = e.sheetId,
						n = e.handleClick,
						o = e.sheetCount,
						a = e.disabled,
						l = Object(i.d)(),
						c = "".concat(f.a.SHEET_TAB_OPTIONS_FLYOUT, "-").concat(t),
						u = Object(i.e)(function (e) {
							return h.a.getFlyout(e, c);
						});
					return (
						Object(r.useLayoutEffect)(
							function () {
								var e = {
									dataElement: c,
									className: "TabOptionsFlyout",
									items: A.map(function (e) {
										var t = ("Delete" === e.option && 1 === o) || a;
										return j(
											j({}, e),
											{},
											{
												onClick: function () {
													return n(e.option);
												},
												disabled: t,
											},
										);
									}),
								};
								l(u ? p.a.updateFlyout(e.dataElement, e) : p.a.addFlyout(e));
							},
							[o, a, n],
						),
						null
					);
				};
			C.propTypes = {
				sheetId: g.a.string,
				handleClick: g.a.func,
				sheetCount: g.a.number,
				disabled: g.a.bool,
			};
			var k = C;
			function I(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (o = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return D(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return D(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function D(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var N = function (e) {
				var t = e.handleClick,
					n = void 0 === t ? function () {} : t,
					r = e.id,
					a = e.onToggle,
					i = e.label,
					c = e.disabled,
					u = e.sheetCount,
					d = I(Object(l.a)(), 1)[0];
				return o.a.createElement(
					o.a.Fragment,
					null,
					o.a.createElement(s.a, {
						dataElement: "options-".concat(r),
						title: ""
							.concat(d("option.searchPanel.moreOptions"), " ")
							.concat(i),
						img: "icon-tools-more-vertical",
						onToggle: function (e) {
							e && a(i);
						},
						disabled: c,
						toggleElement: ""
							.concat(f.a.SHEET_TAB_OPTIONS_FLYOUT, "-")
							.concat(r),
					}),
					o.a.createElement(k, {
						sheetId: r,
						sheetCount: u,
						handleClick: function (e) {
							return n(r, i, e);
						},
						disabled: c,
					}),
				);
			};
			N.propTypes = {
				id: g.a.string,
				label: g.a.string,
				onToggle: g.a.func,
				handleClick: g.a.func,
				disabled: g.a.bool,
				sheetCount: g.a.number,
			};
			var R = N;
			n(1992);
			function M(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (o = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return P(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return P(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function P(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var F = {
					sheet: g.a.any.isRequired,
					sheetCount: g.a.number,
					activeSheetLabel: g.a.string.isRequired,
					setLabelBeingEdited: g.a.func.isRequired,
					setActiveSheet: g.a.func.isRequired,
					renameSheet: g.a.func.isRequired,
					deleteSheet: g.a.func.isRequired,
					onClick: g.a.func.isRequired,
					isEditMode: g.a.bool.isRequired,
					validateName: g.a.func,
					noRightBorder: g.a.bool,
					isReadOnlyMode: g.a.bool,
					skipDeleteWarning: g.a.bool,
				},
				L = function (e) {
					var t = e.sheet,
						n = e.activeSheetLabel,
						a = e.onClick,
						c = e.sheetCount,
						s = e.setLabelBeingEdited,
						d = e.setActiveSheet,
						f = e.isEditMode,
						b = e.noRightBorder,
						h = e.validateName,
						m = e.isReadOnlyMode,
						g = e.deleteSheet,
						y = e.renameSheet,
						v = e.skipDeleteWarning,
						S = t.sheetIndex,
						O = t.name,
						E = t.disabled,
						j = O === n,
						x = M(Object(r.useState)(""), 2),
						T = x[0],
						A = x[1],
						C = M(Object(r.useState)(!1), 2),
						k = C[0],
						I = C[1],
						D = Object(l.a)().t,
						N = Object(r.useRef)(),
						P = Object(i.d)();
					Object(r.useEffect)(
						function () {
							T && N.current && N.current.focus();
						},
						[T],
					);
					var F = function () {
							if (k)
								return (
									(t = {
										message: D(
											"".concat(
												(e =
													"" === T
														? "warning.sheetTabRenameIssueTwo"
														: "warning.sheetTabRenameIssueOne"),
												".message",
											),
										),
										title: D("".concat(e, ".title")),
										confirmBtnText: D("action.ok"),
										onConfirm: function () {
											return N.current.focus();
										},
										onCancel: function () {
											s(!1), I(!1), A(O);
										},
									}),
									void P(p.a.showWarningMessage(t))
								);
							var e, t;
							y(O, T), s(null), I(!1), A("");
						},
						L = null;
					return (
						(L = f
							? o.a.createElement("input", {
									type: "text",
									className: w()("sheet-input", { "input-error": k }),
									ref: N,
									value: T,
									onChange: function (e) {
										var t = e.target.value,
											n = h(O, t),
											r = "" === t.trim();
										I(n || r), A(t);
									},
									onBlur: F,
									onKeyDown: function (e) {
										"Enter" !== e.key || k || F();
									},
									"aria-invalid": k,
								})
							: o.a.createElement(
									o.a.Fragment,
									null,
									o.a.createElement(u.a, {
										role: "tab",
										ariaControls: "document-container",
										ariaSelected: j,
										ariaLabel: O,
										className: "sheet-label",
										onClick: function (e) {
											return a(e, O, S);
										},
										title: O,
										label: O,
										useI18String: !1,
										disabled: E,
									}),
									o.a.createElement(
										"div",
										{ className: "sheet-options" },
										o.a.createElement(R, {
											id:
												null == O
													? void 0
													: O.replace(/\s+/g, "-").toLowerCase(),
											label: O,
											sheetCount: c,
											onToggle: function (e) {
												return (t = e), (n = S), s(null), I(!1), void d(t, n);
												var t, n;
											},
											handleClick: function (e, t, n) {
												if ("Rename" === n) A((o = t)), I(!1), s(o);
												else if ("Delete" === n) {
													if (v) return g(O);
													var r = {
														message: D("warning.sheetTabDeleteMessage.message"),
														title: D("warning.sheetTabDeleteMessage.title"),
														confirmBtnText: D("action.ok"),
														secondaryBtnText: D("action.cancel"),
														onConfirm: function () {
															g(O);
														},
													};
													P(p.a.showWarningMessage(r));
												}
												var o;
											},
											disabled: m,
										}),
									),
								)),
						o.a.createElement(
							"div",
							{
								className: w()(
									{ active: j, "no-left-border": b, disabled: E },
									"sheet-tab",
								),
							},
							o.a.createElement(
								"div",
								{ className: w()({ "input-mode": f }, "tab-body") },
								L,
							),
						)
					);
				};
			L.propTypes = F;
			var B = o.a.memo(L);
			n(1994);
			function W(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (o = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return _(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return _(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function _(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var q = function (e) {
				var t,
					n = e.tabs,
					a = void 0 === n ? [] : n,
					p = e.activeSheetIndex,
					m = void 0 === p ? 0 : p,
					g = e.setActiveSheet,
					y = e.createNewSheet,
					S = e.deleteSheet,
					w = e.renameSheet,
					O = e.validateName,
					E = e.skipDeleteWarning,
					j = void 0 !== E && E,
					x = (null === (t = a[m]) || void 0 === t ? void 0 : t.name) || "",
					T = Object(c.a)().width,
					A = Object(r.useMemo)(
						function () {
							return Math.floor((T - 80) / 170);
						},
						[T],
					),
					C = Object(l.a)().t,
					k = W(Object(r.useState)(""), 2),
					I = k[0],
					D = k[1],
					N = Object(i.e)(h.a.getSpreadsheetEditorEditMode) === b.d.VIEW_ONLY,
					R = function (e, t) {
						D(null), g(e, t);
					},
					M = function (e, t, n) {
						e.preventDefault(), e.stopPropagation(), R(t, n);
					},
					P = W(
						Object(r.useMemo)(
							function () {
								return [a.slice(0, A), a.slice(A)];
							},
							[a, A],
						),
						2,
					),
					F = P[0],
					L = P[1],
					_ = F.map(function (e) {
						return o.a.createElement(B, {
							key: e.sheetIndex,
							sheet: e,
							sheetCount: a.length,
							activeSheetLabel: x,
							onClick: M,
							isEditMode: I === e.name,
							setLabelBeingEdited: D,
							setActiveSheet: g,
							deleteSheet: S,
							renameSheet: w,
							noRightBorder:
								a[e.sheetIndex + 1] && a[e.sheetIndex + 1].name === x,
							validateName: O,
							isReadOnlyMode: N,
							skipDeleteWarning: j,
						});
					}),
					q = Object(r.useMemo)(
						function () {
							return L.some(function (e) {
								return e.name === x;
							});
						},
						[L, x],
					);
				return o.a.createElement(
					"div",
					{
						className:
							"SpreadsheetSwitcher ModularHeader BottomHeader stroke start",
					},
					o.a.createElement(
						"div",
						{ className: "GenericFileTab", role: "tablist" },
						_,
						(null == L ? void 0 : L.length) > 0
							? o.a.createElement(
									s.a,
									{
										className: "dropdown-menu tab-dropdown-button",
										dataElement: "tabTrigger",
										title: C("message.showMore"),
										toggleElement: f.a.ADDITIONAL_SPREADSHEET_TABS_MENU,
										label: L.length.toString(),
									},
									q &&
										o.a.createElement(d.a, { glyph: "icon-active-indicator" }),
								)
							: null,
						o.a.createElement(u.a, {
							className: "add-sheet-tab",
							title: "action.addSheet",
							img: "icon-menu-add",
							onClick: y,
							dataElement: "addTabButton",
							label: "",
							ariaLabel: C("action.addSheet"),
							disabled: N,
						}),
						(null == L ? void 0 : L.length) > 0 &&
							o.a.createElement(v, {
								id: f.a.ADDITIONAL_SPREADSHEET_TABS_MENU,
								additionalTabs: L,
								tabsForReference: a,
								onClick: R,
								activeItem: x,
							}),
					),
				);
			};
			q.propTypes = {
				tabs: g.a.arrayOf(
					g.a.shape({
						name: g.a.string,
						sheetIndex: g.a.number,
						disabled: g.a.bool,
					}),
				),
				activeSheetIndex: g.a.number,
				setActiveSheet: g.a.func,
				createNewSheet: g.a.func,
				deleteSheet: g.a.func,
				renameSheet: g.a.func,
				skipDeleteWarning: g.a.bool,
				validateName: g.a.func,
			};
			var H = q,
				U = n(186);
			function G(e) {
				return (G =
					"function" == typeof Symbol && "symbol" == typeof Symbol.iterator
						? function (e) {
								return typeof e;
							}
						: function (e) {
								return e &&
									"function" == typeof Symbol &&
									e.constructor === Symbol &&
									e !== Symbol.prototype
									? "symbol"
									: typeof e;
							})(e);
			}
			function V(e, t) {
				var n = Object.keys(e);
				if (Object.getOwnPropertySymbols) {
					var r = Object.getOwnPropertySymbols(e);
					t &&
						(r = r.filter(function (t) {
							return Object.getOwnPropertyDescriptor(e, t).enumerable;
						})),
						n.push.apply(n, r);
				}
				return n;
			}
			function Y(e) {
				for (var t = 1; t < arguments.length; t++) {
					var n = null != arguments[t] ? arguments[t] : {};
					t % 2
						? V(Object(n), !0).forEach(function (t) {
								$(e, t, n[t]);
							})
						: Object.getOwnPropertyDescriptors
							? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n))
							: V(Object(n)).forEach(function (t) {
									Object.defineProperty(
										e,
										t,
										Object.getOwnPropertyDescriptor(n, t),
									);
								});
				}
				return e;
			}
			function $(e, t, n) {
				return (
					(t = (function (e) {
						var t = (function (e, t) {
							if ("object" !== G(e) || null === e) return e;
							var n = e[Symbol.toPrimitive];
							if (void 0 !== n) {
								var r = n.call(e, t || "default");
								if ("object" !== G(r)) return r;
								throw new TypeError(
									"@@toPrimitive must return a primitive value.",
								);
							}
							return ("string" === t ? String : Number)(e);
						})(e, "string");
						return "symbol" === G(t) ? t : String(t);
					})(t)) in e
						? Object.defineProperty(e, t, {
								value: n,
								enumerable: !0,
								configurable: !0,
								writable: !0,
							})
						: (e[t] = n),
					e
				);
			}
			function z(e, t) {
				return (
					(function (e) {
						if (Array.isArray(e)) return e;
					})(e) ||
					(function (e, t) {
						var n =
							null == e
								? null
								: ("undefined" != typeof Symbol && e[Symbol.iterator]) ||
									e["@@iterator"];
						if (null != n) {
							var r,
								o,
								a,
								i,
								l = [],
								c = !0,
								u = !1;
							try {
								if (((a = (n = n.call(e)).next), 0 === t)) {
									if (Object(n) !== n) return;
									c = !1;
								} else
									for (
										;
										!(c = (r = a.call(n)).done) &&
										(l.push(r.value), l.length !== t);
										c = !0
									);
							} catch (e) {
								(u = !0), (o = e);
							} finally {
								try {
									if (
										!c &&
										null != n.return &&
										((i = n.return()), Object(i) !== i)
									)
										return;
								} finally {
									if (u) throw o;
								}
							}
							return l;
						}
					})(e, t) ||
					(function (e, t) {
						if (!e) return;
						if ("string" == typeof e) return J(e, t);
						var n = Object.prototype.toString.call(e).slice(8, -1);
						"Object" === n && e.constructor && (n = e.constructor.name);
						if ("Map" === n || "Set" === n) return Array.from(e);
						if (
							"Arguments" === n ||
							/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
						)
							return J(e, t);
					})(e, t) ||
					(function () {
						throw new TypeError(
							"Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.",
						);
					})()
				);
			}
			function J(e, t) {
				(null == t || t > e.length) && (t = e.length);
				for (var n = 0, r = new Array(t); n < t; n++) r[n] = e[n];
				return r;
			}
			var K = "SpreadsheetEditorDocument is not loaded",
				Q = 2;
			function X(e) {
				var t = Object(l.a)().t,
					n = z(Object(r.useState)([]), 2),
					i = n[0],
					c = n[1],
					u = z(Object(r.useState)(0), 2),
					s = u[0],
					d = u[1],
					f = function (e) {
						for (var t = e.sheetCount, n = [], r = 0; r < t; r++) {
							var o = e.getSheetAt(r);
							n.push({ name: o.name, sheetIndex: r });
						}
						return n;
					};
				Object(r.useEffect)(function () {
					var e = function (e) {
						var t = a.a
							.getDocumentViewer()
							.getDocument()
							.getSpreadsheetEditorDocument()
							.getWorkbook();
						c(f(t)), d(e.getSheetIndex());
					};
					return (
						a.a.addEventListener("activeSheetChanged", e),
						function () {
							a.a.removeEventListener("activeSheetChanged", e);
						}
					);
				}, []);
				var b = Object(r.useCallback)(function () {
					c([]), d(0);
				}, []);
				Object(U.a)(b);
				var h = Y(
					Y({}, e),
					{},
					{
						tabs: i,
						activeSheetIndex: s,
						setActiveSheet: function (e, t) {
							var n,
								r,
								o =
									null === (n = a.a.getDocument()) ||
									void 0 === n ||
									null === (r = n.getSpreadsheetEditorDocument()) ||
									void 0 === r
										? void 0
										: r.getWorkbook();
							if (!o) return console.error(K);
							o.getSheetAt(t) && (o.setActiveSheet(t), d(t));
						},
						createNewSheet: function () {
							var e,
								n,
								r =
									null === (e = a.a.getDocument()) ||
									void 0 === e ||
									null === (n = e.getSpreadsheetEditorDocument()) ||
									void 0 === n
										? void 0
										: n.getWorkbook();
							if (!r) return console.error(K);
							for (
								var o = t("spreadsheetEditor.blankSheet"), i = !r.getSheet(o);
								!i;
							)
								(o = ""
									.concat(t("spreadsheetEditor.blankSheet"), " ")
									.concat(Q++)),
									(i = !r.getSheet(o));
							r.createSheet(o);
						},
						deleteSheet: function (e) {
							var t,
								n,
								r =
									null === (t = a.a.getDocument()) ||
									void 0 === t ||
									null === (n = t.getSpreadsheetEditorDocument()) ||
									void 0 === n
										? void 0
										: n.getWorkbook();
							if (!r) return console.error(K);
							r.getSheet(e) && r.removeSheet(e), c(f(r));
						},
						renameSheet: function (e, t) {
							var n,
								r,
								o =
									null === (n = a.a.getDocument()) ||
									void 0 === n ||
									null === (r = n.getSpreadsheetEditorDocument()) ||
									void 0 === r
										? void 0
										: r.getWorkbook();
							if (!o) return console.error(K);
							var i = o.getSheet(e);
							i && (i.name = t), c(f(o));
						},
						validateName: function (e, t) {
							var n,
								r,
								o =
									null === (n = a.a.getDocument()) ||
									void 0 === n ||
									null === (r = n.getSpreadsheetEditorDocument()) ||
									void 0 === r
										? void 0
										: r.getWorkbook();
							return o ? e !== t && !!o.getSheet(t) : (console.error(K), !1);
						},
					},
				);
				return o.a.createElement(H, h);
			}
			X.propTypes = {};
			var Z = X;
			t.default = Z;
		},
	},
]);
//# sourceMappingURL=chunk.52.js.map
