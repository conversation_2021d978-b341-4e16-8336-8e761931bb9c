/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function () {
	(window.wpCoreControlsBundle = window.wpCoreControlsBundle || []).push([
		[1],
		{
			628: function (ya, va, r) {
				r.r(va);
				var la = r(0),
					na = r(366);
				ya = r(624);
				r = r(547);
				var ma = window,
					ja = (function (ia) {
						function ca(y, x) {
							var n = ia.call(this, y, x) || this;
							n.url = y;
							n.range = x;
							n.request = new XMLHttpRequest();
							n.request.open("GET", n.url, !0);
							ma.Uint8Array && (n.request.responseType = "arraybuffer");
							n.request.setRequestHeader("X-Requested-With", "XMLHttpRequest");
							n.status = na.a.NOT_STARTED;
							return n;
						}
						Object(la.c)(ca, ia);
						return ca;
					})(ya.ByteRangeRequest);
				ya = (function (ia) {
					function ca(y, x, n, h) {
						y = ia.call(this, y, x, n, h) || this;
						y.CG = ja;
						return y;
					}
					Object(la.c)(ca, ia);
					ca.prototype.DD = function (y, x) {
						return ""
							.concat(y, "/bytes=")
							.concat(x.start, ",")
							.concat(x.stop ? x.stop : "");
					};
					return ca;
				})(ya["default"]);
				Object(r.a)(ya);
				Object(r.b)(ya);
				va["default"] = ya;
			},
		},
	]);
}).call(this || window);
