ALTER PROC dbo.cf_auditLogAddFieldValue
@valueID int,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @siteID int, @orgID int, @usageID int, @controllingSiteResourceID int,@controllingResourceType varchar(50),
		@areaName varchar(20), @resourceType varchar(50), @fieldText varchar(max), @valueString varchar(max),
		@msgjson varchar(max), @crlf varchar(10);

	SET @crlf = char(13) + char(10);

	IF @enteredByMemberID IS NULL
		SELECT @enteredByMemberID = dbo.fn_ams_getMCSystemMemberID();

	SELECT @siteID = s.siteID, @orgID = s.orgID, @controllingSiteResourceID = f.controllingSiteResourceID,
		@controllingResourceType = rt.resourceType, @usageID = f.usageID, @fieldText = f.fieldText,
		@valueString = v.valueString
	FROM dbo.cf_fieldValues AS v
	INNER JOIN dbo.cf_fields AS f ON f.fieldID = v.fieldID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = f.controllingSiteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = sr.resourceTypeID
	INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
	WHERE v.valueID = @valueID;

	IF @usageID IS NULL
		GOTO on_done;

	SELECT @areaName = fu.areaName, @resourceType = rt.resourceType
	FROM dbo.cf_fieldUsages AS fu
	INNER JOIN dbo.cms_siteResourceTypes AS rt ON rt.resourceTypeID = fu.siteResourceTypeID
	WHERE fu.usageID = @usageID;

	IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
		IF @controllingResourceType = 'ReferralPanel'
			SELECT @msgjson = 'New Answer [' + @valueString + '] has been added to the Subpanel Question within Panel [' + [name] + '].'
			FROM dbo.ref_panels
			WHERE siteResourceID = @controllingSiteResourceID;
		ELSE	
			SET @msgjson = 'New Answer [' + @valueString + '] has been added to the Panel Selections Question.';

		SET @msgjson = @msgjson + @crlf + 'Related Question: [' + @fieldText + ']';

		SET @msgjson = STRING_ESCAPE(@msgjson,'json');

		EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
			@enteredByMemberID=@enteredByMemberID;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
