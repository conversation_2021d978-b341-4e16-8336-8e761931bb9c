(window.webpackJsonp = window.webpackJsonp || []).push([
	[12],
	{
		1577: function (n, t, o) {
			"use strict";
			var i = o(53),
				r = o(132),
				e = o(252),
				f = o(71),
				u = o(131),
				a = o(86),
				c = o(295),
				l = o(589),
				s = o(110),
				p = e && e.prototype;
			if (
				(i(
					{
						target: "Promise",
						proto: !0,
						real: !0,
						forced:
							!!e &&
							f(function () {
								p.finally.call({ then: function () {} }, function () {});
							}),
					},
					{
						finally: function (n) {
							var t = c(this, u("Promise")),
								o = a(n);
							return this.then(
								o
									? function (o) {
											return l(t, n()).then(function () {
												return o;
											});
										}
									: n,
								o
									? function (o) {
											return l(t, n()).then(function () {
												throw o;
											});
										}
									: n,
							);
						},
					},
				),
				!r && a(e))
			) {
				var h = u("Promise").prototype.finally;
				p.finally !== h && s(p, "finally", h, { unsafe: !0 });
			}
		},
	},
]);
//# sourceMappingURL=chunk.12.js.map
