{"version": 3, "sources": ["webpack:///./src/ui/node_modules/dayjs/locale/lt.js"], "names": ["module", "exports", "e", "i", "default", "s", "d", "split", "a", "l", "M", "test", "month", "f", "t", "name", "weekdays", "weekdaysShort", "weekdaysMin", "months", "monthsShort", "ordinal", "weekStart", "relativeTime", "future", "past", "m", "mm", "h", "hh", "dd", "MM", "y", "yy", "format", "LT", "LTS", "L", "LL", "LLL", "LLLL", "ll", "lll", "llll", "formats", "locale"], "mappings": "gFAAoEA,EAAOC,QAA6K,SAAUC,GAAG,aAAqF,IAAIC,EAA5E,SAAWD,GAAG,OAAOA,GAAG,iBAAiBA,GAAG,YAAYA,EAAEA,EAAE,CAACE,QAAQF,GAASG,CAAEH,GAAGI,EAAE,oGAAoGC,MAAM,KAAKC,EAAE,kGAAkGD,MAAM,KAAKE,EAAE,8DAA8DC,EAAE,SAASR,EAAEG,GAAG,OAAOI,EAAEE,KAAKN,GAAGC,EAAEJ,EAAEU,SAASJ,EAAEN,EAAEU,UAAUF,EAAEL,EAAEG,EAAEE,EAAEG,EAAEP,EAAE,IAAIQ,EAAE,CAACC,KAAK,KAAKC,SAAS,2FAA2FT,MAAM,KAAKU,cAAc,8BAA8BV,MAAM,KAAKW,YAAY,iBAAiBX,MAAM,KAAKY,OAAOT,EAAEU,YAAY,kDAAkDb,MAAM,KAAKc,QAAQ,SAASnB,GAAG,OAAOA,EAAE,KAAKoB,UAAU,EAAEC,aAAa,CAACC,OAAO,QAAQC,KAAK,WAAWpB,EAAE,kBAAkBqB,EAAE,SAASC,GAAG,aAAaC,EAAE,UAAUC,GAAG,cAAcvB,EAAE,QAAQwB,GAAG,YAAYpB,EAAE,SAASqB,GAAG,cAAcC,EAAE,QAAQC,GAAG,YAAYC,OAAO,CAACC,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,sCAAsCC,KAAK,4CAA4C/B,EAAE,aAAagC,GAAG,wBAAwBC,IAAI,sCAAsCC,KAAK,4CAA4CC,QAAQ,CAACT,GAAG,QAAQC,IAAI,WAAWC,EAAE,aAAaC,GAAG,wBAAwBC,IAAI,sCAAsCC,KAAK,4CAA4C/B,EAAE,aAAagC,GAAG,wBAAwBC,IAAI,sCAAsCC,KAAK,6CAA6C,OAAOxC,EAAEC,QAAQyC,OAAO/B,EAAE,MAAK,GAAIA,EAA7xDT,CAAE,EAAQ", "file": "chunks/chunk.184.js", "sourcesContent": ["!function(e,s){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=s(require(\"dayjs\")):\"function\"==typeof define&&define.amd?define([\"dayjs\"],s):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_locale_lt=s(e.dayjs)}(this,(function(e){\"use strict\";function s(e){return e&&\"object\"==typeof e&&\"default\"in e?e:{default:e}}var i=s(e),d=\"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio\".split(\"_\"),a=\"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis\".split(\"_\"),l=/D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?|MMMM?(\\[[^\\[\\]]*\\]|\\s)+D[oD]?/,M=function(e,s){return l.test(s)?d[e.month()]:a[e.month()]};M.s=a,M.f=d;var t={name:\"lt\",weekdays:\"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis\".split(\"_\"),weekdaysShort:\"sek_pir_ant_tre_ket_pen_šeš\".split(\"_\"),weekdaysMin:\"s_p_a_t_k_pn_š\".split(\"_\"),months:M,monthsShort:\"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd\".split(\"_\"),ordinal:function(e){return e+\".\"},weekStart:1,relativeTime:{future:\"už %s\",past:\"prieš %s\",s:\"kelias sekundes\",m:\"minutę\",mm:\"%d minutes\",h:\"valandą\",hh:\"%d valandas\",d:\"dieną\",dd:\"%d dienas\",M:\"mėnesį\",MM:\"%d mėnesius\",y:\"metus\",yy:\"%d metus\"},format:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY [m.] MMMM D [d.]\",LLL:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",LLLL:\"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]\",l:\"YYYY-MM-DD\",ll:\"YYYY [m.] MMMM D [d.]\",lll:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",llll:\"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]\"},formats:{LT:\"HH:mm\",LTS:\"HH:mm:ss\",L:\"YYYY-MM-DD\",LL:\"YYYY [m.] MMMM D [d.]\",LLL:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",LLLL:\"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]\",l:\"YYYY-MM-DD\",ll:\"YYYY [m.] MMMM D [d.]\",lll:\"YYYY [m.] MMMM D [d.], HH:mm [val.]\",llll:\"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]\"}};return i.default.locale(t,null,!0),t}));"], "sourceRoot": ""}