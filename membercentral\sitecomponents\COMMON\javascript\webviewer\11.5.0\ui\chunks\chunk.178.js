(window.webpackJsonp = window.webpackJsonp || []).push([
	[178],
	{
		1436: function (_, t, e) {
			_.exports = (function (_) {
				"use strict";
				var t = (function (_) {
						return _ && "object" == typeof _ && "default" in _
							? _
							: { default: _ };
					})(_),
					e = {
						name: "kn",
						weekdays:
							"ಭಾನುವಾರ_ಸೋಮವಾರ_ಮಂಗಳವಾರ_ಬುಧವಾರ_ಗುರುವಾರ_ಶುಕ್ರವಾರ_ಶನಿವಾರ".split("_"),
						months:
							"ಜನವರಿ_ಫೆಬ್ರವರಿ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂಬರ್_ಅಕ್ಟೋಬರ್_ನವೆಂಬರ್_ಡಿಸೆಂಬರ್".split(
								"_",
							),
						weekdaysShort: "ಭಾನು_ಸೋಮ_ಮಂಗಳ_ಬುಧ_ಗುರು_ಶುಕ್ರ_ಶನಿ".split("_"),
						monthsShort:
							"ಜನ_ಫೆಬ್ರ_ಮಾರ್ಚ್_ಏಪ್ರಿಲ್_ಮೇ_ಜೂನ್_ಜುಲೈ_ಆಗಸ್ಟ್_ಸೆಪ್ಟೆಂ_ಅಕ್ಟೋ_ನವೆಂ_ಡಿಸೆಂ".split("_"),
						weekdaysMin: "ಭಾ_ಸೋ_ಮಂ_ಬು_ಗು_ಶು_ಶ".split("_"),
						ordinal: function (_) {
							return _;
						},
						formats: {
							LT: "A h:mm",
							LTS: "A h:mm:ss",
							L: "DD/MM/YYYY",
							LL: "D MMMM YYYY",
							LLL: "D MMMM YYYY, A h:mm",
							LLLL: "dddd, D MMMM YYYY, A h:mm",
						},
						relativeTime: {
							future: "%s ನಂತರ",
							past: "%s ಹಿಂದೆ",
							s: "ಕೆಲವು ಕ್ಷಣಗಳು",
							m: "ಒಂದು ನಿಮಿಷ",
							mm: "%d ನಿಮಿಷ",
							h: "ಒಂದು ಗಂಟೆ",
							hh: "%d ಗಂಟೆ",
							d: "ಒಂದು ದಿನ",
							dd: "%d ದಿನ",
							M: "ಒಂದು ತಿಂಗಳು",
							MM: "%d ತಿಂಗಳು",
							y: "ಒಂದು ವರ್ಷ",
							yy: "%d ವರ್ಷ",
						},
					};
				return t.default.locale(e, null, !0), e;
			})(e(105));
		},
	},
]);
//# sourceMappingURL=chunk.178.js.map
