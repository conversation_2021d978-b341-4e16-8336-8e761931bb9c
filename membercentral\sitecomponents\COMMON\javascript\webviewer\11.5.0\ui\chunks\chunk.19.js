(window.webpackJsonp = window.webpackJsonp || []).push([
	[19],
	{
		1637: function (n, t, i) {
			i(586)("Int8", function (n) {
				return function (t, i, u) {
					return n(this, t, i, u);
				};
			});
		},
		1638: function (n, t, i) {
			i(586)(
				"Uint8",
				function (n) {
					return function (t, i, u) {
						return n(this, t, i, u);
					};
				},
				!0,
			);
		},
	},
]);
//# sourceMappingURL=chunk.19.js.map
